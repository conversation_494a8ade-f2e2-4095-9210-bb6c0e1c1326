package action_test

import (
	"context"
	"testing"

	"github.com/qbox/pay-sdk/dict"
	"github.com/stretchr/testify/assert"
)

func TestItemGroupInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	dictClient := sandbox.dictClient

	const productID uint64 = 1
	gs := []dict.ItemGroup{
		{
			ProductId:   productID,
			Code:        "storage",
			Name:        "存储",
			Description: "云存储的存储",
			Remark:      "云存储的存储备注",
			Order:       1,
		},
		{
			ProductId:   productID,
			Code:        "flow",
			Name:        "流量",
			Description: "云存储的流量",
			Remark:      "云存储的流量备注",
			Order:       2,
		},
	}
	assertFields := func(group *dict.ItemGroup, n int, msgAndArgs ...any) {
		if n >= len(gs) {
			return
		}
		assert.Equal(t, gs[n].GetProductId(), group.GetProductId(), msgAndArgs...)
		assert.Equal(t, gs[n].GetName(), group.GetName(), msgAndArgs...)
		assert.Equal(t, gs[n].GetDescription(), group.GetDescription(), msgAndArgs...)
		assert.Equal(t, gs[n].GetRemark(), group.GetRemark(), msgAndArgs...)
		assert.Equal(t, gs[n].GetOrder(), group.GetOrder(), msgAndArgs...)
	}
	ids := make([]uint64, len(gs))
	for n, g := range gs {
		group, err := dictClient.CreateItemGroup(context.Background(), &g)
		if assert.NoError(t, err, "CreateItemGroup") {
			assert.NotZero(t, group.GetId(), "CreateItemGroup")
			ids[n] = group.GetId()
		}

		group, err = dictClient.GetItemGroupByID(context.Background(), &dict.IDParam{Id: group.GetId()})
		if assert.NoError(t, err, "GetItemGroupByID") {
			assertFields(group, n, "GetItemGroupByID")
		}

		group2, err := dictClient.GetItemGroupByProductIDAndCode(context.Background(), &dict.IDCodeParam{Id: productID, Code: group.GetCode()})
		if assert.NoError(t, err, "GetItemGroupByProductIDAndCode") {
			assertFields(group2, n, "GetItemGroupByProductIDAndCode")
		}

		group.Name += group.GetDescription()
		gs[n].Name += g.GetDescription()
		group, err = dictClient.UpdateItemGroupByID(context.Background(), &dict.IDItemGroupParam{Id: group.GetId(), ItemGroup: group})
		if assert.NoError(t, err, "UpdateItemGroupByID") {
			assert.Equal(t, gs[n].GetName(), group.GetName(), "UpdateItemGroupByID")
		}
	}

	count, err := dictClient.CountItemGroupsByProductID(context.Background(), &dict.IDParam{Id: productID})
	if assert.NoError(t, err, "CountItemGroupsByProductID") {
		assert.Len(t, gs, int(count.GetCount()), "CountItemGroupsByProductID")
	}

	list, err := dictClient.ListItemGroupsByProductID(context.Background(), &dict.IDPagingParam{Id: productID, Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListItemGroupsByProductID") {
		assert.Len(t, gs, int(list.GetCount()), "ListItemGroupsByProductID")
		for i, g := range list.GetItemGroups() {
			assertFields(g, i, "ListItemGroupsByProductID")
		}
	}

	for _, id := range ids {
		_, err = dictClient.DeleteItemGroupByID(context.Background(), &dict.IDParam{Id: id})
		assert.NoError(t, err, "DeleteItemGroupByID")
	}
}
