package adapter

import (
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service"
)

// BuildPbSyncItemDataTypeExtraArgsParam converts from service.SyncItemDataTypeExtraArgsParam to the protobuf type.
func BuildPbSyncItemDataTypeExtraArgsParam(
	x *service.SyncItemDataTypeExtraArgsParam,
) (*pb.SyncItemDataTypeExtraArgsParam, error) {
	pbArgs := make([]*pb.ItemDataTypeExtraArg, len(x.Args))
	for i, arg := range x.Args {
		pbArg, err := BuildPbItemDataTypeExtraArg(&arg)
		if err != nil {
			return nil, err
		}
		pbArgs[i] = pbArg
	}
	return &pb.SyncItemDataTypeExtraArgsParam{
		ItemDataTypeId: x.ItemDataTypeID,
		Args:           pbArgs,
	}, nil
}

// BuildServiceSyncItemDataTypeExtraArgsParam converts from pb.SyncItemDataTypeExtraArgsParam to the service layer type.
func BuildServiceSyncItemDataTypeExtraArgsParam(
	x *pb.SyncItemDataTypeExtraArgsParam,
) (*service.SyncItemDataTypeExtraArgsParam, error) {
	srvArgs := make([]model.ItemDataTypeExtraArg, len(x.Args))
	for i, arg := range x.Args {
		srvArg, err := BuildModelItemDataTypeExtraArg(arg)
		if err != nil {
			return nil, err
		}
		srvArgs[i] = *srvArg
	}

	return &service.SyncItemDataTypeExtraArgsParam{
		ItemDataTypeID: x.GetItemDataTypeId(),
		Args:           srvArgs,
	}, nil
}
