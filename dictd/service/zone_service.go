package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/dictd/model"
)

// GetZoneByID 根据id获取Zone记录
func (s *DictBizService) GetZoneByID(
	ctx context.Context,
	id uint64,
) (*model.Zone, error) {
	return s.dictDao.Zone.GetByID(id, s.cacheExpires)
}

// GetZoneByCode 根据code获取Zone记录
func (s *DictBizService) GetZoneByCode(
	ctx context.Context,
	code int64,
) (*model.Zone, error) {
	return s.dictDao.Zone.GetByCode(code, s.cacheExpires)
}

// GetZoneByName 根据name获取Zone记录
func (s *DictBizService) GetZoneByName(
	ctx context.Context,
	name string,
) (*model.Zone, error) {
	return s.dictDao.Zone.GetByName(name, s.cacheExpires)
}

// ListAllZones 获取所有Zone记录
func (s *DictBizService) ListAllZones(
	ctx context.Context,
	offset int,
	limit int,
) (zones []model.Zone, err error) {
	return s.dictDao.Zone.ListAll(offset, limit, s.cacheExpires)
}

// ListZonesByCodes 根据 codes 批量获取Zone记录
func (s *DictBizService) ListZonesByCodes(
	ctx context.Context,
	codes []int64,
) (zones []model.Zone, err error) {
	if len(codes) == 0 {
		return zones, nil
	}
	return s.dictDao.Zone.ListByCodes(codes)
}

// CountAllZones 获取所有Zone记录的条数
func (s *DictBizService) CountAllZones(
	ctx context.Context,
) (uint64, error) {
	return s.dictDao.Zone.CountAll(s.cacheExpires)
}

// CreateZone 创建Zone记录
func (s *DictBizService) CreateZone(
	ctx context.Context,
	zone *model.Zone,
) (*model.Zone, error) {
	zone.ID = 0
	err := s.dictDao.Zone.Save(zone, s.cacheExpires)
	return zone, err
}

// UpdateZoneByID 根据ID更新Zone记录
func (s *DictBizService) UpdateZoneByID(
	ctx context.Context,
	id uint64,
	zone *model.Zone,
) (*model.Zone, error) {
	zone.ID = id
	err := s.dictDao.Zone.Save(zone, s.cacheExpires)
	return zone, err
}

// UpdateZoneByCode 根据code更新Zone记录
func (s *DictBizService) UpdateZoneByCode(
	ctx context.Context,
	code int64,
	zone *model.Zone,
) (*model.Zone, error) {
	z, err := s.GetZoneByCode(ctx, code)
	if err != nil {
		return nil, errors.Trace(err).WithField("code", code)
	}
	return s.UpdateZoneByID(ctx, z.ID, zone)
}

// DeleteZoneByID 根据ID删除Zone记录
func (s *DictBizService) DeleteZoneByID(
	ctx context.Context,
	id uint64,
) (*model.Zone, error) {
	zone, err := s.GetZoneByID(ctx, id)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	err = s.dictDao.Zone.Delete(zone)
	return zone, err
}

// DeleteZoneByCode 根据code删除Zone记录
func (s *DictBizService) DeleteZoneByCode(
	ctx context.Context,
	code int64,
) (*model.Zone, error) {
	zone, err := s.GetZoneByCode(ctx, code)
	if err != nil {
		return nil, errors.Trace(err).WithField("code", code)
	}
	err = s.dictDao.Zone.Delete(zone)
	return zone, err
}
