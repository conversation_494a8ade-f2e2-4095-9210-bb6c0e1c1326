package dataplaneModel

import (
	"fmt"
	"hash/fnv"
	"strings"
	"time"

	"github.com/qbox/bo-base/v4/base"

	"qiniu.io/pay/kirby/model/adminModel"
)

type dataTableKind int

const (
	dataTableKindUnknown          dataTableKind = 0
	dataTableKindSeries           dataTableKind = 1
	dataTableKindAggregatedSeries dataTableKind = 2
)

type computedShardInfo struct {
	name      string
	numPoints int
}

func (x dataTableKind) tableNamePrefix() string {
	switch x {
	case dataTableKindSeries:
		return "series"
	case dataTableKindAggregatedSeries:
		return "agg_series"
	default:
		// should be unreachable
		return ""
	}
}

func getGranularityName(g adminModel.Granularity) string {
	switch g {
	case adminModel.Granularity5Min:
		return "5min"
	case adminModel.GranularityDay:
		return "day"
	case adminModel.GranularityMonth:
		return "month"
	default:
		return "unknown"
	}
}

func incrementOneRowWrtGranularity(g adminModel.Granularity, t time.Time) time.Time {
	switch g {
	case adminModel.Granularity5Min:
		// one row is one day
		return t.AddDate(0, 0, 1)
	case adminModel.GranularityDay, adminModel.GranularityMonth:
		// one row is one month
		return t.AddDate(0, 1, 0)
	default:
		// should be unreachable
		return t
	}
}

func getNumPointsPerRow(g adminModel.Granularity, t time.Time) int {
	switch g {
	case adminModel.Granularity5Min:
		// one day per row
		return 288
	case adminModel.GranularityDay:
		// one month per row
		return int(base.NumDaysInMonth(t))
	case adminModel.GranularityMonth:
		// one month per row
		return 1
	default:
		// shouldn't happen in normal operation
		return 0
	}
}

func isTimeAlignedWrtGranularity(g adminModel.Granularity, t time.Time, loc *time.Location) bool {
	t = t.In(loc)

	if t.Nanosecond() != 0 || t.Second() != 0 {
		return false
	}

	switch g {
	case adminModel.Granularity5Min:
		return t.Minute()%5 == 0

	case adminModel.GranularityDay:
		return t.Minute() == 0 && t.Hour() == 0

	case adminModel.GranularityMonth:
		return t.Minute() == 0 && t.Hour() == 0 && t.Day() == 1

	default:
		return false
	}
}

func computeDataTableShard(
	kind dataTableKind,
	g adminModel.Granularity,
	t time.Time,
	loc *time.Location,
	key string,
) computedShardInfo {
	if kind == dataTableKindAggregatedSeries {
		return newDataTableShardInfo(kind, g, t, loc, 0)
	}

	// 给原始序列的存储按月份和 key 分片，首先追求均匀
	h := fnv.New32()
	h.Write([]byte(key))
	// NOTE: 理论上取哪一位为 shard 编号都差不多，取 LSB 是当时一个随意的选择，但一旦选择了就不能轻易变动该逻辑
	// （一定需要变动也可以，但需要做 re-sharding，不见得愿意在线上花时间做这件事）
	shardIdx := byte(h.Sum32() & 0xff)

	return newDataTableShardInfo(kind, g, t, loc, int(shardIdx))
}

func newDataTableShardInfo(
	kind dataTableKind,
	g adminModel.Granularity,
	t time.Time,
	loc *time.Location,
	shardIdx int,
) computedShardInfo {
	t = t.In(loc)
	numPoints := getNumPointsPerRow(g, t)

	switch kind {
	case dataTableKindAggregatedSeries:
		// 团队认为汇总序列数量不那么多，不太需要分片，因此只按月份分片
		return computedShardInfo{
			name: fmt.Sprintf(
				"%s_%s_m%04d%02d",
				kind.tableNamePrefix(),
				getGranularityName(g),
				t.Year(),
				t.Month(),
			),
			numPoints: numPoints,
		}
	case dataTableKindSeries:
		return computedShardInfo{
			name: fmt.Sprintf(
				"%s_%s_m%04d%02d_%02x",
				kind.tableNamePrefix(),
				getGranularityName(g),
				t.Year(),
				t.Month(),
				shardIdx,
			),
			numPoints: numPoints,
		}
	default:
		// should never happen
		return computedShardInfo{}
	}
}

func getAggSeriesMetadataTableName(t time.Time, loc *time.Location) string {
	monthStr := t.In(loc).Format("200601")
	return fmt.Sprintf("md_agg_series_m%s", monthStr)
}

func getSeriesMetadataTableName(t time.Time, loc *time.Location) string {
	monthStr := t.In(loc).Format("200601")
	return fmt.Sprintf("md_series_m%s", monthStr)
}

func isTableNotFound(err error) bool {
	// e.g. "Error 1146: Table 'bo_pay_kirby_dataplane_series_metadata_sharding.md_series_m202210' doesn't exist"
	// or "Error 1146 (42S02): Table 'bo_pay_kirby_dataplane_series_metadata_sharding_utc.md_series_m202210' doesn't exist"
	return strings.HasPrefix(err.Error(), "Error 1146")
}

func isTableAlreadyExisting(err error) bool {
	// e.g. "Error 1050 (42S01): Table 'md_series_m202305' already exists"
	return strings.HasPrefix(err.Error(), "Error 1050")
}
