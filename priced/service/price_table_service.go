package service

import (
	"context"
	"encoding/json"
	"sort"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/eventbus"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/priced/model"
)

// GetPriceTableByID 根据 ID 查询价格
func (s *PriceBizService) GetPriceTableByID(
	ctx context.Context,
	id uint64,
) (*model.PriceTable, error) {
	return s.priceDao.PriceTable.GetByID(id, s.cacheExpires)
}

// ListPriceTableByIDsWithoutCache 批量获取 price table
func (s *PriceBizService) ListPriceTableByIDsWithoutCache(
	ctx context.Context,
	ids []uint64,
) ([]*model.PriceTable, error) {
	return s.priceDao.PriceTable.ListByIDs(ids)
}

// GetPriceTableAndItemsByID 查询pricetable并填充items
func (s *PriceBizService) GetPriceTableAndItemsByID(
	ctx context.Context,
	id uint64,
) (*model.PriceTable, error) {
	return s.priceDao.PriceTable.GetWithItemsByID(id, s.cacheExpires)
}

// CreatePriceTable 创建价格
func (s *PriceBizService) CreatePriceTable(
	ctx context.Context,
	priceTableModel *model.PriceTable,
) (*model.PriceTable, error) {
	priceTableModel.ID = 0
	err := s.priceDao.PriceTable.Save(priceTableModel, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return priceTableModel, err
}

// UpdatePriceTableByID 根据 ID 更新价格
func (s *PriceBizService) UpdatePriceTableByID(
	ctx context.Context,
	id uint64,
	priceTableModel *model.PriceTable,
) (*model.PriceTable, error) {
	priceTableModel.ID = id
	err := s.priceDao.PriceTable.Save(priceTableModel, s.cacheExpires)
	return priceTableModel, err
}

// DeletePriceTableByID 根据 ID 删除价格
func (s *PriceBizService) DeletePriceTableByID(
	ctx context.Context,
	id uint64,
) (*model.PriceTable, error) {
	priceTableModel, err := s.GetPriceTableByID(ctx, id)
	if err != nil {
		return nil, errors.Trace(err)
	}

	err = s.priceDao.PriceTable.Delete(priceTableModel)
	return priceTableModel, err
}

// ListAllPriceTables 查询所有的价格
func (s *PriceBizService) ListAllPriceTables(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.PriceTable, error) {
	return s.priceDao.PriceTable.List(offset, limit, s.cacheExpires)
}

// CountAllPriceTables 查询所有的价格的数量
func (s *PriceBizService) CountAllPriceTables(
	ctx context.Context,
) (uint64, error) {
	return s.priceDao.PriceTable.Count(s.cacheExpires)
}

// DisablePriceTable 使指定报价单失效, 并更新所有相关用户的 Final PriceTable
func (s *PriceBizService) DisablePriceTable(
	ctx context.Context,
	id uint64,
) (err error) {
	err = s.priceDao.PriceTable.DisablePriceTable(id)
	if err != nil {
		return errors.Trace(err)
	}

	err = s.UpdateFinalTableWithAsyncCheck(ctx, id)
	if err != nil {
		return errors.Trace(err)
	}
	return nil
}

// EnablePriceTable 使指定报价单生效, 并更新所有相关用户的 Final PriceTable
func (s *PriceBizService) EnablePriceTable(
	ctx context.Context,
	id uint64,
) (err error) {
	priceTable, err := s.GetPriceTableByID(ctx, id)
	if err != nil {
		return errors.Trace(err)
	}
	if !priceTable.IsDisabled {
		// 已经生效了，对外幂等返回
		return nil
	}
	err = s.priceDao.PriceTable.EnablePriceTable(id)
	if err != nil {
		return errors.Trace(err)
	}

	err = s.UpdateFinalTableWithAsyncCheck(ctx, id)
	if err != nil {
		return errors.Trace(err)
	}
	return nil
}

// UpdateFinalTableWithAsyncCheck  更新用户final price table 并异步检查价格表
func (s *PriceBizService) UpdateFinalTableWithAsyncCheck(
	ctx context.Context,
	id uint64,
) error {
	pums, err := s.ListPriceUserMapsByPriceID(ctx, id, 0, -1)
	if err != nil {
		return errors.Trace(err)
	}
	distinctUIDs := make([]uint64, 0)
	for _, pum := range pums {
		distinctUIDs = append(distinctUIDs, pum.UID)
	}
	distinctUIDs = base.UniqueIntSlice(distinctUIDs)

	_, err = resultgroup.ThrottledParallelMap(
		distinctUIDs, 8,
		func(uid uint64) (any, error) {
			err1 := s.UpdateUserFinalPrice(ctx, uid)
			if err1 != nil {
				return nil, errors.Errorf("uid:%d, err:%s", uid, err1)
			}
			return nil, nil
		},
	)
	if err != nil {
		logging.GetLogger(ctx).
			WithError(err).
			WithField("id", id).
			Error("[checkEnablePrice] UpdateUserFinalPrice failed")
		return errors.Trace(err)
	}

	// async check
	for _, u := range distinctUIDs {
		go func(uid uint64) {
			time.Sleep(10 * time.Second)
			if e := s.checkEnablePrice(ctx, uid); e != nil {
				logging.GetLogger(ctx).
					WithError(e).
					WithFields(logrus.Fields{
						"uid": uid,
						"id":  id,
					}).
					Warn("[asyncCheckEnablePrice] enable price may be failed")
			}
		}(u)
	}

	for _, pum := range pums {
		go func(uid uint64, priceID uint64) {
			msg, _ := json.Marshal(eventbus.UserPriceTableChangedMessage{
				UID:     uid,
				PriceID: priceID,
			})
			if e := s.eventbus.Publish(eventbus.UserPriceTableChangedTopic, msg); e != nil {
				logging.GetLogger(ctx).
					WithError(e).
					WithFields(logrus.Fields{
						"uid": uid,
						"id":  priceID,
					}).
					Warn("[publishPriceTableChanged] publish msg failed")
			}
		}(pum.UID, pum.PriceID)
	}
	return nil
}

// checkEnablePrice 检查 FINAL 表生成是否有问题，用于添加日志告警及时感知问题，fix 生成 FINAL 表问题之后去掉
// 简易检查，生成的新的 FINAL 表与上一个 FINAL 表计费项
func (s *PriceBizService) checkEnablePrice(ctx context.Context, uid uint64) (err error) {
	priceTables, err := s.ListAllPriceTablesByUID(ctx, uid)
	if err != nil {
		return
	}

	// 按创建时间降序排列
	sort.Slice(priceTables, func(i, j int) bool {
		return priceTables[i].CreatedAt.After(priceTables[j].CreatedAt)
	})

	if len(priceTables) < 2 ||
		priceTables[0].Type != model.PriceTableTypeFinal ||
		priceTables[1].Type != model.PriceTableTypeFinal ||
		len(priceTables[0].Items) != len(priceTables[1].Items) {
		return
	}

	sortPriceItems(priceTables[0].Items)
	sortPriceItems(priceTables[1].Items)

	// 任一项不匹配则视为不一样
	for i, item := range priceTables[0].Items {
		if item.DeadTime != priceTables[1].Items[i].DeadTime ||
			item.EffectTime != priceTables[1].Items[i].EffectTime ||
			item.AlgorithmID != priceTables[1].Items[i].AlgorithmID ||
			item.StairPriceType != priceTables[1].Items[i].StairPriceType ||
			item.ZoneID != priceTables[1].Items[i].ZoneID ||
			item.ItemID != priceTables[1].Items[i].ItemID {
			return
		}
	}

	return errors.New("duplicated price items")
}

func sortPriceItems(items []model.PriceItem) {
	sort.Slice(items, func(i, j int) bool {
		return items[i].ItemID < items[j].ItemID ||
			items[i].ZoneID < items[j].ZoneID ||
			items[i].EffectTime < items[j].EffectTime ||
			items[i].DeadTime < items[j].DeadTime
	})
}
