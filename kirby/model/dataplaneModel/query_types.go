package dataplaneModel

import (
	"time"

	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
)

// 用于查询的类型定义
// 保持与 pb 一侧一致

type ReqListSeries struct {
	// Epoch 参考当前时刻，传入空值代表 time.Now
	Epoch    time.Time
	Page     uint64
	PageSize uint64
	// DataTimeFrom 与序列所涵盖时间有重合的时间的起始时刻（含）
	DataTimeFrom time.Time
	// DataTimeTo 与序列所涵盖时间有重合的时间的截止时刻（不含）
	DataTimeTo time.Time
	// CtimeFrom 序列的创建时刻范围的起始时刻（含）
	CtimeFrom time.Time
	// CtimeTo 序列的创建时刻范围的截止时刻（不含）
	CtimeTo time.Time
	// SeriesKeys 欲查询的序列 key(s)
	SeriesKeys []string
	// MBUUIDs 欲查询的序列所属指标包的 UUID(s)
	MBUUIDs []string
	// QRNs 欲查询的序列属于的 QRN(s)
	QRNs []string
	// RTDCodes 欲查询的序列对应资源属于的 RTD code(s)
	RTDCodes []string
	// UIDs 欲查询的序列的 UID(s)
	UIDs []uint64
	// MMs 欲查询的序列可属于的指标 code(s)
	MMs []string
	// SPConditions 欲查询的序列的属性满足的条件
	SPConditions []*SPCondition
}

type SPCondition struct {
	// Key 序列属性名
	Key string
	// SevIn 对字符串枚举类型：IN (?)
	SevIn []string
}

type ReqListAggregatedSeries struct {
	// Epoch 参考当前时刻，传入空值代表 time.Now
	Epoch    time.Time
	Page     uint64
	PageSize uint64
	// DataTimeFrom 与序列所涵盖时间有重合的时间的起始时刻（含）
	DataTimeFrom time.Time
	// DataTimeTo 与序列所涵盖时间有重合的时间的截止时刻（不含）
	DataTimeTo time.Time
	// CtimeFrom 汇总序列的创建时刻范围的起始时刻（含）
	CtimeFrom time.Time
	// CtimeTo 汇总序列的创建时刻范围的截止时刻（不含）
	CtimeTo time.Time
	// AggSeriesKeys 欲查询的汇总序列 key(s)
	AggSeriesKeys []string
	// UIDs 欲查询的序列的 UID(s)
	UIDs []uint64
	// ItemCodes 欲查询的序列可属于的计费系统口径的计费项 code(s)
	ItemCodes []string
}

// SeriesDetail 响应中的序列明细
type SeriesDetail struct {
	SeriesKey string
	// QRN 资源 QRN
	QRN string
	// MM 计量指标 code
	MM string
	// Props 序列属性
	Props SP
	// Start 该序列计量的起始时刻（含）
	Start time.Time
	// End 该序列计量的结束时刻（不含）
	End time.Time
	// MBUUID 被推入系统时所属的指标包 UUID
	MBUUID   string
	UID      uint64
	RTDCode  string
	AggRCode string
	Unit     string
	G        adminModel.Granularity
	// Data 计量点
	Data []*pb.StatPoint
	// Ctime 创建时刻
	Ctime time.Time
}

type SeriesDetailList struct {
	Data  []*SeriesDetail
	Count uint64
}

// SeriesKind 序列类型
type SeriesKind int32

const (
	// SeriesKindUnknown 非法零值
	SeriesKindUnknown SeriesKind = 0
	// SeriesKindSeries 序列
	SeriesKindSeries SeriesKind = 1
	// SeriesKindAggregatedSeries 汇总序列
	SeriesKindAggregatedSeries SeriesKind = 2
)

// AggregatedSource 汇总序列的源序列信息
type AggregatedSource struct {
	// Kind 源序列的类型
	Kind SeriesKind
	// Key 源序列的唯一 key
	Key string
}

// AggregatedSeriesDetail 响应中的汇总序列明细
type AggregatedSeriesDetail struct {
	AggSeriesKey string
	UID          uint64
	Start        time.Time
	End          time.Time
	ItemCode     string
	ZoneCode     int64
	AggRCode     string
	Unit         string
	G            adminModel.Granularity
	// Data 计量点
	Data []*pb.StatPoint
	// Ctime 创建时刻
	Ctime time.Time
	// Sources 用来产生该汇总序列的数据源列表
	Sources []*AggregatedSource
}

type AggregatedSeriesDetailList struct {
	Data  []*AggregatedSeriesDetail
	Count uint64
}
