package model

import (
	"context"
	"errors"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
)

const qvmPriceItemStairTableName = "qvm_price_item_stairs"

// QVMPriceItemStair model definition
type QVMPriceItemStair struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// QVMPriceItemID 关联的 QVMPriceItem ID
	QVMPriceItemID uint64 `gorm:"column:qvm_price_item_id;not null;unique_index:idx_qvmpis_qvmpiid_order;comment:'关联的 QVMPriceItem ID'"`
	// UID 冗余的 QVMPriceTable uid 字段
	UID uint64 `gorm:"column:uid;index:idx_qvmpis_uid_item;not null;comment:'冗余的 QVMPriceTable uid 字段'"`
	// ItemCode 冗余的 QVMPriceItem item_code 字段
	ItemCode string `gorm:"column:item_code;type:VARCHAR(256);not null;index:idx_qvmpis_uid_item;comment:'冗余的 QVMPriceItem item_code 字段'"`
	// Quantity 阶梯的量
	Quantity uint64 `gorm:"column:quantity;not null;comment:'阶梯的量'"`
	// Price 阶梯价格，8 位小数的定点数（如 0.12 CNY 的 0.12 会储存为 0.12000000）
	Price *base.NMoney `gorm:"column:price;type:DECIMAL(20,8);not null;comment:'阶梯价格，8 位小数的定点数'"`
	// Order 本条记录在阶梯中的顺序
	//
	// 将同一 QVMPriceItem 关联的所有本类型记录升序排序，即得正确的价格表 v3 样式阶梯。
	Order uint64 `gorm:"column:order;not null;unique_index:idx_qvmpis_qvmpiid_order;comment:'本条记录在阶梯中的顺序。将同一 QVMPriceItem 关联的所有本类型记录升序排序，即得正确的价格表 v3 样式阶梯。'"`

	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);default:now(6)"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME(6);default:now(6) ON UPDATE now(6)"`
}

// 由于 QVM 不是 gorm 认识的缩略词，默认它会生成形如 q_vm_xxx 的表名，因此显式指定表名
func (*QVMPriceItemStair) TableName() string {
	return qvmPriceItemStairTableName
}

// QVMPriceItemStairDao is data access object of QVMPriceItemStair model
type QVMPriceItemStairDao struct {
	base *dao.BaseDao
}

// NewQVMPriceItemStairDao is constructor of QVMPriceItemStairDao
func NewQVMPriceItemStairDao(base *dao.BaseDao) *QVMPriceItemStairDao {
	return &QVMPriceItemStairDao{
		base: base,
	}
}

func (d *QVMPriceItemStairDao) BulkInsert(ctx context.Context, rows []QVMPriceItemStair) error {
	return d.base.BulkInsert(ctx, rows)
}

func (d *QVMPriceItemStairDao) ListByUIDItemCode(
	uid uint64,
	itemCodes []string,
) ([]*QVMPriceItemStair, error) {
	if len(itemCodes) == 0 {
		return nil, errors.New("QVMPriceItemStair cannot be listed without explicit item codes")
	}

	var result []*QVMPriceItemStair
	err := d.base.Model(&QVMPriceItemStair{}).
		Where(
			"`uid` = ? AND `item_code` IN (?)",
			uid,
			itemCodes,
		).
		Find(&result).
		Error
	if err != nil {
		return nil, err
	}

	return result, nil
}
