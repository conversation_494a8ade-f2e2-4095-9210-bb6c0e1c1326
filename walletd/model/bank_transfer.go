package model

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/uuid"
)

// BankTransferStatus 银行转账状态
type BankTransferStatus int32

const (
	// BankTransferStatusUnknown 未知状态
	BankTransferStatusUnknown BankTransferStatus = 0
	// BankTransferStatusPendingReview 待审核
	BankTransferStatusPendingReview BankTransferStatus = 1
	// BankTransferStatusPendingConfirm 待确认
	BankTransferStatusPendingConfirm BankTransferStatus = 2
	// BankTransferStatusConfirmed 已确认
	BankTransferStatusConfirmed BankTransferStatus = 3
	// BankTransferStatusRecharging 充值中
	BankTransferStatusRecharging BankTransferStatus = 4
	// BankTransferStatusRecharged 已充值到账
	BankTransferStatusRecharged BankTransferStatus = 5
	// BankTransferStatusRevoking 撤销中
	BankTransferStatusRevoking BankTransferStatus = 6
	// BankTransferStatusRevoked 已撤销
	BankTransferStatusRevoked BankTransferStatus = 7
	// BankTransferStatusVoid 已作废
	BankTransferStatusVoid BankTransferStatus = 8
)

// RecordType 记录类型
type RecordType int32

const (
	// RecordTypeUnknown 未知类型
	RecordTypeUnknown RecordType = 0
	// RecordTypeFirstAllocate 初次分配
	RecordTypeFirstAllocate RecordType = 1
	// RecordTypeReAllocate 重新分配
	RecordTypeReAllocate RecordType = 2
)

// MatchingType 匹配方式
type MatchingType int32

const (
	// MatchingTypeUnknown 未知匹配方式
	MatchingTypeUnknown MatchingType = 0
	// MatchingTypeVirtualAccount 虚账号匹配
	MatchingTypeVirtualAccount MatchingType = 1
	// MatchingTypeRemark 备注匹配
	MatchingTypeRemark MatchingType = 2
	// MatchingTypeManual 人工匹配
	MatchingTypeManual MatchingType = 3
)

// BankTransfer 银行转账记录模型
type BankTransfer struct {
	ID                uint64             `gorm:"primary_key" json:"id,omitempty"`
	ParentSN          string             `gorm:"type:varchar(64)" json:"parent_sn,omitempty"`                            // 关联记录
	SN                string             `gorm:"type:varchar(64);unique_index:idx_bank_transfer_sn" json:"sn,omitempty"` // 唯一编号
	PaymentAccount    string             `gorm:"type:varchar(128)" json:"payment_account,omitempty"`                     // 打款账户
	PaymentAccountNo  string             `gorm:"type:varchar(64)" json:"payment_account_no,omitempty"`                   // 打款账号
	PaymentBank       string             `gorm:"type:varchar(128)" json:"payment_bank,omitempty"`                        // 打款银行
	ReceivedAccount   string             `gorm:"type:varchar(128)" json:"received_account,omitempty"`                    // 收款账户
	ReceivedAccountNo string             `gorm:"type:varchar(64)" json:"received_account_no,omitempty"`                  // 收款账号
	ReceivedBank      string             `gorm:"type:varchar(128)" json:"received_bank,omitempty"`                       // 收款银行
	CurrencyType      base.CurrencyType  `gorm:"type:varchar(10);not null;default:'CNY'" json:"currency_type,omitempty"` // 币种
	Amount            *base.NMoney       `gorm:"type:decimal(20,4)" json:"amount,omitempty"`                             // 金额
	ReceivedDate      time.Time          `sql:"type:DATETIME(6)" json:"received_date"`                                   // 收款日期
	Remark            string             `gorm:"type:text" json:"remark,omitempty"`                                      // 备注(对应银行消息的Notes)
	IsVirtualAccount  bool               `sql:"type:tinyint" json:"is_virtual_account,omitempty"`                        // 是否虚账号打款
	BankTxnNo         string             `gorm:"type:varchar(128)" json:"bank_txn_no,omitempty"`                         // 银行交易唯一标识
	BankDescription   string             `gorm:"type:varchar(256)" json:"bank_description,omitempty"`                    // 银行摘要(对应银行消息的remark)
	UID               uint64             `gorm:"index:idx_bank_transfer_uid" json:"uid,omitempty"`                       // 七牛 uid
	RecordType        RecordType         `sql:"type:tinyint" json:"record_type,omitempty"`                               // 记录类型(初次分配、重新分配)
	MatchingType      MatchingType       `sql:"type:tinyint" json:"matching_type,omitempty"`                             // 匹配方式(虚账号、备注、人工匹配)
	Creator           string             `gorm:"type:varchar(128)" json:"creator,omitempty"`                             // 创建者
	Status            BankTransferStatus `sql:"type:tinyint" json:"status,omitempty"`                                    // 状态
	PaymentID         string             `gorm:"type:varchar(128)" json:"payment_id"`                                    // 钱包流水 id

	CreatedAt time.Time `sql:"type:DATETIME(6)" json:"created_at"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)" json:"updated_at"`

	Reallocates []*BankTransfer `gorm:"-" json:"reallocates,omitempty"` // 重新分配记录
}

// CanAutoRecharge 判断该银行转账是否满足自动充值条件
// 自动充值需满足以下任一条件：
// 1. 匹配类型为虚拟账户（MatchingTypeVirtualAccount）
// 2. 匹配类型为备注信息（MatchingTypeRemark）
// 同时必须保证用户ID有效（UID > 0）
func (bt *BankTransfer) CanAutoRecharge() bool {
	if bt.UID == 0 {
		return false
	}
	if bt.MatchingType == MatchingTypeVirtualAccount {
		return true
	}
	if bt.MatchingType == MatchingTypeRemark {
		return true
	}
	return false
}

// Clone 创建当前对象的深拷贝
func (bt *BankTransfer) Clone() *BankTransfer {
	clone := &BankTransfer{}
	*clone = *bt // 复制基本结构

	// 特殊处理指针类型字段
	if bt.Amount != nil {
		clone.Amount = base.NewNMoney(bt.Amount.MustGetMoney())
	}
	clone.ID = 0
	clone.SN = ""
	return clone
}

// GetCreator 获取创建者名称（系统默认值）
func (bt *BankTransfer) GetCreator() string {
	if bt.Creator == "" {
		return "system"
	}
	return bt.Creator
}

// BankTransferQuery 银行转账记录查询条件
type BankTransferQuery struct {
	UID              uint64             // 用户ID
	Status           BankTransferStatus // 状态
	PaymentAccountNo string             // 打款账号
	PaymentAccount   string             // 打款账户
	ReceivedAccount  string             // 收款银行
	CreatedAtStart   *time.Time         // 创建时间开始
	CreatedAtEnd     *time.Time         // 创建时间结束
	Offset           int64              // 分页偏移
	Limit            int64              // 分页大小
}

// BankTransferRevokeParam param of revoke
type BankTransferRevokeParam struct {
	SN          string             `json:"sn"`
	Creator     string             `json:"creator"`
	Reallocates []*ReallocateParam `json:"reallocates"`
}

// ReallocateParam param of reallocate
type ReallocateParam struct {
	UID uint64 `json:"uid"`
	// 金额
	Amount *base.NMoney `json:"amount"`
	// 备注
	Remark string `json:"remark"`
}

// BankTransferDao 银行转账数据访问对象
type BankTransferDao struct {
	base *dao.BaseDao
}

// DoTransaction do in transaction
func (d *BankTransferDao) DoTransaction(fn func(*BankTransferDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		return fn(NewBankTransferDao(base))
	})
}

// NewBankTransferDao 创建银行转账数据访问对象
func NewBankTransferDao(
	base *dao.BaseDao,
) *BankTransferDao {
	return &BankTransferDao{
		base: base,
	}
}

// Save 保存银行转账记录
func (d *BankTransferDao) Save(
	model *BankTransfer,
) error {
	if model.SN == "" {
		model.SN = fmt.Sprintf("SN%s", uuid.New())
	}
	return d.base.Save(model).Error
}

// UpdatePaymentID 充值完成后更新银行转账记录的充值流水ID
func (d *BankTransferDao) UpdatePaymentID(
	sn string,
	paymentID string,
) error {
	if paymentID == "" {
		return errors.New("paymentID is empty")
	}
	err := d.base.Model(&BankTransfer{}).
		Where("sn=?", sn).
		Updates(map[string]interface{}{
			"payment_id": paymentID,
			"status":     BankTransferStatusRecharged,
		}).Error
	if err != nil {
		return errors.Trace(err).WithFields(errors.Fields{
			"sn":        sn,
			"paymentID": paymentID,
		})
	}
	return nil
}

// GetBySN 通过序列号查询银行转账记录
func (d *BankTransferDao) GetBySN(
	sn string,
) (*BankTransfer, error) {
	model := &BankTransfer{}
	err := d.base.Where("sn=?", sn).First(model).Error
	if err != nil {
		return nil, errors.Trace(err).WithField("sn", sn)
	}
	return model, nil
}

// GetByParentSN 通过 parent 序列号查询银行转账记录
func (d *BankTransferDao) GetByParentSN(
	parentSN string,
) ([]*BankTransfer, error) {
	var transfers []*BankTransfer
	err := d.base.Where("parent_sn=?", parentSN).
		Find(&transfers).
		Offset(0).
		Limit(1000). // 重新分配记录数不会太多
		Error
	if err != nil {
		return nil, errors.Trace(err).WithField("parent_sn", parentSN)
	}
	return transfers, nil
}

// ListByUID 通过用户ID查询银行转账记录列表
func (d *BankTransferDao) ListByUID(
	uid uint64,
	offset, limit int,
) ([]*BankTransfer, error) {
	var transfers []*BankTransfer
	err := d.base.Where("uid=?", uid).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&transfers).Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid":    uid,
			"offset": offset,
			"limit":  limit,
		})
	}
	return transfers, nil
}

// CountByUID 统计用户的银行转账记录数量
func (d *BankTransferDao) CountByUID(
	uid uint64,
) (int64, error) {
	var count int64
	err := d.base.Model(&BankTransfer{}).
		Where("uid=?", uid).
		Count(&count).Error
	if err != nil {
		return 0, errors.Trace(err).WithField("uid", uid)
	}
	return count, nil
}

// UpdateStatus 更新银行转账记录状态
func (d *BankTransferDao) UpdateStatus(
	status BankTransferStatus,
	sn ...string,
) error {
	err := d.base.Model(&BankTransfer{}).
		Where("sn IN(?)", sn).
		Update("status", status).Error
	if err != nil {
		return errors.Trace(err).WithFields(errors.Fields{
			"sn":     sn,
			"status": status,
		})
	}
	return nil
}

// ListByStatus 通过状态查询银行转账记录列表
func (d *BankTransferDao) ListByStatus(
	status BankTransferStatus,
	offset, limit int,
) ([]*BankTransfer, error) {
	var transfers []*BankTransfer
	err := d.base.Where("status=?", status).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&transfers).Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"status": status,
			"offset": offset,
			"limit":  limit,
		})
	}
	return transfers, nil
}

// CountByStatus 统计特定状态的银行转账记录数量
func (d *BankTransferDao) CountByStatus(
	status BankTransferStatus,
) (int64, error) {
	var count int64
	err := d.base.Model(&BankTransfer{}).
		Where("status=?", status).
		Count(&count).Error
	if err != nil {
		return 0, errors.Trace(err).WithField("status", status)
	}
	return count, nil
}

// ListByQuery 根据查询条件获取银行转账记录列表
func (d *BankTransferDao) ListByQuery(
	ctx context.Context,
	query *BankTransferQuery,
) ([]*BankTransfer, error) {
	db := d.base.Model(&BankTransfer{})

	// 构建查询条件
	db = d.buildQueryCondition(db, query)

	// 排序、分页
	db = db.Order("created_at DESC").
		Offset(int(query.Offset)).
		Limit(int(query.Limit))

	// 执行查询
	var transfers []*BankTransfer
	err := db.Find(&transfers).Error
	if err != nil {
		return nil, errors.Trace(err).WithField("query", query)
	}

	return transfers, nil
}

// CountByQuery 根据查询条件统计银行转账记录数量
func (d *BankTransferDao) CountByQuery(
	ctx context.Context,
	query *BankTransferQuery,
) (uint64, error) {
	db := d.base.Model(&BankTransfer{})

	// 构建查询条件
	db = d.buildQueryCondition(db, query)

	// 执行统计
	var count uint64
	err := db.Count(&count).Error
	if err != nil {
		return 0, errors.Trace(err).WithField("query", query)
	}

	return count, nil
}

// buildQueryCondition 构建查询条件
func (d *BankTransferDao) buildQueryCondition(
	db *gorm.DB,
	query *BankTransferQuery,
) *gorm.DB {
	// 根据 UID 过滤
	if query.UID > 0 {
		db = db.Where("uid = ?", query.UID)
	}

	// 根据状态过滤
	if query.Status > 0 {
		db = db.Where("status = ?", query.Status)
	}

	// 根据打款账号
	if query.PaymentAccountNo != "" {
		db = db.Where("payment_account_no LIKE ?", "%"+query.PaymentAccountNo+"%")
	}

	// 根据打款账户过滤
	if query.PaymentAccount != "" {
		db = db.Where("payment_account LIKE ?", "%"+query.PaymentAccount+"%")
	}

	// 根据收款银行过滤
	if query.ReceivedAccount != "" {
		db = db.Where("received_account LIKE ?", "%"+query.ReceivedAccount+"%")
	}

	// 根据时间范围过滤
	if query.CreatedAtStart != nil {
		db = db.Where("created_at >= ?", query.CreatedAtStart)
	}

	if query.CreatedAtEnd != nil {
		db = db.Where("created_at <= ?", query.CreatedAtEnd)
	}

	return db
}

// SearchPaymentAccounts 模糊查询打款账户信息
func (d *BankTransferDao) SearchPaymentAccounts(
	ctx context.Context,
	paymentAccount string,
) ([]string, error) {
	if paymentAccount == "" {
		return []string{}, nil
	}

	// 构建 SQL 查询，使用 DISTINCT 去重
	var accounts []string
	err := d.base.Model(&BankTransfer{}).
		Select("DISTINCT payment_account").
		Where("payment_account LIKE ?", "%"+paymentAccount+"%").
		Limit(30). // 限制返回数量，避免返回过多结果
		Pluck("payment_account", &accounts).Error

	if err != nil {
		return nil, errors.Trace(err).WithField("payment_account", paymentAccount)
	}

	return accounts, nil
}
