package action

import (
	"context"

	"qiniu.io/pay/walletd/action/adapter"

	"github.com/qbox/bo-base/v4/errors"

	pb "github.com/qbox/pay-sdk/wallet"
)

// GetBankVirtualAccountByUID 查询用户的银行虚拟账号
func (a *WalletAction) GetBankVirtualAccountByUID(
	ctx context.Context,
	param *pb.UIDParam,
) (*pb.UserBankVirtualAccount, error) {
	userBankVirtualAccount, err := a.bankVirtAccountSrv.GetBankVirtualAccountByUID(ctx, param.GetUid())
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbUserBankVirtualAccount(userBankVirtualAccount), nil
}

// GetUIDByBankVirtualAccount 通过银行虚拟账号查询对应 uid
func (a *WalletAction) GetUIDByBankVirtualAccount(
	ctx context.Context,
	param *pb.GetUIDByBankVirtualAccountParam,
) (*pb.GetUIDByBankVirtualAccountResp, error) {
	userBankVirtualAccount, err := a.bankVirtAccountSrv.GetUIDByBankVirtualAccount(ctx, param.GetBankVirtualAccount())
	if err != nil {
		return nil, errors.Trace(err)
	}
	return &pb.GetUIDByBankVirtualAccountResp{
		Uid: userBankVirtualAccount.UID,
	}, nil
}

// BatchGetUIDByBankVirtualAccount 批量通过银行虚拟账号查询对应 uid
func (a *WalletAction) BatchGetUIDByBankVirtualAccount(
	ctx context.Context,
	param *pb.BatchGetUIDByBankVirtualAccountParam,
) (*pb.BatchGetUIDByBankVirtualAccountResp, error) {
	userBankVirtualAccounts, err := a.bankVirtAccountSrv.BatchGetUIDByBankVirtualAccount(ctx, param.GetBankVirtualAccounts())
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbBatchGetUIDByBankVirtualAccountResp(userBankVirtualAccounts)
}

// GenBankVirtualAccountByUID 为用户生成银行虚拟账号，接口幂等
func (a *WalletAction) GenBankVirtualAccountByUID(
	ctx context.Context,
	param *pb.UIDParam,
) (*pb.UserBankVirtualAccount, error) {
	userBankVirtualAccount, err := a.bankVirtAccountSrv.GenBankVirtualAccountByUID(ctx, param.GetUid())
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbUserBankVirtualAccount(userBankVirtualAccount), nil
}
