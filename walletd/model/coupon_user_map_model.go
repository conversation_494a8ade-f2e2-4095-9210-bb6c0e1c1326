package model

import (
	"database/sql/driver"
	stdErrors "errors"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// CouponType 优惠券类型
type CouponType string

const (
	// CouponTypeCash 现金券
	CouponTypeCash CouponType = "CASH"
	// CouponTypeDiscount 折扣券
	CouponTypeDiscount CouponType = "DISCOUNT"
	// CouponTypeRebate 返利券
	CouponTypeRebate CouponType = "REBATE"
)

// IsValid check whether the coupon type is valid
func (t CouponType) IsValid() bool {
	switch t {
	case CouponTypeCash, CouponTypeDiscount, CouponTypeRebate:
		return true
	}
	return false
}

// Value implements the driver.Valuer interface
func (t CouponType) Value() (driver.Value, error) {
	return string(t), nil
}

// <PERSON><PERSON> implements the sql.Scanner interface
func (t *CouponType) Scan(src any) error {
	if src == nil {
		*t = ""
	} else {
		*t = CouponType(string(src.([]uint8)))
	}
	return nil
}

// CouponUserMap model definition
type CouponUserMap struct {
	ID            uint64     `gorm:"primary_key"`
	CouponID      uint64     `gorm:"index:idx_coupon_user_map_coupon_id"`
	UID           uint64     `gorm:"index:idx_coupon_user_map_uid"`
	ZoneID        uint64     `gorm:"index:idx_coupon_user_map_zone_id"`
	TransactionID uint64     `gorm:"index:idx_coupon_user_map_transaction_id"`
	Type          CouponType `gorm:"type:varchar(16)"`
	EffectTime    base.HNS
	DeadTime      base.HNS

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// CouponUserMapDao is data access object of CouponUserMap model
type CouponUserMapDao struct {
	base *dao.BaseDao
}

// NewCouponUserMapDao is constructor of CouponUserMapDao
func NewCouponUserMapDao(base *dao.BaseDao) *CouponUserMapDao {
	return &CouponUserMapDao{
		base: base,
	}
}

// GetByID select a record of CouponUserMap by id
func (d *CouponUserMapDao) GetByID(id uint64, expires ...time.Duration) (*CouponUserMap, error) {
	model := &CouponUserMap{ID: id}
	err := d.base.First(model, id).Error
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// GetByCouponID selects a record of CouponUserMap by uid and coupon_id
func (d *CouponUserMapDao) GetByCouponID(couponID uint64) (list []CouponUserMap, err error) {
	err = d.base.
		Model(&CouponUserMap{}).
		Where("coupon_id = ?", couponID).
		Find(&list).Error
	if err != nil {
		return nil, errors.Trace(err).WithField("coupon_id", couponID)
	}
	return list, nil
}

// GetBoundCouponByCouponIDs selects records of CouponUserMap by coupon_id
func (d *CouponUserMapDao) GetBoundCouponByCouponIDs(
	couponIDs []uint64,
	couponType CouponType,
	uid uint64,
) (boundCouponIDs []uint64, err error) {
	query := d.base.
		Model(&CouponUserMap{}).
		Where("coupon_id in (?) AND `type` = ?", couponIDs, couponType)

	if uid != 0 {
		query = query.Where("uid = ?", uid)
	}

	err = query.
		Group("coupon_id").
		Having("count(id) > ?", 0).
		Pluck("coupon_id", &boundCouponIDs).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithField("coupon_ids", couponIDs)
	}
	return boundCouponIDs, nil
}

func (d *CouponUserMapDao) GetExistingBindingInTimeRange(
	uid uint64,
	couponID uint64,
	couponType CouponType,
	zoneID uint64,
	from base.HNS,
	to base.HNS,
) (*CouponUserMap, error) {
	var obj CouponUserMap
	err := d.base.Execute(func(v any) error {
		return d.base.Model(&CouponUserMap{}).
			Where(
				"`uid` = ? AND `coupon_id` = ? AND `type` = ? AND `zone_id` = ? AND `effect_time` <= ? AND `dead_time` >= ?",
				uid,
				couponID,
				couponType,
				zoneID,
				from,
				to,
			).First(v).Error
	}, &obj)
	if err != nil {
		if stdErrors.Is(errors.Cause(err), dao.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, err
	}

	return &obj, nil
}

// ListByUID select records by uid
func (d *CouponUserMapDao) ListByUID(uid uint64, typ CouponType, offset, limit int,
	expires ...time.Duration) (list []CouponUserMap, err error) {
	query := &CouponUserMap{UID: uid}
	if typ.IsValid() {
		query.Type = typ
	}

	err = d.base.Where(query).Offset(offset).Limit(limit).Find(&list).Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid":    uid,
			"type":   typ,
			"offset": offset,
			"limit":  limit,
		})
	}
	return
}

// CountByUID select count of records by uid
func (d *CouponUserMapDao) CountByUID(uid uint64, typ CouponType,
	expires ...time.Duration) (count uint64, err error) {

	query := &CouponUserMap{UID: uid}
	if typ.IsValid() {
		query.Type = typ
	}
	err = d.base.Model(&CouponUserMap{}).Where(query).Count(&count).Error
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"uid":  uid,
			"type": typ,
		})
	}
	return
}

// Save insert or update a record of CouponUserMap by id
func (d *CouponUserMapDao) Save(model *CouponUserMap, expires ...time.Duration) error {
	return d.base.Save(model).Error
}

// Delete delete a record of CouponUserMap
func (d *CouponUserMapDao) Delete(model *CouponUserMap) error {
	return d.base.Delete(model).Error
}
