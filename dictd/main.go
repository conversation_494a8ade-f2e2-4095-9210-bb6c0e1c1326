package main

import (
	"flag"

	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/qiniu/version/v2"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"github.com/qbox/bo-base/v4/cli"
	"github.com/qbox/bo-base/v4/dao"
	hook "github.com/qbox/bo-base/v4/errors/logrus"
	"github.com/qbox/bo-base/v4/intl"
	baselog "github.com/qbox/bo-base/v4/log"
	"github.com/qbox/bo-base/v4/rpc"
	pb "github.com/qbox/pay-sdk/dict"

	"qiniu.io/pay/dictd/action"
	dictdCfg "qiniu.io/pay/dictd/config"
	"qiniu.io/pay/dictd/i18n"
	"qiniu.io/pay/dictd/model"
	"qiniu.io/pay/dictd/service"
)

func main() {
	var confPath string
	var enableHTTPPprof bool
	var runMigration bool
	flag.StringVar(&confPath, "conf", "dictd.yml", "config file path")
	flag.BoolVar(&enableHTTPPprof, "pprof", false, "enable net/http/pprof under /debug/pprof paths")
	flag.BoolVar(&runMigration, "automigrate", false, "enable auto migration of db schema")
	_ = flag.Bool("version", false, "print version info and exit")
	flag.Parse()
	cli.InitFlagMap()

	if cli.IsFlagProvided("version") {
		version.Print()
		return
	}

	conf, err := dictdCfg.LoadDictdConfig(confPath)
	if err != nil {
		log.WithField("err", err).Fatal("failed to load config")
	}

	_, err = intl.Init(&conf.Intl, i18n.L10nFS, i18n.RelativePath)
	if err != nil {
		log.WithError(err).Fatal("failed to init l10n mechanism")
		return
	}

	// command-line --pprof switch has higher priority over config settings
	if cli.IsFlagProvided("pprof") {
		log.WithFields(log.Fields{
			"configValue": conf.RPC.EnablePprof,
			"cliValue":    enableHTTPPprof,
		}).Info("overriding pprof option with command-line flag")
		conf.RPC.EnablePprof = enableHTTPPprof
	}

	log.AddHook(hook.NewHook(hook.WithKeys("reqid")))
	log.SetFormatter(baselog.NewFlattenJSONFormatter())
	loggerEntry := rpc.NewLoggerEntry(log.StandardLogger())
	rpc.InitLogging(loggerEntry)

	baseDao, err := dao.InitMysqlDao(&conf.MySQL, &conf.Cache)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init dao layer")
	}

	if runMigration {
		model.RegisterMigrate(baseDao.DB)
	}

	dictDao := model.NewDictDao(baseDao)
	dictService := service.NewDictBizService(dictDao, conf.Cache.DefaultExpires)
	dictAction := action.NewDictAction(dictService, conf.Dict.DefaultPageSize)

	if err := rpc.Serve(
		&conf.RPC,
		loggerEntry,
		func(s *grpc.Server) {
			pb.RegisterPayDictServiceServer(s, dictAction)
		},
		pb.RegisterPayDictServiceHandlerFromEndpoint,
	); err != nil {
		log.WithField("err", err).Fatal("failed to serve")
	}
}
