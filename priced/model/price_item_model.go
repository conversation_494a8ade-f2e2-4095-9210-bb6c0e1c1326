package model

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"fmt"
	"sort"
	"time"

	"github.com/Masterminds/squirrel"

	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// CumulativeType 账单累计周期类型
type CumulativeType int64

const (
	// CumulativeTypeMonth 按月累计
	CumulativeTypeMonth CumulativeType = iota
	// CumulativeTypeDay 按日累计
	CumulativeTypeDay
	// CumulativeTypeHour 按小时累计
	CumulativeTypeHour
)

// Value implements the driver.Valuer interface
func (t CumulativeType) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *CumulativeType) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = CumulativeType(ns.Int64)
	return nil
}

// BillPeriodType 账单周期类型
type BillPeriodType int64

const (
	// BillPeriodTypeMonth 按月出账
	BillPeriodTypeMonth BillPeriodType = iota
	// BillPeriodTypeDay 按日出账
	BillPeriodTypeDay
	// BillPeriodTypeHour 按小时出账
	BillPeriodTypeHour
)

// Value implements the driver.Valuer interface
func (t BillPeriodType) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *BillPeriodType) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = BillPeriodType(ns.Int64)
	return nil
}

// PriceItem model definition
type PriceItem struct {
	ID             uint64 `gorm:"primary_key"`
	PriceID        uint64 `gorm:"index:idx_price_item_price_id_item_id_zone_id_effect_dead_time"`
	ItemID         uint64 `gorm:"index:idx_price_item_price_id_item_id_zone_id_effect_dead_time"`
	ZoneID         uint64 `gorm:"index:idx_price_item_price_id_item_id_zone_id_effect_dead_time"`
	AlgorithmID    uint64 `gorm:"index:idx_price_item_algorithm_id"`
	StairPriceType string `gorm:"type:varchar(64);index:idx_price_item_stair_price_type"`
	IsDisabled     bool
	// 是否默认的计费方式
	IsDefaultAlgorithm bool
	UnitRate           uint64
	Type               PriceTableType
	EffectTime         base.HNS  `gorm:"index:idx_price_item_price_id_item_id_zone_id_effect_dead_time"`
	DeadTime           base.HNS  `gorm:"index:idx_price_item_price_id_item_id_zone_id_effect_dead_time"`
	CreatedAt          time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt          time.Time `sql:"type:DATETIME(6)"`
	ShouldSkip         bool
	// CumulativeCycle 账单累计周期类型
	CumulativeCycle CumulativeType
	// BillPeriodType 账单周期类型
	BillPeriodType BillPeriodType
	// CurrencyType 币种（如 CNY USD）
	CurrencyType base.CurrencyType `gorm:"type:VARCHAR(10);not null"`

	Stairs []PriceItemStair
}

// Clone new a instance of PriceItem same as m
func (m *PriceItem) Clone() PriceItem {
	replica := *m

	if len(m.Stairs) > 0 {
		replica.Stairs = make([]PriceItemStair, len(m.Stairs))
		copy(replica.Stairs, m.Stairs)
	}

	return replica
}

// Hash hash a PriceItem
func (m *PriceItem) Hash() string {
	return fmt.Sprintf("%d-%d-%d-%d-%d-%d",
		m.PriceID,
		m.ItemID,
		m.ZoneID,
		m.AlgorithmID,
		m.EffectTime,
		m.DeadTime)
}

// PriceItemSliceByUpdatedAt attaches the methods of Interface to []PriceItem,
// sorting with updated_at in increasing order.
type PriceItemSliceByUpdatedAt []PriceItem

// Len is the number of elements in the collection.
func (s PriceItemSliceByUpdatedAt) Len() int {
	return len(s)
}

// Less reports whether the element with index i should sort before the element with index j.
func (s PriceItemSliceByUpdatedAt) Less(i, j int) bool {
	return !s[i].UpdatedAt.After(s[j].UpdatedAt)
}

// Swap swaps the elements with indexes i and j.
func (s PriceItemSliceByUpdatedAt) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

// PriceItemDao is data access object of PriceItem model
type PriceItemDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *PriceItemDao) GetCachePrefix() string {
	return "price:price_item:"
}

// GetCacheRefs get cache refs
func (d *PriceItemDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewPriceItemDao is constructor of PriceItemDao
func NewPriceItemDao(base *dao.BaseDao) *PriceItemDao {
	cachePrefix := (*PriceItemDao)(nil).GetCachePrefix()
	return &PriceItemDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id:{ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of PriceItem by id
func (d *PriceItemDao) GetByID(
	id uint64,
	expires ...time.Duration,
) (*PriceItem, error) {
	model := &PriceItem{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// Save insert or update a record of PriceItem by id
func (d *PriceItemDao) Save(
	model *PriceItem,
	expires ...time.Duration,
) error {
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// BulkInsert 批量插入一组 PriceItems
func (d *PriceItemDao) BulkInsert(ctx context.Context, priceItems []PriceItem) error {
	return d.base.ExecuteWithSyncDelCache(func(i any) error {
		return d.base.BulkInsert(ctx, priceItems)
	}, d.keys, nil, d)
}

// Delete delete a record of PriceItem
func (d *PriceItemDao) Delete(model *PriceItem) error {
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// DeleteByID delete a record of PriceItem by id
func (d *PriceItemDao) DeleteByID(id uint64) error {
	model := &PriceItem{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}

// BatchDisableByIDs batch disables records of PriceItem by ids
// NOTE:
// 主要用于批量禁用公开价和成本价（如无必要，后期可考虑改为删除）
func (d *PriceItemDao) BatchDisableByIDs(ids []uint64) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Model(&PriceItem{}).
			Where("id in (?)", ids).
			Update("is_disabled", true).
			Error
	}, d.keys, nil, d)
}

// List select records of PriceItem
func (d *PriceItemDao) List(
	offset int,
	limit int,
	expires ...time.Duration,
) (priceItems []PriceItem, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:offset=%d&limit=%d",
		d.cachePrefix, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Offset(offset).Limit(limit).Find(value).Error
	}, keys, &priceItems, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("offset", offset).WithField("limit", limit)
	}
	return
}

// Count select count of PriceItem
func (d *PriceItemDao) Count(expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count",
		d.cachePrefix,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceItem{}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err)
	}
	return
}

// ListByPriceIDs select records of PriceItem by price_ids
func (d *PriceItemDao) ListByPriceIDs(
	priceIDs []uint64, offset, limit int, expires ...time.Duration,
) (priceItems []PriceItem, err error) {
	if len(priceIDs) <= 0 {
		return
	}
	keys := dao.NewCacheKeysFmt(
		"%s:price_ids=%s&offset=%d&limit=%d",
		d.cachePrefix,
		base.JoinUIntSlice(priceIDs, "_"),
		offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.
			Model(&PriceItem{}).
			Where("price_id IN (?)", priceIDs).
			Offset(offset).
			Limit(limit).
			Find(value).Error
	}, keys, &priceItems, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_ids": priceIDs,
			"offset":    offset,
			"limit":     limit,
		})
	}
	return
}

// ListByPriceID select records of PriceItem by price_id
func (d *PriceItemDao) ListByPriceID(
	priceID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (priceItems []PriceItem, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:price_id=%d&offset=%d&limit=%d",
		d.cachePrefix, priceID, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.
			Model(&PriceItem{}).
			Where("price_id = ?", priceID).
			Offset(offset).
			Limit(limit).
			Find(value).Error
	}, keys, &priceItems, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_id": priceID,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return
}

// CountByPriceID select count of PriceItem by price_id
func (d *PriceItemDao) CountByPriceID(
	priceID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:price_id=%d:count",
		d.cachePrefix, priceID,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceItem{}).Where(&PriceItem{PriceID: priceID}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithField("price_id", priceID)
	}
	return
}

func (d *PriceItemDao) listByPriceIDItemsQuery(
	base *gorm.DB,
	priceID uint64,
	zoneID uint64,
	itemIDs []uint64,
	at base.HNS,
) *gorm.DB {
	itemCnt := len(itemIDs)
	db := base.Where("price_id=?", priceID)

	if itemCnt > 0 {
		db = db.Where("item_id in (?)", itemIDs)
	}

	if zoneID > 0 {
		db = db.Where("zone_id = ?", zoneID)
	}

	return db.Where(
		"effect_time<=? and dead_time>? and is_disabled=0",
		at, at,
	)
}

// CountByPriceIDItemsAtTimePoint select count of PriceItem by price_id
func (d *PriceItemDao) CountByPriceIDItemsAtTimePoint(
	priceID uint64,
	zoneID uint64,
	itemIDs []uint64,
	at base.HNS,
) (count uint64, err error) {

	err = d.base.Execute(func(value any) error {
		return d.listByPriceIDItemsQuery(d.base.Model(&PriceItem{}), priceID, zoneID, itemIDs, at).Count(value).Error
	}, &count)
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"price_id": priceID,
			"zone_id":  zoneID,
			"item_ids": itemIDs,
			"at":       at,
		})
	}
	return
}

// ListByPriceIDItemsAtTimePoint select records of PriceItem by price_id and item_ids
func (d *PriceItemDao) ListByPriceIDItemsAtTimePoint(
	priceID uint64,
	zoneID uint64,
	itemIDs []uint64,
	at base.HNS,
	offset int,
	limit int,
) (priceItems []PriceItem, err error) {

	err = d.base.Execute(func(value any) error {
		return d.listByPriceIDItemsQuery(d.base.Model(&PriceItem{}), priceID, zoneID, itemIDs, at).
			Offset(offset).Limit(limit).Find(value).Error
	}, &priceItems)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_id": priceID,
			"item_ids": itemIDs,
			"zone_id":  zoneID,
			"at":       at,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return
}

// GetItemPriceByTimePointParam  时间点查询，参数选填
type GetItemPriceByTimePointParam struct {
	PriceID            uint64
	ItemID             uint64
	ZoneID             uint64
	AlgorithmID        uint64
	At                 base.HNS
	IsDefaultAlgorithm *bool
	CurrencyType       base.CurrencyType
}

func (p *GetItemPriceByTimePointParam) toWhereClause() (conditionStr string, args []any, err error) {
	andCond := squirrel.And{}

	if p.PriceID != 0 {
		andCond = append(andCond, squirrel.Eq{
			"price_id": p.PriceID,
		})
	}
	if p.ItemID != 0 {
		andCond = append(andCond, squirrel.Eq{
			"item_id": p.ItemID,
		})
	}
	if p.ZoneID != 0 {
		andCond = append(andCond, squirrel.Eq{
			"zone_id": p.ZoneID,
		})
	}

	if p.AlgorithmID != 0 {
		andCond = append(andCond, squirrel.Eq{
			"algorithm_id": p.AlgorithmID,
		})
	}

	// effect_time <= param.At && dead_time > param.At
	if p.At != 0 {
		andCond = append(andCond, squirrel.LtOrEq{
			"effect_time": p.At,
		}, squirrel.Gt{
			"dead_time": p.At,
		})
	}

	if p.IsDefaultAlgorithm != nil {
		andCond = append(andCond, squirrel.Eq{
			"is_default_algorithm": *p.IsDefaultAlgorithm,
		})
	}

	if p.CurrencyType != "" {
		andCond = append(andCond, squirrel.Eq{
			"currency_type": p.CurrencyType,
		})
	}

	andCond = append(andCond, squirrel.Eq{
		"is_disabled": 0,
	})

	return andCond.ToSql()
}

// GetItemPriceByItemIDZoneIDAlgorithmIDAndTimePoint 根据 itemID,zoneID,algorithmID 以及时间点查询 priceItem
func (d *PriceItemDao) GetItemPriceByItemIDZoneIDAlgorithmIDAndTimePoint(
	param *GetItemPriceByTimePointParam,
) (*PriceItem, error) {

	model := &PriceItem{PriceID: param.PriceID, ItemID: param.ItemID}

	conditionStr, args, err := param.toWhereClause()
	if err != nil {
		return nil, errors.Trace(err)
	}

	// "price_id=? and item_id=? and zone_id=? and algorithm_id = ? and currency_type = ? and effect_time<=? and dead_time>? and is_disabled=0",
	err = d.base.Execute(func(value any) error {
		return d.base.Model(&PriceItem{}).Where(conditionStr, args...).First(value).Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_id":      param.PriceID,
			"item_id":       param.ItemID,
			"zone_id":       param.ZoneID,
			"algorithm_id":  param.AlgorithmID,
			"currency_type": param.CurrencyType,
			"effect_time":   param.At,
			"dead_time":     param.At,
		})
	}

	return model, nil
}

// GetItemByPropertiesAtTimePoint query specific price_item which has same properties and takes effect at specified time point
func (d *PriceItemDao) GetItemByPropertiesAtTimePoint(
	priceID uint64,
	itemID uint64,
	zoneID uint64,
	algorithmID uint64,
	stairPriceType string,
	at base.HNS,
	currencyType base.CurrencyType,
) (*PriceItem, error) {

	model := &PriceItem{PriceID: priceID, ItemID: itemID}

	err := d.base.Execute(func(value any) error {
		return d.base.Model(&PriceItem{}).Where(
			"price_id=? and item_id=? and zone_id=? and effect_time<=? and dead_time>? and algorithm_id=? and stair_price_type=? and currency_type=? and is_disabled=0",
			priceID, itemID, zoneID, at, at, algorithmID, stairPriceType, currencyType,
		).First(value).Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_id":         priceID,
			"item_id":          itemID,
			"zone_id":          zoneID,
			"algorithm_id":     algorithmID,
			"stair_price_type": stairPriceType,
			"effect_time":      at,
			"dead_time":        at,
			"currency_type":    currencyType,
		})
	}

	return model, nil
}

// GetItemPriceByTimeRangeParam 时间段查询，参数选填
type GetItemPriceByTimeRangeParam struct {
	PriceID            uint64
	ItemIDs            []uint64
	ZoneID             uint64
	AlgorithmIDs       []uint64
	StartTime          base.HNS
	EndTime            base.HNS
	IsDefaultAlgorithm *bool
	CurrencyType       base.CurrencyType
}

func (p *GetItemPriceByTimeRangeParam) toWhereClause() (conditionStr string, args []any, err error) {
	andCond := squirrel.And{}

	if p.PriceID != 0 {
		andCond = append(andCond, squirrel.Eq{
			"price_id": p.PriceID,
		})
	}
	if len(p.ItemIDs) != 0 {
		andCond = append(andCond, squirrel.Eq{
			"item_id": p.ItemIDs,
		})
	}
	if p.ZoneID != 0 {
		andCond = append(andCond, squirrel.Eq{
			"zone_id": p.ZoneID,
		})
	}

	if len(p.AlgorithmIDs) != 0 {
		andCond = append(andCond, squirrel.Eq{
			"algorithm_id": p.AlgorithmIDs,
		})
	}

	// effect_time < param.EndTime && dead_time > param.StartTime
	if p.StartTime != 0 {
		andCond = append(andCond, squirrel.Lt{
			"effect_time": p.EndTime,
		})
	}

	if p.EndTime != 0 {
		andCond = append(andCond, squirrel.Gt{
			"dead_time": p.StartTime,
		})
	}

	if p.IsDefaultAlgorithm != nil {
		andCond = append(andCond, squirrel.Eq{
			"is_default_algorithm": *p.IsDefaultAlgorithm,
		})
	}

	if p.CurrencyType != "" {
		andCond = append(andCond, squirrel.Eq{
			"currency_type": p.CurrencyType,
		})
	}

	andCond = append(andCond, squirrel.Eq{
		"is_disabled": 0,
	})

	return andCond.ToSql()
}

// GetItemPriceByItemIDZoneIDAlgorithmIDAndTimeRange query price_items which take effect at specified time range
func (d *PriceItemDao) GetItemPriceByItemIDZoneIDAlgorithmIDAndTimeRange(
	param *GetItemPriceByTimeRangeParam,
	expires ...time.Duration,
) (priceItems []PriceItem, err error) {

	sort.Stable(base.Uint64Slice(param.ItemIDs))
	itemIDStr := base.UintSliceJoin(param.ItemIDs, ",")

	conditionStr, args, err := param.toWhereClause()
	if err != nil {
		return nil, errors.Trace(err)
	}

	// "price_id=? and item_id in (?) and zone_id=? and algorithm_id in (?) and effect_time<? and dead_time>? and is_disabled=0",
	err = d.base.Model(&PriceItem{}).Where(conditionStr, args...).Find(&priceItems).Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_id":             param.PriceID,
			"item_ids":             itemIDStr,
			"zone_id":              param.ZoneID,
			"algorithm_ids":        param.AlgorithmIDs,
			"start_time":           param.StartTime,
			"end_time":             param.EndTime,
			"is_default_algorithm": param.IsDefaultAlgorithm,
			"currency_type":        param.CurrencyType,
		})
	}
	return
}

// GetItemPriceByZoneIDAndTimePoint query price_items which take effect at specified time point
func (d *PriceItemDao) GetItemPriceByZoneIDAndTimePoint(
	priceID uint64,
	zoneID uint64,
	at base.HNS,
	currencyType base.CurrencyType,
) (priceItems []PriceItem, err error) {

	err = d.base.Execute(func(value any) error {
		return d.base.Model(&PriceItem{}).Where(
			"price_id=? and zone_id=? and effect_time<=? and dead_time>? and currency_type=? and is_disabled=0",
			priceID, zoneID, at, at, currencyType,
		).Find(value).Error
	}, &priceItems)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_id":      priceID,
			"zone_id":       zoneID,
			"at":            at,
			"currency_type": currencyType,
		})
	}
	return
}

// GetItemPriceByZoneIDAndTimeRange query price_items which take effect at specified time range
func (d *PriceItemDao) GetItemPriceByZoneIDAndTimeRange(
	priceID uint64,
	zoneID uint64,
	startTime base.HNS,
	endTime base.HNS,
	currencyType base.CurrencyType,
) (priceItems []PriceItem, err error) {

	err = d.base.Execute(func(value any) error {
		return d.base.Model(&PriceItem{}).Where(
			"price_id=? and zone_id=? and effect_time<? and dead_time>? and currency_type=? and is_disabled=0",
			priceID, zoneID, endTime, startTime, currencyType,
		).Find(value).Error
	}, &priceItems)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_id":      priceID,
			"zone_id":       zoneID,
			"start_time":    startTime,
			"end_time":      endTime,
			"currency_type": currencyType,
		})
	}
	return
}

// GetItemPriceWithTimeRange 按时间段获取用户全量price_items
func (d *PriceItemDao) GetItemPriceWithTimeRange(
	priceID uint64,
	startTime base.HNS,
	endTime base.HNS,
	currencyType base.CurrencyType,
) (priceItems []PriceItem, err error) {

	err = d.base.Execute(func(value any) error {
		return d.base.Model(&PriceItem{}).Where(
			"price_id=? and effect_time<? and dead_time>? and currency_type=? and is_disabled=0",
			priceID, endTime, startTime, currencyType,
		).Find(value).Error
	}, &priceItems)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_id":      priceID,
			"start_time":    startTime,
			"end_time":      endTime,
			"currency_type": currencyType,
		})
	}
	return
}

// GetItemPriceByItemIDZoneIDAndTimePoint 按时间点获取指定 item_id,zone_id 的 price_items
func (d *PriceItemDao) GetItemPriceByItemIDZoneIDAndTimePoint(
	priceID uint64,
	itemID uint64,
	zoneID uint64,
	at base.HNS,
	currencyType string,
) (priceItems []*PriceItem, err error) {

	err = d.base.Execute(func(value any) error {
		return d.base.Model(&PriceItem{}).Where(
			"price_id=? and item_id = ? and zone_id = ? and effect_time<? and dead_time>? and is_disabled=0 and currency_type=?",
			priceID, itemID, zoneID, at, at, currencyType,
		).Find(value).Error
	}, &priceItems)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_id": priceID,
			"item_id":  itemID,
			"zone_id":  zoneID,
			"at":       at,
		})
	}
	return
}
