package action

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/action"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/eventbus"
	pb "github.com/qbox/pay-sdk/credit"

	"qiniu.io/pay/creditd/action/adapter"
	"qiniu.io/pay/creditd/config"
	"qiniu.io/pay/creditd/model/enums"
	"qiniu.io/pay/creditd/service"
)

// CreditAction implements the server definition in proto buffer
type CreditAction struct {
	*action.BaseAction
	pb.UnimplementedPayCreditServiceServer

	creditSrv             *service.CreditService
	notificationSrv       *service.NotificationService
	thresholdSrv          *service.AvailableBalanceThresholdService
	freezeSrv             *service.FreezeService
	eventbus              eventbus.EventBus
	defaultPageSize       uint64
	delayedNoticeDuration time.Duration

	eventContext context.Context
}

// NewCreditAction is constructor of CreditAction
func NewCreditAction(
	creditSrv *service.CreditService,
	notificationSrv *service.NotificationService,
	thresholdSrv *service.AvailableBalanceThresholdService,
	freezeSrv *service.FreezeService,
	bus eventbus.EventBus,
	defaultPageSize uint64,
	delayedNoticeDuration time.Duration,
) *CreditAction {
	return &CreditAction{
		BaseAction:            action.NewBaseAction(defaultPageSize),
		creditSrv:             creditSrv,
		notificationSrv:       notificationSrv,
		thresholdSrv:          thresholdSrv,
		freezeSrv:             freezeSrv,
		eventbus:              bus,
		defaultPageSize:       defaultPageSize,
		delayedNoticeDuration: delayedNoticeDuration,
	}
}

// CreateCredit 新建授信
func (c *CreditAction) CreateCredit(ctx context.Context, in *pb.Credit) (*pb.Credit, error) {
	m, err := adapter.BuildModelCredit(in)
	if err != nil {
		return nil, errors.Trace(err)
	}

	credit, isUpdateCreditLine, err := c.creditSrv.ManualCredit.CreateCredit(ctx, *m)
	if err != nil {
		return nil, errors.Trace(err)
	}

	pbCredit, err := adapter.BuildPbCreditFromManualCredit(credit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 只对改信用额度的情况发通知，其他改账期等不发通知
	if isUpdateCreditLine {
		// 发送对应通知 (传到 action 调用 noticeSrv)
		// 之前有记录，且为 valid 则发送 updated 类型信息
		if credit.Status == enums.StatusValid {
			// TODO log error
			go c.notificationSrv.SendInstantNotice(ctx, pbCredit.GetUid(),
				enums.EventAdjustLineOrPeriod, c.notificationSrv.BuildNotification(config.UpdatedTmplUnit))
			go c.freezeSrv.TriggerUnfreeze(ctx, pbCredit.GetUid())
		}

		// 之前有记录，且为 unactivated 则发送 created 类型信息，同时之前的延迟通知失效
		// 之前无记录，则发送 created 类型信息
		if credit.Status == enums.StatusUnActivated {
			go func() {
				c.notificationSrv.SendInstantNotice(ctx, pbCredit.GetUid(),
					enums.EventCreate, c.notificationSrv.BuildNotification(config.CreatedTmplUnit))
				c.notificationSrv.RegisterDelayedNotice(ctx, pbCredit.GetUid(), enums.EventCreate)
			}()
		}
	}

	return pbCredit, nil
}

func (c *CreditAction) CreateManualCredit(ctx context.Context, in *pb.ManualCredit) (*pb.ManualCredit, error) {
	m, err := adapter.BuildModelManualCredit(in)
	if err != nil {
		return nil, errors.Trace(err)
	}

	credit, isUpdateCreditLine, err := c.creditSrv.ManualCredit.CreateCredit(ctx, *m)
	if err != nil {
		return nil, errors.Trace(err)
	}

	pbCredit, err := adapter.BuildPbManualCredit(credit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 只对改信用额度的情况发通知，其他改账期等不发通知
	if isUpdateCreditLine {
		// 发送对应通知 (传到 action 调用 noticeSrv)
		// 之前有记录，且为 valid 则发送 updated 类型信息
		if credit.Status == enums.StatusValid {
			// TODO log error
			go c.notificationSrv.SendInstantNotice(ctx, pbCredit.GetUid(),
				enums.EventAdjustLineOrPeriod, c.notificationSrv.BuildNotification(config.UpdatedTmplUnit))
			go c.freezeSrv.TriggerUnfreeze(ctx, pbCredit.GetUid())
		}

		// 之前有记录，且为 unactivated 则发送 created 类型信息，同时之前的延迟通知失效
		// 之前无记录，则发送 created 类型信息
		if credit.Status == enums.StatusUnActivated {
			go func() {
				c.notificationSrv.SendInstantNotice(ctx, pbCredit.GetUid(),
					enums.EventCreate, c.notificationSrv.BuildNotification(config.CreatedTmplUnit))
				c.notificationSrv.RegisterDelayedNotice(ctx, pbCredit.GetUid(), enums.EventCreate)
			}()
		}
	}

	return pbCredit, nil
}

func (c *CreditAction) CancelManualCreditByUID(ctx context.Context, in *pb.UIDParam) (*pb.ManualCredit, error) {
	credit, err := c.creditSrv.ManualCredit.UpdateCreditStatusByUID(ctx, in.GetUid(), enums.EventCancel)
	if err != nil {
		return nil, errors.Trace(err)
	}
	go c.notificationSrv.SendInstantNotice(ctx, credit.UID,
		enums.EventCancel,
		c.notificationSrv.BuildNotification(config.CanceledTmplUnit))
	return adapter.BuildPbManualCredit(credit)
}

func (c *CreditAction) RevokeManualCreditByUID(ctx context.Context, in *pb.UIDParam) (*pb.ManualCredit, error) {
	credit, err := c.creditSrv.ManualCredit.UpdateCreditStatusByUID(ctx, in.GetUid(), enums.EventRevoke)
	if err != nil {
		return nil, errors.Trace(err)
	}

	go c.notificationSrv.SendInstantNotice(ctx, credit.UID,
		enums.EventRevoke,
		c.notificationSrv.BuildNotification(config.RevokedTmplUnit))

	_, err = c.UpsertSystemCreditByUID(ctx, &pb.SystemCredit{
		Uid:          in.GetUid(),
		AssignReason: pb.ASSIGN_REASON_REVOKE_MANUAL_CREDIT,
	})
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbManualCredit(credit)
}

func (c *CreditAction) GetManualCreditsByUID(ctx context.Context, in *pb.ListManualUIDCreditsReq) (*pb.ListManualCreditsResp, error) {
	offset, limit := c.Paging(in)
	start, end, err := adapter.BuildStartEnd(in.GetStart(), in.GetEnd())
	if err != nil {
		return nil, errors.Trace(err)
	}

	params := &service.GetCreditsByUIDParams{
		UID:    in.GetUid(),
		Status: adapter.BuildStatus(in.Status),
		Offset: offset,
		Limit:  limit,
		Start:  start,
		End:    end,
	}

	creditModels, totalCnt, err := c.creditSrv.ManualCredit.GetCreditsByUID(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}

	list := &pb.ListManualCreditsResp{
		Count:   totalCnt,
		Credits: make([]*pb.ManualCredit, len(creditModels)),
	}
	for i, creditModel := range creditModels {
		pbCredit, err := adapter.BuildPbManualCredit(&creditModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.Credits[i] = pbCredit
	}
	return list, nil
}

func (c *CreditAction) ListManualCredits(ctx context.Context, in *pb.ListManualCreditsReq) (*pb.ListManualCreditsResp, error) {
	offset, limit := c.Paging(in)
	start, end, err := adapter.BuildStartEnd(in.GetStart(), in.GetEnd())
	if err != nil {
		return nil, errors.Trace(err)
	}

	params := &service.ListAllCreditsParam{
		Status: adapter.BuildStatus(in.Status),
		Offset: offset,
		Limit:  limit,
		Start:  start,
		End:    end,
	}

	creditModels, totalCnt, err := c.creditSrv.ManualCredit.ListAllCredits(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}

	list := &pb.ListManualCreditsResp{
		Count:   totalCnt,
		Credits: make([]*pb.ManualCredit, len(creditModels)),
	}
	for i, creditModel := range creditModels {
		pbCredit, err := adapter.BuildPbManualCredit(&creditModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.Credits[i] = pbCredit
	}
	return list, nil
}

func (c *CreditAction) ActivateManualCreditByUID(ctx context.Context, in *pb.UIDParam) (*pb.ManualCredit, error) {
	credit, err := c.creditSrv.ManualCredit.UpdateCreditStatusByUID(ctx, in.GetUid(), enums.EventActivate)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// TODO log error
	go c.notificationSrv.SendInstantNotice(ctx, credit.UID,
		enums.EventActivate,
		c.notificationSrv.BuildNotification(config.ActivatedTmplUnit))
	go c.freezeSrv.TriggerUnfreeze(ctx, credit.UID)
	return adapter.BuildPbManualCredit(credit)
}

// GetCreditByUID 获取用户当前有效的信用额度
func (c *CreditAction) GetCreditByUID(ctx context.Context, in *pb.UIDParam) (*pb.Credit, error) {
	manualCredits, total, err := c.creditSrv.ManualCredit.GetCreditsByUID(ctx, &service.GetCreditsByUIDParams{
		UID:    in.GetUid(),
		Status: []enums.CreditStatus{enums.StatusValid},
		Offset: 0,
		Limit:  1,
	})
	if err != nil {
		return nil, errors.Trace(err)
	}
	if total != 0 {
		manualCredit := manualCredits[0]
		return adapter.BuildPbCreditFromManualCredit(&manualCredit)
	}
	systemCredit, err := c.creditSrv.SystemCredit.GetSystemCreditByUID(ctx, in.GetUid())
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbCreditFromSystemCredit(systemCredit, c.creditSrv.SystemCredit.L10nProvider.DefaultLocalizer())
}

func (c *CreditAction) UpsertSystemCreditByUID(ctx context.Context, in *pb.SystemCredit) (*pb.SystemCredit, error) {
	systemCredit, err := adapter.BuildModelSystemCredit(in)
	if err != nil {
		return nil, errors.Trace(err)
	}
	newSystemCredit, isUpdated, err := c.creditSrv.SystemCredit.UpsertSystemCredit(ctx, *systemCredit)
	if err != nil {
		return nil, errors.Trace(err)
	}
	go func() {
		_, total, err := c.creditSrv.ManualCredit.GetCreditsByUID(ctx, &service.GetCreditsByUIDParams{
			UID:    in.GetUid(),
			Status: []enums.CreditStatus{enums.StatusUnActivated, enums.StatusValid},
			Offset: 0,
			Limit:  1,
		})
		if err != nil {
			return
		}
		if total > 0 {
			return
		}
		if systemCredit.AssignReason != enums.AssignReasonRevokeManualCredit && !isUpdated {
			return
		}
		c.notificationSrv.SendInstantNotice(ctx, in.GetUid(),
			enums.EventAdjustLineOrPeriod,
			c.notificationSrv.BuildNotification(config.UpdatedTmplUnit))
	}()
	return adapter.BuildPbSystemCredit(newSystemCredit)
}

func (c *CreditAction) GetSystemCreditByUID(ctx context.Context, in *pb.UIDParam) (*pb.SystemCredit, error) {
	systemCredit, err := c.creditSrv.SystemCredit.GetSystemCreditByUID(ctx, in.GetUid())
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbSystemCredit(systemCredit)
}

func (c *CreditAction) ListSystemCreditHistory(ctx context.Context, in *pb.ListSystemCreditHistoryReq) (*pb.ListSystemCreditHistoryResp, error) {
	offset, limit := c.Paging(in)
	count, err := c.creditSrv.SystemCreditHistory.CountSystemCreditHistory(ctx, in.GetUid())
	if err != nil {
		return nil, err
	}
	histories, err := c.creditSrv.SystemCreditHistory.ListSystemCreditHistory(ctx, in.GetUid(), offset, limit)
	if err != nil {
		return nil, err
	}
	var result []*pb.SystemCreditHistory
	for _, history := range histories {
		creditHistory, err := adapter.BuildPbSystemCreditHistory(history)
		if err != nil {
			return nil, errors.Trace(err)
		}
		result = append(result, creditHistory)
	}
	return &pb.ListSystemCreditHistoryResp{
		Histories: result,
		Count:     count,
	}, nil
}

// SendExpiredNotices sends expired notices 发送到期信息
func (c *CreditAction) SendExpiredNotices(ctx context.Context, in *pb.SendExpiredNoticesReq) (*pb.SendExpiredNoticesResp, error) {
	// 目前过期的通知一定是创建完成、要求激活的情况
	total, failureCount, err := c.notificationSrv.
		SendExpiredNotices(ctx, c.notificationSrv.BuildNotification(config.NeedActivationTmplUnit))
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.SendExpiredNoticesResp{
		Total:        uint64(total),
		FailureCount: uint64(failureCount),
	}, nil
}
