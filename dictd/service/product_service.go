package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/dictd/model"
)

// GetProductByID 根据产品ID获取Product记录
func (s *DictBizService) GetProductByID(
	ctx context.Context,
	id uint64,
) (*model.Product, error) {
	return s.dictDao.Product.GetByID(id, s.cacheExpires)
}

// GetProductByCode 根据code获取Product记录
func (s *DictBizService) GetProductByCode(
	ctx context.Context,
	code string,
) (*model.Product, error) {
	return s.dictDao.Product.GetByCode(code, s.cacheExpires)
}

// ListAllProducts 获取所有Product记录
func (s *DictBizService) ListAllProducts(
	ctx context.Context,
	ignoreDeprecated bool,
	offset int,
	limit int,
) ([]model.Product, error) {
	return s.dictDao.Product.ListAll(ignoreDeprecated, offset, limit, s.cacheExpires)
}

// ListProductByItemIDs 通过itemid筛选product
func (s *DictBizService) ListProductByItemIDs(
	ctx context.Context,
	itemIDs []uint64,
) ([]model.Product, error) {
	return s.dictDao.Product.ListByItemIDs(itemIDs)
}

// CountAllProducts 获取所有Product记录的条数
func (s *DictBizService) CountAllProducts(
	ctx context.Context,
	ignoreDeprecated bool,
) (uint64, error) {
	return s.dictDao.Product.CountAll(ignoreDeprecated, s.cacheExpires)
}

// CreateProduct 创建Product记录
func (s *DictBizService) CreateProduct(
	ctx context.Context,
	product *model.Product,
) (*model.Product, error) {
	product.ID = 0
	err := s.dictDao.Product.Save(product, s.cacheExpires)
	return product, err
}

// UpdateProductByID 根据产品ID更新Product记录
func (s *DictBizService) UpdateProductByID(
	ctx context.Context,
	id uint64,
	product *model.Product,
) (*model.Product, error) {
	product.ID = id
	err := s.dictDao.Product.Save(product, s.cacheExpires)
	return product, err
}

// UpdateProductByCode 根据code更新Product记录
func (s *DictBizService) UpdateProductByCode(
	ctx context.Context,
	code string,
	product *model.Product,
) (*model.Product, error) {
	p, err := s.GetProductByCode(ctx, code)
	if err != nil {
		return nil, errors.Trace(err).WithField("code", code)
	}
	return s.UpdateProductByID(ctx, p.ID, product)
}

// DeleteProductByID 根据产品ID删除Product记录
func (s *DictBizService) DeleteProductByID(
	ctx context.Context,
	id uint64,
) (*model.Product, error) {
	product, err := s.GetProductByID(ctx, id)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	err = s.dictDao.Product.Delete(product)
	return product, err
}

// DeleteProductByCode 根据code删除Product记录
func (s *DictBizService) DeleteProductByCode(
	ctx context.Context,
	code string,
) (*model.Product, error) {
	product, err := s.GetProductByCode(ctx, code)
	if err != nil {
		return nil, errors.Trace(err).WithField("code", code)
	}
	err = s.dictDao.Product.Delete(product)
	return product, err
}
