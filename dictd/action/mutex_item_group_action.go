package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/dict"
	"google.golang.org/protobuf/types/known/emptypb"
	"qiniu.io/pay/dictd/action/adapter"
)

func (a *DictAction) ListMutexItemGroups(ctx context.Context, empty *emptypb.Empty) (*pb.MutexItemGroupList, error) {
	allMutexGroups, err := a.dictBizSrv.ListAllMutexItemGroups(ctx)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbMutexItemGroupList(allMutexGroups), nil
}

func (a *DictAction) CreateMutexItemGroup(ctx context.Context, group *pb.MutexItemGroup) (*emptypb.Empty, error) {
	mutexItemGroup := adapter.BuildMutexItemGroup(group)
	err := a.dictBizSrv.CreateOrUpdateMutexItemGroup(ctx, mutexItemGroup)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (a *DictAction) UpdateMutexItemGroup(ctx context.Context, group *pb.MutexItemGroup) (*emptypb.Empty, error) {
	mutexItemGroup := adapter.BuildMutexItemGroup(group)
	err := a.dictBizSrv.CreateOrUpdateMutexItemGroup(ctx, mutexItemGroup)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
