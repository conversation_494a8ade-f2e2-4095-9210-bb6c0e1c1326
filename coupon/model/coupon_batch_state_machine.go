package model

import (
	"time"
)

// ComputeNextStateForCouponBatch 计算 CouponBatch 的下一个状态
//
// now < StartTime => 未开始
// now >= EndTime => 已结束
// NumActivated >= BatchSize => 已结束
// IsSuspended == true => 已冻结 -- TODO
// else => 发放中
func ComputeNextStateForCouponBatch(
	cb *CouponBatch,
	epoch time.Time,
) CouponBatchState {
	// XXX 现在没有另外的字段存冻结状态
	if cb.State == CouponBatchStateSuspended {
		return CouponBatchStateSuspended
	}

	if cb.State == CouponBatchStateInvalid {
		return CouponBatchStateInvalid
	}

	if epoch.Before(cb.StartTime.TimeIn(epoch.Location())) {
		return CouponBatchStateNotStarted
	}

	if !epoch.Before(cb.EndTime.TimeIn(epoch.Location())) {
		return CouponBatchStateEnded
	}

	if cb.NumActivated >= cb.BatchSize {
		return CouponBatchStateEnded
	}

	return CouponBatchStateInProgress
}
