package action_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/pay-sdk/wallet"
	"github.com/stretchr/testify/assert"
)

func TestScopeItemGroupMapInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	walletClient := sandbox.walletClient

	const scopeID uint64 = 1
	ms := []wallet.ScopeItemGroupMap{
		{
			ScopeId:    scopeID,
			GroupId:    1,
			IsExcluded: false,
		},
		{
			ScopeId:    scopeID,
			GroupId:    2,
			IsExcluded: false,
		},
		{
			ScopeId:    scopeID,
			GroupId:    3,
			IsExcluded: false,
		},
	}
	assertFields := func(_map *wallet.ScopeItemGroupMap, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(ms) }) {
			assert.Equal(t, ms[n].GetScopeId(), _map.GetScopeId(), msgAndArgs...)
			assert.Equal(t, ms[n].GetGroupId(), _map.GetGroupId(), msgAndArgs...)
			assert.Equal(t, ms[n].GetIsExcluded(), _map.GetIsExcluded(), msgAndArgs...)
		}
	}
	ids := make(map[uint64]int, len(ms)+1)
	{
		for n, m := range ms {
			_map, err := walletClient.CreateScopeItemGroupMap(context.Background(), &m)
			if assert.NoError(t, err, "CreateScopeItemGroupMap") {
				assert.NotZero(t, _map.GetId(), "CreateScopeItemGroupMap")
				ids[_map.GetId()] = n
			}

			_map, err = walletClient.GetScopeItemGroupMapByID(context.Background(), &wallet.IDParam{Id: _map.GetId()})
			if assert.NoError(t, err, "GetScopeItemGroupMapByID") {
				assertFields(_map, n, "GetScopeItemGroupMapByID")
			}
		}

		count, err := walletClient.CountAllScopeItemGroupMaps(context.Background(), &empty.Empty{})
		if assert.NoError(t, err, "CountAllScopeItemGroupMaps") {
			assert.Len(t, ms, int(count.GetCount()), "CountAllScopeItemGroupMaps")
		}
	}

	list, err := walletClient.ListAllScopeItemGroupMaps(context.Background(), &wallet.PagingParam{Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListAllScopeItemGroupMaps") {
		assert.Len(t, ms, int(list.GetCount()), "ListAllScopeItemGroupMaps")
		for _, _map := range list.GetScopeItemGroupMaps() {
			if assert.Contains(t, ids, _map.GetId(), "ListAllScopeItemGroupMaps->Recv") {
				assertFields(_map, ids[_map.GetId()], "ListAllScopeItemGroupMaps->Recv")
			}
		}
	}
	{
		list, err := walletClient.ListScopeItemGroupMapsByScopeID(context.Background(), &wallet.IDPagingParam{Id: scopeID, PageSize: 10, Page: 1})
		if assert.NoError(t, err, "ListScopeItemGroupMapsByScopeID") {
			assert.Len(t, ms, int(list.GetCount()), "ListScopeItemGroupMapsByScopeID")
			for _, _map := range list.GetScopeItemGroupMaps() {
				if assert.Contains(t, ids, _map.GetId(), "ListScopeItemGroupMapsByScopeID->Recv") {
					assertFields(_map, ids[_map.GetId()], "ListScopeItemGroupMapsByScopeID->Recv")
				}
			}
		}
		count, err := walletClient.CountScopeItemGroupMapsByScopeID(context.Background(), &wallet.IDParam{Id: scopeID})
		if assert.NoError(t, err, "CountScopeItemGroupMapsByScopeID") {
			assert.Len(t, ms, int(count.GetCount()), "CountScopeItemGroupMapsByScopeID")
		}
	}
	{
		const testGroupID uint64 = 1
		ms = append(ms, wallet.ScopeItemGroupMap{
			ScopeId:    2,
			GroupId:    testGroupID,
			IsExcluded: false,
		})
		scope, err := walletClient.CreateScopeItemGroupMap(context.Background(), &ms[len(ms)-1])
		if assert.NoError(t, err, "CreateScopeItemGroupMap") {
			assert.NotZero(t, scope.GetId(), "CreateScopeItemGroupMap")
			ids[scope.GetId()] = len(ms) - 1
		}

		list, err = walletClient.ListScopeItemGroupMapsByGroupID(context.Background(), &wallet.IDPagingParam{Id: testGroupID, PageSize: 10, Page: 1})
		if assert.NoError(t, err, "ListScopeItemGroupMapsByGroupID") {
			assert.Equal(t, 2, int(list.GetCount()), "ListScopeItemGroupMapsByGroupID")
			for _, _map := range list.GetScopeItemGroupMaps() {
				assert.Equal(t, testGroupID, _map.GetGroupId(), "ListScopeItemGroupMapsByGroupID->group.ID")
				if assert.Contains(t, ids, _map.GetId(), "ListScopeItemGroupMapsByGroupID->Recv") {
					assertFields(_map, ids[_map.GetId()], "ListScopeItemGroupMapsByGroupID->Recv")
				}
			}
		}
		count, err := walletClient.CountScopeItemGroupMapsByGroupID(context.Background(), &wallet.IDParam{Id: testGroupID})
		if assert.NoError(t, err, "CountScopeItemGroupMapsByGroupID") {
			assert.Equal(t, uint64(2), count.GetCount(), "CountScopeItemGroupMapsByGroupID")
		}
	}
	for id, n := range ids {
		_map, err := walletClient.GetScopeItemGroupMapByID(context.Background(), &wallet.IDParam{Id: id})
		if assert.NoError(t, err, "GetScopeItemGroupMapByID") {
			assertFields(_map, n, "GetScopeItemGroupMapByID")
		}
		ms[n].GroupId += 100
		_map.GroupId += 100
		_map, err = walletClient.UpdateScopeItemGroupMapByID(context.Background(), &wallet.IDScopeItemGroupMapParam{Id: _map.GetId(), ScopeItemGroupMap: _map})
		if assert.NoError(t, err, "UpdateScopeItemGroupMapByID") {
			assert.Equal(t, ms[n].GetGroupId(), _map.GetGroupId(), "UpdateScopeItemGroupMapByID")
		}
	}
	for id, n := range ids {
		_map, err := walletClient.DeleteScopeItemGroupMapByID(context.Background(), &wallet.IDParam{Id: id})
		if assert.NoError(t, err, "DeleteScopeItemGroupMapByID") {
			assertFields(_map, n, "DeleteScopeItemGroupMapByID")
		}
	}
}
