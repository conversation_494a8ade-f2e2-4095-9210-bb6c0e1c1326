package model

import (
	"time"
)

// ComputeNextStateForCoupon 计算 Coupon 的下一个状态
//
// now < EffectTime => 未生效
// now >= DeadTime => 已过期
// Balance <= 0 => 已用完
// IsSuspended => 已冻结 TODO
// select count(couponID) from coupon_lock_records; count > 0 => 已锁定
// else => 可使用
func ComputeNextStateForCoupon(
	c *Coupon,
	epoch time.Time,
) CouponState {
	// 这些状态原样返回
	// XXX 目前没有单独 bool 字段描述这些状态
	switch c.State {
	case CouponStateCanceled, CouponStateLocked, CouponStateSuspended, CouponStateExpired, CouponStateExhausted:
		return c.State
	}

	if c.Balance <= 0 {
		return CouponStateExhausted
	}

	if epoch.Before(c.EffectTime.TimeIn(epoch.Location())) {
		return CouponStatePending
	}

	if !epoch.Before(c.DeadTime.TimeIn(epoch.Location())) {
		return CouponStateExpired
	}

	return CouponStateUsable
}
