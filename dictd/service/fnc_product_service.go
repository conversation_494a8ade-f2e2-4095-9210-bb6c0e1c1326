package service

import (
	"context"

	"qiniu.io/pay/dictd/model"
)

func (s *DictBizService) GetFncProductByID(
	ctx context.Context,
	param uint64,
) (*model.FncProduct, error) {
	return s.dictDao.FncProduct.GetByID(param, s.cacheExpires)
}

func (s *DictBizService) ListFncProductsByIDs(
	ctx context.Context,
	param []uint64,
) ([]*model.FncProduct, error) {
	return s.dictDao.FncProduct.ListByIDs(param, s.cacheExpires)
}

func (s *DictBizService) CreateFncProduct(
	ctx context.Context,
	param *model.FncProduct,
) (*model.FncProduct, error) {
	err := s.dictDao.FncProduct.Save(param, s.cacheExpires)
	if err != nil {
		return nil, err
	}
	return param, nil
}
