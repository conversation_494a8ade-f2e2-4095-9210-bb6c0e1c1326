package model

import (
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// CouponDiscount model definition
type CouponDiscount struct {
	ID          uint64 `gorm:"primary_key"`
	Code        string `gorm:"type:varchar(128);unique_index:idx_coupon_discount_code"`
	Type        string `gorm:"type:varchar(128)"`
	ScopeID     uint64 `gorm:"index:idx_coupon_discount_scope_id"`
	Name        string `gorm:"type:varchar(128)"`
	Description string `gorm:"type:varchar(512)"`
	Remark      string `gorm:"type:varchar(512)"`
	Discount    uint64
	EffectTime  base.HNS
	DeadTime    base.HNS
	CreatedAt   time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt   time.Time `sql:"type:DATETIME(6)"`
}

// CouponDiscountDao is data access object of CouponDiscount model
type CouponDiscountDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *CouponDiscountDao) GetCachePrefix() string {
	return "wallet:coupon_discount:"
}

// GetCacheRefs get cache refs
func (d *CouponDiscountDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewCouponDiscountDao is constructor of CouponDiscountDao
func NewCouponDiscountDao(base *dao.BaseDao) *CouponDiscountDao {
	cachePrefix := (*CouponDiscountDao)(nil).GetCachePrefix()
	return &CouponDiscountDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of CouponDiscount by id
func (d *CouponDiscountDao) GetByID(id uint64, expires ...time.Duration) (*CouponDiscount, error) {
	model := &CouponDiscount{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// GetByCode select a record of CouponDiscount by code
func (d *CouponDiscountDao) GetByCode(code string, expires ...time.Duration) (*CouponDiscount, error) {
	model := &CouponDiscount{Code: code}
	keys := dao.NewCacheKeysFmt(
		"%s:code:%s",
		d.cachePrefix, code,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, "`coupon_discounts`.`code` = ?", code).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("code", code)
	}
	return model, nil
}

type ListCouponConds struct {
	// CodePattern Coupon Code 包含的子串
	CodePattern string

	Offset int
	Limit  int
}

func (p *ListCouponConds) toWhereClause() (conditionStr string, args []any, err error) {
	andCond := squirrel.And{}

	if p.CodePattern != "" {
		andCond = append(andCond, squirrel.Like{
			"code": fmt.Sprintf("%%%s%%", dao.EscapeForMySQLLike(p.CodePattern)),
		})
	}

	return andCond.ToSql()
}

// ListByConds select records by conditions
func (d *CouponDiscountDao) ListByConds(
	p *ListCouponConds,
	expires ...time.Duration,
) (list []CouponDiscount, err error) {
	where, args, err := p.toWhereClause()
	if err != nil {
		return nil, err
	}

	keys := dao.NewCacheKeysFmt(
		"%s:list:pat=%s&offset=%d&limit=%d",
		d.cachePrefix,
		p.CodePattern,
		p.Offset,
		p.Limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&CouponDiscount{}).
			Where(where, args...).
			Offset(p.Offset).
			Limit(p.Limit).
			Order("created_at DESC").
			Find(value).
			Error
	}, keys, &list, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"code_pattern": p.CodePattern,
			"offset":       p.Offset,
			"limit":        p.Limit,
		})
	}
	return
}

// CountAll select count of all records
func (d *CouponDiscountDao) CountAll(expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:count",
		d.cachePrefix,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&CouponDiscount{}).Count(value).Error
	}, keys, &count, expires...)
	return
}

// ListByScopeID select records by scope_id
func (d *CouponDiscountDao) ListByScopeID(scopeID uint64, offset, limit int, expires ...time.Duration) (list []CouponDiscount, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:scope_id=%d&offset=%d&limit=%d",
		d.cachePrefix, scopeID, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&CouponDiscount{ScopeID: scopeID}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &list, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return
}

// CountByScopeID select count of records by scope_id
func (d *CouponDiscountDao) CountByScopeID(scopeID uint64, expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:scope_id=%d",
		d.cachePrefix, scopeID,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&CouponDiscount{}).Where(&CouponDiscount{ScopeID: scopeID}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
		})
	}
	return
}

// ListByIDs select records by ids
func (d *CouponDiscountDao) ListByIDs(ids []uint64, expires ...time.Duration) (list []CouponDiscount, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:ids=%s",
		d.cachePrefix, base.UintSliceJoin(ids, ","),
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where("`id` in (?)", ids).Find(value).Error
	}, keys, &list, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"ids": ids,
		})
	}
	return
}

// Save insert or update a record of CouponDiscount by id
func (d *CouponDiscountDao) Save(model *CouponDiscount, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// Delete delete a record of CouponDiscount
func (d *CouponDiscountDao) Delete(model *CouponDiscount) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
}
