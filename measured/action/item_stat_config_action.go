package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/action/adapter"
)

// impl PayMeasureServiceServer for MeasureAction

// GetItemStatConfigByID 根据 ID 查询单个计费项计量源配置
func (a *MeasureAction) GetItemStatConfigByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.ItemStatConfig, error) {
	obj, err := a.srv.GetItemStatConfigByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemStatConfig(obj)
}

// GetItemStatConfigByItemID 根据计费项 ID 查询单个计费项计量源配置
func (a *MeasureAction) GetItemStatConfigByItemID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.ItemStatConfig, error) {
	obj, err := a.srv.GetItemStatConfigByItemID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemStatConfig(obj)
}

// CreateItemStatConfig 创建一个计费项计量源配置
func (a *MeasureAction) CreateItemStatConfig(
	ctx context.Context,
	item *pb.ItemStatConfig,
) (*pb.ItemStatConfig, error) {
	obj, err := adapter.BuildModelItemStatConfig(item)
	if err != nil {
		return nil, err
	}
	obj, err = a.srv.CreateItemStatConfig(ctx, obj)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemStatConfig(obj)
}

// DeleteItemStatConfigByID 根据 ID 删除一个计费项计量源配置
func (a *MeasureAction) DeleteItemStatConfigByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.ItemStatConfig, error) {
	obj, err := a.srv.DeleteItemStatConfigByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemStatConfig(obj)
}
