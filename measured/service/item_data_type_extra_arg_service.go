package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/measured/model"
)

// GetItemDataTypeExtraArgByID 根据 ID 查询单个计费项数据类型计量查询附加参数
func (s *MeasureBizService) GetItemDataTypeExtraArgByID(
	ctx context.Context,
	id uint64,
) (*model.ItemDataTypeExtraArg, error) {
	return s.dao.ItemDataTypeExtraArg.GetByID(id, s.cacheExpires)
}

// ListItemDataTypeExtraArgsByItemDataTypeID 根据计费项数据类型 ID 列出计量查询附加参数列表
func (s *MeasureBizService) ListItemDataTypeExtraArgsByItemDataTypeID(
	ctx context.Context,
	idtID uint64,
	offset int,
	limit int,
) ([]model.ItemDataTypeExtraArg, error) {
	return s.dao.ItemDataTypeExtraArg.ListByItemDataTypeID(
		idtID,
		offset,
		limit,
		s.cacheExpires,
	)
}

// CountItemDataTypeExtraArgsByItemDataTypeID 根据计费项数据类型 ID 获取计量查询附加参数数量
func (s *MeasureBizService) CountItemDataTypeExtraArgsByItemDataTypeID(
	ctx context.Context,
	idtID uint64,
) (uint64, error) {
	return s.dao.ItemDataTypeExtraArg.CountByItemDataTypeID(idtID, s.cacheExpires)
}

// CreateItemDataTypeExtraArg 创建一个计费项数据类型计量查询附加参数
func (s *MeasureBizService) CreateItemDataTypeExtraArg(
	ctx context.Context,
	obj *model.ItemDataTypeExtraArg,
) (*model.ItemDataTypeExtraArg, error) {
	obj.ID = 0
	err := s.dao.ItemDataTypeExtraArg.Save(obj, s.cacheExpires)
	return obj, err
}

// DeleteItemDataTypeExtraArgByID 根据 ID 删除一个计费项数据类型计量查询附加参数
func (s *MeasureBizService) DeleteItemDataTypeExtraArgByID(
	ctx context.Context,
	id uint64,
) (*model.ItemDataTypeExtraArg, error) {
	obj, err := s.dao.ItemDataTypeExtraArg.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	err = s.dao.ItemDataTypeExtraArg.DeleteByID(id)
	return obj, err
}
