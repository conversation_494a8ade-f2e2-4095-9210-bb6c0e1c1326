package model

import (
	"sort"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/qpay/enums"
)

// ScopeProductMap model definition
type ScopeProductMap struct {
	ID        uint64 `gorm:"primary_key"`
	ScopeID   uint64 `gorm:"unique_index:idx_scope_product_map_scope_id_product_id"`
	ProductID uint64 `gorm:"unique_index:idx_scope_product_map_scope_id_product_id"`
	// productCode 冗余字段，不存数据库
	ProductCode string `gorm:"-"`
	IsExcluded  bool
	CreatedAt   time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt   time.Time `sql:"type:DATETIME(6)"`
}

// TableName return table name
func (*ScopeProductMap) TableName() string {
	return "scope_product_map"
}

// ScopeProductMapDao data access object of ScopeProductMap model
type ScopeProductMapDao struct {
	base *dao.BaseDao
}

// NewScopeProductMapDao constructor of NewScopeProductMapDao
func NewScopeProductMapDao(base *dao.BaseDao) *ScopeProductMapDao {
	return &ScopeProductMapDao{
		base: base,
	}
}

// GetByID select a record by id
func (d *ScopeProductMapDao) GetByID(
	id uint64,
	expires ...time.Duration,
) (*ScopeProductMap, error) {
	model := &ScopeProductMap{}
	err := d.base.Where(&ScopeProductMap{ID: id}).
		First(model).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// ListAll select all records
func (d *ScopeProductMapDao) ListAll(
	offset int,
	limit int,
	expires ...time.Duration,
) (list []ScopeProductMap, err error) {
	err = d.base.Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": offset,
			"limit":  limit,
		})
	}
	return
}

// CountAll select count of all records
func (d *ScopeProductMapDao) CountAll(
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&ScopeProductMap{}).
		Count(&count).
		Error
	return count, err
}

// ListByScopeID select records by scope_id
func (d *ScopeProductMapDao) ListByScopeID(
	scopeID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (list []ScopeProductMap, err error) {
	err = d.base.Where(&ScopeProductMap{ScopeID: scopeID}).
		Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return
}

// CountByScopeID select count of records by product_id
func (d *ScopeProductMapDao) CountByScopeID(
	scopeID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&ScopeProductMap{}).
		Where(&ScopeProductMap{ScopeID: scopeID}).
		Count(&count).
		Error
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
		})
	}
	return
}

// ListByProductID select records by product_id
func (d *ScopeProductMapDao) ListByProductID(
	productID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (list []ScopeProductMap, err error) {
	err = d.base.Where(&ScopeProductMap{ProductID: productID}).
		Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"product_id": productID,
			"offset":     offset,
			"limit":      limit,
		})
	}
	return
}

// CountByProductID select count of records by product_id
func (d *ScopeProductMapDao) CountByProductID(
	productID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&ScopeProductMap{}).
		Where(&ScopeProductMap{ProductID: productID}).
		Count(&count).
		Error
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"product_id": productID,
		})
	}
	return
}

// Save insert or update a record
func (d *ScopeProductMapDao) Save(
	model *ScopeProductMap,
	expires ...time.Duration,
) error {
	return d.base.Save(model).Error
}

// Delete delete a record
func (d *ScopeProductMapDao) Delete(
	model *ScopeProductMap,
) error {
	return d.base.Delete(model).Error
}

// ListScopeIDByProductIDs 根据 ProductID 列表批量查询获取 ScopeID 列表
func (d *ScopeProductMapDao) ListScopeIDByProductIDs(
	productIDs []uint64,
) ([]uint64, error) {
	type scopeIDList []struct {
		ID      int    `gorm:"id"`
		ScopeID uint64 `gorm:"scope_id"`
	}

	var scopeIDs scopeIDList
	var allScopeIDs scopeIDList
	limit := 3000
	id := 0

	// 由于 product_id 字段的区分度很低，加索引效率也提升不多，因此采用循环的方式，每次查找 3000 条
	for {
		err := d.base.Model(&ScopeProductMap{}).
			Where("product_id in (?) and id > ?", productIDs, id).
			Limit(limit).Select("id,scope_id").
			Order("id asc").Scan(&scopeIDs).Error
		if err != nil {
			return nil, errors.Trace(err).WithFields(errors.Fields{
				"product_ids": productIDs,
			})
		}

		length := len(scopeIDs)
		if length == 0 {
			break
		}

		id = scopeIDs[length-1].ID
		allScopeIDs = append(allScopeIDs, scopeIDs...)
	}

	// 排序并去重
	distinctScopeIDs := make([]uint64, 0)
	for _, scopeID := range allScopeIDs {
		distinctScopeIDs = append(distinctScopeIDs, scopeID.ScopeID)
	}
	distinctScopeIDs = base.UniqueIntSlice(distinctScopeIDs)
	sort.Sort(base.Uint64Slice(distinctScopeIDs))
	return distinctScopeIDs, nil
}

type ProductIDVoucherQuotaList []struct {
	ProductID uint64
	AssetType enums.AssetType
	Quota     int64
}

// CalculateUserProductVoucherQuota 根据用户和 code 列表查询指定产品线的账单抵用券总金额
func (d *ScopeProductMapDao) CalculateUserProductVoucherQuota(
	UID uint64,
	codes []string,
	productIDs []uint64,
) (ProductIDVoucherQuotaList, error) {
	resultMap := ProductIDVoucherQuotaList{}
	err := d.base.Model(&ScopeProductMap{}).
		Joins("LEFT JOIN `wallet_voucher` on `wallet_voucher`.scope_id = `scope_product_map`.scope_id").
		Where("uid=? AND code in (?) AND product_id IN (?) AND status <> ? AND record_status = ?", UID, codes, productIDs, enums.VoucherStatusCancel, enums.RecordStatusActive).
		Group("`scope_product_map`.product_id, `wallet_voucher`.asset_type").
		Select("product_id, asset_type, sum(quota) as quota").
		Scan(&resultMap).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid":         UID,
			"product_ids": productIDs,
			"codes":       codes,
		})
	}
	return resultMap, nil
}
