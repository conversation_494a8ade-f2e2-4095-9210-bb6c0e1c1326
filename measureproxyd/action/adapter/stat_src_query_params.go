package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measureproxyd/model"
)

// BuildPbStatSrcQueryParams converts from model.StatSrcQueryParams to the protobuf type.
func BuildPbStatSrcQueryParams(x *model.StatSrcQueryParams) (*pb.StatSrcQueryParams, error) {
	query := make([]*pb.KV, len(x.Query))
	for i, kv := range x.Query {
		query[i] = BuildPbKV(&kv)
	}

	return &pb.StatSrcQueryParams{
		Uid:      x.UID,
		Begin:    timestamppb.New(x.Begin),
		End:      timestamppb.New(x.End),
		HaveZone: x.HaveZone,
		ZoneCode: x.ZoneCode,
		Key:      x.Key,
		G:        x.G,
		Select:   x.Select,
		Query:    query,
		Src:      x.Src,
		Resource: &pb.Resource{
			ResourceName: x.Resource.ResourceName,
			ResourceType: x.Resource.ResourceType,
		},
		Debug: x.Debug,
	}, nil
}

// BuildModelStatSrcQueryParams converts from pb.StatSrcQueryParams to the model layer type.
func BuildModelStatSrcQueryParams(x *pb.StatSrcQueryParams) (*model.StatSrcQueryParams, error) {
	var begin, end time.Time
	if x.Begin != nil {
		err := x.Begin.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		begin = x.Begin.AsTime()
	}

	if x.End != nil {
		err := x.End.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		end = x.End.AsTime()
	}

	query := make([]model.KV, len(x.Query))
	for i, kv := range x.Query {
		query[i] = *BuildModelKV(kv)
	}

	var res model.Resource
	if x.Resource != nil {
		res = model.Resource{
			ResourceName: x.Resource.ResourceName,
			ResourceType: x.Resource.ResourceType,
		}
	}

	return &model.StatSrcQueryParams{
		UID:      x.Uid,
		Begin:    begin,
		End:      end,
		HaveZone: x.HaveZone,
		ZoneCode: x.ZoneCode,
		Key:      x.Key,
		G:        x.G,
		Select:   x.Select,
		Query:    query,
		Src:      x.Src,
		Resource: res,
		Debug:    x.Debug,
	}, nil
}
