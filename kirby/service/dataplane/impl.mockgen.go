// Code generated by MockGen. DO NOT EDIT.
// Source: kirby/service/dataplane/impl.go

package dataplane

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	kirbypb "github.com/qbox/bo-sdk/kirbypb"
	adminModel "qiniu.io/pay/kirby/model/adminModel"
	dataplaneModel "qiniu.io/pay/kirby/model/dataplaneModel"
)

// MockIKirbyDataplane is a mock of IKirbyDataplane interface.
type MockIKirbyDataplane struct {
	ctrl     *gomock.Controller
	recorder *MockIKirbyDataplaneMockRecorder
}

// MockIKirbyDataplaneMockRecorder is the mock recorder for MockIKirbyDataplane.
type MockIKirbyDataplaneMockRecorder struct {
	mock *MockIKirbyDataplane
}

// NewMockIKirbyDataplane creates a new mock instance.
func NewMockIKirbyDataplane(ctrl *gomock.Controller) *MockIKirbyDataplane {
	mock := &MockIKirbyDataplane{ctrl: ctrl}
	mock.recorder = &MockIKirbyDataplaneMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIKirbyDataplane) EXPECT() *MockIKirbyDataplaneMockRecorder {
	return m.recorder
}

// DetachAndWait mocks base method.
func (m *MockIKirbyDataplane) DetachAndWait() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DetachAndWait")
}

// DetachAndWait indicates an expected call of DetachAndWait.
func (mr *MockIKirbyDataplaneMockRecorder) DetachAndWait() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetachAndWait", reflect.TypeOf((*MockIKirbyDataplane)(nil).DetachAndWait))
}

// ListAggregatedSeries mocks base method.
func (m *MockIKirbyDataplane) ListAggregatedSeries(ctx context.Context, req *dataplaneModel.ReqListAggregatedSeries) (*dataplaneModel.AggregatedSeriesDetailList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAggregatedSeries", ctx, req)
	ret0, _ := ret[0].(*dataplaneModel.AggregatedSeriesDetailList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAggregatedSeries indicates an expected call of ListAggregatedSeries.
func (mr *MockIKirbyDataplaneMockRecorder) ListAggregatedSeries(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAggregatedSeries", reflect.TypeOf((*MockIKirbyDataplane)(nil).ListAggregatedSeries), ctx, req)
}

// ListAggregatedSeriesDistinctUIDs mocks base method.
func (m *MockIKirbyDataplane) ListAggregatedSeriesDistinctUIDs(ctx context.Context, req *dataplaneModel.ReqListAggregatedSeries) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAggregatedSeriesDistinctUIDs", ctx, req)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAggregatedSeriesDistinctUIDs indicates an expected call of ListAggregatedSeriesDistinctUIDs.
func (mr *MockIKirbyDataplaneMockRecorder) ListAggregatedSeriesDistinctUIDs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAggregatedSeriesDistinctUIDs", reflect.TypeOf((*MockIKirbyDataplane)(nil).ListAggregatedSeriesDistinctUIDs), ctx, req)
}

// ListItemUIDsByMonthAndZone mocks base method.
func (m *MockIKirbyDataplane) ListItemUIDsByMonthAndZone(ctx context.Context, itemCodes []string, month time.Time, zoneCode int64, epoch time.Time) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListItemUIDsByMonthAndZone", ctx, itemCodes, month, zoneCode, epoch)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListItemUIDsByMonthAndZone indicates an expected call of ListItemUIDsByMonthAndZone.
func (mr *MockIKirbyDataplaneMockRecorder) ListItemUIDsByMonthAndZone(ctx, itemCodes, month, zoneCode, epoch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListItemUIDsByMonthAndZone", reflect.TypeOf((*MockIKirbyDataplane)(nil).ListItemUIDsByMonthAndZone), ctx, itemCodes, month, zoneCode, epoch)
}

// ListSeries mocks base method.
func (m *MockIKirbyDataplane) ListSeries(ctx context.Context, req *dataplaneModel.ReqListSeries) (*dataplaneModel.SeriesDetailList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSeries", ctx, req)
	ret0, _ := ret[0].(*dataplaneModel.SeriesDetailList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSeries indicates an expected call of ListSeries.
func (mr *MockIKirbyDataplaneMockRecorder) ListSeries(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSeries", reflect.TypeOf((*MockIKirbyDataplane)(nil).ListSeries), ctx, req)
}

// ListSeriesDistinctUIDs mocks base method.
func (m *MockIKirbyDataplane) ListSeriesDistinctUIDs(ctx context.Context, req *dataplaneModel.ReqListSeries) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSeriesDistinctUIDs", ctx, req)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSeriesDistinctUIDs indicates an expected call of ListSeriesDistinctUIDs.
func (mr *MockIKirbyDataplaneMockRecorder) ListSeriesDistinctUIDs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSeriesDistinctUIDs", reflect.TypeOf((*MockIKirbyDataplane)(nil).ListSeriesDistinctUIDs), ctx, req)
}

// ListUserItemsByMonthAndZone mocks base method.
func (m *MockIKirbyDataplane) ListUserItemsByMonthAndZone(ctx context.Context, uid uint64, month time.Time, zoneCode int64, epoch time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserItemsByMonthAndZone", ctx, uid, month, zoneCode, epoch)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUserItemsByMonthAndZone indicates an expected call of ListUserItemsByMonthAndZone.
func (mr *MockIKirbyDataplaneMockRecorder) ListUserItemsByMonthAndZone(ctx, uid, month, zoneCode, epoch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserItemsByMonthAndZone", reflect.TypeOf((*MockIKirbyDataplane)(nil).ListUserItemsByMonthAndZone), ctx, uid, month, zoneCode, epoch)
}

// MakeBundleProcessCtx mocks base method.
func (m *MockIKirbyDataplane) MakeBundleProcessCtx(ctx context.Context, cm kirbypb.CompressionMethod) (*BundleProcessCtx, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeBundleProcessCtx", ctx, cm)
	ret0, _ := ret[0].(*BundleProcessCtx)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeBundleProcessCtx indicates an expected call of MakeBundleProcessCtx.
func (mr *MockIKirbyDataplaneMockRecorder) MakeBundleProcessCtx(ctx, cm interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeBundleProcessCtx", reflect.TypeOf((*MockIKirbyDataplane)(nil).MakeBundleProcessCtx), ctx, cm)
}

// ProvisionShards mocks base method.
func (m *MockIKirbyDataplane) ProvisionShards(ctx context.Context, month time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProvisionShards", ctx, month)
	ret0, _ := ret[0].(error)
	return ret0
}

// ProvisionShards indicates an expected call of ProvisionShards.
func (mr *MockIKirbyDataplaneMockRecorder) ProvisionShards(ctx, month interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProvisionShards", reflect.TypeOf((*MockIKirbyDataplane)(nil).ProvisionShards), ctx, month)
}

// QueryAggregatedSeriesData mocks base method.
func (m *MockIKirbyDataplane) QueryAggregatedSeriesData(ctx context.Context, uid uint64, month time.Time, itemCode string, zoneCode int64, dataType string, desiredG adminModel.Granularity, epoch time.Time) ([]*kirbypb.StatPoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAggregatedSeriesData", ctx, uid, month, itemCode, zoneCode, dataType, desiredG, epoch)
	ret0, _ := ret[0].([]*kirbypb.StatPoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAggregatedSeriesData indicates an expected call of QueryAggregatedSeriesData.
func (mr *MockIKirbyDataplaneMockRecorder) QueryAggregatedSeriesData(ctx, uid, month, itemCode, zoneCode, dataType, desiredG, epoch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAggregatedSeriesData", reflect.TypeOf((*MockIKirbyDataplane)(nil).QueryAggregatedSeriesData), ctx, uid, month, itemCode, zoneCode, dataType, desiredG, epoch)
}

// RefreshAggregationRules mocks base method.
func (m *MockIKirbyDataplane) RefreshAggregationRules(arg0 []*adminModel.AggregationRule) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RefreshAggregationRules", arg0)
}

// RefreshAggregationRules indicates an expected call of RefreshAggregationRules.
func (mr *MockIKirbyDataplaneMockRecorder) RefreshAggregationRules(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshAggregationRules", reflect.TypeOf((*MockIKirbyDataplane)(nil).RefreshAggregationRules), arg0)
}

// RefreshRTDs mocks base method.
func (m *MockIKirbyDataplane) RefreshRTDs(arg0 []*adminModel.ResourceTypeDefinitionDetail) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RefreshRTDs", arg0)
}

// RefreshRTDs indicates an expected call of RefreshRTDs.
func (mr *MockIKirbyDataplaneMockRecorder) RefreshRTDs(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshRTDs", reflect.TypeOf((*MockIKirbyDataplane)(nil).RefreshRTDs), arg0)
}

// RunAggregations mocks base method.
func (m *MockIKirbyDataplane) RunAggregations(ctx context.Context, month, since, epoch time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunAggregations", ctx, month, since, epoch)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunAggregations indicates an expected call of RunAggregations.
func (mr *MockIKirbyDataplaneMockRecorder) RunAggregations(ctx, month, since, epoch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunAggregations", reflect.TypeOf((*MockIKirbyDataplane)(nil).RunAggregations), ctx, month, since, epoch)
}
