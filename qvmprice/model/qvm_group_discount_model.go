package model

import (
	"context"
	"errors"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
)

const qvmGroupDiscountTableName = "qvm_group_discounts"

// QVMGroupDiscount model definition
type QVMGroupDiscount struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// QVMPriceTableID 关联的 QVMPriceTable ID
	QVMPriceTableID uint64 `gorm:"column:qvm_price_table_id;not null;unique_index:idx_qvmgd_qvmptid_group_zone_effect_dead_ctime;comment:'关联的 QVMPriceTable ID'"`
	// UID 冗余的 QVMPriceTable UID 字段
	UID uint64 `gorm:"column:uid;not null;index:idx_qvmgd_uid_group_zone_effect_dead_ctime;comment:'冗余的 QVMPriceTable UID 字段'"`
	// GroupCode 计费项组 code
	GroupCode string `gorm:"column:group_code;type:VARCHAR(256);not null;unique_index:idx_qvmgd_qvmptid_group_zone_effect_dead_ctime;index:idx_qvmgd_uid_group_zone_effect_dead_ctime;comment:'计费项 code'"`
	// ZoneCode 区域 code
	ZoneCode int64 `gorm:"column:zone_code;not null;unique_index:idx_qvmgd_qvmptid_group_zone_effect_dead_ctime;index:idx_qvmgd_uid_group_zone_effect_dead_ctime;comment:'区域 code'"`
	// IsDisabled 该条报价是否被禁用
	IsDisabled bool `gorm:"column:is_disabled;not null;default:0;comment:'该条报价是否被禁用'"`
	// EffectTime 生效时刻（含）
	EffectTime base.HNS `gorm:"column:effect_time;not null;unique_index:idx_qvmgd_qvmptid_group_zone_effect_dead_ctime;index:idx_qvmgd_uid_group_zone_effect_dead_ctime;comment:'生效时刻（含），百纳秒'"`
	// DeadTime 失效时刻（不含）
	DeadTime base.HNS `gorm:"column:dead_time;not null;unique_index:idx_qvmgd_qvmptid_group_zone_effect_dead_ctime;index:idx_qvmgd_uid_group_zone_effect_dead_ctime;comment:'失效时刻（不含），百纳秒'"`
	// Discount 折扣的百分比，100 意为不打折
	Discount uint32 `gorm:"column:discount;not null;comment:'折扣的百分比，100 意为不打折'"`

	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);unique_index:idx_qvmgd_qvmptid_group_zone_effect_dead_ctime;index:idx_qvmgd_uid_group_zone_effect_dead_ctime;default:now(6)"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME(6);default:now(6) ON UPDATE now(6)"`
}

// 由于 QVM 不是 gorm 认识的缩略词，默认它会生成形如 q_vm_xxx 的表名，因此显式指定表名
func (*QVMGroupDiscount) TableName() string {
	return qvmGroupDiscountTableName
}

// QVMGroupDiscountDao is data access object of QVMGroupDiscount model
type QVMGroupDiscountDao struct {
	base *dao.BaseDao
}

// NewQVMGroupDiscountDao is constructor of QVMGroupDiscountDao
func NewQVMGroupDiscountDao(base *dao.BaseDao) *QVMGroupDiscountDao {
	return &QVMGroupDiscountDao{
		base: base,
	}
}

func (d *QVMGroupDiscountDao) BulkInsert(ctx context.Context, rows []QVMGroupDiscount) error {
	return d.base.BulkInsert(ctx, rows)
}

type GroupSpec struct {
	GroupCode string
	ZoneCode  int64
}

func (d *QVMGroupDiscountDao) ListByUIDGroupSpecsTimePoint(
	uid uint64,
	specs []GroupSpec,
	t base.HNS,
) ([]*QVMGroupDiscount, error) {
	if len(specs) == 0 {
		return nil, errors.New("QVMGroupDiscount cannot be listed without explicit group specs")
	}

	specsExpr := squirrel.Or{}
	for _, s := range specs {
		specsExpr = append(specsExpr, squirrel.Eq{
			"group_code": s.GroupCode,
			"zone_code":  s.ZoneCode,
		})
	}

	cond := squirrel.And{
		squirrel.Eq{"uid": uid},
		specsExpr,
		squirrel.Gt{"dead_time": t},
		squirrel.LtOrEq{"effect_time": t},
	}

	sql, args, err := cond.ToSql()
	if err != nil {
		return nil, err
	}

	var result []*QVMGroupDiscount
	err = d.base.Model(&QVMGroupDiscount{}).
		Where(sql, args...).
		Find(&result).
		Error
	if err != nil {
		return nil, err
	}

	return result, nil

}

func (d *QVMGroupDiscountDao) ListByUIDGroupSpecsTimeRange(
	uid uint64,
	specs []GroupSpec,
	effectTime base.HNS,
	deadTime base.HNS,
) ([]*QVMGroupDiscount, error) {
	if len(specs) == 0 {
		return nil, errors.New("QVMGroupDiscount cannot be listed without explicit group specs")
	}

	specsExpr := squirrel.Or{}
	for _, s := range specs {
		specsExpr = append(specsExpr, squirrel.Eq{
			"group_code": s.GroupCode,
			"zone_code":  s.ZoneCode,
		})
	}

	cond := squirrel.And{
		squirrel.Eq{"uid": uid},
		specsExpr,
		squirrel.Gt{"dead_time": effectTime},
		squirrel.Lt{"effect_time": deadTime},
	}

	sql, args, err := cond.ToSql()
	if err != nil {
		return nil, err
	}

	var result []*QVMGroupDiscount
	err = d.base.Model(&QVMGroupDiscount{}).
		Where(sql, args...).
		Find(&result).
		Error
	if err != nil {
		return nil, err
	}

	return result, nil

}
