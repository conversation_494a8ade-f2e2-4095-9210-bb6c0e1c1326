package config

import (
	"os"

	"gopkg.in/yaml.v2"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/db"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/bo-base/v4/rpc"

	kirbyConfig "qiniu.io/pay/kirby/config"
)

// MeasureConfig config for measure business
type MeasureConfig struct {
	DefaultPageSize uint64 `yaml:"default_pagesize"`
	// EnableKirbyDatapath 是否启用新计量中心的数据通路
	EnableKirbyDatapath bool `yaml:"enable_kirby_datapath"`
}

// ServiceConfig contain host of service dependencies
type ServiceConfig struct {
	Dict         string `yaml:"dict"`
	Measureproxy string `yaml:"measureproxy"`
}

// MeasuredConfig is config for measured service
type MeasuredConfig struct {
	RPC      rpc.Config
	Intl     intl.Config
	MySQL    db.MySQLConfig
	Cache    dao.CacheConfig
	Measure  MeasureConfig
	Kirby    kirbyConfig.KirbyConfig
	Services ServiceConfig
}

// LoadMeasuredConfig load config from yaml file
func LoadMeasuredConfig(yamlPath string) (*MeasuredConfig, error) {
	byts, err := os.ReadFile(yamlPath)
	if err != nil {
		return nil, err
	}
	conf := &MeasuredConfig{}
	err = yaml.UnmarshalStrict(byts, conf)
	if err != nil {
		return nil, err
	}
	return conf, nil
}
