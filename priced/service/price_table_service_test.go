package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
)

func TestPriceTableCRUD(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	priceTables := []model.PriceTable{
		{
			Type:   model.PriceTableTypeDefault,
			Remark: "公开报价",
		},
		{
			Type:   model.PriceTableTypeVIP,
			Remark: "VIP 价格1",
		},
		{
			Type:   model.PriceTableTypeFinal,
			Remark: "最终价格信息",
		},
	}

	assertFields := func(expect, actual *model.PriceTable, msgAndArgs ...any) {
		assert.Equal(t, expect.Type, actual.Type, msgAndArgs...)
		assert.Equal(t, expect.Remark, actual.Remark, msgAndArgs...)
	}

	ctx := context.TODO()

	for i, priceTable := range priceTables {
		// create
		m, err := priceService.CreatePriceTable(ctx, &priceTable)
		if assert.NoError(t, err) {
			assert.NotZero(t, m.ID, "CreatePriceTable return zero id")
			assertFields(&priceTable, m, "Unmatched priceTable")
		}
		priceTables[i].ID = m.ID

		// query
		priceTableID := m.ID
		m, err = priceService.GetPriceTableByID(ctx, priceTableID)
		if assert.NoError(t, err) {
			assertFields(&priceTable, m, "Unmatched priceTable")
		}

		// update
		m.Remark = "七牛价格信息"
		updatedStair, err := priceService.UpdatePriceTableByID(ctx, priceTableID, m)
		if assert.NoError(t, err) {
			assertFields(m, updatedStair)
		}
		priceTables[i].Remark = m.Remark
	}

	// list all price tables
	priceTableList, err := priceService.ListAllPriceTables(ctx, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, priceTableList, len(priceTables))
	}

	// delete all price tables
	for _, priceTable := range priceTables {
		deletedStair, err := priceService.DeletePriceTableByID(ctx, priceTable.ID)
		if assert.NoError(t, err) {
			assertFields(deletedStair, &priceTable)
		}
	}

	// check price table list, should be empty
	l, err := priceService.ListAllPriceTables(ctx, 0, 10)
	if assert.NoError(t, err) {
		assert.Empty(t, l)
	}

	// de-duplication
	for _, priceTable := range priceTables {
		_, err := priceService.DeletePriceTableByID(ctx, priceTable.ID)
		assert.Error(t, err)
	}
}

func TestEnablePriceTable(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService
	priceTables := []model.PriceTable{
		{
			IsDisabled: true,
		},
		{
			IsDisabled: false,
		},
	}

	ctx := context.Background()
	for i, priceTable := range priceTables {
		// create
		m, err := priceService.CreatePriceTable(ctx, &priceTable)
		assert.NoError(t, err)
		priceTables[i].ID = m.ID

		err = priceService.EnablePriceTable(ctx, m.ID)
		assert.NoError(t, err)
		// 再次 enable, 预期无错误
		err = priceService.EnablePriceTable(ctx, m.ID)
		assert.NoError(t, err)

		m, err = priceService.GetPriceTableByID(ctx, m.ID)
		assert.NoError(t, err)

		assert.False(t, m.IsDisabled)
		// 多次 enable, 预期无错误
		err = priceService.EnablePriceTable(ctx, m.ID)
		assert.NoError(t, err)
	}
}
