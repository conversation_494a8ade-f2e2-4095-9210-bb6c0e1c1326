package config

import (
	"github.com/qbox/bo-base/v4/db"
)

type KirbyConfig struct {
	// Enabled 是否初始化计量中心服务，如为 false，运行时完全不可用
	Enabled bool `yaml:"enabled"`
	// Debug 是否启用调试模式（大量日志输出）
	Debug bool `yaml:"debug"`
	// MaxNumSeriesBeforeFlush 最多连续接受多少条序列即触发持久化
	MaxNumSeriesBeforeFlush int `yaml:"max_num_series_before_flush"`
	// Persistence 持久化层配置
	Persistence KirbyPersistenceConfig `yaml:"persistence"`
}

type KirbyPersistenceConfig struct {
	// SeriesMySQL 是序列数据的 MySQL 持久化后端配置
	SeriesMySQL db.MySQLConfig `yaml:"series_mysql"`
}
