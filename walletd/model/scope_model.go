package model

import (
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// Scope model definition
type Scope struct {
	ID        uint64 `gorm:"primary_key"`
	IsAll     bool
	Remark    string    `gorm:"type:varchar(512)"`
	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// ScopeDao is data access object of Scope model
type ScopeDao struct {
	base *dao.BaseDao
}

// NewScopeDao is constructor of ScopeDao
func NewScopeDao(base *dao.BaseDao) *ScopeDao {
	return &ScopeDao{
		base: base,
	}
}

// GetByID select a record of Scope by id
func (d *ScopeDao) GetByID(
	id uint64,
	expires ...time.Duration,
) (*Scope, error) {
	model := &Scope{ID: id}
	err := d.base.First(model, id).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// ListAll select all records of Scope
func (d *ScopeDao) ListAll(
	offset int,
	limit int,
	expires ...time.Duration,
) (list []Scope, err error) {
	err = d.base.Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": offset,
			"limit":  limit,
		})
	}
	return
}

// CountAll select count  of Scope
func (d *ScopeDao) CountAll(
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&Scope{}).Count(&count).Error
	return
}

// Save insert or update a record of Scope by id
func (d *ScopeDao) Save(
	model *Scope,
	expires ...time.Duration,
) error {
	return d.base.Save(model).Error
}

// Delete delete a record of Scope
func (d *ScopeDao) Delete(
	model *Scope,
) error {
	return d.base.Delete(model).Error
}
