package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/dict"
	"qiniu.io/pay/dictd/action/adapter"
)

// GetZoneByID 根据ID获取区域/机房记录
func (a *DictAction) GetZoneByID(
	ctx context.Context,
	zoneID *pb.IDParam,
) (*pb.Zone, error) {
	zone, err := a.dictBizSrv.GetZoneByID(ctx, zoneID.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZone(zone)
}

// GetZoneByCode 根据代码获取区域/机房记录
func (a *DictAction) GetZoneByCode(
	ctx context.Context,
	zoneCode *pb.ZoneCodeParam,
) (*pb.Zone, error) {
	zone, err := a.dictBizSrv.GetZoneByCode(ctx, zoneCode.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZone(zone)
}

// MultiGetZoneByCodes 根据 codes 批量获取区域/机房记录
func (a *DictAction) MultiGetZoneByCodes(
	ctx context.Context,
	param *pb.MultiZoneCodeParam,
) (*pb.ZoneList, error) {
	zones, err := a.dictBizSrv.ListZonesByCodes(ctx, param.GetCodes())
	if err != nil {
		return nil, err
	}
	pbZones, err := adapter.BuildPbZones(zones)
	if err != nil {
		return nil, err
	}
	return &pb.ZoneList{
		Count: uint64(len(zones)),
		Zones: pbZones,
	}, nil
}

// GetZoneByName 根据代码获取区域/机房记录
func (a *DictAction) GetZoneByName(
	ctx context.Context,
	zoneCode *pb.NameParam,
) (*pb.Zone, error) {
	zone, err := a.dictBizSrv.GetZoneByName(ctx, zoneCode.GetName())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZone(zone)
}

// ListAllZones 获取所有的区域/机房记录
func (a *DictAction) ListAllZones(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.ZoneList, error) {
	count, err := a.dictBizSrv.CountAllZones(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	zones, err := a.dictBizSrv.ListAllZones(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	pbZones, err := adapter.BuildPbZones(zones)
	if err != nil {
		return nil, err
	}
	return &pb.ZoneList{
		Count: count,
		Zones: pbZones,
	}, nil
}

// CountAllZones 获取所有区域/机房记录的条数
func (a *DictAction) CountAllZones(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountAllZones(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, err
}

// CreateZone 创建区域/机房记录
func (a *DictAction) CreateZone(
	ctx context.Context,
	zone *pb.Zone,
) (*pb.Zone, error) {
	m, err := adapter.BuildZone(zone)
	if err != nil {
		return nil, err
	}

	z, err := a.dictBizSrv.CreateZone(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZone(z)
}

// UpdateZoneByID 根据ID更新区域/机房记录
func (a *DictAction) UpdateZoneByID(
	ctx context.Context,
	param *pb.IDZoneParam,
) (*pb.Zone, error) {
	zone := param.GetZone()
	m, err := adapter.BuildZone(zone)
	if err != nil {
		return nil, err
	}

	z, err := a.dictBizSrv.UpdateZoneByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZone(z)
}

// UpdateZoneByCode 根据code更新区域/机房记录
func (a *DictAction) UpdateZoneByCode(
	ctx context.Context,
	param *pb.CodeZoneParam,
) (*pb.Zone, error) {
	zone := param.GetZone()
	m, err := adapter.BuildZone(zone)
	if err != nil {
		return nil, err
	}

	z, err := a.dictBizSrv.UpdateZoneByCode(ctx, param.GetCode(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZone(z)
}

// DeleteZoneByID 根据ID删除区域/机房记录
func (a *DictAction) DeleteZoneByID(
	ctx context.Context,
	zoneID *pb.IDParam,
) (*pb.Zone, error) {
	zone, err := a.dictBizSrv.DeleteZoneByID(ctx, zoneID.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZone(zone)
}

// DeleteZoneByCode 根据Code删除区域/机房记录
func (a *DictAction) DeleteZoneByCode(
	ctx context.Context,
	zoneCode *pb.ZoneCodeParam,
) (*pb.Zone, error) {
	zone, err := a.dictBizSrv.DeleteZoneByCode(ctx, zoneCode.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZone(zone)
}
