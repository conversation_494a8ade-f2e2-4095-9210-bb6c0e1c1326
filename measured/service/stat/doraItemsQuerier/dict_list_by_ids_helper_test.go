package doraItemsQuerier

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/qbox/pay-sdk/dict"
	dictpb "github.com/qbox/pay-sdk/dict"
	mockdict "github.com/qbox/pay-sdk/mocks/dict"
)

func TestQueryDictItems(t *testing.T) {
	// 为了能检查 ctx 被正确透传了
	type dummyKey struct{}
	ctx := context.WithValue(context.Background(), dummyKey{}, 1)

	ctrl := gomock.NewController(t)

	objA := &dictpb.Item{Id: 233, GroupId: 2, Code: "foo:bar:a"}
	objB := &dictpb.Item{Id: 344, GroupId: 3, Code: "quux:baz:b"}
	objC := &dictpb.Item{Id: 455, GroupId: 2, Code: "foo:bar:c"}
	objD := &dictpb.Item{Id: 566, GroupId: 4, Code: "frob:xxx:d"}

	// 空列表不应该有 RPC 调用
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)

		ids := []uint64(nil)
		expected := []*dict.Item(nil)

		actual, err := queryDictItems(ctx, mockDictCl, ids)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual)
	}

	// 正常查询
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: objA.Id}).
			Times(1).
			Return(objA, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: objB.Id}).
			Times(1).
			Return(objB, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: objC.Id}).
			Times(1).
			Return(objC, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: objD.Id}).
			Times(1).
			Return(objD, nil)

		ids := []uint64{455, 566, 233, 344}
		expected := []*dict.Item{objC, objD, objA, objB}

		actual, err := queryDictItems(ctx, mockDictCl, ids)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual, "目前的实现会保序")
	}

	// 有的记录不存在
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: objA.Id}).
			Times(1).
			Return(objA, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: objB.Id}).
			Times(1).
			Return(objB, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: objC.Id}).
			Times(1).
			Return(objC, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: objD.Id + 1}).
			Times(1).
			Return((*dictpb.Item)(nil), assert.AnError)

		ids := []uint64{455, 567, 233, 344}

		actual, err := queryDictItems(ctx, mockDictCl, ids)
		assert.Error(t, err)
		assert.Nil(t, actual)
	}
}

func TestQueryDictItemGroups(t *testing.T) {
	// 为了能检查 ctx 被正确透传了
	type dummyKey struct{}
	ctx := context.WithValue(context.Background(), dummyKey{}, 1)

	ctrl := gomock.NewController(t)

	objA := &dictpb.ItemGroup{Id: 233, ProductId: 2, Code: "foo:a"}
	objB := &dictpb.ItemGroup{Id: 344, ProductId: 3, Code: "bar:b"}
	objC := &dictpb.ItemGroup{Id: 455, ProductId: 2, Code: "foo:c"}
	objD := &dictpb.ItemGroup{Id: 566, ProductId: 4, Code: "quux:d"}

	// 空列表不应该有 RPC 调用
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)

		ids := []uint64(nil)
		expected := []*dict.ItemGroup(nil)

		actual, err := queryDictItemGroups(ctx, mockDictCl, ids)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual)
	}

	// 正常查询
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: objA.Id}).
			Times(1).
			Return(objA, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: objB.Id}).
			Times(1).
			Return(objB, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: objC.Id}).
			Times(1).
			Return(objC, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: objD.Id}).
			Times(1).
			Return(objD, nil)

		ids := []uint64{455, 566, 233, 344}
		expected := []*dict.ItemGroup{objC, objD, objA, objB}

		actual, err := queryDictItemGroups(ctx, mockDictCl, ids)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual, "目前的实现会保序")
	}

	// 有的记录不存在
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: objA.Id}).
			Times(1).
			Return(objA, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: objB.Id}).
			Times(1).
			Return(objB, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: objC.Id}).
			Times(1).
			Return(objC, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: objD.Id + 1}).
			Times(1).
			Return((*dictpb.ItemGroup)(nil), assert.AnError)

		ids := []uint64{455, 567, 233, 344}

		actual, err := queryDictItemGroups(ctx, mockDictCl, ids)
		assert.Error(t, err)
		assert.Nil(t, actual)
	}
}

func TestQueryDictProducts(t *testing.T) {
	// 为了能检查 ctx 被正确透传了
	type dummyKey struct{}
	ctx := context.WithValue(context.Background(), dummyKey{}, 1)

	ctrl := gomock.NewController(t)

	objA := &dictpb.Product{Id: 233, Code: "foo"}
	objB := &dictpb.Product{Id: 344, Code: "bar"}
	objC := &dictpb.Product{Id: 455, Code: "baz"}
	objD := &dictpb.Product{Id: 566, Code: "quux"}

	// 空列表不应该有 RPC 调用
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)

		ids := []uint64(nil)
		expected := []*dict.Product(nil)

		actual, err := queryDictProducts(ctx, mockDictCl, ids)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual)
	}

	// 正常查询
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: objA.Id}).
			Times(1).
			Return(objA, nil)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: objB.Id}).
			Times(1).
			Return(objB, nil)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: objC.Id}).
			Times(1).
			Return(objC, nil)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: objD.Id}).
			Times(1).
			Return(objD, nil)

		ids := []uint64{455, 566, 233, 344}
		expected := []*dict.Product{objC, objD, objA, objB}

		actual, err := queryDictProducts(ctx, mockDictCl, ids)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual, "目前的实现会保序")
	}

	// 有的记录不存在
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: objA.Id}).
			Times(1).
			Return(objA, nil)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: objB.Id}).
			Times(1).
			Return(objB, nil)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: objC.Id}).
			Times(1).
			Return(objC, nil)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: objD.Id + 1}).
			Times(1).
			Return((*dictpb.Product)(nil), assert.AnError)

		ids := []uint64{455, 567, 233, 344}

		actual, err := queryDictProducts(ctx, mockDictCl, ids)
		assert.Error(t, err)
		assert.Nil(t, actual)
	}
}
