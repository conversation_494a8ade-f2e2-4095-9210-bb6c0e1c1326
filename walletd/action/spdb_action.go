package action

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	"qiniu.io/pay/walletd/action/adapter"

	pb "github.com/qbox/pay-sdk/wallet"
)

func (a *WalletAction) SPDBTransfer(
	ctx context.Context, req *pb.SPDBTransferPacketReq,
) (*pb.SPDBTransferPacketResp, error) {

	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"req": *req,
	}).Info("New-Wallet : SPDBTransfer")

	r, err := adapter.BuildSPDBTransferCurrencyPacketBody(req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"req": *req,
		}).WithError(err).Error("BuildPbSPDBTransferPacketReq failed")
		return nil, errors.Trace(err)
	}
	if r.PacketID == "" {
		r.PacketID = time.Now().Format("**************") + base.GenRandomString(12)
	}
	r.BusiTime = time.Now().Format("2006-01-02T15:04:05")
	for i := range r.Lists {
		r.Lists[i].TradeDate = time.Now().Format("2006-01-02")
		if r.Lists[i].SerialNo == "" {
			r.Lists[i].SerialNo = time.Now().Format("**************") + base.GenRandomString(10)
		}
	}

	result, err := a.bankVirtAccountSrv.TransferCurrency(ctx, *r)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"r": *r,
		}).WithError(err).Error("TransferCurrency failed")
		return nil, errors.Trace(err)
	}
	return adapter.BuildSPDBTransferPacketResp(result)
}

func (a *WalletAction) SPDBTransferResultQuery(
	ctx context.Context, req *pb.SPDBTransferResultQueryReq,
) (*pb.SPDBTransferResultQueryResp, error) {
	logger := logging.GetLogger(ctx)
	r, err := adapter.BuildQueryTransferCurrencyResultReqBody(req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"req": req,
		}).WithError(err).Error("BuildQueryTransferCurrencyResultReqBody failed")
		return nil, errors.Trace(err)
	}
	r.BusiTime = time.Now().Format("2006-01-02T15:04:05")

	result, err := a.bankVirtAccountSrv.QueryTransferCurrencyResult(ctx, *r)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"*r": *r,
		}).WithError(err).Error("QueryTransferCurrencyResult failed")
		return nil, errors.Trace(err)
	}
	return adapter.BuildSPDBTransferResultQueryResp(result)
}

func (a *WalletAction) SPDBBalanceQuery(
	ctx context.Context, req *pb.SPDBBalanceQueryReq,
) (*pb.SPDBBalanceQueryResp, error) {
	logger := logging.GetLogger(ctx)

	r, err := adapter.BuildQueryBalanceReqBody(req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"req": *req,
		}).WithError(err).Error("BuildQueryBalanceReqBody failed")
		return nil, errors.Trace(err)
	}
	r.BusiTime = time.Now().Format("2006-01-02T15:04:05")

	balance, err := a.bankVirtAccountSrv.QueryBalance(ctx, *r)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"*r": *r,
		}).WithError(err).Error("QueryBalance failed")
		return nil, errors.Trace(err)
	}
	return adapter.BuildSPDBBalanceQueryResp(balance)
}
