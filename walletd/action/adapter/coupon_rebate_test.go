package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"qiniu.io/pay/walletd/model"
)

func TestCouponRebateRoundTrip(t *testing.T) {
	x := &model.CouponRebate{
		ID:          233,
		Code:        "foo",
		Type:        "SPONSOR",
		ScopeID:     456,
		Name:        "test",
		Description: "test description",
		Remark:      "test remark",
		Threshold:   200000,
		Money:       200000,
		EffectTime:  base.NewHNS(time.Now().Round(0)),
		DeadTime:    base.NewHNS(time.Now().Round(0)),
		CreatedAt:   time.Now().Round(0),
		UpdatedAt:   time.Now().Round(0),
	}

	y, err := BuildPbCouponRebate(x)
	assert.NoError(t, err)

	z, err := BuildCouponRebate(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.Code, z.Code)
	assert.Equal(t, x.Type, z.Type)
	assert.Equal(t, x.ScopeID, z.<PERSON>opeID)
	assert.Equal(t, x.Name, z.Name)
	assert.Equal(t, x.Description, z.Description)
	assert.Equal(t, x.Remark, z.Remark)
	assert.Equal(t, x.Threshold, z.Threshold)
	assert.Equal(t, x.Money, z.Money)
	assert.Equal(t, x.EffectTime, z.EffectTime)
	assert.Equal(t, x.DeadTime, z.DeadTime)
	assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
}
