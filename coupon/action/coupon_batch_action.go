package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"

	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/action/adapter"
)

// GetCouponBatchByID 根据 ID 获取一个抵用券批次记录
func (a *CouponAction) GetCouponBatchByID(
	ctx context.Context,
	req *pb.IDParam,
) (*pb.CouponBatch, error) {
	l := logging.GetLogger(ctx)
	couponBatchAndScope, err := a.couponBatchSrv.GetByID(ctx, req.GetId())
	if err != nil {
		l.<PERSON>rror("invoke couponBatchSrv.GetByID error", err)
		return nil, err
	}

	pbCouponBatch, err := adapter.BuildPbCouponBatch(couponBatchAndScope)
	if err != nil {
		l.Error("invoke BuildPbCouponBatch error", err)
		return nil, err
	}
	return pbCouponBatch, nil
}

// CreateCouponBatch 创建一个抵用券批次记录
func (a *CouponAction) CreateCouponBatch(
	ctx context.Context,
	req *pb.CreateCouponBatchParams,
) (*pb.CouponBatch, error) {
	l := logging.GetLogger(ctx)
	param, err := adapter.BuildCouponBatch(req)
	if err != nil {
		l.Errorln("adapt req error", param)
		return nil, err
	}
	couponBatchAndScope, err := a.couponBatchSrv.CreateCouponBatch(ctx, param)
	if err != nil {
		l.Errorln("couponBatchSrv.CreateCouponBatch error", param)
		return nil, err
	}
	pbCouponBatch, err := adapter.BuildPbCouponBatch(couponBatchAndScope)
	if err != nil {
		l.Errorln("couponBatchSrv.BuildPbCouponBatch error", param)
		return nil, err
	}

	return pbCouponBatch, nil
}

func (a *CouponAction) RecreateAndBindCoupon(ctx context.Context, params *pb.RecreateBindCouponParams) (*pb.CreateAndBindCouponResp, error) {
	l := logging.GetLogger(ctx)
	couponBatchAndScope, err := a.couponBatchSrv.GetByID(ctx, params.GetBatchId())
	if err != nil {
		l.Errorln("invoke couponBatchSrv.GetByID error", err)
		return nil, err
	}
	couponBatch, err := adapter.BuildPbCouponBatch(couponBatchAndScope)
	if err != nil {
		return nil, err
	}
	includedSellerIDs := make([]int64, 0)
	excludedSellerIDs := make([]int64, 0)
	includedProductIDs := make([]int64, 0)
	excludedProductIDs := make([]int64, 0)
	includedPackageIDs := make([]int64, 0)
	excludedPackageIDs := make([]int64, 0)
	if couponBatch.GetScope() != nil {
		for _, sellerScope := range couponBatch.GetScope().GetSellers() {
			if sellerScope.GetIsExcluded() {
				excludedSellerIDs = append(excludedSellerIDs, sellerScope.GetSellerId())
			} else {
				includedSellerIDs = append(includedSellerIDs, sellerScope.GetSellerId())
			}
		}

		for _, productScope := range couponBatch.GetScope().GetProducts() {
			if productScope.GetIsExcluded() {
				excludedProductIDs = append(excludedProductIDs, productScope.GetProductId())
			} else {
				includedProductIDs = append(includedProductIDs, productScope.GetProductId())
			}
		}

		for _, packageScope := range couponBatch.GetScope().GetPackages() {
			if packageScope.GetIsExcluded() {
				excludedPackageIDs = append(excludedPackageIDs, packageScope.GetPackageId())
			} else {
				includedPackageIDs = append(includedPackageIDs, packageScope.GetPackageId())
			}
		}
	}
	return a.CreateAndBindCoupon(ctx, &pb.CreateBindCouponParams{
		Uids: []uint64{params.GetUid()},
		CreateCouponBatchParams: &pb.CreateCouponBatchParams{
			Name:                      couponBatch.Name,
			Description:               couponBatch.Description,
			Remark:                    couponBatch.Remark,
			Reason:                    couponBatch.Reason,
			ReasonDesc:                params.GetReasonDesc(),
			BindMethod:                pb.COUPON_BIND_METHOD_PREBOUND,
			StartTime:                 couponBatch.StartTime,
			EndTime:                   couponBatch.EndTime,
			BatchSize:                 1,
			MaxActivationTimesPerUser: couponBatch.MaxActivationTimesPerUser,
			TimePeriodType:            couponBatch.TimePeriodType,
			EffectDays:                couponBatch.EffectDays,
			CouponEffectTime:          couponBatch.CouponEffectTime,
			CouponDeadTime:            couponBatch.CouponDeadTime,
			ThresholdMoney:            couponBatch.ThresholdMoney,
			CouponMoney:               params.GetCouponMoney(),
			IsMultipleUse:             couponBatch.IsMultipleUse,
			UserScope:                 couponBatch.UserScope,
			PayModeScope:              couponBatch.PayModeScope,
			ArrearCanUse:              couponBatch.ArrearCanUse,
			IsUnlimitedScope:          couponBatch.Scope.IsUnlimitedScope,
			Scope: &pb.CreateCouponScopeParamsV0{
				SellerIdsInclude:  includedSellerIDs,
				SellerIdsExclude:  excludedSellerIDs,
				ProductIdsInclude: includedProductIDs,
				ProductIdsExclude: excludedProductIDs,
				PackageIdsInclude: includedPackageIDs,
				PackageIdsExclude: excludedPackageIDs,
			},
			CouponScopeDesc: couponBatch.CouponScopeDesc,
			Applicant:       couponBatch.Applicant,
		},
	})
}

// ListCouponBatchesByCondsUnpaginated 按条件查询所有抵用券批次记录，对调用方不支持分页
func (a *CouponAction) ListCouponBatchesByCondsUnpaginated(
	ctx context.Context,
	req *pb.ListCouponBatchesByCondsParam,
) (*pb.CouponBatchList, error) {
	l := logging.GetLogger(ctx)
	param, err := adapter.BuildListCouponBatchesByCondsParam(req)
	if err != nil {
		l.Errorln("adapt req error", param)
		return nil, err
	}

	couponBatchList, err := a.couponBatchSrv.ListByCondsUnpaginated(ctx, param)
	if err != nil {
		l.Errorf("couponBatchSrv.ListByConds failed, param: %+v, error: %s", param, err)
		return nil, err
	}

	pbCouponBatchList, err := adapter.BuildPbCouponBatchList(couponBatchList)
	if err != nil {
		l.Errorf("couponBatchSrv.BuildPbCouponBatchList failed, param: %+v, error: %s", param, err)
		return nil, err
	}

	return pbCouponBatchList, nil
}

// ListCouponBatchesByConds 按条件查询抵用券批次记录
func (a *CouponAction) ListCouponBatchesByConds(
	ctx context.Context,
	req *pb.ListCouponBatchesByCondsParam,
) (*pb.CouponBatchList, error) {
	l := logging.GetLogger(ctx)
	// TODO paging 可以抽出来而不放到 baseAction 中，以期 adapter 或其他地方中可以使用
	offset, limit := a.Paging(req)
	param, err := adapter.BuildListCouponBatchesByCondsParam(req)
	if err != nil {
		l.Errorln("adapt req error", param)
		return nil, err
	}
	couponBatchList, err := a.couponBatchSrv.ListByConds(ctx, param, offset, limit)
	if err != nil {
		l.Errorln("couponBatchSrv.ListByConds error", param)
		return nil, err
	}
	pbCouponBatchList, err := adapter.BuildPbCouponBatchList(couponBatchList)
	if err != nil {
		l.Errorln("couponBatchSrv.BuildPbCouponBatchList error", param)
		return nil, err
	}

	return pbCouponBatchList, nil
}

// ListBoundUIDsByBatchID 根据 batchID 获取一个抵用券批次绑定的用户
func (a *CouponAction) ListBoundUIDsByBatchID(
	ctx context.Context,
	req *pb.ListBoundUIDsByBatchIDParam,
) (*pb.ListBoundUIDsByBatchIDResp, error) {
	l := logging.GetLogger(ctx)
	boundUIDs, err := a.couponSrv.ListBoundUIDsByBatchID(ctx, req.GetBatchId())
	if err != nil {
		l.Error("invoke couponSrv.ListBoundUIDsByBatchID error", err)
		return nil, err
	}

	return &pb.ListBoundUIDsByBatchIDResp{
		Uids: boundUIDs,
	}, nil
}

// CancelCouponBatchByID 过期一个抵用券批次，如果有绑定给用户，那么对应用户券状态改为冻结
func (a *CouponAction) CancelCouponBatchByID(
	ctx context.Context,
	req *pb.IDParam,
) (*empty.Empty, error) {
	l := logging.GetLogger(ctx)
	err := a.couponSrv.CancelCouponBatchByID(ctx, req.GetId())
	if err != nil {
		l.Errorf("couponBatchSrv.CancelCouponBatchByID batch_id:%d, error:%s", req.GetId(), err)
		return nil, err
	}

	return &empty.Empty{}, nil
}

// UpdateCouponBatchByID 通过 batchID 更新 couponBatch
// NOTE: 根据状态不同能够更新的字段不同，非通用全部字段更新接口
func (a *CouponAction) UpdateCouponBatchByID(
	ctx context.Context,
	req *pb.IDCouponBatchParam,
) (*pb.CouponBatch, error) {
	l := logging.GetLogger(ctx)
	param, err := adapter.BuildCouponBatchWithID(req)
	if err != nil {
		l.Errorln("adapt req error", param)
		return nil, err
	}

	couponBatchAndScope, err := a.couponBatchSrv.UpdateCouponBatchByID(ctx, param)
	if err != nil {
		l.Errorf("couponBatchSrv.UpdateCouponBatchByID batch_id:%d, error:%s", req.GetId(), err)
		return nil, err
	}

	pbCouponBatch, err := adapter.BuildPbCouponBatch(couponBatchAndScope)
	if err != nil {
		l.Errorln("couponBatchSrv.BuildPbCouponBatch error", param)
		return nil, err
	}

	return pbCouponBatch, nil
}
