package action

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"
	"qiniu.io/pay/priced/action/adapter"
)

// GetPriceUserMapByID 根据 ID 获取用户价格关系
func (a *PriceAction) GetPriceUserMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.PriceUserMap, error) {
	priceUserMapModel, err := a.priceBizSrv.GetPriceUserMapByID(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceUserMap(priceUserMapModel)
}

// CreatePriceUserMap 创建用户价格关系
func (a *PriceAction) CreatePriceUserMap(
	ctx context.Context,
	param *pb.PriceUserMap,
) (*pb.PriceUserMap, error) {
	m, err := adapter.BuildPriceUserMap(param)
	if err != nil {
		return nil, errors.Trace(err)
	}

	priceUserMapModel, err := a.priceBizSrv.CreatePriceUserMap(ctx, m)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceUserMap(priceUserMapModel)
}

// UpdatePriceUserMapByID 根据 ID 更新用户价格关系
func (a *PriceAction) UpdatePriceUserMapByID(
	ctx context.Context,
	param *pb.IDPriceUserMapParam,
) (*pb.PriceUserMap, error) {
	pbPriceUserMapModel := param.GetPriceUserMap()
	m, err := adapter.BuildPriceUserMap(pbPriceUserMapModel)
	if err != nil {
		return nil, errors.Trace(err)
	}

	priceUserMapModel, err := a.priceBizSrv.UpdatePriceUserMapByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceUserMap(priceUserMapModel)
}

// DeletePriceUserMapByID 根据 ID 删除用户价格关系
func (a *PriceAction) DeletePriceUserMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.PriceUserMap, error) {
	priceUserMapModel, err := a.priceBizSrv.DeletePriceUserMapByID(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceUserMap(priceUserMapModel)
}

// ListAllPriceUserMaps 查询所有的用户价格关系
func (a *PriceAction) ListAllPriceUserMaps(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.PriceUserMapList, error) {
	count, err := a.priceBizSrv.CountAllPriceUserMaps(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}
	offset, limit := a.Paging(param)
	priceUserMapModels, err := a.priceBizSrv.ListAllPriceUserMaps(ctx, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}
	list := &pb.PriceUserMapList{
		Count:         count,
		PriceUserMaps: make([]*pb.PriceUserMap, len(priceUserMapModels)),
	}
	for i, priceUserMapModel := range priceUserMapModels {
		priceUserMapModel, err := adapter.BuildPbPriceUserMap(&priceUserMapModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceUserMaps[i] = priceUserMapModel
	}
	return list, nil
}

// ListAllPriceUserMapsByUID 根据 UID 查询所有的用户价格关系
func (a *PriceAction) ListAllPriceUserMapsByUID(
	ctx context.Context,
	param *pb.UIDPagingParam,
) (*pb.PriceUserMapList, error) {
	count, err := a.priceBizSrv.CountAllPriceUserMapsByUID(ctx, param.GetUid())
	if err != nil {
		return nil, errors.Trace(err)
	}
	offset, limit := a.Paging(param)
	priceUserMapModels, err := a.priceBizSrv.ListAllPriceUserMapsByUID(ctx, param.GetUid(), offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}
	list := &pb.PriceUserMapList{
		Count:         count,
		PriceUserMaps: make([]*pb.PriceUserMap, len(priceUserMapModels)),
	}
	for i, priceUserMapModel := range priceUserMapModels {
		priceUserMapModel, err := adapter.BuildPbPriceUserMap(&priceUserMapModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceUserMaps[i] = priceUserMapModel
	}
	return list, nil
}

// ListPriceUserMapsByPriceID 根据 PriceID 查询所有的用户价格关系
func (a *PriceAction) ListPriceUserMapsByPriceID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.PriceUserMapList, error) {
	count, err := a.priceBizSrv.CountPriceUserMapsByPriceID(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}
	offset, limit := a.Paging(param)
	priceUserMapModels, err := a.priceBizSrv.ListPriceUserMapsByPriceID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}
	list := &pb.PriceUserMapList{
		Count:         count,
		PriceUserMaps: make([]*pb.PriceUserMap, len(priceUserMapModels)),
	}
	for i, priceUserMapModel := range priceUserMapModels {
		priceUserMapModel, err := adapter.BuildPbPriceUserMap(&priceUserMapModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceUserMaps[i] = priceUserMapModel
	}
	return list, nil
}
