package adapter

import (
	pb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/model/dataplaneModel"
)

func BuildReqListAggregatedSeries(
	in *pb.ReqListAggregatedSeries,
) *dataplaneModel.ReqListAggregatedSeries {
	return &dataplaneModel.ReqListAggregatedSeries{
		Epoch:         in.Epoch.AsTime(),
		Page:          in.Page,
		PageSize:      in.PageSize,
		DataTimeFrom:  in.DataTimeFrom.AsTime(),
		DataTimeTo:    in.DataTimeTo.AsTime(),
		CtimeFrom:     in.CtimeFrom.AsTime(),
		CtimeTo:       in.CtimeTo.AsTime(),
		AggSeriesKeys: in.AggSeriesKeys,
		UIDs:          in.Uids,
		ItemCodes:     in.ItemCodes,
	}
}
