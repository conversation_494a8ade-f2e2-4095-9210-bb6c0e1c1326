package action

import (
	"context"

	"github.com/qbox/pay-sdk/middleware/logging"

	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/walletd/action/adapter"
)

// RefundCash refund cash
func (a *PaymentAction) RefundCash(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : RefundCash")

	refundRequest, err := adapter.BuildRefundRequest(request)
	if err != nil {
		logger.WithError(err).Error("BuildRefundRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, refundRequest.UID)
	if err != nil {
		logger.WithField("uid", request.Uid).WithError(err).Error("BuildRefundRequest failed")
		return nil, err
	}
	refundRequest.CurrencyType = currencyType

	txn, err := a.billRefundService.RefundCash(ctx, refundRequest)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("refund cash failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}

	return pbTransaction, nil
}

// RefundNiuCoin refund niu coin
func (a *PaymentAction) RefundNiuCoin(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : RefundNiuCoin")

	refundRequest, err := adapter.BuildRefundRequest(request)
	if err != nil {
		logger.WithError(err).Error("BuildRefundRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, refundRequest.UID)
	if err != nil {
		logger.WithField("uid", refundRequest.UID).WithError(err).Error("PaymentAction.RefundNiuCoin GetOnlyOneCurrencyType failed")
		return nil, err
	}
	refundRequest.CurrencyType = currencyType

	txn, err := a.billRefundService.RefundNiuCoin(ctx, refundRequest)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("refund niu coin failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}

	return pbTransaction, nil
}
