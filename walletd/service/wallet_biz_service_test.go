package service_test

import (
	"context"
	"log"
	"net"
	"net/http"
	"strconv"
	"testing"
	"time"

	"google.golang.org/grpc/keepalive"

	"github.com/go-redis/redis/v8"
	"github.com/golang/mock/gomock"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/lock"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/bo-base/v4/test"
	pbDict "github.com/qbox/pay-sdk/dict"
	mock_wallet "github.com/qbox/pay-sdk/mocks/wallet"
	pbWallet "github.com/qbox/pay-sdk/wallet"

	dictAction "qiniu.io/pay/dictd/action"
	dictModel "qiniu.io/pay/dictd/model"
	dictService "qiniu.io/pay/dictd/service"

	"qiniu.io/pay/walletd/config"
	"qiniu.io/pay/walletd/model"
	"qiniu.io/pay/walletd/service"
)

type sandbox struct {
	walletTestWrap *test.Wrap
	dictTestWrap   *test.Wrap
	walletService  *service.WalletBizService

	dictListener net.Listener
	dictServer   *grpc.Server
	dictClient   pbDict.PayDictServiceClient
}

func buildSandbox(t *testing.T) *sandbox {
	ctrl := gomock.NewController(t)
	// no need to defer ctrl.Finish() on go1.14+

	walletTestWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in wallet/service return error")
	}

	dictTestWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(dictModel.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in wallet/dict return error")
	}

	dictDao := dictModel.NewDictDao(dictTestWrap.BaseDao())
	dictService := dictService.NewDictBizService(dictDao, dao.CacheExpiresNoCache)
	dictAction := dictAction.NewDictAction(dictService, 10)

	dictServer := grpc.NewServer()
	pbDict.RegisterPayDictServiceServer(dictServer, dictAction)
	reflection.Register(dictServer)

	sandbox := new(sandbox)
	sandbox.dictServer = dictServer
	sandbox.initDictServer()

	conf := &config.WalletdConfig{
		Services: config.ServiceConfig{
			Dict: sandbox.dictListener.Addr().String(),
		},
		Cache: dao.CacheConfig{
			Enabled: true,
			Prefix:  strconv.FormatInt(time.Now().UnixNano(), 10),
			RedisConfig: redis.UniversalOptions{
				Addrs: []string{walletTestWrap.Miniredis().Addr()},
			},
			DefaultExpires: time.Second * 5,
		},
	}

	sandbox.initDictClient(conf)

	mockPaymentService := mock_wallet.NewMockPaymentServiceClient(ctrl)
	{
		mockPaymentService.EXPECT().GetSingleCurrency(
			gomock.Any(),
			gomock.AssignableToTypeOf((*pbWallet.UIDParam)(nil)),
			gomock.Any(),
		).AnyTimes().DoAndReturn(func(ctx context.Context, req *pbWallet.UIDParam, opts ...any) (*pbWallet.Currency, error) {
			return &pbWallet.Currency{
				Uid:          req.Uid,
				CurrencyType: "CNY",
			}, nil
		})
	}

	userLocker := lock.NewRedisLocker(redis.NewUniversalClient(&redis.UniversalOptions{
		Addrs: []string{walletTestWrap.Miniredis().Addr()},
	}))

	walletDao := model.NewWalletDao(walletTestWrap.BaseDao())
	walletService, err := service.NewWalletBizService(conf, walletDao, http.DefaultClient, userLocker, dao.CacheExpiresNoCache)
	if err != nil {
		logrus.WithError(err).Fatalln("init wallet service failed")
	}
	walletService.SetPaymentServiceClient(mockPaymentService)

	sandbox.walletTestWrap = walletTestWrap
	sandbox.dictTestWrap = dictTestWrap
	sandbox.walletService = walletService
	t.Cleanup(sandbox.cleanup)

	return sandbox
}

func (s *sandbox) initDictServer() {
	var err error
	s.dictListener, err = net.Listen("tcp", ":0")
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	go func() {
		if err := s.dictServer.Serve(s.dictListener); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()

	time.Sleep(time.Millisecond * 300)
}

func (s *sandbox) initDictClient(conf *config.WalletdConfig) {
	dictConn, err := rpc.GrpcConnectWithName(conf.Services.Dict, rpc.ServicePayV4Dict, keepalive.ClientParameters{})
	if err != nil {
		log.Fatalf("init dict client failed")
	}

	s.dictClient = pbDict.NewPayDictServiceClient(dictConn)
}

func (s *sandbox) cleanup() {
	if s.dictServer != nil {
		s.dictServer.Stop()
	}
}
