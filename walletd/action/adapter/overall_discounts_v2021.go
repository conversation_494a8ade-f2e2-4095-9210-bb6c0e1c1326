package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/qpay/payment/model"
)

func BuildQueryUserOverallDiscountsV2021Request(
	x *pb.QueryUserOverallDiscountsV2021Request,
) (*model.QueryUserOverallDiscountsV2021Request, error) {
	return &model.QueryUserOverallDiscountsV2021Request{
		UIDs: x.Uids,
	}, nil
}

func BuildPbUserOverallDiscountsV2021(
	x *model.UserOverallDiscountsV2021,
) (*pb.UserOverallDiscountsV2021, error) {
	return &pb.UserOverallDiscountsV2021{
		TotalFreenb:     x.TotalFreeNb.ToInt64(),
		AvailableFreenb: x.AvailableFreeNb.ToInt64(),
		TotalCoupon:     x.TotalCoupon.ToInt64(),
		AvailableCoupon: x.AvailableCoupon.ToInt64(),
	}, nil
}
