package adapter

import (
	"github.com/qbox/bo-base/v4/errors"

	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/service"
)

func BuildQueryBalanceReqBody(req *pb.SPDBBalanceQueryReq) (*service.QueryBalanceReqBody, error) {
	if req == nil {
		return nil, errors.New("SPDBBalanceQueryReq must not nil")
	}
	return &service.QueryBalanceReqBody{
		AccCode: req.AccCode,
	}, nil
}

func BuildSPDBBalanceQueryResp(
	r *service.BankRespPacket[service.QueryBalanceRespBody],
) (*pb.SPDBBalanceQueryResp, error) {
	if r == nil {
		return nil, errors.New("BankRespPacket[service.QueryBalanceRespBody] must not nil")
	}
	var body = &pb.BalanceInfo{
		AccCode:        r.Body.AccCode,
		CurrencyCode:   r.Body.CurrencyCode,
		Balance:        r.Body.Balance,
		ReserveBalance: r.Body.ReserveBalance,
		FreezeBalance:  r.Body.FreezeBalance,
		CortrolBalance: r.Body.CortrolBalance,
		CanUseBalance:  r.Body.CanUseBalance,
	}
	return &pb.SPDBBalanceQueryResp{
		CreatedDate: r.CreatedDate,
		OrionNo:     r.OrionNo,
		PacketNo:    r.PacketNo,
		ReturnCode:  r.ReturnCode,
		ReturnMess:  r.ReturnMess,
		VerNo:       r.VerNo,
		Body:        body,
	}, nil
}
func BuildQueryTransferCurrencyResultReqBody(
	req *pb.SPDBTransferResultQueryReq,
) (*service.QueryTransferCurrencyResultReqBody, error) {
	if req == nil {
		return nil, errors.New("SPDBTransferResultQueryReq must not nil")
	}

	var list = make([]service.QueryTransferCurrencyResultReqItem, len(req.Lists))
	for i, obj := range req.Lists {
		list[i] = service.QueryTransferCurrencyResultReqItem{
			SerialNo: obj.SerialNo,
		}
	}
	return &service.QueryTransferCurrencyResultReqBody{
		PacketID: req.PacketId,
		Lists:    list,
	}, nil
}

func BuildSPDBTransferResultQueryResp(
	r *service.BankRespPacket[service.QueryTransferCurrencyResultRespBody],
) (*pb.SPDBTransferResultQueryResp, error) {
	if r == nil {
		return nil, errors.New("service.BankRespPacket[service.QueryTransferCurrencyResultRespBody] must not nil")
	}
	var body = &pb.SPDBTransferResultQueryRespItem{
		PacketId: r.Body.PacketID,
		Lists:    make([]*pb.SPDBTransferResultQueryRespItem_TransferTransaction, len(r.Body.Lists)),
	}
	for i, obj := range r.Body.Lists {
		body.Lists[i] = &pb.SPDBTransferResultQueryRespItem_TransferTransaction{
			SerialNo:    obj.SerialNo,
			RstCode:     obj.RstCode,
			RstMess:     obj.RstMess,
			SplitNumber: obj.SplitNumber,
			SplitLists:  make([]*pb.SPDBTransferResultQueryRespItem_SplitObj, len(obj.SplitLists)),
		}
		for j, sl := range obj.SplitLists {
			body.Lists[i].SplitLists[j] = &pb.SPDBTransferResultQueryRespItem_SplitObj{
				SplitSerialNo: sl.SplitSerialNo,
				RstCode:       sl.RstCode,
				RstMess:       sl.RstMess,
			}
		}
	}
	return &pb.SPDBTransferResultQueryResp{
		CreatedDate: r.CreatedDate,
		OrionNo:     r.OrionNo,
		PacketNo:    r.PacketNo,
		ReturnCode:  r.ReturnCode,
		ReturnMess:  r.ReturnMess,
		VerNo:       r.VerNo,
		Body:        body,
	}, nil
}
func BuildSPDBTransferCurrencyPacketBody(
	req *pb.SPDBTransferPacketReq,
) (*service.TransferCurrencyPacketBody, error) {
	if req == nil {
		return nil, errors.New("SPDBTransferPacketReq must not nil")
	}
	var list []service.TransferCurrencyItem
	for _, obj := range req.Lists {
		tmp := service.TransferCurrencyItem{
			SerialNo:         obj.SerialNo,
			Amount:           obj.Amount,
			PayAccCode:       obj.PayAccCode,
			PayReserver1:     "",
			PayReserver2:     "",
			PayReserver3:     "",
			RecAccName:       obj.RecAccName,
			RecAccCode:       obj.RecAccCode,
			RecAccType:       obj.RecAccType,
			RecBankCode:      obj.RecBankCode,
			RecHouseBankName: obj.RecHouseBankName,
			RecHouseBankCode: obj.RecHouseBankCode,
			RecReserver1:     "",
			RecReserver2:     "",
			RecReserver3:     "",
			CurrencyCode:     obj.CurrencyCode,
			Purpose:          obj.Purpose,
			Reserver1:        "",
			Reserver2:        "",
			Reserver3:        "",
			Reserver4:        "",
			Reserver5:        "",
		}
		list = append(list, tmp)
	}

	var r = service.TransferCurrencyPacketBody{
		PacketID:    req.PacketId,
		TotalNumber: req.TotalNumber,
		TotalAmount: req.TotalAmount,
		Lists:       list,
	}
	return &r, nil
}

func BuildSPDBTransferPacketResp(
	result *service.BankRespPacket[service.CommonBankRespPacketBody],
) (*pb.SPDBTransferPacketResp, error) {
	if result == nil {
		return nil, errors.New("nil service.BankRespPacket[service.CommonBankRespPacketBody]")
	}
	var body = pb.SPDBTransferPacketBody{
		RstCode: result.Body.RstCode,
		RstMess: result.Body.RstMess,
	}
	var resp = pb.SPDBTransferPacketResp{
		CreatedDate: result.CreatedDate,
		OrionNo:     result.OrionNo,
		PacketNo:    result.PacketNo,
		ReturnCode:  result.ReturnCode,
		ReturnMess:  result.ReturnMess,
		VerNo:       result.VerNo,
		Body:        &body,
	}
	return &resp, nil
}
