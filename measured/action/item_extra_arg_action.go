package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/action/adapter"
)

// impl PayMeasureServiceServer for MeasureAction

// GetItemExtraArgByID 根据 ID 查询单个计费项计量查询附加参数
func (a *MeasureAction) GetItemExtraArgByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.ItemExtraArg, error) {
	obj, err := a.srv.GetItemExtraArgByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemExtraArg(obj)
}

// ListItemExtraArgsByItemID 根据计费项 ID 列出计量查询附加参数列表
func (a *MeasureAction) ListItemExtraArgsByItemID(
	ctx context.Context,
	p *pb.IDPagingParam,
) (*pb.ItemExtraArgList, error) {
	count, err := a.srv.CountItemExtraArgsByItemID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(p)
	objs, err := a.srv.ListItemExtraArgsByItemID(ctx, p.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemExtraArgList{
		Count:         count,
		ItemExtraArgs: make([]*pb.ItemExtraArg, len(objs)),
	}
	for i, obj := range objs {
		result, err := adapter.BuildPbItemExtraArg(&obj)
		if err != nil {
			return nil, err
		}
		list.ItemExtraArgs[i] = result
	}
	return list, nil
}

// CountItemExtraArgsByItemID 根据计费项 ID 获取计量查询附加参数数量
func (a *MeasureAction) CountItemExtraArgsByItemID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.srv.CountItemExtraArgsByItemID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// CreateItemExtraArg 创建一个计费项计量查询附加参数
func (a *MeasureAction) CreateItemExtraArg(
	ctx context.Context,
	item *pb.ItemExtraArg,
) (*pb.ItemExtraArg, error) {
	obj, err := adapter.BuildModelItemExtraArg(item)
	if err != nil {
		return nil, err
	}
	obj, err = a.srv.CreateItemExtraArg(ctx, obj)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemExtraArg(obj)
}

// SyncItemExtraArgs 为给定的计费项 ID 同步计费项计量查询附加参数配置
func (a *MeasureAction) SyncItemExtraArgs(
	ctx context.Context,
	p *pb.SyncItemExtraArgsParam,
) (*empty.Empty, error) {
	obj, err := adapter.BuildServiceSyncItemExtraArgsParam(p)
	if err != nil {
		return nil, err
	}
	err = a.srv.SyncItemExtraArgs(ctx, obj)
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}

// DeleteItemExtraArgByID 根据 ID 删除一个计费项计量查询附加参数
func (a *MeasureAction) DeleteItemExtraArgByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.ItemExtraArg, error) {
	obj, err := a.srv.DeleteItemExtraArgByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemExtraArg(obj)
}
