package adapter

import (
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service"
)

// BuildPbSyncItemExtraArgsParam converts from service.SyncItemExtraArgsParam to the protobuf type.
func BuildPbSyncItemExtraArgsParam(
	x *service.SyncItemExtraArgsParam,
) (*pb.SyncItemExtraArgsParam, error) {
	pbArgs := make([]*pb.ItemExtraArg, len(x.Args))
	for i, arg := range x.Args {
		pbArg, err := BuildPbItemExtraArg(&arg)
		if err != nil {
			return nil, err
		}
		pbArgs[i] = pbArg
	}
	return &pb.SyncItemExtraArgsParam{
		ItemId: x.ItemID,
		Args:   pbArgs,
	}, nil
}

// BuildServiceSyncItemExtraArgsParam converts from pb.SyncItemExtraArgsParam to the service layer type.
func BuildServiceSyncItemExtraArgsParam(
	x *pb.SyncItemExtraArgsParam,
) (*service.SyncItemExtraArgsParam, error) {
	srvArgs := make([]model.ItemExtraArg, len(x.Args))
	for i, arg := range x.Args {
		srvArg, err := BuildModelItemExtraArg(arg)
		if err != nil {
			return nil, err
		}
		srvArgs[i] = *srvArg
	}

	return &service.SyncItemExtraArgsParam{
		ItemID: x.GetItemId(),
		Args:   srvArgs,
	}, nil
}
