package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/walletd/model"
)

// GetCouponCashByID 通过ID获取现金优惠券
func (s *WalletBizService) GetCouponCashByID(
	ctx context.Context,
	id uint64,
) (*model.CouponCash, error) {
	return s.walletDao.CouponCash.GetByID(id, s.cacheExpires)
}

// GetCouponCashByCode 通过 code 获取现金优惠券
func (s *WalletBizService) GetCouponCashByCode(
	ctx context.Context,
	code string,
) (*model.CouponCash, error) {
	return s.walletDao.CouponCash.GetByCode(code, s.cacheExpires)
}

// CreateCouponCash 创建现金优惠券
func (s *WalletBizService) CreateCouponCash(
	ctx context.Context,
	cash *model.CouponCash,
) (*model.CouponCash, error) {
	cash.ID = 0
	err := s.walletDao.CouponCash.Save(cash, s.cacheExpires)
	return cash, err
}

// UpdateCouponCashByID 按照 ID 更新现金优惠券
func (s *WalletBizService) UpdateCouponCashByID(
	ctx context.Context,
	id uint64,
	cash *model.CouponCash,
) (*model.CouponCash, error) {
	cash.ID = id
	err := s.walletDao.CouponCash.Save(cash, s.cacheExpires)
	return cash, err
}

// UpdateCouponCashByCode 按照 code 更新现金优惠券
func (s *WalletBizService) UpdateCouponCashByCode(
	ctx context.Context,
	code string,
	cash *model.CouponCash,
) (*model.CouponCash, error) {
	cash.Code = code
	err := s.walletDao.CouponCash.Save(cash, s.cacheExpires)
	return cash, err
}

// DeleteCouponCashByID 按照 ID 删除现金优惠券
func (s *WalletBizService) DeleteCouponCashByID(
	ctx context.Context,
	id uint64,
) (*model.CouponCash, error) {
	cash, err := s.GetCouponCashByID(ctx, id)
	if err != nil {
		return nil, errors.Annotate(err, "query coupon cash failed").WithField("id", id)
	}
	err = s.walletDao.CouponCash.Delete(cash)
	return cash, err
}

// DeleteCouponCashByCode 按照 code 删除现金优惠券
func (s *WalletBizService) DeleteCouponCashByCode(
	ctx context.Context,
	code string,
) (*model.CouponCash, error) {
	cash, err := s.GetCouponCashByCode(ctx, code)
	if err != nil {
		return nil, errors.Annotate(err, "query coupon cash failed").WithField("code", code)
	}
	err = s.walletDao.CouponCash.Delete(cash)
	return cash, err
}

// ListAllCouponCash 列举所有现金优惠券
func (s *WalletBizService) ListAllCouponCash(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.CouponCash, error) {
	return s.walletDao.CouponCash.ListAll(offset, limit, s.cacheExpires)
}

// CountAllCouponCash 获取所有现金优惠券数量
func (s *WalletBizService) CountAllCouponCash(
	ctx context.Context,
) (uint64, error) {
	return s.walletDao.CouponCash.CountAll(s.cacheExpires)
}

// ListCouponCashByScopeID 通过计费范围列举现金优惠券
func (s *WalletBizService) ListCouponCashByScopeID(
	ctx context.Context,
	scopeID uint64,
	offset int,
	limit int,
) ([]model.CouponCash, error) {
	return s.walletDao.CouponCash.ListByScopeID(scopeID, offset, limit, s.cacheExpires)
}

// CountCouponCashByScopeID 通过计费范围获取现金优惠券数量
func (s *WalletBizService) CountCouponCashByScopeID(
	ctx context.Context,
	scopeID uint64,
) (uint64, error) {
	return s.walletDao.CouponCash.CountByScopeID(scopeID, s.cacheExpires)
}

// ListCouponCashByUID 通过用户ID列举现金优惠券
func (s *WalletBizService) ListCouponCashByUID(
	ctx context.Context,
	uid uint64,
	offset int,
	limit int,
) ([]model.CouponCash, error) {
	cs, err := s.walletDao.CouponUserMap.ListByUID(uid,
		model.CouponTypeCash, offset, limit, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "list coupon cash failed").WithField("uid", uid)
	}

	ids := make([]uint64, len(cs))
	for i, c := range cs {
		ids[i] = c.CouponID
	}

	return s.walletDao.CouponCash.ListByIDs(ids, s.cacheExpires)
}

// CountCouponCashByUID 通过用户ID获取现金优惠券数量
func (s *WalletBizService) CountCouponCashByUID(
	ctx context.Context,
	uid uint64,
) (uint64, error) {
	return s.walletDao.CouponUserMap.CountByUID(uid, model.CouponTypeCash, s.cacheExpires)
}
