package action_test

import (
	"context"
	"testing"

	"github.com/qbox/pay-sdk/dict"
	"github.com/stretchr/testify/assert"
)

func TestItemDataTypeAlgorithmInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	dictClient := sandbox.dictClient

	const dataTypeID uint64 = 1
	data := []dict.ItemDataTypeAlgorithm{
		{
			DataTypeId: dataTypeID,
			Code:       "bandwidth:top",
			Name:       "月计费月峰值",
			Script:     "top",
			IsDefault:  true,
		},
		{
			DataTypeId: dataTypeID,
			Code:       "bandwidth:95%",
			Name:       "月计费95峰值",
			Script:     "95%",
			IsDefault:  false,
		},
	}
	assertFields := func(algorithm *dict.ItemDataTypeAlgorithm, n int, msgAndArgs ...any) {
		if n >= len(data) {
			return
		}
		assert.Equal(t, data[n].GetDataTypeId(), algorithm.GetDataTypeId(), msgAndArgs...)
		assert.Equal(t, data[n].GetCode(), algorithm.GetCode(), msgAndArgs...)
		assert.Equal(t, data[n].GetName(), algorithm.GetName(), msgAndArgs...)
		assert.Equal(t, data[n].GetScript(), algorithm.GetScript(), msgAndArgs...)
		assert.Equal(t, data[n].GetIsDefault(), algorithm.GetIsDefault(), msgAndArgs...)
	}
	ids := make([]uint64, len(data))
	for n, d := range data {
		algorithm, err := dictClient.CreateItemDataTypeAlgorithm(context.Background(), &d)
		if assert.NoError(t, err, "CreateItemDataTypeAlgorithm") {
			assert.NotZero(t, algorithm.GetId(), "CreateItemDataTypeAlgorithm")
			ids[n] = algorithm.GetId()
		}

		algorithm, err = dictClient.GetItemDataTypeAlgorithmByID(context.Background(), &dict.IDParam{Id: algorithm.GetId()})
		if assert.NoError(t, err, "GetItemDataTypeAlgorithmByID") {
			assertFields(algorithm, n, "GetItemDataTypeAlgorithmByID")
		}

		algorithm.Name += "suffix"
		data[n].Name += "suffix"
		algorithm, err = dictClient.UpdateItemDataTypeAlgorithmByID(context.Background(), &dict.IDItemDataTypeAlgorithmParam{Id: algorithm.GetId(), ItemDataTypeAlgorithm: algorithm})
		if assert.NoError(t, err, "UpdateItemDataTypeAlgorithmByID") {
			assert.Equal(t, data[n].GetName(), algorithm.GetName(), "UpdateItemDataTypeAlgorithmByID")
		}
	}

	count, err := dictClient.CountItemDataTypeAlgorithmsByItemDataTypeID(context.Background(), &dict.IDParam{Id: dataTypeID})
	if assert.NoError(t, err, "CountItemDataTypeAlgorithmsByItemDataTypeID") {
		assert.Len(t, data, int(count.GetCount()), "CountItemDataTypeAlgorithmsByItemDataTypeID")
	}

	list, err := dictClient.ListItemDataTypeAlgorithmsByItemDataTypeID(context.Background(), &dict.IDPagingParam{Id: dataTypeID, Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListItemDataTypeAlgorithmsByItemDataTypeID") {
		assert.Len(t, data, int(list.GetCount()), "ListItemDataTypeAlgorithmsByItemDataTypeID")
		for _, dta := range list.GetItemDataTypeAlgorithms() {
			for i, id := range ids {
				if id == dta.Id {
					assertFields(dta, i, "ListItemDataTypeAlgorithmsByItemDataTypeID")
				}
			}
		}
	}

	for _, id := range ids {
		_, err = dictClient.DeleteItemDataTypeAlgorithmByID(context.Background(), &dict.IDParam{Id: id})
		assert.NoError(t, err, "DeleteItemDataTypeAlgorithmByID")
	}
}
