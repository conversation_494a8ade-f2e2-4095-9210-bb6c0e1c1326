package service_test

import (
	"context"
	"io"
	"log"
	"net"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"google.golang.org/grpc/keepalive"

	"github.com/go-redis/redis/v8"
	"github.com/golang/mock/gomock"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/bo-base/v4/test"
	"github.com/qbox/pay-sdk/base/account"
	pbDict "github.com/qbox/pay-sdk/dict"
	mock_wallet "github.com/qbox/pay-sdk/mocks/wallet"
	pbWallet "github.com/qbox/pay-sdk/wallet"

	dictAction "qiniu.io/pay/dictd/action"
	dictModel "qiniu.io/pay/dictd/model"
	dictService "qiniu.io/pay/dictd/service"
	"qiniu.io/pay/priced/config"
	"qiniu.io/pay/priced/model"
	"qiniu.io/pay/priced/service"
)

type sandbox struct {
	testWrap     *test.Wrap
	dictTestWrap *test.Wrap
	priceService *service.PriceBizService

	dictListener net.Listener
	dictServer   *grpc.Server
}

func buildSandbox(t *testing.T) *sandbox {
	ctrl := gomock.NewController(t)
	// don't have to, and cannot, defer ctrl.Finish *here*
	// because we are not TestFoo() and we are way past go1.14 so it's automatically done anyway

	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in price/service return error")
	}

	dictTestWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(dictModel.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in price/dict return error")
	}

	dictDao := dictModel.NewDictDao(dictTestWrap.BaseDao())
	dictService := dictService.NewDictBizService(dictDao, dao.CacheExpiresNoCache)
	dictAction := dictAction.NewDictAction(dictService, 10)

	dictServer := grpc.NewServer()
	pbDict.RegisterPayDictServiceServer(dictServer, dictAction)
	reflection.Register(dictServer)

	sandbox := new(sandbox)
	sandbox.dictServer = dictServer
	sandbox.initDictServer()

	dictConn, err := rpc.GrpcConnectWithName(sandbox.dictListener.Addr().String(), rpc.ServicePayV4Dict, keepalive.ClientParameters{})
	if err != nil {
		logrus.WithError(err).Fatalln("connecting dict server return error")
	}
	dictClient := pbDict.NewPayDictServiceClient(dictConn)

	paymentSrvClient := mock_wallet.NewMockPaymentServiceClient(ctrl)
	{
		paymentSrvClient.EXPECT().GetSingleCurrency(
			gomock.Any(),
			gomock.Any(),
		).AnyTimes().DoAndReturn(func(ctx context.Context, req *pbWallet.UIDParam, opts ...any) (*pbWallet.Currency, error) {
			return &pbWallet.Currency{
				Uid:          req.Uid,
				CurrencyType: base.CurrencyTypeCNY.String(),
			}, nil
		})
	}
	accServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		io.WriteString(w, `{"code":200,"message":""}`)
	}))

	conf := &config.PricedConfig{
		Cache: dao.CacheConfig{
			Enabled: true,
			Prefix:  strconv.FormatInt(time.Now().UnixNano(), 10),
			RedisConfig: redis.UniversalOptions{
				Addrs: []string{testWrap.Miniredis().Addr()},
			},
			DefaultExpires: time.Second * 5,
		},
		Acc: account.AccConfig{
			UserName: "root",
			Password: "root",
			Host:     accServer.URL,
		},
	}

	priceDao := model.NewPriceDao(testWrap.BaseDao())
	priceService, err := service.NewPriceBizService(
		conf,
		dictClient,
		paymentSrvClient,
		priceDao,
		dao.CacheExpiresNoCache,
		nil,
	)
	if err != nil {
		logrus.WithError(err).Fatalln("init price service failed")
	}

	sandbox.testWrap = testWrap
	sandbox.dictTestWrap = dictTestWrap
	sandbox.priceService = priceService
	t.Cleanup(sandbox.cleanup)

	return sandbox
}

func (s *sandbox) initDictServer() {
	var err error
	s.dictListener, err = net.Listen("tcp", ":0")
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	go func() {
		if err := s.dictServer.Serve(s.dictListener); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()

	time.Sleep(time.Millisecond * 300)
}

func (s *sandbox) closeDictServer() {
	if s.dictServer != nil {
		s.dictServer.Stop()
	}
}

func (s *sandbox) cleanup() {
	s.testWrap.DropDatabase()
	s.dictTestWrap.DropDatabase()
	s.closeDictServer()
}

func TestChangePriceTableLifecycle(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	srcPriceTable := &model.PriceTable{
		Type: model.PriceTableTypeVIP,
	}

	srcPriceItem := &model.PriceItem{
		ItemID:         10,
		AlgorithmID:    2,
		StairPriceType: "test-stair-type",
		IsDisabled:     false,
		ShouldSkip:     false,
		UnitRate:       1024,
		EffectTime:     base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:       base.NewHNS(time.Date(2018, 2, 1, 0, 0, 0, 0, time.UTC)),
	}

	srcPriceItemStairs := []*model.PriceItemStair{
		{
			UnitID:   1,
			Quantity: 10,
			Price:    base.NewNMoneyWithHighAccuracyI64(10000),
			Order:    0,
		},
		{
			UnitID:   2,
			Quantity: 1024,
			Price:    base.NewNMoneyWithHighAccuracyI64(10240),
			Order:    1,
		},
	}

	// create src price_table and related price_items、price_item_stairs
	ctx := tz.WithRefLocation(context.Background(), time.UTC)
	srcPriceTable, err := priceService.CreatePriceTable(ctx, srcPriceTable)
	assert.NoError(t, err)

	srcPriceItem.PriceID = srcPriceTable.ID
	srcPriceItem, err = priceService.CreatePriceItem(ctx, srcPriceItem)
	assert.NoError(t, err)

	for i := range srcPriceItemStairs {
		srcPriceItemStairs[i].PriceItemID = srcPriceItem.ID
		srcPriceItemStairs[i], err = priceService.CreatePriceItemStair(ctx, srcPriceItemStairs[i])
		assert.NoError(t, err)
	}

	// change lifecycle
	param := &service.PriceTableLifecycleChangeParam{
		UID:          1,
		PriceTableID: srcPriceTable.ID,
		EffectTime:   base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:     base.NewHNS(time.Date(2018, 8, 1, 0, 0, 0, 0, time.UTC)),
	}

	dstPriceTable, err := priceService.ChangePriceTableLifecycle(ctx, param)
	assert.NoError(t, err)

	assert.NotEqual(t, srcPriceTable.ID, dstPriceTable.ID)
	assert.Equal(t, srcPriceTable.Type, dstPriceTable.Type)

	// check price_items
	dstPriceItems, err := priceService.ListAllPriceItemsByPriceID(ctx, dstPriceTable.ID, 0, -1)
	assert.NoError(t, err)
	assert.Len(t, dstPriceItems, 1)

	assert.NotEqual(t, srcPriceItem.ID, dstPriceItems[0].ID)
	assert.Equal(t, srcPriceItem.ItemID, dstPriceItems[0].ItemID)
	assert.Equal(t, srcPriceItem.UnitRate, dstPriceItems[0].UnitRate)
	assert.Equal(t, srcPriceItem.IsDisabled, dstPriceItems[0].IsDisabled)
	assert.Equal(t, srcPriceItem.ShouldSkip, dstPriceItems[0].ShouldSkip)
	assert.Equal(t, srcPriceItem.AlgorithmID, dstPriceItems[0].AlgorithmID)
	assert.Equal(t, srcPriceItem.StairPriceType, dstPriceItems[0].StairPriceType)
	assert.Equal(t, param.EffectTime, dstPriceItems[0].EffectTime)
	assert.Equal(t, param.DeadTime, dstPriceItems[0].DeadTime)

	// check price_item_stairs
	dstPriceItemStairs, err := priceService.ListAllPriceItemStairsByPriceItemID(ctx, dstPriceItems[0].ID, 0, -1)
	assert.NoError(t, err)

	for i := range dstPriceItemStairs {
		assert.NotEqual(t, srcPriceItemStairs[i].ID, dstPriceItemStairs[i].ID)
		assert.Equal(t, srcPriceItemStairs[i].UnitID, dstPriceItemStairs[i].UnitID)
		assert.Equal(t, srcPriceItemStairs[i].Quantity, dstPriceItemStairs[i].Quantity)
		assert.Equal(t, srcPriceItemStairs[i].Price, dstPriceItemStairs[i].Price)
		assert.Equal(t, srcPriceItemStairs[i].Order, dstPriceItemStairs[i].Order)
	}

	// clean test datas
	_, err = priceService.DeletePriceTableByID(ctx, srcPriceTable.ID)
	assert.NoError(t, err)
	_, err = priceService.DeletePriceTableByID(ctx, dstPriceTable.ID)
	assert.NoError(t, err)
	_, err = priceService.DeletePriceItemByID(ctx, srcPriceItem.ID)
	assert.NoError(t, err)
	_, err = priceService.DeletePriceItemByID(ctx, dstPriceItems[0].ID)
	assert.NoError(t, err)
	for _, stair := range srcPriceItemStairs {
		_, err = priceService.DeletePriceItemStairByID(ctx, stair.ID)
		assert.NoError(t, err)
	}
	for _, stair := range dstPriceItemStairs {
		_, err = priceService.DeletePriceItemStairByID(ctx, stair.ID)
		assert.NoError(t, err)
	}
}

func TestMergePriceItems(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	type TestCase struct {
		title            string
		priceItems       []model.PriceItem
		mergedPriceItems []model.PriceItem
	}

	testCases := []TestCase{
		{
			title:            "nil priceItems",
			priceItems:       nil,
			mergedPriceItems: []model.PriceItem{},
		},
		{
			title:            "empty priceItems",
			priceItems:       []model.PriceItem{},
			mergedPriceItems: []model.PriceItem{},
		},
		{
			title: "same id and consecutive effective range, should merged",
			priceItems: []model.PriceItem{
				{
					ID:         1,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
				},
				{
					ID:         1,
					EffectTime: base.HNS(2000),
					DeadTime:   base.HNS(3000),
				},
				{
					ID:         2,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
				},
			},
			mergedPriceItems: []model.PriceItem{
				{
					ID:         1,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(3000),
				},
				{
					ID:         2,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
				},
			},
		},
		{
			title: "same id and inconsecutive effective range, should not merged",
			priceItems: []model.PriceItem{
				{
					ID:         1,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
				},
				{
					ID:         2,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
				},
				{
					ID:         1,
					EffectTime: base.HNS(2000),
					DeadTime:   base.HNS(3000),
				},
			},
			mergedPriceItems: []model.PriceItem{
				{
					ID:         1,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
				},
				{
					ID:         2,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
				},
				{
					ID:         1,
					EffectTime: base.HNS(2000),
					DeadTime:   base.HNS(3000),
				},
			},
		},
	}

	for _, testCase := range testCases {
		mergedPriceItems := priceService.MergePriceItems(testCase.priceItems)
		assert.Equal(t, testCase.mergedPriceItems, mergedPriceItems, testCase.title)
	}
}

func TestSuperposePriceItems(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	type TestCase struct {
		title            string
		priceItems       []model.PriceItem
		expectPriceItems []model.PriceItem
	}

	testCases := []TestCase{
		{
			title:            "nil priceItems",
			priceItems:       nil,
			expectPriceItems: []model.PriceItem{},
		},
		{
			title:            "empty priceItems",
			priceItems:       []model.PriceItem{},
			expectPriceItems: []model.PriceItem{},
		},
		{
			title: "consecutive effective range",
			priceItems: []model.PriceItem{
				{
					ID:         1,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         1,
					EffectTime: base.HNS(2000),
					DeadTime:   base.HNS(3000),
					UpdatedAt:  time.Date(2018, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         1,
					EffectTime: base.HNS(3000),
					DeadTime:   base.HNS(4000),
					UpdatedAt:  time.Date(2018, 1, 3, 0, 0, 0, 0, time.UTC),
				},
			},
			expectPriceItems: []model.PriceItem{
				{
					ID:         1,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(4000),
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			title: "inconsecutive effective range",
			priceItems: []model.PriceItem{
				{
					ID:         1,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         1,
					EffectTime: base.HNS(3000),
					DeadTime:   base.HNS(4000),
					UpdatedAt:  time.Date(2018, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         1,
					EffectTime: base.HNS(5000),
					DeadTime:   base.HNS(6000),
					UpdatedAt:  time.Date(2018, 1, 3, 0, 0, 0, 0, time.UTC),
				},
			},
			expectPriceItems: []model.PriceItem{
				{
					ID:         1,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(2000),
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         1,
					EffectTime: base.HNS(3000),
					DeadTime:   base.HNS(4000),
					UpdatedAt:  time.Date(2018, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         1,
					EffectTime: base.HNS(5000),
					DeadTime:   base.HNS(6000),
					UpdatedAt:  time.Date(2018, 1, 3, 0, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			title: "non-overlapping effective range",
			priceItems: []model.PriceItem{
				{
					ID:         1,
					EffectTime: base.HNS(1000),
					DeadTime:   base.HNS(3000),
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         2,
					EffectTime: base.HNS(2000),
					DeadTime:   base.HNS(4000),
					UpdatedAt:  time.Date(2018, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         3,
					EffectTime: base.HNS(0),
					DeadTime:   base.HNS(1500),
					UpdatedAt:  time.Date(2018, 1, 3, 0, 0, 0, 0, time.UTC),
				},
			},
			expectPriceItems: []model.PriceItem{
				{
					ID:         3,
					EffectTime: base.HNS(0),
					DeadTime:   base.HNS(1500),
					UpdatedAt:  time.Date(2018, 1, 3, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         1,
					EffectTime: base.HNS(1500),
					DeadTime:   base.HNS(2000),
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					ID:         2,
					EffectTime: base.HNS(2000),
					DeadTime:   base.HNS(4000),
					UpdatedAt:  time.Date(2018, 1, 2, 0, 0, 0, 0, time.UTC),
				},
			},
		},
	}

	for _, testCase := range testCases {
		priceItems := priceService.SuperposePriceItems(testCase.priceItems)
		assert.Equal(t, testCase.expectPriceItems, priceItems, testCase.title)
	}
}

func TestGroupPriceItemsByItemID(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	type TestCase struct {
		title              string
		priceItems         []model.PriceItem
		expectPriceItemMap map[uint64][]model.PriceItem
	}

	testCases := []TestCase{
		{
			title:              "nil priceItems",
			priceItems:         nil,
			expectPriceItemMap: map[uint64][]model.PriceItem{},
		},
		{
			title:              "empty priceItems",
			priceItems:         []model.PriceItem{},
			expectPriceItemMap: map[uint64][]model.PriceItem{},
		},
		{
			title: "normal case",
			priceItems: []model.PriceItem{
				{
					ID:     1,
					ItemID: 1,
				},
				{
					ID:     2,
					ItemID: 2,
				},
				{
					ID:     3,
					ItemID: 1,
				},
				{
					ID:     4,
					ItemID: 2,
				},
			},
			expectPriceItemMap: map[uint64][]model.PriceItem{
				1: {
					{
						ID:     1,
						ItemID: 1,
					},
					{
						ID:     3,
						ItemID: 1,
					},
				},
				2: {
					{
						ID:     2,
						ItemID: 2,
					},
					{
						ID:     4,
						ItemID: 2,
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		priceItemMap := priceService.GroupPriceItemsByItemID(testCase.priceItems)
		for itemID, priceItems := range priceItemMap {
			expectPriceItems := testCase.expectPriceItemMap[itemID]
			assert.Equal(t, expectPriceItems, priceItems, testCase.title)
		}
	}
}

func TestGroupPriceItemsByZoneID(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	type TestCase struct {
		title              string
		priceItems         []model.PriceItem
		expectPriceItemMap map[uint64][]model.PriceItem
	}

	testCases := []TestCase{
		{
			title:              "nil priceItems",
			priceItems:         nil,
			expectPriceItemMap: map[uint64][]model.PriceItem{},
		},
		{
			title:              "empty priceItems",
			priceItems:         []model.PriceItem{},
			expectPriceItemMap: map[uint64][]model.PriceItem{},
		},
		{
			title: "normal case",
			priceItems: []model.PriceItem{
				{
					ID:     1,
					ZoneID: 1,
				},
				{
					ID:     2,
					ZoneID: 2,
				},
				{
					ID:     3,
					ZoneID: 1,
				},
				{
					ID:     4,
					ZoneID: 2,
				},
			},
			expectPriceItemMap: map[uint64][]model.PriceItem{
				1: {
					{
						ID:     1,
						ZoneID: 1,
					},
					{
						ID:     3,
						ZoneID: 1,
					},
				},
				2: {
					{
						ID:     2,
						ZoneID: 2,
					},
					{
						ID:     4,
						ZoneID: 2,
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		priceItemMap := priceService.GroupPriceItemsByZoneID(testCase.priceItems)
		for zoneID, priceItems := range priceItemMap {
			expectPriceItems := testCase.expectPriceItemMap[zoneID]
			assert.Equal(t, expectPriceItems, priceItems, testCase.title)
		}
	}
}

func TestUpdateUserFinalPrice(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	var zoneID uint64 = uint64(time.Now().UnixNano())
	var itemID uint64 = uint64(time.Now().UnixNano())

	type Lifecycle struct {
		effectTime base.HNS
		deadTime   base.HNS
	}

	type TestCase struct {
		title                string
		itemTimeRanges       []Lifecycle
		expectItemTimeRanges []Lifecycle
	}

	testCases := []TestCase{
		{
			title: "non-overlapping lifecycle",
			itemTimeRanges: []Lifecycle{
				{
					effectTime: base.HNS(1000),
					deadTime:   base.HNS(2000),
				},
				{
					effectTime: base.HNS(3000),
					deadTime:   base.HNS(4000),
				},
				{
					effectTime: base.HNS(0),
					deadTime:   base.HNS(500),
				},
			},
			expectItemTimeRanges: []Lifecycle{
				{
					effectTime: base.HNS(0),
					deadTime:   base.HNS(500),
				},
				{
					effectTime: base.HNS(1000),
					deadTime:   base.HNS(2000),
				},
				{
					effectTime: base.HNS(3000),
					deadTime:   base.HNS(4000),
				},
			},
		},
		{
			title: "overlapping lifecycle",
			itemTimeRanges: []Lifecycle{
				{
					effectTime: base.HNS(1000),
					deadTime:   base.HNS(3000),
				},
				{
					effectTime: base.HNS(2000),
					deadTime:   base.HNS(4000),
				},
				{
					effectTime: base.HNS(0),
					deadTime:   base.HNS(1500),
				},
			},
			expectItemTimeRanges: []Lifecycle{
				{
					effectTime: base.HNS(0),
					deadTime:   base.HNS(1500),
				},
				{
					effectTime: base.HNS(1500),
					deadTime:   base.HNS(2000),
				},
				{
					effectTime: base.HNS(2000),
					deadTime:   base.HNS(4000),
				},
			},
		},
	}

	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	for _, testCase := range testCases {
		var uid uint64 = uint64(time.Now().UnixNano())
		for _, timeRange := range testCase.itemTimeRanges {
			userPriceTableParam := &service.UserPriceTableParam{
				UID: uid,
				Prices: service.PricesParam{
					Type: model.PriceTableTypeVIP,
					ItemPrices: []service.ItemPriceParam{
						{
							ItemID:         itemID,
							ZoneID:         zoneID,
							AlgorithmID:    2,
							StairPriceType: "test-stair-price-type",
							IsDisabled:     false,
							UnitRate:       1024,
							StairPrices: []service.StairPrice{
								{
									UnitID:   1,
									Quantity: 1000,
									Price:    base.NewNMoneyWithHighAccuracyI64(10),
								},
								{
									UnitID:   1,
									Quantity: 10000,
									Price:    base.NewNMoneyWithHighAccuracyI64(100),
								},
							},
							EffectTime: timeRange.effectTime,
							DeadTime:   timeRange.deadTime,
						},
					},
				},
			}

			_, err := priceService.CreateUserPrice(ctx, userPriceTableParam)
			assert.NoError(t, err)
		}

		err := priceService.UpdateUserFinalPrice(ctx, uid)
		assert.NoError(t, err)

		// check
		finalPriceTable, err := priceService.GetFinalPriceTableByUID(ctx, uid)
		assert.NoError(t, err)

		finalPriceItems, err := priceService.ListAllPriceItemsByPriceID(ctx, finalPriceTable.ID, 0, -1)
		assert.NoError(t, err)

		assert.Len(t, finalPriceItems, len(testCase.expectItemTimeRanges), testCase.title)

		for i := range finalPriceItems {
			assert.Equal(t, testCase.expectItemTimeRanges[i].effectTime, finalPriceItems[i].EffectTime, testCase.title)
			assert.Equal(t, testCase.expectItemTimeRanges[i].deadTime, finalPriceItems[i].DeadTime, testCase.title)
		}
	}
}

func TestGetTimeGaps(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	type TestCase struct {
		title      string
		start      time.Time
		end        time.Time
		priceItems []model.PriceItem
		gaps       []base.TimeRange
	}

	testCases := []TestCase{
		{
			title:      "nil priceItems",
			start:      time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
			end:        time.Date(2018, 5, 1, 0, 0, 0, 0, time.UTC),
			priceItems: nil,
			gaps: []base.TimeRange{
				{
					Start: time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
					End:   time.Date(2018, 5, 1, 0, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			title:      "empty priceItems",
			start:      time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
			end:        time.Date(2018, 5, 1, 0, 0, 0, 0, time.UTC),
			priceItems: []model.PriceItem{},
			gaps: []base.TimeRange{
				{
					Start: time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
					End:   time.Date(2018, 5, 1, 0, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			title: "no gap",
			start: time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
			end:   time.Date(2018, 5, 1, 0, 0, 0, 0, time.UTC),
			priceItems: []model.PriceItem{
				{
					EffectTime: base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2018, 3, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					EffectTime: base.NewHNS(time.Date(2018, 3, 1, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2018, 5, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			gaps: []base.TimeRange{},
		},
		{
			title: "no gap but start >= firstPriceItem.effect_time and end <= lastPriceItem.dead_time",
			start: time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
			end:   time.Date(2018, 5, 1, 0, 0, 0, 0, time.UTC),
			priceItems: []model.PriceItem{
				{
					EffectTime: base.NewHNS(time.Date(2017, 1, 1, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2018, 3, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					EffectTime: base.NewHNS(time.Date(2018, 3, 1, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2019, 5, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			gaps: []base.TimeRange{},
		},
	}

	for _, testCase := range testCases {
		gaps := priceService.GetTimeGaps(testCase.start, testCase.end, testCase.priceItems)
		assert.Equal(t, testCase.gaps, gaps, testCase.title)
	}
}

func TestGetEffectPriceTablesBeforeTime(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	type TestCase struct {
		title             string
		checkTime         time.Time
		priceTables       []model.PriceTable
		ExpectPriceTables []model.PriceTable
		hasError          bool
	}

	testCases := []TestCase{
		{
			title:             "empty price_tables",
			checkTime:         time.Date(2018, 1, 20, 0, 0, 0, 0, time.UTC),
			priceTables:       []model.PriceTable{},
			ExpectPriceTables: []model.PriceTable{},
			hasError:          false,
		},
		{
			title:     "all disabled vip price_tables",
			checkTime: time.Date(2018, 1, 20, 0, 0, 0, 0, time.UTC),
			priceTables: []model.PriceTable{
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: true,
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: true,
					UpdatedAt:  time.Date(2018, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: true,
					UpdatedAt:  time.Date(2018, 1, 3, 0, 0, 0, 0, time.UTC),
				},
			},
			ExpectPriceTables: []model.PriceTable{},
			hasError:          false,
		},
		{
			title:     "all enabled final price_tables",
			checkTime: time.Date(2018, 1, 20, 0, 0, 0, 0, time.UTC),
			priceTables: []model.PriceTable{
				{
					Type:       model.PriceTableTypeFinal,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeFinal,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeFinal,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 3, 0, 0, 0, 0, time.UTC),
				},
			},
			ExpectPriceTables: []model.PriceTable{},
			hasError:          false,
		},
		{
			title:     "all enabled vip price_tables",
			checkTime: time.Date(2018, 1, 20, 0, 0, 0, 0, time.UTC),
			priceTables: []model.PriceTable{
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 3, 0, 0, 0, 0, time.UTC),
				},
			},
			ExpectPriceTables: []model.PriceTable{
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 3, 0, 0, 0, 0, time.UTC),
				},
			},
			hasError: false,
		},
		{
			title:     "all enabled vip price_tables, but updated_time >= check_time",
			checkTime: time.Date(2018, 1, 20, 0, 0, 0, 0, time.UTC),
			priceTables: []model.PriceTable{
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 20, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 21, 0, 0, 0, 0, time.UTC),
				},
			},
			ExpectPriceTables: []model.PriceTable{
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
			},
			hasError: false,
		},
		{
			title:     "all enabled vip and final price_tables, but updated_time >= check_time",
			checkTime: time.Date(2018, 1, 20, 0, 0, 0, 0, time.UTC),
			priceTables: []model.PriceTable{
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeFinal,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 19, 0, 0, 0, 0, time.UTC),
				},
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 21, 0, 0, 0, 0, time.UTC),
				},
			},
			ExpectPriceTables: []model.PriceTable{
				{
					Type:       model.PriceTableTypeVIP,
					IsDisabled: false,
					UpdatedAt:  time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
				},
			},
			hasError: false,
		},
	}

	var uid uint64 = 1
	ctx := tz.WithRefLocation(context.Background(), time.UTC)
	for _, testCase := range testCases {
		uid++
		// create price_tables and price_user_maps
		for _, priceTable := range testCase.priceTables {
			p, err := priceService.CreatePriceTable(ctx, &priceTable)
			assert.NoError(t, err)

			_, err = priceService.CreatePriceUserMap(ctx, &model.PriceUserMap{
				UID:     uid,
				PriceID: p.ID,
			})
			assert.NoError(t, err)
		}

		actualPriceTables, err := priceService.GetEffectPriceTablesBeforeTime(ctx, uid, testCase.checkTime)
		assert.Equal(t, testCase.hasError, err != nil, testCase.title)
		assert.Equal(t, len(testCase.ExpectPriceTables), len(actualPriceTables), testCase.title)

		if err != nil {

			for i, expectPriceTable := range testCase.ExpectPriceTables {
				assert.Equal(t, expectPriceTable.Type, actualPriceTables[i].Type, testCase.title)
				assert.Equal(t, expectPriceTable.IsDisabled, actualPriceTables[i].IsDisabled, testCase.title)
				assert.Equal(t, expectPriceTable.UpdatedAt.Round(0), actualPriceTables[i].UpdatedAt.Round(0), testCase.title)
			}
		}
	}
}

func TestBatchCreatePriceItems(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	type TestCase struct {
		title              string
		PriceParam         service.PricesParam
		PriceItem          model.PriceItem
		ExpectedItemPrices []model.PriceItemStair
		hasError           bool
	}

	testCases := []TestCase{
		{
			title: "empty cost prices",
			PriceParam: service.PricesParam{
				Type: model.PriceTableTypeCost,
				ItemPrices: []service.ItemPriceParam{
					{
						ItemID:         1,
						ZoneID:         1,
						AlgorithmID:    1,
						StairPriceType: "UNITPRICE",
						EffectTime:     base.NewHNS(time.Date(2010, 1, 1, 0, 0, 0, 0, time.UTC)),
						DeadTime:       base.NewHNS(time.Now().AddDate(2, 0, 0)),
						StairPrices: []service.StairPrice{
							{
								Quantity: 0,
								UnitID:   1,
								Price:    base.NewNMoneyWithHighAccuracyI64(1500),
							},
						},
					},
				},
			},
			PriceItem: model.PriceItem{
				ItemID:         1,
				ZoneID:         1,
				AlgorithmID:    1,
				StairPriceType: "UNITPRICE",
			},
			ExpectedItemPrices: []model.PriceItemStair{
				{
					Quantity: 0,
					UnitID:   1,
					Price:    base.NewNMoneyWithHighAccuracyI64(1500),
				},
			},
			hasError: false,
		},
		{
			title: "overide cost prices",
			PriceParam: service.PricesParam{
				Type: model.PriceTableTypeCost,
				ItemPrices: []service.ItemPriceParam{
					{
						ItemID:         1,
						ZoneID:         1,
						AlgorithmID:    1,
						StairPriceType: "UNITPRICE",
						EffectTime:     base.NewHNS(time.Date(2010, 1, 1, 0, 0, 0, 0, time.UTC)),
						DeadTime:       base.NewHNS(time.Now().AddDate(2, 0, 0)),
						StairPrices: []service.StairPrice{
							{
								Quantity: 0,
								UnitID:   1,
								Price:    base.NewNMoneyWithHighAccuracyI64(1200),
							},
						},
					},
					{
						ItemID:         1,
						ZoneID:         2,
						AlgorithmID:    2,
						StairPriceType: "UNITPRICE",
						EffectTime:     base.NewHNS(time.Date(2010, 1, 1, 0, 0, 0, 0, time.UTC)),
						DeadTime:       base.NewHNS(time.Now().AddDate(2, 0, 0)),
						StairPrices: []service.StairPrice{
							{
								Quantity: 0,
								UnitID:   1,
								Price:    base.NewNMoneyWithHighAccuracyI64(1200),
							},
						},
					},
				},
			},
			PriceItem: model.PriceItem{
				ItemID:         1,
				ZoneID:         1,
				AlgorithmID:    1,
				StairPriceType: "UNITPRICE",
			},
			ExpectedItemPrices: []model.PriceItemStair{
				{
					Quantity: 0,
					UnitID:   1,
					Price:    base.NewNMoneyWithHighAccuracyI64(1200),
				},
			},
			hasError: false,
		},
	}

	ctx := tz.WithRefLocation(context.Background(), time.UTC)
	costTable, err := priceService.CreatePriceTable(ctx, &model.PriceTable{
		ID:   1,
		Type: model.PriceTableTypeCost,
	})
	assert.NoError(t, err)

	for _, testCase := range testCases {
		_, err := priceService.BatchCreatePrice(ctx, &testCase.PriceParam)
		assert.Equal(t, testCase.hasError, err != nil, testCase.title)
		if err != nil {
			continue
		}

		costPrice, err := priceService.QueryItemCostPriceWithDefault(ctx, &model.PriceItem{
			PriceID:        costTable.ID,
			ItemID:         testCase.PriceParam.ItemPrices[0].ItemID,
			ZoneID:         testCase.PriceParam.ItemPrices[0].ZoneID,
			AlgorithmID:    testCase.PriceParam.ItemPrices[0].AlgorithmID,
			StairPriceType: testCase.PriceParam.ItemPrices[0].StairPriceType,
		}, time.Now())
		assert.NoError(t, err, testCase.title)

		stairs, err := priceService.ListAllPriceItemStairsByPriceItemID(ctx, costPrice.ID, 0, 10000)
		assert.NoError(t, err, testCase.title)

		if err == nil {
			assert.Equal(t, len(testCase.ExpectedItemPrices), len(stairs), testCase.title)
			if len(testCase.ExpectedItemPrices) == len(stairs) {
				for i, stair := range stairs {
					assert.Equal(t, testCase.ExpectedItemPrices[i].Quantity, stair.Quantity, testCase.title)
					assert.Equal(t, testCase.ExpectedItemPrices[i].UnitID, stair.UnitID, testCase.title)
					assert.Equal(t, testCase.ExpectedItemPrices[i].Price, stair.Price, testCase.title)
				}
			}
		}
	}
}

func TestFillGapWithPriceItems(t *testing.T) {
	testCases := []struct {
		title            string
		gap              base.TimeRange
		inputPriceItems  []model.PriceItem
		filledPriceItems []model.PriceItem
	}{
		{
			title: "nil input price_items",
			gap: base.TimeRange{
				Start: time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2019, 1, 15, 0, 0, 0, 0, time.UTC),
			},
			inputPriceItems:  nil,
			filledPriceItems: []model.PriceItem{},
		},
		{
			title: "empty input price_items",
			gap: base.TimeRange{
				Start: time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2019, 1, 15, 0, 0, 0, 0, time.UTC),
			},
			inputPriceItems:  []model.PriceItem{},
			filledPriceItems: []model.PriceItem{},
		},
		{
			title: "no matched price_items",
			gap: base.TimeRange{
				Start: time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2019, 1, 15, 0, 0, 0, 0, time.UTC),
			},
			inputPriceItems: []model.PriceItem{
				{
					EffectTime: base.NewHNS(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC)),
				},
			},
			filledPriceItems: []model.PriceItem{},
		},
		{
			title: "partial matched price_items",
			gap: base.TimeRange{
				Start: time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2019, 1, 15, 0, 0, 0, 0, time.UTC),
			},
			inputPriceItems: []model.PriceItem{
				{
					EffectTime: base.NewHNS(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC)),
				},
				{
					EffectTime: base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2019, 1, 10, 0, 0, 0, 0, time.UTC)),
				},
			},
			filledPriceItems: []model.PriceItem{
				{
					EffectTime: base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2019, 1, 10, 0, 0, 0, 0, time.UTC)),
				},
			},
		},
		{
			title: "all matched price_items",
			gap: base.TimeRange{
				Start: time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2019, 1, 15, 0, 0, 0, 0, time.UTC),
			},
			inputPriceItems: []model.PriceItem{
				{
					EffectTime: base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2019, 1, 6, 0, 0, 0, 0, time.UTC)),
				},
				{
					EffectTime: base.NewHNS(time.Date(2019, 1, 10, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2019, 1, 15, 0, 0, 0, 0, time.UTC)),
				},
			},
			filledPriceItems: []model.PriceItem{
				{
					EffectTime: base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2019, 1, 6, 0, 0, 0, 0, time.UTC)),
				},
				{
					EffectTime: base.NewHNS(time.Date(2019, 1, 10, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2019, 1, 15, 0, 0, 0, 0, time.UTC)),
				},
			},
		},
	}

	for _, testCase := range testCases {
		actualFilledPriceItems := service.FillGapWithPriceItems(testCase.gap, testCase.inputPriceItems)
		assert.Equal(t, testCase.filledPriceItems, actualFilledPriceItems, testCase.title)
	}
}

func TestBatchListPriceItemStairs(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	// 准备测试数据
	stairs := []model.PriceItemStair{
		{
			PriceItemID: 1,
			UnitID:      1,
			Quantity:    1,
			Price:       base.NewNMoneyWithHighAccuracyI64(100),
			Order:       0,
		},
		{
			PriceItemID: 1,
			UnitID:      2,
			Quantity:    2,
			Price:       base.NewNMoneyWithHighAccuracyI64(200),
			Order:       0,
		},
		{
			PriceItemID: 3,
			UnitID:      3,
			Quantity:    3,
			Price:       base.NewNMoneyWithHighAccuracyI64(300),
			Order:       0,
		},
	}

	ctx := tz.WithRefLocation(context.Background(), time.UTC)
	for i, stair := range stairs {
		// create
		_, err := priceService.CreatePriceItemStair(ctx, &stair)
		if assert.NoError(t, err) {
			assert.NotZero(t, stair.ID, "CreatePriceItemStair return zero id")
		}
		stairs[i].ID = stair.ID
	}

	m, err := priceService.BatchListPriceItemStairs(ctx, []uint64{1, 2, 3})
	if assert.NoError(t, err) {
		assert.Len(t, m[1], 2)
		assert.Len(t, m[2], 0)
		assert.Len(t, m[3], 1)
	}
}

func TestBatchFillStairPrices(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	// 准备测试数据
	priceItems := []model.PriceItem{
		{ID: 1},
		{ID: 3},
	}

	stairs := []model.PriceItemStair{
		{
			PriceItemID: 1,
			UnitID:      1,
			Quantity:    1,
			Price:       base.NewNMoneyWithHighAccuracyI64(100),
			Order:       0,
		},
		{
			PriceItemID: 1,
			UnitID:      2,
			Quantity:    2,
			Price:       base.NewNMoneyWithHighAccuracyI64(200),
			Order:       0,
		},
		{
			PriceItemID: 3,
			UnitID:      3,
			Quantity:    3,
			Price:       base.NewNMoneyWithHighAccuracyI64(300),
			Order:       0,
		},
	}

	ctx := tz.WithRefLocation(context.Background(), time.UTC)
	for i, stair := range stairs {
		// create
		_, err := priceService.CreatePriceItemStair(ctx, &stair)
		if assert.NoError(t, err) {
			assert.NotZero(t, stair.ID, "CreatePriceItemStair return zero id")
		}
		stairs[i].ID = stair.ID
	}

	actualPriceItems, err := priceService.BatchFillStairPrices(ctx, priceItems)
	if assert.NoError(t, err) {
		assert.Len(t, actualPriceItems, len(priceItems))
	}

	priceItem1 := actualPriceItems[0]
	assert.Len(t, priceItem1.Stairs, 2)
	assert.Equal(t, stairs[0].PriceItemID, priceItem1.Stairs[0].PriceItemID)
	assert.Equal(t, stairs[0].UnitID, priceItem1.Stairs[0].UnitID)
	assert.Equal(t, stairs[0].Quantity, priceItem1.Stairs[0].Quantity)
	assert.Equal(t, stairs[0].Price, priceItem1.Stairs[0].Price)

	assert.Equal(t, stairs[1].PriceItemID, priceItem1.Stairs[1].PriceItemID)
	assert.Equal(t, stairs[1].UnitID, priceItem1.Stairs[1].UnitID)
	assert.Equal(t, stairs[1].Quantity, priceItem1.Stairs[1].Quantity)
	assert.Equal(t, stairs[1].Price, priceItem1.Stairs[1].Price)

	priceItem3 := actualPriceItems[1]
	assert.Len(t, priceItem3.Stairs, 1)
	assert.Equal(t, stairs[2].PriceItemID, priceItem3.Stairs[0].PriceItemID)
	assert.Equal(t, stairs[2].UnitID, priceItem3.Stairs[0].UnitID)
	assert.Equal(t, stairs[2].Quantity, priceItem3.Stairs[0].Quantity)
	assert.Equal(t, stairs[2].Price, priceItem3.Stairs[0].Price)
}

func TestPriceBizService_GetPublicItemPriceByItemIDZoneIDAndTimeRange(t *testing.T) {
	sandbox := buildSandbox(t)

	// 目的：对于用户变更过计费方式的情况，测试用户某个时间段的公开价，查看获取公开价是否符合预期
	// 一、构造数据：1.填入公开价 2.设置用户的计费方式偏好
	// 二、获取时间段内的公开价

	priceService := sandbox.priceService
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	// 改成固定时间，否则，不同时间获取到的分段可能不同
	now := time.Date(2020, 9, 27, 11, 11, 11, 1, time.UTC)
	// 比如 9.1
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC)
	// 比如 11.1
	next2MonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC).AddDate(0, 2, 0)

	uid := uint64(1)
	publicPriceTableParam := &model.PriceTable{
		Type:       model.PriceTableTypeDefault,
		Remark:     "",
		IsDisabled: false,
	}
	publicPriceTable, err := priceService.CreatePriceTable(ctx, publicPriceTableParam)
	assert.NoError(t, err)
	// 计费方式 1 价格段 1 起始时间
	algorithm1PriceSpan1EffectTime := monthStart.AddDate(0, 0, -3)
	// 计费方式 1 价格段 1 结束时间
	algorithm1PriceSpan1DeadTime := now.AddDate(0, 0, -1)
	algorithm1PriceSpan2EffectTime := algorithm1PriceSpan1DeadTime
	algorithm1PriceSpan2DeadTime := next2MonthStart.AddDate(0, 0, 1)

	algorithm2PriceSpan1EffectTime := monthStart
	algorithm2PriceSpan1DeadTime := now.AddDate(0, 0, 2)
	algorithm2PriceSpan2EffectTime := algorithm2PriceSpan1DeadTime
	algorithm2PriceSpan2DeadTime := next2MonthStart.AddDate(0, 0, 1)
	// 填入公开价
	err = priceService.BatchCreateItemPrices(ctx, publicPriceTable.ID, "", []service.ItemPriceParam{
		{
			// 计费方式 1 价格段 1
			ItemID:             1,
			ZoneID:             1,
			AlgorithmID:        1,
			StairPriceType:     "UNITPRICE",
			IsDisabled:         false,
			IsDefaultAlgorithm: true,
			UnitRate:           0,
			Type:               model.PriceTableTypeDefault,
			CurrencyType:       base.CurrencyTypeCNY,
			EffectTime:         base.NewHNS(algorithm1PriceSpan1EffectTime),
			DeadTime:           base.NewHNS(algorithm1PriceSpan1DeadTime),
		}, {
			// 计费方式 1 价格段 2
			ItemID:             1,
			ZoneID:             1,
			AlgorithmID:        1,
			StairPriceType:     "UNITPRICE",
			IsDisabled:         false,
			IsDefaultAlgorithm: true,
			UnitRate:           0,
			Type:               model.PriceTableTypeDefault,
			CurrencyType:       base.CurrencyTypeCNY,
			EffectTime:         base.NewHNS(algorithm1PriceSpan2EffectTime),
			DeadTime:           base.NewHNS(algorithm1PriceSpan2DeadTime),
		}, {
			// 计费方式 2 价格段 1
			ItemID:         1,
			ZoneID:         1,
			AlgorithmID:    2,
			StairPriceType: "UNITPRICE",
			IsDisabled:     false,
			UnitRate:       0,
			Type:           model.PriceTableTypeDefault,
			CurrencyType:   base.CurrencyTypeCNY,
			EffectTime:     base.NewHNS(algorithm2PriceSpan1EffectTime),
			DeadTime:       base.NewHNS(algorithm2PriceSpan1DeadTime),
		}, {
			// 计费方式 2 价格段 2
			ItemID:         1,
			ZoneID:         1,
			AlgorithmID:    2,
			StairPriceType: "UNITPRICE",
			IsDisabled:     false,
			UnitRate:       0,
			Type:           model.PriceTableTypeDefault,
			CurrencyType:   base.CurrencyTypeCNY,
			EffectTime:     base.NewHNS(algorithm2PriceSpan2EffectTime),
			DeadTime:       base.NewHNS(algorithm2PriceSpan2DeadTime),
		},
	})
	assert.NoError(t, err)

	switchParam := &service.SwitchPriceUserAlgorithmByID{
		UID:             uid,
		PriceID:         publicPriceTable.ID,
		ItemID:          1,
		ZoneID:          1,
		FromAlgorithmID: 1,
		ToAlgorithmID:   2,
		SwitchAt:        &now,
	}
	// 切换用户计费方式偏好
	err = priceService.SwitchUserPriceAlgorithmByID(ctx, switchParam)
	assert.NoError(t, err)

	getPublicItemPriceParam := &service.BatchItemPricesWithTimeRangeParam{
		UID:          uid,
		ItemIDs:      []uint64{1},
		ZoneID:       1,
		PriceTableID: publicPriceTable.ID,
		StartTime:    base.NewHNS(monthStart),
		EndTime:      base.NewHNS(next2MonthStart),
	}
	publicPriceItems, err := priceService.GetPublicItemPriceByItemIDZoneIDAndTimeRange(
		ctx, getPublicItemPriceParam, base.CurrencyTypeCNY)
	assert.NoError(t, err)
	assert.Equal(t, 4, len(publicPriceItems))

	/* 这个 case，用户的公开价，应该是被分成了四段：
	背景：对 algorithmID = 1 两段公开价，algorithmID = 2 两段公开价（以 effect_time,dead_time 为界），以及用户偏好进行叠加
	计费方式 1： |------------------|--------------|
	计费方式 2：   |--------------------|----------|
	用户偏好：  ----------------------|---------------
	最终取值：      |---------------|-|-|-------|
	结果值分成了四段。
	*/
	if len(publicPriceItems) == 4 {
		assert.Equal(t, publicPriceItems[0].EffectTime, base.NewHNS(monthStart))
		assert.Equal(t, publicPriceItems[0].DeadTime, base.NewHNS(now.AddDate(0, 0, -1)))
		assert.Equal(t, publicPriceItems[1].EffectTime, base.NewHNS(now.AddDate(0, 0, -1)))
		assert.WithinDuration(t, now, publicPriceItems[1].DeadTime.TimeIn(time.UTC), 48*time.Hour)
		assert.WithinDuration(t, now, publicPriceItems[2].EffectTime.TimeIn(time.UTC), 48*time.Hour)
		assert.Equal(t, publicPriceItems[2].DeadTime, base.NewHNS(now.AddDate(0, 0, 2)))
		assert.Equal(t, publicPriceItems[3].EffectTime, base.NewHNS(now.AddDate(0, 0, 2)))
		assert.Equal(t, publicPriceItems[3].DeadTime, base.NewHNS(next2MonthStart))
	}
}
