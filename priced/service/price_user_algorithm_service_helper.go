package service

import (
	"context"
	"math"
	"sort"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/dict"
	"github.com/qbox/pay-sdk/errcode"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/priced/model"
)

// PriceRangeItem 价格阶梯
type PriceRangeItem struct {
	// 该阶梯计量的门槛值
	Threshold uint64
	// 顺序
	Order uint64
	// 价格
	Price *base.NMoney
}

type priceRangeItemSorter []*PriceRangeItem

func (s priceRangeItemSorter) Len() int      { return len(s) }
func (s priceRangeItemSorter) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s priceRangeItemSorter) Less(i, j int) bool {
	if s[i].Threshold < s[j].Threshold {
		return true
	} else if s[i].Threshold > s[j].Threshold {
		return false
	}

	return s[i].Order < s[j].Order
}

// isSwitchWithinADay 检查是否一天之内设置过
func (s *PriceBizService) isSwitchWithinADay(ctx context.Context, uid, itemID, zoneID uint64, now time.Time) (bool, *model.PriceUserAlgorithm, error) {
	l := logging.GetLogger(ctx)
	getLastByCondsParam := &model.GetLastByCondsParam{
		UID:    uid,
		ItemID: itemID,
		ZoneID: zoneID,
	}

	// 先确认用户 1 天之内是否设置过
	lastUserAlgorithm, err := s.priceDao.PriceUserAlgorithm.GetLastByConds(getLastByCondsParam)
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		l.WithField("getLastByCondsParam", getLastByCondsParam).WithError(err).
			Error("SwitchUserPriceAlgorithmByID invoke GetLastByConds error")
		return false, nil, errors.Trace(err)
	}

	if !gorm.IsRecordNotFoundError(err) &&
		lastUserAlgorithm.CreatedAt.Format(timeFormat) == now.Format(timeFormat) {
		return true, lastUserAlgorithm, nil
	}

	return false, lastUserAlgorithm, nil
}

// hasVIPPrice 是否设置了 vip 价格
func (s *PriceBizService) hasVIPPrice(ctx context.Context, uid, itemID, zoneID uint64, now time.Time) (bool, error) {
	l := logging.GetLogger(ctx)
	// 判断当前的用户是否有 VIP 价格
	userPriceWithTimePointParam := &UserPriceWithTimePointParam{
		UID:    uid,
		ZoneID: zoneID,
		At:     base.NewHNS(now),
	}
	userVIPPriceItemsResp, err := s.GetUserPriceWithTimePoint(ctx, userPriceWithTimePointParam)
	if err != nil && !errcode.IsNotFound(errors.Cause(err)) {
		l.WithField("userPriceWithTimePointParam", userPriceWithTimePointParam).WithError(err).
			Error("SwitchUserPriceAlgorithmByID invoke GetUserPriceWithTimePoint error")
		return false, errors.Trace(err)
	}
	// 如果存在 (item, zone) 对应的条目，说明当前存在生效的 VIP 价格，那么不允许切换
	if userVIPPriceItemsResp != nil {
		if _, ok := userVIPPriceItemsResp.ItemPrices[itemID]; ok {
			return true, nil
		}
	}
	return false, nil
}

// isFromAlgoIDMatchUserCurrAlgo 入参 fromAlgorithmID 是否和用户当前的计费方式匹配
func (s *PriceBizService) isFromAlgoIDMatchUserCurrAlgo(
	ctx context.Context,
	uid, itemID, zoneID, fromAlgorithmID uint64,
	now time.Time,
	fromAlgoRelatedPriceItem *model.PriceItem,
) (bool, error) {
	l := logging.GetLogger(ctx)
	listByCondsParam := &model.ListByCondsParam{
		UID:       uid,
		ItemIDs:   []uint64{itemID},
		ZoneID:    zoneID,
		StartTime: base.NewHNS(now),
		EndTime:   base.NewHNS(now),
	}
	usingAlgorithms, err := s.priceDao.PriceUserAlgorithm.ListByConds(listByCondsParam)
	if err != nil {
		return false, errors.Trace(err)
	}

	l.WithFields(logrus.Fields{
		"from_algorithm":   fromAlgoRelatedPriceItem,
		"using_algorithms": usingAlgorithms,
	}).Info("<SwitchUserPriceAlgorithmByID.isFromAlgoIDMatchUserCurrAlgo> check if fromAlgorithmID matching usingAlgorithmID")

	// 判断当前的计费方式和参数传入的 algorithmID 一致
	if len(usingAlgorithms) == 0 {
		if !fromAlgoRelatedPriceItem.IsDefaultAlgorithm {
			return false, nil
		}
	} else {
		if usingAlgorithms[0].AlgorithmID != fromAlgorithmID {
			return false, nil
		}
	}
	return true, nil
}

// convertQuantityByUnit 把 quantity 换算到 anchor 所在阶梯，从 fromUnitPower 转换到 toUnitPower
// convertedQuantity = quantity * unitRate ** (fromUnitPower - toUnitPower)
// 例如：unitRate = 1024, units = GB (power 3, anchor), TB (power 4), PB (power 5)
// quantity = 50 TB ，转换后结果为 50 * 1024 ** (4 - 3) = 51200
func convertQuantityByUnit(quantity uint64, unitRate uint64, fromUnitPower uint64, toUnitPower uint64) uint64 {
	unitPowerDelta := float64(fromUnitPower - toUnitPower)
	toUnitQuantity := math.Pow(float64(unitRate), unitPowerDelta)
	return uint64(float64(quantity) * toUnitQuantity)
}

// getPriceInfo 获取用户当前计费方式对应的价格信息
// TODO 看是否需要统一入参为 model 中的定义
func (s *PriceBizService) getPriceInfo(
	ctx context.Context,
	priceItem *model.PriceItem,
	itemDataType *pb.ItemDataType,
) (*PriceInfo, error) {
	// 获取价格阶梯
	priceItemStairs, err := s.ListAllPriceItemStairsByPriceItemID(ctx, priceItem.ID, 0, largePageSize)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 获取计费项数据类型对应的计费项单位
	itemDataTypeUnitsList, err := s.dictClient.ListItemDataTypeUnitsByItemDataTypeID(
		ctx,
		&pb.IDPagingParam{
			Id:       itemDataType.GetId(),
			Page:     1,
			PageSize: largePageSize,
		},
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 获取单位进率
	unitRate := priceItem.UnitRate
	if unitRate == 0 {
		unitRate = itemDataType.GetUnitRate()
	}

	priceStairs, err := buildPriceStairsByPriceRangeItems(priceItemStairs, itemDataTypeUnitsList.GetItemDataTypeUnits(), unitRate)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &PriceInfo{
		PriceStairs:    priceStairs,
		StairPriceType: priceItem.StairPriceType,
		CurrencyType:   priceItem.CurrencyType,
	}, nil
}

// buildPriceStairsByPriceRangeItems 从 priceRangeItems 以及其他相关配置构造价格阶梯信息
func buildPriceStairsByPriceRangeItems(
	priceItemStairs []model.PriceItemStair,
	units []*pb.ItemDataTypeUnit,
	unitRate uint64,
) ([]*PriceStair, error) {
	priceRangeItems := make([]*PriceRangeItem, len(priceItemStairs))
	for i, stair := range priceItemStairs {
		priceRangeItem, err := buildPriceRangeItemByStair(stair, units, unitRate)
		if err != nil {
			return nil, errors.Trace(err)
		}
		priceRangeItems[i] = priceRangeItem
	}

	sort.Sort(priceRangeItemSorter(priceRangeItems))

	priceStairs := make([]*PriceStair, len(priceItemStairs))
	from := uint64(0)
	for i, priceRangeItem := range priceRangeItems {
		priceStairs[i] = &PriceStair{
			From:  from,
			To:    priceRangeItem.Threshold,
			Price: priceRangeItem.Price,
		}
		from = priceRangeItem.Threshold
	}

	return priceStairs, nil
}

// buildPriceRangeItemByStair 从 PriceItemStair (以及其他相关配置) 构造价格阶梯中间表示
func buildPriceRangeItemByStair(
	priceItemStair model.PriceItemStair,
	units []*pb.ItemDataTypeUnit,
	unitRate uint64,
) (*PriceRangeItem, error) {
	if len(units) == 0 {
		return nil, errors.New("no units")
	}

	var thisUnit, anchorUnit *pb.ItemDataTypeUnit

	// 找到 anchor unit 和当前阶梯所参照的 unit
	for _, unit := range units {
		if unit.GetId() == priceItemStair.UnitID {
			thisUnit = unit
		}

		if unit.GetIsPriceAnchor() {
			anchorUnit = unit
		}
	}

	if thisUnit == nil {
		return nil, errors.Errorf("invalid unit reference: priceItemStair=%+v but units=%+v", priceItemStair, units)
	}

	if anchorUnit == nil {
		return nil, errors.Errorf("no anchor unit found for units=%+v", units)
	}

	// 把 priceItemStair.Quantity 换算到 anchor 所在阶梯
	convertedQuantity := convertQuantityByUnit(priceItemStair.Quantity, unitRate, thisUnit.GetPower(), anchorUnit.GetPower())

	return &PriceRangeItem{
		Threshold: convertedQuantity,
		Order:     priceItemStair.Order,
		Price:     priceItemStair.Price,
	}, nil
}
