package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measured/model"
)

func TestItemDataTypeExtraArgRoundTrip(t *testing.T) {
	x := &model.ItemDataTypeExtraArg{
		ID:             123,
		ItemDataTypeID: 233,
		Key:            "foo",
		Value:          "bar=2&baz=3&quux=%20",
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	y, err := BuildPbItemDataTypeExtraArg(x)
	assert.NoError(t, err)

	z, err := BuildModelItemDataTypeExtraArg(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.ItemDataTypeID, z.ItemDataTypeID)
	assert.Equal(t, x.Key, z.Key)
	assert.Equal(t, x.Value, z.Value)
	assert.Equal(t, x.CreatedAt.Unix<PERSON>ano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
}
