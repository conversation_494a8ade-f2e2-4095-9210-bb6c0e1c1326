package dao

import (
	"fmt"

	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

// NoticeRecordDao is data access object of Notice model
type NoticeRecordDao struct {
	base *dao.BaseDao
}

// NewNoticeRecordDao is constructor of NoticeRecordDao
func NewNoticeRecordDao(base *dao.BaseDao) *NoticeRecordDao {
	return &NoticeRecordDao{
		base: base,
	}
}

// DoTransaction do a transaction
func (d *NoticeRecordDao) DoTransaction(fn func(*NoticeRecordDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		noticeDao := NewNoticeRecordDao(base)
		return fn(noticeDao)
	})
}

// RegisterMigrateNotice migrates notice model
func RegisterMigrateNotice(db *gorm.DB) {
	db.AutoMigrate(&model.NoticeRecord{})
}

// Save inserts or updates a record of Credit by id
func (d *NoticeRecordDao) Save(m *model.NoticeRecord) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.Save(m).Error

	if err != nil {
		return errors.Trace(err).WithField("model", m)
	}

	return nil
}

// Update updates a record of NoticeRecord by id
func (d *NoticeRecordDao) Update(m *model.NoticeRecord) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.Model(&model.NoticeRecord{}).Updates(m).Find(m).Error

	if err != nil {
		return errors.Trace(err).WithField("id", m.ID)
	}
	return nil
}

// GetByID select a record of NoticeRecord by id
func (d *NoticeRecordDao) GetByID(id uint64) (*model.NoticeRecord, error) {
	m := &model.NoticeRecord{ID: id}
	err := d.base.First(m, id).Error
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return m, nil
}

// UpdateStatusToCanceled updates a record of NoticeRecord by uid
func (d *NoticeRecordDao) UpdateStatusToCanceled(uid uint64,
	event enums.Event,
	status enums.NoticeStatus) error {
	err := d.base.Model(&model.NoticeRecord{}).Where("uid = ? and event = ? and status = ?", uid, event, status).
		Updates(model.NoticeRecord{
			Status: enums.NoticeStatusCanceled,
			Remark: fmt.Sprintf("%s->%s", status, enums.NoticeStatusCanceled),
		}).Error

	if err != nil {
		return errors.Trace(err).WithFields(errors.Fields{
			"uid":    uid,
			"event":  event,
			"status": status,
		})
	}
	return nil
}

// ListAll select all records
func (d *NoticeRecordDao) ListAll(status []enums.NoticeStatus, offset, limit int) (list []model.NoticeRecord, err error) {
	err = d.base.Offset(offset).Limit(limit).Where("`status` in (?)", status).Find(&list).Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": offset,
			"limit":  limit,
			"status": status,
		})
	}
	return
}

// ListExpiredIDs selects all expired record ids
func (d *NoticeRecordDao) ListExpiredIDs() (list []model.ExpiredNotice, err error) {
	notices := make([]model.NoticeRecord, 0)
	err = d.base.Select("id, uid").
		Where("`status` = ? AND now() >= `expired_at`", enums.NoticeStatusDelayed).
		Find(&notices).
		Error

	list = make([]model.ExpiredNotice, len(notices))
	for i, notice := range notices {
		list[i].ID = notice.ID
		list[i].UID = notice.UID
	}

	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"status": enums.NoticeStatusDelayed,
		})
	}
	return
}
