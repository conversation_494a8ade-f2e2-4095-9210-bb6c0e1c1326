package service

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/test"
	"github.com/qbox/pay-sdk/base/account"
	mock_wallet "github.com/qbox/pay-sdk/mocks/wallet"
	walletpb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/creditd/config"
	creditDao "qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

type noticeSrvSandbox struct {
	testWrap           *test.Wrap
	noticeSrv          *NotificationService
	uidInfoServer      *httptest.Server
	salesInfoServer    *httptest.Server
	morseServer        *httptest.Server
	notificationServer *httptest.Server
	paymentServiceCl   walletpb.PaymentServiceClient
}

func newNoticeSrvSandbox(t *testing.T) *noticeSrvSandbox {
	ctrl := gomock.NewController(t)

	uidInfoServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		io.WriteString(w, `{"data":{"email":"<EMAIL>","phoneNumber":"***********"}}`)
	}))

	salesInfoServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		io.WriteString(w, `{"data":{"email":"<EMAIL>","mobile":"17702177771"}}`)
	}))

	morseServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	notificationServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	mockPaymentServiceClient := mock_wallet.NewMockPaymentServiceClient(ctrl)
	{
		mockPaymentServiceClient.EXPECT().GetSingleCurrency(
			gomock.Any(),
			gomock.Any(),
		).AnyTimes().DoAndReturn(func(ctx context.Context, req *walletpb.UIDParam, opts ...any) (*walletpb.Currency, error) {
			return &walletpb.Currency{
				Uid:          req.Uid,
				CurrencyType: base.CurrencyTypeCNY.String(),
			}, nil
		})
	}
	paymentServiceCl := mockPaymentServiceClient

	testWrap, err := test.NewTestWrap(
		t,
		test.WithMigrateFuncs(creditDao.RegisterMigrateNotice, creditDao.RegisterMigrate),
	)
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in credit service return error")
	}

	cDao := creditDao.NewCreditDao(testWrap.BaseDao())
	accServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		io.WriteString(w, `{"code":200,"message":""}`)
	}))

	conf := &config.CreditdConfig{
		AccConfig: account.AccConfig{
			Host:     accServer.URL, // oauth2/token will be appended in roundTripper
			UserName: "root",
			Password: "root",
			ClientID: "abcd",
		},
		Services: config.ServiceConfig{
			GaeaAdmin:      uidInfoServer.URL,
			Sofa:           salesInfoServer.URL,
			Morse:          morseServer.URL,
			NotificationV2: notificationServer.Listener.Addr().String(),
		},
		NoticeConfig: config.NoticeConfig{
			DelayedDuration: 48 * time.Hour,
		},
	}
	nDao := creditDao.NewNoticeRecordDao(testWrap.BaseDao())
	noticeSrv, err := NewNotificationService(conf, cDao, nDao, mockPaymentServiceClient)
	if err != nil {
		t.Fatal(err)
	}

	t.Cleanup(func() {
		uidInfoServer.Close()
		salesInfoServer.Close()
		morseServer.Close()
	})

	return &noticeSrvSandbox{
		testWrap:           testWrap,
		noticeSrv:          noticeSrv,
		uidInfoServer:      uidInfoServer,
		salesInfoServer:    salesInfoServer,
		morseServer:        morseServer,
		notificationServer: notificationServer,
		paymentServiceCl:   paymentServiceCl,
	}
}

func TestNotificationService_getEmailAndPhone(t *testing.T) {
	sandbox := newNoticeSrvSandbox(t)

	userNTI, err := sandbox.noticeSrv.getUserEmailAndPhone(sandbox.uidInfoServer.URL, 123456789)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, "<EMAIL>", userNTI.GetEmail())
	assert.Equal(t, "***********", userNTI.GetPhoneNumber())

	salesNTI, err := sandbox.noticeSrv.getSalesEmailAndPhone(sandbox.salesInfoServer.URL, 183952789)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, "<EMAIL>", salesNTI.GetEmail())
	assert.Equal(t, "17702177771", salesNTI.GetPhoneNumber())
}

func TestNotificationService_send(t *testing.T) {
	sandbox := newNoticeSrvSandbox(t)

	// build mail
	mail := model.Email{
		UID:     1,
		To:      []string{"<EMAIL>"},
		Subject: "test_subject",
		Content: "test_content",
	}

	err := sandbox.noticeSrv.send(mail, "")
	assert.NoError(t, err)

	// test fail case
	morseFailServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusBadGateway)
	}))
	defer morseFailServer.Close()

	sandbox.morseServer = morseFailServer
	sandbox.noticeSrv.conf.Services.Morse = morseFailServer.URL

	ns, err := NewNotificationService(
		sandbox.noticeSrv.conf,
		sandbox.noticeSrv.creditDao,
		sandbox.noticeSrv.noticeRecordDao,
		sandbox.paymentServiceCl,
	)
	if err != nil {
		t.Fatal(err)
	}
	err = ns.send(mail, "")
	assert.Error(t, err)
}

func TestNotificationService_SendExpiredNotices(t *testing.T) {
	sandbox := newNoticeSrvSandbox(t)

	records := []model.NoticeRecord{
		{
			UID:        1,
			Event:      enums.EventCreate,
			NoticeType: enums.TypeScheduled,
			Status:     enums.NoticeStatusDelayed,
			TriggerAt:  time.Now().AddDate(0, -1, 0),
			ExpiredAt:  time.Now().AddDate(0, -1, 0),
			Remark:     "test uid 1",
		}, {
			UID:        2,
			Event:      enums.EventCreate,
			NoticeType: enums.TypeScheduled,
			Status:     enums.NoticeStatusDelayed,
			TriggerAt:  time.Now().AddDate(0, -1, 0),
			ExpiredAt:  time.Now().AddDate(0, -1, 0),
			Remark:     "test uid 2",
		}, {
			UID:        3,
			Event:      enums.EventCreate,
			NoticeType: enums.TypeScheduled,
			Status:     enums.NoticeStatusDelayed,
			TriggerAt:  time.Now().AddDate(0, -1, 0),
			ExpiredAt:  time.Now().AddDate(0, -1, 0),
			Remark:     "test uid 3",
		}, {
			UID:        7,
			Event:      enums.EventCreate,
			NoticeType: enums.TypeScheduled,
			Status:     enums.NoticeStatusDelayed,
			TriggerAt:  time.Now().AddDate(0, -1, 0),
			ExpiredAt:  time.Now().AddDate(0, -1, 0),
			Remark:     "test uid 7",
		},
	}

	for _, record := range records {
		err := sandbox.noticeSrv.noticeRecordDao.Save(&record)
		if err != nil {
			t.Fatal(err)
		}
	}

	noticeRecords, err := sandbox.noticeSrv.noticeRecordDao.ListAll([]enums.NoticeStatus{enums.NoticeStatusDelayed}, 0, 100)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, len(records), len(noticeRecords))
	total, failCount, err := sandbox.
		noticeSrv.
		SendExpiredNotices(context.Background(), sandbox.noticeSrv.BuildNotification(config.CreatedTmplUnit))

	assert.Equal(t, len(records), total)
	assert.Equal(t, 0, failCount)
	assert.NoError(t, err)
}
