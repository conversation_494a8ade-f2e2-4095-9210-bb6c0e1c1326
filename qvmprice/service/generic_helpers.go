package service

func mapSlice[T any, U any](fn func(T) U, src []T) []U {
	result := make([]U, len(src))
	for i := range src {
		result[i] = fn(src[i])
	}

	return result
}

func mapAndFlattenSlice[T any, U any](fn func(T) []U, src []T) []U {
	var result []U
	for i := range src {
		result = append(result, fn(src[i])...)
	}

	return result
}

func mapAndFlattenSliceFallible[T any, U any](fn func(T) ([]U, error), src []T) ([]U, error) {
	var result []U
	for i := range src {
		l, err := fn(src[i])
		if err != nil {
			return nil, err
		}
		result = append(result, l...)
	}

	return result, nil
}

func enumerateMapSlice[T any, U any](fn func(idx int, elem T) U, src []T) []U {
	result := make([]U, len(src))
	for i := range src {
		result[i] = fn(i, src[i])
	}

	return result
}

func filter[T any](predicate func(T) bool, src []T) []T {
	// NOTE: 此处不做 capacity 优化，是因为并非所有 filter 的场景都只过滤掉少数元素
	// 如果有必要的话可以另加一个类似于 filterLikely 的函数
	var result []T
	for idx := range src {
		if !predicate(src[idx]) {
			continue
		}
		result = append(result, src[idx])
	}

	return result
}

// select distinct <some field> from src (set result)
func distinctSet[T any, U comparable](
	fn func(T) U,
	src []T,
) map[U]struct{} {
	seen := map[U]struct{}{}
	for idx := range src {
		y := fn(src[idx])
		if _, ok := seen[y]; ok {
			continue
		}

		seen[y] = struct{}{}
	}

	return seen
}

// select distinct <some field> from src (slice result)
func distinctSlice[T any, U comparable](
	fn func(T) U,
	src []T,
) []U {
	var result []U
	seen := map[U]struct{}{}
	for idx := range src {
		y := fn(src[idx])
		if _, ok := seen[y]; ok {
			continue
		}

		result = append(result, y)
		seen[y] = struct{}{}
	}

	return result
}

// select distinct <some field> from src (slice result, controllable deduplication key)
func distinctSliceWithKey[T any, U any, ComparisonKey comparable](
	keyFn func(T) ComparisonKey,
	fn func(T) U,
	src []T,
) []U {
	var result []U
	seen := map[ComparisonKey]struct{}{}
	for idx := range src {
		k := keyFn(src[idx])
		if _, ok := seen[k]; ok {
			continue
		}
		seen[k] = struct{}{}

		y := fn(src[idx])
		result = append(result, y)
	}

	return result
}

func mergeDistinctSlice[T comparable](
	src ...[]T,
) []T {
	var result []T
	seen := map[T]struct{}{}
	for _, l := range src {
		for _, x := range l {
			if _, ok := seen[x]; ok {
				continue
			}
			result = append(result, x)
			seen[x] = struct{}{}
		}
	}

	return result
}

// []T -> map[someField]someOtherField
//
// 按传入 slice 的顺序逐个处理。如果有返回的 Key 重复了：
// firstOneWins == true 则只保留第一个 Value，否则只保留最后一个 Value。
// 一般如果没有特别需求都可以传 false。
func listToMap[T any, Key comparable, Val any](
	fn func(T) (Key, Val),
	src []T,
	firstOneWins bool,
) map[Key]Val {
	result := map[Key]Val{}
	for idx := range src {
		k, v := fn(src[idx])
		_, seen := result[k]
		if seen && firstOneWins {
			// 如果 lastOneWins 也就是 !firstOneWins，那么对于 key 冲突的情况，
			// 每次都应该覆盖之前的值
			// 所以只有 firstOneWins 时候才应该 continue
			continue
		}

		result[k] = v
	}

	return result
}
