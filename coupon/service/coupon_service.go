package service

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/eventbus"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/lock"

	gaeaClient "github.com/qbox/pay-sdk/gaea/client"
	gaeaOperations "github.com/qbox/pay-sdk/gaea/client/operations"
	gaeaModels "github.com/qbox/pay-sdk/gaea/models"
	"github.com/qbox/pay-sdk/middleware/logging"
	notificationV2Client "github.com/qbox/pay-sdk/notification-v2/client"
	notificationV2Operations "github.com/qbox/pay-sdk/notification-v2/client/operations"
	notificationV2Models "github.com/qbox/pay-sdk/notification-v2/models"
	qBPMClient "github.com/qbox/pay-sdk/qbpm/client"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/model"
)

// CouponService is service handler of Coupon
type CouponService struct {
	dao          *model.CouponDao
	locker       lock.Locker
	cacheExpires time.Duration

	couponBatchService   *CouponBatchService
	qbpmClient           *qBPMClient.Qbpm
	notificationV2Client *notificationV2Client.NotificationV2
	gaeaClient           *gaeaClient.Gaea
	eventbus             eventbus.EventBus
}

// GetEventbus get event bus
func (s *CouponService) GetEventbus() eventbus.EventBus {
	return s.eventbus
}

// NewCouponService is constructor of CouponService
func NewCouponService(
	dao *model.CouponDao,
	couponLocker lock.Locker,
	couponBatchService *CouponBatchService,
	qbpmClient *qBPMClient.Qbpm,
	notificationV2Client *notificationV2Client.NotificationV2,
	gaeaClient *gaeaClient.Gaea,
	expires time.Duration,
	eventbus eventbus.EventBus,
) (*CouponService, error) {
	return &CouponService{
		dao:                  dao,
		locker:               couponLocker,
		cacheExpires:         expires,
		couponBatchService:   couponBatchService,
		qbpmClient:           qbpmClient,
		notificationV2Client: notificationV2Client,
		gaeaClient:           gaeaClient,
		eventbus:             eventbus,
	}, nil
}

// BindCouponFromBatchParams 按批次绑券参数
type BindCouponFromBatchParams struct {
	// Epoch 绑券动作发生在何时
	//
	// 不传则为当前时间
	Epoch time.Time

	// UID UID
	UID uint64
	// BatchID 批次 ID
	BatchID uint64
	// IsOverrideEffectTime 是否覆盖生效时间
	IsOverrideEffectTime bool
	// OverrideEffectTime 覆盖生效时间，可选
	//
	// 若 `is_override_effect_time` 为真，绑定的券从该时间而非当前时间生效，否则该参数无意义
	OverrideEffectTime time.Time
}

// BindCouponResp 绑券响应
type BindCouponResp struct {
	// OK 是否成功
	OK bool
	// Coupon 成功绑定的券
	Coupon *model.Coupon
	// Err 错误码
	Err BindCouponError
}

// BindCouponError 绑券错误码
type BindCouponError int

const (
	// BindCouponErrorUnknown 未知绑券错误
	BindCouponErrorUnknown BindCouponError = 0
	// BindCouponErrorWrongBatchState 抵用券批次状态不是进行中
	BindCouponErrorWrongBatchState = BindCouponError(pb.BIND_COUPON_ERROR_WRONG_BATCH_STATE)
	// BindCouponErrorBatchExhausted 该批次券已经发完
	BindCouponErrorBatchExhausted = BindCouponError(pb.BIND_COUPON_ERROR_BATCH_EXHAUSTED)
	// BindCouponErrorUserLimitExceeded 该用户该批次最大激活次数已用完
	BindCouponErrorUserLimitExceeded = BindCouponError(pb.BIND_COUPON_ERROR_USER_LIMIT_EXCEEDED)
	// BindCouponErrorImmediateExpire 该券绑定即过期
	BindCouponErrorImmediateExpire = BindCouponError(pb.BIND_COUPON_ERROR_IMMEDIATE_EXPIRE)
	// BindCouponErrorNonexistentBatch 引用的抵用券批次不存在
	BindCouponErrorNonexistentBatch = BindCouponError(pb.BIND_COUPON_ERROR_NONEXISTENT_BATCH)
	// BindCouponErrorUnknownTimePeriodType 未知的生效时间类型
	BindCouponErrorUnknownTimePeriodType = BindCouponError(pb.BIND_COUPON_ERROR_UNKNOWN_TIME_PERIOD_TYPE)
)

func newBindCouponRespOk(coupon *model.Coupon) *BindCouponResp {
	return &BindCouponResp{
		OK:     true,
		Coupon: coupon,
		Err:    BindCouponErrorUnknown,
	}
}

func newBindCouponRespErr(err BindCouponError) *BindCouponResp {
	return &BindCouponResp{
		OK:     false,
		Coupon: nil,
		Err:    err,
	}
}

// GetCouponByCode 根据券码查询一张券
func (s *CouponService) GetCouponByCode(
	ctx context.Context,
	code string,
) (*model.Coupon, error) {
	obj, err := s.dao.Coupon.GetByCode(code, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return obj, nil
}

// ListAllCoupons 分页列出所有抵用券
func (s *CouponService) ListAllCoupons(
	ctx context.Context,
	offset int,
	limit int,
) (result []model.Coupon, count uint64, err error) {
	param := &model.ListCouponsByCondsParam{}

	return s.ListCouponsByConds(ctx, param, offset, limit)
}

// ListCouponsByUID 按 UID 分页列出抵用券
func (s *CouponService) ListCouponsByUID(
	ctx context.Context,
	uid uint64,
	offset int,
	limit int,
) (result []model.Coupon, count uint64, err error) {
	param := &model.ListCouponsByCondsParam{
		UID: uid,
	}

	return s.ListCouponsByConds(ctx, param, offset, limit)
}

// ListCouponsByBatchID 按批次 ID 分页列出抵用券
func (s *CouponService) ListCouponsByBatchID(
	ctx context.Context,
	batchID uint64,
	offset int,
	limit int,
) (result []model.Coupon, count uint64, err error) {
	param := &model.ListCouponsByCondsParam{
		BatchID: batchID,
	}

	return s.ListCouponsByConds(ctx, param, offset, limit)
}

// CouponAndBatch coupon & batch
type CouponAndBatch struct {
	Coupon *model.Coupon
	Batch  CouponBatchAndScope
}

// AssociateCouponsWithBatches 取出给定抵用券列表的所有关联批次
func (s *CouponService) AssociateCouponsWithBatches(
	ctx context.Context,
	coupons []model.Coupon,
) ([]*CouponAndBatch, error) {
	result := make([]*CouponAndBatch, len(coupons))
	if len(coupons) <= 0 {
		return result, nil
	}

	batchIDSet := make(map[uint64]struct{})
	batchIDs := make([]uint64, 0)
	for _, c := range coupons {
		if _, ok := batchIDSet[c.BatchID]; ok {
			continue
		}
		batchIDSet[c.BatchID] = struct{}{}
		batchIDs = append(batchIDs, c.BatchID)
	}

	// []batchID -> []CouponBatchAndScope
	batchesList, err := s.couponBatchService.ListByConds(
		ctx,
		&model.ListCouponBatchesByCondsParam{
			IDs: batchIDs,
		},
		0,  // offset
		-1, // limit
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// []CouponBatchAndScope -> map[batchID]CouponBatchAndScope
	batchMap := make(map[uint64]CouponBatchAndScope, len(batchesList.CouponBatches))
	for _, couponBatch := range batchesList.CouponBatches {
		batchMap[couponBatch.CouponBatch.ID] = couponBatch
	}

	// 组装结果
	for i := range coupons {
		result[i] = &CouponAndBatch{
			Coupon: &coupons[i],
			Batch:  batchMap[coupons[i].BatchID],
		}
	}
	return result, nil
}

// ListCouponsByConds 按条件列出抵用券
func (s *CouponService) ListCouponsByConds(
	ctx context.Context,
	param *model.ListCouponsByCondsParam,
	offset int,
	limit int,
) (result []model.Coupon, count uint64, err error) {
	result, count, err = s.dao.Coupon.ListByCondsWithCount(param, offset, limit)
	if err != nil {
		return nil, 0, errors.Trace(err)
	}

	return result, count, nil
}

// ListCouponsByDetailConds 按细节查询条件列出抵用券
func (s *CouponService) ListCouponsByDetailConds(
	ctx context.Context,
	param *model.ListCouponDetailsByCondsParam,
	offset int,
	limit int,
) (result []model.Coupon, count uint64, err error) {
	result, count, err = s.dao.Coupon.ListByDetailConds(param, offset, limit, s.cacheExpires)
	if err != nil {
		return nil, 0, errors.Trace(err)
	}

	return result, count, nil
}

// BindCouponFromBatch 给用户绑一张来自给定批次的券
func (s *CouponService) BindCouponFromBatch(
	ctx context.Context,
	req *BindCouponFromBatchParams,
) (*BindCouponResp, error) {
	epoch := req.Epoch
	if epoch.IsZero() {
		epoch = time.Now()
	}

	// 以批次维度，需要串行化
	serializeKeyBatch := genSerializationKeyByBatchID(req.BatchID)
	s.locker.Lock(serializeKeyBatch)
	defer s.locker.Unlock(serializeKeyBatch)

	// 以用户维度，也要串行化
	s.locker.LockUser(req.UID)
	defer s.locker.UnlockUser(req.UID)

	couponBatch, err := s.dao.CouponBatch.GetByID(req.BatchID, s.cacheExpires)
	if err != nil {
		if errors.Cause(err) == dao.ErrRecordNotFound {
			return newBindCouponRespErr(BindCouponErrorNonexistentBatch), nil
		}
		return nil, errors.Trace(err)
	}

	// 当前时间必须在发放时间段内
	{
		startTime := couponBatch.StartTime.Time(ctx)
		endTime := couponBatch.EndTime.Time(ctx)

		// 左闭右开
		// 在时间段内: startTime <= epoch && epoch < endTime
		// 不在时间段内: epoch < startTime || endTime <= epoch
		// 仅使用严格不等号: epoch < startTime || !(endTime > epoch)
		// 且非指定发放
		if couponBatch.BindMethod != model.CouponBindMethodPrebound &&
			(epoch.Before(startTime) || !(endTime.After(epoch))) {
			// NOTE: 不在发放时段的话，应该状态也不对
			return newBindCouponRespErr(BindCouponErrorWrongBatchState), nil
		}
	}

	// 批次状态必须是发放中
	if couponBatch.State != model.CouponBatchStateInProgress {
		return newBindCouponRespErr(BindCouponErrorWrongBatchState), nil
	}

	// 批次中必须还剩余发放次数
	if couponBatch.NumActivated >= couponBatch.BatchSize {
		return newBindCouponRespErr(BindCouponErrorBatchExhausted), nil
	}

	// 这个用户在这个批次的已激活次数不能超过批次限制
	if couponBatch.MaxActivationTimesPerUser > 0 {
		couponCountInBatch, err := s.dao.Coupon.CountByBatchIDAndUID(req.BatchID, req.UID)
		if err != nil {
			return nil, errors.Trace(err)
		}

		if couponCountInBatch >= couponBatch.MaxActivationTimesPerUser {
			return newBindCouponRespErr(BindCouponErrorUserLimitExceeded), nil
		}
	}

	// NOTE: couponBatch.UserScope 暂未使用

	// 生效/结束时间逻辑
	effectTime, deadTime, errcode := makeCouponEffectDeadTime(
		ctx,
		epoch,
		couponBatch,
		req.IsOverrideEffectTime,
		req.OverrideEffectTime,
	)
	if errcode != 0 {
		return newBindCouponRespErr(errcode), nil
	}

	// BO-9675 如果绑定即失效，就不让绑
	// (deadTime <= epoch) -> !(deadTime > epoch)
	if !deadTime.Time(ctx).After(epoch) {
		return newBindCouponRespErr(BindCouponErrorImmediateExpire), nil
	}

	if effectTime.Time(ctx).Before(epoch) {
		effectTime = base.NewHNS(epoch)
	}

	// 生成一个券码
	couponCode := genCouponCode()

	// 制作抵用券
	couponObj := &model.Coupon{
		BatchID:    req.BatchID,
		UID:        req.UID,
		Code:       couponCode,
		BindMethod: model.CouponBindMethodAPI, // TODO: 目前就这一种
		State:      model.CouponStatePending,
		Money:      couponBatch.CouponMoney,
		Balance:    couponBatch.CouponMoney,
		BoundAt:    base.NewHNS(epoch),
		EffectTime: effectTime,
		DeadTime:   deadTime,
	}

	// TODO: 事务
	var savedObj *model.Coupon
	{
		// 入库
		err = s.dao.Coupon.Save(couponObj, s.cacheExpires)
		if err != nil {
			return nil, err
		}
		savedObj = couponObj

		// 更新批次已发放数量
		couponBatch.NumActivated++
		err = s.dao.CouponBatch.Update(couponBatch, s.cacheExpires)
		if err != nil {
			return nil, err
		}

		// 以 epoch 为当前时间，推动一次状态机
		newState, err := s.dao.Coupon.ProgressState(ctx, savedObj, epoch)
		if err != nil {
			return nil, err
		}

		savedObj.State = newState
	}

	return newBindCouponRespOk(savedObj), nil
}

// makeCouponEffectDeadTime 计算券的生效、过期时间
func makeCouponEffectDeadTime(
	ctx context.Context,
	epoch time.Time,
	couponBatch *model.CouponBatch,
	isOverrideEffectTime bool,
	overrideEffectTime time.Time,
) (effectTime base.HNS, deadTime base.HNS, bcerr BindCouponError) {
	var et time.Time
	{
		if isOverrideEffectTime {
			et = overrideEffectTime
		} else {
			switch couponBatch.TimePeriodType {
			case model.CouponTimePeriodTypeConstDuration:
				et = epoch
			case model.CouponTimePeriodTypeAbsolute:
				et = couponBatch.CouponEffectTime.Time(ctx)
			default:
				return 0, 0, BindCouponErrorUnknownTimePeriodType
			}
		}
	}

	var dt time.Time
	{
		switch couponBatch.TimePeriodType {
		case model.CouponTimePeriodTypeConstDuration:
			dt = et.AddDate(0, 0, int(couponBatch.EffectDays))
		case model.CouponTimePeriodTypeAbsolute:
			dt = couponBatch.CouponDeadTime.Time(ctx)
		default:
			return 0, 0, BindCouponErrorUnknownTimePeriodType
		}
	}

	return base.NewHNS(et), base.NewHNS(dt), BindCouponErrorUnknown
}

// SyncAllCouponStates 批量更新所有 Coupon 的状态字段
func (s *CouponService) SyncAllCouponStates(
	ctx context.Context,
) error {
	return s.SyncAllCouponStatesWithEpoch(ctx, time.Now())
}

// SyncAllCouponStatesWithEpoch 以给定的时间为当前时间，批量更新所有 Coupon 的状态字段
func (s *CouponService) SyncAllCouponStatesWithEpoch(
	ctx context.Context,
	epoch time.Time,
) error {
	logger := logging.GetLogger(ctx)

	limit := uint64(1000)
	idMarker := uint64(0)
	for {
		availableCoupons, err := s.dao.Coupon.ListAvailable(idMarker, limit)
		if err != nil {
			return errors.Trace(err)
		}
		if len(availableCoupons) == 0 {
			return nil
		}

		wg := sync.WaitGroup{}
		wg.Add(len(availableCoupons))
		for i := range availableCoupons {
			idMarker = availableCoupons[i].ID
			go func(index int) {
				defer wg.Done()
				_, err := s.dao.Coupon.ProgressState(ctx, &availableCoupons[index], epoch)
				if err != nil {
					logger.WithError(err).WithFields(logrus.Fields{
						"coupon": availableCoupons[index],
					}).Error("state update for coupon failed")
					// 要接着处理别的，不失败返回
				}
			}(i)
		}
		wg.Wait()
	}
}

// ListOrderedCouponsByDetailConds 按细节查询条件列出抵用券，并按照 balance、dead_time、created_at 排序
func (s *CouponService) ListOrderedCouponsByDetailConds(
	ctx context.Context,
	param *model.ListCouponDetailsByCondsParam,
	offset, limit int,
) (result []model.Coupon, count uint64, err error) {
	result, count, err = s.dao.Coupon.ListByDetailCondsWithOrder(param, offset, limit, s.cacheExpires)
	if err != nil {
		return nil, 0, errors.Trace(err)
	}

	return result, count, nil
}

// CancelCouponResp 撤销券响应
type CancelCouponResp struct {
	// OK 是否成功
	OK bool
	// Err 错误码
	Err CancelCouponError
}

// CancelCouponError 撤销券错误码
type CancelCouponError int

const (
	// CancelCouponErrorUnknown 未知撤销券错误
	CancelCouponErrorUnknown CancelCouponError = 0
	// CancelCouponErrorAlreadyCanceled 已经处于撤销状态
	CancelCouponErrorAlreadyCanceled CancelCouponError = 1
	// CancelCouponErrorExpired 券已经过期
	CancelCouponErrorExpired CancelCouponError = 2
)

func newCancelCouponRespOk() *CancelCouponResp {
	return &CancelCouponResp{
		OK:  true,
		Err: CancelCouponErrorUnknown,
	}
}

func newCancelCouponRespErr(err CancelCouponError) *CancelCouponResp {
	return &CancelCouponResp{
		OK:  false,
		Err: err,
	}
}

// CancelCouponByCode 按券码撤销一张券
func (s *CouponService) CancelCouponByCode(
	ctx context.Context,
	code string,
) (*CancelCouponResp, error) {
	coupon, err := s.dao.Coupon.GetByCode(code, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	switch coupon.State {
	case model.CouponStateCanceled:
		// 空操作
		return newCancelCouponRespErr(CancelCouponErrorAlreadyCanceled), nil
	case model.CouponStateExpired:
		return newCancelCouponRespErr(CancelCouponErrorExpired), nil
	}

	coupon.State = model.CouponStateCanceled
	err = s.dao.Coupon.Update(coupon)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return newCancelCouponRespOk(), nil
}

type CreateAndBindCoupon struct {
	UIDs                    []uint64
	CreateCouponBatchParams *CreateCouponBatchParam
}

// DoTransaction do a transaction
func (s *CouponService) DoTransaction(fn func(couponService *CouponService) error) error {
	return s.dao.DoTransaction(func(dao *model.CouponDao) error {
		newCouponBatchSrv, err := NewCouponBatchService(
			dao.CouponBatch,
			s.couponBatchService.tradeClient,
			s.couponBatchService.themisClient,
			s.cacheExpires,
		)
		if err != nil {
			return err
		}
		newCouponSrv, err := NewCouponService(
			dao,
			s.locker,
			newCouponBatchSrv,
			s.qbpmClient,
			s.notificationV2Client,
			s.gaeaClient,
			s.cacheExpires,
			s.eventbus,
		)
		if err != nil {
			return err
		}

		return fn(newCouponSrv)
	})
}

func (s *CouponService) CreateAndBindCoupon(
	ctx context.Context,
	params *CreateAndBindCoupon,
) ([]*BindCouponResp, error) {
	l := logging.GetLogger(ctx)

	bindCouponResps := make([]*BindCouponResp, len(params.UIDs))
	s.DoTransaction(func(couponService *CouponService) error {
		// 创建 couponBatch
		createCouponBatchResp, err := couponService.couponBatchService.CreateCouponBatch(ctx, params.CreateCouponBatchParams)
		if err != nil {
			return errors.Trace(err)
		}

		for i, uid := range params.UIDs {
			bindCouponReq := BindCouponFromBatchParams{
				Epoch:                time.Time{},
				UID:                  uid,
				BatchID:              createCouponBatchResp.CouponBatch.ID,
				IsOverrideEffectTime: false,
				OverrideEffectTime:   time.Time{},
			}
			// 绑定 coupon
			bindCouponResp, err1 := couponService.BindCouponFromBatch(ctx, &bindCouponReq)
			if err1 != nil {
				return errors.Trace(err1)
			}
			bindCouponResps[i] = bindCouponResp
		}

		// 更新批次状态为完成
		_, err = couponService.dao.CouponBatch.ProgressState(ctx, createCouponBatchResp.CouponBatch, time.Now(), s.cacheExpires)
		if err != nil {
			l.WithError(err).WithFields(logrus.Fields{
				"couponBatch": createCouponBatchResp.CouponBatch,
			}).Error("state update for coupon batch failed")

			return errors.Trace(err)
		}
		return nil
	})

	return bindCouponResps, nil
}

// ListBoundUIDsByBatchID 按批次 ID 列出抵用券绑定的所有 uid
func (s *CouponService) ListBoundUIDsByBatchID(
	ctx context.Context,
	batchID uint64,
) (uids []uint64, err error) {
	return s.dao.Coupon.ListBoundUIDsByBatchID(batchID)
}

// CouponUsageRecord 券的抵扣记录
//
// XXX: 需要在这里重写一遍的原因是 model 层存的 coupon ID，而对外 API 是券码，以及 model 层目前没存 UID
type CouponUsageRecord struct {
	// ID 主键
	ID uint64
	// CouponCode 关联的券码
	CouponCode string
	// UID 用券的 UID
	UID uint64
	// 批次 ID
	BatchID uint64
	// OrderHash 关联的 order hash
	OrderHash string
	// PoID 关联的 po ID
	PoID uint64
	// Money 这张券在这个 po 上抵扣的金额
	Money base.Money

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// ListCouponUsageRecordsByCouponIDs 查询一组 couponIDs 的抵用券抵扣记录
func (s *CouponService) ListCouponUsageRecordsByCouponIDs(
	ctx context.Context,
	couponIDs []uint64,
) ([]CouponUsageRecord, error) {
	logger := logging.GetLogger(ctx)

	resp, err := s.dao.CouponUsageRecord.ListByCouponIDs(couponIDs)
	if err != nil {
		logger.WithError(err).WithField("couponIDs", couponIDs).Error("list coupon usage record failed")
		return nil, errors.Trace(err)
	}

	// 转换为对外 API 级别的返回值
	result := transformUsageRecords(resp)

	return result, nil
}

// ListCouponUsageRecordsByOrderHashes 查询一组 order_hash 的抵用券抵扣记录
func (s *CouponService) ListCouponUsageRecordsByOrderHashes(
	ctx context.Context,
	orderHashes []string,
) ([]CouponUsageRecord, error) {
	logger := logging.GetLogger(ctx)

	resp, err := s.dao.CouponUsageRecord.ListByOrderHashes(orderHashes)
	if err != nil {
		logger.WithError(err).WithField("orderHashes", orderHashes).Error("list coupon usage record failed")
		return nil, errors.Trace(err)
	}
	// 转换为对外 API 级别的返回值
	result := transformUsageRecords(resp)

	return result, nil
}

func transformUsageRecords(resp []model.CouponUsageRecord) []CouponUsageRecord {
	if len(resp) == 0 {
		return []CouponUsageRecord{}
	}
	result := make([]CouponUsageRecord, len(resp))
	for i, record := range resp {
		result[i] = CouponUsageRecord{
			ID:         record.ID,
			CouponCode: record.CouponCode,
			UID:        record.UID,
			BatchID:    record.BatchID,
			OrderHash:  record.OrderHash,
			PoID:       record.PoID,
			Money:      record.Money,
			CreatedAt:  record.CreatedAt,
			UpdatedAt:  record.UpdatedAt,
		}
	}
	return result
}

const couponExpirationChannel = 25
const couponExpirationTmplName = "coupon_expiration"

type UIDNotifyCouponExpirationDataMap map[uint64]NotifyCouponExpirationData

type NotifyCouponExpirationUnit struct {
	Name            string     `json:"name"`
	Balance         base.Money `json:"balance"`
	CouponScopeDesc string     `json:"coupon_scope_desc"`
	ID              uint64
}

type NotifyCouponExpirationData struct {
	Email            string                       `json:"email"`
	Count            uint64                       `json:"count"`
	CouponBalanceSum base.Money                   `json:"coupon_balance_sum"`
	Coupons          []NotifyCouponExpirationUnit `json:"coupons"`
}

// NotifyCouponExpiration 提醒抵用券过期
func (s *CouponService) NotifyCouponExpiration(
	ctx context.Context,
) (total, failureCount uint64, err error) {
	l := logging.GetLogger(ctx)
	loc := tz.MustLocationFromCtx(ctx)

	now := time.Now().In(loc)
	// 5 天后的日期（需求中的要求）
	expectedExpirationDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).AddDate(0, 0, 5)
	// 查询所有用户所有 5 天后将会到期的抵用券
	coupons, err := s.dao.Coupon.ListCouponAboutToExpire(expectedExpirationDate)
	if err != nil {
		l.Error("NotifyCouponExpiration invoke ListCouponAboutToExpire error", err)
		return 0, 0, err
	}
	l.Infof("NotifyCouponExpiration: %+v\n", coupons)
	// 并关联相应的 batch 信息
	couponAndBatchArr, err := s.AssociateCouponsWithBatches(ctx, coupons)
	if err != nil {
		l.Error("NotifyCouponExpiration invoke AssociateCouponsWithBatches error", err)
		return 0, 0, err
	}

	// 构造通知内容，调用消息通知服务来通知用户
	uidNotifyCouponExpirationDataMap, err := s.buildNotifyCouponExpirationData(ctx, couponAndBatchArr)
	if err != nil {
		l.Error("NotifyCouponExpiration invoke buildNotifyCouponExpirationData error", err)
		return 0, 0, err
	}

	l.Infof("uidNotifyCouponExpirationDataMap %+v\n", uidNotifyCouponExpirationDataMap)

	failedCoupons := make([]NotifyCouponExpirationUnit, 0)
	// 每个用户发一遍通知
	for uid, data := range uidNotifyCouponExpirationDataMap {
		msg := &notificationV2Models.SendMessageReq{
			ChannelID:    strconv.Itoa(couponExpirationChannel),
			UID:          int64(uid),
			TemplateID:   couponExpirationTmplName,
			TemplateData: data,
		}
		params := notificationV2Operations.NewSendMessageParamsWithContext(ctx).WithBody(msg)

		_, err = s.notificationV2Client.Operations.SendMessage(params)
		if err != nil {
			l.Warnf("NotifyCouponExpiration invoke send error,%+v", errors.Trace(err))
			failedCoupons = append(failedCoupons, data.Coupons...)
			failureCount++
		}
		total += 1
	}
	if failureCount > 0 {
		l.Errorln("NotifyCouponExpiration failedCoupons:", failedCoupons)
	}
	return total, failureCount, nil
}

// 按照 uid 聚合该用户所有需要通知的即将过期的 coupon
func (s *CouponService) buildNotifyCouponExpirationData(ctx context.Context, couponAndBatchArr []*CouponAndBatch) (UIDNotifyCouponExpirationDataMap, error) {
	l := logging.GetLogger(ctx)

	uidNotifyCouponDataMap := make(UIDNotifyCouponExpirationDataMap)
	for _, couponAndBatch := range couponAndBatchArr {
		notifyCoupon := NotifyCouponExpirationUnit{
			Name:            couponAndBatch.Batch.CouponBatch.Name,
			Balance:         couponAndBatch.Coupon.Balance,
			CouponScopeDesc: couponAndBatch.Batch.CouponBatch.CouponScopeDesc,
			ID:              couponAndBatch.Coupon.ID,
		}
		// 之前没有这个 uid 的条目那么新建
		if _, ok := uidNotifyCouponDataMap[couponAndBatch.Coupon.UID]; !ok {
			userInfo, err := s.getUserInfo(ctx, couponAndBatch.Coupon.UID)
			if err != nil {
				l.Errorln("buildNotifyCouponExpirationData getUserInfo error:", err)
				return nil, err
			}

			uidNotifyCouponDataMap[couponAndBatch.Coupon.UID] = NotifyCouponExpirationData{
				Email:            userInfo.Email,
				Count:            1,
				CouponBalanceSum: notifyCoupon.Balance,
				Coupons:          []NotifyCouponExpirationUnit{notifyCoupon},
			}
		} else {
			// 有的话，那么追加
			data := uidNotifyCouponDataMap[couponAndBatch.Coupon.UID]
			data.CouponBalanceSum += notifyCoupon.Balance
			data.Count += 1
			data.Coupons = append(data.Coupons, notifyCoupon)
			uidNotifyCouponDataMap[couponAndBatch.Coupon.UID] = data
		}
	}
	return uidNotifyCouponDataMap, nil
}

func (s *CouponService) getUserInfo(ctx context.Context, uid uint64) (*gaeaModels.UserInfo, error) {
	l := logging.GetLogger(ctx)

	params := gaeaOperations.NewGetDeveloperParams().WithUID(fmt.Sprintf("%d", uid))
	ret, err := s.gaeaClient.Operations.GetDeveloper(params)
	if err != nil {
		return nil, err
	}
	payLoad := ret.GetPayload()
	if payLoad.Code != http.StatusOK {
		l.Errorf("getUserInfo error code:%d, message:%s", payLoad.Code, payLoad.Message)
		return nil, errors.New(payLoad.Message)
	}

	return ret.GetPayload().Data, nil
}

// CancelCouponBatchByID cancels a CouponBatch
func (s *CouponService) CancelCouponBatchByID(
	ctx context.Context,
	batchID uint64,
) error {

	err := s.DoTransaction(func(couponService *CouponService) error {
		// 更新 couponBatch 状态为已过期
		err := couponService.dao.CouponBatch.Update(&model.CouponBatch{
			ID:    batchID,
			State: model.CouponBatchStateInvalid,
		})

		if err != nil {
			return errors.Trace(err)
		}

		// 更新已绑定该批次的用户券的状态为冻结
		err = couponService.dao.Coupon.UpdateCouponStateByBatchID(batchID, model.CouponStateSuspended)
		if err != nil {
			return errors.Trace(err)
		}

		return nil
	})

	return err
}

// CountTradeCoupon 获取用户可用优惠券数量
func (s *CouponService) CountTradeCoupon(ctx context.Context, uid uint64) (count uint64, err error) {
	return s.dao.Coupon.CountAvailableCoupon(uid)
}

// CountCouponUsageByBatchID 获取 batchID 所对应券的已使用数量
func (s *CouponService) CountCouponUsageByBatchID(ctx context.Context, batchID uint64) (count uint64, err error) {
	return s.dao.CouponUsageRecord.CountUsageByBatchID(batchID)
}
