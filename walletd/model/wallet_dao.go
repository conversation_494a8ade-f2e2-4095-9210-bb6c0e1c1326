package model

import (
	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/dao"
)

// WalletDao is data access object for wallet service
type WalletDao struct {
	base              *dao.BaseDao
	Scope             *ScopeDao
	CouponCash        *CouponCashDao
	CouponDiscount    *CouponDiscountDao
	CouponRebate      *CouponRebateDao
	CouponUserMap     *CouponUserMapDao
	ScopeProductMap   *ScopeProductMapDao
	ScopeItemGroupMap *ScopeItemGroupMapDao
	ScopeItemMap      *ScopeItemMapDao
}

// NewWalletDao is constructor of WalletDao
func NewWalletDao(base *dao.BaseDao) *WalletDao {
	return &WalletDao{
		base:              base,
		Scope:             NewScopeDao(base),
		CouponCash:        NewCouponCashDao(base),
		CouponDiscount:    NewCouponDiscountDao(base),
		CouponRebate:      NewCouponRebateDao(base),
		CouponUserMap:     NewCouponUserMapDao(base),
		ScopeProductMap:   NewScopeProductMapDao(base),
		ScopeItemGroupMap: NewScopeItemGroupMapDao(base),
		ScopeItemMap:      NewScopeItemMapDao(base),
	}
}

// DoTransaction do a transaction
func (d *WalletDao) DoTransaction(fn func(*WalletDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		walletDao := NewWalletDao(base)
		walletDao.base = d.base
		return fn(walletDao)
	})
}

// RegisterMigrate migrate all models
func RegisterMigrate(db *gorm.DB) {
	db.AutoMigrate(&Scope{})
	db.AutoMigrate(&ScopeProductMap{})
	db.AutoMigrate(&ScopeItemGroupMap{})
	db.AutoMigrate(&ScopeItemMap{})
	db.AutoMigrate(&CouponCash{})
	db.AutoMigrate(&CouponDiscount{})
	db.AutoMigrate(&CouponRebate{})
	db.AutoMigrate(&CouponUserMap{})
	db.AutoMigrate(&BalanceInsufficiencyPrediction{})
	db.AutoMigrate(&UserBankVirtualAccount{})
}
