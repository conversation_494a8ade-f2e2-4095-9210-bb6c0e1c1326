package service

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"time"

	planPb "github.com/qbox/pay-sdk/plan"

	"golang.org/x/exp/maps"

	"github.com/jinzhu/gorm"
	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/bo-base/v4/uuid"
	"github.com/qbox/pay-sdk/dict"
	gaeaOp "github.com/qbox/pay-sdk/gaea/client/operations"
	gaeamodels "github.com/qbox/pay-sdk/gaea/models"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/qbox/pay-sdk/respack"
	priceOp "github.com/qbox/pay-sdk/v3/price/client/operations"
	pricemodels "github.com/qbox/pay-sdk/v3/price/models"
	walletOp "github.com/qbox/pay-sdk/v3/walletEx/client/operations"
	walletmodels "github.com/qbox/pay-sdk/v3/walletEx/models"
	"google.golang.org/protobuf/types/known/timestamppb"

	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/models/enums"
)

// CheckEffectDuplicate 检查是否存重复生效时间的 plan
func (s *PlanService) CheckEffectDuplicate(
	ctx context.Context,
	plan *models.Plan,
) (*models.Plan, error) {
	if plan.Draft || plan.EffectTime == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	exists, err := s.dao.PlanDao.GetExistsTimingPlan(
		ctx,
		plan.Level,
		plan.EffectTime,
	)
	if err != nil {
		return nil, err
	}
	return exists, nil
}

// SavePlan 保存 plan 配置
func (s *PlanService) SavePlan(
	ctx context.Context,
	plan *models.Plan,
) (*models.Plan, error) {
	logger := logging.GetLogger(ctx)

	previousPlan, err := s.dao.PlanDao.GetLatestPlanByLevel(ctx, plan.Level)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.WithField("plan", plan).
			WithError(err).Error("get latest plan failed")
		return nil, err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 当前 code 不存在, 即认为是新建
		plan.Version = 1
	} else {
		plan.Version = previousPlan.Version + 1
	}
	// 检查 packs、rights 参数传递是否合法
	err = checkPacksRights(plan)
	if err != nil {
		logger.WithField("plan", plan).
			WithError(err).Error("check plan packs failed")
		return nil, err
	}

	plan = calcPlanStatus(ctx, plan)

	// 检查是否存在相同生效时间的 plan, 存在则之前的取消定时
	if plan.TimerFlag {
		exists, err1 := s.dao.PlanDao.GetExistsTimingPlan(
			ctx,
			plan.Level,
			plan.EffectTime,
		)
		if err1 != nil && !errors.Is(err1, gorm.ErrRecordNotFound) {
			logger.WithField("plan", plan).
				WithError(err1).Error("get exists timing plan failed")
			return nil, err1
		}
		if exists != nil {
			err1 = s.dao.PlanDao.ResetTimingFlag(ctx, exists.ID)
			if err1 != nil {
				logger.WithField("plan", plan).
					WithError(err1).Error("reset timing flag failed")
				return nil, err1
			}
		}
	}

	plan = genCodeForPackAndRights(plan)

	// 填充区域名称
	err = s.fillZoneName(ctx, plan)
	if err != nil {
		logger.WithField("plan", plan).
			WithError(err).Error("full zone name failed")
		return nil, err
	}
	// 填充 unit_str
	err = s.fillUnitStr(ctx, plan)
	if err != nil {
		logger.WithField("plan", plan).
			WithError(err).Error("full unit str failed")
		return nil, err
	}
	// 生成 respack  权益
	err = s.genRespackRights(ctx, plan)
	if err != nil {
		logger.WithField("plan", plan).
			WithError(err).Error("gen package rights failed")
		return nil, err
	}
	// 当前版本是立即上线时，则需要将其他版本立即下线
	if plan.Status == enums.PlanStatusOnline {
		err = s.dao.PlanDao.SetOffline(
			ctx,
			plan.Level,
		)
		if err != nil {
			logger.WithField("plan", plan).
				WithError(err).Error("update plan status failed")
			return nil, err
		}
	}
	err = s.dao.PlanDao.Insert(plan)
	if err != nil {
		logger.WithField("plan", plan).
			WithError(err).Error("insert plan failed")
		return nil, err
	}

	return plan, nil
}

func (s *PlanService) fillZoneName(
	ctx context.Context,
	plan *models.Plan,
) error {
	logger := logging.GetLogger(ctx)
	if plan.Level == enums.LevelEnterprise {
		return nil
	}
	if plan.Packs == nil || len(plan.Packs.Packs) <= 0 {
		return nil
	}
	// 普通计费项直接读取 zone 对应的 名称
	// 融合计费项则读取融合计费项的 zone_str
	zoneCodes := make([]uint32, 0)
	comboItemCodes := make([]string, 0)
	for _, pack := range plan.Packs.Packs {
		if len(pack.Rights) <= 0 {
			continue
		}
		for _, rights := range pack.Rights {
			if rights.IsCombo {
				comboItemCodes = append(comboItemCodes, rights.ItemCode)
			} else {
				zoneCodes = append(zoneCodes, rights.Zone)
			}
		}
	}

	type nameEntry struct {
		comboItem string
		zone      uint32
		zoneName  string
	}
	zoneNames, err := resultgroup.ParallelMap(
		zoneCodes,
		func(zone uint32) (nameEntry, error) {
			z, err := s.dict.GetZoneByCode(
				ctx,
				&dict.ZoneCodeParam{
					Code: int64(zone),
				},
			)
			if err != nil {
				return nameEntry{}, err
			}
			return nameEntry{
				zone:     zone,
				zoneName: z.GetName(),
			}, nil
		},
	)
	if err != nil {
		logger.WithField("plan", plan).
			WithError(err).Error("fill normal item zone name failed")
		return err
	}
	comboZoneNames, err := resultgroup.ParallelMap(
		comboItemCodes,
		func(code string) (nameEntry, error) {
			c, e := s.respack.GetComboItemDetailByCode(
				ctx,
				&respack.GetComboItemDetailByCodeParam{
					Code: code,
				},
			)
			if e != nil {
				return nameEntry{}, e
			}
			return nameEntry{
				comboItem: code,
				zoneName:  c.GetZoneDesc(),
			}, nil
		},
	)
	if err != nil {
		logger.WithField("plan", plan).
			WithError(err).Error("fill combo item zone name failed")
		return err
	}
	// 转为 map，方便取用
	zoneNameMap := make(map[uint32]string)
	for _, entry := range zoneNames {
		zoneNameMap[entry.zone] = entry.zoneName
	}
	comboItemZoneMap := make(map[string]string)
	for _, entry := range comboZoneNames {
		comboItemZoneMap[entry.comboItem] = entry.zoneName
	}

	// 填充原 rights 中 zoneName 字段
	for _, pack := range plan.Packs.Packs {
		for j, rights := range pack.Rights {
			if rights.IsCombo {
				pack.Rights[j].ZoneName = comboItemZoneMap[rights.ItemCode]
			} else {
				pack.Rights[j].ZoneName = zoneNameMap[rights.Zone]
			}
		}
	}
	return nil
}

func (s *PlanService) fillUnitStr(
	ctx context.Context,
	plan *models.Plan,
) error {
	logger := logging.GetLogger(ctx)
	if plan.Level == enums.LevelEnterprise {
		return nil
	}
	if plan.Packs == nil || len(plan.Packs.Packs) <= 0 {
		return nil
	}

	type measureEntry struct {
		itemCode string
		quota    uint64
		unitStr  string
	}
	// 收集要转换单位的参数
	translateItemQuotaMap := make(map[string]measureEntry)
	for _, pack := range plan.Packs.Packs {
		if len(pack.Rights) <= 0 {
			continue
		}
		for _, rights := range pack.Rights {
			translateItemQuotaMap[fmt.Sprintf("%s:%d", rights.ItemCode, rights.Quota)] = measureEntry{
				itemCode: rights.ItemCode,
				quota:    rights.Quota,
			}
		}
	}
	if len(translateItemQuotaMap) <= 0 {
		return nil
	}
	// 请求 gaea translateMeasure 接口
	measureRes, err := resultgroup.ParallelMap(
		maps.Values(translateItemQuotaMap),
		func(param measureEntry) (measureEntry, error) {
			res, err := s.gaea.Operations.TranslateMeasure(&gaeaOp.TranslateMeasureParams{
				Entries: &gaeamodels.TranslateMeasureReq{
					Entries: []*gaeamodels.TranslateMeasureInputEntry{
						{
							DateType: "",
							RawVal:   int64(param.quota),
						},
					},
				},
				ID:      param.itemCode,
				Context: ctx,
			})
			if err != nil || res.GetPayload().Code != http.StatusOK {
				if err == nil {
					err = errors.New(res.GetPayload().Message)
				}
				logger.WithFields(logrus.Fields{
					"itemCode": param.itemCode,
					"quota":    param.quota,
				}).WithError(err).Error("translate measure failed")
				return measureEntry{}, err
			}

			return measureEntry{
				itemCode: param.itemCode,
				quota:    param.quota,
				unitStr:  res.GetPayload().Data[0].Unit,
			}, nil
		},
	)
	if err != nil {
		logger.WithField("itemQuotaMap", translateItemQuotaMap).
			WithError(err).Error("translate measure failed")
		return err
	}
	translateResMap := make(map[string]string)
	for _, re := range measureRes {
		translateResMap[fmt.Sprintf("%s:%d", re.itemCode, re.quota)] = re.unitStr
	}
	// 填充原 rights 中 unitStr 字段
	for _, pack := range plan.Packs.Packs {
		for j, rights := range pack.Rights {
			pack.Rights[j].UnitStr = translateResMap[fmt.Sprintf("%s:%d", rights.ItemCode, rights.Quota)]
		}
	}
	return nil
}

func (s *PlanService) genRespackRights(
	ctx context.Context,
	plan *models.Plan,
) error {
	logger := logging.GetLogger(ctx)

	if plan.Level == enums.LevelEnterprise {
		// 企业版 无需 生成 respack
		return nil
	}

	freeRespackRights := make([]models.PackRights, 0, len(plan.Packs.Packs))
	for _, pack := range plan.Packs.Packs {
		for _, right := range pack.Rights {
			if right.Type == enums.RightsTypePriceDiscount {
				continue
			}
			freeRespackRights = append(freeRespackRights, right)
		}
	}
	res, errs := resultgroup.ParallelMapWithErrors(
		freeRespackRights,
		func(rights models.PackRights) (models.PackRights, error) {
			id := rights.GenFreeRespackID()
			getRes, err := s.respack.GetRespackByCode(
				ctx,
				&respack.CodeParam{
					Code: id,
				},
			)
			if err == nil && getRes.Code != "" {
				return models.PackRights{}, nil
			}
			if err != nil {
				// 认为还没有这个 respack, 则创建
				addParam := &respack.V3ModelResPack{
					Code:            id,
					Type:            "PLAN",
					Name:            "plan respack",
					Desc:            "plan free respack rights",
					ItemDataType:    "", // v3 的默认数据类型,为空即是 default
					Quota:           int64(rights.Quota),
					CarryOverPolicy: respack.CarryOverPolicyNoCarryOverToBindEnd,
					EffectTime: timestamppb.New(
						time.Date(2024, 1, 1, 0, 0, 0, 0, tz.MustLocationFromCtx(ctx)),
					),
					DeadTime: timestamppb.New(
						time.Date(2099, 1, 1, 0, 0, 0, 0, tz.MustLocationFromCtx(ctx)),
					),
				}
				if rights.IsCombo {
					addParam.ComboItemCode = rights.ItemCode
				} else {
					addParam.ItemCode = rights.ItemCode
				}
				_, err1 := s.respack.CreateRespack(ctx, addParam)
				if err1 != nil {
					logger.WithFields(logrus.Fields{
						"rights": rights,
					}).WithError(err1).Error("respack add failed")
					// 没有创建成功，则返回出去报错并记录日志
					return rights, err1
				}
			}
			return models.PackRights{}, nil
		},
	)
	for i, re := range res {
		if re.ItemCode == "" {
			continue
		}
		return errs[i]
	}
	return nil
}

func checkPacksRights(plan *models.Plan) error {
	if plan.Level == enums.LevelEnterprise {
		// 企业版 plan 权益相关信息为空
		return nil
	}
	packModel := plan.Packs
	if packModel == nil || len(packModel.Packs) <= 0 {
		return errors.New("empty packs")
	}
	for _, pack := range packModel.Packs {
		if len(pack.Rights) <= 0 {
			return errors.New("empty rights")
		}
		for _, rights := range pack.Rights {
			if rights.ItemCode == "" {
				return errors.New("empty item code")
			}
			if rights.IsCombo && rights.Zone != 0 {
				return errors.New("combo item zone must be zero")
			}
			if !rights.Type.Valid() {
				return errors.New("invalid rights type")
			}
			switch rights.Type {
			case enums.RightsTypeFreeRespack:
				if rights.Quota == 0 {
					return errors.New("quota is zero")
				}
			case enums.RightsTypePriceDiscount:
				if rights.IsCombo {
					return errors.New("combo item not supported discount")
				}
				if rights.Discount == 0 {
					return errors.New("discount is zero")
				}
			}
		}
	}
	return nil
}
func genCodeForPackAndRights(plan *models.Plan) *models.Plan {
	for i, pack := range plan.Packs.Packs {
		if pack.Code == "" {
			plan.Packs.Packs[i].Code = uuid.New()
		}
		for j, rights := range pack.Rights {
			if rights.Code == "" {
				pack.Rights[j].Code = uuid.New()
			}
		}
	}
	return plan
}

func calcPlanStatus(ctx context.Context, plan *models.Plan) *models.Plan {
	if plan.Draft {
		plan.Status = enums.PlanStatusDraft
		return plan
	}
	// 生效时间未传递或者生效时间<=now 则认为是 立即上线
	// NOTE: pb.timestamp.asTime.IsZero == false, cannot be used in here
	now := time.Now().In(tz.MustLocationFromCtx(ctx))
	if plan.EffectTime == 0 ||
		!time.Unix(plan.EffectTime, 0).After(now) {

		plan.Status = enums.PlanStatusOnline
		plan.EffectTime = now.Unix()
		plan.TimerFlag = false
	} else {
		plan.Status = enums.PlanStatusOffline
		plan.TimerFlag = true
	}
	return plan
}

func (s *PlanService) List(
	ctx context.Context,
	level string,
	offset int,
	limit int,
) ([]*models.Plan, error) {
	return s.dao.PlanDao.List(ctx, level, offset, limit)
}

// GetOnlineByLevel 获取某 level 在线的 plan
func (s *PlanService) GetOnlineByLevel(
	ctx context.Context,
	level enums.PlanLevel,
) (*models.Plan, error) {
	return s.dao.PlanDao.GetOnlineByLevel(ctx, level)
}

// ListOnlinePlans 获取在线的 plan 列表
func (s *PlanService) ListOnlinePlans(
	ctx context.Context,
) ([]*models.Plan, error) {
	return s.dao.PlanDao.ListOnlinePlans(ctx)
}

// GetByLevelAndVersion 获取特定版本的 plan
func (s *PlanService) GetByLevelAndVersion(
	ctx context.Context,
	level enums.PlanLevel,
	version uint32,
) (*models.Plan, error) {
	return s.dao.PlanDao.GetByLevelAndVersion(ctx, level, version)
}

// AttachPriceOnRights 补充价格明细
func (s *PlanService) AttachPriceOnRights(
	ctx context.Context,
	plan *models.Plan,
) error {
	logger := logging.GetLogger(ctx)
	if plan.Level == enums.LevelEnterprise {
		return nil
	}
	if plan.Packs == nil || len(plan.Packs.Packs) <= 0 {
		return nil
	}
	// 提取要查询价格的计费项
	itemCodes := make([]string, 0)
	for _, pack := range plan.Packs.Packs {
		for _, rights := range pack.Rights {
			if rights.Type == enums.RightsTypeFreeRespack {
				// 免费资源包权益不需要查询计费项价格
				continue
			}
			itemCodes = append(itemCodes, rights.ItemCode)
		}
	}
	prices, err := s.fetchItemPrice(ctx, itemCodes)
	if err != nil {
		logger.WithField("itemCode", itemCodes).
			WithError(err).
			Error("fetch item price failed")
		return err
	}
	// 赋值查到的价格
	for _, pack := range plan.Packs.Packs {
		for j, rights := range pack.Rights {
			if rights.Type == enums.RightsTypeFreeRespack {
				continue
			}
			if _, ok := prices[rights.ItemCode][int64(rights.Zone)]; !ok {
				err = errors.New("zone price missing")
				logger.WithField("rights", rights).
					WithError(err).
					Error("missing zone price")
				return err
			}
			pack.Rights[j].Price = prices[rights.ItemCode][int64(rights.Zone)]
		}
	}
	return nil
}

// fetchItemPrice 获取计费项各区域公开价(美元)
func (s *PlanService) fetchItemPrice(
	ctx context.Context,
	itemCodes []string,
) (map[string]map[int64]*models.Price, error) {
	logger := logging.GetLogger(ctx)
	itemCodes = base.UniqueStringSlice(itemCodes)

	prices, err := resultgroup.ParallelMap(
		itemCodes,
		func(itemCode string) (map[int64]*models.Price, error) {
			itemRes, err := s.price.Operations.ItemGet(&priceOp.ItemGetParams{
				ID:      itemCode,
				Context: ctx,
			})
			if err != nil || !itemRes.IsSuccess() {
				if err == nil {
					err = errors.New("query itemDef failed")
				}
				logger.WithField("itemCode", itemCode).
					WithError(err).
					Error("query itemDef failed")
				return nil, err
			}
			price := itemRes.GetPayload().Price
			if len(price) <= 0 {
				err = errors.New("none any price")
				logger.WithField("itemCode", itemCode).
					WithError(err).
					Error("none any price")
				return nil, err
			}

			usdPriceIDs := make([]string, 0)
			for _, p := range price {
				if p.USD == "" {
					err = errors.New("none usd price")
					logger.WithField("itemCode", itemCode).
						WithError(err).
						Error("none usd price")
					return nil, err
				}
				usdPriceIDs = append(usdPriceIDs, p.USD)
			}
			basePrices, e := resultgroup.ParallelMap(
				usdPriceIDs,
				func(priceID string) (*pricemodels.ModelBaseWithZone, error) {
					// 获取基础价格
					baseRes, e := s.price.Operations.BaseGet(&priceOp.BaseGetParams{
						ID:      priceID,
						Context: ctx,
					})
					if e != nil || !baseRes.IsSuccess() {
						if e == nil {
							e = errors.New("get price failed")
						}
						logger.WithField("itemCode", itemCode).
							WithError(e).
							Error("get price failed")
						return nil, e
					}
					basePrice, ok := baseRes.GetPayload().Items[itemCode]
					if !ok || basePrice.Price == nil {
						e = errors.New("empty base price")
						logger.WithField("itemCode", itemCode).
							WithError(e).
							Error("empty base price")
						return nil, e
					}
					return baseRes.GetPayload(), nil
				},
			)
			if e != nil {
				return nil, e
			}

			rangeVals := make([]int64, 0)
			for _, bp := range basePrices {
				for _, priceRange := range bp.Items[itemCode].Price.Ranges {
					rangeVals = append(rangeVals, priceRange.Range)
				}
			}
			translateMap, e := s.translateMeasureUnit(
				ctx,
				itemCode,
				rangeVals,
			)
			if e != nil {
				logger.WithField("itemCode", itemCode).
					WithError(e).
					Error("translate measure unit failed")
				return nil, e
			}

			zonePrices := make(map[int64]*models.Price)
			for _, bp := range basePrices {
				basePrice := bp.Items[itemCode]
				ranges := make([]*models.Range, 0)
				for idx, priceRange := range basePrice.Price.Ranges {
					if idx == 0 {
						ranges = append(ranges, &models.Range{
							From:    0,
							To:      uint64(priceRange.Range),
							Price:   priceRange.Price,
							FromStr: "0",
							ToStr:   translateMap[priceRange.Range],
						})
					} else {
						ranges = append(ranges, &models.Range{
							From:    uint64(basePrice.Price.Ranges[idx-1].Range),
							To:      uint64(priceRange.Range),
							Price:   priceRange.Price,
							FromStr: translateMap[basePrice.Price.Ranges[idx-1].Range],
							ToStr:   translateMap[priceRange.Range],
						})
					}
				}
				zonePrices[bp.Zone] = &models.Price{
					Type:   basePrice.Price.Type,
					Ranges: ranges,
				}
			}
			return zonePrices, nil
		},
	)
	if err != nil {
		logger.WithField("itemCodes", itemCodes).
			WithError(err).
			Error("fetch item price failed")
		return nil, err
	}

	priceMap := make(map[string]map[int64]*models.Price)
	for i, code := range itemCodes {
		priceMap[code] = prices[i]
	}
	return priceMap, nil
}

func (s *PlanService) translateMeasureUnit(
	ctx context.Context,
	itemCode string,
	rangeVals []int64,
) (map[int64]string, error) {
	logger := logging.GetLogger(ctx)

	translateMap := make(map[int64]string)

	rawValues := make([]*gaeamodels.TranslateMeasureInputEntry, 0)
	for _, priceRange := range base.UniqueIntSlice(rangeVals) {
		if priceRange == 0 {
			continue
		}
		rawValues = append(rawValues, &gaeamodels.TranslateMeasureInputEntry{
			DateType: "", // 空: 默认计费类型
			RawVal:   priceRange,
		})
	}

	if len(rawValues) <= 0 {
		return translateMap, nil
	}

	res, err := s.gaea.Operations.TranslateMeasureWithHigherPriceUnit(
		&gaeaOp.TranslateMeasureWithHigherPriceUnitParams{
			Entries: &gaeamodels.TranslateMeasureReq{
				Entries: rawValues,
			},
			ID:      itemCode,
			Context: ctx,
		},
	)
	if err != nil || res.GetPayload().Code != http.StatusOK {
		if err == nil {
			err = errors.New(res.GetPayload().Message)
		}
		logger.WithField("itemCode", itemCode).
			WithError(err).
			Error("translate measure failed")
		return translateMap, err
	}

	for _, d := range res.GetPayload().Data {
		translateMap[d.RawVal] = fmt.Sprintf("%0.0f%s", d.Val, d.Unit)
	}
	return translateMap, nil
}

// AttachUsageOnRights 补充免费额度已用量数据
func (s *PlanService) AttachUsageOnRights(
	ctx context.Context,
	usrPlan *models.UserPlan,
	plan *models.Plan,
) error {
	logger := logging.GetLogger(ctx)
	if plan.Level == enums.LevelEnterprise {
		return nil
	}
	if plan.Packs == nil || len(plan.Packs.Packs) <= 0 {
		return nil
	}
	packs, ok := plan.SearchPacks(usrPlan.PacksCode)
	if !ok {
		return errors.New("packs not found")
	}

	excode := usrPlan.GenRightsBindingExcode()
	respackIDs := make([]string, 0)
	for _, rights := range packs.Rights {
		respackID := rights.GenFreeRespackID()
		if respackID == "" {
			continue
		}
		respackIDs = append(respackIDs, respackID)
	}
	if len(respackIDs) <= 0 {
		// 用户的这个 plan 套餐里面没有一个免费额度类的权益
		return nil
	}

	usage, err := s.wallet.Operations.RespackDummyUsage(&walletOp.RespackDummyUsageParams{
		Req: &walletmodels.ReqRespackDummyUsage{
			Excodes:    []string{excode},
			RespackIds: respackIDs,
			Month:      base.NewMonth(time.Now().In(tz.MustLocationFromCtx(ctx))).String(),
			UID:        uint32(usrPlan.UID),
		},
		Context: ctx,
	})
	if err != nil || !usage.IsSuccess() {
		if err == nil {
			err = errors.New("query respack usage failed")
		}
		logger.WithFields(logrus.Fields{
			"usrPlan":    usrPlan,
			"excode":     excode,
			"respackIDs": respackIDs,
		}).WithError(err).Error("query usage failed")
		return err
	}
	usageMap := make(map[string]*walletmodels.ModelRespackUsage)
	for _, dummyUsage := range usage.GetPayload().DummyUsages {
		usageMap[fmt.Sprintf("%s:%d", dummyUsage.RespackID, dummyUsage.ZoneCode)] = dummyUsage
	}
	// 赋值查到的用量
	for _, pack := range plan.Packs.Packs {
		for j, rights := range pack.Rights {
			if rights.Type != enums.RightsTypeFreeRespack {
				continue
			}
			key := fmt.Sprintf("%s:%d", rights.GenFreeRespackID(), rights.Zone)
			if usedDetail, okk := usageMap[key]; okk {
				pack.Rights[j].Used = usedDetail.Used
				if usedDetail.Used > 0 {
					itemDeductDetails := make([]*planPb.ItemDeductDetail, 0)
					for _, detail := range usedDetail.DeductDetails[0].ItemDeductDetails {
						itemDeductDetails = append(itemDeductDetails, &planPb.ItemDeductDetail{
							ZoneCode:        detail.ZoneCode,
							ZoneName:        detail.ZoneName,
							ItemCode:        detail.ItemCode,
							ItemName:        detail.ItemName,
							DeductAmount:    detail.DeductAmount,
							DeductAmountRaw: detail.DeductAmountRaw,
						})
					}
					pack.Rights[j].DeductDetails = &planPb.DeductDetail{
						DeductDate:        timestamppb.New(time.Time(usedDetail.DeductDetails[0].DeductDate)),
						DeductAmount:      usedDetail.DeductDetails[0].DeductAmount,
						DeductAmountRaw:   usedDetail.DeductDetails[0].DeductAmount,
						ItemDeductDetails: itemDeductDetails,
					}
				}
			}
		}
	}

	return nil
}

// OnlineWaitingPlans 上线等待上线的 plan
func (s *PlanService) OnlineWaitingPlans(
	ctx context.Context,
	now time.Time,
) error {
	_, err := resultgroup.ParallelMap(
		[]enums.PlanLevel{
			enums.LevelFree, enums.LevelPro, enums.LevelBusiness,
		},
		func(level enums.PlanLevel) (error, error) {
			plans, err := s.dao.PlanDao.ListWaitingOnline(ctx, level, now)
			if err != nil {
				return nil, err
			}
			sort.SliceStable(plans, func(i, j int) bool {
				return plans[i].EffectTime < plans[j].EffectTime
			})
			var candidatePlan *models.Plan
			// plans 已按照 effect_time asc 排序, 这里取最新的满足条件的 effect_time
			for _, plan := range plans {
				candidatePlan = plan
			}
			if candidatePlan == nil {
				return nil, nil
			}
			// 下线 current online
			err = s.dao.PlanDao.SetOffline(
				ctx, level,
			)
			if err != nil {
				return nil, err
			}
			// 再上线这个候选 plan
			err = s.dao.PlanDao.UpdateStatusByID(
				ctx, candidatePlan.ID, enums.PlanStatusOnline,
			)
			if err != nil {
				return nil, err
			}
			return nil, nil
		},
	)
	if err != nil {
		return err
	}
	return nil
}
