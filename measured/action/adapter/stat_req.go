package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
)

// BuildPbStatReqV1 converts from model.StatReq to the protobuf type.
func BuildPbStatReqV1(x *model.StatReq) (*pb.StatReqV1, error) {
	return &pb.StatReqV1{
		ItemCode: x.ItemCode,
		DataType: x.DataType,
		Uid:      x.UID,
		Begin:    timestamppb.New(x.Begin),
		End:      timestamppb.New(x.End),
		ZoneCode: x.ZoneCode,
		Debug:    x.Debug,

		IsForIdleRespack: x.IsForIdleRespack,
		ResourceName:     x.ResourceName,
	}, nil
}

// BuildModelStatReqV1 converts from pb.StatReqV1 to the model layer type.
func BuildModelStatReqV1(x *pb.StatReqV1) (*model.StatReq, error) {
	var begin, end time.Time
	if x.Begin != nil {
		err := x.Begin.CheckValid()
		if err != nil {
			return nil, err
		}
		begin = x.Begin.AsTime()
	}

	if x.End != nil {
		err := x.End.CheckValid()
		if err != nil {
			return nil, err
		}
		end = x.End.AsTime()
	}

	return &model.StatReq{
		ItemCode: x.ItemCode,
		DataType: x.DataType,
		UID:      x.Uid,
		Begin:    begin,
		End:      end,
		ZoneCode: x.ZoneCode,
		Debug:    x.Debug,

		IsForIdleRespack: x.IsForIdleRespack,
		ResourceName:     x.ResourceName,
	}, nil
}
