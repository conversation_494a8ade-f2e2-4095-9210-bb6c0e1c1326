package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestItemDataType(t *testing.T) {
	sandbox := buildSandbox(t)

	dictService := sandbox.dictService

	const itemID uint64 = 1
	ts := []model.ItemDataType{
		{
			ItemID:    itemID,
			Name:      "流量",
			Type:      "",
			UnitRate:  1024,
			IsDefault: true,
		},
		{
			ItemID:    itemID,
			Name:      "带宽",
			Type:      "bandwidth",
			UnitRate:  1000,
			IsDefault: false,
		},
	}
	assertFields := func(group *model.ItemDataType, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(ts) }) {
			assert.Equal(t, ts[n].ItemID, group.ItemID, msgAndArgs...)
			assert.Equal(t, ts[n].Name, group.Name, msgAndArgs...)
			assert.Equal(t, ts[n].UnitRate, group.UnitRate, msgAndArgs...)
			assert.Equal(t, ts[n].IsDefault, group.IsDefault, msgAndArgs...)
		}
	}
	ctx := context.TODO()
	ids := make(map[uint64]int, len(ts))
	for n, _t := range ts {
		dataType, err := dictService.CreateItemDataType(ctx, &_t)
		if assert.NoError(t, err, "CreateItemDataType") {
			assert.NotZero(t, dataType.ID, "CreateItemDataType return zero id")
			ids[dataType.ID] = n
		}

		dataType, err = dictService.GetItemDataTypeByID(ctx, dataType.ID)
		if assert.NoError(t, err, "GetItemDataTypeByID") {
			assertFields(dataType, n, "GetItemDataTypeByID fields not match")
		}

		dataType.Name += "suffix1"
		ts[n].Name += "suffix1"
		dataType, err = dictService.UpdateItemDataTypeByID(ctx, dataType.ID, dataType)
		if assert.NoError(t, err, "UpdateItemDataTypeByID") {
			assert.NotZero(t, dataType.ID, "UpdateItemDataTypeByID return zero id")
		}
	}

	groups, err := dictService.ListItemDataTypesByItemID(ctx, itemID, 0, 10)
	if assert.NoError(t, err, "ListItemDataTypesByItemID") {
		assert.Len(t, groups, len(ts), "ListItemDataTypesByItemID unexpected length")
		count, err := dictService.CountItemDataTypesByItemID(ctx, itemID)
		if assert.NoError(t, err, "CountItemDataTypesByItemID") {
			assert.Equal(t, uint64(len(ts)), count, "CountItemDataTypesByItemID count unexpected")
		}
		for _, group := range groups {
			if assert.Contains(t, ids, group.ID) {
				assertFields(&group, ids[group.ID])
			}
		}
	}

	for id, n := range ids {
		group, err := dictService.DeleteItemDataTypeByID(ctx, id)
		if assert.NoError(t, err, "DeleteItemDataTypeByID") {
			assertFields(group, n)
		}
	}

	count, err := dictService.CountItemDataTypesByItemID(ctx, itemID)
	if assert.NoError(t, err, "CountItemDataTypesByItemID") {
		assert.Equal(t, uint64(0), count, "CountItemDataTypesByItemID count after delete all is not zero")
	}
}
