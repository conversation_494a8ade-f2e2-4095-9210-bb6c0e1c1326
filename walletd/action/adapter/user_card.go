package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"
	"google.golang.org/protobuf/types/known/timestamppb"
	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/wallet/model"
)

func BuildPbUserCard(card *model.UserCard) *pb.UserCard {
	return &pb.UserCard{
		Uid:          card.UID,
		LastDigits:   card.LastDigits,
		Brand:        card.Brand,
		CardType:     pb.CardType(card.CardType),
		IsDefault:    card.IsDefault,
		CardStatus:   pb.CardStatus(card.Status),
		PaymentToken: card.PaymentToken,
		CreatedAt:    timestamppb.New(*card.CreatedAt),
		UpdatedAt:    timestamppb.New(*card.UpdatedAt),
	}
}

func BuildPbUserCards(cards []model.UserCard) *pb.UserCards {
	c := make([]*pb.UserCard, 0)
	for _, card := range cards {
		c = append(c, BuildPbUserCard(&card))
	}
	return &pb.UserCards{
		UserCards: c,
	}
}

func BuildUserCard(card *pb.UserCard) model.UserCard {
	return model.UserCard{
		UID:          card.Uid,
		LastDigits:   card.LastDigits,
		Brand:        card.Brand,
		CardType:     enums.CardType(card.CardType),
		Status:       enums.CardStatus(card.CardStatus),
		PaymentToken: card.PaymentToken,
		IsDefault:    card.IsDefault,
	}
}
