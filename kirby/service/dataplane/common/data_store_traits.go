package common

import (
	"context"
	"time"

	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/dataplaneModel"
)

type IDataStore interface {
	// FeedSeries 推入一条序列
	FeedSeries(ctx context.Context, s *dataplaneModel.FlattenedSeries)
	// PersistAllPendingSeries 将所有未持久化的序列数据写回
	PersistAllPendingSeries(ctx context.Context) (int, error)
	// RunAggregations 聚合 month 月份的从 since（含）到 epoch（不含）为止的所有未处理的数据，更新汇总序列
	RunAggregations(
		ctx context.Context,
		month time.Time,
		since time.Time,
		epoch time.Time,
	) error
	// ProvisionShards 为一个月份建立分片（建表）
	ProvisionShards(ctx context.Context, month time.Time) error

	NotifyEngineStart()
	NotifyEngineStop()
	EventChan() <-chan time.Time

	ListSeries(
		ctx context.Context,
		req *dataplaneModel.ReqListSeries,
	) (*dataplaneModel.SeriesDetailList, error)

	ListSeriesDistinctUIDs(
		ctx context.Context,
		req *dataplaneModel.ReqListSeries,
	) ([]uint64, error)

	ListAggregatedSeries(
		ctx context.Context,
		req *dataplaneModel.ReqListAggregatedSeries,
	) (*dataplaneModel.AggregatedSeriesDetailList, error)

	ListAggregatedSeriesDistinctUIDs(
		ctx context.Context,
		req *dataplaneModel.ReqListAggregatedSeries,
	) ([]uint64, error)

	QueryAggregatedSeriesData(
		ctx context.Context,
		uid uint64,
		month time.Time,
		itemCode string,
		zoneCode int64,
		// dataType string, // TODO
		desiredG adminModel.Granularity,
		epoch time.Time,
	) ([]*pb.StatPoint, error)

	ListItemUIDsByMonthAndZone(
		ctx context.Context,
		itemCodes []string,
		month time.Time,
		zoneCode int64,
		epoch time.Time,
	) ([]uint64, error)

	ListUserItemsByMonthAndZone(
		ctx context.Context,
		uid uint64,
		month time.Time,
		zoneCode int64,
		epoch time.Time,
	) ([]string, error)
}
