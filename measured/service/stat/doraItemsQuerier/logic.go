package doraItemsQuerier

func mapDoraCmdKeysToItems(
	dict doraCmdsItemsDict,
	productCode string,
	keys []string,
) []string {
	if len(keys) == 0 {
		return nil
	}

	seenItemCodesSet := make(map[string]struct{})
	var result []string
	for _, key := range keys {
		itemEntries, ok := dict[key]
		if !ok {
			// 计费系统没有记录这个 cmd，可能是暂未对接或不收费的 cmd，不处理
			continue
		}

		for _, itemEntry := range itemEntries {
			if productCode != itemEntry.ProductCode {
				// 这次没请求这个产品线
				continue
			}

			if _, ok := seenItemCodesSet[itemEntry.ItemCode]; ok {
				// 结果列表中已经包含这个 item code 了
				continue
			}

			seenItemCodesSet[itemEntry.ItemCode] = struct{}{}
			result = append(result, itemEntry.ItemCode)
		}
	}

	return result
}
