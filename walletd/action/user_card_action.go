package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

func (a *WalletAction) ListUserCards(ctx context.Context, param *pb.UIDParam) (*pb.UserCards, error) {
	cards, err := a.userCardSrv.ListUserCards(ctx, param.Uid)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbUserCards(cards), nil
}

func (a *WalletAction) BindCardToUser(ctx context.Context, userCard *pb.UserCard) (*empty.Empty, error) {
	err := a.userCardSrv.BindCardToUser(ctx, adapter.BuildUserCard(userCard))
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}

func (a *WalletAction) UnbindUserCard(ctx context.Context, userCard *pb.UserCard) (*empty.Empty, error) {
	err := a.userCardSrv.UnbindUserCard(ctx, adapter.BuildUserCard(userCard))
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}

func (a *WalletAction) GetUserDefaultCard(ctx context.Context, param *pb.UIDParam) (*pb.UserCard, error) {
	card, err := a.userCardSrv.GetUserDefaultCard(ctx, param.Uid)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbUserCard(card), nil
}

func (a *WalletAction) UpdateUserDefaultCard(ctx context.Context, userCard *pb.UserCard) (*empty.Empty, error) {
	card := adapter.BuildUserCard(userCard)
	err := a.userCardSrv.UpdateUserDefaultCard(ctx, card)
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}
