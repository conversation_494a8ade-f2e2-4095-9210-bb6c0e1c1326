package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
)

// BuildPbItemExtraArg converts from model.ItemExtraArg to the protobuf type.
func BuildPbItemExtraArg(x *model.ItemExtraArg) (*pb.ItemExtraArg, error) {
	return &pb.ItemExtraArg{
		Id:        x.ID,
		ItemId:    x.ItemID,
		Key:       x.Key,
		Value:     x.Value,
		CreatedAt: timestamppb.New(x.CreatedAt),
		UpdatedAt: timestamppb.New(x.UpdatedAt),
	}, nil
}

// BuildModelItemExtraArg converts from pb.ItemExtraArg to the model layer type.
func BuildModelItemExtraArg(x *pb.ItemExtraArg) (*model.ItemExtraArg, error) {
	var createdAt, updatedAt time.Time
	if x.GetCreatedAt() != nil {
		err := x.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = x.GetCreatedAt().AsTime()
	}

	if x.GetUpdatedAt() != nil {
		err := x.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = x.GetUpdatedAt().AsTime()
	}

	return &model.ItemExtraArg{
		ID:        x.GetId(),
		ItemID:    x.GetItemId(),
		Key:       x.GetKey(),
		Value:     x.GetValue(),
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	}, nil
}
