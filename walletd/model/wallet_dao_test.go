package model_test

import (
	"testing"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/walletd/model"
)

type sandbox struct {
	testWrap                  *test.Wrap
	walletDao                 *model.WalletDao
	userBankVirtualAccountDao *model.UserBankVirtualAccountDao
}

func buildSandbox(t *testing.T) *sandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in wallet/model return error")
	}

	return &sandbox{
		testWrap:                  testWrap,
		walletDao:                 model.NewWalletDao(testWrap.BaseDao()),
		userBankVirtualAccountDao: model.NewUserBankVirtualAccountDao(testWrap.BaseDao()),
	}
}
