package action

import (
	"context"

	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	pb "github.com/qbox/pay-sdk/wallet"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/coupon/action/adapter"
)

// GetCouponByCode 根据券码查询一张券
func (a *CouponAction) GetCouponByCode(
	ctx context.Context,
	req *pb.CodeParam,
) (*pb.CouponDetail, error) {
	coupon, err := a.couponSrv.GetCouponByCode(ctx, req.Code)
	if err != nil {
		return nil, errors.Trace(err)
	}

	couponBatchAndScope, err := a.couponBatchSrv.GetByID(ctx, coupon.BatchID)
	if err != nil {
		return nil, errors.Trace(err)
	}

	resp, err := adapter.ComposeCouponDetail(
		coupon,
		couponBatchAndScope.CouponBatch,
		couponBatchAndScope.Scope,
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// ListAllCoupons 分页列出所有抵用券，返回底层数据结构
func (a *CouponAction) ListAllCoupons(
	ctx context.Context,
	req *pb.PagingParam,
) (*pb.CouponList, error) {
	offset, limit := a.Paging(req)

	srvObjs, count, err := a.couponSrv.ListAllCoupons(ctx, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCoupons(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponList{
		Coupons: objs,
		Count:   count,
	}, nil
}

// ListCouponsByUID 按 UID 分页列出抵用券，返回底层数据结构
func (a *CouponAction) ListCouponsByUID(
	ctx context.Context,
	req *pb.UIDPagingParam,
) (*pb.CouponList, error) {
	offset, limit := a.Paging(req)

	srvObjs, count, err := a.couponSrv.ListCouponsByUID(ctx, req.Uid, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCoupons(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponList{
		Coupons: objs,
		Count:   count,
	}, nil
}

// ListCouponsByBatchID 按抵用券批次 ID 分页列出抵用券，返回底层数据结构
func (a *CouponAction) ListCouponsByBatchID(
	ctx context.Context,
	req *pb.IDPagingParam,
) (*pb.CouponList, error) {
	offset, limit := a.Paging(req)

	srvObjs, count, err := a.couponSrv.ListCouponsByBatchID(ctx, req.Id, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCoupons(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponList{
		Coupons: objs,
		Count:   count,
	}, nil
}

// ListCouponsByConds 按条件分页列出抵用券，返回底层数据结构
func (a *CouponAction) ListCouponsByConds(
	ctx context.Context,
	req *pb.UIDStatePagingParam,
) (*pb.CouponList, error) {
	offset, limit := a.Paging(req)

	srvReq, err := adapter.BuildListCouponsByCondsParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	srvObjs, count, err := a.couponSrv.ListCouponsByConds(ctx, srvReq, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCoupons(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponList{
		Coupons: objs,
		Count:   count,
	}, nil
}

// ListAllCouponDetails 分页列出所有抵用券，适合前端展示
func (a *CouponAction) ListAllCouponDetails(
	ctx context.Context,
	req *pb.PagingParam,
) (*pb.CouponDetailList, error) {
	offset, limit := a.Paging(req)

	srvCoupons, count, err := a.couponSrv.ListAllCoupons(ctx, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	srvObjs, err := a.couponSrv.AssociateCouponsWithBatches(ctx, srvCoupons)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCouponDetails(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponDetailList{
		CouponDetails: objs,
		Count:         count,
	}, nil
}

// ListCouponDetailsByUID 按 UID 分页列出抵用券，适合前端展示
func (a *CouponAction) ListCouponDetailsByUID(
	ctx context.Context,
	req *pb.UIDPagingParam,
) (*pb.CouponDetailList, error) {
	offset, limit := a.Paging(req)

	srvCoupons, count, err := a.couponSrv.ListCouponsByUID(ctx, req.Uid, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	srvObjs, err := a.couponSrv.AssociateCouponsWithBatches(ctx, srvCoupons)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCouponDetails(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponDetailList{
		CouponDetails: objs,
		Count:         count,
	}, nil
}

// ListCouponDetailsByBatchID 按抵用券批次 ID 分页列出抵用券，适合前端展示
func (a *CouponAction) ListCouponDetailsByBatchID(
	ctx context.Context,
	req *pb.IDPagingParam,
) (*pb.CouponDetailList, error) {
	offset, limit := a.Paging(req)

	srvCoupons, count, err := a.couponSrv.ListCouponsByBatchID(ctx, req.Id, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	srvObjs, err := a.couponSrv.AssociateCouponsWithBatches(ctx, srvCoupons)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCouponDetails(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponDetailList{
		CouponDetails: objs,
		Count:         count,
	}, nil
}

// ListCouponDetailsByConds 按条件分页列出抵用券，适合前端展示
func (a *CouponAction) ListCouponDetailsByConds(
	ctx context.Context,
	req *pb.CouponDetailsByCondsParam,
) (*pb.CouponDetailList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"req": req,
	}).Info("ListCouponDetailsByConds")

	offset, limit := a.Paging(req)

	srvReq, err := adapter.BuildListCouponDetailsByCondsParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	srvCoupons, count, err := a.couponSrv.ListCouponsByDetailConds(ctx, srvReq, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	srvObjs, err := a.couponSrv.AssociateCouponsWithBatches(ctx, srvCoupons)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCouponDetails(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponDetailList{
		CouponDetails: objs,
		Count:         count,
	}, nil
}

// ListOrderedCouponDetailsByConds 按条件分页列出抵用券，按照余额、过期时间排序
func (a *CouponAction) ListOrderedCouponDetailsByConds(
	ctx context.Context,
	req *pb.CouponDetailsByCondsParam,
) (*pb.CouponDetailList, error) {
	offset, limit := a.Paging(req)
	srvReq, err := adapter.BuildListCouponDetailsByCondsParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	srvCoupons, count, err := a.couponSrv.ListOrderedCouponsByDetailConds(ctx, srvReq, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	srvObjs, err := a.couponSrv.AssociateCouponsWithBatches(ctx, srvCoupons)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCouponDetails(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponDetailList{
		CouponDetails: objs,
		Count:         count,
	}, nil
}
