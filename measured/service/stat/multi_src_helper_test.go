package stat

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseMultiSrcItemSpec(t *testing.T) {
	{
		input := map[string]string{
			"stat_query": "__stat_zone__=3001@fusion:transfer:all:ov~3001,fusion:transfer:http:ov~3007,fusion:transfer:https:ov~3007,pili:transfer:down:cn~0",
		}
		expected := multiSrcItemSpec{
			3001: multiSrcItemZoneSpec{
				PostprocessStrategy: postprocessStrategySum,
				Axes: []multiSrcAxisSpec{
					{ItemCode: "fusion:transfer:all:ov", ZoneCode: 3001},
					{ItemCode: "fusion:transfer:http:ov", ZoneCode: 3007},
					{ItemCode: "fusion:transfer:https:ov", ZoneCode: 3007},
					{ItemCode: "pili:transfer:down:cn", ZoneCode: 0},
				},
			},
		}
		actual, err := parseMultiSrcItemSpec(input)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=10001@sum(foo~10001,bar~20002)",
		}
		expected := multiSrcItemSpec{
			10001: multiSrcItemZoneSpec{
				PostprocessStrategy: postprocessStrategySum,
				Axes: []multiSrcAxisSpec{
					{ItemCode: "foo", ZoneCode: 10001},
					{ItemCode: "bar", ZoneCode: 20002},
				},
			},
		}
		actual, err := parseMultiSrcItemSpec(input)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=10001@max(foo~10001,bar~20002)",
		}
		expected := multiSrcItemSpec{
			10001: multiSrcItemZoneSpec{
				PostprocessStrategy: postprocessStrategyMax,
				Axes: []multiSrcAxisSpec{
					{ItemCode: "foo", ZoneCode: 10001},
					{ItemCode: "bar", ZoneCode: 20002},
				},
			},
		}
		actual, err := parseMultiSrcItemSpec(input)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=10001@max(foo~10001)",
		}
		expected := multiSrcItemSpec{
			10001: multiSrcItemZoneSpec{
				PostprocessStrategy: postprocessStrategyMax,
				Axes: []multiSrcAxisSpec{
					{ItemCode: "foo", ZoneCode: 10001},
				},
			},
		}
		actual, err := parseMultiSrcItemSpec(input)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=10001@max(foo~10001,bar~20002)|3002@bar~0,baz~1|233@sum(quux~0,foo~10001)",
		}
		expected := multiSrcItemSpec{
			10001: multiSrcItemZoneSpec{
				PostprocessStrategy: postprocessStrategyMax,
				Axes: []multiSrcAxisSpec{
					{ItemCode: "foo", ZoneCode: 10001},
					{ItemCode: "bar", ZoneCode: 20002},
				},
			},
			3002: multiSrcItemZoneSpec{
				PostprocessStrategy: postprocessStrategySum,
				Axes: []multiSrcAxisSpec{
					{ItemCode: "bar", ZoneCode: 0},
					{ItemCode: "baz", ZoneCode: 1},
				},
			},
			233: multiSrcItemZoneSpec{
				PostprocessStrategy: postprocessStrategySum,
				Axes: []multiSrcAxisSpec{
					{ItemCode: "quux", ZoneCode: 0},
					{ItemCode: "foo", ZoneCode: 10001},
				},
			},
		}
		actual, err := parseMultiSrcItemSpec(input)
		assert.NoError(t, err)
		assert.Equal(t, expected, actual)
	}

	{
		// 不能处理原先支持的简化配置
		input := map[string]string{
			"stat_key": "fusion:transfer:http:ov,fusion:transfer:https:ov",
		}
		_, err := parseMultiSrcItemSpec(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=3001%2g",
		}
		_, err := parseMultiSrcItemSpec(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=3001",
		}
		_, err := parseMultiSrcItemSpec(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=300x@",
		}
		_, err := parseMultiSrcItemSpec(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=3001@fusion:transfer:all:ov,pili:transfer:down:cn~0",
		}
		_, err := parseMultiSrcItemSpec(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=3001@fusion:transfer:all:ov~x,pili:transfer:down:cn~0",
		}
		_, err := parseMultiSrcItemSpec(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=10001@maxfoo~10001,bar~20002)",
		}
		_, err := parseMultiSrcItemSpec(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=10001@sun(foo~10001,bar~20002)",
		}
		_, err := parseMultiSrcItemSpec(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_query": "__stat_zone__=10001@max()",
		}
		_, err := parseMultiSrcItemSpec(input)
		assert.Error(t, err)
	}
}

func TestParsePostprocessStrategy(t *testing.T) {
	testcases := []struct {
		input    string
		ok       bool
		expected postprocessStrategy
	}{
		{
			input: "",
			ok:    false,
		},
		{
			input:    "sum",
			ok:       true,
			expected: postprocessStrategySum,
		},
		{
			input:    "max",
			ok:       true,
			expected: postprocessStrategyMax,
		},
		{
			input: "Sum",
			ok:    false,
		},
		{
			input: "may",
			ok:    false,
		},
		{
			input: "sum ",
			ok:    false,
		},
		{
			input: " max",
			ok:    false,
		},
		{
			input: "ｍａｘ",
			ok:    false,
		},
		{
			input: "mах",
			ok:    false, // Can you spot the difference?
		},
	}

	for _, tc := range testcases {
		actual, err := parsePostprocessStrategy(tc.input)
		if tc.ok {
			assert.NoError(t, err)
			assert.Equal(t, tc.expected, actual)
		} else {
			assert.Error(t, err)
			assert.Equal(t, postprocessStrategyUnknown, actual)
		}
	}
}
