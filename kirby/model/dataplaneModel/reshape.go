package dataplaneModel

import (
	"errors"
	"math"
	"sort"
	"time"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
)

var errTimeNotGranularityAligned = errors.New("time is not granularity-aligned")

// NonexistentValue 代表一个密集表示中不存在的计量点
//
// 如一个时刻的计量值为此值，意味着该点在原始输入中不存在，用于在存储时区分“该时刻的值被明确推为 0”的情况。
// 作为代价，输入数据无法取到该值，如果序列中有任何计量点取到该值，整条序列将在推量时被拒绝。
const NonexistentValue = math.MaxUint64

// 将给定的稀疏表示和起止时刻（左闭右开区间），变换为密集表示、用于存储的数据行。
//
// 位于起止时刻之间，没有给出值的点，其计量被视为 0；起止时刻之外，但在结果中（不得不）被包含的点，其值为
// NonexistentValue。
func rowsFromSparseReprAndTimespan(
	g adminModel.Granularity,
	start time.Time,
	end time.Time,
	loc *time.Location,
	points []*pb.StatPoint,
) ([]dataRow, error) {
	err := g.Validate()
	if err != nil {
		return nil, err
	}

	err = checkTimeAlignment(g, start, end, loc, points)
	if err != nil {
		return nil, err
	}

	result := prepareEmptyDataRows(g, start, end, loc)
	fillRowsWithSparsePointsInplace(result, g, points)
	return result, nil
}

func checkTimeAlignment(
	g adminModel.Granularity,
	start time.Time,
	end time.Time,
	loc *time.Location,
	points []*pb.StatPoint,
) error {
	if !isTimeAlignedWrtGranularity(g, start, loc) || !isTimeAlignedWrtGranularity(g, end, loc) {
		return errTimeNotGranularityAligned
	}

	for _, p := range points {
		if !isTimeAlignedWrtGranularity(g, p.Time.AsTime(), loc) {
			return errTimeNotGranularityAligned
		}
	}

	return nil
}

func prepareEmptyDataRows(
	g adminModel.Granularity,
	start time.Time,
	end time.Time,
	loc *time.Location,
) []dataRow {
	start = start.In(loc)
	end = end.In(loc)
	alignedStart := rowT0FromStartTime(g, start)

	var result []dataRow
	for t := alignedStart; t.Before(end); t = incrementOneRowWrtGranularity(g, t) {
		result = append(result, dataRow{
			T0: t,
			P:  make([]uint64, getNumPointsPerRow(g, t)),
		})
	}

	// 第一行最初一些点不存在的情况
	if !alignedStart.Equal(start) {
		fillNonExistentPointsInplace(&result[0], g, start, false)
	}

	// 处理最后一行最后一些点不存在的情况
	alignedEnd := incrementOneRowWrtGranularity(g, result[len(result)-1].T0)
	if !alignedEnd.Equal(end) {
		fillNonExistentPointsInplace(&result[len(result)-1], g, end, true)
	}

	return result
}

// 根据 start 和 g 计算这个 start 所在的 dataRow 应该取什么样的 T0
//
// 要求 g 合法，start 已经按照 g 正确对齐
func rowT0FromStartTime(g adminModel.Granularity, start time.Time) time.Time {
	switch g {
	case adminModel.Granularity5Min:
		// 5 分钟粒度数据的一行起始点的时刻是当天 00:00:00
		return base.Today(start)
	case adminModel.GranularityDay:
		// 日粒度数据的一行起始点的时刻是当自然月 1 号 00:00:00
		return base.ThisMonth(start)
	case adminModel.GranularityMonth:
		// 月粒度数据的一行只有一个点
		return start
	default:
		// 不知道怎么处理
		return start
	}
}

// 就地将某一侧不存在的点填充为 NonexistentValue
//
// dest 不可以为空
func fillNonExistentPointsInplace(
	dest *dataRow,
	g adminModel.Granularity,
	t time.Time,
	towardsEnd bool,
) {
	if g == adminModel.GranularityMonth {
		// 不应该出现这种情况：月粒度的数据一行只有一个点
		return
	}

	if !t.After(dest.T0) {
		// t 不属于这一行，或者 t 就是 t0 从而不需要处理
		return
	}

	rowTMax := incrementOneRowWrtGranularity(g, dest.T0)
	if !t.Before(rowTMax) {
		// t 不属于这一行
		return
	}

	// t 时刻对应 dest 的第几个点
	offsetIdx := int(t.Sub(dest.T0) / g.MinIntervalBetweenPoints())

	// 填充后部（从 offsetIdx 开始填到 row 最后一列）
	if towardsEnd {
		for i := offsetIdx; i < len(dest.P); i++ {
			dest.P[i] = NonexistentValue
		}
		return
	}

	// 填充前部（从 0 位置开始填到 offsetIdx - 1 一列）
	for i := 0; i < offsetIdx; i++ {
		dest.P[i] = NonexistentValue
	}
}

func fillRowsWithSparsePointsInplace(
	dest []dataRow,
	g adminModel.Granularity,
	points []*pb.StatPoint,
) {
	if len(points) == 0 {
		return
	}

	sort.Slice(points, func(i int, j int) bool {
		return points[i].Time.AsTime().Before(points[j].Time.AsTime())
	})

	pointDuration := g.MinIntervalBetweenPoints()
	pointIdx := 0
	for rowIdx := range dest {
		rowT0 := dest[rowIdx].T0
		rowTMax := incrementOneRowWrtGranularity(g, rowT0)
		for pointIdx < len(points) {
			p := points[pointIdx]
			pT := p.Time.AsTime()
			// p.Time >= rowTMax => 这个点不属于这一 dataRow
			if !pT.Before(rowTMax) {
				break
			}

			var idxInRow int
			if g != adminModel.GranularityMonth {
				idxInRow = int(pT.Sub(rowT0) / pointDuration)
			}
			dest[rowIdx].P[idxInRow] = p.Value

			pointIdx++
		}
	}
}

// 将给定的密集表示变换为稀疏表示，无视值为 0 和 NonexistentValue 的点。
//
// 为了性能，当前会假定输入不会包含重复的点。
func sparseReprFromRows(
	g adminModel.Granularity,
	loc *time.Location,
	rows []dataRow,
) ([]*pb.StatPoint, error) {
	err := g.Validate()
	if err != nil {
		return nil, err
	}

	var result []*pb.StatPoint
	for _, r := range rows {
		for i, t := 0, r.T0.In(loc); i < len(r.P); i, t = i+1, g.TimeOfNextPoint(t) {
			v := r.P[i]
			if v == NonexistentValue || v == 0 {
				continue
			}
			result = append(result, &pb.StatPoint{
				Time:  timestamppb.New(t),
				Value: v,
			})
		}
	}

	sortPointsInplace(result)
	return result, nil
}

func sortPointsInplace(p []*pb.StatPoint) {
	sort.Slice(p, func(i int, j int) bool { return p[i].Time.AsTime().Before(p[j].Time.AsTime()) })
}

func fillZeroesForSparseRepr(
	g adminModel.Granularity,
	start time.Time,
	end time.Time,
	loc *time.Location,
	data []*pb.StatPoint,
) []*pb.StatPoint {
	existingTimesSet := lo.SliceToMap(data, func(x *pb.StatPoint) (int64, struct{}) {
		return x.Time.AsTime().UnixNano(), struct{}{}
	})
	madeChanges := false
	for t := start.In(loc); t.Before(end); t = g.TimeOfNextPoint(t) {
		if _, ok := existingTimesSet[t.UnixNano()]; ok {
			continue
		}
		data = append(data, &pb.StatPoint{Time: timestamppb.New(t), Value: 0})
		madeChanges = true
	}

	if !madeChanges {
		// already fully populated
		return data
	}

	sortPointsInplace(data)
	return data
}
