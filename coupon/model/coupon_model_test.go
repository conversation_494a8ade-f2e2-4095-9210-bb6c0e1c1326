package model

import (
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/test"
)

type sandbox struct {
	testWrap    *test.Wrap
	couponModel *CouponModelDao
}

func buildSandbox(t *testing.T) *sandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in wallet/model return error")
	}

	return &sandbox{
		testWrap:    testWrap,
		couponModel: NewCouponModelDao(testWrap.BaseDao()),
	}
}

func TestCouponEqualsSaveAndGetByCode(t *testing.T) {
	s := buildSandbox(t)

	equalsCoupon := func(x *Coupon, y *Coupon) bool {
		return x.ID == y.ID &&
			x.BatchID == y.BatchID &&
			x.UID == y.UID &&
			x.Code == y.Code &&
			x.BindMethod == y.BindMethod &&
			x.State == y.State &&
			x.Money == y.Money &&
			x.Balance == y.Balance &&
			x.BoundAt == y.BoundAt &&
			x.EffectTime == y.EffectTime &&
			x.DeadTime == y.DeadTime &&
			// XXX 此处之所以用 Unix() 等号而不用 time.Equals 方法是测试发现单测的数据库保存时间值后竟然会丢失精度，在本地测试没有这个问题
			x.CreatedAt.Unix() == y.CreatedAt.Unix() &&
			x.UpdatedAt.Unix() == y.CreatedAt.Unix()
	}

	expireTime := time.Second * 100
	code := "code"

	x := &Coupon{
		BatchID:    2,
		UID:        2,
		Code:       code,
		BindMethod: CouponBindMethodAPI,
		State:      CouponStatePending,
		Money:      base.Money(100000),
		Balance:    base.Money(100000),
		BoundAt:    base.NewHNS(time.Now()),
		EffectTime: base.NewHNS(time.Now()),
		DeadTime:   base.NewHNS(time.Now()),
	}
	err := s.couponModel.Save(x, expireTime)
	assert.NoError(t, err)
	y, err := s.couponModel.GetByCode(code, expireTime)
	assert.NoError(t, err)
	assert.True(t, equalsCoupon(x, y))
}
