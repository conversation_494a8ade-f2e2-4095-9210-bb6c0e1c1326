package adminModel

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
)

var errUnknownGranularity = errors.New("unknown granularity")

// SPValueType 序列属性值类型
type SPValueType int

const (
	// SPValueTypeUnknown 未知的序列属性值类型
	SPValueTypeUnknown SPValueType = 0
	// SPValueTypeStringEnum 序列属性值类型：字符串枚举
	SPValueTypeStringEnum SPValueType = 1
)

// StringEnumDecl 字符串枚举类型定义
type StringEnumDecl struct {
	// Variants 枚举的可能取值列表
	Variants []StringEnumVariant `json:"v"`
}

// StringEnumVariant 字符串枚举的可能取值
type StringEnumVariant struct {
	// Value 取值
	Value string `json:"v"`
	// Desc 取值的人类可读的含义
	Desc string `json:"d"`
}

// Granularity 原始计量粒度
type Granularity int

// NOTE: 与 measureproxy 的定义一致
const (
	// GranularityUnknown 未知的原始计量粒度
	GranularityUnknown Granularity = 0
	// GranularityDay 一天一个点
	GranularityDay Granularity = 1
	// Granularity5Min 5 分钟一个点
	Granularity5Min Granularity = 2
	// GranularityMonth 一个月一个点，用于 Linking 等少数奇葩产品线
	GranularityMonth Granularity = 3
)

// SPMatcher 序列属性的匹配条件描述
type SPMatcher struct {
	// StringEnumValueIn 如被匹配的 key 类型为字符串枚举，其将被匹配的取值范围
	StringEnumValueIn []string `json:"sev_in"`
}

// SPMatchExpr 序列属性的匹配表达式
//
// 目前仅支持相互之间均为 AND 关系的 IN 操作（e.g. `foo = 'aaa' AND bar IN ('bbb', 'ccc')` 之类）
type SPMatchExpr map[string]SPMatcher

// DB adapters for SPValueType

var _ driver.Valuer = SPValueType(0)

func (x SPValueType) Value() (driver.Value, error) {
	return int64(x), nil
}

var _ sql.Scanner = (*SPValueType)(nil)

func (x *SPValueType) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*x = SPValueType(ns.Int64)
	return nil
}

// DB adapters for StringEnumDecl

var _ driver.Valuer = (*StringEnumDecl)(nil)

func (x *StringEnumDecl) Value() (driver.Value, error) {
	payload, err := json.Marshal(x)
	if err != nil {
		return nil, err
	}
	return string(payload), nil
}

var _ sql.Scanner = (*SPValueType)(nil)

func (x *StringEnumDecl) Scan(src any) error {
	ns := sql.NullString{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	return json.Unmarshal([]byte(ns.String), x)
}

// DB adapters for Granularity

var _ driver.Valuer = Granularity(0)

func (x Granularity) Value() (driver.Value, error) {
	return int64(x), nil
}

var _ sql.Scanner = (*Granularity)(nil)

func (x *Granularity) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*x = Granularity(ns.Int64)
	return nil
}

// DB adapters for SPMatches

var _ driver.Valuer = (*SPMatchExpr)(nil)

func (x *SPMatchExpr) Value() (driver.Value, error) {
	payload, err := json.Marshal(x)
	if err != nil {
		return nil, err
	}
	return string(payload), nil
}

var _ sql.Scanner = (*SPMatchExpr)(nil)

func (x *SPMatchExpr) Scan(src any) error {
	ns := sql.NullString{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	return json.Unmarshal([]byte(ns.String), x)
}
