package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/wallet"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/coupon/action/adapter"
)

// ListCouponUsageRecordsByOrderHashes 查询一组 order_hash 的抵用券抵扣记录
func (a *CouponAction) ListCouponUsageRecordsByOrderHashes(
	ctx context.Context,
	req *pb.CodesParam,
) (*pb.CouponUsageRecordList, error) {
	srvObjs, err := a.couponSrv.ListCouponUsageRecordsByOrderHashes(ctx, req.Codes)
	if err != nil {
		return nil, errors.Trace(err)
	}

	objs, err := adapter.BuildPbCouponUsageRecords(srvObjs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.CouponUsageRecordList{
		CouponUsageRecords: objs,
	}, nil
}
