package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measureproxyd/model"
)

func TestRespTableItemWithoutGroupRoundTrip(t *testing.T) {
	x := &model.RespTableItemWithoutGroup{
		Time: time.Now(),
		Values: map[string]int64{
			"flow":       233,
			"bandwidth":  456,
			"concurrent": 789,
		},
	}

	y, err := BuildPbRespTableItemWithoutGroup(x)
	assert.NoError(t, err)

	z, err := BuildModelRespTableItemWithoutGroup(y)
	assert.NoError(t, err)

	assert.Equal(t, x.Time.UnixNano(), z.Time.UnixNano())
	assert.Equal(t, x.Values, z.Values)
}
