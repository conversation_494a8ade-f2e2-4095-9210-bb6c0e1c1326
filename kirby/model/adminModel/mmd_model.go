package adminModel

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/uuid"
)

// MeasureMetricDefinition 计量指标定义（MMD）
type MeasureMetricDefinition struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// UniqueKey MMD 的唯一内部标识
	UniqueKey string `gorm:"column:unique_key;type:VARCHAR(64);not null;unique_index:idx_mmd_unique_key;comment:'MMD 的唯一标识'"`
	// RTDCode 关联的 RTD 的唯一标识
	RTDCode string `gorm:"column:rtd_code;type:VARCHAR(64);not null;unique_index:idx_mmd_rtd_code_code;comment:'关联的 RTD 的唯一标识'"`
	// Code 计量指标 Code，用于推量
	Code string `gorm:"column:mm_code;type:VARCHAR(64);not null;unique_index:idx_mmd_rtd_code_code;comment:'计量指标 Code，用于推量'"`
	// Name 计量指标描述
	Name string `gorm:"column:name;type:VARCHAR(256);not null;comment:'计量指标描述'"`
	// G 原始计量粒度
	G Granularity `gorm:"column:g;type:tinyint(1);not null;comment:'值的类型'"`
	// Unit 原始计量单位
	Unit MeasureUnit `gorm:"column:unit;type:VARCHAR(16);not null;comment:'原始计量单位'"`

	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);default:now(6)"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME(6);default:now(6) ON UPDATE now(6)"`
}

type MeasureMetricDefinitionDetail struct {
	Code string
	Name string
	G    Granularity
	Unit MeasureUnit
}

func (x *MeasureMetricDefinition) ToDetail() *MeasureMetricDefinitionDetail {
	return &MeasureMetricDefinitionDetail{
		Code: x.Code,
		Name: x.Name,
		G:    x.G,
		Unit: x.Unit,
	}
}

func generateMMDUniqueKey(epoch time.Time) string {
	return "QKMMD" + uuid.NewWithEpoch(epoch)
}

func (x *MeasureMetricDefinitionDetail) toModel(
	uniqueKey string,
	rtdCode string,
	epoch time.Time,
) *MeasureMetricDefinition {
	return &MeasureMetricDefinition{
		UniqueKey: uniqueKey,
		RTDCode:   rtdCode,
		Code:      x.Code,
		Name:      x.Name,
		G:         x.G,
		Unit:      x.Unit,
		CreatedAt: epoch,
	}
}

// MeasureMetricDefinitionDao is data access object of MeasureMetricDefinition model
type MeasureMetricDefinitionDao struct {
	base *dao.BaseDao
}

// NewMeasureMetricDefinitionDao is constructor of MeasureMetricDefinitionDao
func NewMeasureMetricDefinitionDao(base *dao.BaseDao) *MeasureMetricDefinitionDao {
	return &MeasureMetricDefinitionDao{
		base: base,
	}
}

// Save inserts or updates a MeasureMetricDefinition by id
func (d *MeasureMetricDefinitionDao) Save(model *MeasureMetricDefinition) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Save(value).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

func (d *MeasureMetricDefinitionDao) BulkInsert(
	ctx context.Context,
	l []*MeasureMetricDefinition,
) error {
	return d.base.BulkInsert(ctx, l)
}

func (d *MeasureMetricDefinitionDao) ListByRTDCodes(
	rtdCodes []string,
	epoch time.Time,
) ([]*MeasureMetricDefinition, error) {
	var result []*MeasureMetricDefinition
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&MeasureMetricDefinition{}).
			Where("`rtd_code` IN (?) AND `created_at` <= ?", rtdCodes, epoch).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return result, nil
}
