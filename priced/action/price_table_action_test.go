package action_test

import (
	"context"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/pay-sdk/price"

	"github.com/stretchr/testify/assert"
)

func TestGetPriceItemByPriceID(t *testing.T) {
	sandbox := buildSandbox(t)

	effectTime := timestamppb.New(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local))
	deadTime := timestamppb.New(time.Date(2018, 1, 20, 0, 0, 0, 0, time.Local))
	priceClient := sandbox.priceClient

	priceTable := &price.PriceTable{
		Type:   price.VIP,
		Remark: "VIP 价格",
	}
	pt, err := priceClient.CreatePriceTable(context.Background(), priceTable)
	assert.NoError(t, err)
	priceItems := []price.PriceItem{
		{
			ItemId:     1,
			PriceId:    pt.Id,
			ZoneId:     111,
			EffectTime: effectTime,
			DeadTime:   deadTime,
		},
	}
	for _, item := range priceItems {
		_, err := priceClient.CreatePriceItem(context.Background(), &item)
		assert.NoError(t, err)
	}

	endTime := timestamppb.New(time.Date(2018, 2, 1, 0, 0, 0, 0, time.Local))
	priceItemList, err := priceClient.GetItemPriceWithPriceTableID(context.Background(), &price.ItemPriceWithPriceTableIDParam{
		ItemId:       1,
		PriceTableId: pt.Id,
		ZoneId:       111,
		StartTime:    effectTime,
		EndTime:      endTime,
	})
	if assert.NoError(t, err) {
		assert.Equal(t, 1, len(priceItemList.PriceItems))
		item := priceItemList.PriceItems[0]
		assert.Equal(t, uint64(1), item.ItemId)
		assert.Equal(t, uint64(111), item.ZoneId)
		assert.Equal(t, effectTime.Seconds, item.EffectTime.Seconds)
		assert.Equal(t, endTime.Seconds, item.DeadTime.Seconds)
	}
}

func TestPriceTableAction(t *testing.T) {
	sandbox := buildSandbox(t)

	priceClient := sandbox.priceClient

	priceTables := []price.PriceTable{
		{
			Type:   price.DEFAULT,
			Remark: "公开报价",
		},
		{
			Type:   price.VIP,
			Remark: "VIP 价格1",
		},
		{
			Type:   price.FINAL,
			Remark: "最终价格信息",
		},
	}

	assertFields := func(expect, actual *price.PriceTable, msgAndArgs ...any) {
		assert.Equal(t, expect.Type, actual.Type, msgAndArgs...)
		assert.Equal(t, expect.Remark, actual.Remark, msgAndArgs...)
	}

	for i, priceTable := range priceTables {
		// create
		m, err := priceClient.CreatePriceTable(context.Background(), &priceTable)
		if assert.NoError(t, err) {
			assert.NotZero(t, m.Id, "CreatePriceTable return zero id")
			assertFields(&priceTable, m)
		}
		priceTables[i].Id = m.Id

		// query
		priceTableId := m.Id
		m, err = priceClient.GetPriceTableByID(context.Background(), &price.IDParam{Id: priceTableId})
		if assert.NoError(t, err) {
			assertFields(&priceTable, m)
		}

		// update
		m.Remark = "七牛价格信息"
		updatedPriceTable, err := priceClient.UpdatePriceTableByID(context.Background(), &price.IDPriceTableParam{
			Id:         priceTableId,
			PriceTable: m,
		})
		if assert.NoError(t, err) {
			assertFields(m, updatedPriceTable)
			priceTables[i].Remark = m.Remark
		}
	}

	// list all price items
	list, err := priceClient.ListAllPriceTables(context.Background(), &price.PagingParam{
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Len(t, priceTables, int(list.GetCount()))
		for _, item := range list.GetPriceTables() {
			for _, expectedItem := range priceTables {
				if item.Id == expectedItem.Id {
					assertFields(&expectedItem, item)
					break
				}
			}
		}
	}

	// delete all price items
	for _, priceTable := range priceTables {
		deletedPriceTable, err := priceClient.DeletePriceTableByID(context.Background(), &price.IDParam{Id: priceTable.Id})
		if assert.NoError(t, err) {
			assertFields(deletedPriceTable, &priceTable)
		}
	}

	// check price item list, should be empty
	list, err = priceClient.ListAllPriceTables(context.Background(), &price.PagingParam{
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Zero(t, list.GetCount())
		assert.Empty(t, list.GetPriceTables())
	}

	// de-duplication
	for _, priceTable := range priceTables {
		_, err := priceClient.DeletePriceTableByID(context.Background(), &price.IDParam{Id: priceTable.Id})
		assert.Error(t, err)
	}
}
