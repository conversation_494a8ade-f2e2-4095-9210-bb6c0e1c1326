CREATE TABLE IF NOT EXISTS `{{ .TableName }}` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `qrn` varchar(512) NOT NULL COMMENT '该序列对应资源的 QRN',
  `mm` varchar(64) NOT NULL COMMENT '该序列的计量指标 code',
  `sp` text NOT NULL COMMENT '该序列的属性',
  `start` datetime(6) NOT NULL COMMENT '该序列计量的起始时刻（含）',
  `end` datetime(6) NOT NULL COMMENT '该序列计量的结束时刻（不含）',
  `mb_uuid` varchar(64) NOT NULL COMMENT '推来计量包的 UUID，用于服务端去重',
  `uid` bigint(20) unsigned NOT NULL COMMENT '从 QRN 中解出的 UID',
  `sp_hash` int(10) unsigned NOT NULL COMMENT '由 SP 得到的一致性 hash 值，用于高效的复合查询',
  `rt` varchar(64) NOT NULL COMMENT '资源类型 code',
  `aggr` varchar(64) NOT NULL COMMENT '该序列匹配到的聚合规则 code',
  `measure_unit` varchar(16) NOT NULL COMMENT '该序列数据的计量单位',
  `g` tinyint(1) NOT NULL COMMENT '该序列数据的粒度',
  `series_key` varchar(64) NOT NULL COMMENT '该序列的数据的检索 key',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '记录的创建时刻',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_{{ .TableName }}_sk` (`series_key`),
  KEY `idx_{{ .TableName }}_ctime_sk` (`created_at`, `series_key`),
  KEY `idx_{{ .TableName }}_qrn_sk` (`qrn`, `series_key`),
  KEY `idx_{{ .TableName }}_uid_sk` (`uid`, `series_key`),
  KEY `idx_{{ .TableName }}_mb_uuid_sk` (`mb_uuid`, `series_key`)
)
  ENGINE=InnoDB
  ROW_FORMAT=COMPRESSED
  KEY_BLOCK_SIZE=8
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci;
