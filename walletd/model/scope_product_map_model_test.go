package model_test

import (
	"context"
	"testing"

	"github.com/qbox/bo-base/v4/base"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/payment/dao"
	qpayModel "qiniu.io/pay/qpay/wallet/model"
	"qiniu.io/pay/walletd/model"
)

// 测试当一个产品线和一个 scopeID 一一对应的情况
func TestScopeProductMapDao_ListScopeIDByProductIDs_0(t *testing.T) {
	sandbox := buildSandbox(t)

	var i uint64
	for i = 0; i < 5; i++ {
		scopeProductMap := model.ScopeProductMap{
			ProductID: i,
			ScopeID:   1000 + i,
		}
		err := sandbox.walletDao.ScopeProductMap.Save(&scopeProductMap)
		assert.NoError(t, err, "Save ScopeProductMap error %+v", err)
	}

	productIDs := []uint64{0, 1, 2, 3, 4}
	scopeIDs, err := sandbox.walletDao.ScopeProductMap.ListScopeIDByProductIDs(productIDs)
	assert.NoError(t, err, "Get scope id list failed")
	assert.Equal(t, []uint64{1000, 1001, 1002, 1003, 1004}, scopeIDs)
}

// 测试当一个产品线对应多个 scopeID 的情况, 需要去重
func TestScopeProductMapDao_ListScopeIDByProductIDs_1(t *testing.T) {
	sandbox := buildSandbox(t)

	var i uint64
	for i = 0; i < 3; i++ {
		scopeProductMap := &model.ScopeProductMap{
			ProductID: i,
			ScopeID:   1000,
		}
		err := sandbox.walletDao.ScopeProductMap.Save(scopeProductMap)
		assert.NoError(t, err, "Save ScopeProductMap error")
	}

	productIDs := []uint64{0, 1, 2}
	scopeIDs, err := sandbox.walletDao.ScopeProductMap.ListScopeIDByProductIDs(productIDs)
	assert.NoError(t, err, "Get scope id list failed")
	assert.Equal(t, []uint64{1000}, scopeIDs)
}

// 测试产品线和 scopeID 多对多的情况
func TestScopeProductMapDao_ListScopeIDByProductIDs_2(t *testing.T) {
	sandbox := buildSandbox(t)

	var i uint64
	for i = 0; i < 3; i++ {
		scopeProductMap := &model.ScopeProductMap{
			ProductID: i,
			ScopeID:   1000 + i,
		}
		err := sandbox.walletDao.ScopeProductMap.Save(scopeProductMap)
		assert.NoError(t, err, "Save ScopeProductMap error")
	}
	for i = 0; i < 4; i++ {
		scopeProductMap := &model.ScopeProductMap{
			ProductID: i,
			ScopeID:   1001 + i,
		}
		err := sandbox.walletDao.ScopeProductMap.Save(scopeProductMap)
		assert.NoError(t, err, "Save ScopeProductMap error")
	}

	productIDs := []uint64{0, 1, 2, 3}
	scopeIDs, err := sandbox.walletDao.ScopeProductMap.ListScopeIDByProductIDs(productIDs)
	assert.NoError(t, err, "Get scope id list failed")
	assert.Equal(t, []uint64{1000, 1001, 1002, 1003, 1004}, scopeIDs)

	productIDs = []uint64{2, 3}
	scopeIDs, err = sandbox.walletDao.ScopeProductMap.ListScopeIDByProductIDs(productIDs)
	assert.NoError(t, err, "Get scope id list failed")
	assert.Equal(t, []uint64{1002, 1003, 1004}, scopeIDs)
}

// 测试有记录不存在的情况，应该返回空数组而非 nil
func TestScopeProductMapDao_ListScopeIDByProductIDs_3(t *testing.T) {
	sandbox := buildSandbox(t)

	productIDs := []uint64{100, 101}
	scopeIDs, err := sandbox.walletDao.ScopeProductMap.ListScopeIDByProductIDs(productIDs)
	assert.NoError(t, err)
	assert.Equal(t, []uint64{}, scopeIDs, "ScopeID array should be empty instead of nil")

	// 部分 productID 找不到对应 scopeID 的情况
	scopeProductMap := &model.ScopeProductMap{
		ProductID: 100,
		ScopeID:   9999,
	}
	err = sandbox.walletDao.ScopeProductMap.Save(scopeProductMap)
	assert.NoError(t, err, "Save ScopeProductMap error")

	productIDs = []uint64{100, 102}
	scopeIDs, err = sandbox.walletDao.ScopeProductMap.ListScopeIDByProductIDs(productIDs)
	assert.NoError(t, err)
	assert.Equal(t, []uint64{9999}, scopeIDs)
}

// 当 productID 和 scopeID 一对一的时候
func TestScopeProductMapDao_CalculateUserProductVoucherQuota_0(t *testing.T) {
	sandbox := dao.BuildSandbox(t)
	var i uint64
	for i = 0; i < 5; i++ {
		scopeProductMap := model.ScopeProductMap{
			ProductID: i,
			ScopeID:   1000 + i,
		}
		err := sandbox.QPayWalletDAO.ScopeProductMap.Save(&scopeProductMap)
		assert.NoError(t, err, "Save ScopeProductMap error %+v", err)
	}

	ctx := context.Background()
	var voucherCodes []string
	for i = 0; i < 5; i++ {
		voucherBase := qpayModel.VoucherBase{
			UID:       sandbox.UID,
			Quota:     base.Money((i + 1) * 10000),
			ScopeID:   1000 + i,
			AssetType: enums.AssetTypeCNYVoucher,
		}
		voucher := qpayModel.Voucher{
			VoucherBase: voucherBase,
		}
		insertedVoucher, err := sandbox.VoucherDAO.InsertVoucher(ctx, &voucher)
		assert.NoError(t, err, "inserted voucher should not have error: %+v", err)
		voucherCodes = append(voucherCodes, insertedVoucher.Code)
	}
	product, err := sandbox.QPayWalletDAO.ScopeProductMap.CalculateUserProductVoucherQuota(sandbox.UID, voucherCodes, []uint64{0, 1, 2})
	assert.NoError(t, err)
	assert.EqualValues(t, model.ProductIDVoucherQuotaList{
		{
			ProductID: 0,
			Quota:     10000,
			AssetType: enums.AssetTypeCNYVoucher,
		},
		{
			ProductID: 1,
			Quota:     20000,
			AssetType: enums.AssetTypeCNYVoucher,
		},
		{
			ProductID: 2,
			Quota:     30000,
			AssetType: enums.AssetTypeCNYVoucher,
		},
	}, product)
}

// 当 productID 和 scopeID 多对一的时候
func TestScopeProductMapDao_CalculateUserProductVoucherQuota_1(t *testing.T) {
	sandbox := dao.BuildSandbox(t)
	var i uint64
	for i = 0; i < 5; i++ {
		scopeProductMap := model.ScopeProductMap{
			ProductID: i,
			ScopeID:   1000,
		}
		err := sandbox.QPayWalletDAO.ScopeProductMap.Save(&scopeProductMap)
		assert.NoError(t, err, "Save ScopeProductMap error %+v", err)
	}

	ctx := context.Background()
	var voucherCodes []string
	for i = 0; i < 5; i++ {
		voucherBase := qpayModel.VoucherBase{
			UID:       sandbox.UID,
			Quota:     base.Money((i + 1) * 10000),
			ScopeID:   1000,
			AssetType: enums.AssetTypeUSDVoucher,
		}
		voucher := qpayModel.Voucher{
			VoucherBase: voucherBase,
		}
		insertedVoucher, err := sandbox.VoucherDAO.InsertVoucher(ctx, &voucher)
		assert.NoError(t, err, "inserted voucher should not have error: %+v", err)
		voucherCodes = append(voucherCodes, insertedVoucher.Code)
	}
	product, err := sandbox.QPayWalletDAO.ScopeProductMap.CalculateUserProductVoucherQuota(sandbox.UID, voucherCodes, []uint64{0, 1, 2})
	assert.NoError(t, err)
	// 应该都是 10000 + 20000 + 30000 + 40000 + 50000 = 150000
	assert.EqualValues(t, model.ProductIDVoucherQuotaList{
		{
			ProductID: 0,
			Quota:     150000,
			AssetType: enums.AssetTypeUSDVoucher,
		},
		{
			ProductID: 1,
			Quota:     150000,
			AssetType: enums.AssetTypeUSDVoucher,
		},
		{
			ProductID: 2,
			Quota:     150000,
			AssetType: enums.AssetTypeUSDVoucher,
		},
	}, product)
}

// 当 productID 和 scopeID 多对多的时候
func TestScopeProductMapDao_CalculateUserProductVoucherQuota_2(t *testing.T) {
	sandbox := dao.BuildSandbox(t)
	var i uint64
	for i = 0; i < 3; i++ {
		scopeProductMap := &model.ScopeProductMap{
			ProductID: i,
			ScopeID:   1000 + i,
		}
		err := sandbox.QPayWalletDAO.ScopeProductMap.Save(scopeProductMap)
		assert.NoError(t, err, "Save ScopeProductMap error")
	}
	for i = 0; i < 4; i++ {
		scopeProductMap := &model.ScopeProductMap{
			ProductID: i,
			ScopeID:   1001 + i,
		}
		err := sandbox.QPayWalletDAO.ScopeProductMap.Save(scopeProductMap)
		assert.NoError(t, err, "Save ScopeProductMap error")
	}

	ctx := context.Background()
	var voucherCodes []string
	for i = 0; i < 5; i++ {
		voucherBase := qpayModel.VoucherBase{
			UID:       sandbox.UID,
			Quota:     base.Money((i + 1) * 10000),
			ScopeID:   1000 + i,
			AssetType: enums.AssetTypeCNYVoucher,
		}
		voucher := qpayModel.Voucher{
			VoucherBase: voucherBase,
		}
		insertedVoucher, err := sandbox.VoucherDAO.InsertVoucher(ctx, &voucher)
		assert.NoError(t, err, "inserted voucher should not have error: %+v", err)
		voucherCodes = append(voucherCodes, insertedVoucher.Code)
	}
	product, err := sandbox.QPayWalletDAO.ScopeProductMap.CalculateUserProductVoucherQuota(sandbox.UID, voucherCodes, []uint64{0, 1, 2, 3})
	assert.NoError(t, err)

	assert.EqualValues(t, model.ProductIDVoucherQuotaList{
		{
			ProductID: 0,
			Quota:     30000,
			AssetType: enums.AssetTypeCNYVoucher,
		},
		{
			ProductID: 1,
			Quota:     50000,
			AssetType: enums.AssetTypeCNYVoucher,
		},
		{
			ProductID: 2,
			Quota:     70000,
			AssetType: enums.AssetTypeCNYVoucher,
		},
		{
			ProductID: 3,
			Quota:     50000,
			AssetType: enums.AssetTypeCNYVoucher,
		},
	}, product)
}
