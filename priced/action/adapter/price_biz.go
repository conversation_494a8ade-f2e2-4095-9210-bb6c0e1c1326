package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"

	"qiniu.io/pay/priced/model"
	"qiniu.io/pay/priced/service"
)

// BuildPbStairPrice 转换 service.StairPrice 为 proto.StairPriceParam
func BuildPbStairPrice(stairPrice *service.StairPrice) (*pb.StairPriceParam, error) {

	priceI64, err := stairPrice.Price.GetBigMoneyI64()
	if err != nil {
		return nil, err
	}

	return &pb.StairPriceParam{
		UnitId:   stairPrice.UnitID,
		Quantity: stairPrice.Quantity,
		Price:    uint64(priceI64),
	}, nil
}

// BuildStairPrice 转换 proto.StairPriceParam 为 service.StairPrice
func BuildStairPrice(stairPrice *pb.StairPriceParam) (*service.StairPrice, error) {
	return &service.StairPrice{
		UnitID:   stairPrice.UnitId,
		Quantity: stairPrice.Quantity,
		Price:    base.NewNMoneyWithHighAccuracyI64(int64(stairPrice.Price)),
	}, nil
}

// BuildPbItemPriceParam 转换 service.ItemPriceParam 为 proto.ItemPriceParam
func BuildPbItemPriceParam(itemPriceParam *service.ItemPriceParam) (*pb.ItemPriceParam, error) {
	stairPrices := make([]*pb.StairPriceParam, len(itemPriceParam.StairPrices))
	for i, stairPrice := range itemPriceParam.StairPrices {
		pbStairPrice, err := BuildPbStairPrice(&stairPrice)
		if err != nil {
			return nil, errors.Trace(err)
		}
		stairPrices[i] = pbStairPrice
	}

	return &pb.ItemPriceParam{
		ItemId:         itemPriceParam.ItemID,
		ZoneId:         itemPriceParam.ZoneID,
		AlgorithmId:    itemPriceParam.AlgorithmID,
		StairPriceType: itemPriceParam.StairPriceType,
		IsDisabled:     itemPriceParam.IsDisabled,
		UnitRate:       itemPriceParam.UnitRate,
		CurrencyType:   itemPriceParam.CurrencyType.String(),
		StairPrices:    stairPrices,
		EffectTime:     timestamppb.New(itemPriceParam.EffectTime.TimeIn(time.UTC)),
		DeadTime:       timestamppb.New(itemPriceParam.DeadTime.TimeIn(time.UTC)),
	}, nil
}

// BuildItemPriceParam 转换 proto.ItemPriceParam 为 service.ItemPriceParam
func BuildItemPriceParam(itemPriceParam *pb.ItemPriceParam) (*service.ItemPriceParam, error) {
	err := itemPriceParam.GetEffectTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	effectTime := itemPriceParam.GetEffectTime().AsTime()

	err = itemPriceParam.GetDeadTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	deadTime := itemPriceParam.GetDeadTime().AsTime()

	stairPrices := make([]service.StairPrice, len(itemPriceParam.StairPrices))
	for i, stairPrice := range itemPriceParam.StairPrices {
		s, err := BuildStairPrice(stairPrice)
		if err != nil {
			return nil, errors.Trace(err)
		}
		stairPrices[i] = *s
	}

	return &service.ItemPriceParam{
		ItemID:             itemPriceParam.ItemId,
		ZoneID:             itemPriceParam.ZoneId,
		AlgorithmID:        itemPriceParam.AlgorithmId,
		IsDefaultAlgorithm: itemPriceParam.GetIsDefaultAlgorithm(),
		StairPriceType:     itemPriceParam.StairPriceType,
		IsDisabled:         itemPriceParam.IsDisabled,
		UnitRate:           itemPriceParam.UnitRate,
		CurrencyType:       base.CurrencyType(itemPriceParam.CurrencyType),
		StairPrices:        stairPrices,
		EffectTime:         base.NewHNS(effectTime),
		DeadTime:           base.NewHNS(deadTime),
		CumulativeCycle:    model.CumulativeType(itemPriceParam.CumulativeCycle),
		BillPeriodType:     model.BillPeriodType(itemPriceParam.BillPeriodType),
	}, nil
}

// BuildPbPricesParam 转换 service.PricesParam 为 proto.PricesParam
func BuildPbPricesParam(pricesParam *service.PricesParam) (*pb.PricesParam, error) {
	itemPrices := make([]*pb.ItemPriceParam, len(pricesParam.ItemPrices))
	for i, itemPriceParam := range pricesParam.ItemPrices {
		s, err := BuildPbItemPriceParam(&itemPriceParam)
		if err != nil {
			return nil, errors.Trace(err)
		}

		itemPrices[i] = s
	}

	return &pb.PricesParam{
		Type:       BuildPbPriceType(pricesParam.Type),
		Remark:     pricesParam.Remark,
		ItemPrices: itemPrices,
	}, nil
}

// BuildPricesParam 转换 proto.PricesParam 为 service.PricesParam
func BuildPricesParam(pricesParam *pb.PricesParam) (*service.PricesParam, error) {
	itemPrices := make([]service.ItemPriceParam, len(pricesParam.ItemPrices))
	for i, itemPriceParam := range pricesParam.ItemPrices {
		s, err := BuildItemPriceParam(itemPriceParam)
		if err != nil {
			return nil, errors.Trace(err)
		}

		s.Type = BuildPriceTableType(pricesParam.Type)
		itemPrices[i] = *s
	}

	return &service.PricesParam{
		Type:       BuildPriceTableType(pricesParam.Type),
		Remark:     pricesParam.Remark,
		ItemPrices: itemPrices,
	}, nil
}

// BuildPbUserPriceParam 转换 service.UserPriceTableParam 为 proto.UserPriceParam
func BuildPbUserPriceParam(userPrices *service.UserPriceTableParam) (*pb.UserPriceParam, error) {
	pricesParam, err := BuildPbPricesParam(&userPrices.Prices)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.UserPriceParam{
		Uid:        userPrices.UID,
		Prices:     pricesParam,
		IsDisabled: userPrices.IsDisabled,
	}, nil
}

// BuildUserPricesParam 转换 proto.UserPriceParam 为 service.UserPriceTableParam
func BuildUserPricesParam(userPrices *pb.UserPriceParam) (*service.UserPriceTableParam, error) {
	pricesParam, err := BuildPricesParam(userPrices.Prices)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &service.UserPriceTableParam{
		UID:        userPrices.Uid,
		Prices:     *pricesParam,
		IsDisabled: userPrices.IsDisabled,
	}, nil
}

// BuildPbItemPriceWithTimePointParam 转换 service.ItemPriceWithTimePointParam 为 proto.ItemPriceWithTimePointParam
func BuildPbItemPriceWithTimePointParam(param *service.ItemPriceWithTimePointParam) (*pb.ItemPriceWithTimePointParam, error) {
	return &pb.ItemPriceWithTimePointParam{
		Uid:    param.UID,
		ItemId: param.ItemID,
		ZoneId: param.ZoneID,
		At:     timestamppb.New(param.At.TimeIn(time.UTC)),
	}, nil
}

// BuildItemPriceWithTimePointParam 转换 proto.ItemPriceWithTimePointParam 为 service.ItemPriceWithTimePointParam
func BuildItemPriceWithTimePointParam(param *pb.ItemPriceWithTimePointParam) (*service.ItemPriceWithTimePointParam, error) {
	err := param.GetAt().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	at := param.GetAt().AsTime()

	return &service.ItemPriceWithTimePointParam{
		UID:    param.GetUid(),
		ItemID: param.GetItemId(),
		ZoneID: param.GetZoneId(),
		At:     base.NewHNS(at),
	}, nil
}

// BuildPbItemPriceWithTimeRangeParam 转换 service.ItemPriceWithTimeRangeParam 为 proto.ItemPriceWithTimeRangeParam
func BuildPbItemPriceWithTimeRangeParam(param *service.ItemPriceWithTimeRangeParam) (*pb.ItemPriceWithTimeRangeParam, error) {
	return &pb.ItemPriceWithTimeRangeParam{
		Uid:       param.UID,
		ItemId:    param.ItemID,
		ZoneId:    param.ZoneID,
		StartTime: timestamppb.New(param.StartTime.TimeIn(time.UTC)),
		EndTime:   timestamppb.New(param.EndTime.TimeIn(time.UTC)),
	}, nil
}

// BuildItemPriceWithTimeRangeParam 转换 proto.ItemPriceWithTimeRangeParam 为 service.ItemPriceWithTimeRangeParam
func BuildItemPriceWithTimeRangeParam(param *pb.ItemPriceWithTimeRangeParam) (*service.ItemPriceWithTimeRangeParam, error) {
	err := param.GetStartTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	startTime := param.GetStartTime().AsTime()

	err = param.GetEndTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	endTime := param.GetEndTime().AsTime()

	return &service.ItemPriceWithTimeRangeParam{
		UID:       param.GetUid(),
		ItemID:    param.GetItemId(),
		ZoneID:    param.GetZoneId(),
		StartTime: base.NewHNS(startTime),
		EndTime:   base.NewHNS(endTime),
	}, nil
}

// BuildPbBatchItemPricesWithTimeRangeParam 转换 service.BatchItemPricesWithTimeRangeParam 为 proto.BatchItemPricesWithTimeRangeParam
func BuildPbBatchItemPricesWithTimeRangeParam(param *service.BatchItemPricesWithTimeRangeParam) (*pb.BatchItemPricesWithTimeRangeParam, error) {
	return &pb.BatchItemPricesWithTimeRangeParam{
		Uid:       param.UID,
		ItemIds:   param.ItemIDs,
		ZoneId:    param.ZoneID,
		StartTime: timestamppb.New(param.StartTime.TimeIn(time.UTC)),
		EndTime:   timestamppb.New(param.EndTime.TimeIn(time.UTC)),
	}, nil
}

// BuildBatchItemPricesWithTimeRangeParam 转换 proto.BatchItemPricesWithTimeRangeParam 为 service.BatchItemPricesWithTimeRangeParam
func BuildBatchItemPricesWithTimeRangeParam(param *pb.BatchItemPricesWithTimeRangeParam) (*service.BatchItemPricesWithTimeRangeParam, error) {
	err := param.GetStartTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	startTime := param.GetStartTime().AsTime()

	err = param.GetEndTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	endTime := param.GetEndTime().AsTime()

	return &service.BatchItemPricesWithTimeRangeParam{
		UID:       param.GetUid(),
		ItemIDs:   param.GetItemIds(),
		ZoneID:    param.GetZoneId(),
		StartTime: base.NewHNS(startTime),
		EndTime:   base.NewHNS(endTime),
	}, nil
}

// BuildItemPriceWithPriceTableIDParam 转换 proto.ItemPriceWithPriceTableID 为 service.ItemPriceWithTimeRangeParam
func BuildItemPriceWithPriceTableIDParam(param *pb.ItemPriceWithPriceTableIDParam) (*service.ItemPriceWithTimeRangeParam, error) {
	err := param.GetStartTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	startTime := param.GetStartTime().AsTime()

	err = param.GetEndTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	endTime := param.GetEndTime().AsTime()

	return &service.ItemPriceWithTimeRangeParam{
		UID:          param.GetUid(),
		ItemID:       param.GetItemId(),
		ZoneID:       param.GetZoneId(),
		PriceTableID: param.GetPriceTableId(),
		StartTime:    base.NewHNS(startTime),
		EndTime:      base.NewHNS(endTime),
	}, nil
}

// BuildPbUserPriceWithTimePointParam 转换 service.UserPriceWithTimePointParam 为 proto.UserPriceWithTimePointParam
func BuildPbUserPriceWithTimePointParam(param *service.UserPriceWithTimePointParam) (*pb.UserPriceWithTimePointParam, error) {
	return &pb.UserPriceWithTimePointParam{
		Uid:    param.UID,
		ZoneId: param.ZoneID,
		At:     timestamppb.New(param.At.TimeIn(time.UTC)),
	}, nil
}

// BuildUserPriceWithTimePointParam 转换 proto.UserPriceWithTimePointParam 为 service.UserPriceWithTimePointParam
func BuildUserPriceWithTimePointParam(param *pb.UserPriceWithTimePointParam) (*service.UserPriceWithTimePointParam, error) {
	err := param.GetAt().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	at := param.GetAt().AsTime()

	return &service.UserPriceWithTimePointParam{
		UID:    param.GetUid(),
		ZoneID: param.GetZoneId(),
		At:     base.NewHNS(at),
	}, nil
}

// BuildPbUserPriceWithTimePointResp 转换 service.UserPriceWithTimePointResp 为 proto.UserPriceWithTimePointResp
func BuildPbUserPriceWithTimePointResp(param *service.UserPriceWithTimePointResp) (*pb.UserPriceWithTimePointResp, error) {
	itemPrices := make(map[uint64]*pb.PriceItem, len(param.ItemPrices))
	for itemID, priceItem := range param.ItemPrices {
		s, err := BuildPbPriceItem(&priceItem)
		if err != nil {
			return nil, errors.Trace(err)
		}

		itemPrices[itemID] = s
	}
	return &pb.UserPriceWithTimePointResp{
		ItemPrices: itemPrices,
	}, nil
}

// BuildUserPriceWithTimePointResp 转换 proto.UserPriceWithTimePointResp 为 service.UserPriceWithTimePointResp
func BuildUserPriceWithTimePointResp(param *pb.UserPriceWithTimePointResp) (*service.UserPriceWithTimePointResp, error) {
	itemPrices := make(map[uint64]model.PriceItem, len(param.ItemPrices))
	for itemID, priceItem := range param.ItemPrices {
		s, err := BuildPriceItem(priceItem)
		if err != nil {
			return nil, errors.Trace(err)
		}

		itemPrices[itemID] = *s
	}
	return &service.UserPriceWithTimePointResp{
		ItemPrices: itemPrices,
	}, nil
}

// BuildPbUserPriceWithTimeRangeParam 转换 service.UserPriceWithTimeRangeParam 为 proto.UserPriceWithTimeRangeParam
func BuildPbUserPriceWithTimeRangeParam(param *service.UserPriceWithTimeRangeParam) (*pb.UserPriceWithTimeRangeParam, error) {
	return &pb.UserPriceWithTimeRangeParam{
		Uid:       param.UID,
		ZoneId:    param.ZoneID,
		StartTime: timestamppb.New(param.StartTime.TimeIn(time.UTC)),
		EndTime:   timestamppb.New(param.EndTime.TimeIn(time.UTC)),
	}, nil
}

// BuildUserPriceWithTimeRangeParam 转换 proto.UserPriceWithTimeRangeParam 为 service.UserPriceWithTimeRangeParam
func BuildUserPriceWithTimeRangeParam(param *pb.UserPriceWithTimeRangeParam) (*service.UserPriceWithTimeRangeParam, error) {
	err := param.GetStartTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	startTime := param.GetStartTime().AsTime()

	err = param.GetEndTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	endTime := param.GetEndTime().AsTime()

	return &service.UserPriceWithTimeRangeParam{
		UID:       param.GetUid(),
		ZoneID:    param.GetZoneId(),
		StartTime: base.NewHNS(startTime),
		EndTime:   base.NewHNS(endTime),
	}, nil
}

// BuildPbUserPriceWithTimeRangeResp 转换 service.UserPriceWithTimeRangeResp 为 proto.UserPriceWithTimeRangeResp
func BuildPbUserPriceWithTimeRangeResp(param *service.UserPriceWithTimeRangeResp) (*pb.UserPriceWithTimeRangeResp, error) {
	itemPrices := make(map[uint64]*pb.PriceItemList, len(param.ItemPrices))
	for itemID, priceItemList := range param.ItemPrices {
		l := &pb.PriceItemList{
			PriceItems: make([]*pb.PriceItem, len(priceItemList)),
			Count:      uint64(len(priceItemList)),
		}

		for i, priceItem := range priceItemList {
			s, err := BuildPbPriceItem(&priceItem)
			if err != nil {
				return nil, errors.Trace(err)
			}

			l.PriceItems[i] = s
		}
		itemPrices[itemID] = l
	}

	return &pb.UserPriceWithTimeRangeResp{
		ItemPrices: itemPrices,
	}, nil
}

// BuildUserPriceWithTimeRangeResp 转换 proto.UserPriceWithTimeRangeResp 为 service.UserPriceWithTimeRangeResp
func BuildUserPriceWithTimeRangeResp(param *pb.UserPriceWithTimeRangeResp) (*service.UserPriceWithTimeRangeResp, error) {
	itemPrices := make(map[uint64][]model.PriceItem, len(param.ItemPrices))
	for itemID, priceItemList := range param.ItemPrices {
		l := make([]model.PriceItem, len(priceItemList.PriceItems))

		for i, priceItem := range priceItemList.PriceItems {
			s, err := BuildPriceItem(priceItem)
			if err != nil {
				return nil, errors.Trace(err)
			}

			l[i] = *s
		}

		itemPrices[itemID] = l
	}

	return &service.UserPriceWithTimeRangeResp{
		ItemPrices: itemPrices,
	}, nil
}

// BuildPbPriceTableLifecycleChangeParam 转换 service.PriceTableLifecycleChangeParam 为 proto.PriceTableLifecycleChangeParam
func BuildPbPriceTableLifecycleChangeParam(
	x *service.PriceTableLifecycleChangeParam,
) (*pb.PriceTableLifecycleChangeParam, error) {
	return &pb.PriceTableLifecycleChangeParam{
		Uid:          x.UID,
		PriceTableId: x.PriceTableID,
		EffectTime:   timestamppb.New(x.EffectTime.TimeIn(time.UTC)),
		DeadTime:     timestamppb.New(x.DeadTime.TimeIn(time.UTC)),
	}, nil
}

// BuildPriceTableLifecycleChangeParam 转换 proto.PriceTableLifecycleChangeParam 为 service.PriceTableLifecycleChangeParam
func BuildPriceTableLifecycleChangeParam(
	x *pb.PriceTableLifecycleChangeParam,
) (*service.PriceTableLifecycleChangeParam, error) {
	err := x.GetEffectTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	effectTime := x.GetEffectTime().AsTime()

	err = x.GetDeadTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	deadTime := x.GetDeadTime().AsTime()

	return &service.PriceTableLifecycleChangeParam{
		UID:          x.GetUid(),
		PriceTableID: x.GetPriceTableId(),
		EffectTime:   base.NewHNS(effectTime),
		DeadTime:     base.NewHNS(deadTime),
	}, nil
}

// BuildPbPriceInfluence 转换 service.PriceInfluence 为 proto.PriceInfluence
func BuildPbPriceInfluence(x *service.PriceInfluence) (*pb.PriceInfluence, error) {
	before, err := BuildPbUserPriceWithTimeRangeResp(&x.Before)
	if err != nil {
		return nil, err
	}

	after, err := BuildPbUserPriceWithTimeRangeResp(&x.After)
	if err != nil {
		return nil, err
	}
	return &pb.PriceInfluence{
		Before: before,
		After:  after,
	}, nil
}

// BuildPriceInfluence 转换 proto.PriceInfluce 为 service.PriceInfluence
func BuildPriceInfluence(x *pb.PriceInfluence) (*service.PriceInfluence, error) {
	before, err := BuildUserPriceWithTimeRangeResp(x.Before)
	if err != nil {
		return nil, err
	}
	after, err := BuildUserPriceWithTimeRangeResp(x.After)
	if err != nil {
		return nil, err
	}

	return &service.PriceInfluence{
		Before: *before,
		After:  *after,
	}, nil
}

// BuildPbUpdateStairPriceParam 转换 service.UpdateStairPriceParam 为 proto.UpdateStairPriceParam
func BuildPbUpdateStairPriceParam(
	x *service.UpdateStairPriceParam,
) (*pb.UpdateStairPriceParam, error) {
	i64, err := x.Price.GetBigMoneyI64()
	if err != nil {
		return nil, err
	}
	return &pb.UpdateStairPriceParam{
		Quantity: x.Quantity,
		Price:    uint64(i64),
	}, nil
}

// BuildUpdateStairPriceParam 转换 proto.UpdateStairPriceParamParam 为 service.UpdateStairPriceParam
func BuildUpdateStairPriceParam(x *pb.UpdateStairPriceParam) *service.UpdateStairPriceParam {

	return &service.UpdateStairPriceParam{
		Quantity: x.Quantity,
		Price:    base.NewNMoneyWithHighAccuracyI64(int64(x.Price)),
	}
}

// BuildPbUpdateItemPriceParam 转换 service.UpdateItemPriceParam 为 proto.UpdateItemPriceParam
func BuildPbUpdateItemPriceParam(
	x *service.UpdateItemPriceParam,
) (*pb.UpdateItemPriceParam, error) {
	stairPrices := make([]*pb.UpdateStairPriceParam, 0, len(x.StairPrices))
	for _, stairPrice := range x.StairPrices {
		stair, err := BuildPbUpdateStairPriceParam(&stairPrice)
		if err != nil {
			return nil, err
		}
		stairPrices = append(stairPrices, stair)
	}

	return &pb.UpdateItemPriceParam{
		ItemCode:     x.ItemCode,
		ZoneCode:     x.ZoneCode,
		DataType:     x.DataType,
		StairPrices:  stairPrices,
		EffectTime:   timestamppb.New(x.EffectTime.TimeIn(time.UTC)),
		DeadTime:     timestamppb.New(x.DeadTime.TimeIn(time.UTC)),
		CurrencyType: x.CurrencyType.String(),
	}, nil
}

// BuildUpdateItemPriceParam 转换 proto.UpdateItemPriceParam 为 service.UpdateItemPriceParam
func BuildUpdateItemPriceParam(
	x *pb.UpdateItemPriceParam,
) (*service.UpdateItemPriceParam, error) {
	err := x.GetEffectTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	effectTime := x.GetEffectTime().AsTime()

	err = x.GetDeadTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	deadTime := x.GetDeadTime().AsTime()

	stairPrices := make([]service.UpdateStairPriceParam, 0, len(x.StairPrices))
	for _, stairPrice := range x.StairPrices {
		p := BuildUpdateStairPriceParam(stairPrice)
		stairPrices = append(stairPrices, *p)
	}

	return &service.UpdateItemPriceParam{
		ItemCode:     x.ItemCode,
		ZoneCode:     x.ZoneCode,
		DataType:     x.DataType,
		CurrencyType: base.CurrencyType(x.CurrencyType),
		StairPrices:  stairPrices,
		EffectTime:   base.NewHNS(effectTime),
		DeadTime:     base.NewHNS(deadTime),
	}, nil
}

// BuildPbQueryPriceWithTimeRangeParam 转换 service model 为 proto model
func BuildPbQueryPriceWithTimeRangeParam(param *service.QueryPriceWithTimeRangeParam) (*pb.QueryPriceWithTimeRangeParam, error) {
	return &pb.QueryPriceWithTimeRangeParam{
		ItemIds:   param.ItemIDs,
		ZoneId:    param.ZoneID,
		StartTime: timestamppb.New(param.StartTime.TimeIn(time.UTC)),
		EndTime:   timestamppb.New(param.EndTime.TimeIn(time.UTC)),
	}, nil
}

// BuildQueryPriceWithTimeRangeParam 转换 proto model 为 service model
func BuildQueryPriceWithTimeRangeParam(param *pb.QueryPriceWithTimeRangeParam) (*service.QueryPriceWithTimeRangeParam, error) {
	err := param.GetStartTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	startTime := param.GetStartTime().AsTime()

	err = param.GetEndTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	endTime := param.GetEndTime().AsTime()

	return &service.QueryPriceWithTimeRangeParam{
		ItemIDs:      param.GetItemIds(),
		ZoneID:       param.GetZoneId(),
		StartTime:    base.NewHNS(startTime),
		EndTime:      base.NewHNS(endTime),
		CurrencyType: base.CurrencyType(param.GetCurrencyType()),
	}, nil
}

// BuildPbItemPricesResp 转换 PriceItem 为 pb.ItemPricesResp
func BuildPbItemPricesResp(param *model.PriceItem) (*pb.ItemPricesResp, error) {
	pbPriceItem, err := BuildPbPriceItem(param)
	if err != nil {
		return nil, err
	}

	pbPriceItemStairs := make([]*pb.PriceItemStair, len(param.Stairs))
	for i := range param.Stairs {
		pbStair, err1 := BuildPbPriceItemStair(&param.Stairs[i])
		if err1 != nil {
			return nil, err1
		}

		pbPriceItemStairs[i] = pbStair
	}

	return &pb.ItemPricesResp{
		PriceItem:       pbPriceItem,
		PriceItemStairs: pbPriceItemStairs,
	}, nil
}

// BuildItemPricesListResp 转换一组 PriceItem 为 pb.ItemPricesListResp
func BuildItemPricesListResp(params []model.PriceItem) (*pb.ItemPricesListResp, error) {
	itemPrices := make([]*pb.ItemPricesResp, len(params))
	for i := range params {
		pbItemPricesResp, err := BuildPbItemPricesResp(&params[i])
		if err != nil {
			return nil, err
		}

		itemPrices[i] = pbItemPricesResp
	}

	return &pb.ItemPricesListResp{
		ItemPrices: itemPrices,
	}, nil
}
