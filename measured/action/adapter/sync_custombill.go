package adapter

import (
	"time"

	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/model"
)

func BuildModelSyncCustomBillParams(
	x *pb.SyncCustomBillParamsV1,
) (*model.SyncCustomBillParams, error) {
	var begin time.Time
	var end time.Time
	if x.Begin != nil {
		err := x.Begin.CheckValid()
		if err != nil {
			return nil, err
		}
		begin = x.Begin.AsTime()
	}

	if x.End != nil {
		err := x.End.CheckValid()
		if err != nil {
			return nil, err
		}
		end = x.End.AsTime()
	}

	return &model.SyncCustomBillParams{
		UID:         x.Uid,
		ProductCode: x.ProductCode,
		Begin:       begin,
		End:         end,
		ZoneCode:    x.ZoneCode,
		Debug:       x.Debug,
	}, nil
}
