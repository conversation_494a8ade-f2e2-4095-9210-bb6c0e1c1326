package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/action/adapter"
)

// impl PayMeasureServiceServer for MeasureAction

// GetPackageUserMapByExcode get package user maps by excode
func (a *MeasureAction) GetPackageUserMapByExcode(
	ctx context.Context,
	p *pb.UIDExcodeParam,
) (*pb.PackageUserMapList, error) {

	objs, err := a.srv.ListPackageUserMapsByExcode(
		ctx,
		p.GetUid(),
		p.GetExcode(),
	)
	if err != nil {
		return nil, err
	}
	// count 不需要查询，如有需要再加上
	return adapter.BuildPbPackageUserMapList(objs, 0)
}

// GetPackageUserMapByID 根据 ID 查询单个资源包-用户关联关系记录
func (a *MeasureAction) GetPackageUserMapByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.PackageUserMap, error) {
	obj, err := a.srv.GetPackageUserMapByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbPackageUserMap(obj)
}

// ListAllPackageUserMaps 列举所有资源包-用户关联关系记录
func (a *MeasureAction) ListAllPackageUserMaps(
	ctx context.Context,
	p *pb.PagingParam,
) (*pb.PackageUserMapList, error) {
	count, err := a.srv.CountAllPackageUserMaps(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(p)
	objs, err := a.srv.ListAllPackageUserMaps(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageUserMapList(objs, count)
}

// CountAllPackageUserMaps 获取资源包-用户关联关系记录总数
func (a *MeasureAction) CountAllPackageUserMaps(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.srv.CountAllPackageUserMaps(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListPackageUserMapsByUID 根据 UID 列举资源包-用户关联关系记录
func (a *MeasureAction) ListPackageUserMapsByUID(
	ctx context.Context,
	p *pb.UIDPagingParam,
) (*pb.PackageUserMapList, error) {
	uid := p.GetUid()
	count, err := a.srv.CountPackageUserMapsByUID(ctx, uid)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(p)
	objs, err := a.srv.ListPackageUserMapsByUID(ctx, uid, offset, limit)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageUserMapList(objs, count)
}

// CountPackageUserMapsByUID 根据 UID 获取资源包-用户关联关系记录数量
func (a *MeasureAction) CountPackageUserMapsByUID(
	ctx context.Context,
	p *pb.UIDParam,
) (*pb.CountParam, error) {
	count, err := a.srv.CountPackageUserMapsByUID(ctx, p.GetUid())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// CreatePackageUserMap 创建一个资源包-用户关联关系记录
func (a *MeasureAction) CreatePackageUserMap(
	ctx context.Context,
	p *pb.PackageUserMap,
) (*pb.PackageUserMap, error) {
	obj, err := adapter.BuildModelPackageUserMap(p)
	if err != nil {
		return nil, err
	}
	obj, err = a.srv.CreatePackageUserMap(ctx, obj)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageUserMap(obj)
}

// DeletePackageUserMapByID 根据 ID 删除一个资源包-用户关联关系记录
func (a *MeasureAction) DeletePackageUserMapByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.PackageUserMap, error) {
	obj, err := a.srv.DeletePackageUserMapByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageUserMap(obj)
}
