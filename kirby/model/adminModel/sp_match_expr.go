package adminModel

func (x SPMatchExpr) IsMatch(sp map[string]string) bool {
	if x == nil || sp == nil {
		return false
	}

	for keyToMatch, matchExpr := range x {
		v, ok := sp[keyToMatch]
		if !ok {
			// 传入的序列属性没有这个 key，返回不匹配
			return false
		}

		// TODO: 支持看其他类型
		if len(matchExpr.StringEnumValueIn) > 0 {
			matched := false
			for _, valueToMatch := range matchExpr.StringEnumValueIn {
				if v == valueToMatch {
					matched = true
					break
				}
			}
			if !matched {
				// 当前 key 的 value 不匹配，返回不匹配
				return false
			}

			continue
		}

		// 这似乎是一个空的匹配表达式
		return false
	}

	return true
}
