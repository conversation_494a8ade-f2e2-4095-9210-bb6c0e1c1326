package model

import (
	"database/sql"
	"database/sql/driver"
	"errors"

	pb "github.com/qbox/pay-sdk/wallet"
)

// CouponBatchState 抵用券批次状态
type CouponBatchState int64

const (
	// CouponBatchStateUnknown 未知抵用券批次状态
	CouponBatchStateUnknown CouponBatchState = CouponBatchState(pb.COUPON_BATCH_STATE_UNKNOWN)
	// CouponBatchStateNotStarted 未开始
	CouponBatchStateNotStarted CouponBatchState = CouponBatchState(pb.COUPON_BATCH_STATE_NOT_STARTED)
	// CouponBatchStateInProgress 发放中
	CouponBatchStateInProgress CouponBatchState = CouponBatchState(pb.COUPON_BATCH_STATE_IN_PROGRESS)
	// CouponBatchStateSuspended 已冻结
	CouponBatchStateSuspended CouponBatchState = CouponBatchState(pb.COUPON_BATCH_STATE_SUSPENDED)
	// CouponBatchStateEnded 已结束
	CouponBatchStateEnded CouponBatchState = CouponBatchState(pb.COUPON_BATCH_STATE_ENDED)
	// CouponBatchStateInvalid 已作废
	CouponBatchStateInvalid CouponBatchState = CouponBatchState(pb.COUPON_BATCH_STATE_INVALID)
)

// Value implements the driver.Valuer interface
func (t CouponBatchState) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *CouponBatchState) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = CouponBatchState(ns.Int64)
	return nil
}

// CouponState 抵用券状态
type CouponState int64

const (
	// CouponStateUnknown 未知抵用券状态
	CouponStateUnknown CouponState = CouponState(pb.COUPON_STATE_UNKNOWN)
	// CouponStatePending 未生效
	CouponStatePending CouponState = CouponState(pb.COUPON_STATE_PENDING)
	// CouponStateUsable 可使用
	CouponStateUsable CouponState = CouponState(pb.COUPON_STATE_USABLE)
	// CouponStateSuspended 已冻结
	CouponStateSuspended CouponState = CouponState(pb.COUPON_STATE_SUSPENDED)
	// CouponStateExhausted 已用完
	CouponStateExhausted CouponState = CouponState(pb.COUPON_STATE_EXHAUSTED)
	// CouponStateExpired 已过期
	CouponStateExpired CouponState = CouponState(pb.COUPON_STATE_EXPIRED)
	// CouponStateLocked 已锁定
	CouponStateLocked CouponState = CouponState(pb.COUPON_STATE_LOCKED)
	// CouponStateCanceled 已撤销
	CouponStateCanceled CouponState = CouponState(pb.COUPON_STATE_CANCELED)
)

// Value implements the driver.Valuer interface
func (t CouponState) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *CouponState) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = CouponState(ns.Int64)
	return nil
}

// CouponReason 抵用券发放事由
type CouponReason int64

const (
	// CouponReasonUnknown 未知抵用券发放事由
	CouponReasonUnknown CouponReason = CouponReason(pb.COUPON_REASON_UNKNOWN)
	// CouponReasonCustomerMaintenance 客情维护
	CouponReasonCustomerMaintenance CouponReason = CouponReason(pb.COUPON_REASON_CUSTOMER_MAINTENANCE)
	// CouponReasonTrial 测试
	CouponReasonTrial CouponReason = CouponReason(pb.COUPON_REASON_TRIAL)
	// CouponReasonCampaign 活动
	CouponReasonCampaign CouponReason = CouponReason(pb.COUPON_REASON_CAMPAIGN)
	// CouponReasonSLA 事故赔付
	CouponReasonSLA CouponReason = CouponReason(pb.COUPON_REASON_SLA)
)

// Value implements the driver.Valuer interface
func (t CouponReason) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *CouponReason) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = CouponReason(ns.Int64)
	return nil
}

// CouponBindMethod 抵用券发放形式
type CouponBindMethod int64

const (
	// CouponBindMethodUnknown 未知抵用券发放形式
	CouponBindMethodUnknown CouponBindMethod = CouponBindMethod(pb.COUPON_BIND_METHOD_UNKNOWN)
	// CouponBindMethodAPI 接口领取，用户领取
	CouponBindMethodAPI CouponBindMethod = CouponBindMethod(pb.COUPON_BIND_METHOD_API)
	// CouponBindMethodURL 链接领取
	CouponBindMethodURL CouponBindMethod = CouponBindMethod(pb.COUPON_BIND_METHOD_URL)
	// CouponBindMethodPrebound 发放至账户，指定发放
	CouponBindMethodPrebound CouponBindMethod = CouponBindMethod(pb.COUPON_BIND_METHOD_PREBOUND)
	// CouponBindMethodActivationCode 激活码
	CouponBindMethodActivationCode CouponBindMethod = CouponBindMethod(pb.COUPON_BIND_METHOD_ACTIVATION_CODE)
)

// Value implements the driver.Valuer interface
func (t CouponBindMethod) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *CouponBindMethod) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = CouponBindMethod(ns.Int64)
	return nil
}

// CouponUserType 抵用券适用用户类型
type CouponUserType int64

const (
	// CouponUserTypeUnknown 未知抵用券适用用户类型
	CouponUserTypeUnknown CouponUserType = CouponUserType(pb.COUPON_USER_SCOPE_UNKNOWN)
	// CouponUserTypeUnlimited 无限制
	CouponUserTypeUnlimited CouponUserType = CouponUserType(pb.COUPON_USER_SCOPE_UNLIMITED)
	// CouponUserTypeNewUser 仅新用户
	CouponUserTypeNewUser CouponUserType = CouponUserType(pb.COUPON_USER_SCOPE_NEW_USER)
)

// Value implements the driver.Valuer interface
func (t CouponUserType) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *CouponUserType) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = CouponUserType(ns.Int64)
	return nil
}

// CouponPayMode 抵用券适用付费类型
//
// NOTE: 类似 `PayMode` 但有所不同
type CouponPayMode int64

const (
	// CouponPayModeScopeUnknown 未知抵用券适用付费类型
	CouponPayModeScopeUnknown CouponPayMode = CouponPayMode(pb.COUPON_PAY_MODE_SCOPE_UNKNOWN)
	// CouponPayModeScopeUnlimited 不限（预付 & 后付）
	CouponPayModeScopeUnlimited CouponPayMode = CouponPayMode(pb.COUPON_PAY_MODE_SCOPE_UNLIMITED)
	// CouponPayModeScopePrepaid 预付（订单券）
	CouponPayModeScopePrepaid CouponPayMode = CouponPayMode(pb.COUPON_PAY_MODE_SCOPE_PREPAID)
	// CouponPayModeScopePostpaid 后付（账单券）
	CouponPayModeScopePostpaid CouponPayMode = CouponPayMode(pb.COUPON_PAY_MODE_SCOPE_POSTPAID)
)

// Value implements the driver.Valuer interface
func (t CouponPayMode) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *CouponPayMode) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = CouponPayMode(ns.Int64)
	return nil
}

// CouponTimePeriodType 抵用券生效时间种类
type CouponTimePeriodType int64

const (
	// CouponTimePeriodTypeUnknown 未知抵用券生效时间种类
	CouponTimePeriodTypeUnknown CouponTimePeriodType = CouponTimePeriodType(pb.COUPON_TIME_PERIOD_TYPE_UNKNOWN)
	// CouponTimePeriodTypeConstDuration 固定天数
	CouponTimePeriodTypeConstDuration CouponTimePeriodType = CouponTimePeriodType(pb.COUPON_TIME_PERIOD_TYPE_CONST_DURATION)
	// CouponTimePeriodTypeAbsolute 固定时间段
	CouponTimePeriodTypeAbsolute CouponTimePeriodType = CouponTimePeriodType(pb.COUPON_TIME_PERIOD_TYPE_ABSOLUTE)
)

// Value implements the driver.Valuer interface
func (t CouponTimePeriodType) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *CouponTimePeriodType) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = CouponTimePeriodType(ns.Int64)
	return nil
}

// ApplicantType 申请人类型
type ApplicantType int64

const (
	// ApplicantTypeUnknown 未知申请人种类
	ApplicantTypeUnknown ApplicantType = ApplicantType(pb.APPLICANT_TYPE_UNKNOWN)
	// ApplicantTypeKodo kodo 产品线
	ApplicantTypeKodo ApplicantType = ApplicantType(pb.APPLICANT_TYPE_KODO)
	// ApplicantTypeAtlab atlab 产品线
	ApplicantTypeAtlab ApplicantType = ApplicantType(pb.APPLICANT_TYPE_ATLAB)
	// ApplicantTypeMps mps 产品线
	ApplicantTypeMps ApplicantType = ApplicantType(pb.APPLICANT_TYPE_MPS)
	// ApplicantTypeMarket market 产品线
	ApplicantTypeMarket ApplicantType = ApplicantType(pb.APPLICANT_TYPE_MARKET)
	// ApplicantTypeUfop ufop 产品线
	ApplicantTypeUfop ApplicantType = ApplicantType(pb.APPLICANT_TYPE_UFOP)
	// ApplicantTypeUfop2 ufop2 产品线
	ApplicantTypeUfop2 ApplicantType = ApplicantType(pb.APPLICANT_TYPE_UFOP2)
	// ApplicantTypePili pili 产品线
	ApplicantTypePili ApplicantType = ApplicantType(pb.APPLICANT_TYPE_PILI)
	// ApplicantTypeEvm evm 产品线
	ApplicantTypeEvm ApplicantType = ApplicantType(pb.APPLICANT_TYPE_EVM)
	// ApplicantTypeFusion fusion 产品线
	ApplicantTypeFusion ApplicantType = ApplicantType(pb.APPLICANT_TYPE_FUSION)
	// ApplicantTypeKylin kylin 产品线
	ApplicantTypeKylin ApplicantType = ApplicantType(pb.APPLICANT_TYPE_KYLIN)
	// ApplicantTypeCstorage cstorage 产品线
	ApplicantTypeCstorage ApplicantType = ApplicantType(pb.APPLICANT_TYPE_CSTORAGE)
	// ApplicantTypeJedi jedi 产品线
	ApplicantTypeJedi ApplicantType = ApplicantType(pb.APPLICANT_TYPE_JEDI)
	// ApplicantTypeKirk kirk 产品线
	ApplicantTypeKirk ApplicantType = ApplicantType(pb.APPLICANT_TYPE_KIRK)
	// ApplicantTypePandora pandora 产品线
	ApplicantTypePandora ApplicantType = ApplicantType(pb.APPLICANT_TYPE_PANDORA)
	// ApplicantTypeAtaraxia ataraxia 产品线
	ApplicantTypeAtaraxia ApplicantType = ApplicantType(pb.APPLICANT_TYPE_ATARAXIA)
	// ApplicantTypeES es 产品线
	ApplicantTypeES ApplicantType = ApplicantType(pb.APPLICANT_TYPE_ES)
	// ApplicantTypeMserver mserver 产品线
	ApplicantTypeMserver ApplicantType = ApplicantType(pb.APPLICANT_TYPE_MSERVER)
	// ApplicantTypeMstorage mstorage 产品线
	ApplicantTypeMstorage ApplicantType = ApplicantType(pb.APPLICANT_TYPE_MSTORAGE)
	// ApplicantTypeDistribution distribution 产品线
	ApplicantTypeDistribution ApplicantType = ApplicantType(pb.APPLICANT_TYPE_DISTRIBUTION)
	// ApplicantTypeQvm qvm 产品线
	ApplicantTypeQvm ApplicantType = ApplicantType(pb.APPLICANT_TYPE_QVM)
	// ApplicantTypeKE ke 产品线
	ApplicantTypeKE ApplicantType = ApplicantType(pb.APPLICANT_TYPE_KE)
	// ApplicantTypeLinking linking 产品线
	ApplicantTypeLinking ApplicantType = ApplicantType(pb.APPLICANT_TYPE_LINKING)
	// ApplicantTypeSMS sms 产品线
	ApplicantTypeSMS ApplicantType = ApplicantType(pb.APPLICANT_TYPE_SMS)
	// ApplicantTypePiliSDK pili-sdk 产品线
	ApplicantTypePiliSDK ApplicantType = ApplicantType(pb.APPLICANT_TYPE_PILI_SDK)
	// ApplicantTypeBO bo 产品线
	ApplicantTypeBO ApplicantType = ApplicantType(pb.APPLICANT_TYPE_BO)
	// ApplicantTypeMarketing marketing 产品线
	ApplicantTypeMarketing ApplicantType = ApplicantType(pb.APPLICANT_TYPE_MARKETING)
	// ApplicantTypeTS ts 产品线
	ApplicantTypeTS ApplicantType = ApplicantType(pb.APPLICANT_TYPE_TS)
	// ApplicantTypeOther other 产品线
	ApplicantTypeOther ApplicantType = ApplicantType(pb.APPLICANT_TYPE_OTHER)
	// ApplicantTypeSales 销售
	ApplicantTypeSales ApplicantType = ApplicantType(pb.APPLICANT_TYPE_SALES)
	// ApplicantTypeQvs qvs 产品线
	ApplicantTypeQvs ApplicantType = ApplicantType(pb.APPLICANT_TYPE_QVS)
	// ApplicantTypeRtc rtc 产品线
	ApplicantTypeRtc ApplicantType = ApplicantType(pb.APPLICANT_TYPE_RTC)
	// ApplicantTypeActOp 活动运营: https://jira.qiniu.io/browse/BO-18692
	ApplicantTypeActOp ApplicantType = ApplicantType(pb.APPLICANT_TYPE_ACT_OP)
)

// Value implements the driver.Valuer interface
func (t ApplicantType) Value() (driver.Value, error) {
	return int64(t), nil
}

// Scan implements the sql.Scanner interface
func (t *ApplicantType) Scan(src any) error {
	// taken from https://github.com/go-sql-driver/mysql/issues/655
	ns := sql.NullInt64{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*t = ApplicantType(ns.Int64)
	return nil
}
