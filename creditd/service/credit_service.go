package service

import (
	"time"

	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/creditd/dao"
)

// CreditService is service handler of credit
type CreditService struct {
	creditDao           *dao.CreditDao
	ManualCredit        *ManualCreditService
	SystemCredit        *SystemCreditService
	SystemCreditHistory *SystemCreditHistoryService
}

// NewCreditService is constructor of CreditService
func NewCreditService(
	l10nProvider intl.L10nProvider,
	creditDao *dao.CreditDao,
	paymentClient wallet.PaymentServiceClient,
	walletClient wallet.PayWalletServiceClient,
	gaeaClient *client.Gaea,
	minSystemCreditLine int64,
	maxSystemCreditLine int64,
	expires time.Duration,
) (*CreditService, error) {
	manualCreditSrv, err := NewManualCreditService(creditDao.ManualCredit, expires, gaeaClient, paymentClient, walletClient)
	if err != nil {
		return nil, err
	}
	systemCreditSrv := NewSystemCreditService(
		l10nProvider, creditDao,
		gaeaClient, paymentClient, walletClient,
		minSystemCreditLine, maxSystemCreditLine,
	)
	systemCreditHistorySrv := NewSystemCreditHistoryService(creditDao.SystemCreditHistory)
	return &CreditService{
		creditDao:           creditDao,
		ManualCredit:        manualCreditSrv,
		SystemCredit:        systemCreditSrv,
		SystemCreditHistory: systemCreditHistorySrv,
	}, nil
}
