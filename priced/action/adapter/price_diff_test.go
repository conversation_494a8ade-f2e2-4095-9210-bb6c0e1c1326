package adapter

import (
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
	"qiniu.io/pay/priced/service"
)

func TestAdapterPriceDiffType(t *testing.T) {
	expects := []service.PriceDiffType{
		service.PriceDiffTypeIncomparable,
		service.PriceDiffTypeEQ,
		service.PriceDiffTypeGT,
		service.PriceDiffTypeLT,
	}

	for _, expect := range expects {
		m := BuildPbPriceDiffType(expect)
		actual := BuildPriceDiffType(m)
		assert.Equal(t, expect, actual)
	}
}

func TestAdapterDiffPriceItemStair(t *testing.T) {
	expect := &service.DiffPriceItemStair{
		Stair: model.PriceItemStair{
			ID:          1,
			PriceItemID: 2,
			UnitID:      3,
			Quantity:    4,
			Price:       base.NewNMoneyWithHighAccuracyI64(5),
			Order:       6,
			CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
			UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
		},
		Type: service.PriceDiffTypeEQ,
	}

	m, err := BuildPbDiffPriceItemStair(expect)
	assert.NoError(t, err)

	actual, err := BuildDiffPriceItemStair(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterDiffPriceItem(t *testing.T) {
	expect := &service.DiffPriceItem{
		PriceItem: model.PriceItem{
			Type: model.PriceTableTypeVIP,
		},
		Stairs: []service.DiffPriceItemStair{
			{
				Stair: model.PriceItemStair{
					ID:          1,
					PriceItemID: 2,
					UnitID:      3,
					Quantity:    4,
					Price:       base.NewNMoneyWithHighAccuracyI64(5),
					Order:       6,
					CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
					UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
				},
				Type: service.PriceDiffTypeEQ,
			},
			{
				Stair: model.PriceItemStair{
					ID:          2,
					PriceItemID: 2,
					UnitID:      4,
					Quantity:    5,
					Price:       base.NewNMoneyWithHighAccuracyI64(5),
					Order:       6,
					CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
					UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
				},
				Type: service.PriceDiffTypeGT,
			},
		},
	}

	m, err := BuildPbDiffPriceItem(expect)
	assert.NoError(t, err)

	actual, err := BuildDiffPriceItem(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterDiffPriceItemList(t *testing.T) {
	expect := &service.DiffPriceItemList{
		PriceItems: []service.DiffPriceItem{
			{
				PriceItem: model.PriceItem{
					Type: model.PriceTableTypeVIP,
				},
				Stairs: []service.DiffPriceItemStair{
					{
						Stair: model.PriceItemStair{
							ID:          1,
							PriceItemID: 2,
							UnitID:      3,
							Quantity:    4,
							Price:       base.NewNMoneyWithHighAccuracyI64(5),
							Order:       6,
							CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
							UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
						},
						Type: service.PriceDiffTypeEQ,
					},
					{
						Stair: model.PriceItemStair{
							ID:          2,
							PriceItemID: 2,
							UnitID:      4,
							Quantity:    5,
							Price:       base.NewNMoneyWithHighAccuracyI64(5),
							Order:       6,
							CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
							UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
						},
						Type: service.PriceDiffTypeGT,
					},
				},
			},
			{
				PriceItem: model.PriceItem{
					Type: model.PriceTableTypeVIP,
				},
				Stairs: []service.DiffPriceItemStair{
					{
						Stair: model.PriceItemStair{
							ID:          5,
							PriceItemID: 2,
							UnitID:      3,
							Quantity:    4,
							Price:       base.NewNMoneyWithHighAccuracyI64(5),
							Order:       6,
							CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
							UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
						},
						Type: service.PriceDiffTypeEQ,
					},
					{
						Stair: model.PriceItemStair{
							ID:          6,
							PriceItemID: 2,
							UnitID:      4,
							Quantity:    5,
							Price:       base.NewNMoneyWithHighAccuracyI64(5),
							Order:       6,
							CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
							UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
						},
						Type: service.PriceDiffTypeLT,
					},
				},
			},
		},
	}

	m, err := BuildPbDiffPriceItemList(expect)
	assert.NoError(t, err)

	actual, err := BuildDiffPriceItemList(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterDiffItemPrices(t *testing.T) {
	expect := &service.DiffItemPrices{
		ItemPriceMap: map[uint64]service.DiffPriceItemList{
			5: {
				PriceItems: []service.DiffPriceItem{
					{
						PriceItem: model.PriceItem{
							Type: model.PriceTableTypeVIP,
						},
						Stairs: []service.DiffPriceItemStair{
							{
								Stair: model.PriceItemStair{
									ID:          1,
									PriceItemID: 2,
									UnitID:      3,
									Quantity:    4,
									Price:       base.NewNMoneyWithHighAccuracyI64(5),
									Order:       6,
									CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
									UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
								},
								Type: service.PriceDiffTypeEQ,
							},
							{
								Stair: model.PriceItemStair{
									ID:          2,
									PriceItemID: 2,
									UnitID:      4,
									Quantity:    5,
									Price:       base.NewNMoneyWithHighAccuracyI64(5),
									Order:       6,
									CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
									UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
								},
								Type: service.PriceDiffTypeGT,
							},
						},
					},
				},
			},
		},
	}

	m, err := BuildPbDiffItemPrices(expect)
	assert.NoError(t, err)

	actual, err := BuildDiffItemPrices(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterPriceDiffResp(t *testing.T) {
	expect := &service.PriceDiffResp{
		Before: service.DiffItemPrices{
			ItemPriceMap: map[uint64]service.DiffPriceItemList{
				5: {
					PriceItems: []service.DiffPriceItem{
						{
							PriceItem: model.PriceItem{
								Type: model.PriceTableTypeVIP,
							},
							Stairs: []service.DiffPriceItemStair{
								{
									Stair: model.PriceItemStair{
										ID:          1,
										PriceItemID: 2,
										UnitID:      3,
										Quantity:    4,
										Price:       base.NewNMoneyWithHighAccuracyI64(5),
										Order:       6,
										CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
										UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
									},
									Type: service.PriceDiffTypeEQ,
								},
								{
									Stair: model.PriceItemStair{
										ID:          2,
										PriceItemID: 2,
										UnitID:      4,
										Quantity:    5,
										Price:       base.NewNMoneyWithHighAccuracyI64(5),
										Order:       6,
										CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
										UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
									},
									Type: service.PriceDiffTypeGT,
								},
							},
						},
					},
				},
			},
		},
		After: service.DiffItemPrices{
			ItemPriceMap: map[uint64]service.DiffPriceItemList{
				5: {
					PriceItems: []service.DiffPriceItem{
						{
							PriceItem: model.PriceItem{
								Type: model.PriceTableTypeVIP,
							},
							Stairs: []service.DiffPriceItemStair{
								{
									Stair: model.PriceItemStair{
										ID:          1,
										PriceItemID: 2,
										UnitID:      3,
										Quantity:    4,
										Price:       base.NewNMoneyWithHighAccuracyI64(5),
										Order:       6,
										CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
										UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
									},
									Type: service.PriceDiffTypeEQ,
								},
								{
									Stair: model.PriceItemStair{
										ID:          2,
										PriceItemID: 2,
										UnitID:      4,
										Quantity:    5,
										Price:       base.NewNMoneyWithHighAccuracyI64(5),
										Order:       6,
										CreatedAt:   time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC),
										UpdatedAt:   time.Date(2018, 1, 6, 0, 0, 0, 0, time.UTC),
									},
									Type: service.PriceDiffTypeLT,
								},
							},
						},
					},
				},
			},
		},
	}

	m, err := BuildPbPriceDiffResp(expect)
	assert.NoError(t, err)

	actual, err := BuildPriceDiffResp(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}
