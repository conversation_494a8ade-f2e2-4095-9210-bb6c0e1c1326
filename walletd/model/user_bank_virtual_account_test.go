package model_test

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/walletd/model"
)

func TestUserBankVirtualAccountDao_Save(t *testing.T) {
	sandbox := buildSandbox(t)

	userBankVirtualAccount := &model.UserBankVirtualAccount{
		UID:                1,
		BankVirtualAccount: "*************",
	}
	err := sandbox.userBankVirtualAccountDao.Save(userBankVirtualAccount)
	assert.NoError(t, err)
	userBankVirtualAccountRet, err := sandbox.userBankVirtualAccountDao.GetByID(userBankVirtualAccount.ID)
	assert.NoError(t, err)
	assert.Equal(t, userBankVirtualAccount.UID, userBankVirtualAccountRet.UID)
	assert.Equal(t, userBankVirtualAccount.BankVirtualAccount, userBankVirtualAccountRet.BankVirtualAccount)
}

func TestUserBankVirtualAccountDao_GetByUID(t *testing.T) {
	sandbox := buildSandbox(t)

	userBankVirtualAccount := &model.UserBankVirtualAccount{
		UID:                2,
		BankVirtualAccount: "*************",
	}
	err := sandbox.userBankVirtualAccountDao.Save(userBankVirtualAccount)
	assert.NoError(t, err)
	userBankVirtualAccountRet, err := sandbox.userBankVirtualAccountDao.GetByUID(userBankVirtualAccount.UID)
	assert.NoError(t, err)
	assert.Equal(t, userBankVirtualAccount.UID, userBankVirtualAccountRet.UID)
	assert.Equal(t, userBankVirtualAccount.BankVirtualAccount, userBankVirtualAccountRet.BankVirtualAccount)

	deletedUserBankVirtualAccount := &model.UserBankVirtualAccount{
		UID:                114514,
		BankVirtualAccount: "*************",
		IsDeleted:          true,
	}
	err = sandbox.userBankVirtualAccountDao.Save(deletedUserBankVirtualAccount)
	assert.NoError(t, err)
	deletedAccountRet, err := sandbox.userBankVirtualAccountDao.GetByUID(deletedUserBankVirtualAccount.UID)
	assert.NoError(t, err)
	assert.Equal(t, &model.UserBankVirtualAccount{UID: deletedUserBankVirtualAccount.UID}, deletedAccountRet)

}

func TestUserBankVirtualAccountDao_GetByBankVirtAccount(t *testing.T) {
	sandbox := buildSandbox(t)

	userBankVirtualAccount := &model.UserBankVirtualAccount{
		UID:                3,
		BankVirtualAccount: "*************",
	}
	err := sandbox.userBankVirtualAccountDao.Save(userBankVirtualAccount)
	assert.NoError(t, err)
	userBankVirtualAccountRet, err := sandbox.userBankVirtualAccountDao.GetByBankVirtAccount(userBankVirtualAccount.BankVirtualAccount)
	assert.NoError(t, err)
	assert.Equal(t, userBankVirtualAccount.UID, userBankVirtualAccountRet.UID)
	assert.Equal(t, userBankVirtualAccount.BankVirtualAccount, userBankVirtualAccountRet.BankVirtualAccount)

	deletedUserBankVirtualAccount := &model.UserBankVirtualAccount{
		UID:                3,
		BankVirtualAccount: "*************",
		IsDeleted:          true,
	}
	err = sandbox.userBankVirtualAccountDao.Save(deletedUserBankVirtualAccount)
	assert.NoError(t, err)
	deletedAccountRet, err := sandbox.userBankVirtualAccountDao.GetByBankVirtAccount(deletedUserBankVirtualAccount.BankVirtualAccount)
	assert.Error(t, err)
	assert.Nil(t, deletedAccountRet)
}

func TestUserBankVirtualAccountDao_GetByBankVirtAccounts(t *testing.T) {
	sandbox := buildSandbox(t)

	userBankVirtualAccounts := []*model.UserBankVirtualAccount{
		{
			UID:                2,
			BankVirtualAccount: "*************",
		}, {
			UID:                3,
			BankVirtualAccount: "*************",
		}, {
			UID:                3,
			BankVirtualAccount: "*************",
		},
		{
			UID:                4,
			BankVirtualAccount: "*************",
			IsDeleted:          true,
		},
	}
	for _, userBankVirtualAccount := range userBankVirtualAccounts {
		err := sandbox.userBankVirtualAccountDao.Save(userBankVirtualAccount)
		assert.NoError(t, err)
	}

	userBankVirtualAccountsRet, err := sandbox.userBankVirtualAccountDao.
		GetByBankVirtAccounts([]string{"*************", "*************", "*************", "*************"})
	assert.NoError(t, err)
	assert.Equal(t, len(userBankVirtualAccounts), len(userBankVirtualAccountsRet)+1)
}
