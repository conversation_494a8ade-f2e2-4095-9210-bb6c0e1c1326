package action

import (
	"context"

	"github.com/qbox/pay-sdk/middleware/logging"

	"qiniu.io/pay/coupon/service"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/coupon/action/adapter"
)

// BindCouponFromBatch 给用户绑券
func (a *CouponAction) BindCouponFromBatch(
	ctx context.Context,
	req *pb.BindCouponFromBatchParams,
) (*pb.BindCouponResp, error) {
	srvReq, err := adapter.BuildBindCouponFromBatchParams(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	srvResp, err := a.couponSrv.BindCouponFromBatch(ctx, srvReq)
	if err != nil {
		return nil, errors.Trace(err)
	}

	resp, err := adapter.BuildPbBindCouponResp(srvResp)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// CancelCouponByCode 根据券码撤销一张券
func (a *CouponAction) CancelCouponByCode(
	ctx context.Context,
	req *pb.CodeParam,
) (*pb.CancelCouponResp, error) {
	srvResp, err := a.couponSrv.CancelCouponByCode(ctx, req.Code)
	if err != nil {
		return nil, errors.Trace(err)
	}

	resp, err := adapter.BuildPbCancelCouponResp(srvResp)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// InternalSyncCouponBatchStates 同步所有抵用券批次的状态字段，用于批量任务
func (a *CouponAction) InternalSyncCouponBatchStates(
	ctx context.Context,
	req *empty.Empty,
) (*empty.Empty, error) {
	err := a.couponBatchSrv.SyncAllCouponBatchStates(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &empty.Empty{}, nil
}

// InternalSyncCouponStates 同步所有抵用券的状态字段，用于批量任务
func (a *CouponAction) InternalSyncCouponStates(
	ctx context.Context,
	req *empty.Empty,
) (*empty.Empty, error) {
	err := a.couponSrv.SyncAllCouponStates(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &empty.Empty{}, nil
}

// CreateAndBindCoupon 创建并绑定一张券给用户
func (a *CouponAction) CreateAndBindCoupon(
	ctx context.Context,
	req *pb.CreateBindCouponParams,
) (*pb.CreateAndBindCouponResp, error) {
	l := logging.GetLogger(ctx)
	couponBatchReq, err := adapter.BuildCouponBatch(req.CreateCouponBatchParams)
	if err != nil {
		l.Errorln("invoke adapter.BuildCouponBatch error", err)
		return nil, errors.Trace(err)
	}

	params := &service.CreateAndBindCoupon{
		UIDs:                    req.GetUids(),
		CreateCouponBatchParams: couponBatchReq,
	}

	bindCouponResps, err := a.couponSrv.CreateAndBindCoupon(ctx, params)
	if err != nil {
		l.Errorln("invoke couponSrv.CreateAndBindCoupon error", err)
		return nil, errors.Trace(err)
	}

	resp := &pb.CreateAndBindCouponResp{
		Results: make([]*pb.BindCouponResp, len(bindCouponResps)),
	}

	for i := 0; i < len(bindCouponResps); i += 1 {
		pbBindCouponResp, err1 := adapter.BuildPbBindCouponResp(bindCouponResps[i])
		if err1 != nil {
			l.Errorln("invoke adapter.BuildPbBindCouponResp error", err1)
			return nil, errors.Trace(err1)
		}

		resp.Results[i] = pbBindCouponResp
	}

	return resp, nil
}

// NotifyCouponExpiration 抵用券过期提醒
func (a *CouponAction) NotifyCouponExpiration(
	ctx context.Context,
	empty *empty.Empty,
) (*pb.NotifyCouponExpirationResp, error) {
	l := logging.GetLogger(ctx)
	total, failureCount, err := a.couponSrv.NotifyCouponExpiration(ctx)
	if err != nil {
		l.Error("NotifyCouponExpiration invoke couponSrv.NotifyCouponExpiration error", err)
		return nil, err
	}

	return &pb.NotifyCouponExpirationResp{
		Total:        total,
		FailureCount: failureCount,
	}, nil
}
