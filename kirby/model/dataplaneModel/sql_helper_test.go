package dataplaneModel

import (
	_ "embed"
	"testing"

	"github.com/stretchr/testify/assert"
)

//go:embed testdata/make_create_data_table_stmt_1.sql
var makeCreateDataTableStmt1Fixture string

//go:embed testdata/make_create_agg_series_metadata_table_stmt_1.sql
var makeCreateAggSeriesMetadataTableStmt1Fixture string

//go:embed testdata/make_create_series_metadata_table_stmt_1.sql
var makeCreateSeriesMetadataTableStmt1Fixture string

//go:embed testdata/make_bulk_insert_query_template_1.sql
var makeBulkInsertQueryTemplate1Fixture string

func TestMakeCreateDataTableStmt(t *testing.T) {
	actual, err := makeCreateDataTableStmt("foo", 31)
	assert.NoError(t, err)

	assert.Equal(t, makeCreateDataTableStmt1Fixture, actual)
}

func TestMakeCreateSeriesMetadataTableStmt(t *testing.T) {
	actual, err := makeCreateSeriesMetadataTableStmt("md_series_m202307")
	assert.NoError(t, err)

	assert.Equal(t, makeCreateSeriesMetadataTableStmt1Fixture, actual)
}

func TestMakeCreateAggSeriesMetadataTableStmt(t *testing.T) {
	actual, err := makeCreateAggSeriesMetadataTableStmt("md_agg_series_m202307")
	assert.NoError(t, err)

	assert.Equal(t, makeCreateAggSeriesMetadataTableStmt1Fixture, actual)
}

func TestMakeBulkInsertQueryTemplate(t *testing.T) {
	actual, err := makeBulkInsertQueryTemplate("foo", 4, 10)
	assert.NoError(t, err)

	assert.Equal(t, makeBulkInsertQueryTemplate1Fixture, actual)
}
