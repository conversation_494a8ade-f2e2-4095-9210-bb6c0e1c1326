package qrn

import (
	"database/sql"
	"database/sql/driver"
	"errors"
	"strings"
)

var errMalformedMatchExpr = errors.New("malformed qrn.MatchExpr")

// MatchExpr QRN 匹配表达式，形如 "productCode:resource 字符串的前缀匹配正则"
//
// e.g. "kodo:bucket/" 代表匹配 product code 为 "kodo" 且 resource 部分以 "bucket/" 打头的那些 QRN
type MatchExpr string

func (x MatchExpr) Parse() (*ParsedMatchExpr, error) {
	p, r, ok := strings.Cut(string(x), ":")
	if !ok {
		return nil, errMalformedMatchExpr
	}

	in := matchExprInput{
		productMatchExact:  p,
		resourceMatchRegex: r,
	}
	return in.makeParsedMatchExpr()
}

//
// database interface
//

var _ driver.Valuer = MatchExpr("")

func (x MatchExpr) Value() (driver.Value, error) {
	return string(x), nil
}

var _ sql.Scanner = (*MatchExpr)(nil)

func (x *MatchExpr) Scan(src any) error {
	ns := sql.NullString{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*x = MatchExpr(ns.String)
	return nil
}
