package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
)

// BuildPbScopeItemGroupMap converts model.ScopeItemGroupMap to the protobuf type.
func BuildPbScopeItemGroupMap(scopeItemGroupMap *model.ScopeItemGroupMap) (*pb.ScopeItemGroupMap, error) {
	return &pb.ScopeItemGroupMap{
		Id:         scopeItemGroupMap.ID,
		ScopeId:    scopeItemGroupMap.ScopeID,
		GroupId:    scopeItemGroupMap.GroupID,
		IsExcluded: scopeItemGroupMap.IsExcluded,
		CreatedAt:  timestamppb.New(scopeItemGroupMap.CreatedAt),
		UpdatedAt:  timestamppb.New(scopeItemGroupMap.UpdatedAt),
	}, nil
}

// BuildScopeItemGroupMap converts from pb.ScopeItemGroupMap to the model layer type.
func BuildScopeItemGroupMap(scopeItemGroupMap *pb.ScopeItemGroupMap) (*model.ScopeItemGroupMap, error) {
	var createdAt, updatedAt time.Time
	if scopeItemGroupMap.GetCreatedAt() != nil {
		err := scopeItemGroupMap.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = scopeItemGroupMap.GetCreatedAt().AsTime()
	}

	if scopeItemGroupMap.GetUpdatedAt() != nil {
		err := scopeItemGroupMap.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = scopeItemGroupMap.GetUpdatedAt().AsTime()
	}

	return &model.ScopeItemGroupMap{
		ID:         scopeItemGroupMap.GetId(),
		ScopeID:    scopeItemGroupMap.GetScopeId(),
		GroupID:    scopeItemGroupMap.GetGroupId(),
		GroupCode:  scopeItemGroupMap.GetGroupCode(),
		IsExcluded: scopeItemGroupMap.GetIsExcluded(),
		CreatedAt:  createdAt,
		UpdatedAt:  updatedAt,
	}, nil
}

// BuildPbScopeItemGroupMaps converts from []model.ScopeItemGroupMap to the protobuf type.
func BuildPbScopeItemGroupMaps(x []model.ScopeItemGroupMap) ([]*pb.ScopeItemGroupMap, error) {
	y := make([]*pb.ScopeItemGroupMap, len(x))
	for i, obj := range x {
		result, err := BuildPbScopeItemGroupMap(&obj)
		if err != nil {
			return nil, err
		}
		y[i] = result
	}
	return y, nil
}

// BuildScopeItemGroupMaps converts from []*pb.ScopeItemGroupMap to the model layer type.
func BuildScopeItemGroupMaps(x []*pb.ScopeItemGroupMap) ([]model.ScopeItemGroupMap, error) {
	y := make([]model.ScopeItemGroupMap, len(x))
	for i, obj := range x {
		result, err := BuildScopeItemGroupMap(obj)
		if err != nil {
			return nil, err
		}
		y[i] = *result
	}
	return y, nil
}
