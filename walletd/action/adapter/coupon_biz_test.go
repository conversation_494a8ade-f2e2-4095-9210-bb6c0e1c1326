package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"qiniu.io/pay/walletd/service"
)

func TestBindCouponRebateParamRoundTrip(t *testing.T) {
	x := &service.BindCouponRebateParam{
		UID:           233,
		Code:          "foo",
		TransactionID: 2333,
		ZoneID:        1,
		EffectTime:    base.NewHNS(time.Now().Round(0)),
		DeadTime:      base.NewHNS(time.Now().Round(0)),
	}

	y, err := BuildPbBindCouponRebateParam(x)
	assert.NoError(t, err)

	z, err := BuildBindCouponRebateParam(y)
	assert.NoError(t, err)

	assert.Equal(t, x.UID, z.UID)
	assert.Equal(t, x.Code, z.Code)
	assert.Equal(t, x.TransactionID, z.TransactionID)
	assert.Equal(t, x.ZoneID, z.ZoneID)
	assert.Equal(t, x.EffectTime, z.EffectTime)
	assert.Equal(t, x.DeadTime, z.DeadTime)
}

func TestUpgradeCouponRebateParamRoundTrip(t *testing.T) {
	x := &service.UpgradeCouponRebateParam{
		UID:           233,
		ZoneID:        1,
		TransactionID: 2333,
		SrcCode:       "src-code",
		DstCode:       "dst-code",
		EffectTime:    base.NewHNS(time.Now().Round(0)),
		DeadTime:      base.NewHNS(time.Now().Round(0)),
	}

	y, err := BuildPbUpgradeCouponRebateParam(x)
	assert.NoError(t, err)

	z, err := BuildUpgradeCouponRebateParam(y)
	assert.NoError(t, err)

	assert.Equal(t, x.UID, z.UID)
	assert.Equal(t, x.ZoneID, z.ZoneID)
	assert.Equal(t, x.TransactionID, z.TransactionID)
	assert.Equal(t, x.SrcCode, z.SrcCode)
	assert.Equal(t, x.DstCode, z.DstCode)
	assert.Equal(t, x.EffectTime, z.EffectTime)
	assert.Equal(t, x.DeadTime, z.DeadTime)
}
