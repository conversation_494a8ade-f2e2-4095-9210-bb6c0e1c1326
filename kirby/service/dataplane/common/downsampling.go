package common

import (
	"errors"
	"sort"
	"time"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
)

var errRequestedGranularityTooFine = errors.New("requested granularity is too fine")

func TransformGranularity(
	srcG adminModel.Granularity,
	destG adminModel.Granularity,
	points []*pb.StatPoint,
	loc *time.Location,
) ([]*pb.StatPoint, error) {
	if destG == adminModel.GranularityUnknown || srcG == destG {
		return points, nil
	}

	err := srcG.Validate()
	if err != nil {
		return nil, err
	}
	err = destG.Validate()
	if err != nil {
		return nil, err
	}

	// Unfortunately we have to post-process a bit more, due to the mismatch
	// between the aggregated series' granularity and requested granularity.
	// We can go coarser but not finer, obviously because we can't magically
	// gather the finer-granularity data from thin air.
	switch srcG {
	case adminModel.Granularity5Min:
		// all ok

	case adminModel.GranularityDay:
		switch destG {
		case adminModel.Granularity5Min:
			return nil, errRequestedGranularityTooFine
		}

	case adminModel.GranularityMonth:
		// this is the coarsest granularity we support so far, and identity
		// transformation is already handled earlier, so unconditionally error
		// out
		return nil, errRequestedGranularityTooFine
	}

	// now do the downsampling
	switch destG {
	case adminModel.Granularity5Min:
		fallthrough
	default:
		panic("should never happen")

	case adminModel.GranularityDay:
		return downsamplePoints(points, base.Today, loc), nil
	case adminModel.GranularityMonth:
		return downsamplePoints(points, base.ThisMonth, loc), nil
	}
}

// accumulate into coarser granularity
// TODO: optimize this code if performance turns out to be a concern,
// the current approach favors readability over raw performance
func downsamplePoints(
	x []*pb.StatPoint,
	downsampler func(time.Time) time.Time,
	loc *time.Location,
) []*pb.StatPoint {
	groups := lo.GroupBy(x, func(p *pb.StatPoint) int64 {
		return downsampler(p.Time.AsTime().In(loc)).UnixNano()
	})
	resultPointsMap := lo.MapEntries(groups, func(k int64, v []*pb.StatPoint) (int64, uint64) {
		return k, lo.Sum(lo.Map(v, func(i *pb.StatPoint, _ int) uint64 { return i.Value }))
	})
	result := lo.MapToSlice(resultPointsMap, func(k int64, v uint64) *pb.StatPoint {
		return &pb.StatPoint{
			Time:  timestamppb.New(time.Unix(0, k)),
			Value: v,
		}
	})
	sort.Slice(result, func(i int, j int) bool {
		return result[i].Time.AsTime().Before(result[j].Time.AsTime())
	})
	return result
}
