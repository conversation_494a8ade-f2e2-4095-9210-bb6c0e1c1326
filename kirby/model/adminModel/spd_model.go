package adminModel

import (
	"strings"
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/uuid"
)

var errReservedSPKey = errors.New("reserved SP key")

// SeriesPropertyDefinition 序列属性定义（SPD）
type SeriesPropertyDefinition struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// UniqueKey SPD 的唯一内部标识
	UniqueKey string `gorm:"column:unique_key;type:VARCHAR(64);not null;unique_index:idx_spd_unique_key;comment:'SPD 的唯一标识'"`
	// RTDCode 关联的 RTD 的唯一标识
	RTDCode string `gorm:"column:rtd_code;type:VARCHAR(64);not null;index:idx_spd_rtd_code;comment:'关联的 RTD 的唯一标识'"`
	// Key 属性名称
	Key string `gorm:"column:key;type:VARCHAR(256);not null;comment:'属性名称'"`
	// Name 属性描述
	Name string `gorm:"column:name;type:VARCHAR(256);not null;comment:'属性描述'"`
	// ValueType 值的类型
	ValueType SPValueType `gorm:"column:value_type;type:tinyint(2);not null;comment:'值的类型'"`

	// StringEnum 字符串枚举类型的定义，以 JSON StringEnumDecl 对象形式存储，仅当 ValueType == SPValueTypeStringEnum 时取值有意义
	StringEnum *StringEnumDecl `gorm:"column:string_enum;type:TEXT(65535);not null;comment:'合法的字符串枚举类型的定义，以 JSON StringEnumDecl 对象形式存储，仅当 ValueType == SPValueTypeStringEnum 时取值有意义'"`

	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);default:now(6)"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME(6);default:now(6) ON UPDATE now(6)"`
}

type SeriesPropertyDefinitionDetail struct {
	Key       string
	Name      string
	ValueType SPValueType

	StringEnum *StringEnumDecl
}

func (x *SeriesPropertyDefinition) ToDetail() *SeriesPropertyDefinitionDetail {
	return &SeriesPropertyDefinitionDetail{
		Key:        x.Key,
		Name:       x.Name,
		ValueType:  x.ValueType,
		StringEnum: x.StringEnum,
	}
}

func generateSPDUniqueKey(epoch time.Time) string {
	return "QKSPD" + uuid.NewWithEpoch(epoch)
}

func (x *SeriesPropertyDefinitionDetail) toModel(
	uniqueKey string,
	rtdCode string,
	epoch time.Time,
) *SeriesPropertyDefinition {
	return &SeriesPropertyDefinition{
		UniqueKey:  uniqueKey,
		RTDCode:    rtdCode,
		Key:        x.Key,
		Name:       x.Name,
		ValueType:  x.ValueType,
		StringEnum: x.StringEnum,
		CreatedAt:  epoch,
	}
}

// SeriesPropertyDefinitionDao is data access object of SeriesPropertyDefinition model
type SeriesPropertyDefinitionDao struct {
	base *dao.BaseDao
}

// NewSeriesPropertyDefinitionDao is constructor of SeriesPropertyDefinitionDao
func NewSeriesPropertyDefinitionDao(base *dao.BaseDao) *SeriesPropertyDefinitionDao {
	return &SeriesPropertyDefinitionDao{
		base: base,
	}
}

// Save inserts or updates a SeriesPropertyDefinition by id
func (d *SeriesPropertyDefinitionDao) Save(model *SeriesPropertyDefinition) error {
	// 不允许在管理后台配置 $ 开头的序列属性定义，这部分名称保留给计量中心自行使用
	if strings.HasPrefix(model.Key, "$") {
		return errReservedSPKey
	}

	err := d.base.Execute(func(value any) error {
		return d.base.Save(value).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

func (d *SeriesPropertyDefinitionDao) BulkInsert(l []*SeriesPropertyDefinition) error {
	// 因为用到了神奇的类型，base.BulkInsert 不能支持，只能挨个插入
	// return d.base.BulkInsert(l)
	for _, r := range l {
		err := d.Save(r)
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *SeriesPropertyDefinitionDao) ListByRTDCodes(
	rtdCodes []string,
	epoch time.Time,
) ([]*SeriesPropertyDefinition, error) {
	var result []*SeriesPropertyDefinition
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&SeriesPropertyDefinition{}).
			Where("`rtd_code` IN (?) AND `created_at` <= ?", rtdCodes, epoch).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return result, nil
}
