CREATE TABLE IF NOT EXISTS `{{ .TableName }}` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `series_key` varchar(64) NOT NULL,
  `t0` datetime(6) NOT NULL,
  {{- range $i, $_ := .PointsSlice }}
  `p{{ $i }}` bigint(20) unsigned NOT NULL DEFAULT 0,
  {{- end }}
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_{{ .TableName }}_series_t` (`series_key`, `t0`)
)
  ENGINE=InnoDB
  ROW_FORMAT=COMPRESSED
  KEY_BLOCK_SIZE=8
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci;
