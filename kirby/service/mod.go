package service

import (
	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/service/dataplane"
)

// KirbyService is service handler of Kirby
type KirbyService struct {
	adminDao *adminModel.KirbyAdminDao
	DP       dataplane.IKirbyDataplane
}

// NewKirbyService is constructor of KirbyService
func NewKirbyService(
	adminDao *adminModel.KirbyAdminDao,
	dp dataplane.IKirbyDataplane,
) *KirbyService {
	return &KirbyService{
		adminDao: adminDao,
		DP:       dp,
	}
}
