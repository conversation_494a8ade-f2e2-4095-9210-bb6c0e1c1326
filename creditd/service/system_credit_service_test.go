package service_test

import (
	"context"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

// 绑定默认支付卡
func TestSystemCreditService_UpsertSystemCredit_1(t *testing.T) {
	sandbox := newCreditSrvSandbox(t, withUserCard(true), withCurrencyType(base.CurrencyTypeUSD))
	ctx := context.Background()
	systemCredit, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonBindFirstCard,
	})
	assert.Nil(t, err)
	assert.Equal(t, sandbox.minCreditLine, systemCredit.CreditLine)
	assert.Equal(t, uint64(1), systemCredit.CreditPeriod)
	assert.Equal(t, enums.PeriodUnitMonth, systemCredit.PeriodUnit)
	assert.Equal(t, base.CurrencyTypeUSD.String(), systemCredit.CurrencyType)

	sc, err := sandbox.creditSrv.SystemCredit.GetSystemCreditByUID(ctx, sandbox.uid)
	assert.Nil(t, err)
	assert.Equal(t, systemCredit.CurrencyType, sc.CurrencyType)
	assert.Equal(t, systemCredit.AssignReason, sc.AssignReason)
	assert.Equal(t, systemCredit.CreditLine, sc.CreditLine)
	assert.Equal(t, systemCredit.CreditPeriod, sc.CreditPeriod)
	assert.Equal(t, systemCredit.PeriodUnit, sc.PeriodUnit)
}

// 先绑卡，然后切换默认支付卡
func TestSystemCreditService_UpsertSystemCredit_2(t *testing.T) {
	sandbox := newCreditSrvSandbox(t, withUserCard(true), withCurrencyType(base.CurrencyTypeUSD))
	ctx := context.Background()

	_, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonBindFirstCard,
	})
	assert.Nil(t, err)

	systemCredit, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonChangeDefaultCard,
	})
	assert.Nil(t, err)
	assert.Equal(t, sandbox.minCreditLine, systemCredit.CreditLine)
	assert.Equal(t, uint64(1), systemCredit.CreditPeriod)
	assert.Equal(t, enums.PeriodUnitMonth, systemCredit.PeriodUnit)
	assert.Equal(t, base.CurrencyTypeUSD.String(), systemCredit.CurrencyType)

	sc, err := sandbox.creditSrv.SystemCredit.GetSystemCreditByUID(ctx, sandbox.uid)
	assert.Nil(t, err)
	assert.Equal(t, systemCredit.CurrencyType, sc.CurrencyType)
	assert.Equal(t, systemCredit.AssignReason, sc.AssignReason)
	assert.Equal(t, systemCredit.CreditLine, sc.CreditLine)
	assert.Equal(t, systemCredit.CreditPeriod, sc.CreditPeriod)
	assert.Equal(t, systemCredit.PeriodUnit, sc.PeriodUnit)
}

// 绑卡后扣费
func TestSystemCreditService_UpsertSystemCredit_3(t *testing.T) {
	sandbox := newCreditSrvSandbox(t, withUserCard(true), withCurrencyType(base.CurrencyTypeUSD))
	ctx := context.Background()

	_, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonBindFirstCard,
	})
	assert.Nil(t, err)

	systemCredit, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonChargeCard,
		CreditLine:   200000,
	})
	assert.Nil(t, err)
	assert.Equal(t, int64(2*200000), systemCredit.CreditLine)
	assert.Equal(t, uint64(1), systemCredit.CreditPeriod)
	assert.Equal(t, enums.PeriodUnitMonth, systemCredit.PeriodUnit)
	assert.Equal(t, base.CurrencyTypeUSD.String(), systemCredit.CurrencyType)

	sc, err := sandbox.creditSrv.SystemCredit.GetSystemCreditByUID(ctx, sandbox.uid)
	assert.Nil(t, err)
	assert.Equal(t, systemCredit.CreditLine, sc.CreditLine)
	assert.Equal(t, systemCredit.CreditPeriod, sc.CreditPeriod)
	assert.Equal(t, systemCredit.PeriodUnit, sc.PeriodUnit)
	assert.Equal(t, systemCredit.CurrencyType, sc.CurrencyType)

	newSystemCredit, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonChargeCard,
		CreditLine:   500000,
	})
	assert.Nil(t, err)
	assert.Equal(t, int64(500000*2+2*200000), newSystemCredit.CreditLine)
	assert.Equal(t, uint64(1), systemCredit.CreditPeriod)
	assert.Equal(t, enums.PeriodUnitMonth, systemCredit.PeriodUnit)
	assert.Equal(t, base.CurrencyTypeUSD.String(), systemCredit.CurrencyType)
}

// 绑卡后出账
func TestSystemCreditService_UpsertSystemCredit_4(t *testing.T) {
	sandbox := newCreditSrvSandbox(t, withUserCard(true), withCurrencyType(base.CurrencyTypeUSD))
	ctx := context.Background()

	// 不绑卡咋出账扣费
	_, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonBindFirstCard,
	})
	assert.Nil(t, err)

	systemCredit, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonGenMonthStatement,
		Month:        base.ThisMonth(time.Now()),
	})
	assert.Nil(t, err)
	// 刚绑卡就出账，说明账单金额小于 $10, 维持 $10 足够了
	assert.Equal(t, sandbox.minCreditLine, systemCredit.CreditLine)
	assert.Equal(t, uint64(1), systemCredit.CreditPeriod)
	assert.Equal(t, enums.PeriodUnitMonth, systemCredit.PeriodUnit)
	assert.Equal(t, base.CurrencyTypeUSD.String(), systemCredit.CurrencyType)

	sc, err := sandbox.creditSrv.SystemCredit.GetSystemCreditByUID(ctx, sandbox.uid)
	assert.Nil(t, err)
	assert.Equal(t, systemCredit.CreditPeriod, sc.CreditPeriod)
	assert.Equal(t, systemCredit.PeriodUnit, sc.PeriodUnit)
	assert.Equal(t, systemCredit.CurrencyType, sc.CurrencyType)

	/*
		// 本月出账已更新就不处理
		newSystemCredit, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
			UID:          sandbox.uid,
			AssignReason: enums.AssignReasonGenMonthStatement,
			Month:        base.ThisMonth(time.Now()),
		})
		assert.Nil(t, err)
		assert.Equal(t, int64(0), newSystemCredit.CreditLine)*/
}

// 绑卡后发生了扣费，然后出账
func TestSystemCreditService_UpsertSystemCredit_5(t *testing.T) {
	sandbox := newCreditSrvSandbox(t, withUserCard(true), withCurrencyType(base.CurrencyTypeUSD))
	ctx := context.Background()

	// 不绑卡咋出账扣费
	_, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonBindFirstCard,
	})
	assert.Nil(t, err)

	_, _, err = sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonChargeCard,
		CreditLine:   300000,
	})
	assert.Nil(t, err)
	// 目前信用额度应为 $60

	systemCredit, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonGenMonthStatement,
		Month:        base.ThisMonth(time.Now()),
	})
	assert.Nil(t, err)
	// 前三个月前账单金额最大值为 $50, 当前信用额度为 $60，取较小值
	assert.Equal(t, int64(500000), systemCredit.CreditLine)
	assert.Equal(t, uint64(1), systemCredit.CreditPeriod)
	assert.Equal(t, enums.PeriodUnitMonth, systemCredit.PeriodUnit)
	assert.Equal(t, base.CurrencyTypeUSD.String(), systemCredit.CurrencyType)

	sc, err := sandbox.creditSrv.SystemCredit.GetSystemCreditByUID(ctx, sandbox.uid)
	assert.Nil(t, err)
	assert.Equal(t, systemCredit.CreditLine, sc.CreditLine)
	assert.Equal(t, systemCredit.CreditPeriod, sc.CreditPeriod)
	assert.Equal(t, systemCredit.PeriodUnit, sc.PeriodUnit)
	assert.Equal(t, systemCredit.CurrencyType, sc.CurrencyType)
}

// 绑卡后频繁扣费
func TestSystemCreditService_UpsertSystemCredit_6(t *testing.T) {
	sandbox := newCreditSrvSandbox(t, withUserCard(true), withCurrencyType(base.CurrencyTypeUSD))
	ctx := context.Background()

	// 不绑卡咋出账扣费
	_, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonBindFirstCard,
	})
	assert.Nil(t, err)

	_, _, err = sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonChargeCard,
		CreditLine:   300000,
	})
	assert.Nil(t, err)
	// 目前信用额度应为 $60

	_, _, err = sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
		UID:          sandbox.uid,
		AssignReason: enums.AssignReasonChargeCard,
		CreditLine:   900000,
	})
	assert.Nil(t, err)

	systemCredit, err := sandbox.creditSrv.SystemCredit.GetSystemCreditByUID(ctx, sandbox.uid)
	assert.Nil(t, err)
	// 按照公式，信用额度 90*2+60 = 240 美元，但是实际上限为 200 美元
	assert.Equal(t, sandbox.maxCreditLine, systemCredit.CreditLine)
	assert.Equal(t, uint64(1), systemCredit.CreditPeriod)
	assert.Equal(t, enums.PeriodUnitMonth, systemCredit.PeriodUnit)
	assert.Equal(t, base.CurrencyTypeUSD.String(), systemCredit.CurrencyType)
}

// 没有绑卡记录就更新其他信用额度,应报错
func TestSystemCreditService_UpsertSystemCredit_7(t *testing.T) {
	sandbox := newCreditSrvSandbox(t, withUserCard(true), withCurrencyType(base.CurrencyTypeUSD))
	ctx := context.Background()

	assignReasons := []enums.AssignReason{
		enums.AssignReasonGenMonthStatement,
		enums.AssignReasonChargeCard,
		enums.AssignReasonRevokeManualCredit,
		enums.AssignReasonChangeDefaultCard,
	}

	for _, reason := range assignReasons {
		_, _, err := sandbox.creditSrv.SystemCredit.UpsertSystemCredit(ctx, model.SystemCredit{
			UID:          sandbox.uid,
			AssignReason: reason,
		})
		assert.NotNil(t, err)
	}
}
