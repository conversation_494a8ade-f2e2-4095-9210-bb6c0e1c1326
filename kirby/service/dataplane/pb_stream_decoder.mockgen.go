// Code generated by MockGen. DO NOT EDIT.
// Source: kirby/service/dataplane/pb_stream_decoder.go

package dataplane

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockvtMessageSink is a mock of vtMessageSink interface.
type MockvtMessageSink struct {
	ctrl     *gomock.Controller
	recorder *MockvtMessageSinkMockRecorder
}

// MockvtMessageSinkMockRecorder is the mock recorder for MockvtMessageSink.
type MockvtMessageSinkMockRecorder struct {
	mock *MockvtMessageSink
}

// NewMockvtMessageSink creates a new mock instance.
func NewMockvtMessageSink(ctrl *gomock.Controller) *MockvtMessageSink {
	mock := &MockvtMessageSink{ctrl: ctrl}
	mock.recorder = &MockvtMessageSinkMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockvtMessageSink) EXPECT() *MockvtMessageSinkMockRecorder {
	return m.recorder
}

// UnmarshalVT mocks base method.
func (m *MockvtMessageSink) UnmarshalVT(arg0 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnmarshalVT", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnmarshalVT indicates an expected call of UnmarshalVT.
func (mr *MockvtMessageSinkMockRecorder) UnmarshalVT(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnmarshalVT", reflect.TypeOf((*MockvtMessageSink)(nil).UnmarshalVT), arg0)
}
