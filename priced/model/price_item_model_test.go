package model_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/base"
	basedao "github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
)

func TestModelPriceItem(t *testing.T) {
	sandbox := buildSandbox(t)

	dao := sandbox.priceDao.PriceItem

	const priceID = 1
	priceItems := []model.PriceItem{
		{
			PriceID:    priceID,
			ItemID:     1,
			ZoneID:     1,
			IsDisabled: true,
			UnitRate:   1000,
			EffectTime: base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local)),
			DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.Local)),

			CumulativeCycle: model.CumulativeTypeDay,
			BillPeriodType:  model.BillPeriodTypeDay,
		},
		{
			PriceID:    priceID,
			ItemID:     2,
			ZoneID:     1,
			IsDisabled: true,
			UnitRate:   1024,
			EffectTime: base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local)),
			DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.Local)),
		},
		{
			PriceID:    priceID,
			ItemID:     3,
			ZoneID:     1,
			IsDisabled: false,
			UnitRate:   1000,
			EffectTime: base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local)),
			DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.Local)),
		},
		{
			PriceID:    priceID + 1,
			ItemID:     1,
			ZoneID:     1,
			IsDisabled: false,
			UnitRate:   1024,
			EffectTime: base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local)),
			DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.Local)),
		},
	}

	assertFields := func(expect, actual *model.PriceItem, msgAndArgs ...any) {
		assert.Equal(t, expect.PriceID, actual.PriceID, msgAndArgs...)
		assert.Equal(t, expect.ItemID, actual.ItemID, msgAndArgs...)
		assert.Equal(t, expect.ZoneID, actual.ZoneID, msgAndArgs...)
		assert.Equal(t, expect.IsDisabled, actual.IsDisabled, msgAndArgs...)
		assert.Equal(t, expect.UnitRate, actual.UnitRate, msgAndArgs...)
		assert.Equal(t, expect.CumulativeCycle, actual.CumulativeCycle, msgAndArgs...)
		assert.Equal(t, expect.BillPeriodType, actual.BillPeriodType, msgAndArgs...)
	}

	for i, priceItem := range priceItems {
		// create
		err := dao.Save(&priceItem)
		if assert.NoError(t, err) {
			assert.NotZero(t, priceItem.ID, "Save return zero id")
		}
		priceItems[i].ID = priceItem.ID

		// query
		priceItemID := priceItem.ID
		m, err := dao.GetByID(priceItemID)
		if assert.NoError(t, err) {
			assertFields(&priceItem, m, fmt.Sprintf("Unmatched item, item_id:%d", priceItem.ItemID))
		}
	}

	// list all price items
	priceItemList, err := dao.List(0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, priceItemList, len(priceItems))
	}

	// list all price items by price_id
	priceItemListWithSamePriceItem, err := dao.ListByPriceID(priceID, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, priceItemListWithSamePriceItem, len(priceItems)-1)
		for _, item := range priceItemListWithSamePriceItem {
			assert.Equal(t, uint64(priceID), item.PriceID, "price_id should be the same")

			for _, expectedItem := range priceItems {
				if item.ID == expectedItem.ID {
					assertFields(&expectedItem, &item, fmt.Sprintf("Unmatched item, item_id:%d", item.ItemID))
					break
				}
			}
		}
	}

	// delete all price items
	for _, priceItem := range priceItems {
		err := dao.DeleteByID(priceItem.ID)
		assert.NoError(t, err)
	}

	// check price item list, should be empty
	l, err := dao.List(0, 10, basedao.CacheExpiresNoCache)
	if assert.NoError(t, err) {
		assert.Empty(t, l)
	}

	// de-duplication
	for _, priceItem := range priceItems {
		err := dao.DeleteByID(priceItem.ID)
		assert.Error(t, err)
	}
}

func TestListPublicItemPrice(t *testing.T) {
	sandbox := buildSandbox(t)

	// 准备测试数据
	publicPriceTable := &model.PriceTable{
		Type: model.PriceTableTypeDefault,
	}
	assert.NoError(t, sandbox.priceDao.PriceTable.Save(publicPriceTable))

	var itemID uint64 = 1
	var zoneID uint64 = 1
	priceItem1 := model.PriceItem{
		PriceID:    publicPriceTable.ID,
		ItemID:     itemID,
		ZoneID:     zoneID,
		EffectTime: base.HNS(10),
		DeadTime:   base.HNS(20),
	}

	priceItem2 := model.PriceItem{
		PriceID:    publicPriceTable.ID,
		ItemID:     itemID,
		ZoneID:     zoneID,
		EffectTime: base.HNS(20),
		DeadTime:   base.HNS(30),
	}

	assert.NoError(t, sandbox.priceDao.PriceItem.Save(&priceItem1))
	assert.NoError(t, sandbox.priceDao.PriceItem.Save(&priceItem2))

	// otherwise fails on Travis
	priceItem1.CreatedAt = time.Time{}
	priceItem1.UpdatedAt = time.Time{}
	priceItem2.CreatedAt = time.Time{}
	priceItem2.UpdatedAt = time.Time{}

	testCases := []struct {
		title            string
		effectTime       base.HNS
		deadTime         base.HNS
		expectPriceItems []model.PriceItem
		failed           bool
	}{
		{
			title:            "not found",
			effectTime:       base.HNS(1),
			deadTime:         base.HNS(10),
			expectPriceItems: nil,
			failed:           false,
		},
		{
			title:            "multiple price_items, contain time range",
			effectTime:       base.HNS(5),
			deadTime:         base.HNS(40),
			expectPriceItems: []model.PriceItem{priceItem1, priceItem2},
			failed:           false,
		},
		{
			title:            "multiple price_items, cross time range",
			effectTime:       base.HNS(15),
			deadTime:         base.HNS(40),
			expectPriceItems: []model.PriceItem{priceItem1, priceItem2},
			failed:           false,
		},
		{
			title:            "multiple price_items, same time range",
			effectTime:       base.HNS(10),
			deadTime:         base.HNS(30),
			expectPriceItems: []model.PriceItem{priceItem1, priceItem2},
			failed:           false,
		},
		{
			title:            "one price_item, cross time range",
			effectTime:       base.HNS(5),
			deadTime:         base.HNS(20),
			expectPriceItems: []model.PriceItem{priceItem1},
			failed:           false,
		},
		{
			title:            "one price_item, same time range",
			effectTime:       base.HNS(10),
			deadTime:         base.HNS(20),
			expectPriceItems: []model.PriceItem{priceItem1},
			failed:           false,
		},
	}

	for _, testCase := range testCases {
		param := &model.GetItemPriceByTimeRangeParam{
			PriceID:   publicPriceTable.ID,
			ItemIDs:   []uint64{itemID},
			ZoneID:    zoneID,
			StartTime: testCase.effectTime,
			EndTime:   testCase.deadTime,
		}
		actualPriceItems, err := sandbox.priceDao.PriceItem.GetItemPriceByItemIDZoneIDAlgorithmIDAndTimeRange(param)
		assert.Equal(t, testCase.failed, err != nil, testCase.title)
		if assert.NoError(t, err, testCase.title) {
			// otherwise fails on Travis
			for i := range actualPriceItems {
				actualPriceItems[i].CreatedAt = time.Time{}
				actualPriceItems[i].UpdatedAt = time.Time{}
			}
			assert.ElementsMatch(t, testCase.expectPriceItems, actualPriceItems, testCase.title)
		}
	}
}

func TestBulkInsert(t *testing.T) {
	loc := time.UTC
	ctx := tz.WithRefLocation(context.Background(), loc)

	sandbox := buildSandbox(t)

	dao := sandbox.priceDao.PriceItem

	const priceID = 1
	priceItems := []model.PriceItem{
		{
			PriceID:        priceID,
			ItemID:         1,
			ZoneID:         1,
			AlgorithmID:    3,
			StairPriceType: "UNITPRICE",
			Type:           "XXX",
			IsDisabled:     true,
			UnitRate:       1000,
			EffectTime:     base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, loc)),
			DeadTime:       base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, loc)),

			ShouldSkip:      false,
			CumulativeCycle: model.CumulativeTypeDay,
			BillPeriodType:  model.BillPeriodTypeDay,
		},
		{
			PriceID:        priceID,
			ItemID:         2,
			ZoneID:         1,
			AlgorithmID:    3,
			StairPriceType: "UNITPRICE",
			Type:           "XXX",
			IsDisabled:     true,
			UnitRate:       1024,
			EffectTime:     base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, loc)),
			DeadTime:       base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, loc)),

			ShouldSkip:      false,
			CumulativeCycle: model.CumulativeTypeDay,
			BillPeriodType:  model.BillPeriodTypeDay,
		},
		{
			PriceID:        priceID,
			ItemID:         3,
			ZoneID:         1,
			AlgorithmID:    3,
			StairPriceType: "UNITPRICE",
			Type:           "XXX",
			IsDisabled:     false,
			UnitRate:       1000,
			EffectTime:     base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, loc)),
			DeadTime:       base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, loc)),

			ShouldSkip:      false,
			CumulativeCycle: model.CumulativeTypeDay,
			BillPeriodType:  model.BillPeriodTypeDay,
		},
		{
			PriceID:        priceID + 1,
			ItemID:         1,
			ZoneID:         1,
			AlgorithmID:    3,
			StairPriceType: "UNITPRICE",
			Type:           "XXX",
			IsDisabled:     false,
			UnitRate:       1024,
			EffectTime:     base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, loc)),
			DeadTime:       base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, loc)),

			ShouldSkip:      false,
			CumulativeCycle: model.CumulativeTypeDay,
			BillPeriodType:  model.BillPeriodTypeDay,
		},
	}

	assertFields := func(expect, actual *model.PriceItem, msgAndArgs ...any) {
		assert.Equal(t, expect.PriceID, actual.PriceID, msgAndArgs...)
		assert.Equal(t, expect.ItemID, actual.ItemID, msgAndArgs...)
		assert.Equal(t, expect.ZoneID, actual.ZoneID, msgAndArgs...)
		assert.Equal(t, expect.AlgorithmID, actual.AlgorithmID, msgAndArgs...)
		assert.Equal(t, expect.StairPriceType, actual.StairPriceType, msgAndArgs...)
		assert.Equal(t, expect.Type, actual.Type, msgAndArgs...)
		assert.Equal(t, expect.IsDisabled, actual.IsDisabled, msgAndArgs...)
		assert.Equal(t, expect.UnitRate, actual.UnitRate, msgAndArgs...)
		assert.Equal(t, expect.ShouldSkip, actual.ShouldSkip, msgAndArgs...)
		assert.Equal(t, expect.CumulativeCycle, actual.CumulativeCycle, msgAndArgs...)
		assert.Equal(t, expect.BillPeriodType, actual.BillPeriodType, msgAndArgs...)
	}

	assert.NoError(t, dao.BulkInsert(ctx, priceItems))

	actualPriceItems, err := dao.List(0, -1)
	if assert.NoError(t, err) {
		assert.Len(t, actualPriceItems, len(priceItems))
		for i, priceItem := range actualPriceItems {
			assertFields(&priceItems[i], &priceItem, fmt.Sprintf("Unmatched item, item_id:%d", priceItem.ItemID))
		}
	}
}
