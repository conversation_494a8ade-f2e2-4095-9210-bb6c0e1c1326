package service

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"

	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/pay-sdk/dict"
	"github.com/qbox/pay-sdk/middleware/logging"
)

// 不能使用 Locker 的 LockUser wrapper 的原因是不想跟别的服务（尤其钱包）共享锁的 span
func deriveCouponBizLockKey(uid uint64) string {
	return fmt.Sprintf("walletd:coupon-biz:uid=%d", uid)
}

func (s *WalletBizService) lockUserForCouponBiz(uid uint64) bool {
	return s.lk.Lock(deriveCouponBizLockKey(uid))
}

func (s *WalletBizService) unlockUserForCouponBiz(uid uint64) bool {
	return s.lk.Unlock(deriveCouponBizLockKey(uid))
}

// 根据 Code 补全 CouponDiscountWithScope 和 CouponRebateWithScope 中部分字段缺失的 ID
func (s *WalletBizService) supplementIDByCode(
	ctx context.Context,
	discounts []*CouponDiscountWithScope,
	rebates []*CouponRebateWithScope,
) error {
	logger := logging.GetLogger(ctx)
	startTime := time.Now()
	defer func() {
		logger.Infof("<WalletBizService.supplementIDByCode> supplementIDByCode function costs %v", time.Since(startTime))
	}()

	productCodeIDMap := map[string]uint64{}
	itemGroupCodeIDMap := map[string]uint64{}
	itemCodeIDMap := map[string]uint64{}
	zoneCodeIDMap := map[int64]uint64{}

	for _, discount := range discounts {
		// 获取传了 ZoneCode 却没传 ZoneID 的 Zone
		for _, zone := range discount.Zones {
			// ZoneCode = 0 代表 0 区域，而非空, 因此 ZoneCode 为 0 直接覆盖 ZoneID
			if zone.ZoneCode == 0 {
				zoneCodeIDMap[zone.ZoneCode] = 0
			} else if zone.ZoneID == 0 && zone.ZoneCode != 0 {
				// 传了非零 ZoneCode 且未传 zoneID 的 Zone
				zoneCodeIDMap[zone.ZoneCode] = 0
			}
		}
		if discount.Scope == nil {
			continue
		}
		// 获取传了 ProductCode 却没传 ProductID 的 Product
		for _, product := range discount.Scope.Products {
			if product.ProductID == 0 && product.ProductCode != "" {
				productCodeIDMap[product.ProductCode] = 0
			}
		}
		// 获取传了 GroupCode 却没传 GroupID 的 ItemGroup
		for _, itemGroup := range discount.Scope.ItemGroups {
			if itemGroup.GroupID == 0 && itemGroup.GroupCode != "" {
				itemGroupCodeIDMap[itemGroup.GroupCode] = 0
			}
		}
		// 获取传了 ItemCode 却没传 ItemID 的 Item
		for _, item := range discount.Scope.Items {
			if item.ItemID == 0 && item.ItemCode != "" {
				itemCodeIDMap[item.ItemCode] = 0
			}
		}
	}

	// 同上
	for _, rebate := range rebates {
		for _, zone := range rebate.Zones {
			if zone.ZoneCode == 0 {
				zoneCodeIDMap[zone.ZoneCode] = 0
			} else if zone.ZoneID == 0 && zone.ZoneCode != 0 {
				zoneCodeIDMap[zone.ZoneCode] = 0
			}
		}
		if rebate.Scope == nil {
			continue
		}
		for _, product := range rebate.Scope.Products {
			if product.ProductID == 0 && product.ProductCode != "" {
				productCodeIDMap[product.ProductCode] = 0
			}
		}
		for _, itemGroup := range rebate.Scope.ItemGroups {
			if itemGroup.GroupID == 0 && itemGroup.GroupCode != "" {
				itemGroupCodeIDMap[itemGroup.GroupCode] = 0
			}
		}
		for _, item := range rebate.Scope.Items {
			if item.ItemID == 0 && item.ItemCode != "" {
				itemCodeIDMap[item.ItemCode] = 0
			}
		}
	}

	err := s.supplementIDsForCodeMaps(ctx, productCodeIDMap, itemGroupCodeIDMap, itemCodeIDMap, zoneCodeIDMap)
	if err != nil {
		logger.Errorf("<WalletBizService.SupplementIDByCode>supplementIDsForCodeMaps failed. err: %+v", err)
		return err
	}

	// 补全 ID
	for _, discount := range discounts {
		// 补全没传 ZoneID 的 ZoneGroup
		for _, zone := range discount.Zones {
			if zone.ZoneID == 0 {
				zone.ZoneID = zoneCodeIDMap[zone.ZoneCode]
			}
		}
		if discount.Scope == nil {
			continue
		}
		products := discount.Scope.Products
		itemGroups := discount.Scope.ItemGroups
		items := discount.Scope.Items
		// 补全传了 ProductCode 却没传 ProductID 的 Product
		for i := range discount.Scope.Products {
			if products[i].ProductID == 0 && products[i].ProductCode != "" {
				products[i].ProductID = productCodeIDMap[products[i].ProductCode]
			}
		}
		// 补全传了 GroupCode 却没传 GroupID 的 ItemGroup
		for i := range discount.Scope.ItemGroups {
			if itemGroups[i].GroupID == 0 && itemGroups[i].GroupCode != "" {
				itemGroups[i].GroupID = itemGroupCodeIDMap[itemGroups[i].GroupCode]
			}
		}
		// 补全传了 ItemCode 却没传 ItemID 的 Item
		for i := range items {
			if items[i].ItemID == 0 && items[i].ItemCode != "" {
				items[i].ItemID = itemCodeIDMap[items[i].ItemCode]
			}
		}
	}

	// 同上
	for _, rebate := range rebates {
		for _, zone := range rebate.Zones {
			if zone.ZoneID == 0 {
				zone.ZoneID = zoneCodeIDMap[zone.ZoneCode]
			}
		}
		if rebate.Scope == nil {
			continue
		}
		products := rebate.Scope.Products
		itemGroups := rebate.Scope.ItemGroups
		items := rebate.Scope.Items
		for i := range products {
			if products[i].ProductID == 0 && products[i].ProductCode != "" {
				products[i].ProductID = productCodeIDMap[products[i].ProductCode]
			}
		}
		for i := range itemGroups {
			if itemGroups[i].GroupID == 0 && itemGroups[i].GroupCode != "" {
				itemGroups[i].GroupID = itemGroupCodeIDMap[itemGroups[i].GroupCode]
			}
		}
		for i := range items {
			if items[i].ItemID == 0 && items[i].ItemCode != "" {
				items[i].ItemID = itemCodeIDMap[items[i].ItemCode]
			}
		}
	}
	return nil
}

// supplementIDsForCodeMaps 给 map 中对应的 Code 补充 ID
func (s *WalletBizService) supplementIDsForCodeMaps(
	ctx context.Context,
	productCodeIDMap map[string]uint64,
	itemGroupCodeIDMap map[string]uint64,
	itemCodeIDMap map[string]uint64,
	zoneCodeIDMap map[int64]uint64,
) error {
	logger := logging.GetLogger(ctx)

	// 根据 ProductCode 批量获取 ProductID
	err := s.replenishProductIDsForCodeMap(ctx, productCodeIDMap)
	if err != nil {
		logger.Errorf("<WalletBizService.supplementIDsForCodeMaps>replenishProductIDsForCodeMap failed. err: %+v", err)
		return err
	}

	// 根据 GroupCode 批量获取 GroupID
	err = s.replenishItemGroupIDsForCodeMap(ctx, itemGroupCodeIDMap)
	if err != nil {
		logger.Errorf("<WalletBizService.supplementIDsForCodeMaps>replenishItemGroupIDsForCodeMap failed. err: %+v", err)
		return err
	}

	// 获取 itemCode 列表
	itemCodes := make([]string, len(itemCodeIDMap))
	for key := range itemCodeIDMap {
		itemCodes = append(itemCodes, key)
	}

	// MultiCodeParam 的 Codes 参数不能为空
	if len(itemCodes) > 0 {
		// 根据 ItemCode 批量获取 ItemID
		items, err := s.dictClient.MultiGetItemByCodes(ctx, &dict.MultiCodeParam{Codes: itemCodes})
		if err != nil {
			logger.Errorf("<WalletBizService.supplementIDsForCodeMaps>MultiGetItemByCodes failed. err: %+v", err)
			return err
		}
		for _, item := range items.Items {
			itemCodeIDMap[item.Code] = item.Id
		}
	}

	// 获取 ZoneCode 列表
	zoneCodes := make([]int64, len(zoneCodeIDMap))
	for key := range zoneCodeIDMap {
		zoneCodes = append(zoneCodes, key)
	}
	// MultiZoneCodeParam 的 Codes 参数不能为空
	if len(zoneCodes) > 0 {
		// 根据 ZoneCode 批量获取 ZoneID
		zoneByCodes, err := s.dictClient.MultiGetZoneByCodes(ctx, &dict.MultiZoneCodeParam{Codes: zoneCodes})
		if err != nil {
			logger.Errorf("<WalletBizService.supplementIDsForCodeMaps>MultiGetZoneByCodes failed. err: %+v", err)
			return err
		}
		for _, zone := range zoneByCodes.Zones {
			zoneCodeIDMap[zone.Code] = zone.Id
		}
	}
	return nil
}

// codeIDPair 用于存储 code 和 ID 的键值对
type codeIDPair struct {
	code string
	ID   uint64
}

// replenishProductIDsForCodeMap 根据 ProductCode 获取对应的 ProductID
func (s *WalletBizService) replenishProductIDsForCodeMap(
	ctx context.Context,
	productCodeIDMap map[string]uint64,
) error {
	logger := logging.GetLogger(ctx)

	productCodes := lo.Keys(productCodeIDMap)
	oks, err := resultgroup.ParallelMap(productCodes, func(key string) (codeIDPair, error) {
		productByCode, err := s.dictClient.GetProductByCode(ctx, &dict.CodeParam{Code: key})
		if err != nil {
			logger.Errorf("<WalletBizService.replenishProductIDsForCodeMap> GetProductByCode failed. Code: %v, err: %+v", key, err)
			return codeIDPair{}, err
		}
		return codeIDPair{code: key, ID: productByCode.Id}, nil
	})
	if err != nil {
		logger.Errorf("<WalletBizService.replenishProductIDsForCodeMap> resultGroup get ProductID by ProductCode failed: err: %+v", err)
		return err
	}

	for _, ok := range oks {
		productCodeIDMap[ok.code] = ok.ID
	}

	return nil
}

// replenishItemGroupIDsForCodeMap 根据 GroupCode 获取 对应的 GroupID
func (s *WalletBizService) replenishItemGroupIDsForCodeMap(
	ctx context.Context,
	itemGroupCodeIDMap map[string]uint64,
) error {
	logger := logging.GetLogger(ctx)

	itemGroupCodes := lo.Keys(itemGroupCodeIDMap)
	oks, err := resultgroup.ParallelMap(itemGroupCodes, func(key string) (codeIDPair, error) {
		itemGroupByCode, err := s.dictClient.GetItemGroupByCode(ctx, &dict.CodeParam{Code: key})
		if err != nil {
			logger.Errorf("<WalletBizService.replenishItemGroupIDsForCodeMap> GetItemGroupByCode failed. Code: %v, err: %+v", key, err)
			return codeIDPair{}, err
		}
		return codeIDPair{code: key, ID: itemGroupByCode.Id}, nil
	})
	if err != nil {
		logger.Errorf("<WalletBizService.replenishItemGroupIDsForCodeMap> resultGroup get GroupID by GroupCode failed: err: %+v", err)
		return err
	}

	for _, ok := range oks {
		itemGroupCodeIDMap[ok.code] = ok.ID
	}

	return nil
}
