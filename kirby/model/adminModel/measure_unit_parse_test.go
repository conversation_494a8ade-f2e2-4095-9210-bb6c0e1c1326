package adminModel

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestParseMeasureUnitRepr(t *testing.T) {
	testcases := []struct {
		input    string
		ok       bool
		expected *ParsedMeasureUnit
	}{
		{
			input:    "",
			expected: nil,
		},
		{
			input:    "a-张",
			expected: nil,
		},
		{
			input:    "0xa-张",
			expected: nil,
		},
		{
			input:    "-foo",
			expected: nil,
		},
		{
			input:    "gb",
			expected: nil,
		},
		{
			input:    "Bps",
			expected: nil,
		},
		{
			input: "1",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindDimensionless,
				classifier:          "",
				premultipliedFactor: 1,
			},
		},
		{
			input: "233",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindDimensionless,
				classifier:          "",
				premultipliedFactor: 233,
			},
		},
		{
			input: "1000",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindDimensionless,
				classifier:          "",
				premultipliedFactor: 1000,
			},
		},
		{
			input: "1-次",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindDimensionless,
				classifier:          "次",
				premultipliedFactor: 1,
			},
		},
		{
			input: "10-张",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindDimensionless,
				classifier:          "张",
				premultipliedFactor: 10,
			},
		},
		{
			input: "10--张",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindDimensionless,
				classifier:          "-张",
				premultipliedFactor: 10,
			},
		},
		{
			input: "1-很多个字的量词",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindDimensionless,
				classifier:          "很多个字的量词",
				premultipliedFactor: 1,
			},
		},
		{
			input: "GB",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindData,
				classifier:          "",
				premultipliedFactor: 1_000_000_000,
			},
		},
		{
			input: "GiB",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindData,
				classifier:          "",
				premultipliedFactor: 1 << 30,
			},
		},
		{
			input: "bps",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindBandwidth,
				classifier:          "",
				premultipliedFactor: 1,
			},
		},
		{
			input: "min",
			expected: &ParsedMeasureUnit{
				Kind:                MeasureUnitKindDuration,
				classifier:          "",
				premultipliedFactor: ScalingFactor(time.Minute),
			},
		},
	}

	for _, tc := range testcases {
		actual, err := ParseMeasureUnitRepr(tc.input)
		if tc.expected != nil {
			assert.NoError(t, err, tc.input)
			assert.Equal(t, tc.expected, actual, tc.input)

			// test round-trip
			rt, err := actual.Repr()
			assert.NoError(t, err, tc.input)
			assert.Equal(t, tc.input, rt, tc.input)
		} else {
			assert.Error(t, err, tc.input)
			assert.Nil(t, actual, tc.input)
		}
	}
}
