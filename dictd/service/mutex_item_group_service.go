package service

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/pay-sdk/middleware/logging"
	"qiniu.io/pay/dictd/model"
)

func (s *DictBizService) CreateOrUpdateMutexItemGroup(ctx context.Context, group *model.MutexItemGroup) error {
	logger := logging.GetLogger(ctx)
	err := group.Validate()
	if err != nil {
		return err
	}

	mutexGroups := make([][]string, 0)
	for _, mutexGroup := range group.MutexGroups {
		if len(mutexGroup) != 0 {
			mutexGroups = append(mutexGroups, mutexGroup)
		}
	}

	err = s.validateItemCodes(mutexGroups)
	if err != nil {
		logger.WithError(err).
			WithField("mutexItemGroup", group).
			Error("invalid item code")
		return err
	}

	mutexGroupBytes, err := json.Marshal(mutexGroups)
	if err != nil {
		logger.WithError(err).
			With<PERSON>ield("mutexItemGroup", group).
			Error("marshal mutex groups failed")
		return err
	}
	err = s.dictDao.MutexItemGroup.Save(&model.MutexItemGroupTable{
		ID:             group.ID,
		Name:           group.Name,
		MutexGroups:    string(mutexGroupBytes),
		MustEnableItem: group.MustEnabledItem,
		IsDeleted:      false,
	})
	if err != nil {
		logger.WithError(err).
			WithField("mutexItemGroup", group).
			Error("create mutex item group failed")
		return err
	}
	return nil
}

func (s *DictBizService) ListAllMutexItemGroups(ctx context.Context) ([]*model.MutexItemGroup, error) {
	logger := logging.GetLogger(ctx)
	mutexItemGroupTables, err := s.dictDao.MutexItemGroup.List(s.cacheExpires)
	if err != nil {
		logger.WithError(err).Error("list mutex item groups failed")
		return nil, err
	}
	result := make([]*model.MutexItemGroup, 0)
	for _, table := range mutexItemGroupTables {
		groupsStr := table.MutexGroups
		var mutexGroups [][]string
		e := json.Unmarshal([]byte(groupsStr), &mutexGroups)
		if e != nil {
			logger.WithError(err).Error("unmarshal mutex groups failed")
			return nil, e
		}
		result = append(result, &model.MutexItemGroup{
			ID:              table.ID,
			Name:            table.Name,
			MutexGroups:     mutexGroups,
			MustEnabledItem: table.MustEnableItem,
			UpdatedAt:       table.UpdatedAt,
		})
	}
	return result, nil
}

func (s *DictBizService) validateItemCodes(codes [][]string) error {
	// 判断 item code 是否存在
	_, errs := resultgroup.ParallelMapWithErrors(codes, func(itemCodes []string) ([]model.Item, error) {
		items, err := s.dictDao.Item.ListByCodes(itemCodes)
		if err != nil {
			return nil, err
		}
		if len(items) != len(itemCodes) {
			return nil, fmt.Errorf("non-existent item code in %+v", itemCodes)
		}
		return items, nil
	})
	for _, err := range errs {
		if err != nil {
			return err
		}
	}
	return nil
}
