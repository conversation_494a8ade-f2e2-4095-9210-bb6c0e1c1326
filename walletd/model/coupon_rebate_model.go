package model

import (
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// CouponRebate model definition
type CouponRebate struct {
	ID          uint64 `gorm:"primary_key"`
	Code        string `gorm:"type:varchar(128);unique_index:idx_coupon_rebate_code"`
	Type        string `gorm:"type:varchar(128)"`
	ScopeID     uint64 `gorm:"index:idx_coupon_rebate_scope_id"`
	Name        string `gorm:"type:varchar(128)"`
	Description string `gorm:"type:varchar(512)"`
	Remark      string `gorm:"type:varchar(512)"`
	Threshold   uint64
	Money       int64
	EffectTime  base.HNS
	DeadTime    base.HNS
	CreatedAt   time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt   time.Time `sql:"type:DATETIME(6)"`
	// CurrencyType 币种（如 CNY USD）
	CurrencyType base.CurrencyType `gorm:"type:VARCHAR(10);not null"`
}

// CouponRebateDao is data access object of CouponRebate model
type CouponRebateDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

func (d *CouponRebateDao) GetCachePrefix() string {
	return "wallet:coupon_rebate:"
}

func (d *CouponRebateDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewCouponRebateDao is constructor of CouponRebateDao
func NewCouponRebateDao(base *dao.BaseDao) *CouponRebateDao {
	cachePrefix := (*CouponRebateDao)(nil).GetCachePrefix()
	return &CouponRebateDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of CouponRebate by id
func (d *CouponRebateDao) GetByID(id uint64, expires ...time.Duration) (*CouponRebate, error) {
	model := &CouponRebate{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// GetByCode select a record of CouponRebate by code
func (d *CouponRebateDao) GetByCode(code string, expires ...time.Duration) (*CouponRebate, error) {
	model := &CouponRebate{Code: code}
	keys := dao.NewCacheKeysFmt(
		"%s:code:%s",
		d.cachePrefix, code,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, "`coupon_rebates`.`code` = ?", code).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("code", code)
	}
	return model, nil
}

// ListByConds select records by conditions
func (d *CouponRebateDao) ListByConds(
	p *ListCouponConds,
	expires ...time.Duration,
) (list []CouponRebate, err error) {
	where, args, err := p.toWhereClause()
	if err != nil {
		return nil, err
	}

	keys := dao.NewCacheKeysFmt(
		"%s:list:pat=%s&offset=%d&limit=%d",
		d.cachePrefix,
		p.CodePattern,
		p.Offset,
		p.Limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&CouponRebate{}).
			Where(where, args...).
			Offset(p.Offset).
			Limit(p.Limit).
			Order("created_at DESC").
			Find(value).
			Error
	}, keys, &list, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"code_pattern": p.CodePattern,
			"offset":       p.Offset,
			"limit":        p.Limit,
		})
	}
	return
}

// CountAll select count of all records
func (d *CouponRebateDao) CountAll(expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:count",
		d.cachePrefix,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&CouponRebate{}).Count(value).Error
	}, keys, &count, expires...)
	return
}

// ListByScopeID select records by scope_id
func (d *CouponRebateDao) ListByScopeID(scopeID uint64, offset, limit int, expires ...time.Duration) (list []CouponRebate, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:scope_id=%d&offset=%d&limit=%d",
		d.cachePrefix, scopeID, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&CouponRebate{ScopeID: scopeID}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &list, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return
}

// CountByScopeID select count of records by scope_id
func (d *CouponRebateDao) CountByScopeID(scopeID uint64, expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:count:scope_id=%d",
		d.cachePrefix, scopeID,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&CouponRebate{}).Where(&CouponRebate{ScopeID: scopeID}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
		})
	}
	return
}

// ListByIDs select records by ids
func (d *CouponRebateDao) ListByIDs(ids []uint64, expires ...time.Duration) (list []CouponRebate, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:ids=%s",
		d.cachePrefix, base.UintSliceJoin(ids, ","),
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where("`id` in (?)", ids).Find(value).Error
	}, keys, &list, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"ids": ids,
		})
	}
	return
}

// Save insert or update a record of CouponRebate by id
func (d *CouponRebateDao) Save(model *CouponRebate, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// Delete delete a record of CouponRebate
func (d *CouponRebateDao) Delete(model *CouponRebate) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
}
