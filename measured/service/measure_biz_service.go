package service

import (
	"time"

	"github.com/qbox/bo-base/v4/lock"
	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service/stat"
)

// MeasureBizService contains business logic for measured service.
type MeasureBizService struct {
	dao          *model.MeasureDao
	cacheExpires time.Duration

	statClient stat.Client
	lk         lock.Locker
}

// NewMeasureBizService constructs a MeasureBizService.
func NewMeasureBizService(
	dao *model.MeasureDao,
	statClient stat.Client,
	userLocker lock.Locker,
	expires time.Duration,
) *MeasureBizService {
	return &MeasureBizService{
		dao:          dao,
		cacheExpires: expires,

		statClient: statClient,
		lk:         userLocker,
	}
}
