package adapter

import (
	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/payment/model"
)

// BuildOrderRechargeRequest convert pb order recharge request
func BuildOrderRechargeRequest(pbOrderRechargeReq *pb.OrderRechargeRequest) *model.OrderRechargeRequest {
	if pbOrderRechargeReq == nil {
		return nil
	}
	return &model.OrderRechargeRequest{
		Uid:    pbOrderRechargeReq.GetUid(),
		Money:  base.Money(pbOrderRechargeReq.GetMoney()),
		Excode: pbOrderRechargeReq.GetExcode(),
		Desc:   pbOrderRechargeReq.GetDesc(),
		Prefix: pbOrderRechargeReq.GetPrefix(),
		Type:   pbOrderRechargeReq.GetType(),
	}
}

// BuildPORequests converts []*pb.ProductOrderPaymentRequest to []model.DeductPORequest
func BuildPORequests(pbPOPaymentReqs []*pb.ProductOrderPaymentRequest) []model.DeductPORequest {
	poRequests := make([]model.DeductPORequest, len(pbPOPaymentReqs))

	for i, poReq := range pbPOPaymentReqs {
		poRequests[i] = model.DeductPORequest{
			OrderHash:      poReq.OrderHash,
			ProductOrderId: poReq.ProductOrderId,
			Money:          base.Money(poReq.Money),
			Description:    poReq.Description,
		}
	}
	return poRequests
}

// BuildDeductOrderRequest converts pb order deduct request
func BuildDeductOrderRequest(request *pb.OrderPaymentRequest) *model.DeductOrderRequest {
	return &model.DeductOrderRequest{
		Uid:                  request.GetUid(),
		PayMode:              enums.PayMode(request.GetPayMode()),
		UnionOrderHash:       request.GetUnionOrderHash(),
		PORequests:           BuildPORequests(request.GetProductOrderRequests()),
		OrderRechargeRequest: BuildOrderRechargeRequest(request.GetRechargeRequest()),
	}
}

// BuildRefundOrderRequest convert pb order refund request
func BuildRefundOrderRequest(request *pb.OrderRefundRequest) *model.RefundOrderRequest {
	poRequest := make([]model.RefundPORequest, len(request.ProductOrderRefundRequests))

	for i, poReq := range request.ProductOrderRefundRequests {
		poRequest[i] = model.RefundPORequest{
			ProductOrderId: poReq.ProductOrderId,
			Money:          poReq.Money,
			Description:    poReq.Description,
		}
	}

	return &model.RefundOrderRequest{
		Uid:        request.Uid,
		OrderHash:  request.OrderHash,
		RefundMode: enums.RefundMode(request.RefundMode),
		PORequests: poRequest,
	}
}
