package action

import (
	"github.com/qbox/bo-base/v4/action"
	"github.com/qbox/bo-sdk/kirbypb"
	kirbyadminpb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/service"
)

// KirbyAction defines KirbyAction
type KirbyAction struct {
	*action.BaseAction
	kirbypb.UnimplementedKirbyServiceServer
	kirbyadminpb.UnimplementedKirbyAdminServiceServer

	srv *service.KirbyService
}

var _ kirbypb.KirbyServiceServer = (*KirbyAction)(nil)
var _ kirbyadminpb.KirbyAdminServiceServer = (*KirbyAction)(nil)

// NewKirbyAction new kirby action
func NewKirbyAction(
	srv *service.KirbyService,
	defaultPageSize uint64,
) *KirbyAction {
	return &KirbyAction{
		BaseAction: action.NewBaseAction(defaultPageSize),
		srv:        srv,
	}
}
