package model

import (
	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/dao"
)

// MeasureDao is data access object for measured service.
type MeasureDao struct {
	ItemDataTypeExtraArg *ItemDataTypeExtraArgDao
	ItemExtraArg         *ItemExtraArgDao
	ItemStatConfig       *ItemStatConfigDao
	Package              *PackageDao
	PackageItem          *PackageItemDao
	PackageUserMap       *PackageUserMapDao
}

// NewMeasureDao constructs a MeasureDao.
func NewMeasureDao(base *dao.BaseDao) *MeasureDao {
	return &MeasureDao{
		ItemDataTypeExtraArg: NewItemDataTypeExtraArgDao(base),
		ItemExtraArg:         NewItemExtraArgDao(base),
		ItemStatConfig:       NewItemStatConfigDao(base),
		Package:              NewPackageDao(base),
		PackageItem:          NewPackageItemDao(base),
		PackageUserMap:       NewPackageUserMapDao(base),
	}
}

// RegisterMigrate migrate all models
func RegisterMigrate(db *gorm.DB) {
	db.AutoMigrate(&Package{})
	db.AutoMigrate(&PackageItem{})
	db.AutoMigrate(&PackageUserMap{})
	db.AutoMigrate(&ItemExtraArg{})
	db.AutoMigrate(&ItemDataTypeExtraArg{})
	db.AutoMigrate(&ItemStatConfig{})
}
