package adminModel

import (
	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/dao"
)

// KirbyAdminDao is data access object for kirby admin service.
type KirbyAdminDao struct {
	base   *dao.BaseDao
	Client *ClientDao
	RTD    *ResourceTypeDefinitionDao
	MMD    *MeasureMetricDefinitionDao
	SPD    *SeriesPropertyDefinitionDao
	AggR   *AggregationRuleDao
}

// NewKirbyAdminDao constructs a KirbyAdminDao.
func NewKirbyAdminDao(base *dao.BaseDao) *KirbyAdminDao {
	return &KirbyAdminDao{
		base:   base,
		Client: NewClientDao(base),
		RTD:    NewResourceTypeDefinitionDao(base),
		MMD:    NewMeasureMetricDefinitionDao(base),
		SPD:    NewSeriesPropertyDefinitionDao(base),
		AggR:   NewAggregationRuleDao(base),
	}
}

// DoTransaction do a transaction
func (d *KirbyAdminDao) DoTransaction(fn func(*KirbyAdminDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		newDao := NewKirbyAdminDao(base)
		return fn(newDao)
	})
}

// RegisterMigrate migrate all models
func RegisterMigrate(db *gorm.DB) {
	db.AutoMigrate(&Client{})
	db.AutoMigrate(&ResourceTypeDefinition{})
	db.AutoMigrate(&MeasureMetricDefinition{})
	db.AutoMigrate(&SeriesPropertyDefinition{})
	db.AutoMigrate(&AggregationRule{})
}
