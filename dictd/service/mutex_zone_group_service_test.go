package service_test

import (
	"context"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestMutexZoneGroupService_CreateOrUpdateZoneGroup(t *testing.T) {
	sandbox := buildSandbox(t)
	ctx := context.TODO()

	// 不存在的 item code
	err := sandbox.dictService.CreateOrUpdateMutexZoneGroup(ctx, &model.MutexZoneGroup{
		ItemCode: "LikeARollingStone",
		MutexGroups: [][]int64{
			{114514},
			{1919810},
		},
	})
	assert.NotNil(t, err)

	itemCode := "cdn:http"
	item, err := sandbox.dictService.CreateItem(ctx, &model.Item{Code: itemCode})
	assert.Nil(t, err)
	// empty mutex groups
	err = sandbox.dictService.CreateOrUpdateMutexZoneGroup(ctx, &model.MutexZoneGroup{
		ItemCode:    itemCode,
		MutexGroups: nil,
	})
	assert.NotNil(t, err)

	// 计费项不存在的 zone
	err = sandbox.dictService.CreateOrUpdateMutexZoneGroup(ctx, &model.MutexZoneGroup{
		ItemCode: itemCode,
		MutexGroups: [][]int64{
			{114514},
			{1919810},
		},
	})
	assert.NotNil(t, err)

	for i := 1; i < 4; i++ {
		zone, err := sandbox.dictService.CreateZone(ctx, &model.Zone{
			Code: int64(3000 + i),
			Name: strconv.FormatInt(int64(i), 10),
		})
		assert.Nil(t, err)
		_, err = sandbox.dictService.CreateZoneItemMap(ctx, &model.ZoneItemMap{
			ZoneID: zone.ID,
			ItemID: item.ID,
		})
		assert.Nil(t, err)
	}

	mutexGroups := [][]int64{
		{3001, 3003},
		{3002},
	}
	err = sandbox.dictService.CreateOrUpdateMutexZoneGroup(ctx, &model.MutexZoneGroup{
		ItemCode:    itemCode,
		MutexGroups: mutexGroups,
	})
	assert.Nil(t, err)

	mutexZoneGroups, err := sandbox.dictService.ListMutexZoneGroupByItemCode(ctx, []string{itemCode})
	assert.Nil(t, err)
	assert.Equal(t, 1, len(mutexZoneGroups))
	assert.Equal(t, 2, len(mutexZoneGroups[0].MutexGroups))
	assert.Equal(t, mutexGroups, mutexZoneGroups[0].MutexGroups)
	assert.Equal(t, itemCode, mutexZoneGroups[0].ItemCode)

	allMutexZoneGroups, err := sandbox.dictService.ListMutexZoneGroups(ctx)
	assert.Nil(t, err)
	assert.Equal(t, mutexZoneGroups, allMutexZoneGroups)
}

func TestMutexZoneGroupService_ListMutexZoneGroupByItemCode(t *testing.T) {
	sandbox := buildSandbox(t)
	ctx := context.TODO()

	itemCode := "cdn:http"
	item, err := sandbox.dictService.CreateItem(ctx, &model.Item{Code: itemCode})
	assert.Nil(t, err)

	anotherItemCode := "cdn:https"
	anotherItem, err := sandbox.dictService.CreateItem(ctx, &model.Item{Code: anotherItemCode})
	assert.Nil(t, err)

	emptyResult, err := sandbox.dictService.ListMutexZoneGroupByItemCode(ctx, []string{itemCode})
	assert.Nil(t, err)
	assert.Equal(t, 0, len(emptyResult))

	for i := 1; i < 4; i++ {
		zone, err := sandbox.dictService.CreateZone(ctx, &model.Zone{
			Code: int64(3000 + i),
			Name: strconv.FormatInt(int64(i), 10),
		})
		assert.Nil(t, err)
		_, err = sandbox.dictService.CreateZoneItemMap(ctx, &model.ZoneItemMap{
			ZoneID: zone.ID,
			ItemID: item.ID,
		})
		assert.Nil(t, err)
	}

	for i := 4; i < 8; i++ {
		zone, err := sandbox.dictService.CreateZone(ctx, &model.Zone{
			Code: int64(3000 + i),
			Name: strconv.FormatInt(int64(i), 10),
		})
		assert.Nil(t, err)
		_, err = sandbox.dictService.CreateZoneItemMap(ctx, &model.ZoneItemMap{
			ZoneID: zone.ID,
			ItemID: anotherItem.ID,
		})
		assert.Nil(t, err)
	}
	mutexGroups := [][]int64{
		{3001, 3003},
		{3002},
	}
	err = sandbox.dictService.CreateOrUpdateMutexZoneGroup(ctx, &model.MutexZoneGroup{
		ItemCode:    itemCode,
		MutexGroups: mutexGroups,
	})
	assert.Nil(t, err)

	anotherMutexGroups := [][]int64{
		{3004, 3005},
		{3006},
		{3007},
	}
	err = sandbox.dictService.CreateOrUpdateMutexZoneGroup(ctx, &model.MutexZoneGroup{
		ItemCode:    anotherItemCode,
		MutexGroups: anotherMutexGroups,
	})
	assert.Nil(t, err)

	mutexZoneGroups, err := sandbox.dictService.ListMutexZoneGroupByItemCode(ctx, []string{itemCode, anotherItemCode})
	assert.Nil(t, err)
	assert.Equal(t, 2, len(mutexZoneGroups))

	allMutexZoneGroups, err := sandbox.dictService.ListMutexZoneGroups(ctx)
	assert.Nil(t, err)
	assert.Equal(t, mutexZoneGroups, allMutexZoneGroups)
}
