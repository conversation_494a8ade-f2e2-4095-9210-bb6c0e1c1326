// Code generated by MockGen. DO NOT EDIT.
// Source: measured/service/stat/cache_helper.go

package stat

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// Mockcacheable is a mock of cacheable interface.
type Mockcacheable struct {
	ctrl     *gomock.Controller
	recorder *MockcacheableMockRecorder
}

// MockcacheableMockRecorder is the mock recorder for Mockcacheable.
type MockcacheableMockRecorder struct {
	mock *Mockcacheable
}

// NewMockcacheable creates a new mock instance.
func NewMockcacheable(ctrl *gomock.Controller) *Mockcacheable {
	mock := &Mockcacheable{ctrl: ctrl}
	mock.recorder = &MockcacheableMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *Mockcacheable) EXPECT() *MockcacheableMockRecorder {
	return m.recorder
}

// ToCacheKey mocks base method.
func (m *Mockcacheable) ToCacheKey() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ToCacheKey")
	ret0, _ := ret[0].(string)
	return ret0
}

// ToCacheKey indicates an expected call of ToCacheKey.
func (mr *MockcacheableMockRecorder) ToCacheKey() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ToCacheKey", reflect.TypeOf((*Mockcacheable)(nil).ToCacheKey))
}
