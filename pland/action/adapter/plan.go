package adapter

import (
	"errors"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	baseEnums "github.com/qbox/bo-base/v4/biz/enums"
	pb "github.com/qbox/pay-sdk/plan"
	"qiniu.io/pay/pland/service"

	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/models/enums"
)

// BuildPlan convert pb.PlanParam to models.Plan
func BuildPlan(in *pb.PlanParam) (*models.Plan, error) {

	packModel := &models.PlanPackModel{
		Packs: make([]models.PlanPacks, 0),
	}

	for _, packs := range in.GetPacks() {
		rights := make([]models.PackRights, 0, len(packs.GetRights()))
		for _, packRights := range packs.GetRights() {
			rights = append(rights, models.PackRights{
				Code:     packRights.GetCode(),
				Name:     packRights.GetName(),
				Type:     enums.RightsType(packRights.GetType()),
				ItemCode: packRights.GetItemCode(),
				IsCombo:  baseEnums.IsComboItem(packRights.GetItemCode()),
				Zone:     packRights.GetZone(),
				ZoneName: packRights.GetZoneName(),
				UnitStr:  packRights.GetUnitStr(),
				Quota:    packRights.GetQuota(),
				Discount: packRights.GetDiscount(),
			})
		}
		packModel.Packs = append(packModel.Packs, models.PlanPacks{
			Code:        packs.GetCode(),
			Name:        packs.GetName(),
			Description: packs.GetDescription(),
			Rights:      rights,
		})
	}
	return &models.Plan{
		Description:  in.GetDescription(),
		Level:        enums.PlanLevel(in.GetLevel()),
		Price:        base.MustParseNMoney(in.GetPrice()),
		CurrencyType: base.CurrencyType(in.GetCurrencyType()),
		EffectTime:   in.GetEffectTime().AsTime().Unix(),
		Comment:      in.GetComment(),
		Packs:        packModel,
		Draft:        in.Draft,
	}, nil
}

// BuildPbPlan convert models.Plan to pb.Plan
func BuildPbPlan(plan *models.Plan) (*pb.Plan, error) {

	return &pb.Plan{
		Version:      plan.Version,
		Description:  plan.Description,
		Level:        plan.Level.String(),
		Price:        plan.Price.String(),
		Status:       pb.PlanStatus(plan.Status),
		CurrencyType: plan.CurrencyType.String(),
		EffectTime:   timestamppb.New(time.Unix(plan.EffectTime, 0)),
		TimerFlag:    plan.TimerFlag,
		CreatedAt:    timestamppb.New(plan.CreatedAt),
		Comment:      plan.Comment,
		Packs:        buildPbPlanPack(plan.Packs),
	}, nil
}
func buildPbPlanPack(ppm *models.PlanPackModel) []*pb.PlanPacks {
	if ppm == nil {
		return nil
	}
	res := make([]*pb.PlanPacks, len(ppm.Packs))
	for i, packs := range ppm.Packs {
		res[i] = &pb.PlanPacks{
			Code:        packs.Code,
			Name:        packs.Name,
			Description: packs.Description,
			Rights:      buildPbPackRights(packs.Rights),
		}
	}
	return res
}
func buildPbPackRights(rights []models.PackRights) []*pb.PackRights {
	res := make([]*pb.PackRights, len(rights))
	for i, right := range rights {
		res[i] = &pb.PackRights{
			Code:     right.Code,
			Name:     right.Name,
			Type:     right.Type.String(),
			ItemCode: right.ItemCode,
			IsCombo:  right.IsCombo,
			Zone:     right.Zone,
			ZoneName: right.ZoneName,
			UnitStr:  right.UnitStr,
			Quota:    right.Quota,
			Discount: right.Discount,
		}
	}
	return res
}

// BuildPbPlanList convert []models.Plan to pb.PlanList
func BuildPbPlanList(plans []*models.Plan) (*pb.PlanList, error) {
	list := make([]*pb.Plan, len(plans))
	for i, plan := range plans {
		pbPlan, err := BuildPbPlan(plan)
		if err != nil {
			return nil, err
		}
		list[i] = pbPlan
	}
	return &pb.PlanList{
		List: list,
	}, nil
}

// BuildPbUserPlanList convert to pb user plan list
func BuildPbUserPlanList(
	userPlans []*models.UserPlan,
	planMap map[string]*models.Plan,
) (*pb.UserPlanList, error) {

	res := make([]*pb.UserPlan, 0, len(userPlans))

	for _, usrPlan := range userPlans {
		plan, ok := planMap[service.GenPlanIndexString(usrPlan.Version, usrPlan.Level)]
		if !ok {
			return nil, errors.New("plan not found")
		}

		up := &pb.UserPlan{
			Uid:                  usrPlan.UID,
			Version:              usrPlan.Version,
			Level:                usrPlan.Level.String(),
			PacksCode:            usrPlan.PacksCode,
			RemainingSwitchCount: usrPlan.RemainingSwitchCount,
			Month:                timestamppb.New(usrPlan.Month),
			PacksName:            "",
		}
		if usrPlan.Level == enums.LevelEnterprise {
			// 企业定制 plan 没有 packs 相关信息
			res = append(res, up)
			continue
		}
		pack, ok := plan.SearchPacks(usrPlan.PacksCode)
		if !ok {
			return nil, errors.New("packs not found")
		}
		up.PacksName = pack.Name

		res = append(res, up)
	}

	return &pb.UserPlanList{
		List: res,
	}, nil
}
