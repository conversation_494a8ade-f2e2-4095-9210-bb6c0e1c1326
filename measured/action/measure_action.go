package action

import (
	"gopkg.in/go-playground/validator.v9"

	"github.com/qbox/bo-base/v4/action"
	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/service"
)

// MeasureAction is implementation of the measured gRPC interface.
type MeasureAction struct {
	*action.BaseAction
	pb.UnimplementedPayMeasureServiceServer

	srv             *service.MeasureBizService
	defaultPageSize uint64
	validate        *validator.Validate
}

// NewMeasureAction constructs a MeasureAction.
func NewMeasureAction(
	srv *service.MeasureBizService,
	defaultPageSize uint64,
) *MeasureAction {
	return &MeasureAction{
		BaseAction:      action.NewBaseAction(defaultPageSize),
		srv:             srv,
		defaultPageSize: defaultPageSize,
		validate:        validator.New(),
	}
}
