package action_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/pay-sdk/dict"
)

func TestZoneItemMapInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	dictClient := sandbox.dictClient

	zims := []dict.ZoneItemMap{
		{
			ZoneId:    1,
			ItemId:    1,
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
		{
			ZoneId:    1,
			ItemId:    2,
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
		{
			ZoneId:    1,
			ItemId:    3,
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
		{
			ZoneId:    2,
			ItemId:    4,
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
		{
			ZoneId:    3,
			ItemId:    4,
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
		{
			ZoneId:    4,
			ItemId:    4,
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
	}
	// 创建对应的 zone 和 item
	for i := 1; i <= 4; i++ {
		dictClient.CreateItem(context.Background(), &dict.Item{
			Code: fmt.Sprintf("cdn:%d", i),
		})
		dictClient.CreateZone(context.Background(), &dict.Zone{
			Code: int64(i),
			Name: fmt.Sprintf("zone %d", i),
		})
	}
	assertFields := func(zim *dict.ZoneItemMap, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(zims) }) {
			assert.Equal(t, zims[n].GetZoneId(), zim.GetZoneId(), msgAndArgs...)
			assert.Equal(t, zims[n].GetItemId(), zim.GetItemId(), msgAndArgs...)
		}
	}
	ids := make(map[uint64]int, len(zims))
	for n, zim := range zims {
		_zim, err := dictClient.CreateZoneItemMap(context.Background(), &zim)
		if assert.NoError(t, err, "CreateZoneItemMap") {
			ids[_zim.GetId()] = n
			assertFields(_zim, n, "CreateZoneItemMap")
		}
	}
	count, err := dictClient.CountZoneItemMapsByZoneID(context.Background(), &dict.IDParam{Id: 1})
	if assert.NoError(t, err, "CountZoneItemMapsByZoneID") {
		assert.Equal(t, uint64(3), count.GetCount(), "CountZoneItemMapsByZoneID")
	}
	count, err = dictClient.CountZoneItemMapsByItemID(context.Background(), &dict.IDParam{Id: 4})
	if assert.NoError(t, err, "CountZoneItemMapsByItemID") {
		assert.Equal(t, uint64(3), count.GetCount(), "CountZoneItemMapsByItemID")
	}
	list, err := dictClient.ListZoneItemMapsByZoneID(context.Background(), &dict.IDPagingParam{Id: 1, Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListZoneItemMapsByZoneID") {
		assert.Equal(t, uint64(3), list.GetCount(), "ListZoneItemMapsByZoneID")
		for n, zim := range list.GetZoneItemMaps() {
			assertFields(zim, n, "ListZoneItemMapsByZoneID")
			zim, err := dictClient.GetZoneItemMapByID(context.Background(), &dict.IDParam{Id: zim.GetId()})
			if assert.NoError(t, err, "GetZoneItemMapByID") {
				assertFields(zim, n, "GetZoneItemMapByID")
			}
			zim.ItemId += 10
			zims[n].ItemId = zim.ItemId
			zim, err = dictClient.UpdateZoneItemMapByID(context.Background(), &dict.IDZoneItemMapParam{Id: zim.GetId(), ZoneItemMap: zim})
			if assert.NoError(t, err, "UpdateZoneItemMapByID") {
				assertFields(zim, n, "UpdateZoneItemMapByID")
			}
		}
	}
	list, err = dictClient.ListZoneItemMapsByItemID(context.Background(), &dict.IDPagingParam{Id: 4, Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListZoneItemMapsByItemID") {
		assert.Equal(t, uint64(3), list.GetCount(), "ListZoneItemMapsByItemID")
		for n, zim := range list.GetZoneItemMaps() {
			assertFields(zim, n+3, "ListZoneItemMapsByItemID")
			zim, err = dictClient.DeleteZoneItemMapByID(context.Background(), &dict.IDParam{Id: zim.GetId()})
			if assert.NoError(t, err, "DeleteZoneItemMapByID") {
				assertFields(zim, n+3, "DeleteZoneItemMapByID")
			}
		}
	}
	count, err = dictClient.CountZoneItemMapsByItemID(context.Background(), &dict.IDParam{Id: 4})
	if assert.NoError(t, err, "CountZoneItemMapsByItemID") {
		assert.Zero(t, count.GetCount(), "CountZoneItemMapsByItemID")
	}
}
