package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"qiniu.io/pay/walletd/model"
)

func TestCouponDiscountRoundTrip(t *testing.T) {
	x := &model.CouponDiscount{
		ID:          233,
		Code:        "foo",
		Type:        "SPONSOR",
		ScopeID:     456,
		Name:        "test",
		Description: "test description",
		Remark:      "test remark",
		Discount:    75,
		EffectTime:  base.NewHNS(time.Now().Round(0)),
		DeadTime:    base.NewHNS(time.Now().Round(0)),
		CreatedAt:   time.Now().Round(0),
		UpdatedAt:   time.Now().Round(0),
	}

	y, err := BuildPbCouponDiscount(x)
	assert.NoError(t, err)

	z, err := BuildCouponDiscount(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.<PERSON>, z.Code)
	assert.Equal(t, x.Type, z.Type)
	assert.Equal(t, x.ScopeID, z.ScopeID)
	assert.Equal(t, x.Name, z.Name)
	assert.Equal(t, x.Description, z.Description)
	assert.Equal(t, x.Remark, z.Remark)
	assert.Equal(t, x.Discount, z.Discount)
	assert.Equal(t, x.EffectTime, z.EffectTime)
	assert.Equal(t, x.DeadTime, z.DeadTime)
	assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
}
