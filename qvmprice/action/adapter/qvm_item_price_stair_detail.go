package adapter

import (
	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/qvmprice"

	"qiniu.io/pay/qvmprice/service"
)

func buildPbQVMItemPriceStairDetail(
	x *service.QVMItemPriceStairDetail,
) (*pb.QVMItemPriceStairDetail, error) {
	priceI64, err := x.Price.GetBigMoneyI64()
	if err != nil {
		return nil, err
	}

	return &pb.QVMItemPriceStairDetail{
		Quantity: x.Quantity,
		Price:    uint64(priceI64),
	}, nil
}

func buildPbQVMItemPriceStairDetails(
	x []*service.QVMItemPriceStairDetail,
) ([]*pb.QVMItemPriceStairDetail, error) {
	return mapSliceFallible(buildPbQVMItemPriceStairDetail, x)
}

func buildServiceQVMItemPriceStairDetail(
	x *pb.QVMItemPriceStairDetail,
) *service.QVMItemPriceStairDetail {
	price := base.NewNMoneyWithHighAccuracyI64(int64(x.Price))

	return &service.QVMItemPriceStairDetail{
		Quantity: x.Quantity,
		Price:    price,
	}
}

func buildServiceQVMItemPriceStairDetails(
	x []*pb.QVMItemPriceStairDetail,
) []*service.QVMItemPriceStairDetail {
	return mapSlice(buildServiceQVMItemPriceStairDetail, x)
}
