package action

import (
	"context"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/walletd/action/adapter"
)

func (a *PaymentAction) ListVoucherByCodes(ctx context.Context, codes *pb.CodeParamList) (*pb.VoucherItemList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"codes": codes,
	}).Info("New-Wallet : ListVoucherByCodes")

	vouchers, err := a.voucherService.ListVoucherByCodes(ctx, adapter.BuildVoucherCodes(codes))
	if err != nil {
		logger.WithFields(logrus.Fields{
			"codes is": codes,
		}).WithError(err).Error("ListVoucherByCodes failed")
		return nil, err
	}

	pbVoucherList, err := adapter.BuildPbVoucherItemList(vouchers)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"codes is": codes,
		}).WithError(err).Error("BuildPbVoucherItemList failed")
		return nil, err
	}

	return pbVoucherList, nil
}

func (a *PaymentAction) CountVoucherByCondition(ctx context.Context, condition *pb.ListVoucherConditionRequest) (*pb.CountParam, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"condition": condition,
	}).Info("New-Wallet : CountVoucherByCondition")

	voucherCondition, err := adapter.BuildVoucherCondition(condition)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"condition is": condition,
		}).WithError(err).Error("BuildVoucherCondition failed")
		return nil, err
	}

	count, err := a.voucherService.CountyVoucherByCondition(ctx, voucherCondition)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"condition is": condition,
		}).WithError(err).Error("CountyVoucherByCondition failed")
		return nil, err
	}

	return &pb.CountParam{Count: uint64(count)}, nil
}

// ListVoucherByCondition list voucher by condition
func (a *PaymentAction) ListVoucherByCondition(ctx context.Context, condition *pb.ListVoucherConditionRequest) (*pb.VoucherItemList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"condition": condition,
	}).Info("New-Wallet : ListVoucherByCondition")

	voucherCondition, err := adapter.BuildVoucherCondition(condition)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"condition is": condition,
		}).WithError(err).Error("BuildVoucherCondition failed")
		return nil, err
	}

	vouchers, err := a.voucherService.ListVoucherByCondition(ctx, voucherCondition)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"condition is": condition,
		}).WithError(err).Error("ListVoucherByCondition failed")
		return nil, err
	}

	pbVoucherList, err := adapter.BuildPbVoucherItemList(vouchers)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"condition is": condition,
		}).WithError(err).Error("BuildPbVoucherItemList failed")
		return nil, err
	}

	return pbVoucherList, nil
}

// CreateVoucher create voucher
func (a *PaymentAction) CreateVoucher(ctx context.Context, item *pb.VoucherItem) (*pb.VoucherItem, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"VoucherItem": item,
	}).Info("New-Wallet : CreateVoucher")

	voucher, err := adapter.BuildVoucher(item)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"VoucherItem is": item,
		}).WithError(err).Error("BuildVoucher failed")
		return nil, err
	}

	if voucher.UID != 0 {
		currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, voucher.UID)
		if err != nil {
			return nil, err
		}
		if voucher.AssetType != enums.AssetTypeCNYVoucher.AdaptAssetTypeForCurrencyTypeUSD(currencyType) {
			return nil, errors.New("invalid currency type")
		}
	}

	v, err := a.voucherService.CreateVoucher(ctx, voucher)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"VoucherItem is": item,
		}).WithError(err).Error("CreateVoucher failed")
		return nil, err
	}

	vi, err := adapter.BuildPbVoucherItem(v)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"VoucherItem is": item,
		}).WithError(err).Error("BuildPbVoucherItem failed")
		return nil, err
	}

	return vi, nil
}

// ActiveVoucher active voucher
func (a *PaymentAction) ActiveVoucher(ctx context.Context, item *pb.VoucherItem) (*pb.VoucherItem, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"VoucherItem": item,
	}).Info("New-Wallet : ActiveVoucher")

	voucher, err := adapter.BuildVoucher(item)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"VoucherItem is": item,
		}).WithError(err).Error("BuildVoucher failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, voucher.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid": voucher.UID,
		}).WithError(err).Error("PaymentAction.ActiveVoucher GetOnlyOneCurrencyType failed")
		return nil, err
	}
	voucher.AssetType = enums.AssetTypeCNYVoucher.AdaptAssetTypeForCurrencyTypeUSD(currencyType)

	v, err := a.voucherService.ActiveVoucher(ctx, voucher)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"VoucherItem is": item,
		}).WithError(err).Error("ActiveVoucher failed")
		return nil, err
	}

	vi, err := adapter.BuildPbVoucherItem(v)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"VoucherItem is": item,
		}).WithError(err).Error("BuildPbVoucherItem failed")
		return nil, err
	}

	return vi, nil
}

// CancelVoucher cancel voucher
func (a *PaymentAction) CancelVoucher(ctx context.Context, item *pb.VoucherItem) (*pb.VoucherItem, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"VoucherItem": item,
	}).Info("New-Wallet : CancelVoucher")

	voucher, err := adapter.BuildVoucher(item)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"VoucherItem is": item,
		}).WithError(err).Error("BuildVoucher failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, voucher.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid": voucher.UID,
		}).WithError(err).Error("PaymentAction.CancelVoucher GetOnlyOneCurrencyType failed")
		return nil, err
	}
	voucher.AssetType = enums.AssetTypeCNYVoucher.AdaptAssetTypeForCurrencyTypeUSD(currencyType)

	v, err := a.voucherService.CancelVoucher(ctx, voucher)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"VoucherItem is": item,
		}).WithError(err).Error("CancelVoucher failed")
		return nil, err
	}

	vi, err := adapter.BuildPbVoucherItem(v)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"VoucherItem is": item,
		}).WithError(err).Error("BuildPbVoucherItem failed")
		return nil, err
	}

	return vi, nil
}

// GetVoucherByID get voucher by id
func (a *PaymentAction) GetVoucherByCode(ctx context.Context, id *pb.CodeParam) (*pb.VoucherItem, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"id": id.Code,
	}).Info("New-Wallet : GetVoucherByCode")

	voucher, err := a.voucherService.GetVoucherByCode(ctx, id.Code)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"id is": id.Code,
		}).WithError(err).Error("GetVoucherByCode failed")
		return nil, err
	}

	vi, err := adapter.BuildPbVoucherItem(voucher)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"id is": id.Code,
		}).WithError(err).Error("BuildPbVoucherItem failed")
		return nil, err
	}

	return vi, nil
}
