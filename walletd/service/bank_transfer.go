package service

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	sdk "github.com/qbox/bo-sdk/base"
	gaeaClient "github.com/qbox/pay-sdk/gaea/client"
	gaeaOp "github.com/qbox/pay-sdk/gaea/client/operations"
	"github.com/qbox/pay-sdk/middleware/logging"
	walletClient "github.com/qbox/pay-sdk/v3/walletEx/client"
	v3WalletSDKOp "github.com/qbox/pay-sdk/v3/walletEx/client/operations"
	v3WalletSDKModel "github.com/qbox/pay-sdk/v3/walletEx/models"

	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/wallet/dao"
	"qiniu.io/pay/walletd/model"
)

// BankTransferService service
type BankTransferService struct {
	dao                   *model.BankTransferDao
	currencyDao           dao.ICurrencyDAO
	userVirtualAccountDao *model.UserBankVirtualAccountDao
	walletV3Client        *walletClient.WalletEx
	gaeaAdminClient       *gaeaClient.Gaea
}

// NewBankTransferService new service
func NewBankTransferService(
	dao *model.BankTransferDao,
	currencyDao dao.ICurrencyDAO,
	ubvDao *model.UserBankVirtualAccountDao,
	walletV3Client *walletClient.WalletEx,
	gaeaAdminClient *gaeaClient.Gaea,
) *BankTransferService {
	return &BankTransferService{
		dao:                   dao,
		currencyDao:           currencyDao,
		userVirtualAccountDao: ubvDao,
		walletV3Client:        walletV3Client,
		gaeaAdminClient:       gaeaAdminClient,
	}
}

// ListBankTransfer 查询银行转账记录列表
func (s *BankTransferService) ListBankTransfer(
	ctx context.Context,
	query *model.BankTransferQuery,
	offset int,
	limit int,
) ([]*model.BankTransfer, uint64, error) {
	// 不记录info日志，避免与action层重复

	// 设置分页参数
	query.Offset = int64(offset)
	query.Limit = int64(limit)

	// 创建等待组和错误通道
	var wg sync.WaitGroup
	var transfers []*model.BankTransfer
	var total uint64
	var transfersErr, totalErr error

	// 并行执行查询
	wg.Add(2)
	go func() {
		defer wg.Done()
		transfers, transfersErr = s.dao.ListByQuery(ctx, query)
	}()

	go func() {
		defer wg.Done()
		total, totalErr = s.dao.CountByQuery(ctx, query)
	}()

	wg.Wait()

	// 检查错误
	if transfersErr != nil {
		return nil, 0, errors.Trace(transfersErr).WithField("query", query)
	}

	if totalErr != nil {
		return nil, 0, errors.Trace(totalErr).WithField("query", query)
	}

	return transfers, total, nil
}

// GetBankTransfer 获取银行转账记录详情
func (s *BankTransferService) GetBankTransfer(
	ctx context.Context,
	sn string,
) (*model.BankTransfer, error) {
	// 不记录info日志，避免与action层重复

	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return nil, errors.Trace(err).WithField("sn", sn)
	}
	reallocates, err := s.dao.GetByParentSN(transfer.SN)
	if err != nil {
		return nil, errors.Trace(err).WithField("parent_sn", sn)
	}
	transfer.Reallocates = reallocates

	return transfer, nil
}

// ApproveBankTransfer 审批通过银行转账申请
func (s *BankTransferService) ApproveBankTransfer(
	ctx context.Context,
	sn string,
) error {
	// 获取转账记录
	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}
	// 没有 uid,审核不通过
	if transfer.UID == 0 {
		return errors.New("uid is empty").
			WithField("sn", sn)
	}

	// 检查状态是否允许审批
	if transfer.Status != model.BankTransferStatusPendingReview &&
		transfer.Status != model.BankTransferStatusVoid {
		return errors.New("bank transfer status not allowed to approve").
			WithFields(errors.Fields{
				"sn":     sn,
				"status": transfer.Status,
			})
	}

	// 更新状态为待确认
	err = s.dao.UpdateStatus(sn, model.BankTransferStatusPendingConfirm)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	return nil
}

// InvalidateBankTransfer 作废银行转账申请
func (s *BankTransferService) InvalidateBankTransfer(
	ctx context.Context,
	sn string,
) error {
	// 获取转账记录
	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}
	// 已经是作废状态了
	if transfer.Status == model.BankTransferStatusVoid {
		return nil
	}

	// 检查状态是否允许作废
	if transfer.Status != model.BankTransferStatusPendingReview {
		return errors.New("bank transfer status not allowed to invalidate").
			WithFields(errors.Fields{
				"sn":     sn,
				"status": transfer.Status,
			})
	}

	// 更新状态为已作废
	err = s.dao.UpdateStatus(sn, model.BankTransferStatusVoid)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	return nil
}

// RechargeBankTransfer 执行银行转账充值操作
func (s *BankTransferService) RechargeBankTransfer(
	ctx context.Context,
	sn string,
) error {

	// 获取转账记录
	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	// 检查状态是否允许充值
	if transfer.Status != model.BankTransferStatusPendingConfirm {
		return errors.New("bank transfer status not allowed to recharge").
			WithFields(errors.Fields{
				"sn":     sn,
				"status": transfer.Status,
			})
	}
	// 已确认，无需重复执行
	if transfer.Status == model.BankTransferStatusConfirmed {
		return nil
	}

	// 更新状态为已确认
	err = s.dao.UpdateStatus(sn, model.BankTransferStatusConfirmed)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	// 查询相关的记录，确认是否都已经确认完成，如果确认完成了，则开始充值
	ctx = context.WithoutCancel(ctx)
	go func() {
		time.Sleep(3 * time.Second)

		if transfer.ParentSN != "" {
			// 查询关联的记录
			relatedTransfers, err1 := s.dao.GetByParentSN(transfer.ParentSN)
			if err1 != nil {
				logging.GetLogger(ctx).
					WithError(err1).
					WithFields(logrus.Fields{
						"transfer": transfer,
					}).Error("list by query failed")
				return
			}
			if len(relatedTransfers) == 0 {
				logging.GetLogger(ctx).
					WithError(errors.New("missing related transfers")).
					WithFields(logrus.Fields{
						"transfer": transfer,
					}).Error("no related transfers found")
				return
			}
			for _, relatedTransfer := range relatedTransfers {
				if relatedTransfer.SN == transfer.SN {
					// 当前 transfer 无需判断
					continue
				}
				if relatedTransfer.Status != model.BankTransferStatusConfirmed {
					// 还存在没有确认的记录
					return
				}
			}
		}

		// 更新状态为充值中
		// todo 其相关也需要更新状态为充值中
		err = s.dao.UpdateStatus(sn, model.BankTransferStatusRecharging)
		if err != nil {
			return
		}
		// todo 开始充值(如果是reallocate的记录，其parent_sn需要执行撤销)

		// 更新状态为已充值
		err = s.dao.UpdateStatus(sn, model.BankTransferStatusRecharged)
		if err != nil {
			return
		}
	}()

	return nil
}

// RevokeBankTransfer 撤销银行转账申请
func (s *BankTransferService) RevokeBankTransfer(
	ctx context.Context,
	param *model.BankTransferRevokeParam,
) error {
	// 获取转账记录
	transfer, err := s.dao.GetBySN(param.SN)
	if err != nil {
		return errors.Trace(err).WithField("param", param)
	}
	// 已撤销或者正在撤销中
	if transfer.Status == model.BankTransferStatusRevoked ||
		transfer.Status == model.BankTransferStatusRevoking {
		return nil
	}
	err = s.checkReallocateParams(ctx, param, transfer)
	if err != nil {
		return errors.Trace(err).WithField("param", param)
	}
	// 检查状态是否允许撤销
	if transfer.Status != model.BankTransferStatusRecharged {
		return errors.New("bank transfer status not allowed to revoke").
			WithFields(errors.Fields{
				"param":    param,
				"transfer": transfer,
			})
	}
	// 更新状态为撤销中
	err = s.dao.UpdateStatus(param.SN, model.BankTransferStatusRevoking)
	if err != nil {
		return errors.Trace(err).WithField("param", param)
	}
	ctx = context.WithoutCancel(ctx)
	go func() {
		// 仅撤销，不重新分配
		if len(param.Reallocates) == 0 {
			// 该方法内部会自行区分关账状态
			res, err1 := s.walletV3Client.Operations.RegenerateRecharge(
				v3WalletSDKOp.NewRegenerateRechargeParamsWithContext(ctx).
					WithReqRegenerateRecharge(
						&v3WalletSDKModel.ReqRegenerateRecharge{
							FromAPI:               "bank-transfer",
							OriginalPaymentID:     transfer.PaymentID,
							ReqReplacingRecharges: nil,
							UID:                   uint32(transfer.UID),
						},
					),
			)
			if err1 != nil || res.Code() != http.StatusOK {
				return
			}

			// 更新状态为已撤销
			err = s.dao.UpdateStatus(param.SN, model.BankTransferStatusRevoked)
			if err != nil {
				return
			}
			return
		}
		err = s.reallocate(ctx, transfer, param)
		if err != nil {
			return
		}
	}()
	return nil
}

func (s *BankTransferService) checkReallocateParams(
	ctx context.Context,
	param *model.BankTransferRevokeParam,
	transfer *model.BankTransfer,
) error {
	// 没有重新分配，仅撤销场景
	if len(param.Reallocates) == 0 {
		return nil
	}
	// 如果只要一条重新分配并且和原纪录 uid 相同，则不允许重新分配
	if len(param.Reallocates) == 1 &&
		param.Reallocates[0].UID == transfer.UID {
		return errors.New("avoid reallocate to the same UID")
	}
	reallocatedAmount := base.NewNMoney(0)
	uids := make([]uint64, 0)
	for _, reallocate := range param.Reallocates {
		if reallocate.UID == 0 {
			return errors.New("bank transfer reallocate uid invalid")
		}
		uids = append(uids, reallocate.UID)
		reallocatedAmount = reallocatedAmount.Add(reallocate.Amount)
	}
	// 多条分配记录里面出现重复的 uid
	uidSets := base.UniqueIntSlice(uids)

	// 检查金额是否和原纪录匹配
	if reallocatedAmount.MustCmp(transfer.Amount) != 0 {
		return errors.New("bank transfer amount sum not match")
	}
	// 查询 uidSet 的币种，检查币种是否匹配
	currencies, err := resultgroup.ParallelMap(
		uidSets,
		func(uid uint64) (base.CurrencyType, error) {
			c, err := s.currencyDao.GetOnlyOneCurrencyType(ctx, uid)
			if err != nil {
				return "", errors.Trace(err).WithField("uid", uid)
			}
			return base.CurrencyType(c), nil
		},
	)
	if err != nil {
		return errors.Trace(err).WithFields(errors.Fields{
			"uidSet": uidSets,
		})
	}
	for i, currency := range currencies {
		if transfer.CurrencyType != currency {
			return errors.New("bank transfer currency type not match").
				WithFields(errors.Fields{
					"uid":               uidSets[i],
					"currency_type":     currency,
					"transfer_currency": transfer.CurrencyType,
				})
		}
	}
	return nil
}

// reallocate 重新分配
func (s *BankTransferService) reallocate(
	ctx context.Context,
	transfer *model.BankTransfer,
	params *model.BankTransferRevokeParam,
) error {
	log := logging.GetLogger(ctx)
	if len(params.Reallocates) == 0 {
		return nil
	}
	closeRes, err := s.gaeaAdminClient.Operations.BillsProcedureIsClosed(
		&gaeaOp.BillsProcedureIsClosedParams{
			Month:   base.NewMonth(transfer.ReceivedDate).String(),
			Context: ctx,
		},
	)
	if err != nil {
		log.WithFields(logrus.Fields{
			"transfer": transfer,
			"params":   params,
		}).WithError(err).
			Error("query bill procedure isClosed failed")
		return err
	}
	isClosed := closeRes.GetPayload().Data
	reallocated := make([]*model.BankTransfer, len(params.Reallocates))
	for i := range params.Reallocates {
		t := transfer.Clone()

		t.ParentSN = transfer.SN

		t.RecordType = model.RecordTypeReAllocate
		t.MatchingType = model.MatchingTypeManual
		t.Status = model.BankTransferStatusPendingReview

		t.UID = params.Reallocates[i].UID
		t.Remark = params.Reallocates[i].Remark
		t.Amount = params.Reallocates[i].Amount
		t.BankTxnNo = fmt.Sprintf("%s_%d", transfer.SN, i)

		t.Creator = params.Creator
		t.Creator = t.GetCreator()

		t.CreatedAt = time.Now()
		t.UpdatedAt = time.Now()

		t.ReceivedDate = transfer.ReceivedDate
		if isClosed {
			t.ReceivedDate = time.Now()
		}

		reallocated[i] = t
	}
	err = s.DoTransaction(func(srv *BankTransferService) error {
		for _, bankTransfer := range reallocated {
			logging.GetLogger(ctx).WithFields(logrus.Fields{
				"bank_transfer": bankTransfer,
			}).Info("save reallocated bank transfer")
			err1 := srv.dao.Save(bankTransfer)
			if err1 != nil {
				log.WithFields(logrus.Fields{
					"transfer":      transfer,
					"bank_transfer": bankTransfer,
				}).WithError(err1).
					Error("save bank transfer failed")
				return err1
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// SearchPaymentAccount 搜索支付账户信息
func (s *BankTransferService) SearchPaymentAccount(
	ctx context.Context,
	paymentAccount string,
) ([]string, error) {
	// 不记录info日志，避免与action层重复

	// 调用 DAO 层方法模糊查询打款账户
	accounts, err := s.dao.SearchPaymentAccounts(ctx, paymentAccount)
	if err != nil {
		return nil, errors.Trace(err).WithField("payment_account", paymentAccount)
	}

	return accounts, nil
}

// BatchCreateBankTransfer 批量创建银行转账记录
func (s *BankTransferService) BatchCreateBankTransfer(
	ctx context.Context,
	transfers []*model.BankTransfer,
) error {
	transfers, err := resultgroup.ParallelMap(
		transfers,
		func(transfer *model.BankTransfer) (*model.BankTransfer, error) {
			return s.initAndValidateTransfer(ctx, transfer)
		},
	)
	if err != nil {
		return errors.Trace(err).WithField("transfers", transfers)
	}

	err = s.DoTransaction(func(srv *BankTransferService) error {
		for _, t := range transfers {
			err = srv.dao.Save(t)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return errors.Trace(err).WithField("transfers", transfers)
	}
	ctx = context.WithoutCancel(ctx)
	go func() {
		time.Sleep(3 * time.Second)
		for _, t := range transfers {
			if !t.CanAutoRecharge() {
				continue
			}
			log := logging.GetLogger(ctx)

			err1 := s.dao.UpdateStatus(t.SN, model.BankTransferStatusRecharging)
			if err1 != nil {
				log.WithField("transfer", t).
					WithError(err1).
					Error("failed to update status")
				// 允许后续其他充值继续执行
				continue
			}

			log.WithField("transfer", t).Info("start to recharge")
			rechargeRes, err1 := s.walletV3Client.Operations.Recharge(
				&v3WalletSDKOp.RechargeParams{
					At:               time.Now().Unix(),
					Desc:             "",
					Details:          "",
					Excode:           t.SN,
					Money:            t.Amount.MustGetMoney().ToInt64(),
					RelatedPaymentID: "",
					Type:             string(enums.TransactionTypeBank),
					UID:              uint32(t.UID),
					Context:          ctx,
				},
			)
			log.WithFields(logrus.Fields{
				"transfer": t,
				"res":      rechargeRes,
				"err":      err1,
			}).Info("recharge result")
			if err1 != nil {
				log.WithField("transfer", t).
					WithError(err1).
					Error("failed to recharge bank transfer")
				// 允许后续其他充值继续执行
				continue
			}
			t.PaymentID = rechargeRes.GetPayload()
			err1 = s.dao.UpdatePaymentID(t.SN, t.PaymentID)
			if err1 != nil {
				log.WithField("transfer", t).
					WithError(err1).
					Error("failed to update payment id")
				// 允许后续其他充值继续执行
				continue
			}
		}
	}()
	return nil
}

func (s *BankTransferService) initAndValidateTransfer(
	ctx context.Context,
	transfer *model.BankTransfer,
) (*model.BankTransfer, error) {
	// 检查 uid 及 币种
	if transfer.UID > 0 {
		currencyType, err := s.currencyDao.GetOnlyOneCurrencyType(
			ctx, transfer.UID,
		)
		if err != nil {
			return nil, errors.Trace(err).WithField("transfer", transfer)
		}
		if base.CurrencyType(currencyType) != transfer.CurrencyType {
			return nil, errors.Trace(errors.New("currency not match")).
				WithField("transfer", transfer)
		}
	}

	// 设置初始状态为待审核
	transfer.Status = model.BankTransferStatusPendingReview
	transfer.RecordType = model.RecordTypeFirstAllocate
	transfer.Creator = transfer.GetCreator()

	transfer = s.matching(transfer)
	return transfer, nil
}

// matching 处理银行转账的自动匹配逻辑
// 参数 transfer 包含待匹配的转账信息
// 返回包含匹配结果的 BankTransfer 对象
// 优先级：手动指定 > 虚账户匹配 > 备注AccountID匹配
func (s *BankTransferService) matching(
	transfer *model.BankTransfer,
) *model.BankTransfer {
	// 如果 UID 已手动指定，直接返回
	if transfer.UID > 0 {
		transfer.MatchingType = model.MatchingTypeManual
		return transfer
	}

	// 尝试通过虚账户匹配
	transfer = s.matchVirtualAccount(transfer)
	if transfer.UID > 0 {
		return transfer
	}

	// 尝试通过备注中的 AccountID 匹配
	transfer = s.matchingAccountID(transfer)
	if transfer.UID > 0 {
		return transfer
	}

	// 如果以上匹配均未成功，状态改为待确认，需要走人工匹配
	transfer.MatchingType = model.MatchingTypeManual
	return transfer
}

// matchingAccountID 从转账备注中解析并匹配用户ID
// 参数 transfer 包含待解析的转账信息
// 返回包含匹配结果的 BankTransfer 对象
// 如果解析成功且ID有效则返回匹配结果，否则返回原始对象
func (s *BankTransferService) matchingAccountID(
	transfer *model.BankTransfer,
) *model.BankTransfer {
	uid, err := sdk.ParseUID(transfer.Remark)
	if err == nil && uid > 0 {
		// 识别到 uid
		transfer.UID = uint64(uid)
		transfer.MatchingType = model.MatchingTypeRemark
	}
	return transfer
}

// matchVirtualAccount 通过虚账户信息进行匹配
// 参数 transfer 包含待匹配的转账信息
// 返回包含匹配结果的 BankTransfer 对象
// 优先从备注提取虚账号进行匹配，失败时返回原始对象
func (s *BankTransferService) matchVirtualAccount(
	transfer *model.BankTransfer,
) *model.BankTransfer {
	isVirtual, virtualNo := getVirtualNoByNote(transfer.Remark)
	if !isVirtual {
		return transfer
	}
	// 通过 virtual_account 表进行匹配
	vAccount, err := s.userVirtualAccountDao.GetByBankVirtAccount(virtualNo)
	if err != nil {
		// 异常报错或者是 NotFound Error, 都认为没有匹配上
		return transfer
	}
	// 匹配到了
	transfer.UID = vAccount.UID
	transfer.MatchingType = model.MatchingTypeVirtualAccount
	transfer.IsVirtualAccount = true
	return transfer
}

// getVirtualNoByNote 备注，虚账号 @ 客户附言，获取虚账号，如果没有匹配到虚账号默认为实账号
func getVirtualNoByNote(note string) (bool, string) {
	var (
		isVirtual bool
		virtualNo string
	)
	if strings.Contains(note, "@") {
		noteList := strings.Split(note, "@")
		if len(noteList) >= 1 {
			isVirtual = true
			virtualNo = noteList[0]
		}
	}
	return isVirtual, virtualNo
}

// EditBankTransfer 编辑银行转账记录
func (s *BankTransferService) EditBankTransfer(
	ctx context.Context,
	transfer *model.BankTransfer,
) error {
	// 获取原记录
	original, err := s.dao.GetBySN(transfer.SN)
	if err != nil {
		return errors.Trace(err).WithField("sn", transfer.SN)
	}

	// 检查状态是否允许编辑
	if original.Status != model.BankTransferStatusPendingReview &&
		original.Status != model.BankTransferStatusVoid {
		return errors.New("bank transfer status not allowed to edit").WithFields(errors.Fields{
			"sn":     transfer.SN,
			"status": original.Status,
		})
	}
	// 币种不允许更改
	if original.CurrencyType != transfer.CurrencyType {
		return errors.New("currency not match").WithFields(errors.Fields{
			"original": original,
			"transfer": transfer,
		})
	}
	// 检查 uid 币种
	if transfer.UID > 0 {
		currencyType, err1 := s.currencyDao.GetOnlyOneCurrencyType(
			ctx, transfer.UID,
		)
		if err1 != nil {
			return errors.Trace(err1).WithField("transfer", transfer)
		}
		if base.CurrencyType(currencyType) != original.CurrencyType {
			return errors.Trace(errors.New("currency not match")).
				WithField("transfer", transfer)
		}
	}

	// 仅允许修改部分字段
	original.PaymentAccount = transfer.PaymentAccount
	original.PaymentAccountNo = transfer.PaymentAccountNo
	original.PaymentBank = transfer.PaymentBank
	original.Amount = transfer.Amount
	original.ReceivedDate = transfer.ReceivedDate
	original.ReceivedAccount = transfer.ReceivedAccount
	original.ReceivedAccountNo = transfer.ReceivedAccountNo
	original.ReceivedBank = transfer.ReceivedBank
	original.Remark = transfer.Remark
	original.UID = transfer.UID

	// 保存更新
	err = s.dao.Save(original)
	if err != nil {
		return errors.Trace(err).WithField("transfer", transfer)
	}

	return nil
}

// ReturnBankTransfer 退回银行转账记录
func (s *BankTransferService) ReturnBankTransfer(
	ctx context.Context,
	sn string,
) error {
	// 获取转账记录
	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}
	// 已经是退回状态了
	if transfer.Status == model.BankTransferStatusPendingReview {
		return nil
	}

	// 检查状态是否允许取消
	if transfer.Status != model.BankTransferStatusPendingConfirm &&
		transfer.Status != model.BankTransferStatusConfirmed {
		return errors.New("bank transfer status not allowed to return").WithFields(errors.Fields{
			"sn":     sn,
			"status": transfer.Status,
		})
	}

	// 更新状态为已撤销
	err = s.dao.UpdateStatus(sn, model.BankTransferStatusPendingReview)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	return nil
}

// DoTransaction do in transaction
func (s *BankTransferService) DoTransaction(
	fn func(srv *BankTransferService) error,
) error {
	return s.dao.DoTransaction(func(dao *model.BankTransferDao) error {
		return fn(NewBankTransferService(
			dao,
			s.currencyDao,
			s.userVirtualAccountDao,
			s.walletV3Client,
			s.gaeaAdminClient,
		))
	})
}
