package action_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/qbox/pay-sdk/price"
	"github.com/stretchr/testify/assert"
)

func TestPriceItemStairAction(t *testing.T) {
	sandbox := buildSandbox(t)

	priceClient := sandbox.priceClient

	stairs := []price.PriceItemStair{
		{
			PriceItemId: 1,
			UnitId:      1,
			Quantity:    1,
			Price:       100,
			Order:       0,
		},
		{
			PriceItemId: 1,
			UnitId:      2,
			Quantity:    2,
			Price:       200,
			Order:       0,
		},
		{
			PriceItemId: 3,
			UnitId:      3,
			Quantity:    3,
			Price:       300,
			Order:       0,
		},
	}

	assertFields := func(expect, actual *price.PriceItemStair, msgAndArgs ...any) {
		assert.Equal(t, expect.PriceItemId, actual.PriceItemId, msgAndArgs...)
		assert.Equal(t, expect.UnitId, actual.UnitId, msgAndArgs...)
		assert.Equal(t, expect.Quantity, actual.Quantity, msgAndArgs...)
		assert.Equal(t, expect.Price, actual.Price, msgAndArgs...)
		assert.Equal(t, expect.Order, actual.Order, msgAndArgs...)
	}

	for i, stair := range stairs {
		// create
		m, err := priceClient.CreatePriceItemStair(context.Background(), &stair)
		if assert.NoError(t, err) {
			assert.NotZero(t, m.Id, "CreatePriceItemStair return zero id")
			assertFields(&stair, m)
		}
		stairs[i].Id = m.Id

		// query
		stairId := m.Id
		m, err = priceClient.GetPriceItemStairByID(context.Background(), &price.IDParam{Id: stairId})
		if assert.NoError(t, err) {
			assertFields(&stair, m)
		}

		// update
		m.Price = 1000
		updatedPriceItemStair, err := priceClient.UpdatePriceItemStairByID(context.Background(), &price.IDPriceItemStairParam{
			Id:    stairId,
			Stair: m,
		})
		if assert.NoError(t, err) {
			assertFields(m, updatedPriceItemStair)
			stairs[i].Price = m.Price
		}
	}

	// list all price item stairs
	list, err := priceClient.ListAllPriceItemStairs(context.Background(), &price.PagingParam{
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Len(t, stairs, int(list.GetCount()))
		for _, stair := range list.GetPriceItemStairs() {
			for _, expectedItemStair := range stairs {
				if stair.Id == expectedItemStair.Id {
					assertFields(&expectedItemStair, stair)
					break
				}
			}
		}
	}

	// list all price item stairs by item_id
	list, err = priceClient.ListAllPriceItemStairsByPriceItemID(context.Background(), &price.IDPagingParam{
		Id:       1,
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Equal(t, uint64(2), list.GetCount())
		for _, stair := range list.GetPriceItemStairs() {
			for _, expectedItemStair := range stairs {
				if stair.Id == expectedItemStair.Id {
					assertFields(&expectedItemStair, stair, fmt.Sprintf("Unmatched stair, stair_id:%d", stair.Id))
					break
				}
			}
		}
	}

	// delete all price item stairs
	for _, stair := range stairs {
		deletedPriceItemStair, err := priceClient.DeletePriceItemStairByID(context.Background(), &price.IDParam{Id: stair.Id})
		if assert.NoError(t, err) {
			assertFields(deletedPriceItemStair, &stair)
		}
	}

	// check price item list, should be empty
	list, err = priceClient.ListAllPriceItemStairs(context.Background(), &price.PagingParam{
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Zero(t, list.GetCount())
		assert.Empty(t, list.GetPriceItemStairs())
	}

	// de-duplication
	for _, stair := range stairs {
		_, err := priceClient.DeletePriceItemStairByID(context.Background(), &price.IDParam{Id: stair.Id})
		assert.Error(t, err)
	}
}
