package enums

// NoticeStatus defines status type
type NoticeStatus uint32

const (
	// UnknownNoticeStatus is a unknown status
	UnknownNoticeStatus = NoticeStatus(iota) // 0
	// NoticeStatusSuccess 成功发送
	NoticeStatusSuccess // 1
	// NoticeStatusFailed 发送失败
	NoticeStatusFailed // 2
	// NoticeStatusDelayed 延后发送
	NoticeStatusDelayed // 3
	// NoticeStatusCanceled 取消/无需发送
	NoticeStatusCanceled // 4
)

func (s NoticeStatus) String() string {
	switch s {
	case UnknownNoticeStatus:
		return "UNKNOWN"
	case NoticeStatusSuccess:
		return "SUCCESS"
	case NoticeStatusFailed:
		return "FAILED"
	case NoticeStatusDelayed:
		return "DELAYED"
	case NoticeStatusCanceled:
		return "CANCELED"
	}
	return "UNKNOWN"
}
