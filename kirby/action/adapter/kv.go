package adapter

import (
	pb "github.com/qbox/bo-sdk/kirbypb"
	"github.com/samber/lo"

	"qiniu.io/pay/kirby/model/dataplaneModel"
)

func BuildSPFromKVs(in []*pb.KV) dataplaneModel.SP {
	y := dataplaneModel.SP{}
	for _, x := range in {
		y[x.K] = x.V
	}
	return y
}

func BuildPbKVsFromSP(in dataplaneModel.SP) []*pb.KV {
	return lo.MapToSlice(in, func(k string, v string) *pb.KV {
		return &pb.KV{K: k, V: v}
	})
}
