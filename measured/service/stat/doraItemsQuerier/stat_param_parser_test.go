package doraItemsQuerier

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/legacystat/common"
)

func TestParseCmdsFromStatParams(t *testing.T) {
	testcases := []struct {
		desc      string
		statKey   string
		statQuery string
		ok        bool
		expected  []common.LegacyDoraCmd
	}{
		{
			desc:      "empty",
			statKey:   "",
			statQuery: "",
			ok:        true,
			expected:  nil,
		},
		{
			desc:      "statKey != merge, empty",
			statKey:   "foo",
			statQuery: "",
			ok:        true,
			expected:  nil,
		},
		{
			desc:      "statKey == merge, empty",
			statKey:   "merge",
			statQuery: "",
			ok:        true,
			expected:  nil,
		},
		{
			desc:      "statKey != merge，单个 cmd",
			statKey:   "fopg",
			statQuery: "cmd=AVCONVERT_TIMES",
			ok:        true,
			expected: []common.LegacyDoraCmd{
				{Key: "fopg", Cmd: "AVCONVERT_TIMES"},
			},
		},
		{
			desc:      "statKey == merge，单个 cmd",
			statKey:   "merge",
			statQuery: "cmd=fopg:AVCONVERT_TIMES",
			ok:        true,
			expected: []common.LegacyDoraCmd{
				{Key: "fopg", Cmd: "AVCONVERT_TIMES"},
			},
		},
		{
			desc:      "statKey != merge，多个 cmd",
			statKey:   "common",
			statQuery: "cmd=AVA-ARGUS-UTIL_PULP-CERTAIN&cmd=AVA-ARGUS-UTIL_PULP-DEPEND:20180701",
			ok:        true,
			expected: []common.LegacyDoraCmd{
				{Key: "common", Cmd: "AVA-ARGUS-UTIL_PULP-CERTAIN"},
				{Key: "common", Cmd: "AVA-ARGUS-UTIL_PULP-DEPEND"},
			},
		},
		{
			desc:      "statKey == merge，多个 cmd",
			statKey:   "merge",
			statQuery: "cmd=common:AVA-ARGUS-UTIL_PULP-CERTAIN&cmd=common:AVA-ARGUS-UTIL_PULP-DEPEND:20180701&cmd=fopg:IMAGE-CENSOR_CENSOR_PULP&cmd=common:IMAGE-CENSOR_CENSOR_PULP&cmd=fopg:QPULP_CENSOR_PULP&cmd=fopg:VIDEO-CENSOR_CENSOR_PULP:20190901&cmd=common:VIDEO-CENSOR_CENSOR_PULP:20190901&cmd=fopg:IMAGE-CENSOR_CENSOR_PULP_TUPU&cmd=common:IMAGE-CENSOR_CENSOR_PULP_TUPU&cmd=fopg:QPULP_CENSOR_PULP_TUPU&cmd=fopg:VIDEO-CENSOR_CENSOR_PULP_TUPU&cmd=common:VIDEO-CENSOR_CENSOR_PULP_TUPU&cmd=fopg:VIDEO-CENSOR_CENSOR_PULP_ISM&cmd=common:VIDEO-CENSOR_CENSOR_PULP_ISM&cmd=fopg:IMAGE-CENSOR_CENSOR_PULP_ISM&cmd=common:IMAGE-CENSOR_CENSOR_PULP_ISM&cmd=fopg:QPULP_CENSOR_PULP_ISM", //nolint:lll // 真实数据
			ok:        true,
			expected: []common.LegacyDoraCmd{
				{Key: "common", Cmd: "AVA-ARGUS-UTIL_PULP-CERTAIN"},
				{Key: "common", Cmd: "AVA-ARGUS-UTIL_PULP-DEPEND"},
				{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_PULP"},
				{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_PULP"},
				{Key: "fopg", Cmd: "QPULP_CENSOR_PULP"},
				{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_PULP"},
				{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_PULP"},
				{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_PULP_TUPU"},
				{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_PULP_TUPU"},
				{Key: "fopg", Cmd: "QPULP_CENSOR_PULP_TUPU"},
				{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_PULP_TUPU"},
				{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_PULP_TUPU"},
				{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_PULP_ISM"},
				{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_PULP_ISM"},
				{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_PULP_ISM"},
				{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_PULP_ISM"},
				{Key: "fopg", Cmd: "QPULP_CENSOR_PULP_ISM"},
			},
		},
		{
			desc:      "statKey != merge, 无视不叫 cmd 的 query 参数",
			statKey:   "aaa",
			statQuery: "cmd=foo&cmd2=bar&cmd=baz",
			ok:        true,
			expected: []common.LegacyDoraCmd{
				{Key: "aaa", Cmd: "foo"},
				{Key: "aaa", Cmd: "baz"},
			},
		},
		{
			desc:      "statKey == merge, 无视不叫 cmd 的 query 参数",
			statKey:   "merge",
			statQuery: "cmd=aaa:foo&cmd2=bbb:bar&cmd=ccc:baz",
			ok:        true,
			expected: []common.LegacyDoraCmd{
				{Key: "aaa", Cmd: "foo"},
				{Key: "ccc", Cmd: "baz"},
			},
		},
		{
			desc:      "不合法的 URL",
			statKey:   "common",
			statQuery: "cmd=AVA-ARGUS-%2g",
			ok:        false,
		},
		{
			desc:      "不合法的 merge cmd 格式",
			statKey:   "merge",
			statQuery: "cmd=xxxfoo",
			ok:        false,
		},
	}

	for _, tc := range testcases {
		actual, err := parseCmdsFromStatParams(tc.statKey, tc.statQuery)
		if tc.ok {
			assert.NoError(t, err, tc.desc)
			assert.Equal(t, tc.expected, actual, tc.desc)
		} else {
			assert.Error(t, err, tc.desc)
			assert.Nil(t, actual, tc.desc)
		}
	}
}
