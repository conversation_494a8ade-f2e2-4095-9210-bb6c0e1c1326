package action_test

import (
	"io"
	"log"
	"net"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"google.golang.org/grpc/keepalive"

	"github.com/go-redis/redis/v8"
	"github.com/golang/mock/gomock"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/bo-base/v4/test"
	"github.com/qbox/pay-sdk/base/account"
	pbDict "github.com/qbox/pay-sdk/dict"
	mock_wallet "github.com/qbox/pay-sdk/mocks/wallet"
	pb "github.com/qbox/pay-sdk/price"

	dictAction "qiniu.io/pay/dictd/action"
	dictModel "qiniu.io/pay/dictd/model"
	dictService "qiniu.io/pay/dictd/service"
	"qiniu.io/pay/priced/action"
	"qiniu.io/pay/priced/config"
	"qiniu.io/pay/priced/model"
	"qiniu.io/pay/priced/service"
)

type sandbox struct {
	testWrap        *test.Wrap
	dictTestWrap    *test.Wrap
	priceServer     *grpc.Server
	priceClientConn *grpc.ClientConn
	priceClient     pb.PayPriceServiceClient

	listener net.Listener

	dictListener net.Listener
	dictServer   *grpc.Server
}

func buildSandbox(t *testing.T) *sandbox {
	ctrl := gomock.NewController(t)

	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in priced/action return error")
	}

	dictTestWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(dictModel.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in price/dict return error")
	}

	dictDao := dictModel.NewDictDao(dictTestWrap.BaseDao())
	dictService := dictService.NewDictBizService(dictDao, dao.CacheExpiresNoCache)
	dictAction := dictAction.NewDictAction(dictService, 10)

	dictServer := grpc.NewServer()
	pbDict.RegisterPayDictServiceServer(dictServer, dictAction)
	reflection.Register(dictServer)

	sandbox := new(sandbox)
	sandbox.dictServer = dictServer
	sandbox.initDictServer()

	dictConn, err := rpc.GrpcConnectWithName(sandbox.dictListener.Addr().String(), rpc.ServicePayV4Dict, keepalive.ClientParameters{})
	if err != nil {
		logrus.WithError(err).Fatalln("connecting dict server return error")
	}
	dictClient := pbDict.NewPayDictServiceClient(dictConn)

	paymentServiceClient := mock_wallet.NewMockPaymentServiceClient(ctrl)

	accServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		io.WriteString(w, `{"code":200,"message":""}`)
	}))
	conf := &config.PricedConfig{
		Cache: dao.CacheConfig{
			Enabled: true,
			Prefix:  strconv.FormatInt(time.Now().UnixNano(), 10),
			RedisConfig: redis.UniversalOptions{
				Addrs: []string{testWrap.Miniredis().Addr()},
			},
			DefaultExpires: time.Second * 5,
		},
		Acc: account.AccConfig{
			UserName: "root",
			Password: "root",
			Host:     accServer.URL,
		},
	}

	priceDao := model.NewPriceDao(testWrap.BaseDao())
	priceService, err := service.NewPriceBizService(
		conf,
		dictClient,
		paymentServiceClient,
		priceDao,
		dao.CacheExpiresNoCache,
		nil,
	)
	if err != nil {
		logrus.WithError(err).Fatalln("init price service failed")
	}

	priceAction := action.NewPriceAction(priceService, 10)

	priceServer := grpc.NewServer()
	pb.RegisterPayPriceServiceServer(priceServer, priceAction)
	reflection.Register(priceServer)

	sandbox.testWrap = testWrap
	sandbox.dictTestWrap = dictTestWrap
	sandbox.priceServer = priceServer

	sandbox.initPriceServer()
	sandbox.initPriceClient()
	t.Cleanup(sandbox.cleanup)

	return sandbox
}

func (s *sandbox) initDictServer() {
	var err error
	s.dictListener, err = net.Listen("tcp", ":0")
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	go func() {
		if err := s.dictServer.Serve(s.dictListener); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()

	time.Sleep(time.Millisecond * 300)
}

func (s *sandbox) closeDictServer() {
	if s.dictServer != nil {
		s.dictServer.Stop()
	}
}

func (s *sandbox) initPriceServer() {
	var err error
	s.listener, err = net.Listen("tcp", ":0")
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	go func() {
		if err := s.priceServer.Serve(s.listener); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()
}

func (s *sandbox) initPriceClient() {
	var err error
	s.priceClientConn, err = rpc.GrpcConnectWithName(s.listener.Addr().String(), rpc.ServicePayV4Price, keepalive.ClientParameters{})
	if err != nil {
		log.Fatalf("did not connect: %v", err)
	}
	s.priceClient = pb.NewPayPriceServiceClient(s.priceClientConn)
}

func (s *sandbox) closePriceClientConn() {
	if s.priceClientConn != nil {
		s.priceClientConn.Close()
	}
}

func (s *sandbox) closePriceServer() {
	if s.priceServer != nil {
		s.priceServer.Stop()
	}
}

func (s *sandbox) cleanup() {
	s.closePriceClientConn()
	s.closePriceServer()
	s.closeDictServer()
}
