package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/qpay/payment/service"
	"qiniu.io/pay/walletd/action/adapter"
)

// SetUserPaymentSeq 用于设置用户支付顺序
func (a *PaymentAction) SetUserPaymentSeq(ctx context.Context, paymentSeqParam *pb.SetPaymentSeqParam) (*empty.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": paymentSeqParam,
	}).Info("New-Wallet : SetUserPaymentSeq")

	seqParam := &service.SetPaymentSeqParam{
		PaymentSeq:     adapter.BuildUserPaymentSeq(paymentSeqParam.PaymentSeq),
		IsSameAsParent: paymentSeqParam.IsSameAsParent,
	}

	err := a.userPaymentSeqService.SetUserPaymentSeq(ctx, seqParam)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": paymentSeqParam,
		}).WithError(err).Error("set user payment sequence failed")
		return nil, err
	}

	return &empty.Empty{}, nil
}

// GetPaymentSeqByUID 用于获取用户支付顺序
func (a *PaymentAction) GetPaymentSeqByUID(ctx context.Context, req *pb.UIDParam) (*pb.PaymentSeq, error) {
	logger := logging.GetLogger(ctx)
	userPaymentSeq, err := a.userPaymentSeqService.GetUserPaymentSeq(ctx, req.GetUid())
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid": req.GetUid(),
		}).WithError(err).Error("get user payment sequence failed")
		return nil, err
	}
	return adapter.BuildPbUserPaymentSeq(userPaymentSeq)
}

// ListPaymentSeqHistoriesByUID 用于获取用户历史支付顺序
func (a *PaymentAction) ListPaymentSeqHistoriesByUID(
	ctx context.Context,
	req *pb.UIDPagingParam,
) (*pb.PaymentSeqList, error) {

	logger := logging.GetLogger(ctx)
	offset, limit := a.Paging(req)

	paymentSeqHistories, total, err := a.userPaymentSeqService.ListUserPaymentSeqHistories(
		ctx,
		req.GetUid(),
		offset,
		limit,
	)

	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid": req.GetUid(),
		}).WithError(err).Error("list user payment sequences failed")
		return nil, err
	}
	pbPaymentSeqList, err := adapter.BuildPbUserPaymentSeqHistoryList(paymentSeqHistories)
	if err != nil {
		return nil, err
	}

	return &pb.PaymentSeqList{
		PaymentSeqs: pbPaymentSeqList,
		Total:       total,
	}, nil
}
