package action

import (
	"context"

	"qiniu.io/pay/creditd/model/enums"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/credit"
	"qiniu.io/pay/creditd/action/adapter"
)

// SetManagerAvailableBalanceThreshold create/update/enabled ManagerAvailableBalanceThreshold
func (c *CreditAction) SetManagerAvailableBalanceThreshold(ctx context.Context, in *pb.CreateAvailableBalanceThresholdReq) (*pb.AvailableBalanceThresholdResp, error) {
	m := adapter.BuildModelThreshold(in)
	threshold, err := c.thresholdSrv.SetManagerThreshold(ctx, m)
	if err != nil {
		return nil, errors.Trace(err)
	}

	pbThreshold, err := adapter.BuildPbThreshold(threshold)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return pbThreshold, nil

}

func (c *CreditAction) GetManagerAvailableBalanceThreshold(ctx context.Context, in *pb.GetAvailableBalanceThresholdByUIDReq) (*pb.AvailableBalanceThresholdResp, error) {
	thresholdModel, err := c.thresholdSrv.GetEnabledThresholdByUID(ctx, in.GetUid(), true)
	if err != nil {
		return nil, errors.Trace(err)
	}
	pbThreshold, err := adapter.BuildPbThreshold(thresholdModel)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return pbThreshold, nil
}

// CreateAvailableBalanceThreshold creates new entry of AvailableBalanceThreshold (新建可用额度阈值)
func (c *CreditAction) CreateAvailableBalanceThreshold(ctx context.Context, in *pb.CreateAvailableBalanceThresholdReq) (*pb.AvailableBalanceThresholdResp, error) {
	m := adapter.BuildModelThreshold(in)

	threshold, err := c.thresholdSrv.CreateThreshold(ctx, m)
	if err != nil {
		return nil, errors.Trace(err)
	}

	pbThreshold, err := adapter.BuildPbThreshold(threshold)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return pbThreshold, nil
}

// GetAvailableBalanceThresholdByUID gets AvailableBalanceThreshold by uid (获取可用额度阈值信息)
func (c *CreditAction) GetAvailableBalanceThresholdByUID(ctx context.Context, in *pb.GetAvailableBalanceThresholdByUIDReq) (*pb.AvailableBalanceThresholdResp, error) {
	thresholdModel, err := c.thresholdSrv.GetEnabledThresholdByUID(ctx, in.GetUid(), in.GetIsManager())
	if err != nil {
		return nil, errors.Trace(err)
	}
	pbThreshold, err := adapter.BuildPbThreshold(thresholdModel)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return pbThreshold, nil
}

// ListAvailableBalanceThresholdByStatus gets AvailableBalanceThreshold by status (按照 status 条件获取可用额度阈值信息)
func (c *CreditAction) ListAvailableBalanceThresholdByStatus(ctx context.Context, in *pb.ListThresholdsByStatusReq) (*pb.ListThresholdsByStatusResp, error) {
	offset, limit := c.Paging(in)
	thresholds, count, err := c.thresholdSrv.GetByStatus(ctx, enums.ThresholdStatus(in.Status), in.IsManager, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}
	pbResp, err := adapter.BuildPbListThresholdsByStatusResp(thresholds, count)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return pbResp, nil
}
