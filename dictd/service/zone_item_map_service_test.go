package service_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestZoneItemMap(t *testing.T) {
	sandbox := buildSandbox(t)

	dictService := sandbox.dictService

	zims := []model.ZoneItemMap{
		{
			ZoneID:    1,
			ItemID:    1,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ZoneID:    1,
			ItemID:    2,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ZoneID:    1,
			ItemID:    3,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ZoneID:    2,
			ItemID:    4,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ZoneID:    3,
			ItemID:    4,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ZoneID:    4,
			ItemID:    4,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}
	// 创建对应的 zone 和 item
	for i := 1; i <= 4; i++ {
		_, err := dictService.CreateItem(context.Background(), &model.Item{
			Code: fmt.Sprintf("cdn:%d", i),
		})
		assert.Nil(t, err)
		_, err = dictService.CreateZone(context.Background(), &model.Zone{
			Code: int64(i),
			Name: fmt.Sprintf("zone %d", i),
		})
		assert.Nil(t, err)
	}

	assertFields := func(zim *model.ZoneItemMap, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(zims) }) {
			assert.Equal(t, zims[n].ZoneID, zim.ZoneID, msgAndArgs...)
			assert.Equal(t, zims[n].ItemID, zim.ItemID, msgAndArgs...)
		}
	}
	ctx := context.TODO()
	ids := make(map[uint64]int, len(zims))
	for n, zim := range zims {
		_zim, err := dictService.CreateZoneItemMap(ctx, &zim)
		if assert.NoError(t, err, "CreateZoneItemMap") {
			ids[_zim.ID] = n
			assertFields(_zim, n, "CreateZoneItemMap")
		}
	}
	count, err := dictService.CountZoneItemMapsByZoneID(ctx, 1)
	if assert.NoError(t, err, "CountZoneItemMapsByZoneID") {
		assert.Equal(t, uint64(3), count, "CountZoneItemMapsByZoneID")
	}
	count, err = dictService.CountZoneItemMapsByItemID(ctx, 4)
	if assert.NoError(t, err, "CountZoneItemMapsByItemID") {
		assert.Equal(t, uint64(3), count, "CountZoneItemMapsByItemID")
	}
	list, err := dictService.ListZoneItemMapsByZoneID(ctx, 1, 0, 10)
	if assert.NoError(t, err, "ListZoneItemMapsByZoneID") {
		assert.Len(t, list, 3, "ListZoneItemMapsByZoneID")
		for n, zim := range list {
			assertFields(&zim, n, "ListZoneItemMapsByZoneID")
			zim, err := dictService.GetZoneItemMapByID(ctx, zim.ID)
			if assert.NoError(t, err, "GetZoneItemMapByID") {
				assertFields(zim, n, "GetZoneItemMapByID")
			}
			zim.ItemID += 10
			zims[n].ItemID = zim.ItemID
			zim, err = dictService.UpdateZoneItemMapByID(ctx, zim.ID, zim)
			if assert.NoError(t, err, "UpdateZoneItemMapByID") {
				assertFields(zim, n, "UpdateZoneItemMapByID")
			}
		}
	}
	list, err = dictService.ListZoneItemMapsByItemID(ctx, 4, 0, 10)
	if assert.NoError(t, err, "ListZoneItemMapsByItemID") {
		assert.Len(t, list, 3, "ListZoneItemMapsByItemID")
		for n, zim := range list {
			assertFields(&zim, n+3, "ListZoneItemMapsByItemID")
			z, err := dictService.DeleteZoneItemMapByID(ctx, zim.ID)
			if assert.NoError(t, err, "DeleteZoneItemMapByID") {
				assertFields(z, n+3, "DeleteZoneItemMapByID")
			}
		}
	}
	count, err = dictService.CountZoneItemMapsByItemID(ctx, 4)
	if assert.NoError(t, err, "CountZoneItemMapsByItemID") {
		assert.Zero(t, count, "CountZoneItemMapsByItemID")
	}
}
