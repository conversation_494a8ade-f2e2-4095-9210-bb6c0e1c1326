package dataplaneModel

import (
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/test"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/kirby/model/adminModel"
)

func TestComputeDataTableShard(t *testing.T) {
	cst := time.FixedZone("CST", 8*3600)

	testcases := []struct {
		kind     dataTableKind
		g        adminModel.Granularity
		t        time.Time
		loc      *time.Location
		key      string
		expected computedShardInfo
	}{
		{
			kind:     dataTableKindSeries,
			g:        adminModel.GranularityDay,
			t:        time.Date(2022, 10, 31, 16, 0, 0, 0, time.UTC),
			loc:      time.UTC,
			key:      "qrn:kodo:z0:123456789:bucket/foobar",
			expected: computedShardInfo{name: "series_day_m202210_cc", numPoints: 31},
		},
		{
			kind:     dataTableKindSeries,
			g:        adminModel.GranularityDay,
			t:        time.Date(2022, 10, 31, 16, 0, 0, 0, time.UTC),
			loc:      cst,
			key:      "qrn:kodo:z0:123456789:bucket/foobar",
			expected: computedShardInfo{name: "series_day_m202211_cc", numPoints: 30},
		},
		{
			kind:     dataTableKindSeries,
			g:        adminModel.Granularity5Min,
			t:        time.Date(2022, 10, 31, 16, 0, 0, 0, time.UTC),
			loc:      cst,
			key:      "qrn:kodo:z0:123456789:bucket/foobar",
			expected: computedShardInfo{name: "series_5min_m202211_cc", numPoints: 288},
		},
		{
			kind:     dataTableKindSeries,
			g:        adminModel.GranularityMonth,
			t:        time.Date(2022, 10, 31, 16, 0, 0, 0, time.UTC),
			loc:      cst,
			key:      "qrn:kodo:z0:123456789:bucket/foobar",
			expected: computedShardInfo{name: "series_month_m202211_cc", numPoints: 1},
		},
		{
			kind:     dataTableKindSeries,
			g:        adminModel.GranularityDay,
			t:        time.Date(2022, 11, 1, 0, 0, 0, 0, cst),
			loc:      time.UTC,
			key:      "qrn:kodo:z0:123456789:bucket/foobaz",
			expected: computedShardInfo{name: "series_day_m202210_c4", numPoints: 31},
		},
		{
			kind:     dataTableKindSeries,
			g:        adminModel.GranularityDay,
			t:        time.Date(2022, 11, 1, 0, 0, 0, 0, cst),
			loc:      cst,
			key:      "qrn:kodo:z0:123456789:bucket/foobaz",
			expected: computedShardInfo{name: "series_day_m202211_c4", numPoints: 30},
		},
		{
			kind:     dataTableKindSeries,
			g:        adminModel.GranularityDay,
			t:        time.Date(2022, 11, 22, 3, 4, 5, 678901234, time.UTC),
			loc:      cst,
			key:      "qrn:kodo:z0:123456789:bucket/foobaz",
			expected: computedShardInfo{name: "series_day_m202211_c4", numPoints: 30},
		},
		{
			kind:     dataTableKindAggregatedSeries,
			g:        adminModel.GranularityDay,
			t:        time.Date(2022, 11, 1, 0, 0, 0, 0, cst),
			loc:      cst,
			key:      "cafebabe",
			expected: computedShardInfo{name: "agg_series_day_m202211", numPoints: 30},
		},
		{
			kind:     dataTableKindAggregatedSeries,
			g:        adminModel.GranularityDay,
			t:        time.Date(2022, 11, 1, 0, 0, 0, 0, cst),
			loc:      cst,
			key:      "deadbeefdeadf00d",
			expected: computedShardInfo{name: "agg_series_day_m202211", numPoints: 30},
		},
	}

	for _, tc := range testcases {
		assert.Equal(t, tc.expected, computeDataTableShard(tc.kind, tc.g, tc.t, tc.loc, tc.key))
	}
}

func TestIsTimeAlignedWrtGranularity(t *testing.T) {
	test.RunWithUTCAndCST(t, testIsTimeAlignedWrtGranularity)
}

func testIsTimeAlignedWrtGranularity(t *testing.T, loc *time.Location) {
	testCases := []struct {
		g  adminModel.Granularity
		t  time.Time
		ok bool
	}{
		{
			g:  adminModel.Granularity5Min,
			t:  time.Date(2022, time.January, 1, 0, 0, 0, 0, loc),
			ok: true,
		},
		{
			g:  adminModel.Granularity5Min,
			t:  time.Date(2022, time.January, 1, 0, 0, 0, 1, loc),
			ok: false,
		},
		{
			g:  adminModel.GranularityDay,
			t:  time.Date(2022, time.January, 1, 0, 0, 0, 0, loc),
			ok: true,
		},
		{
			g:  adminModel.GranularityMonth,
			t:  time.Date(2022, time.January, 1, 0, 0, 0, 0, loc),
			ok: true,
		},
		{
			g:  adminModel.GranularityUnknown,
			t:  time.Date(2022, time.January, 1, 0, 0, 0, 0, loc),
			ok: false,
		},
		{
			g:  adminModel.Granularity5Min,
			t:  time.Date(2022, time.January, 1, 0, 1, 0, 0, loc),
			ok: false,
		},
		{
			g:  adminModel.GranularityDay,
			t:  time.Date(2022, time.January, 1, 1, 0, 0, 0, loc),
			ok: false,
		},
		{
			g:  adminModel.GranularityMonth,
			t:  time.Date(2022, time.January, 2, 0, 0, 0, 0, loc),
			ok: false,
		},
	}

	for _, tc := range testCases {
		actual := isTimeAlignedWrtGranularity(tc.g, tc.t, loc)
		assert.Equal(t, tc.ok, actual)
	}
}

func TestIncrementOneRowWrtGranularity(t *testing.T) {
	test.RunWithUTCAndCST(t, testIncrementOneRowWrtGranularity)
}

func testIncrementOneRowWrtGranularity(t *testing.T, loc *time.Location) {
	testCases := []struct {
		desc     string
		g        adminModel.Granularity
		t        time.Time
		expected time.Time
	}{
		{
			desc:     "g=5min",
			g:        adminModel.Granularity5Min,
			t:        time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			expected: time.Date(2023, 1, 2, 0, 0, 0, 0, loc),
		},
		{
			desc:     "g=5min, end of month",
			g:        adminModel.Granularity5Min,
			t:        time.Date(2023, 1, 31, 0, 0, 0, 0, loc),
			expected: time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
		},
		{
			desc:     "g=5min, end of year",
			g:        adminModel.Granularity5Min,
			t:        time.Date(2022, 12, 31, 0, 0, 0, 0, loc),
			expected: time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
		},
		{
			desc:     "g=day",
			g:        adminModel.GranularityDay,
			t:        time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			expected: time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
		},
		{
			desc:     "g=day, end of year",
			g:        adminModel.GranularityDay,
			t:        time.Date(2022, 12, 1, 0, 0, 0, 0, loc),
			expected: time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
		},
		{
			desc:     "g=day, February, not leap year",
			g:        adminModel.GranularityDay,
			t:        time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			expected: time.Date(2023, 3, 1, 0, 0, 0, 0, loc),
		},
		{
			desc:     "g=day, February, leap year",
			g:        adminModel.GranularityDay,
			t:        time.Date(2024, 2, 1, 0, 0, 0, 0, loc),
			expected: time.Date(2024, 3, 1, 0, 0, 0, 0, loc),
		},
		{
			desc:     "g=month",
			g:        adminModel.GranularityMonth,
			t:        time.Date(2024, 2, 1, 0, 0, 0, 0, loc),
			expected: time.Date(2024, 3, 1, 0, 0, 0, 0, loc),
		},
		{
			desc:     "unknown g",
			g:        adminModel.GranularityUnknown,
			t:        time.Date(2024, 2, 1, 0, 0, 0, 0, loc),
			expected: time.Date(2024, 2, 1, 0, 0, 0, 0, loc),
		},
	}

	for _, tc := range testCases {
		actual := incrementOneRowWrtGranularity(tc.g, tc.t)
		assert.True(t, tc.expected.Equal(actual), tc.desc)
	}
}
