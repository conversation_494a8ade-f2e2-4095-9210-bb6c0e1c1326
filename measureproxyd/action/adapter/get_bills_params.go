package adapter

import (
	"time"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/legacystat/lcdn"
	pb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measureproxyd/model"
)

// BuildModelGetBillsParams build model get bills param
func BuildModelGetBillsParams(
	x *pb.GetBillsParams,
) (*model.GetBillsParams, error) {
	var begin time.Time
	var end time.Time
	if x.Begin != nil {
		err := x.Begin.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		begin = x.Begin.AsTime()
	}

	if x.End != nil {
		err := x.End.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		end = x.End.AsTime()
	}
	return &model.GetBillsParams{
		UID:      x.GetUid(),
		Begin:    begin,
		End:      end,
		HaveZone: x.GetHaveZone(),
		ZoneCode: x.GetZoneCode(),
		Src:      x.GetSrc(),
		Debug:    x.GetDebug(),
	}, nil
}

// BuildPBGetLCDNBillsResp build pb lcdn bills
func BuildPBGetLCDNBillsResp(
	bills lcdn.ModelLCDNBillSync,
) (*pb.LCDNBillsResp, error) {

	resp := &pb.LCDNBillsResp{
		Bills: make([]*pb.LCDNBill, 0, len(bills.Bills)),
	}
	for _, bill := range bills.Bills {
		x, err := buildPBLCDNBill(bill)
		if err != nil {
			return nil, err
		}
		resp.Bills = append(resp.Bills, x)
	}
	return resp, nil
}
func buildPBLCDNBill(bill lcdn.Bill) (*pb.LCDNBill, error) {
	stairs := make([]*pb.LCDNPriceStair, 0)
	for _, stair := range bill.Price.Stairs {
		stairs = append(stairs, &pb.LCDNPriceStair{
			From:     stair.From,
			FromStr:  stair.FromStr,
			To:       stair.To,
			ToStr:    stair.ToStr,
			Price:    stair.Price.String(),
			Value:    stair.Value,
			ValueStr: stair.ValueStr,
			Money:    stair.Money.String(),
		})
	}
	return &pb.LCDNBill{
		BillId:         bill.BillID,
		Uid:            bill.UID,
		Month:          bill.Month,
		SignatoryId:    bill.SignatoryID,
		Item:           bill.Item,
		ItemStr:        bill.ItemStr,
		Algorithm:      bill.Algorithm,
		AlgorithmStr:   bill.AlgorithmStr,
		Aggregation:    bill.Aggregation,
		AggregationStr: bill.AggregationStr,
		Price: &pb.LCDNPrice{
			StairType:    bill.Price.StairType,
			StairTypeStr: bill.Price.StairTypeStr,
			Unit:         bill.Price.Unit,
			Stairs:       stairs,
		},
		UnitRate:        bill.UnitRate,
		Value:           bill.Value,
		ValueStr:        bill.ValueStr,
		SettlementMoney: bill.SettlementMoney.String(),
		Money:           bill.Money.String(),
		Adjustment:      bill.Adjustment.String(),
		AdjustReason:    bill.AdjustReason,
	}, nil
}
