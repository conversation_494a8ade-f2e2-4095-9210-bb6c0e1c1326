package adminModel

import (
	"context"
	"time"

	"github.com/samber/lo"

	"qiniu.io/pay/kirby/model/qrn"
)

type ResourceTypeDefinitionDetail struct {
	Code       string
	QRNMatches qrn.MatchExprArray
	EffectTime time.Time
	MMDs       []*MeasureMetricDefinitionDetail
	SPDs       []*SeriesPropertyDefinitionDetail
}

func (x *ResourceTypeDefinitionDetail) QueryMMD(mmCode string) *MeasureMetricDefinitionDetail {
	// NOTE: 如果有性能担忧就把 MMDs 变成 map[mmCode]mmd
	for _, mmd := range x.MMDs {
		if mmd.Code == mmCode {
			return mmd
		}
	}
	return nil
}

func (d *KirbyAdminDao) GetRTDDetailByCode(
	code string,
	effectTime time.Time,
	epoch time.Time,
) (*ResourceTypeDefinitionDetail, error) {
	rtd, err := d.RTD.GetByCode(code, effectTime, epoch)
	if err != nil {
		return nil, err
	}

	mmds, err := d.MMD.ListByRTDCodes([]string{code}, epoch)
	if err != nil {
		return nil, err
	}
	mmdDetails := lo.Map(mmds, func(x *MeasureMetricDefinition, _ int) *MeasureMetricDefinitionDetail { return x.ToDetail() })

	spds, err := d.SPD.ListByRTDCodes([]string{code}, epoch)
	if err != nil {
		return nil, err
	}
	spdDetails := lo.Map(spds, func(x *SeriesPropertyDefinition, _ int) *SeriesPropertyDefinitionDetail { return x.ToDetail() })

	return &ResourceTypeDefinitionDetail{
		Code:       rtd.Code,
		QRNMatches: rtd.QRNMatches,
		EffectTime: rtd.EffectTime,
		MMDs:       mmdDetails,
		SPDs:       spdDetails,
	}, nil
}

// FIXME: 不应该拿 effectTime
func (d *KirbyAdminDao) ListAllRTDDetails(
	effectTime time.Time,
	epoch time.Time,
) ([]*ResourceTypeDefinitionDetail, error) {
	rtds, err := d.RTD.ListAll(effectTime, epoch)
	if err != nil {
		return nil, err
	}
	rtdCodes := lo.Map(rtds, func(x *ResourceTypeDefinition, _ int) string { return x.Code })

	mmds, err := d.MMD.ListByRTDCodes(rtdCodes, epoch)
	if err != nil {
		return nil, err
	}
	mmdsByRTDCode := lo.GroupBy(mmds, func(x *MeasureMetricDefinition) string { return x.RTDCode })

	spds, err := d.SPD.ListByRTDCodes(rtdCodes, epoch)
	if err != nil {
		return nil, err
	}
	spdsByRTDCode := lo.GroupBy(spds, func(x *SeriesPropertyDefinition) string { return x.RTDCode })

	return lo.Map(rtds, func(x *ResourceTypeDefinition, _ int) *ResourceTypeDefinitionDetail {
		mmdDetails := lo.Map(
			mmdsByRTDCode[x.Code],
			func(x *MeasureMetricDefinition, _ int) *MeasureMetricDefinitionDetail { return x.ToDetail() },
		)
		spdDetails := lo.Map(
			spdsByRTDCode[x.Code],
			func(x *SeriesPropertyDefinition, _ int) *SeriesPropertyDefinitionDetail { return x.ToDetail() },
		)
		return &ResourceTypeDefinitionDetail{
			Code:       x.Code,
			QRNMatches: x.QRNMatches,
			EffectTime: x.EffectTime,
			MMDs:       mmdDetails,
			SPDs:       spdDetails,
		}
	}), nil
}

func (d *KirbyAdminDao) SaveRTDDetail(
	ctx context.Context,
	rtdd *ResourceTypeDefinitionDetail,
	epoch time.Time,
) (*ResourceTypeDefinitionDetail, error) {
	if epoch.IsZero() || epoch.UnixNano() == 0 {
		epoch = time.Now()
	}

	rtd := ResourceTypeDefinition{
		Code:       rtdd.Code,
		QRNMatches: rtdd.QRNMatches,
		EffectTime: rtdd.EffectTime,
		CreatedAt:  epoch,
	}

	mmds := lo.Map(rtdd.MMDs, func(x *MeasureMetricDefinitionDetail, _ int) *MeasureMetricDefinition {
		uk := generateMMDUniqueKey(epoch)
		return x.toModel(uk, rtdd.Code, epoch)
	})

	spds := lo.Map(rtdd.SPDs, func(x *SeriesPropertyDefinitionDetail, _ int) *SeriesPropertyDefinition {
		uk := generateSPDUniqueKey(epoch)
		return x.toModel(uk, rtdd.Code, epoch)
	})

	err := d.DoTransaction(func(txn *KirbyAdminDao) error {
		err := txn.RTD.Save(&rtd)
		if err != nil {
			return err
		}

		err = txn.MMD.BulkInsert(ctx, mmds)
		if err != nil {
			return err
		}

		err = txn.SPD.BulkInsert(spds)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return d.GetRTDDetailByCode(rtdd.Code, epoch, epoch)
}
