package model

import (
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// CouponLockRecord model definition
type CouponLockRecord struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`

	// UID 持有该锁的用户
	UID uint64 `gorm:"index:coupon_lock_record_uid"`

	// LockKey 锁的句柄
	LockKey string `gorm:"index:coupon_lock_record_lock_key"`

	// OrderHash 关联的 order hash
	// NOTE: 只用于第一版的券锁机制，用于降低 trade 系统的工作量。后续应该删除以保持券与订单系统的相对独立
	OrderHash string `gorm:"index:coupon_lock_record_order_hash"`

	// 关联的 union order hash
	UnionOrderHash string `gorm:"index:coupon_lock_record_union_order_hash"`

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// CouponLockRecordDao is data access object of CouponLockRecord model
type CouponLockRecordDao struct {
	base *dao.BaseDao
}

// NewCouponLockRecordDao is constructor of CouponLockRecordDao
func NewCouponLockRecordDao(base *dao.BaseDao) *CouponLockRecordDao {
	return &CouponLockRecordDao{
		base: base,
	}
}

// GetByID selects a CouponLockRecord by id
func (d *CouponLockRecordDao) GetByID(id uint64, expires ...time.Duration) (*CouponLockRecord, error) {
	model := &CouponLockRecord{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponLockRecord{}).
			Where("`id` = ?", id).
			First(model).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// GetByLockKey selects a CouponLockRecord by lock key
func (d *CouponLockRecordDao) GetByLockKey(lockKey string, expires ...time.Duration) (*CouponLockRecord, error) {
	model := &CouponLockRecord{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponLockRecord{}).
			Where("`lock_key` = ?", lockKey).
			First(value).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("lock_key", lockKey)
	}
	return model, nil
}

// GetByOrderHash selects a CouponLockRecord by order hash
func (d *CouponLockRecordDao) GetByOrderHash(orderHash string, expires ...time.Duration) (*CouponLockRecord, error) {
	model := &CouponLockRecord{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponLockRecord{}).
			Where("`order_hash` = ?", orderHash).
			First(value).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("order_hash", orderHash)
	}
	return model, nil
}

// GetByUnionOrderHash selects a CouponLockRecord by union order hash
func (d *CouponLockRecordDao) GetByUnionOrderHash(unionOrderHash string, expires ...time.Duration) (*CouponLockRecord, error) {
	model := &CouponLockRecord{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponLockRecord{}).
			Where("`union_order_hash` = ?", unionOrderHash).
			First(value).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("union_order_hash", unionOrderHash)
	}
	return model, nil
}

// Save inserts or updates a CouponLockRecord by id
func (d *CouponLockRecordDao) Save(model *CouponLockRecord, expires ...time.Duration) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Save(model).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// DeleteByID delete records of CouponLockRecord by id
func (d *CouponLockRecordDao) DeleteByID(id uint64) error {
	err := d.base.Execute(func(i any) error {
		return d.base.Delete(&CouponLockRecord{}, "`id` = ?", id).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return nil
}

// DeleteByLockKey delete records of CouponLockRecord by lock key
func (d *CouponLockRecordDao) DeleteByLockKey(lockKey string) error {
	err := d.base.Execute(func(i any) error {
		return d.base.Delete(&CouponLockRecord{}, "`lock_key` = ?", lockKey).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("lock_key", lockKey)
	}
	return nil
}

// DeleteByOrderHash delete records of CouponLockRecord by order hash
func (d *CouponLockRecordDao) DeleteByOrderHash(orderHash string) error {
	err := d.base.Execute(func(i any) error {
		return d.base.Delete(&CouponLockRecord{}, "`order_hash` = ?", orderHash).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("order_hash", orderHash)
	}
	return nil
}
