package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measured/model"
)

func TestPackageUserMapRoundTrip(t *testing.T) {
	x := &model.PackageUserMap{
		ID:         123,
		UID:        233,
		PackageID:  456,
		EffectTime: 1112223330000000,
		DeadTime:   2223334440000000,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	y, err := BuildPbPackageUserMap(x)
	assert.NoError(t, err)

	z, err := BuildModelPackageUserMap(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.UID, z.UID)
	assert.Equal(t, x.PackageID, z.PackageID)
	assert.Equal(t, x.EffectTime, z.EffectTime)
	assert.Equal(t, x.DeadTime, z.DeadTime)
	assert.Equal(t, x.CreatedAt.<PERSON><PERSON>ano(), z.CreatedAt.Unix<PERSON>ano())
	assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
}
