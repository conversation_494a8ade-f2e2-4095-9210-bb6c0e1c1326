package action_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/pay-sdk/wallet"
	"github.com/stretchr/testify/assert"
)

func TestScopeProductMapInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	walletClient := sandbox.walletClient

	const scopeID uint64 = 1
	ms := []wallet.ScopeProductMap{
		{
			ScopeId:   scopeID,
			ProductId: 1,
		},
		{
			ScopeId:   scopeID,
			ProductId: 2,
		},
		{
			ScopeId:   scopeID,
			ProductId: 3,
		},
	}
	assertFields := func(_map *wallet.ScopeProductMap, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(ms) }) {
			assert.Equal(t, ms[n].GetScopeId(), _map.GetScopeId(), "ScopeId")
			assert.Equal(t, ms[n].GetProductId(), _map.GetProductId(), "ProductId")
		}
	}
	ids := make(map[uint64]int, len(ms))

	// create, get by id, list all and count
	{
		for n, m := range ms {
			_map, err := walletClient.CreateScopeProductMap(context.Background(), &m)
			if assert.NoError(t, err, "CreateScopeProductMap") {
				assert.NotZero(t, _map.GetId(), "CreateScopeProductMap")
				ids[_map.GetId()] = n
			}

			_map, err = walletClient.GetScopeProductMapByID(context.Background(), &wallet.IDParam{Id: _map.GetId()})
			if assert.NoError(t, err, "GetScopeProductMapByID") {
				assertFields(_map, n, "GetScopeProductMapByID")
			}
		}
		list, err := walletClient.ListAllScopeProductMaps(context.Background(), &wallet.PagingParam{Page: 1, PageSize: 10})
		if assert.NoError(t, err, "ListAllScopeProductMaps") {
			assert.Len(t, ms, int(list.GetCount()), "ListAllScopeProductMaps")
			for _, _map := range list.GetScopeProductMaps() {
				assertFields(_map, ids[_map.GetId()], "ListAllScopeProductMaps")
			}
		}
		count, err := walletClient.CountAllScopeProductMaps(context.Background(), &empty.Empty{})
		if assert.NoError(t, err, "CountAllScopeProductMaps") {
			assert.Len(t, ms, int(count.GetCount()), "CountAllScopeProductMaps")
		}
	}

	// list by scope id
	{
		list, err := walletClient.ListScopeProductMapsByScopeID(context.Background(), &wallet.IDPagingParam{Id: scopeID, PageSize: 10, Page: 1})
		if assert.NoError(t, err, "ListScopeProductMapsByScopeID") {
			assert.Len(t, ms, int(list.GetCount()), "ListScopeProductMapsByScopeID")
			for _, _map := range list.GetScopeProductMaps() {
				if assert.Contains(t, ids, _map.GetId(), "ListScopeProductMapsByScopeID") {
					assertFields(_map, ids[_map.GetId()], "ListScopeProductMapsByScopeID")
				}
			}
		}
		count, err := walletClient.CountScopeProductMapsByScopeID(context.Background(), &wallet.IDParam{Id: scopeID})
		if assert.NoError(t, err, "CountScopeProductMapsByScopeID") {
			assert.Len(t, ms, int(count.GetCount()), "CountScopeProductMapsByScopeID")
		}
	}

	// get and update by id
	{
		for id, n := range ids {
			_map, err := walletClient.GetScopeProductMapByID(context.Background(), &wallet.IDParam{Id: id})
			if assert.NoError(t, err, "GetScopeProductMapByID") {
				assertFields(_map, n, "GetScopeProductMapByID")
			}
			ms[n].ProductId += 100
			_map.ProductId += 100
			_map, err = walletClient.UpdateScopeProductMapByID(context.Background(), &wallet.IDScopeProductMapParam{Id: _map.GetId(), ScopeProductMap: _map})
			if assert.NoError(t, err, "UpdateScopeProductMapByID") {
				assert.Equal(t, ms[n].GetProductId(), _map.GetProductId(), "UpdateScopeProductMapByID")
			}
		}
	}

	// delete
	{
		for id, n := range ids {
			_map, err := walletClient.DeleteScopeProductMapByID(context.Background(), &wallet.IDParam{Id: id})
			if assert.NoError(t, err, "DeleteScopeProductMapByID") {
				assertFields(_map, n, "DeleteScopeProductMapByID")
			}
		}
	}
}
