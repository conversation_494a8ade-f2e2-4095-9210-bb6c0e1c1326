package service

import (
	"context"
	"sort"

	"qiniu.io/pay/dictd/model"

	"github.com/qbox/bo-base/v4/sync/limiter"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"
)

// FindCitiesByProvinceCode 根据 province_code 获取所有 City 记录
func (s *DictBizService) FindCitiesByProvinceCode(
	ctx context.Context,
	provinceCode string,
) ([]*model.City, error) {
	l := logging.GetLogger(ctx)
	result, err := s.dictDao.City.FindCitiesByProvinceCode(provinceCode)
	if err != nil {
		l.WithFields(logrus.Fields{
			"provinceCode": provinceCode,
		}).WithError(err).Error("FindCitiesByProvinceCode failed")
		return nil, err
	}
	return result, nil
}

func (s *DictBizService) GetProvinceCitiesList(ctx context.Context) (result *model.ProvinceCitiesList, err error) {
	l := logging.GetLogger(ctx)

	var (
		gl        = limiter.NewGoroutineLimiter(2)
		cities    []*model.City
		provinces []*model.Province
		err1      error
		err2      error
	)

	gl.Go(func() {
		cities, err1 = s.dictDao.City.GetAll()
	})
	gl.Go(func() {
		provinces, err2 = s.dictDao.Province.GetAll()
	})
	gl.Wait()

	if err1 != nil {
		l.WithError(err1).Error("s.dictDao.City.GetAll() failed")
		err = err1
	}
	if err2 != nil {
		l.WithError(err2).Error("s.dictDao.Province.GetAll() failed")
		err = err2
	}
	if err != nil {
		return nil, err
	}

	var provinceCodeCitiesMap = make(map[string][]*model.SimpleCity, len(provinces))

	for _, obj := range cities {
		provinceCodeCitiesMap[obj.ProvinceCode] = append(provinceCodeCitiesMap[obj.ProvinceCode], &model.SimpleCity{
			CityCode: obj.Code,
			CityName: obj.Name,
		})
	}

	result = &model.ProvinceCitiesList{
		List: make([]*model.ProvinceCities, 0, len(provinces)),
	}
	// for 31 * 31 故 O^2 能接受
	for provinceCode, cs := range provinceCodeCitiesMap {
		obj := &model.ProvinceCities{
			ProvinceCode: provinceCode,
			Cities:       cs,
		}
		for _, tmp := range provinces {
			if tmp.Code == provinceCode {
				obj.ProvinceName = tmp.Name
			}
		}
		result.List = append(result.List, obj)
	}

	sort.Slice(result.List, func(i, j int) bool {
		return result.List[i].ProvinceCode < result.List[j].ProvinceCode
	})
	return result, nil
}
