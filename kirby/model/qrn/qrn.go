package qrn

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
)

var errMalformedQRNString = errors.New("malformed QRN string")

var qrnMatchRE = regexp.MustCompile(`^qrn:([^:]*):([^:]*):(\d+):(.*)$`)

// QRN 七牛资源名称（Qiniu Resource Name）。
//
// see https://cf.qiniu.io/pages/viewpage.action?pageId=58285862
type QRN struct {
	// Product 产品线 code
	Product string
	// Region 区域 code，可以为空
	Region string
	// UID 资源所属的 UID
	UID uint64
	// Resource 自由格式的资源标识符
	Resource string
}

func (x *QRN) String() string {
	return fmt.Sprintf("qrn:%s:%s:%d:%s", x.Product, x.Region, x.UID, x.Resource)
}

func Parse(x string) (QRN, error) {
	var result QRN
	err := ParseInto(x, &result)
	if err != nil {
		return QRN{}, err
	}
	return result, nil
}

func ParseInto(x string, dest *QRN) error {
	m := qrnMatchRE.FindStringSubmatch(x)
	if m == nil {
		return errMalformedQRNString
	}

	uid, err := strconv.ParseUint(m[3], 10, 64)
	if err != nil {
		// 由于前面已经用正则拦住非纯数字的情况了，实际是不可能发生的
		// 但由于本函数的签名使我们有机会返回 error，还是这么做
		return err
	}

	*dest = QRN{
		Product:  m[1],
		Region:   m[2],
		UID:      uid,
		Resource: m[4],
	}

	return nil
}
