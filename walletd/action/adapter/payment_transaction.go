package adapter

import (
	"time"

	tspb "google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/payment/model"
	wm "qiniu.io/pay/qpay/wallet/model"
)

// BuildPbPaymentTransactionBase convert payment txn to pb payment txn
func BuildPbPaymentTransaction(trans model.PaymentTransaction) (*pb.PaymentTransaction, error) {
	var createAt *tspb.Timestamp
	var updateAt *tspb.Timestamp
	var businessAt *tspb.Timestamp
	var relatedMonth *tspb.Timestamp
	var err error
	if trans.CreatedAt != nil {
		createAt = tspb.New(*trans.CreatedAt)
	}
	if trans.UpdatedAt != nil {
		updateAt = tspb.New(*trans.UpdatedAt)
	}
	if trans.BusinessAt != nil {
		businessAt = tspb.New(*trans.BusinessAt)
	}
	if trans.RelatedMonth != nil {
		relatedMonth = tspb.New(*trans.RelatedMonth)
	}

	pbWalletTxns, err := BuildPbWalletTransactions(trans.WalletTransactions)
	if err != nil {
		return nil, err
	}
	walletTransactionList := pb.WalletTransactionList{
		Txns: pbWalletTxns,
	}

	return &pb.PaymentTransaction{
		Id:              trans.ID,
		PaymentId:       trans.PaymentID,
		Uid:             trans.UID,
		EntryId:         trans.EntryID,
		EntryDesc:       trans.EntryDesc,
		EntryType:       pb.EntryType(trans.EntryType),
		PaymentAmount:   trans.PaymentAmount.ToInt64(),
		PaymentRemained: trans.PaymentRemained.ToInt64(),
		PaymentPaid:     trans.PaymentPaid.ToInt64(),
		PaymentStatus:   pb.PaymentStatus(trans.PaymentStatus),
		PaymentType:     pb.PaymentType(trans.PaymentType),
		RecordStatus:    pb.RecordStatus(trans.RecordStatus),
		CreatedAt:       createAt,
		UpdatedAt:       updateAt,
		Description:     trans.Description,
		SnapId:          trans.SnapID,
		Excode:          trans.Excode,
		Type:            trans.Type,
		Details:         trans.Details,
		RelatedMonth:    relatedMonth,
		Product:         trans.Product,
		Group:           trans.Group,
		Item:            trans.Item,

		OrderHash: trans.OrderHash,

		RewardCost: trans.RewardCost.ToInt64(),
		RewardType: pb.RewardType(trans.RewardType),
		RewardId:   trans.RewardID,

		Prefix: string(trans.Prefix),

		LegacyMoney:           int64(trans.Money),
		LegacyLeft:            int64(trans.Left),
		LegacyCash:            int64(trans.Cash),
		LegacyCoupon:          int64(trans.Coupon),
		LegacyNb:              int64(trans.FreeNb),
		WalletTransactionList: &walletTransactionList,
		LegacyAt:              businessAt,
		IsHide:                trans.IsHide,
	}, nil
}

// BuildPbPaymentTransactions build multi payment trans for pb
func BuildPbPaymentTransactions(trans []model.PaymentTransaction) ([]*pb.PaymentTransaction, error) {
	pbTrans := make([]*pb.PaymentTransaction, len(trans))
	for i, tr := range trans {
		pbTr, err := BuildPbPaymentTransaction(tr)
		if err != nil {
			return nil, err
		}
		pbTrans[i] = pbTr
	}
	return pbTrans, nil
}

// BuildPbOrderPaymentResponse builds DeductOrder response
func BuildPbOrderPaymentResponse(deductTxns []model.PaymentTransaction, rechargeTxn *model.PaymentTransaction) (*pb.OrderPaymentResponse, error) {
	pbTrans := make([]*pb.PaymentTransaction, len(deductTxns))
	for i, tr := range deductTxns {
		pbTr, err := BuildPbPaymentTransaction(tr)
		if err != nil {
			return nil, err
		}
		pbTrans[i] = pbTr
	}
	var rechargeTxnID string
	if rechargeTxn != nil {
		rechargeTxnID = rechargeTxn.PaymentID
	}
	return &pb.OrderPaymentResponse{
		Txns:          pbTrans,
		RechargeTxnId: rechargeTxnID,
	}, nil
}

// BuildListOrderPaymentTransRequest convert pb request to model.request
func BuildListOrderPaymentTransRequest(request *pb.ListOrderPaymentTransRequest) *model.ListPaymentTransRequest {

	return &model.ListPaymentTransRequest{
		Uid:           request.Uid,
		PaymentType:   enums.PaymentType(request.PaymentType),
		PaymentStatus: enums.PaymentStatus(request.PaymentStatus),
		EntryIds:      request.EntryIds,
	}
}

// BuildQueryCondition build query condition
func BuildQueryCondition(
	request *pb.ListPaymentTransConditionRequest,
) (*model.QueryCondition, error) {

	paymentTypeArr := make([]enums.PaymentType, 0)

	for _, pType := range request.PaymentType {
		paymentTypeArr = append(paymentTypeArr, enums.PaymentType(pType))
	}

	paymentStatusArr := make([]enums.PaymentStatus, 0)
	for _, pStatus := range request.PaymentStatus {
		paymentStatusArr = append(paymentStatusArr, enums.PaymentStatus(pStatus))
	}

	var startTime *time.Time
	var endTime *time.Time
	var relatedMonth *time.Time
	if request.From != nil && request.From.Seconds != 0 {
		err := request.From.CheckValid()
		if err != nil {
			return nil, err
		}
		from := request.From.AsTime()
		startTime = &from
	}
	if request.To != nil && request.To.Seconds != 0 {
		err := request.To.CheckValid()
		if err != nil {
			return nil, err
		}
		to := request.To.AsTime()
		endTime = &to
	}
	if request.RelatedMonth != nil {
		err := request.RelatedMonth.CheckValid()
		if err != nil {
			return nil, err
		}
		rm := request.RelatedMonth.AsTime()
		relatedMonth = &rm
	}

	return &model.QueryCondition{
		RecordStatus:     []enums.RecordStatus{enums.RecordStatusActive},
		UIDs:             []uint64{request.GetUid()},
		PaymentTypeArr:   paymentTypeArr,
		PaymentStatusArr: paymentStatusArr,
		StartTime:        startTime,
		EndTime:          endTime,
		Types:            request.Type,
		Prefix:           request.Prefix,
		Excodes:          request.Excode,
		SerialNum:        request.SerialNum,
		Offset:           request.Offset,
		Limit:            request.Limit,
		GotDetails:       request.GotDetails,
		RelatedMonth:     relatedMonth,
		IsHide:           request.IsHide,
		IgnoreAbandoned:  request.IgnoreAbandoned,
	}, nil

}

// BuildPbWalletTransaction build pb wallet transaction
func BuildPbWalletTransaction(trans wm.WalletTransaction) (*pb.WalletTransaction, error) {
	var trTime, businessAt, relatedMonth *tspb.Timestamp
	if trans.TrTime != nil {
		trTime = tspb.New(*trans.TrTime)
	}

	if trans.BusinessAt != nil {
		businessAt = tspb.New(*trans.BusinessAt)
	}

	if trans.RelatedMonth != nil {
		relatedMonth = tspb.New(*trans.RelatedMonth)
	}

	return &pb.WalletTransaction{
		Id:                 trans.ID,
		PaymentId:          trans.PaymentID,
		WalletId:           trans.WalletID,
		AssetId:            trans.AssetID,
		AssetType:          trans.AssetType.Value(),
		AssetTypeDesc:      trans.AssetType.Value(),
		TransType:          trans.TransType.Value(),
		TransTypeDesc:      trans.TransTypeDesc,
		WalletRequestId:    trans.WalletRequestID,
		Money:              int64(trans.Money),
		Uid:                trans.UID,
		EntryId:            trans.EntryID,
		EntryDesc:          trans.EntryDesc,
		EntryType:          trans.EntryType.Value(),
		TransDirection:     pb.TransDirection(trans.TransDirection),
		TransDirectionDesc: trans.TransDirectionDesc,

		RecordStatus: pb.RecordStatus(trans.RecordStatus),
		CreatedAt:    tspb.New(trans.CreatedAt),
		UpdatedAt:    tspb.New(trans.UpdatedAt),
		Description:  trans.Description,

		ReverseStatus:  int64(trans.ReverseStatus),
		ReverseTransId: trans.ReverseTransID,
		Remark:         trans.Remark,

		Before: int64(trans.Before),
		Change: int64(trans.Change),
		After:  int64(trans.After),
		Left:   int64(trans.Left),

		PayUid:       trans.PayUid,
		PayTool:      trans.PayTool,
		PayToolId:    trans.PayToolID,
		TrTime:       trTime,
		BusinessAt:   businessAt,
		RelatedMonth: relatedMonth,
		Details:      trans.Details,
		Ver:          trans.Ver,
		Excode:       trans.Excode,
		Prefix:       string(trans.Prefix),
		Type:         trans.Type,
		IsLegacy:     trans.Legacy,
	}, nil
}

// BuildPbWalletTransactions build pb wallet transactions
func BuildPbWalletTransactions(trans []wm.WalletTransaction) ([]*pb.WalletTransaction, error) {
	pbTrans := make([]*pb.WalletTransaction, len(trans))
	for i, tr := range trans {
		pbTr, err := BuildPbWalletTransaction(tr)
		if err != nil {
			return nil, err
		}
		pbTrans[i] = pbTr
	}
	return pbTrans, nil
}

func BuildIDList(request *pb.CodeParamList) []string {
	ids := make([]string, 0)
	for _, id := range request.Code {
		ids = append(ids, id.Code)
	}
	return ids
}

func BuildWalletTransactionQuery(request *pb.ListWalletTransConditionRequest) (*wm.WalletQueryCondition, error) {
	entryTypes := make([]enums.EntryType, 0)
	if len(request.EntryTypes) > 0 {
		for _, typ := range request.EntryTypes {
			entryTypes = append(entryTypes, enums.EntryType(typ))
		}
	}

	assetTypes := make([]enums.AssetType, 0)
	if len(request.AssetTypes) > 0 {
		for _, typ := range request.AssetTypes {
			assetTypes = append(assetTypes, enums.AssetType(typ))
		}
	}

	var startTime *time.Time
	var endTime *time.Time
	if request.From != nil && request.From.Seconds != 0 {
		err := request.From.CheckValid()
		if err != nil {
			return nil, err
		}
		from := request.From.AsTime()
		startTime = &from
	}
	if request.To != nil && request.To.Seconds != 0 {
		err := request.To.CheckValid()
		if err != nil {
			return nil, err
		}
		to := request.To.AsTime()
		endTime = &to
	}

	return &wm.WalletQueryCondition{
		UID:        request.Uid,
		PayUID:     request.PayUid,
		EntryIDs:   request.EntryIds,
		EntryTypes: entryTypes,
		AssetTypes: assetTypes,
		PaymentIDs: request.PaymentIds,
		StartTime:  startTime,
		EndTime:    endTime,
		Offset:     request.Offset,
		Limit:      request.Limit,
	}, nil
}

func BuildUserBalance(balance *model.UserBalance) *pb.UserBalance {
	return &pb.UserBalance{
		Cash:           int64(balance.CashBalance),
		NiuCoin:        int64(balance.NiuCoinBalance),
		Voucher:        int64(balance.VoucherBalance),
		Balance:        int64(balance.TotalBalance),
		Arrears:        int64(balance.Arrears),
		DepositCash:    int64(balance.DepositCash),
		DepositNiuCoin: int64(balance.DepositNiuCoin),
		ActiveVoucher:  int64(balance.ActiveVoucher),
	}
}

func BuildUserDetail(userDetail *model.UserDetail) (*pb.UserDetail, error) {
	txns, err := BuildPbPaymentTransactions(userDetail.PaymentTransactions)
	if err != nil {
		return nil, err
	}
	cashes, err := BuildPbWalletItemList(userDetail.Cashes)
	if err != nil {
		return nil, err
	}
	niuCoins, err := BuildPbWalletItemList(userDetail.NiuCoins)
	if err != nil {
		return nil, err
	}
	vouchers, err := BuildPbVoucherItemList(userDetail.Vouchers)
	if err != nil {
		return nil, err
	}
	return &pb.UserDetail{
		UserBalance:            BuildUserBalance(userDetail.UserBalance),
		PaymentTransactionList: &pb.PaymentTransactionList{Txns: txns},
		Cashes:                 cashes,
		NiuCoins:               niuCoins,
		Vouchers:               vouchers,
	}, nil
}
