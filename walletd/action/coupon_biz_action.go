package action

import (
	"context"

	"github.com/qbox/pay-sdk/middleware/logging"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/action/adapter"
)

// BindCouponRebate bind a new coupon_rebate onto uid
func (a *WalletAction) BindCouponRebate(
	ctx context.Context,
	req *pb.BindCouponRebateParam,
) (*pb.BindCouponRebateResp, error) {
	param, err := adapter.BuildBindCouponRebateParam(req)
	if err != nil {
		return nil, err
	}

	m, err := a.walletBizSrv.BindCouponRebate(ctx, param)
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbBindCouponRebateResp(m)
}

// UnbindCouponRebate unbinds a given coupon_rebate from uid
// NOTE: 目前仅解绑 rebate 绑定了一个用户的情况，若 rebate 绑定了多个用户那么报错
func (a *WalletAction) UnbindCouponRebate(
	ctx context.Context,
	req *pb.CodeParam,
) (*empty.Empty, error) {
	err := a.walletBizSrv.UnbindCouponRebate(ctx, req.GetCode())
	if err != nil {
		return nil, err
	}

	return &empty.Empty{}, nil
}

// UpgradeCouponRebate upgrade coupon_rebate for specified uid
func (a *WalletAction) UpgradeCouponRebate(
	ctx context.Context,
	req *pb.UpgradeCouponRebateParam,
) (*pb.BindCouponRebateResp, error) {
	param, err := adapter.BuildUpgradeCouponRebateParam(req)
	if err != nil {
		return nil, err
	}

	m, err := a.walletBizSrv.UpgradeCouponRebate(ctx, param)
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbBindCouponRebateResp(m)
}

// CreateDiscountRebateAndRegenBills 批量创建折扣、返利优惠券，并发起重出账
func (a *WalletAction) CreateDiscountRebateAndRegenBills(
	ctx context.Context,
	req *pb.CreateDiscountRebateAndRegenBillsReq,
) (*empty.Empty, error) {

	log := logging.GetLogger(ctx)
	// 参数 adapter
	couponDiscounts, couponRebates, err := adapter.BuildCreateDiscountRebate(req)
	if err != nil {
		log.Errorf("<WalletAction.CreateDiscountRebateAndRegenBills> BuildCreateDiscountRebate error: %+v", err)
		return nil, err
	}

	err = a.walletBizSrv.CreateDiscountRebateAndRegenBills(ctx, req.GetUid(), couponDiscounts, couponRebates, req.GetNeedRegenBills())
	if err != nil {
		log.Errorf("<WalletAction.CreateDiscountRebateAndRegenBills> walletBizSrv.CreateDiscountRebateAndRegenBills error: %+v", err)
		return nil, err
	}

	return &empty.Empty{}, nil
}
