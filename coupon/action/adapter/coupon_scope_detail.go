package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/coupon/service"
)

// ComposeCouponScopeDetail 拼接抵用券适用范围详情
func ComposeCouponScopeDetail(
	scope *service.CouponScope,
	isUnlimitedScope bool,
) (*pb.CouponScopeDetail, error) {
	if scope == nil {
		// TODO: 这么处理合适吗？
		return nil, nil
	}

	// 目前只支持订单 scope
	scopeType := pb.SCOPE_TYPE_ORDER

	sellers := make([]*pb.OrderCouponSellerScope, 0, len(scope.SellerIdsExclude)+len(scope.SellerIdsInclude))
	for _, id := range scope.SellerIdsExclude {
		sellers = append(sellers, &pb.OrderCouponSellerScope{
			SellerId:   id,
			IsExcluded: true,
		})
	}
	for _, id := range scope.SellerIdsInclude {
		sellers = append(sellers, &pb.OrderCouponSellerScope{
			SellerId:   id,
			IsExcluded: false,
		})
	}

	products := make([]*pb.OrderCouponProductScope, 0, len(scope.ProductIdsExclude)+len(scope.ProductIdsInclude))
	for _, id := range scope.ProductIdsExclude {
		products = append(products, &pb.OrderCouponProductScope{
			ProductId:  id,
			IsExcluded: true,
		})
	}
	for _, id := range scope.ProductIdsInclude {
		products = append(products, &pb.OrderCouponProductScope{
			ProductId:  id,
			IsExcluded: false,
		})
	}

	skus := make([]*pb.OrderCouponSKUScope, 0, len(scope.SkuIdsExclude)+len(scope.SkuIdsInclude))
	for _, id := range scope.SkuIdsExclude {
		skus = append(skus, &pb.OrderCouponSKUScope{
			SkuId:      id,
			IsExcluded: true,
		})
	}
	for _, id := range scope.SkuIdsInclude {
		skus = append(skus, &pb.OrderCouponSKUScope{
			SkuId:      id,
			IsExcluded: false,
		})
	}

	packages := make([]*pb.OrderCouponPackageScope, 0, len(scope.PackageIdsInclude)+len(scope.PackageIdsExclude))
	for _, id := range scope.PackageIdsExclude {
		packages = append(packages, &pb.OrderCouponPackageScope{
			PackageId:  id,
			IsExcluded: true,
		})
	}
	for _, id := range scope.PackageIdsInclude {
		packages = append(packages, &pb.OrderCouponPackageScope{
			PackageId:  id,
			IsExcluded: false,
		})
	}

	return &pb.CouponScopeDetail{
		IsUnlimitedScope: isUnlimitedScope,
		Type:             scopeType,
		Sellers:          sellers,
		Products:         products,
		Skus:             skus,
		Packages:         packages,
	}, nil
}
