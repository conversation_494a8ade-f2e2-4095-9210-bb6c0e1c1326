package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/action/adapter"
)

// CreatePackageWithItems 创建 Package 及其包含的 PackageItems
func (a *MeasureAction) CreatePackageWithItems(
	ctx context.Context,
	req *pb.PackageWithItems,
) (*pb.PackageWithItemsResp, error) {
	param, err := adapter.BuildPackageWithItems(req)
	if err != nil {
		return nil, err
	}

	m, err := a.srv.CreatePackageWithItems(ctx, param)
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbPackageWithItemsResp(m)
}

// GetPackageWithItemsByID 根据 ID 查询 Package 详情
func (a *MeasureAction) GetPackageWithItemsByID(
	ctx context.Context,
	req *pb.IDParam,
) (*pb.PackageWithItemsResp, error) {
	m, err := a.srv.GetPackageWithItemsByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbPackageWithItemsResp(m)

}

// ListPackageItemsByUIDAndTimePoint 查询用户在指定时间点的生效的所有 PackageItem
func (a *MeasureAction) ListPackageItemsByUIDAndTimePoint(
	ctx context.Context,
	req *pb.UIDTimePointQuery,
) (*pb.PackageItemList, error) {
	param, err := adapter.BuildUIDTimePointQuery(req)
	if err != nil {
		return nil, err
	}

	ms, err := a.srv.ListPackageItemsByUIDAndTimePoint(ctx, param)
	if err != nil {
		return nil, err
	}

	l := &pb.PackageItemList{
		PackageItems: make([]*pb.PackageItem, len(ms)),
		Count:        uint64(len(ms)),
	}

	for i, packageItem := range ms {
		m, err := adapter.BuildPbPackageItem(&packageItem)
		if err != nil {
			return nil, err
		}

		l.PackageItems[i] = m
	}
	return l, nil
}

// ListPackageItemsByUIDAndTimeRange 查询用户在指定时间段生效的所有 PackageItem
func (a *MeasureAction) ListPackageItemsByUIDAndTimeRange(
	ctx context.Context,
	req *pb.UIDTimeRangeQuery,
) (*pb.PackageItemList, error) {
	param, err := adapter.BuildUIDTimeRangeQuery(req)
	if err != nil {
		return nil, err
	}

	ms, err := a.srv.ListPackageItemsByUIDAndTimeRange(ctx, param)
	if err != nil {
		return nil, err
	}

	l := &pb.PackageItemList{
		PackageItems: make([]*pb.PackageItem, len(ms)),
		Count:        uint64(len(ms)),
	}

	for i, packageItem := range ms {
		m, err := adapter.BuildPbPackageItem(&packageItem)
		if err != nil {
			return nil, err
		}

		l.PackageItems[i] = m
	}
	return l, nil
}

// BindPackage 为用户绑定 Package
func (a *MeasureAction) BindPackage(
	ctx context.Context,
	req *pb.BindPackageParam,
) (*pb.BindPackageResp, error) {
	param, err := adapter.BuildBindPackageParam(req)
	if err != nil {
		return nil, err
	}

	m, err := a.srv.BindPackage(ctx, param)
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbBindPackageResp(m)
}

// UnbindPackage 为用户解绑 Package
func (a *MeasureAction) UnbindPackage(
	ctx context.Context,
	req *pb.UnbindPackageParam,
) (*pb.UnbindPackageResp, error) {
	param, err := adapter.BuildUnbindPackageParam(req)
	if err != nil {
		return nil, err
	}
	err = a.srv.UnbindPackage(ctx, param)
	if err != nil {
		return nil, err
	}
	return &pb.UnbindPackageResp{}, nil
}

// UpgradePackage 为用户升级 Package
func (a *MeasureAction) UpgradePackage(
	ctx context.Context,
	req *pb.UpgradePackageParam,
) (*pb.BindPackageResp, error) {
	param, err := adapter.BuildUpgradePackageParam(req)
	if err != nil {
		return nil, err
	}

	m, err := a.srv.UpgradePackage(ctx, param)
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbBindPackageResp(m)
}
