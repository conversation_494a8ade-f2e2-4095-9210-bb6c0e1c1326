package adapter

import (
	pb "github.com/qbox/pay-sdk/kirby"
	"google.golang.org/protobuf/types/known/timestamppb"

	"qiniu.io/pay/kirby/model/dataplaneModel"
)

func buildPbSeriesDetail(in *dataplaneModel.SeriesDetail) *pb.SeriesDetail {
	return &pb.SeriesDetail{
		SeriesKey: in.SeriesKey,
		Qrn:       in.QRN,
		Mm:        in.MM,
		Props:     BuildPbKVsFromSP(in.Props),
		Start:     timestamppb.New(in.Start),
		End:       timestamppb.New(in.End),
		MbUuid:    in.MBUUID,
		Uid:       in.UID,
		RtdCode:   in.RTDCode,
		AggrCode:  in.AggRCode,
		Unit:      in.Unit,
		G:         pb.Granularity(in.G),
		Data:      in.Data,
		Ctime:     timestamppb.New(in.Ctime),
	}
}

func buildPbSeriesDetails(in []*dataplaneModel.SeriesDetail) []*pb.SeriesDetail {
	if in == nil {
		return nil
	}

	y := make([]*pb.SeriesDetail, len(in))
	for i, x := range in {
		y[i] = buildPbSeriesDetail(x)
	}
	return y
}

func BuildPbSeriesDetailList(
	in *dataplaneModel.SeriesDetailList,
) *pb.SeriesDetailList {
	return &pb.SeriesDetailList{
		Data:  buildPbSeriesDetails(in.Data),
		Count: in.Count,
	}
}
