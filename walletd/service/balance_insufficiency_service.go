package service

import (
	"context"
	"math"
	"net/http"
	"strconv"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/pay-sdk/base/account"
	"github.com/qbox/pay-sdk/middleware/logging"
	notificationClient "github.com/qbox/pay-sdk/notification-v2/client"
	notificationOperations "github.com/qbox/pay-sdk/notification-v2/client/operations"
	httpModels "github.com/qbox/pay-sdk/notification-v2/models"
	walletClient "github.com/qbox/pay-sdk/v3/walletEx/client"
	walletOperations "github.com/qbox/pay-sdk/v3/walletEx/client/operations"

	"qiniu.io/pay/walletd/config"
	"qiniu.io/pay/walletd/model"
)

const channelID = 22
const timeFormat = "2006-01-02"
const toUserTmplName = "balance_insufficiency_notice"

// IBalanceInsufficiencyService defines balanceInsufficiency service related interface
type IBalanceInsufficiencyService interface {
	NotifyBalanceInsufficiency(ctx context.Context, now time.Time) (total, failureCnt int, err error)
}

// BalanceInsufficiencyService is business of BalanceInsufficiency service
type BalanceInsufficiencyService struct {
	balanceInsufficiencyDao *model.BalanceInsufficiencyPredictionDao
	conf                    *config.WalletdConfig
	notificationClient      *notificationClient.NotificationV2
	walletClient            *walletClient.WalletEx
	accTransport            *account.Transport
}

type emailData struct {
	CurrentDate       string `json:"current_date"`
	Email             string `json:"email"`
	InsufficiencyDays uint32 `json:"insufficiency_days"`
	UID               int64  `json:"uid"`
}

// NewBalanceInsufficiencyService is constructor of BalanceInsufficiencyService
func NewBalanceInsufficiencyService(
	conf *config.WalletdConfig,
	bDao *model.BalanceInsufficiencyPredictionDao,
	noticeClient *notificationClient.NotificationV2,
	walletV3Client *walletClient.WalletEx,
	accTransport *account.Transport,
) (*BalanceInsufficiencyService, error) {
	return &BalanceInsufficiencyService{
		balanceInsufficiencyDao: bDao,
		conf:                    conf,
		notificationClient:      noticeClient,
		walletClient:            walletV3Client,
		accTransport:            accTransport,
	}, nil
}

// NotifyBalanceInsufficiency notifies users with insufficient balance in 7/3/1 days
func (s *BalanceInsufficiencyService) NotifyBalanceInsufficiency(
	ctx context.Context,
	now time.Time,
) (total, failureCnt int, err error) {
	loc := tz.MustLocationFromCtx(ctx)
	log := logging.GetLogger(ctx)
	insufficiencyInfos, err := s.balanceInsufficiencyDao.GetBalanceInsufficiencyUIDs(now)
	if err != nil {
		return 0, 0, errors.Annotate(err, "invoke balanceInsufficiencyDao.GetBalanceInsufficiencyUIDs error")
	}

	total = len(insufficiencyInfos)
	failureUIDs := make([]uint32, 0)

	notificationParams := notificationOperations.NewSendMessageParamsWithHTTPClient(&http.Client{Transport: s.accTransport}).WithContext(ctx)
	walletParams := walletOperations.NewOverviewParamsWithHTTPClient(&http.Client{Transport: s.accTransport}).WithContext(ctx)

	for _, info := range insufficiencyInfos {
		failed := false
		// 如果昨天预测的天数与今天的一样，那么不发邮件
		if info.LastLeastUnaffordableDays == info.LeastUnaffordableDays {
			continue
		}

		// 获取用户实时的 overview 信息，用于双重校验（数仓数据不具有时效性）
		overviewResp, err1 := s.walletClient.Operations.Overview(walletParams.WithUID(int64(info.UID)))
		if err1 != nil {
			log.Warnf("NotifyBalanceInsufficiency get user: %+v overview error,%+v", info.UID, errors.Trace(err1))
			failed = true
			failureUIDs = append(failureUIDs, info.UID)
			continue
		}
		overview := overviewResp.GetPayload()
		log.Infof("NotifyBalanceInsufficiency uid:%+v invoke overview: %+v", info.UID, overview)

		// 计算可用额度是否小于 0；可用额度 = 现金 + 牛币 - 未支付流水 + 信用额度 - 实时消费（如果小于 0，那么跳过）
		availableBalance := float64(overview.Cash + overview.Nb +
			overview.CreditLine - overview.UncompletedTsMoney - overview.AllUndeductBillsMoney)

		if availableBalance <= 0 {
			continue
		}

		today := time.Now().In(loc)
		firstDayOfMonth := base.ThisMonth(today)
		offset := float64(today.Day() - firstDayOfMonth.Day())

		// 当月已发生天数 >=6
		if offset <= 5 {
			continue
		}

		// 已使用未出账 + 已出账未扣费
		if overview.AllUndeductBillsMoney <= 0 {
			continue
		}
		// 计算当前可用额度能支付天数(天数是否要事先取整)
		minUnaffordableDays := math.Ceil(availableBalance / (float64(overview.AllUndeductBillsMoney) / offset))
		log.Infof("NotifyBalanceInsufficiency availableBalance: %+v, allUndeductBillsMoney: %+v, offset: %+v, minUnaffordableDays: %+v",
			availableBalance, overview.AllUndeductBillsMoney, offset, minUnaffordableDays)
		if minUnaffordableDays > 7 {
			continue
		}

		err1 = s.sendNotificationWithParams(ctx, notificationParams, emailData{
			Email:             info.Email,
			InsufficiencyDays: info.LeastUnaffordableDays,
			UID:               int64(info.UID),
		}, toUserTmplName)
		if err1 != nil {
			log.Warnf("NotifyBalanceInsufficiency notify user error,%+v", errors.Trace(err1))
			failed = true
			failureUIDs = append(failureUIDs, info.UID)
		}

		if failed {
			failureCnt++
		}
	}
	if failureCnt != 0 {
		log.Warnf("NotifyBalanceInsufficiency failure UIDs : %+v", failureUIDs)
	}

	return total, failureCnt, nil
}

func (s *BalanceInsufficiencyService) sendNotificationWithParams(ctx context.Context,
	params *notificationOperations.SendMessageParams, data emailData, tmplName string) (err error) {

	data.CurrentDate = time.Now().Format(timeFormat)
	msg := &httpModels.SendMessageReq{
		UID:        data.UID,
		TemplateID: tmplName,
		ChannelID:  strconv.Itoa(channelID),
	}
	msg.TemplateData = data
	params.WithBody(msg)
	_, err = s.notificationClient.Operations.SendMessage(params)
	if err != nil {
		return errors.Errorf("invoke send error: %+v,%+v", err, data)
	}

	return
}
