package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestItemDataTypeUnit(t *testing.T) {
	sandbox := buildSandbox(t)

	dictService := sandbox.dictService

	const dataTypeID uint64 = 1
	us := []model.ItemDataTypeUnit{
		{
			DataTypeID: dataTypeID,
			Name:       "KB",
			Power:      1,
		},
		{
			DataTypeID: dataTypeID,
			Name:       "MB",
			Power:      2,
		},
	}
	assertFields := func(group *model.ItemDataTypeUnit, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(us) }) {
			assert.Equal(t, us[n].DataTypeID, group.DataTypeID, msgAndArgs...)
			assert.Equal(t, us[n].Name, group.Name, msgAndArgs...)
			assert.Equal(t, us[n].Power, group.Power, msgAndArgs...)
		}
	}
	ctx := context.TODO()
	ids := make(map[uint64]int, len(us))
	for n, _t := range us {
		unit, err := dictService.CreateItemDataTypeUnit(ctx, &_t)
		if assert.NoError(t, err, "CreateItemDataTypeUnit") {
			assert.NotZero(t, unit.ID, "CreateItemDataTypeUnit return zero id")
			ids[unit.ID] = n
		}

		unit, err = dictService.GetItemDataTypeUnitByID(ctx, unit.ID)
		if assert.NoError(t, err, "GetItemDataTypeUnitByID") {
			assertFields(unit, n, "GetItemDataTypeUnitByID fields not match")
		}

		unit.Name += "suffix1"
		us[n].Name += "suffix1"
		unit, err = dictService.UpdateItemDataTypeUnitByID(ctx, unit.ID, unit)
		if assert.NoError(t, err, "UpdateItemDataTypeUnitByID") {
			assert.NotZero(t, unit.ID, "UpdateItemDataTypeUnitByID return zero id")
		}
	}

	groups, err := dictService.ListItemDataTypeUnitsByItemDataTypeID(ctx, dataTypeID, 0, 10)
	if assert.NoError(t, err, "ListItemDataTypeUnitsByItemDataTypeID") {
		assert.Len(t, groups, len(us), "ListItemDataTypeUnitsByItemDataTypeID unexpected length")
		count, err := dictService.CountItemDataTypeUnitsByItemDataTypeID(ctx, dataTypeID)
		if assert.NoError(t, err, "CountItemDataTypeUnitsByItemDataTypeID") {
			assert.Equal(t, uint64(len(us)), count, "CountItemDataTypeUnitsByItemDataTypeID count unexpected")
		}
		for _, group := range groups {
			if assert.Contains(t, ids, group.ID) {
				assertFields(&group, ids[group.ID])
			}
		}
	}

	for id, n := range ids {
		group, err := dictService.DeleteItemDataTypeUnitByID(ctx, id)
		if assert.NoError(t, err, "DeleteItemDataTypeUnitByID") {
			assertFields(group, n)
		}
	}

	count, err := dictService.CountItemDataTypeUnitsByItemDataTypeID(ctx, dataTypeID)
	if assert.NoError(t, err, "CountItemDataTypeUnitsByItemDataTypeID") {
		assert.Equal(t, uint64(0), count, "CountItemDataTypeUnitsByItemDataTypeID count after delete all is not zero")
	}
}
