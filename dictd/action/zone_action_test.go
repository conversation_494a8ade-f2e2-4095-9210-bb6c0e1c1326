package action_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/pay-sdk/dict"
	"github.com/stretchr/testify/assert"
)

func TestZoneInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	dictClient := sandbox.dictClient

	zs := []dict.Zone{
		{
			Code:        1,
			Name:        "qiniu1",
			Title:       "七牛机房1",
			Description: "七牛的机房1",
			Remark:      "测试Zone-1",
		},
		{
			Code:        2,
			Name:        "qiniu2",
			Title:       "七牛机房2",
			Description: "七牛的机房2",
			Remark:      "测试Zone-2",
		},
		{
			Code:        3,
			Name:        "qiniu3",
			Title:       "七牛机房3",
			Description: "七牛的机房3",
			Remark:      "测试Zone-3",
		},
	}
	assertFields := func(zone *dict.Zone, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(zs) }) {
			assert.Equal(t, zs[n].GetName(), zone.GetName(), msgAndArgs...)
			assert.Equal(t, zs[n].GetCode(), zone.GetCode(), msgAndArgs...)
			assert.Equal(t, zs[n].GetDescription(), zone.GetDescription(), msgAndArgs...)
			assert.Equal(t, zs[n].GetRemark(), zone.GetRemark(), msgAndArgs...)
		}
	}
	ids := make(map[uint64]int, len(zs))
	codes := make(map[uint64]int64, len(zs))
	for n, z := range zs {
		zone, err := dictClient.CreateZone(context.Background(), &z)
		if assert.NoError(t, err, "CreateZone") {
			assert.NotZero(t, zone.GetId(), "CreateZone")
			ids[zone.GetId()] = n
			codes[zone.GetId()] = z.GetCode()
		}

		zone, err = dictClient.GetZoneByID(context.Background(), &dict.IDParam{Id: zone.GetId()})
		if assert.NoError(t, err, "GetZoneByID") {
			assertFields(zone, n, "GetZoneByID")
		}

		zone.Name += z.GetName()
		zs[n].Name += z.GetName()
		zone, err = dictClient.UpdateZoneByID(context.Background(), &dict.IDZoneParam{Id: zone.GetId(), Zone: zone})
		if assert.NoError(t, err, "UpdateZoneByID") {
			assert.Equal(t, zone.GetId(), zone.GetId(), "UpdateZoneByID")
		}

		zone, err = dictClient.GetZoneByCode(context.Background(), &dict.ZoneCodeParam{Code: z.Code})
		if assert.NoError(t, err, "GetZoneByCode") {
			assertFields(zone, n, "GetZoneByCode")
		}

		zone.Description += z.GetDescription()
		zs[n].Description += z.GetDescription()
		zone, err = dictClient.UpdateZoneByCode(context.Background(), &dict.CodeZoneParam{Code: zone.GetCode(), Zone: zone})
		if assert.NoError(t, err, "UpdateZoneByCode") {
			assert.Equal(t, zs[n].GetDescription(), zone.GetDescription(), "UpdateZoneByCode")
		}
	}

	count, err := dictClient.CountAllZones(context.Background(), &empty.Empty{})
	if assert.NoError(t, err, "CountAllZones") {
		assert.Len(t, zs, int(count.GetCount()), "CountAllZones")
	}

	list, err := dictClient.ListAllZones(context.Background(), &dict.PagingParam{Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListAllZones") {
		assert.Len(t, zs, int(list.Count), "ListAllZones")
		for _, z := range list.GetZones() {
			if assert.Contains(t, ids, z.GetId(), "ListAllZones") {
				assertFields(z, ids[z.GetId()], "ListAllZones")
			}
		}
	}

	for id, n := range ids {
		if id%2 == 0 {
			zone, err := dictClient.DeleteZoneByID(context.Background(), &dict.IDParam{Id: id})
			if assert.NoError(t, err, "DeleteZoneByID") {
				assertFields(zone, n, "DeleteZoneByID")
			}
		} else {
			zone, err := dictClient.DeleteZoneByCode(context.Background(), &dict.ZoneCodeParam{Code: codes[id]})
			if assert.NoError(t, err, "DeleteZoneByCode") {
				assertFields(zone, n, "DeleteZoneByCode")
			}
		}
	}
}
