package stat

import (
	"context"
	"sort"
	"time"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/legacystat/common"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/bo-base/v4/zone"
	mppb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measured/model"
)

// getUsers 拉某产品线某区域有量用户列表
func (s *measuredStatClient) getUsers(
	ctx context.Context,
	p *model.GetUsersParams,
) (model.UsersResp, error) {
	src, ok := common.StatSrcKindFromProductCode(p.ProductCode)
	if !ok {
		if p.ProductCode != "kodo" {
			return nil, ErrUnsupportedProduct{ProductCode: p.ProductCode}
		}

		// p.ProductCode == "kodo"
		// kodo 的用户列表计量源是 allstat 这个，因为上面那段逻辑是公共的，因此目前只能在此处写死
		src = mppb.STAT_SRC_ALLSTAT
	}

	if s.enableKirbyDatapath && shouldEnableKirbyDatapath(src) {
		return s.getUsersWithKirby(ctx, p)
	}

	req := &mppb.GetUsersParams{
		Begin:    timestamppb.New(p.Begin),
		End:      timestamppb.New(p.End),
		HaveZone: true,
		ZoneCode: uint32(p.ZoneCode),
		Src:      src,
		Debug:    p.Debug,
	}
	resp, err := s.mpClient.ProxyGetUsers(ctx, req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	result := make(model.UsersResp, len(resp.Uids))
	for i, uid := range resp.Uids {
		result[i] = uint64(uid)
	}

	return result, nil
}

// getUsersForMiku 拉取 miku 产品线有量用户列表

// miku 产品线其实是 kodo、fusion、dora、vrman、aitoken 五合一
func (s *measuredStatClient) getUsersForMiku(
	ctx context.Context,
	p *model.GetUsersParams,
) (model.UsersResp, error) {
	p.Debug = true // todo remove after test
	var params []*model.GetUsersParams
	switch zone.Zone(p.ZoneCode) {
	case zone.ZoneFusionChina, zone.ZoneFusionAMEU,
		zone.ZoneFusionAsia, zone.ZoneFusionSEA,
		zone.ZoneFusionSA, zone.ZoneFusionOC:
		params = []*model.GetUsersParams{
			{
				ProductCode: zone.ProductFusion.String(),
				Begin:       p.Begin,
				End:         p.End,
				ZoneCode:    p.ZoneCode,
				Debug:       p.Debug,
			},
		}
	case zone.ZoneHanoi, zone.ZoneDallas, zone.ZoneSG2:
		params = []*model.GetUsersParams{
			{
				ProductCode: zone.ProductKodo.String(),
				Begin:       p.Begin,
				End:         p.End,
				ZoneCode:    p.ZoneCode,
				Debug:       p.Debug,
			},
			{
				ProductCode: zone.ProductMPS.String(),
				Begin:       p.Begin,
				End:         p.End,
				ZoneCode:    p.ZoneCode,
				Debug:       p.Debug,
			},
		}
	case zone.ZoneVrmanMainland:
		params = []*model.GetUsersParams{{
			ProductCode: zone.ProductVrman.String(),
			Begin:       p.Begin,
			End:         p.End,
			ZoneCode:    p.ZoneCode,
			Debug:       p.Debug,
		}}
	case zone.ZoneAITokenMainland:
		params = []*model.GetUsersParams{{
			ProductCode: zone.ProductAIToken.String(),
			Begin:       p.Begin,
			End:         p.End,
			ZoneCode:    p.ZoneCode,
			Debug:       p.Debug,
		}}
	default:
		return nil, nil
	}

	fn := func(usersParam *model.GetUsersParams) (model.UsersResp, error) {
		return s.getUsers(ctx, usersParam)
	}
	res, err := resultgroup.ParallelMap(params, fn)
	if err != nil {
		return nil, err
	}
	users := make(model.UsersResp, 0)
	for _, re := range res {
		users = append(users, re...)
	}
	return base.UniqueIntSlice(users), nil
}

// getUsersWithKirby 拉某产品线某区域有量用户列表（Kirby datapath）
func (s *measuredStatClient) getUsersWithKirby(
	ctx context.Context,
	p *model.GetUsersParams,
) (model.UsersResp, error) {
	loc := tz.MustLocationFromCtx(ctx)
	epoch := time.Now().In(loc) // TODO: stuff into model/pb definition

	itemCodes, err := s.queryProductItemCodes(ctx, &productItemCodesReq{
		ProductCode: p.ProductCode,
	})
	if err != nil {
		return nil, errors.Trace(err)
	}

	// query by months then merge
	startMonth := base.ThisMonth(p.Begin.In(loc))
	endMonth := base.ThisMonth(p.End.In(loc))
	if endMonth.Equal(p.End.In(loc)) {
		endMonth = endMonth.AddDate(0, 1, 0)
	}

	// we can avoid having to maintain a set if only one month's worth of data
	// is requested
	singleMonth := startMonth.AddDate(0, 1, 0).Equal(endMonth)
	if singleMonth {
		result, err := s.kirbyDP.ListItemUIDsByMonthAndZone(ctx, itemCodes.ItemCodes, startMonth, p.ZoneCode, epoch)
		if err != nil {
			return nil, errors.Trace(err)
		}

		return model.UsersResp(result), nil
	}

	// slow path
	uidsSet := make(map[uint64]struct{})

	var months []time.Time
	for month := startMonth; month.Before(endMonth); month = month.AddDate(0, 1, 0) {
		months = append(months, month)
	}

	// 3 目前是随便定的一个不太大的数，主要为了不要打爆数据面
	resultsByMonth, err := resultgroup.ThrottledParallelMap(months, 3, func(month time.Time) ([]uint64, error) {
		resp, err := s.kirbyDP.ListItemUIDsByMonthAndZone(ctx, itemCodes.ItemCodes, month, p.ZoneCode, epoch)
		if err != nil {
			return nil, errors.Trace(err)
		}

		return resp, nil
	})
	if err != nil {
		return nil, err
	}

	for _, monthResult := range resultsByMonth {
		for _, x := range monthResult {
			uidsSet[x] = struct{}{}
		}
	}

	uids := lo.MapToSlice(uidsSet, func(k uint64, _ struct{}) uint64 { return k })
	sort.Slice(uids, func(i int, j int) bool { return uids[i] < uids[j] })

	return model.UsersResp(uids), nil
}
