package action

import (
	"context"

	"qiniu.io/pay/priced/model"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"
	"qiniu.io/pay/priced/action/adapter"
)

// CreatePrice 创建计费项价格（公开报价，只支持单个 PriceItem）
func (a *PriceAction) CreatePrice(
	ctx context.Context,
	req *pb.PricesParam,
) (*pb.PriceItem, error) {
	param, err := adapter.BuildPricesParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	m, err := a.priceBizSrv.CreatePrice(ctx, param)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceItem(m)
}

// BatchCreatePrice 创建计费项价格（公开报价/成本价格）
func (a *PriceAction) BatchCreatePrice(
	ctx context.Context,
	req *pb.PricesParam,
) (*pb.PriceItemList, error) {
	param, err := adapter.BuildPricesParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	items, err := a.priceBizSrv.BatchCreatePrice(ctx, param)
	if err != nil {
		return nil, errors.Trace(err)
	}

	cnt := len(items)
	res := &pb.PriceItemList{
		PriceItems: make([]*pb.PriceItem, cnt),
		Count:      uint64(cnt),
	}

	for i, item := range items {
		pbItem, err := adapter.BuildPbPriceItem(item)
		if err != nil {
			return nil, errors.Trace(err)
		}

		res.PriceItems[i] = pbItem
	}

	return res, nil
}

// UpdatePrice 更新计费项价格
func (a *PriceAction) UpdatePrice(
	ctx context.Context,
	req *pb.PricesParam,
) (*pb.PriceItem, error) {
	param, err := adapter.BuildPricesParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	m, err := a.priceBizSrv.UpdatePrice(ctx, param)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceItem(m)
}

// CreateUserPrice 为用户创建 VIP 价格
func (a *PriceAction) CreateUserPrice(
	ctx context.Context,
	req *pb.UserPriceParam,
) (*pb.PriceTable, error) {
	param, err := adapter.BuildUserPricesParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	m, err := a.priceBizSrv.CreateUserPrice(ctx, param)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceTable(m)
}

// UpdateUserPrice 更新用户 VIP 价格
func (a *PriceAction) UpdateUserPrice(
	ctx context.Context,
	req *pb.UserPriceParam,
) (*pb.PriceTable, error) {
	param, err := adapter.BuildUserPricesParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	m, err := a.priceBizSrv.UpdateUserPrice(ctx, param)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceTable(m)
}

// GetItemPriceWithTimePoint 按时间点查询给定 uid,itemID,zoneID 计费项价格信息
func (a *PriceAction) GetItemPriceWithTimePoint(
	ctx context.Context,
	req *pb.ItemPriceWithTimePointParam,
) (*pb.PriceItem, error) {
	param, err := adapter.BuildItemPriceWithTimePointParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	m, err := a.priceBizSrv.GetItemPriceWithTimePoint(ctx, param)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceItem(m)
}

// GetItemPriceWithTimeRange 按时间范围查询计费项价格信息
func (a *PriceAction) GetItemPriceWithTimeRange(
	ctx context.Context,
	req *pb.ItemPriceWithTimeRangeParam,
) (*pb.PriceItemList, error) {
	param, err := adapter.BuildItemPriceWithTimeRangeParam(req)
	if err != nil {
		return nil, errors.Trace(err).WithField("req", req)
	}

	ms, err := a.priceBizSrv.GetItemPriceWithTimeRange(ctx, param)
	if err != nil {
		return nil, errors.Trace(err).WithField("param", param)
	}

	res := &pb.PriceItemList{
		PriceItems: make([]*pb.PriceItem, len(ms)),
		Count:      uint64(len(ms)),
	}
	for i, m := range ms {
		pbM, err := adapter.BuildPbPriceItem(&m)
		if err != nil {
			return nil, errors.Trace(err).WithField("m", m).WithField("i", i)
		}

		res.PriceItems[i] = pbM
	}
	return res, nil
}

// BatchGetItemPricesWithTimeRange 按时间范围批量查询计费项价格信息
func (a *PriceAction) BatchGetItemPricesWithTimeRange(
	ctx context.Context,
	req *pb.BatchItemPricesWithTimeRangeParam,
) (*pb.PriceItemList, error) {
	param, err := adapter.BuildBatchItemPricesWithTimeRangeParam(req)
	if err != nil {
		return nil, errors.Trace(err).WithField("req", req)
	}

	ms, err := a.priceBizSrv.BatchGetItemPricesWithTimeRange(ctx, param)
	if err != nil {
		return nil, errors.Trace(err).WithField("param", param)
	}

	res := &pb.PriceItemList{
		PriceItems: make([]*pb.PriceItem, len(ms)),
		Count:      uint64(len(ms)),
	}
	for i, m := range ms {
		pbM, err := adapter.BuildPbPriceItem(&m)
		if err != nil {
			return nil, errors.Trace(err).WithField("m", m).WithField("i", i)
		}

		res.PriceItems[i] = pbM
	}
	return res, nil
}

// GetItemPriceWithPriceTableID 按价格表 ID 查询计费项价格信息
func (a *PriceAction) GetItemPriceWithPriceTableID(
	ctx context.Context,
	req *pb.ItemPriceWithPriceTableIDParam,
) (*pb.PriceItemList, error) {
	param, err := adapter.BuildItemPriceWithPriceTableIDParam(req)
	if err != nil {
		return nil, errors.Trace(err).WithField("req", req)
	}

	ms, err := a.priceBizSrv.GetItemPriceByPriceTableID(ctx, param)
	if err != nil {
		return nil, errors.Trace(err).WithField("param", param)
	}
	res := &pb.PriceItemList{
		PriceItems: make([]*pb.PriceItem, len(ms)),
		Count:      uint64(len(ms)),
	}
	for i, m := range ms {
		pbM, err := adapter.BuildPbPriceItem(&m)
		if err != nil {
			return nil, errors.Trace(err).WithField("m", m).WithField("i", i)
		}

		res.PriceItems[i] = pbM
	}
	return res, nil
}

// GetUserPriceWithTimePoint 按时间点查询用户价格信息
func (a *PriceAction) GetUserPriceWithTimePoint(
	ctx context.Context,
	req *pb.UserPriceWithTimePointParam,
) (*pb.UserPriceWithTimePointResp, error) {
	param, err := adapter.BuildUserPriceWithTimePointParam(req)
	if err != nil {
		return nil, errors.Trace(err).WithField("req", req)
	}

	m, err := a.priceBizSrv.GetUserPriceWithTimePoint(ctx, param)
	if err != nil {
		return nil, errors.Trace(err).WithField("param", param)
	}
	return adapter.BuildPbUserPriceWithTimePointResp(m)
}

// GetUserPriceWithTimeRange 按时间范围查询用户价格信息
func (a *PriceAction) GetUserPriceWithTimeRange(
	ctx context.Context,
	req *pb.UserPriceWithTimeRangeParam,
) (*pb.UserPriceWithTimeRangeResp, error) {
	param, err := adapter.BuildUserPriceWithTimeRangeParam(req)
	if err != nil {
		return nil, errors.Trace(err).WithField("req", req)
	}

	m, err := a.priceBizSrv.GetUserPriceWithTimeRange(ctx, param)
	if err != nil {
		return nil, errors.Trace(err).WithField("param", param)
	}
	return adapter.BuildPbUserPriceWithTimeRangeResp(m)
}

// UpdateUserFinalPrice 更新用户最终信息表 (default + vips)
func (a *PriceAction) UpdateUserFinalPrice(
	ctx context.Context,
	req *pb.UIDParam,
) (*empty.Empty, error) {
	err := a.priceBizSrv.UpdateUserFinalPrice(ctx, req.GetUid())
	if err != nil {
		return nil, errors.Trace(err).WithField("uid", req.GetUid())
	}

	return &empty.Empty{}, nil
}

// GetDefaultPriceTable 查询公开报价 PriceTable
func (a *PriceAction) GetDefaultPriceTable(
	ctx context.Context,
	param *empty.Empty,
) (*pb.PriceTable, error) {
	priceTable, err := a.priceBizSrv.GetDefaultPriceTable(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceTable(priceTable)
}

// GetCostPriceTable 查询成本报价 PriceTable
func (a *PriceAction) GetCostPriceTable(
	ctx context.Context,
	param *empty.Empty,
) (*pb.PriceTable, error) {
	priceTable, err := a.priceBizSrv.QueryCostPriceTable(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceTable(priceTable)
}

// GetRefPriceTable 查询参考成本报价 PriceTable
func (a *PriceAction) GetRefCostPriceTable(
	ctx context.Context,
	param *empty.Empty,
) (*pb.PriceTable, error) {
	priceTable, err := a.priceBizSrv.QueryRefCostPriceTable(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceTable(priceTable)
}

// ChangePriceTableLifecycle 变更报价单生效时间
func (a *PriceAction) ChangePriceTableLifecycle(
	ctx context.Context,
	req *pb.PriceTableLifecycleChangeParam,
) (*pb.PriceTable, error) {

	param, err := adapter.BuildPriceTableLifecycleChangeParam(req)
	if err != nil {
		return nil, errors.Trace(err).WithField("req", req)
	}

	m, err := a.priceBizSrv.ChangePriceTableLifecycle(ctx, param)
	if err != nil {
		return nil, errors.Trace(err).WithField("param", param)
	}
	return adapter.BuildPbPriceTable(m)
}

// GetPriceInfluence 查询某张报价单对价格的影响，返回报价单生效前后的价格详情
func (a *PriceAction) GetPriceInfluence(
	ctx context.Context,
	param *pb.UIDPriceIDParam,
) (*pb.PriceInfluence, error) {
	m, err := a.priceBizSrv.GetPriceInfluence(ctx, param.GetUid(), param.GetPriceId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceInfluence(m)
}

// GetPriceInfluenceWithCurrentPrice 查询报价单提交前的价格与当前报价单的差异
func (a *PriceAction) GetPriceInfluenceWithCurrentPrice(
	ctx context.Context,
	param *pb.UIDPriceIDParam,
) (*pb.PriceInfluence, error) {
	m, err := a.priceBizSrv.GetPriceInfluenceWithCurrentPrice(
		ctx, param.GetUid(), param.GetPriceId(), nil,
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceInfluence(m)
}

// UpdateItemPublicPrice 更新计费项公开价格
func (a *PriceAction) UpdateItemPublicPrice(
	ctx context.Context,
	req *pb.UpdateItemPriceParam,
) (*pb.PriceItem, error) {
	param, err := adapter.BuildUpdateItemPriceParam(req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	m, err := a.priceBizSrv.UpdateItemPublicPrice(ctx, param)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceItem(m)
}

// BatchGetItemPublicPriceItems 批量查询计费项公开价记录 PriceItem，不包含阶梯价格
func (a *PriceAction) BatchGetItemPublicPriceItems(
	ctx context.Context,
	param *pb.QueryPriceWithTimeRangeParam,
) (*pb.PriceItemList, error) {
	p, err := adapter.BuildQueryPriceWithTimeRangeParam(param)
	if err != nil {
		return nil, err
	}

	priceItems, err := a.priceBizSrv.BatchQueryPriceItemsByType(
		ctx,
		model.PriceTableTypeDefault,
		p.ItemIDs, p.ZoneID,
		p.StartTime, p.EndTime,
		p.CurrencyType,
	)
	if err != nil {
		return nil, err
	}

	list := &pb.PriceItemList{
		Count:      uint64(len(priceItems)),
		PriceItems: make([]*pb.PriceItem, len(priceItems)),
	}

	for i, priceItemModel := range priceItems {
		priceItem, err := adapter.BuildPbPriceItem(&priceItemModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceItems[i] = priceItem
	}

	return list, nil
}

// BatchGetItemPublicPrices 批量查询计费项公开价，包含阶梯价格
func (a *PriceAction) BatchGetItemPublicPrices(
	ctx context.Context,
	param *pb.QueryPriceWithTimeRangeParam,
) (*pb.ItemPricesListResp, error) {
	p, err := adapter.BuildQueryPriceWithTimeRangeParam(param)
	if err != nil {
		return nil, errors.Trace(err)
	}

	priceItems, err := a.priceBizSrv.BatchQueryPriceItemsWithStairs(
		ctx,
		model.PriceTableTypeDefault,
		p.ItemIDs, p.ZoneID,
		p.StartTime, p.EndTime,
		p.CurrencyType,
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildItemPricesListResp(priceItems)
}

// BatchGetItemCostPrices 批量查询计费项成本价，包含阶梯价格
func (a *PriceAction) BatchGetItemCostPrices(
	ctx context.Context,
	param *pb.QueryPriceWithTimeRangeParam,
) (*pb.ItemPricesListResp, error) {
	p, err := adapter.BuildQueryPriceWithTimeRangeParam(param)
	if err != nil {
		return nil, errors.Trace(err)
	}

	priceItems, err := a.priceBizSrv.BatchQueryPriceItemsWithStairs(
		ctx,
		model.PriceTableTypeCost,
		p.ItemIDs, p.ZoneID,
		p.StartTime, p.EndTime,
		p.CurrencyType,
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildItemPricesListResp(priceItems)
}
