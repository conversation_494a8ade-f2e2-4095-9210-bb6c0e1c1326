package action

import (
	"context"
	"errors"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/qpay/payment/model"
	"qiniu.io/pay/walletd/action/adapter"
)

// DepositCash deposit cash
func (a *PaymentAction) DepositCash(
	ctx context.Context,
	request *pb.DepositRequest,
) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : DepositCash")

	req, err := adapter.BuildDepositRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildDepositRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.DepositCash GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	paymentTransaction, err := a.depositService.DepositCash(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("deposit cash failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*paymentTransaction)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}

	return pbTransaction, nil
}

// DepositNiuCoin deposit niu coin
func (a *PaymentAction) DepositNiuCoin(ctx context.Context, request *pb.DepositRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : DepositNiuCoin")

	req, err := adapter.BuildDepositRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildDepositRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.DepositNiuCoin GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	paymentTransaction, err := a.depositService.DepositNiuCoin(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("deposit niu coin failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*paymentTransaction)
	if err != nil {

		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build pb payment transaction failed")

		return nil, err
	}

	return pbTransaction, nil
}

// WithdrawCash withdraw cash
func (a *PaymentAction) WithdrawCash(ctx context.Context, request *pb.WithdrawRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : WithdrawCash")

	req, err := adapter.BuildWithdrawRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildDepositRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.WithdrawCash GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	paymentTransaction, err := a.depositService.WithdrawCash(ctx, req)

	if err != nil {
		logger.Error(err)
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("withdraw cash failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*paymentTransaction)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}

	return pbTransaction, nil
}

// 提现牛币
func (a *PaymentAction) WithdrawNb(ctx context.Context, request *pb.WithdrawRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : WithdrawNb")

	req, err := adapter.BuildWithdrawRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildDepositRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.WithdrawNb GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	paymentTransaction, err := a.depositService.WithdrawNb(ctx, req)

	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("withdraw nb failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*paymentTransaction)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}

	return pbTransaction, nil
}

// 优先提牛币，金额不足的继续提现金
func (a *PaymentAction) WithdrawNbAndCash(ctx context.Context, request *pb.WithdrawRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : WithdrawNbAndCash")

	req, err := adapter.BuildWithdrawRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildDepositRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.WithdrawNbAndCash GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	paymentTransaction, err := a.depositService.WithdrawNbAndCash(ctx, req)

	if err != nil {
		logger.Error(err)
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("withdraw nb and cash failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*paymentTransaction)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}

	return pbTransaction, nil
}

// DepositByType deposit by type
func (a *PaymentAction) DepositByType(ctx context.Context, request *pb.DepositRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : DepositByType")

	req, err := adapter.BuildDepositRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildDepositRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.DepositByType GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	paymentTransaction, err := a.depositService.DepositByType(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("deposit by type failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*paymentTransaction)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build pb payment transaction failed")

		return nil, err
	}

	return pbTransaction, nil
}

func (a *PaymentAction) DepositForTransaction(ctx context.Context, request *pb.DepositRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : DepositForTransactionWithLock")

	req, err := adapter.BuildDepositRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildDepositRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.DepositForTransaction GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	paymentTransaction, err := a.depositService.DepositForTransactionWithLock(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("deposit for transaction failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*paymentTransaction)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build pb payment transaction failed")

		return nil, err
	}

	return pbTransaction, nil
}

func (a *PaymentAction) DepositForSpecifiedPayment(ctx context.Context, request *pb.DepositRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : DepositForSpecifiedPayment")

	req, err := adapter.BuildDepositRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildDepositRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.DepositForSpecifiedPayment GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType
	paymentTransaction, err := a.depositService.DepositForSpecifiedPayment(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("deposit for transaction failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*paymentTransaction)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build pb payment transaction failed")

		return nil, err
	}

	return pbTransaction, nil
}

func (a *PaymentAction) TransferAccount(ctx context.Context, request *pb.DepositRequest) (*pb.PaymentTransactionList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : TransferAccount")

	req, err := adapter.BuildDepositRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildDepositRequest failed")
		return nil, err
	}
	txnList := make([]model.PaymentTransaction, 0)
	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid": req.UID,
		}).WithError(err).Error("PaymentAction.TransferAccount GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	fromTrans, toTrans, err := a.depositService.TransferAccount(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("transfer account failed")
		return nil, err
	}
	txnList = append(txnList, fromTrans...)
	txnList = append(txnList, toTrans...)

	txns, err := adapter.BuildPbPaymentTransactions(txnList)
	if err != nil {
		logger.WithError(err).Error("BuildPbPaymentTransactions failed")
		return nil, err
	}

	return &pb.PaymentTransactionList{Txns: txns}, nil
}

func (a *PaymentAction) CheckRecharge(ctx context.Context, checkRequest *pb.CheckRequest) (*empty.Empty, error) {
	// 经过仔细检查，这个通过自动完成的检查或者补偿做不了
	// 可能会发生不可控的错误，所以暂时忽略这个方法
	// 还是通过外部任务和重试来优化
	return nil, errors.New("no impl CheckRecharge")
}

// GetAvailableWithdrawCash 获取用户可提现金额
func (a *PaymentAction) GetAvailableWithdrawCash(ctx context.Context, pbUIDParam *pb.UIDParam) (*pb.UserAvailableWithdrawCash, error) {
	logger := logging.GetLogger(ctx)
	availableWithdrawCashInfo, err := a.depositService.GetAvailableWithdrawCash(ctx, pbUIDParam.Uid)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid": pbUIDParam.GetUid(),
		}).WithError(err).Error("GetAvailableWithdrawCash failed")
		return nil, err
	}

	return &pb.UserAvailableWithdrawCash{
		Uid:                    pbUIDParam.GetUid(),
		TransferWithdrawCash:   availableWithdrawCashInfo.TransferWithdrawCash.ToInt64(),
		AvailableWithdrawCash:  availableWithdrawCashInfo.AvailableWithdrawCash.ToInt64(),
		CashBalance:            availableWithdrawCashInfo.CashBalance.ToInt64(),
		InvoiceAmount:          availableWithdrawCashInfo.InvoiceAmount.ToInt64(),
		Arrears:                availableWithdrawCashInfo.Arrears.ToInt64(),
		ProcessingWithdrawCash: availableWithdrawCashInfo.ProcessingWithdrawCash.ToInt64(),
	}, nil
}
