package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
)

// BuildPbPackage converts from model.Package to the protobuf type.
func BuildPbPackage(x *model.Package) (*pb.Package, error) {
	return &pb.Package{
		Id:          x.ID,
		Code:        x.Code,
		Type:        x.Type,
		Name:        x.Name,
		Description: x.Description,
		Remark:      x.Remark,
		Price:       x.Price,
		CreatedAt:   timestamppb.New(x.CreatedAt),
		UpdatedAt:   timestamppb.New(x.UpdatedAt),
	}, nil
}

// BuildModelPackage converts from pb.Package to the model layer type.
func BuildModelPackage(x *pb.Package) (*model.Package, error) {
	var createdAt, updatedAt time.Time
	if x.GetCreatedAt() != nil {
		err := x.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = x.GetCreatedAt().AsTime()
	}

	if x.GetUpdatedAt() != nil {
		err := x.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = x.GetUpdatedAt().AsTime()
	}

	return &model.Package{
		ID:          x.GetId(),
		Code:        x.GetCode(),
		Type:        x.GetType(),
		Name:        x.GetName(),
		Description: x.GetDescription(),
		Remark:      x.GetRemark(),
		Price:       x.GetPrice(),
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
	}, nil
}

// BuildPbPackageList converts []model.Package to the pbPackageList
func BuildPbPackageList(
	objs []model.Package,
	count uint64,
) (*pb.PackageList, error) {
	packages := make([]*pb.Package, len(objs))
	for i := range objs {
		result, err1 := BuildPbPackage(&objs[i])
		if err1 != nil {
			return nil, err1
		}
		packages[i] = result
	}
	return &pb.PackageList{
		Packages: packages,
		Count:    count,
	}, nil
}
