package mysqlStore

import (
	"context"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/intl/tz"
	pb "github.com/qbox/bo-sdk/kirbypb"
	"github.com/qbox/pay-sdk/middleware/logging"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/dataplaneModel"
	"qiniu.io/pay/kirby/service/dataplane/common"
)

type MySQLDataStore struct {
	dao   *dataplaneModel.KirbyDataplaneDao
	aggrp adminModel.IAggRProvider
	debug bool

	mu           sync.Mutex
	pendingState []*dataplaneModel.FlattenedSeries

	flushTrigger *time.Timer
}

var _ common.IDataStore = (*MySQLDataStore)(nil)

const durationBeforeFlush = 30 * time.Second

func NewMySQLDataStore(
	dao *dataplaneModel.KirbyDataplaneDao,
	aggrp adminModel.IAggRProvider,
	debug bool,
) *MySQLDataStore {
	return &MySQLDataStore{
		dao:   dao,
		aggrp: aggrp,
		debug: debug,
	}
}

func (x *MySQLDataStore) kickTrigger() {
	x.flushTrigger.Stop()
	x.flushTrigger.Reset(durationBeforeFlush)
}

func (x *MySQLDataStore) FeedSeries(
	ctx context.Context,
	s *dataplaneModel.FlattenedSeries,
) {
	x.mu.Lock()
	defer x.mu.Unlock()
	x.pendingState = append(x.pendingState, s)
	x.kickTrigger()
}

func getLogger(ctx context.Context) *logrus.Entry {
	return logging.GetLogger(ctx).WithField("system", "mysqlDataStore")
}

func (x *MySQLDataStore) PersistAllPendingSeries(ctx context.Context) (int, error) {
	x.mu.Lock()
	batch := x.pendingState
	x.pendingState = nil
	x.mu.Unlock()

	l := getLogger(ctx)

	buf := x.dao.NewSeriesIngestionBuffer(tz.MustLocationFromCtx(ctx))

	t1 := time.Now()
	err := buf.Add(batch)
	dur := time.Since(t1)
	if err != nil {
		l.WithFields(logrus.Fields{
			"duration": dur,
		}).WithError(err).Error("failed to prepare series ingestion buffer")
		return 0, err
	}

	l.WithFields(logrus.Fields{
		"duration": dur,
		"num":      len(batch),
	}).Info("prepared series ingestion buffer")

	t1 = time.Now()
	err = buf.Commit(ctx)
	dur = time.Since(t1)
	if err != nil {
		l.WithField("duration", dur).WithError(err).Error("failed to commit series")
		return 0, err
	}
	l.WithField("duration", dur).Info("committed series")

	return len(batch), nil
}

func (x *MySQLDataStore) RunAggregations(
	ctx context.Context,
	month time.Time,
	since time.Time,
	epoch time.Time,
) error {
	loc := tz.MustLocationFromCtx(ctx)
	month = month.In(loc)
	since = since.In(loc)
	epoch = epoch.In(loc)
	l := getLogger(ctx)

	t1 := time.Now()
	relatedSeriesToAggregate, err := x.dao.ListRelatedSeriesSince(epoch, month, since, loc)
	dur := time.Since(t1)
	if err != nil {
		l.WithFields(logrus.Fields{
			"duration": dur,
			"month":    month,
			"since":    since,
			"epoch":    epoch,
		}).WithError(err).Error("failed to list related series for aggregation")
		return err
	}
	l.WithFields(logrus.Fields{
		"duration": dur,
		"month":    month,
		"since":    since,
		"epoch":    epoch,
		"len":      len(relatedSeriesToAggregate),
	}).Info("got related series for aggregation")

	aggCtx := newAggregationContext(x.aggrp, x.debug)

	t1 = time.Now()
	aggSeries, err := aggCtx.runOn(ctx, epoch, relatedSeriesToAggregate)
	dur = time.Since(t1)
	if err != nil {
		l.WithField("duration", dur).WithError(err).Error("failed to run aggregations")
		return err
	}
	l.WithFields(logrus.Fields{
		"duration":     dur,
		"numAggSeries": len(aggSeries),
	}).Info("ran aggregations")

	buf := x.dao.NewAggSeriesIngestionBuffer(tz.MustLocationFromCtx(ctx))

	t1 = time.Now()
	err = buf.Add(aggSeries)
	dur = time.Since(t1)
	if err != nil {
		l.WithField("duration", dur).WithError(err).Error("failed to prepare the aggSeries ingestion buffer")
		return err
	}
	l.WithField("duration", dur).Info("prepared the aggSeries ingestion buffer")

	t1 = time.Now()
	err = buf.Commit(ctx)
	dur = time.Since(t1)
	if err != nil {
		l.WithField("duration", dur).WithError(err).Error("failed to commit aggSeries")
		return err
	}
	l.WithField("duration", dur).Info("committed aggSeries")

	return nil
}

func (x *MySQLDataStore) ProvisionShards(ctx context.Context, month time.Time) error {
	loc := tz.MustLocationFromCtx(ctx)
	return x.dao.ProvisionShardsForMonth(month, loc)
}

func (x *MySQLDataStore) NotifyEngineStart() {
	x.flushTrigger = time.NewTimer(durationBeforeFlush)
	// don't schedule aggregation immediately (kickTrigger is responsible for that)
	x.flushTrigger.Stop()
}

func (x *MySQLDataStore) NotifyEngineStop() {
	x.flushTrigger.Stop()
}

func (x *MySQLDataStore) EventChan() <-chan time.Time {
	return x.flushTrigger.C
}

func (x *MySQLDataStore) ListSeries(
	ctx context.Context,
	req *dataplaneModel.ReqListSeries,
) (*dataplaneModel.SeriesDetailList, error) {
	return x.dao.ListSeriesByConds(ctx, req)
}

func (x *MySQLDataStore) ListSeriesDistinctUIDs(
	ctx context.Context,
	req *dataplaneModel.ReqListSeries,
) ([]uint64, error) {
	return x.dao.ListSeriesDistinctUIDsByConds(ctx, req)

}

func (x *MySQLDataStore) ListAggregatedSeries(
	ctx context.Context,
	req *dataplaneModel.ReqListAggregatedSeries,
) (*dataplaneModel.AggregatedSeriesDetailList, error) {
	return x.dao.ListAggregatedSeries(ctx, req)
}

func (x *MySQLDataStore) ListAggregatedSeriesDistinctUIDs(
	ctx context.Context,
	req *dataplaneModel.ReqListAggregatedSeries,
) ([]uint64, error) {
	return x.dao.ListAggregatedSeriesDistinctUIDsByConds(ctx, req)
}

func (x *MySQLDataStore) QueryAggregatedSeriesData(
	ctx context.Context,
	uid uint64,
	month time.Time,
	itemCode string,
	zoneCode int64,
	desiredG adminModel.Granularity,
	epoch time.Time,
) ([]*pb.StatPoint, error) {
	loc := tz.MustLocationFromCtx(ctx)
	month = month.In(loc)
	md, err := x.dao.AggSeriesMD.GetShard(month, loc).GetByMonthUIDItemZone(
		epoch,
		month,
		loc,
		uid,
		itemCode,
		zoneCode,
	)
	if err != nil {
		return nil, err
	}

	data, err := x.dao.DataMgr.QueryAggregatedSeriesData(month, loc, md, true)
	if err != nil {
		return nil, err
	}

	return common.TransformGranularity(md.G, desiredG, data, loc)
}

func (x *MySQLDataStore) ListItemUIDsByMonthAndZone(
	ctx context.Context,
	itemCodes []string,
	month time.Time,
	zoneCode int64,
	epoch time.Time,
) ([]uint64, error) {
	return x.dao.ListDistinctUIDsByConds(ctx, month, itemCodes, true, zoneCode, epoch)
}

func (x *MySQLDataStore) ListUserItemsByMonthAndZone(
	ctx context.Context,
	uid uint64,
	month time.Time,
	zoneCode int64,
	epoch time.Time,
) ([]string, error) {
	return x.dao.ListDistinctItemCodesByConds(ctx, month, []uint64{uid}, true, zoneCode, epoch)
}
