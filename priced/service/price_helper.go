package service

import (
	"context"

	"github.com/qbox/bo-base/v4/base"
	"qiniu.io/pay/priced/model"
)

// FillGapWithPriceItems 在指定时间段内填充合适的 PriceItems
func FillGapWithPriceItems(
	gap base.TimeRange,
	priceItems []model.PriceItem,
) []model.PriceItem {
	filledPriceItems := make([]model.PriceItem, 0)

	from := base.NewHNS(gap.Start)
	to := base.NewHNS(gap.End)
	for _, priceItem := range priceItems {
		// 不相交的情况
		//
		// from         to
		// |------------o
		//              |-|-------------o
		//             effect          dead
		//
		//               from       to
		//                |---------o
		// |------------o-o
		// effect       dead
		//
		// to <= effect || dead <= from
		// !(effect > to) || !(from > dead)
		if to <= priceItem.EffectTime || priceItem.DeadTime <= from {
			continue
		}

		if priceItem.EffectTime < from {
			priceItem.EffectTime = from
		}

		if priceItem.DeadTime > to {
			priceItem.DeadTime = to
		}

		filledPriceItems = append(filledPriceItems, priceItem)
	}

	return filledPriceItems
}

// IsSameAlgorithm 判断两个 PriceItem 是否是相同的取点算法
func IsSameAlgorithm(lhs, rhs model.PriceItem) bool {
	return lhs.AlgorithmID == rhs.AlgorithmID
}

// IsSameStairPriceType 判断两个 PriceItem 是否是相同类型的 StairPriceType
func IsSameStairPriceType(lhs, rhs model.PriceItem) bool {
	return lhs.StairPriceType == rhs.StairPriceType
}

func buildPriceItemByItemPriceParam(
	priceTableID uint64,
	fallbackCurrencyType base.CurrencyType,
	itemPrice *ItemPriceParam,
) *model.PriceItem {
	ct := itemPrice.CurrencyType
	if ct == "" && fallbackCurrencyType != "" {
		ct = fallbackCurrencyType
	}

	return &model.PriceItem{
		PriceID:            priceTableID,
		ItemID:             itemPrice.ItemID,
		ZoneID:             itemPrice.ZoneID,
		AlgorithmID:        itemPrice.AlgorithmID,
		StairPriceType:     itemPrice.StairPriceType,
		IsDisabled:         itemPrice.IsDisabled,
		IsDefaultAlgorithm: itemPrice.IsDefaultAlgorithm,
		UnitRate:           itemPrice.UnitRate,
		Type:               itemPrice.Type,
		EffectTime:         itemPrice.EffectTime,
		DeadTime:           itemPrice.DeadTime,
		CumulativeCycle:    itemPrice.CumulativeCycle,
		BillPeriodType:     itemPrice.BillPeriodType,
		CurrencyType:       ct,
	}
}

func buildPriceItemStairsByItemPriceParam(priceItemID uint64, itemPrice *ItemPriceParam) []model.PriceItemStair {
	stairs := make([]model.PriceItemStair, len(itemPrice.StairPrices))

	for i, stairPrice := range itemPrice.StairPrices {
		stairs[i] = model.PriceItemStair{
			PriceItemID: priceItemID,
			UnitID:      stairPrice.UnitID,
			Quantity:    stairPrice.Quantity,
			Price:       stairPrice.Price,
			Order:       uint64(i),
		}
	}

	return stairs
}

func buildPriceItemWithStairs(
	ctx context.Context,
	priceTableID uint64,
	currencyType base.CurrencyType,
	itemPrices []ItemPriceParam,
) []model.PriceItem {
	priceItemWithStairs := make([]model.PriceItem, len(itemPrices))

	for i, itemPrice := range itemPrices {
		priceItem := buildPriceItemByItemPriceParam(priceTableID, currencyType, &itemPrice)
		stairs := buildPriceItemStairsByItemPriceParam(priceItem.ID, &itemPrice)

		priceItem.Stairs = stairs
		priceItemWithStairs[i] = *priceItem
	}

	return priceItemWithStairs
}
