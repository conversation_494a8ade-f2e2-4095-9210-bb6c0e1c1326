package action

import (
	"net/http"
	"testing"

	"github.com/go-redis/redis/v8"
	"github.com/golang/mock/gomock"
	"github.com/jinzhu/gorm"
	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/test"
	"github.com/qbox/pay-sdk/base/account"
	gaeaClient "github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/gaea/client/operations"
	"github.com/qbox/pay-sdk/gaea/models"
	account_mock "github.com/qbox/pay-sdk/mocks/base/account"
	gaea_mock "github.com/qbox/pay-sdk/mocks/gaea"

	"qiniu.io/pay/qpay/payment"
	"qiniu.io/pay/qpay/payment/dao"
	"qiniu.io/pay/qpay/payment/model"
	"qiniu.io/pay/qpay/payment/service"
	"qiniu.io/pay/qpay/payment/service/handler"
	currencyDao "qiniu.io/pay/qpay/wallet/dao"
	wm "qiniu.io/pay/qpay/wallet/model"
	"qiniu.io/pay/qpay/wrapper"
)

type TestSandbox struct {
	TestWrap      *test.Wrap
	UID           uint64
	Factory       *payment.Factory
	PaymentAction *PaymentAction
}

func getMockAccountService(t *testing.T) *account_mock.MockAccountService {
	ctrl := gomock.NewController(t)

	accountService := account_mock.NewMockAccountService(ctrl)
	accountService.
		EXPECT().
		GetAccInfo(gomock.Any()).
		Return(&account.AccInfo{
			ParentUid: 0,
		}, nil).AnyTimes()
	return accountService
}

func getMockGaeaService(t *testing.T) operations.ClientService {
	ctrl := gomock.NewController(t)
	mockGaeaService := gaea_mock.NewMockClientService(ctrl)
	mockGaeaService.
		EXPECT().
		RelationGet(gomock.Any(), gomock.Any()).
		Return(&operations.RelationGetOK{
			Payload: &models.RelationGetResp{
				Code: 200,
				Data: &models.FinancialRelationModel{
					CreatorID: "",
					ID:        "",
					IsDelete:  false,
					Memo:      "",
					ParentUID: 123,
					Type:      1,
					UID:       0,
				},
				Message: "",
			}}, nil,
		).AnyTimes()
	mockGaeaService.EXPECT().
		WithdrawUserApplyingList(gomock.Any(), gomock.Any()).
		Return(&operations.WithdrawUserApplyingListOK{
			Payload: &models.WithdrawUserApplyingListResp{
				Code: http.StatusOK,
				Data: &models.WithdrawUserApplyingListModel{
					List: []*models.WithdrawUserApplyingModel{
						{
							ApplyMoney:       110000,
							DepositPaymentID: "10086cba",
							Money:            114514,
							Status:           "init",
						},
						{
							ApplyMoney:       1145140,
							DepositPaymentID: "95588abc",
							Money:            1919810,
							Status:           "init",
						},
					},
				},
			},
		}, nil).
		AnyTimes()
	return mockGaeaService
}

func BuildSandbox(t *testing.T) *TestSandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in qpay wallet return error")
	}
	// testWrap.DB().LogMode(true)

	redisClient := redis.NewUniversalClient(&redis.UniversalOptions{
		Addrs: []string{testWrap.Miniredis().Addr()},
	})
	factory := payment.NewPaymentFactory(
		payment.WithBaseDao(testWrap.BaseDao()),
		payment.WithRedisClient(redisClient),
		payment.WithAccClient(getMockAccountService(t)),
	)

	paymentTxnDao := dao.NewPaymentTransactionDAO(factory.BaseDao)
	walletRequestDAO := dao.NewWalletRequestDAO(factory.BaseDao)
	currencyDAO := currencyDao.NewCurrencyDAO(factory.BaseDao)
	userPaymentSeqDAO := dao.NewUserPaymentSeqDAO(factory.BaseDao)
	base := service.BasePaymentImpl{
		WalletService:         factory.GetWalletFactory().WalletService,
		VoucherService:        factory.GetWalletFactory().VoucherService,
		CurrencyDao:           currencyDAO,
		PaymentTransactionDAO: paymentTxnDao,
		WalletRequestDAO:      walletRequestDAO,
		UserPaymentSeqDAO:     userPaymentSeqDAO,
		RedisClient:           redisClient,
		GaeaService:           &gaeaClient.Gaea{Operations: getMockGaeaService(t)},
	}
	factory.VoucherScopeWrapper = wrapper.VoucherScopeWrapperMock{}
	factory.VoucherService = &service.VoucherServiceImpl{
		BasePaymentImpl: base,
		VoucherScope:    factory.VoucherScopeWrapper,
	}

	factory.QueryService = &service.QueryServiceImpl{
		BasePaymentImpl: base,
		VoucherScope:    factory.VoucherScopeWrapper,
	}

	factory.UserBalanceSnapService = &service.UserBalanceSnapServiceImpl{
		BasePaymentImpl:    base,
		QueryService:       factory.QueryService,
		UserBalanceSnapDao: dao.NewUserBalanceSnapDAO(factory.BaseDao),
	}

	factory.UserPaymentSeqService = &service.UserPaymentSeqServiceImpl{
		BasePaymentImpl:          base,
		UserPaymentSeqHistoryDAO: dao.NewUserPaymentSeqHistoryDAO(factory.BaseDao),
	}

	factory.DepositService = &service.DepositServiceImpl{
		BasePaymentImpl:        base,
		UserBalanceSnapService: factory.UserBalanceSnapService,
	}

	billBaseHandlerImpl := handler.BaseHandlerImpl{
		CurrencyDAO:           currencyDAO,
		PaymentTransactionDAO: paymentTxnDao,
		WalletRequestDAO:      walletRequestDAO,
		WalletService:         factory.GetWalletFactory().WalletService,
		VoucherService:        factory.GetWalletFactory().VoucherService,
	}

	CheckScope := handler.CheckVoucherScopeWrapper{
		VoucherScope: factory.VoucherScopeWrapper,
	}

	deductPaymentServiceImpl := service.DeductPaymentServiceImpl{
		BasePaymentImpl: service.BasePaymentImpl{
			PaymentTransactionDAO: dao.NewPaymentTransactionDAO(factory.BaseDao),
			WalletRequestDAO:      dao.NewWalletRequestDAO(factory.BaseDao),
			UserPaymentSeqDAO:     dao.NewUserPaymentSeqDAO(factory.BaseDao),
			WalletService:         factory.GetWalletFactory().WalletService,
			VoucherService:        factory.GetWalletFactory().VoucherService,
			DeductVoucherBill:     handler.NewDeductHandlerVoucherBill(billBaseHandlerImpl, &CheckScope),
			DeductNiuCoin:         handler.NewDeductHandlerNiuCoinImpl(billBaseHandlerImpl),
			CurrencyDao:           currencyDAO,

			DeductCash:     handler.NewDeductHandlerCashPostImpl(billBaseHandlerImpl), // Bill 都是 postCash
			DeductPostCash: handler.NewDeductHandlerCashPostImpl(billBaseHandlerImpl),

			ReverseVoucher:    handler.NewReverseHandlerVoucherImpl(billBaseHandlerImpl),
			ReverseNiuCoin:    handler.NewReverseHandlerNiuCoinImpl(billBaseHandlerImpl),
			ReverseCash:       handler.NewReverseHandlerCashImpl(billBaseHandlerImpl),
			RefundVoucherBill: handler.NewRefundHandlerVoucherImpl(billBaseHandlerImpl),
			RefundNiuCoinBill: handler.NewRefundHandlerNiuCoinImpl(billBaseHandlerImpl),
			RefundCashBill:    handler.NewRefundHandlerCashImpl(billBaseHandlerImpl),
			GaeaService:       &gaeaClient.Gaea{Operations: getMockGaeaService(t)},
			RedisClient:       factory.RedisClient,
		},
		UserBalanceSnapService: factory.UserBalanceSnapService,
	}
	factory.BillPaymentService = &service.BillPaymentServiceImpl{
		DeductPaymentServiceImpl: deductPaymentServiceImpl,
	}

	factory.PreDeductService = &service.PreDeductServiceImpl{
		DeductPaymentServiceImpl: deductPaymentServiceImpl,
	}

	factory.PostDeductService = &service.PostDeductServiceImpl{
		DeductPaymentServiceImpl: deductPaymentServiceImpl,
	}
	sandbox := TestSandbox{
		Factory:       factory,
		PaymentAction: NewPaymentAction(10, factory),
	}
	sandbox.TestWrap = testWrap
	sandbox.UID = 123456789

	return &sandbox
}

func RegisterMigrate(db *gorm.DB) {
	db.AutoMigrate(&wm.Wallet{})
	db.AutoMigrate(&wm.WalletSnap{})
	db.AutoMigrate(&wm.WalletTransaction{})
	db.AutoMigrate(&wm.WalletTransactionSnap{})
	db.AutoMigrate(&wm.Currency{})
	db.AutoMigrate(&wm.Voucher{})
	db.AutoMigrate(&wm.VoucherSnap{})
	db.AutoMigrate(&wm.WalletRequest{})
	db.AutoMigrate(&wm.WalletRequestSnap{})
	db.AutoMigrate(&model.PaymentTransaction{IsSnap: true})
	db.AutoMigrate(&model.PaymentTransaction{IsSnap: false})
	db.AutoMigrate(&model.PaymentTransactionHistory{})
	db.AutoMigrate(&wm.VoucherHistory{})
	db.AutoMigrate(&wm.WalletHistory{})
	db.AutoMigrate(&model.UserBalanceSnap{})
	db.AutoMigrate(&model.UserPaymentSeq{})
	db.AutoMigrate(&model.UserPaymentSeqHistory{})
}
