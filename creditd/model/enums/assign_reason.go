package enums

type AssignReason int64

const (
	AssignReasonUnknown AssignReason = 0
	// AssignReasonBindFirstCard 首次绑卡
	AssignReasonBindFirstCard AssignReason = 1
	// AssignReasonChangeDefaultCard 更新用户默认支付卡
	AssignReasonChangeDefaultCard AssignReason = 2
	// AssignReasonChargeCard 用户额度不足扣费
	AssignReasonChargeCard AssignReason = 3
	// AssignReasonGenMonthStatement 出外部账单
	AssignReasonGenMonthStatement AssignReason = 4
	// AssignReasonRevokeManualCredit 人工授信停用
	AssignReasonRevokeManualCredit AssignReason = 5
)

func (r AssignReason) Valid() bool {
	return r != AssignReasonUnknown
}
