package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/measured/model"
)

// GetPackageByID 根据 ID 查询单个资源包
func (s *MeasureBizService) GetPackageByID(
	ctx context.Context,
	id uint64,
) (*model.Package, error) {
	return s.dao.Package.GetByID(id, s.cacheExpires)
}

// GetPackageByCode 根据 Code 查询单个资源包
func (s *MeasureBizService) GetPackageByCode(
	ctx context.Context,
	code string,
) (*model.Package, error) {
	return s.dao.Package.GetByCode(code, s.cacheExpires)
}

// CreatePackage 创建一个资源包
func (s *MeasureBizService) CreatePackage(
	ctx context.Context,
	obj *model.Package,
) (*model.Package, error) {
	obj.ID = 0
	err := s.dao.Package.Save(obj, s.cacheExpires)
	return obj, err
}

// CountAllPackages 获取所有资源包总数
func (s *MeasureBizService) CountAllPackages(
	ctx context.Context,
) (uint64, error) {
	return s.dao.Package.CountAll(s.cacheExpires)
}

// ListPackagesByConds 按条件列举资源包
func (s *MeasureBizService) ListPackagesByConds(
	ctx context.Context,
	codePattern string,
	offset int,
	limit int,
) ([]model.Package, error) {
	return s.dao.Package.ListByConds(&model.ListPackageConds{
		CodePattern: codePattern,
		Offset:      offset,
		Limit:       limit,
	}, s.cacheExpires)
}

// UpdatePackageByID 根据 ID 更新一个资源包
func (s *MeasureBizService) UpdatePackageByID(
	ctx context.Context,
	id uint64,
	obj *model.Package,
) (*model.Package, error) {
	obj.ID = id
	err := s.dao.Package.Save(obj, s.cacheExpires)
	return obj, err
}

// DeletePackageByID 根据 ID 删除一个资源包
func (s *MeasureBizService) DeletePackageByID(
	ctx context.Context,
	id uint64,
) (*model.Package, error) {
	obj, err := s.dao.Package.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "query package failed").WithField("id", id)
	}
	err = s.dao.Package.DeleteByID(id)
	return obj, err
}

// ListFreeInUsePackages 查询正在使用中的 Package
// 用户有 free 类型的 package 即认为是在使用中，没有判断 effect、dead 时间字段
// 见：https://github.com/qbox/pay-sdk/pull/1210
func (s *MeasureBizService) ListFreeInUsePackages(
	ctx context.Context,
	uid uint64,
) ([]model.Package, error) {
	objs, err := s.dao.Package.ListByType(uid, "FREE")
	if err != nil {
		return nil, errors.Annotate(err, "query free inUse package failed").
			WithField("uid", uid)
	}
	return objs, nil
}
