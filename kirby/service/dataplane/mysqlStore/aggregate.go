package mysqlStore

import (
	"context"
	"errors"
	"fmt"
	"runtime"
	"sort"
	"time"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/dataplaneModel"
)

type aggregationContext struct {
	aggrp       adminModel.IAggRProvider
	extraChecks bool
}

func newAggregationContext(
	aggrp adminModel.IAggRProvider,
	extraChecks bool,
) *aggregationContext {
	return &aggregationContext{
		aggrp:       aggrp,
		extraChecks: extraChecks,
	}
}

func (c *aggregationContext) runOn(
	ctx context.Context,
	epoch time.Time,
	input []*dataplaneModel.SeriesDetail,
) ([]*dataplaneModel.AggregatedSeriesDetail, error) {
	loc := tz.MustLocationFromCtx(ctx)

	sort.Slice(input, func(i int, j int) bool {
		a, b := input[i], input[j]
		if !a.Ctime.Equal(b.Ctime) {
			return a.Ctime.Before(b.Ctime)
		}
		// this should be enough to guarantee deterministic order even
		// with unstable sort
		return a.SeriesKey < b.SeriesKey
	})

	seriesByAggRs := lo.GroupBy(input, func(x *dataplaneModel.SeriesDetail) string { return x.AggRCode })

	aggrs := map[string]*adminModel.AggregationRule{}
	for _, aggrCode := range lo.Keys(seriesByAggRs) {
		aggr, err := c.aggrp.GetAggregationRule(ctx, epoch, aggrCode)
		if err != nil {
			return nil, err
		}
		aggrs[aggrCode] = aggr
	}

	var result []*dataplaneModel.AggregatedSeriesDetail
	for aggrCode, series := range seriesByAggRs {
		aggr := aggrs[aggrCode]
		if aggr == nil {
			continue
		}

		aggrResult, err := runOneAggR(loc, aggr, series, c.extraChecks)
		if err != nil {
			return nil, err
		}

		result = append(result, aggrResult...)
	}

	return result, nil
}

func runOneAggR(
	loc *time.Location,
	aggr *adminModel.AggregationRule,
	data []*dataplaneModel.SeriesDetail,
	extraChecks bool,
) ([]*dataplaneModel.AggregatedSeriesDetail, error) {
	dataByUID := lo.GroupBy(data, func(x *dataplaneModel.SeriesDetail) uint64 { return x.UID })

	type job struct {
		uid  uint64
		data []*dataplaneModel.SeriesDetail
	}
	jobs := lo.MapToSlice(dataByUID, func(k uint64, v []*dataplaneModel.SeriesDetail) job {
		return job{
			uid:  k,
			data: v,
		}
	})

	return resultgroup.ThrottledParallelMap(
		jobs,
		runtime.GOMAXPROCS(0),
		func(param job) (*dataplaneModel.AggregatedSeriesDetail, error) {
			return aggregateForOneUID(param.uid, loc, aggr, param.data, extraChecks)
		},
	)
}

func aggregateForOneUID(
	uid uint64,
	loc *time.Location,
	aggr *adminModel.AggregationRule,
	data []*dataplaneModel.SeriesDetail,
	extraChecks bool,
) (*dataplaneModel.AggregatedSeriesDetail, error) {
	if len(data) == 0 {
		return nil, errors.New("no series data for aggregation")
	}

	start := data[0].Start
	end := data[0].End
	unit := data[0].Unit
	g := data[0].G

	if extraChecks {
		for _, series := range data {
			if series.Unit != unit {
				return nil, errors.New("inconsistent unit among series")
			}
			if series.G != g {
				return nil, errors.New("inconsistent g among series")
			}
			if series.UID != uid {
				return nil, errors.New("inconsistent series owner uid")
			}
			if series.AggRCode != aggr.Code {
				return nil, errors.New("should never happen: inconsistent aggr code")
			}
		}
	}

	equivalenceClassesData := lo.Values(categorizeDataIntoEquivalenceClasses(data))
	effectiveDataOfClasses := lo.Map(equivalenceClassesData, func(x []*dataplaneModel.SeriesDetail, _ int) map[int64]statWithSource {
		return effectiveDataFromEquivalenceClass(x, loc)
	})
	resultWithSources := aggregateEquivalenceClasses(effectiveDataOfClasses)
	result, sources := splitResultWithSources(resultWithSources)

	return &dataplaneModel.AggregatedSeriesDetail{
		UID:      uid,
		Start:    start,
		End:      end,
		ItemCode: aggr.DestItemCode,
		ZoneCode: aggr.DestZoneCode,
		AggRCode: aggr.Code,
		Unit:     unit,
		G:        g,
		Data:     result,
		Ctime:    time.Time{}, // TODO
		Sources:  sources,
	}, nil
}

// 序列数据的等价类 key
//
// 等价类是指：被视为相同序列：「ctime 较新的记录会在每个其包含的时刻，覆盖（而非叠加进）较旧记录数据」的划分结果
type equivalenceClassKey string

func makeEquivalenceClassKey(x *dataplaneModel.SeriesDetail) equivalenceClassKey {
	return equivalenceClassKey(fmt.Sprintf(
		"%s:%s:%s",
		x.QRN,
		x.MM,
		x.Props,
	))
}

func categorizeDataIntoEquivalenceClasses(
	data []*dataplaneModel.SeriesDetail,
) map[equivalenceClassKey][]*dataplaneModel.SeriesDetail {
	return lo.GroupBy(data, makeEquivalenceClassKey)
}

type statWithSource struct {
	v uint64
	s *dataplaneModel.AggregatedSource
}

// 对已经确保归属单一等价类的、已经按 ctime 从旧到新排序的一组序列数据，返回：
//
// * 每个时刻最终生效的计量
// * 每个时刻的计量分别来自哪条序列
func effectiveDataFromEquivalenceClass(
	data []*dataplaneModel.SeriesDetail,
	loc *time.Location,
) map[int64]statWithSource {
	g := data[0].G

	result := map[int64]statWithSource{}
	for _, s := range data {
		src := &dataplaneModel.AggregatedSource{
			Kind: dataplaneModel.SeriesKindSeries,
			Key:  s.SeriesKey,
		}

		seriesPointsByTime := lo.SliceToMap(s.Data, func(x *pb.StatPoint) (int64, uint64) {
			return x.Time.Seconds, x.Value
		})

		for t := s.Start.In(loc); t.Before(s.End); t = g.TimeOfNextPoint(t) {
			ts := t.Unix()
			result[ts] = statWithSource{
				v: seriesPointsByTime[ts], // 零值语义符合此处的预期
				s: src,
			}
		}
	}

	return result
}

type statWithSources struct {
	v uint64
	s []*dataplaneModel.AggregatedSource
}

func aggregateEquivalenceClasses(
	classes []map[int64]statWithSource,
) map[int64]statWithSources {
	result := map[int64]statWithSources{}
	for _, ec := range classes {
		for ts, p := range ec {
			resultPoint := result[ts]
			resultPoint.v += p.v
			resultPoint.s = append(resultPoint.s, p.s)
			result[ts] = resultPoint
		}
	}
	return result
}

func splitResultWithSources(
	resultWithSources map[int64]statWithSources,
) ([]*pb.StatPoint, []*dataplaneModel.AggregatedSource) {
	result := make([]*pb.StatPoint, 0, len(resultWithSources))
	sourcesSet := map[dataplaneModel.AggregatedSource]struct{}{}
	var sources []*dataplaneModel.AggregatedSource
	for ts, v := range resultWithSources {
		if v.v == 0 {
			continue
		}

		result = append(result, &pb.StatPoint{
			Time:  &timestamppb.Timestamp{Seconds: ts},
			Value: v.v,
		})

		for _, s := range v.s {
			if _, ok := sourcesSet[*s]; ok {
				continue
			}

			sourcesSet[*s] = struct{}{}
			sources = append(sources, s)
		}
	}

	sort.Slice(result, func(i int, j int) bool { return result[i].Time.Seconds < result[j].Time.Seconds })

	return result, sources
}
