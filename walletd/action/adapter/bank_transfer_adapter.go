package adapter

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
)

// BuildPbBankTransfer 构造 pb.BankTransfer
func BuildPbBankTransfer(m *model.BankTransfer) *pb.BankTransfer {
	if m == nil {
		return nil
	}
	return &pb.BankTransfer{
		Sn:                m.SN,
		PaymentAccount:    m.PaymentAccount,
		PaymentAccountNo:  m.PaymentAccountNo,
		CreatedAt:         timestamppb.New(m.CreatedAt),
		Amount:            m.Amount.String(),
		Status:            pb.BankTransferStatus(m.Status),
		ReceivedAccount:   m.ReceivedAccount,
		ReceivedAccountNo: m.ReceivedAccountNo,
		ReceivedBank:      m.ReceivedBank,
		ReceivedDate:      timestamppb.New(m.ReceivingDate),
		Uid:               m.UID,
		Creator:           m.Creator,
		PaymentBank:       m.PaymentBank,
		CurrencyType:      m.CurrencyType.String(),
		Remark:            m.Remark,
		BankTxnNo:         m.BankTxnNo,
		BankDescription:   m.BankDescription,
		IsVirtualAccount:  m.IsVirtualAccount,
		RecordType:        pb.RecordType(m.RecordType),
		MatchingType:      pb.MatchingType(m.MatchingType),
		Reallocates:       make([]*pb.BankTransfer, 0),
	}
}

// BuildPbBankTransferListResp 构造 pb.BankTransferListResp
func BuildPbBankTransferListResp(
	transfers []*model.BankTransfer,
	total uint64,
) *pb.BankTransferListResp {
	if transfers == nil {
		return &pb.BankTransferListResp{}
	}

	pbTransfers := make([]*pb.BankTransfer, 0, len(transfers))
	for _, t := range transfers {
		pbTransfers = append(pbTransfers, BuildPbBankTransfer(t))
	}

	return &pb.BankTransferListResp{
		Total: total,
		Data:  pbTransfers,
	}
}

// BuildPbPaymentAccountList 构造 pb.PaymentAccountList
func BuildPbPaymentAccountList(accounts []string) *pb.PaymentAccountList {
	return &pb.PaymentAccountList{
		Accounts: accounts,
	}
}

// BuildBankTransferQuery 构造 model.BankTransferQuery
func BuildBankTransferQuery(
	pbParam *pb.BankTransferListParam,
) *model.BankTransferQuery {
	// 初始化查询对象
	query := &model.BankTransferQuery{
		UID:              pbParam.GetUid(),
		Status:           model.BankTransferStatus(pbParam.GetStatus()),
		PaymentAccountNo: pbParam.GetPaymentAccountNo(),
		PaymentAccount:   pbParam.GetPaymentAccount(),
		ReceivedAccount:  pbParam.GetReceivedAccount(),
	}
	// 处理时间范围
	if pbParam.CreatedAtStart != nil {
		startTime := pbParam.CreatedAtStart.AsTime()
		query.CreatedAtStart = &startTime
	}

	if pbParam.CreatedAtEnd != nil {
		endTime := pbParam.CreatedAtEnd.AsTime()
		query.CreatedAtEnd = &endTime
	}

	return query
}

// BuildModelBankTransfer 构造 model.BankTransfer
func BuildModelBankTransfer(
	pbTransfer *pb.BankTransfer,
) (*model.BankTransfer, error) {
	if pbTransfer == nil {
		return nil, nil
	}

	// 初始化模型对象
	modelTransfer := &model.BankTransfer{
		SN:                pbTransfer.GetSn(),
		PaymentAccount:    pbTransfer.GetPaymentAccount(),
		PaymentAccountNo:  pbTransfer.GetPaymentAccountNo(),
		PaymentBank:       pbTransfer.GetPaymentBank(),
		ReceivedAccount:   pbTransfer.GetReceivedAccount(),
		ReceivedAccountNo: pbTransfer.GetReceivedAccountNo(),
		ReceivedBank:      pbTransfer.GetReceivedBank(),
		CurrencyType:      base.CurrencyType(pbTransfer.GetCurrencyType()),
		Amount:            base.MustParseNMoney(pbTransfer.GetAmount()),
		Remark:            pbTransfer.GetRemark(),
		IsVirtualAccount:  pbTransfer.GetIsVirtualAccount(),
		BankTxnNo:         pbTransfer.GetBankTxnNo(),
		BankDescription:   pbTransfer.GetBankDescription(),
		UID:               pbTransfer.GetUid(),
		RecordType:        model.RecordType(pbTransfer.GetRecordType()),
		MatchingType:      model.MatchingType(pbTransfer.GetMatchingType()),
		Creator:           pbTransfer.GetCreator(),
		Status:            model.BankTransferStatus(pbTransfer.GetStatus()),
	}

	// 处理收款日期
	if pbTransfer.GetReceivedDate() != nil {
		modelTransfer.ReceivingDate = pbTransfer.GetReceivedDate().AsTime()
	}

	return modelTransfer, nil
}

func BuildBankTransferRevokeParam(
	pbParam *pb.SNParam,
) *model.BankTransferRevokeParam {
	if pbParam == nil {
		return nil
	}
	reallocates := make([]*model.ReallocateParam, len(pbParam.GetReallocates()))
	for i, param := range pbParam.GetReallocates() {
		reallocates[i] = &model.ReallocateParam{
			Uid:    param.GetUid(),
			Amount: base.MustParseNMoney(param.GetAmount()),
			Remark: param.GetRemark(),
		}
	}
	return &model.BankTransferRevokeParam{
		SN:          pbParam.GetSn(),
		Creator:     pbParam.GetCreator(),
		Reallocates: reallocates,
	}
}
