package action

import (
	"context"

	log "github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/action"
	"github.com/qbox/bo-base/v4/eventbus"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/service"
)

// CouponAction defines CouponAction
type CouponAction struct {
	*action.BaseAction
	pb.UnimplementedPayCouponServiceServer

	couponSrv       *service.CouponService
	couponBatchSrv  *service.CouponBatchService
	defaultPageSize uint64

	mqCtx context.Context
}

var _ pb.PayCouponServiceServer = (*CouponAction)(nil)

// NewCouponAction new coupon action
func NewCouponAction(
	couponSrv *service.CouponService,
	couponBatchSrv *service.CouponBatchService,
	defaultPageSize uint64,
) *CouponAction {
	return &CouponAction{
		BaseAction:      action.NewBaseAction(defaultPageSize),
		couponSrv:       couponSrv,
		couponBatchSrv:  couponBatchSrv,
		defaultPageSize: defaultPageSize,
		mqCtx:           nil, // populated in EventbusDaemon()
	}
}

// EventbusDaemon subscribe mq topic
func (a *CouponAction) EventbusDaemon(ctx context.Context) {
	a.mqCtx = ctx

	subscribers := []struct {
		Topic    string
		Queue    string
		Handlers []eventbus.Handler
	}{
		{
			Topic:    eventbus.TradeCouponConsumedTopic,
			Queue:    eventbus.TradeCouponConsumedCountQueue,
			Handlers: []eventbus.Handler{eventbus.HandleFunc(a.ConsumedCount)},
		},
	}

	go func() {
		for _, sub := range subscribers {
			if err := a.couponSrv.GetEventbus().Subscribe(sub.Topic, sub.Queue, sub.Handlers...); err != nil {
				log.Printf(
					"<EventBusDaemon> subscribe topic: %s queue: %s failed with error: %s",
					sub.Topic, sub.Queue,
					err,
				)
				return
			}
			log.Printf("subscribe topic %s, queue %s", sub.Topic, sub.Queue)
		}
	}()
}
