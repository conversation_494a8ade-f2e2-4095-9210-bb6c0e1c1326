package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/measured/model"
)

// GetItemStatConfigByID 根据 ID 查询单个计费项计量源配置
func (s *MeasureBizService) GetItemStatConfigByID(
	ctx context.Context,
	id uint64,
) (*model.ItemStatConfig, error) {
	return s.dao.ItemStatConfig.GetByID(id, s.cacheExpires)
}

// GetItemStatConfigByItemID 根据计费项 ID 查询单个计费项计量源配置
func (s *MeasureBizService) GetItemStatConfigByItemID(
	ctx context.Context,
	id uint64,
) (*model.ItemStatConfig, error) {
	return s.dao.ItemStatConfig.GetByItemID(id, s.cacheExpires)
}

// CreateItemStatConfig 创建一个计费项计量源配置
func (s *MeasureBizService) CreateItemStatConfig(
	ctx context.Context,
	obj *model.ItemStatConfig,
) (*model.ItemStatConfig, error) {
	obj.ID = 0
	err := s.dao.ItemStatConfig.Save(obj, s.cacheExpires)
	return obj, err
}

// DeleteItemStatConfigByID 根据 ID 删除一个计费项计量源配置
func (s *MeasureBizService) DeleteItemStatConfigByID(
	ctx context.Context,
	id uint64,
) (*model.ItemStatConfig, error) {
	obj, err := s.dao.ItemStatConfig.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	err = s.dao.ItemStatConfig.DeleteByID(id)
	return obj, err
}
