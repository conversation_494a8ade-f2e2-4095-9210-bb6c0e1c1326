package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// GetCouponRebateByID 通过ID获取返现优惠券
func (a *WalletAction) GetCouponRebateByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CouponRebate, error) {
	rebate, err := a.walletBizSrv.GetCouponRebateByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponRebate(rebate)
}

// GetCouponRebateByCode 通过 code 获取返现优惠券
func (a *WalletAction) GetCouponRebateByCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.CouponRebate, error) {
	rebate, err := a.walletBizSrv.GetCouponRebateWithIsBoundByCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponRebateWithIsBound(rebate)
}

// CreateCouponRebate 创建返现优惠券
func (a *WalletAction) CreateCouponRebate(
	ctx context.Context,
	rebate *pb.CouponRebate,
) (*pb.CouponRebate, error) {
	_rebate, err := adapter.BuildCouponRebate(rebate)
	if err != nil {
		return nil, err
	}
	_rebate, err = a.walletBizSrv.CreateCouponRebate(ctx, _rebate)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponRebate(_rebate)
}

// UpdateCouponRebateByID 按照 ID 更新返现优惠券
func (a *WalletAction) UpdateCouponRebateByID(
	ctx context.Context,
	param *pb.IDCouponRebateParam,
) (*pb.CouponRebate, error) {
	_rebate, err := adapter.BuildCouponRebate(param.GetCouponRebate())
	if err != nil {
		return nil, err
	}
	_rebate, err = a.walletBizSrv.UpdateCouponRebateByID(ctx, param.GetId(), _rebate)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponRebate(_rebate)
}

// UpdateCouponRebateByCode 按照 code 更新返现优惠券
func (a *WalletAction) UpdateCouponRebateByCode(
	ctx context.Context,
	param *pb.CodeCouponRebateParam,
) (*pb.CouponRebate, error) {
	_rebate, err := adapter.BuildCouponRebate(param.GetCouponRebate())
	if err != nil {
		return nil, err
	}
	_rebate, err = a.walletBizSrv.UpdateCouponRebateByCode(ctx, param.GetCode(), _rebate)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponRebate(_rebate)
}

// DeleteCouponRebateByID 按照 ID 删除返现优惠券
func (a *WalletAction) DeleteCouponRebateByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CouponRebate, error) {
	_rebate, err := a.walletBizSrv.DeleteCouponRebateByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponRebate(_rebate)
}

// DeleteCouponRebateByCode 按照 code 删除返现优惠券
func (a *WalletAction) DeleteCouponRebateByCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.CouponRebate, error) {
	_rebate, err := a.walletBizSrv.DeleteCouponRebateByCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponRebate(_rebate)
}

// ListAllCouponRebates 列举所有返现优惠券
func (a *WalletAction) ListAllCouponRebates(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.CouponRebateList, error) {
	count, err := a.walletBizSrv.CountAllCouponRebates(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	rebateWithIsBoundList, err := a.walletBizSrv.ListCouponRebatesByConds(ctx, "", offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponRebateList{
		Count:         count,
		CouponRebates: make([]*pb.CouponRebate, len(rebateWithIsBoundList)),
	}
	for i, rebateWithIsBound := range rebateWithIsBoundList {
		_rebate, err := adapter.BuildPbCouponRebateWithIsBound(rebateWithIsBound)
		if err != nil {
			return nil, err
		}
		list.CouponRebates[i] = _rebate
	}
	return list, nil
}

// CountAllCouponRebates 获取所有返现优惠券数量
func (a *WalletAction) CountAllCouponRebates(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountAllCouponRebates(ctx)
	return &pb.CountParam{Count: count}, err
}

// ListCouponRebatesByScopeID 通过计费范围列举返现优惠券
func (a *WalletAction) ListCouponRebatesByScopeID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.CouponRebateList, error) {
	count, err := a.walletBizSrv.CountCouponRebatesByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	rebates, err := a.walletBizSrv.ListCouponRebatesByScopeID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponRebateList{
		Count:         count,
		CouponRebates: make([]*pb.CouponRebate, len(rebates)),
	}
	for i, rebate := range rebates {
		_rebate, err := adapter.BuildPbCouponRebate(&rebate)
		if err != nil {
			return nil, err
		}
		list.CouponRebates[i] = _rebate
	}
	return list, nil
}

// CountCouponRebatesByScopeID 通过计费范围获取返现优惠券数量
func (a *WalletAction) CountCouponRebatesByScopeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountCouponRebatesByScopeID(ctx, param.GetId())
	return &pb.CountParam{Count: count}, err
}

// ListCouponRebatesByUID 通过用户ID列举返现优惠券
func (a *WalletAction) ListCouponRebatesByUID(
	ctx context.Context,
	param *pb.UIDPagingParam,
) (*pb.CouponRebateList, error) {
	count, err := a.walletBizSrv.CountCouponRebatesByUID(ctx, param.GetUid())
	if err != nil {
		return nil, err
	}
	list := &pb.CouponRebateList{
		Count:         count,
		CouponRebates: make([]*pb.CouponRebate, 0),
	}
	if count <= 0 {
		return list, nil
	}

	offset, limit := a.Paging(param)
	rebates, err := a.walletBizSrv.ListCouponRebatesByUID(ctx, param.GetUid(), offset, limit)
	if err != nil {
		return nil, err
	}

	list.CouponRebates = make([]*pb.CouponRebate, len(rebates))
	for i, rebate := range rebates {
		_rebate, err := adapter.BuildPbCouponRebate(&rebate)
		if err != nil {
			return nil, err
		}
		list.CouponRebates[i] = _rebate
	}
	return list, nil
}

// CountCouponRebatesByUID 通过用户ID获取返现优惠券数量
func (a *WalletAction) CountCouponRebatesByUID(
	ctx context.Context,
	param *pb.UIDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountCouponRebatesByUID(ctx, param.GetUid())
	return &pb.CountParam{Count: count}, err
}

// SearchCouponRebates 模糊搜索 CouponRebates
func (a *WalletAction) SearchCouponRebates(
	ctx context.Context,
	param *pb.SearchCouponsParam,
) (*pb.CouponRebateList, error) {
	count, err := a.walletBizSrv.CountAllCouponRebates(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	rebateWithIsBoundList, err := a.walletBizSrv.ListCouponRebatesByConds(ctx, param.Pattern, offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponRebateList{
		Count:         count,
		CouponRebates: make([]*pb.CouponRebate, len(rebateWithIsBoundList)),
	}
	for i, rebateWithIsBound := range rebateWithIsBoundList {
		_rebate, err := adapter.BuildPbCouponRebateWithIsBound(rebateWithIsBound)
		if err != nil {
			return nil, err
		}
		list.CouponRebates[i] = _rebate
	}
	return list, nil
}
