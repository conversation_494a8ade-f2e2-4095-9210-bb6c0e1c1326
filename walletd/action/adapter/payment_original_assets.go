package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qpay/payment/model"
)

type QueryOriginalAssetsParam struct {
	MonthBillPaymentIDsMap  map[base.Month][]string
	MonthOrderPaymentIDsMap map[base.Month][]string
	RefundedOrderPaymentIDs []string
}

func BuildQueryOriginalAssetsParam(req pb.QueryOriginalAssetsReq, loc *time.Location) QueryOriginalAssetsParam {
	monthBillPaymentIDsMap := make(map[base.Month][]string)
	monthOrderPaymentIDsMap := make(map[base.Month][]string)

	for _, billPaymentIDs := range req.GetDeductBillPaymentIds() {
		m := base.NewMonth(billPaymentIDs.GetMonth().AsTime().In(loc))
		monthBillPaymentIDsMap[m] = billPaymentIDs.GetPaymentIds()
	}

	for _, orderPaymentIDs := range req.GetDeductOrderPaymentIds() {
		m := base.NewMonth(orderPaymentIDs.GetMonth().AsTime().In(loc))
		monthOrderPaymentIDsMap[m] = orderPaymentIDs.GetPaymentIds()
	}

	refundOrderPaymentIDs := req.GetOrderRefundedPaymentIds()

	return QueryOriginalAssetsParam{
		MonthBillPaymentIDsMap:  monthBillPaymentIDsMap,
		MonthOrderPaymentIDsMap: monthOrderPaymentIDsMap,
		RefundedOrderPaymentIDs: refundOrderPaymentIDs,
	}
}

func BuildQueryOriginalAssetsResp(result []model.QueryOriginalAssetsResult) *pb.QueryOriginalAssetsResp {
	list := make([]*pb.QueryOriginalAssetsResult, 0)
	for _, queryResult := range result {
		list = append(list, &pb.QueryOriginalAssetsResult{
			Month:            queryResult.RelatedMonth.String(),
			PaymentId:        queryResult.PaymentID,
			PaymentAmount:    queryResult.PaidAmount.ToInt64(),
			OriginalAmount:   queryResult.Amount.ToInt64(),
			HasRefund:        queryResult.HasRefund,
			Type:             queryResult.Type,
			Excode:           queryResult.Excode,
			EntryId:          queryResult.EntryID,
			AssetType:        int32(queryResult.AssetType),
			AssetId:          queryResult.AssetID,
			AvailableAt:      timestamppb.New(queryResult.AvailableAt),
			LatestBusinessAt: timestamppb.New(queryResult.LatestBusinessAt),
			CurrencyType:     queryResult.CurrencyType.String(),
		})
	}
	return &pb.QueryOriginalAssetsResp{
		Result: list,
	}
}
