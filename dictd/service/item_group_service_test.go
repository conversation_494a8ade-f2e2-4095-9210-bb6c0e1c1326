package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestItemGroup(t *testing.T) {
	sandbox := buildSandbox(t)

	dictService := sandbox.dictService

	const productID uint64 = 1
	gs := []model.ItemGroup{
		{
			ProductID:   productID,
			Code:        "kodo:flow",
			Name:        "流量",
			Description: "云存储的流量",
			Remark:      "云存储的流量备注",
			Order:       1,
		},
		{
			ProductID:   productID,
			Code:        "kodo:storage",
			Name:        "存储",
			Description: "云存储的存储",
			Remark:      "云存储的存储备注",
			Order:       2,
		},
	}
	assertFields := func(group *model.ItemGroup, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(gs) }) {
			assert.Equal(t, gs[n].ProductID, group.ProductID, msgAndArgs...)
			assert.Equal(t, gs[n].Name, group.Name, msgAndArgs...)
			assert.Equal(t, gs[n].Description, group.Description, msgAndArgs...)
			assert.Equal(t, gs[n].Remark, group.Remark, msgAndArgs...)
			assert.Equal(t, gs[n].Order, group.Order, msgAndArgs...)
		}
	}
	ctx := context.TODO()
	ids := make(map[uint64]int, len(gs))
	for n, g := range gs {
		group, err := dictService.CreateItemGroup(ctx, &g)
		if assert.NoError(t, err, "CreateItemGroup") {
			assert.NotZero(t, group.ID, "CreateItemGroup return zero id")
			ids[group.ID] = n
		}

		group, err = dictService.GetItemGroupByID(ctx, group.ID)
		if assert.NoError(t, err, "GetItemGroupByID") {
			assertFields(group, n, "GetItemGroupByID fields not match")
		}

		group, err = dictService.GetItemGroupByCode(ctx, group.Code)
		if assert.NoError(t, err, "GetItemGroupByCode") {
			assertFields(group, n, "GetItemGroupByCode fields not match")
		}

		group2, err := dictService.GetItemGroupByProductIDAndCode(ctx, productID, group.Code)
		if assert.NoError(t, err, "GetItemGroupByID") {
			assertFields(group2, n, "GetItemGroupByID fields not match")
		}

		group.Name += "suffix1"
		gs[n].Name += "suffix1"
		group, err = dictService.UpdateItemGroupByID(ctx, group.ID, group)
		if assert.NoError(t, err, "UpdateItemGroupByID") {
			assert.NotZero(t, group.ID, "UpdateItemGroupByID return zero id")
		}
	}

	groups, err := dictService.ListItemGroupsByProductID(ctx, productID, 0, 10)
	if assert.NoError(t, err, "ListItemGroupsByProductID") {
		assert.Len(t, groups, len(gs), "ListItemGroupsByProductID unexpected length")
		count, err := dictService.CountItemGroupsByProductID(ctx, productID)
		if assert.NoError(t, err, "CountItemGroupsByProductID") {
			assert.Equal(t, uint64(len(gs)), count, "CountItemGroupsByProductID count unexpected")
		}
		for _, group := range groups {
			if assert.Contains(t, ids, group.ID) {
				assertFields(&group, ids[group.ID])
			}
		}
	}

	for id, n := range ids {
		group, err := dictService.DeleteItemGroupByID(ctx, id)
		if assert.NoError(t, err, "DeleteItemGroupByID") {
			assertFields(group, n)
		}
	}

	count, err := dictService.CountItemGroupsByProductID(ctx, productID)
	if assert.NoError(t, err, "CountItemGroupsByProductID") {
		assert.Equal(t, uint64(0), count, "CountItemGroupsByProductID count after delete all is not zero")
	}
}
