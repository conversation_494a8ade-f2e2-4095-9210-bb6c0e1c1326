package stat

import "fmt"

// ErrQueryAxesFailed 拉量失败
type ErrQueryAxesFailed struct {
	// Errors 所有失败的轴的错误
	//
	// TODO: 让这个结构能关联到具体失败的轴
	Errors []error
}

var _ error = ErrQueryAxesFailed{}

func (e ErrQueryAxesFailed) Error() string {
	return fmt.Sprintf("query axes failed: errors=%+v", e.Errors)
}

// ErrAxisQueryPanicked 单条轴拉量 panic
type ErrAxisQueryPanicked struct {
	// V panic 参数
	V any
}

var _ error = ErrAxisQueryPanicked{}

func (e ErrAxisQueryPanicked) Error() string {
	return fmt.Sprintf("axis query panicked: %+v", e.V)
}

// ErrUnsupportedProduct 不支持的产品线
type ErrUnsupportedProduct struct {
	// ProductCode 产品线 code
	ProductCode string
}

var _ error = ErrUnsupportedProduct{}

func (e ErrUnsupportedProduct) Error() string {
	return fmt.Sprintf("unsupported product: code=%s", e.ProductCode)
}
