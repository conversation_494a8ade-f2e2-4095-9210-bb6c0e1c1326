package adapter

import (
	pb "github.com/qbox/pay-sdk/price"
	"qiniu.io/pay/priced/service"
)

// BuildPbPriceDiffType 转换 service.PriceDiffType 为 proto.PriceDiffType
func BuildPbPriceDiffType(typ service.PriceDiffType) pb.PriceDiffType {
	switch typ {
	case service.PriceDiffTypeIncomparable:
		return pb.INCOMPARABLE
	case service.PriceDiffTypeEQ:
		return pb.EQ
	case service.PriceDiffTypeGT:
		return pb.GT
	case service.PriceDiffTypeLT:
		return pb.LT
	default:
		return pb.INCOMPARABLE
	}
}

// BuildPriceDiffType 转换 proto.PriceDiffType 为 service.PriceDiffType
func BuildPriceDiffType(typ pb.PriceDiffType) service.PriceDiffType {
	switch typ {
	case pb.INCOMPARABLE:
		return service.PriceDiffTypeIncomparable
	case pb.EQ:
		return service.PriceDiffTypeEQ
	case pb.GT:
		return service.PriceDiffTypeGT
	case pb.LT:
		return service.PriceDiffTypeLT
	default:
		return service.PriceDiffTypeIncomparable
	}
}

// BuildPriceScope 转换 proto.PriceScopeParam 为 service.PriceScope
func BuildPriceScope(param *pb.PriceScopeParam) *service.PriceScope {
	if param == nil {
		return nil
	}

	scope := &service.PriceScope{
		Products: make(service.PriceScopeMap),
		Groups:   make(service.PriceScopeMap),
		Items:    make(service.PriceScopeMap),
	}

	for _, item := range param.GetItems() {
		scope.Items[item.Id] = !item.GetIsExcluded()
	}

	for _, group := range param.GetGroups() {
		scope.Groups[group.Id] = !group.GetIsExcluded()
	}

	for _, product := range param.GetProducts() {
		scope.Products[product.Id] = !product.GetIsExcluded()
	}

	return scope
}

// BuildPbDiffPriceItemStair 转换 service.DiffPriceItemStair 为 proto.DiffPriceItemStair
func BuildPbDiffPriceItemStair(param *service.DiffPriceItemStair) (*pb.DiffPriceItemStair, error) {
	pbStair, err := BuildPbPriceItemStair(&param.Stair)
	if err != nil {
		return nil, err
	}

	return &pb.DiffPriceItemStair{
		Stair: pbStair,
		Type:  BuildPbPriceDiffType(param.Type),
	}, nil
}

// BuildDiffPriceItemStair 转换 proto.DiffPriceItemStair 为 service.DiffPriceItemStair
func BuildDiffPriceItemStair(param *pb.DiffPriceItemStair) (*service.DiffPriceItemStair, error) {
	stair, err := BuildPriceItemStair(param.Stair)
	if err != nil {
		return nil, err
	}

	return &service.DiffPriceItemStair{
		Stair: *stair,
		Type:  BuildPriceDiffType(param.Type),
	}, nil
}

// BuildPbDiffPriceItem 转换 service.DiffPriceItem 为 proto.DiffPriceItem
func BuildPbDiffPriceItem(param *service.DiffPriceItem) (*pb.DiffPriceItem, error) {
	pbPriceItem, err := BuildPbPriceItem(&param.PriceItem)
	if err != nil {
		return nil, err
	}

	pbStairs := make([]*pb.DiffPriceItemStair, 0, len(param.Stairs))
	for i := range param.Stairs {
		pbStair, err1 := BuildPbDiffPriceItemStair(&param.Stairs[i])
		if err1 != nil {
			return nil, err1
		}
		pbStairs = append(pbStairs, pbStair)
	}

	return &pb.DiffPriceItem{
		PriceItem: pbPriceItem,
		Stairs:    pbStairs,
	}, nil
}

// BuildDiffPriceItem 转换 proto.DiffPriceItem 为 service.DiffPriceItem
func BuildDiffPriceItem(param *pb.DiffPriceItem) (*service.DiffPriceItem, error) {
	priceItem, err := BuildPriceItem(param.PriceItem)
	if err != nil {
		return nil, err
	}

	stairs := make([]service.DiffPriceItemStair, 0, len(param.Stairs))
	for _, pbStair := range param.Stairs {
		stair, err := BuildDiffPriceItemStair(pbStair)
		if err != nil {
			return nil, err
		}

		stairs = append(stairs, *stair)
	}

	return &service.DiffPriceItem{
		PriceItem: *priceItem,
		Stairs:    stairs,
	}, nil
}

// BuildPbDiffPriceItemList 转换 service.DiffPriceItemList 为 proto.DiffPriceItemList
func BuildPbDiffPriceItemList(param *service.DiffPriceItemList) (*pb.DiffPriceItemList, error) {
	l := make([]*pb.DiffPriceItem, 0, len(param.PriceItems))
	for i := range param.PriceItems {
		pbDiffPriceItem, err := BuildPbDiffPriceItem(&param.PriceItems[i])
		if err != nil {
			return nil, err
		}

		l = append(l, pbDiffPriceItem)
	}
	return &pb.DiffPriceItemList{
		PriceItems: l,
	}, nil
}

// BuildDiffPriceItemList 转换 proto.DiffPriceItemList 为 service.DiffPriceItemList
func BuildDiffPriceItemList(param *pb.DiffPriceItemList) (*service.DiffPriceItemList, error) {
	l := make([]service.DiffPriceItem, 0, len(param.PriceItems))
	for _, priceItem := range param.PriceItems {
		diffPriceItem, err := BuildDiffPriceItem(priceItem)
		if err != nil {
			return nil, err
		}

		l = append(l, *diffPriceItem)
	}
	return &service.DiffPriceItemList{
		PriceItems: l,
	}, nil
}

// BuildPbDiffItemPrices 转换 service.DiffItemPrices 为 proto.DiffItemPrices
func BuildPbDiffItemPrices(param *service.DiffItemPrices) (*pb.DiffItemPrices, error) {
	m := make(map[uint64]*pb.DiffPriceItemList, len(param.ItemPriceMap))
	for itemID := range param.ItemPriceMap {
		v := param.ItemPriceMap[itemID]
		pbList, err := BuildPbDiffPriceItemList(&v)
		if err != nil {
			return nil, err
		}

		m[itemID] = pbList
	}
	return &pb.DiffItemPrices{
		ItemPrices: m,
	}, nil
}

// BuildDiffItemPrices 转换 proto.DiffItemPrices 为 service.DiffItemPrices
func BuildDiffItemPrices(param *pb.DiffItemPrices) (*service.DiffItemPrices, error) {
	m := make(map[uint64]service.DiffPriceItemList, len(param.ItemPrices))
	for itemID, pbList := range param.ItemPrices {
		l, err := BuildDiffPriceItemList(pbList)
		if err != nil {
			return nil, err
		}

		m[itemID] = *l
	}
	return &service.DiffItemPrices{
		ItemPriceMap: m,
	}, nil
}

// BuildPbPriceDiffResp 转换 service.PriceDiffResp 为 proto.PriceDiffResp
func BuildPbPriceDiffResp(param *service.PriceDiffResp) (*pb.PriceDiffResp, error) {
	before, err := BuildPbDiffItemPrices(&param.Before)
	if err != nil {
		return nil, err
	}

	after, err := BuildPbDiffItemPrices(&param.After)
	if err != nil {
		return nil, err
	}

	return &pb.PriceDiffResp{
		Before: before,
		After:  after,
	}, nil
}

// BuildPriceDiffResp 转换 proto.PriceDiffResp 为 service.PriceDiffResp
func BuildPriceDiffResp(param *pb.PriceDiffResp) (*service.PriceDiffResp, error) {
	before, err := BuildDiffItemPrices(param.Before)
	if err != nil {
		return nil, err
	}

	after, err := BuildDiffItemPrices(param.After)
	if err != nil {
		return nil, err
	}

	return &service.PriceDiffResp{
		Before: *before,
		After:  *after,
	}, nil
}
