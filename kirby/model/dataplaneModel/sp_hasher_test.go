package dataplaneModel

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSPHasher(t *testing.T) {
	testcases := []struct {
		in       map[string]string
		expected uint32
	}{
		{
			in:       nil,
			expected: 0x811c9dc5,
		},
		{
			in:       map[string]string{},
			expected: 0x811c9dc5,
		},
		{
			in:       map[string]string{"foo": ""},
			expected: 0x811c9dc5,
		},
		{
			in:       map[string]string{"foo": "bar", "baz": "quux"},
			expected: 0x9e5e1606,
		},
		{
			in:       map[string]string{"foo": "bar", "baz": "quux", "frob": ""},
			expected: 0x9e5e1606,
		},
		{
			in:       map[string]string{"space-kind": "std", "$qrnRegion": "z0", "$qrnResource": "1234567"},
			expected: 0xb70d6906,
		},
		{
			in:       map[string]string{"space-kind": "std", "$qrnRegion": "z0", "$qrnResource": "7654321"},
			expected: 0xb70d6906,
		},
	}
	for _, tc := range testcases {
		actual := SPHasher(tc.in).Hash()
		assert.Equal(t, tc.expected, actual)
	}
}
