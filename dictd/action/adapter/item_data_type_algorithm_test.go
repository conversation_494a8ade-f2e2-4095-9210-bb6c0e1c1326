package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/dictd/model"
)

func TestItemDataTypeAlgorithmRoundTrip(t *testing.T) {
	x := &model.ItemDataTypeAlgorithm{
		ID:         233,
		DataTypeID: 234,
		Code:       "test",
		Name:       "测试",
		Script:     "max(stat)",
		IsDefault:  true,
		CreatedAt:  time.Now().Round(0),
		UpdatedAt:  time.Now().Round(0),
	}

	y, err := BuildPbItemDataTypeAlgorithm(x)
	assert.NoError(t, err)

	z, err := BuildItemDataTypeAlgorithm(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.DataTypeID, z.DataTypeID)
	assert.Equal(t, x.Code, z.Code)
	assert.Equal(t, x.Name, z.Name)
	assert.Equal(t, x.<PERSON><PERSON>, z<PERSON>)
	assert.Equal(t, x.<PERSON>, z.<PERSON>)
	assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.Unix<PERSON>ano(), z.UpdatedAt.UnixNano())
}
