package model

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// CouponLockDetail model definition
type CouponLockDetail struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`

	// LockRecordID 对应的锁句柄 ID
	LockRecordID uint64 `gorm:"index:idx_coupon_lock_detail_lock_record_id"`

	UID uint64 `gorm:"column:uid"`
	// BatchID 券所对应的批次 ID
	BatchID uint64 `gorm:"column:batch_id"`
	// CouponCode 券 code
	CouponCode string `gorm:"column:coupon_code"`
	// CouponID 关联的券 ID
	CouponID uint64 `gorm:"index:idx_coupon_lock_detail_coupon_id"`
	// OrderHash 关联的 order hash
	OrderHash string `gorm:"index:idx_coupon_lock_detail_order_hash"`
	// PoID 关联的 po ID
	PoID uint64 `gorm:"index:idx_coupon_lock_detail_po_id"`
	// Money 预留给这个 po 的金额
	Money base.Money

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// CouponLockDetailDao is data access object of CouponLockDetail model
type CouponLockDetailDao struct {
	base *dao.BaseDao
}

// NewCouponLockDetailDao is constructor of CouponLockDetailDao
func NewCouponLockDetailDao(base *dao.BaseDao) *CouponLockDetailDao {
	return &CouponLockDetailDao{
		base: base,
	}
}

// GetByID selects a CouponLockDetail by id
func (d *CouponLockDetailDao) GetByID(id uint64, expires ...time.Duration) (*CouponLockDetail, error) {
	model := &CouponLockDetail{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponLockDetail{}).
			Where("`id` = ?", id).
			First(value).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// Save inserts or updates a CouponLockDetail by id
func (d *CouponLockDetailDao) Save(model *CouponLockDetail, expires ...time.Duration) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Save(model).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// BulkInsert 批量插入一组 CouponLockDetails
func (d *CouponLockDetailDao) BulkInsert(ctx context.Context, l []CouponLockDetail) error {
	return d.base.BulkInsert(ctx, l)
}

// DeleteByIDs delete records of CouponLockDetail by ids
func (d *CouponLockDetailDao) DeleteByIDs(ids []uint64) error {
	err := d.base.Execute(func(i any) error {
		return d.base.Delete(&CouponLockDetail{}, "`id` IN (?)", ids).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("ids", ids)
	}
	return nil
}

// DeleteByLockRecordID delete records of CouponLockDetail by lock record ID
func (d *CouponLockDetailDao) DeleteByLockRecordID(id uint64) error {
	err := d.base.Execute(func(i any) error {
		return d.base.Delete(&CouponLockDetail{}, "`lock_record_id` = ?", id).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("lock_record_id", id)
	}
	return nil
}

// DeleteByCouponID delete records of CouponLockDetail by coupon ID
func (d *CouponLockDetailDao) DeleteByCouponID(id uint64) error {
	err := d.base.Execute(func(i any) error {
		return d.base.Delete(&CouponLockDetail{}, "`coupon_id` = ?", id).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("coupon_id", id)
	}
	return nil
}

// DeleteByOrderHash delete records of CouponLockDetail by order hash
func (d *CouponLockDetailDao) DeleteByOrderHash(orderHash string) error {
	err := d.base.Execute(func(i any) error {
		return d.base.Delete(&CouponLockDetail{}, "`order_hash` = ?", orderHash).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("order_hash", orderHash)
	}
	return nil
}

// ListAll returns all records of CouponLockDetail
func (d *CouponLockDetailDao) ListAll(
	offset int,
	limit int,
	expires ...time.Duration,
) ([]CouponLockDetail, error) {
	var result []CouponLockDetail
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponLockDetail{}).
			Offset(offset).
			Limit(limit).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return result, nil
}

// ListByLockRecordID select records of CouponLockDetail by lock record ID
func (d *CouponLockDetailDao) ListByLockRecordID(
	id uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) ([]CouponLockDetail, error) {
	var result []CouponLockDetail
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponLockDetail{}).
			Where("`lock_record_id` = ?", id).
			Offset(offset).
			Limit(limit).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err).WithField("lock_record_id", id)
	}
	return result, nil
}

// ListByCouponID select records of CouponLockDetail by coupon id
func (d *CouponLockDetailDao) ListByCouponID(
	id uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) ([]CouponLockDetail, error) {
	var result []CouponLockDetail
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponLockDetail{}).
			Where("`coupon_id` = ?", id).
			Offset(offset).
			Limit(limit).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return result, nil
}

// ListByOrderHash select records of CouponLockDetail by order hash
func (d *CouponLockDetailDao) ListByOrderHash(
	orderHash string,
	expires ...time.Duration,
) ([]CouponLockDetail, error) {
	var result []CouponLockDetail
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponLockDetail{}).
			Where("`order_hash` = ?", orderHash).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err).WithField("order_hash", orderHash)
	}
	return result, nil
}
