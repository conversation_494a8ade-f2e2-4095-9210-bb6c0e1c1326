package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/models/enums"
)

// GetUserCurrentPlan 获取用户当前的 plan
func (s *PlanService) GetUserCurrentPlan(
	ctx context.Context,
	uid uint64,
	month time.Time,
) (*models.UserPlan, *models.Plan, error) {
	logger := logging.GetLogger(ctx)
	userPlan, err := s.dao.UserPlanDao.GetCurrentPlan(ctx, uid, month)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"month": month,
		}).WithError(err).Error("get current plan failed")
		return nil, nil, err
	}
	plan, err := s.dao.PlanDao.GetByLevelAndVersion(
		ctx,
		userPlan.Level,
		userPlan.Version,
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"userPlan": userPlan,
		}).WithError(err).Error("get level&version failed")
		return nil, nil, err
	}
	return userPlan, plan, nil
}

// ListUserPlanList 获取用户 plan 列表
func (s *PlanService) ListUserPlanList(
	ctx context.Context,
	uid uint64,
	level enums.PlanLevel,
	month time.Time,
	offset int,
	limit int,
) ([]*models.UserPlan, map[string]*models.Plan, error) {
	logger := logging.GetLogger(ctx)
	userPlans, err := s.dao.UserPlanDao.List(
		ctx,
		uid,
		level,
		month,
		offset,
		limit,
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"level": level,
			"month": month,
		}).WithError(err).Error("list user plan failed")
		return nil, nil, err
	}

	indices := make([]planVersionIdx, len(userPlans))
	for i, plan := range userPlans {
		indices[i] = planVersionIdx{
			Level:   plan.Level,
			Version: plan.Version,
		}
	}
	planMap, err := s.fetchPlanMap(ctx, indices)
	if err != nil {
		return nil, nil, err
	}
	return userPlans, planMap, nil
}

type planVersionIdx struct {
	Level   enums.PlanLevel
	Version uint32
}

func (s *PlanService) fetchPlanMap(
	ctx context.Context,
	indices []planVersionIdx,
) (map[string]*models.Plan, error) {
	logger := logging.GetLogger(ctx)
	// map key: func GenPlanIndexString
	// map value: planVersion struct
	planQueryMap := make(map[string]planVersionIdx)

	for _, idx := range indices {
		planQueryMap[GenPlanIndexString(idx.Version, idx.Level)] = planVersionIdx{
			Level:   idx.Level,
			Version: idx.Version,
		}
	}

	planQuerySlice := make([]planVersionIdx, 0, len(planQueryMap))
	for _, idx := range planQueryMap {
		planQuerySlice = append(planQuerySlice, idx)
	}

	// 查出上面所涉及到的 plan 信息
	plans, err := resultgroup.ParallelMap[planVersionIdx, *models.Plan](
		planQuerySlice,
		func(param planVersionIdx) (*models.Plan, error) {
			return s.dao.PlanDao.GetByLevelAndVersion(ctx, param.Level, param.Version)
		},
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"plans": planQuerySlice,
		}).WithError(err).Error("get by level&version plan failed")
		return nil, err
	}
	// 转为 map, 方便访问
	// map key: func GenPlanIndexString
	// map value: *models.Plan
	planMap := make(map[string]*models.Plan, len(plans))
	for _, plan := range plans {
		planMap[GenPlanIndexString(plan.Version, plan.Level)] = plan
	}
	return planMap, nil
}

// GenPlanIndexString gen index string key for plan
func GenPlanIndexString(version uint32, level enums.PlanLevel) string {
	return fmt.Sprintf("%d:%s", version, level.String())
}

// CustomizeUserPlan 标记定制
func (s *PlanService) CustomizeUserPlan(
	ctx context.Context,
	uid uint64,
	epoch time.Time,
) error {
	logger := logging.GetLogger(ctx)
	err := s.AdminSubscribePlan(
		ctx,
		&models.SubscribePlanParam{
			Uid:          uid,
			Level:        enums.LevelEnterprise,
			PackCode:     "",
			EffectType:   enums.EffectTypeImmediately,
			SubscribeSrc: enums.SubscribeSrcAdmin,
		},
		epoch,
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"epoch": epoch,
		}).WithError(err).Error("customize userPlan failed")
		return err
	}

	return nil
}

// UnCustomizeUserPlan 取消定制
func (s *PlanService) UnCustomizeUserPlan(
	ctx context.Context,
	uid uint64,
	epoch time.Time,
) error {
	logger := logging.GetLogger(ctx)
	month := base.ThisMonth(epoch)
	// 查询出当前uid的plan(预期应该是enterprise)
	usrPlan, err := s.dao.UserPlanDao.GetCurrentPlan(ctx, uid, month)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"month": month,
		}).WithError(err).Error("get currentPlan failed")
		return err
	}
	if usrPlan.Level != enums.LevelEnterprise {
		return errors.New("current plan is not enterprise")
	}
	// 查询出当前在线的free版本plan
	freePlan, err := s.dao.PlanDao.GetOnlineByLevel(ctx, enums.LevelFree)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"level": enums.LevelFree,
		}).WithError(err).Error("get online plan failed")
		return err
	}
	// 订阅回 free 版本
	err = s.AdminSubscribePlan(
		ctx,
		&models.SubscribePlanParam{
			Uid:          uid,
			Level:        enums.LevelFree,
			PackCode:     freePlan.Packs.Packs[0].Code,
			EffectType:   enums.EffectTypeImmediately,
			SubscribeSrc: enums.SubscribeSrcAdmin,
		},
		epoch,
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"plan":  freePlan,
			"epoch": epoch,
		}).WithError(err).Error("admin subscribe plan failed")
		return err
	}
	return nil
}

// SwitchPack 切换套餐
func (s *PlanService) SwitchPack(
	ctx context.Context,
	uid uint64,
	packCode string,
	epoch time.Time,
) error {
	logger := logging.GetLogger(ctx)
	month := base.ThisMonth(epoch)
	userPlan, plan, err := s.GetUserCurrentPlan(ctx, uid, month)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"month": month,
		}).WithError(err).Error("get user current plan failed")
		return err
	}
	if userPlan.RemainingSwitchCount == 0 {
		err = errors.New("switching restricted")
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"month": month,
			"pack":  packCode,
		}).WithError(err).Error("get user current plan failed")
		return err
	}

	found := false
	for _, pack := range plan.Packs.Packs {
		if pack.Code == packCode {
			found = true
			break
		}
	}
	if !found {
		// 当前用户的 plan中 packs 配置已发生变化
		// 则切换成该套餐下的第一个套餐
		packCode = plan.Packs.Packs[0].Code
	}

	// 复用 adminSubscribePlan 里面的解绑权益、重新订阅的逻辑
	err = s.AdminSubscribePlan(
		ctx, &models.SubscribePlanParam{
			Uid:          uid,
			Level:        plan.Level,
			PackCode:     packCode,
			EffectType:   enums.EffectTypeImmediately,
			SubscribeSrc: enums.SubscribeSrcCustomer,
		},
		epoch,
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"pack":  packCode,
			"epoch": epoch,
		}).WithError(err).Error("admin subscribe plan failed")
		return err
	}
	return nil
}
