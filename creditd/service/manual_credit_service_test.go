package service_test

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
	"qiniu.io/pay/creditd/service"
)

func TestManualCreditService_CreateCredit(t *testing.T) {
	sandbox := newCreditSrvSandbox(t)
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	creditIn := model.ManualCredit{
		UID:          1,
		CustomerName: "test create credit",
		CreditLine:   100,
		CreditPeriod: 10,
		PeriodUnit:   enums.PeriodUnitDay,
		Status:       enums.StatusUnActivated,
		Remark:       "test",
	}

	creditOut, isUpdateCreditLine, err := sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}
	assert.NoError(t, err)
	assert.True(t, isUpdateCreditLine)
	assert.Equal(t, creditIn.UID, creditOut.UID)
	assert.Equal(t, creditIn.CreditLine, creditOut.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut.PeriodUnit)
	assert.Equal(t, creditIn.Status, creditOut.Status)
	assert.NotEqual(t, 1000, creditOut.InitAt.Year())
	assert.NotEqual(t, 0, creditOut.ID)
	assert.Equal(t, base.CurrencyTypeCNY.String(), creditOut.CurrencyType)

	creditIn.CreditLine += 1
	creditOut, isUpdateCreditLine, err = sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}
	assert.NoError(t, err)
	assert.True(t, isUpdateCreditLine)
	assert.Equal(t, creditIn.UID, creditOut.UID)
	assert.Equal(t, creditIn.CreditLine, creditOut.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut.PeriodUnit)
	assert.NotEqual(t, 1000, creditOut.InitAt.Year())
	assert.NotEqual(t, 0, creditOut.ID)

	creditIn.CreditPeriod = 244
	creditOut, isUpdateCreditLine, err = sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}
	assert.NoError(t, err)
	assert.False(t, isUpdateCreditLine)
	assert.Equal(t, creditIn.UID, creditOut.UID)
	assert.Equal(t, creditIn.CreditLine, creditOut.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut.PeriodUnit)
	assert.NotEqual(t, 1000, creditOut.InitAt.Year())
	assert.NotEqual(t, 0, creditOut.ID)
}

func TestManualCreditService_CreditCredit_UserWithCard(t *testing.T) {
	ctx := tz.WithRefLocation(context.Background(), time.UTC)
	sandbox := newCreditSrvSandbox(t, withUserCard(true), withCurrencyType(base.CurrencyTypeUSD))
	creditIn := model.ManualCredit{
		UID:          1,
		CustomerName: "test create credit",
		CreditLine:   100,
		CreditPeriod: 10,
		PeriodUnit:   enums.PeriodUnitDay,
		Status:       enums.StatusUnActivated,
		Remark:       "test",
	}
	creditOut, isUpdateCreditLine, err := sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}
	assert.NoError(t, err)
	assert.True(t, isUpdateCreditLine)
	assert.Equal(t, creditIn.UID, creditOut.UID)
	assert.Equal(t, creditIn.CreditLine, creditOut.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut.PeriodUnit)
	// 用户已绑卡时，人工授信状态默认为已激活
	assert.Equal(t, enums.StatusValid, creditOut.Status)
	assert.NotEqual(t, 1000, creditOut.InitAt.Year())
	assert.NotEqual(t, 0, creditOut.ID)
	// 绑卡用户的人工授信默认为美元
	assert.Equal(t, base.CurrencyTypeUSD.String(), creditOut.CurrencyType)

}

func TestManualCreditService_CreateCreditParallel(t *testing.T) {
	sandbox := newCreditSrvSandbox(t)
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	creditIn := model.ManualCredit{
		UID:          1,
		CustomerName: "test create credit",
		CreditLine:   100,
		CreditPeriod: 10,
		PeriodUnit:   enums.PeriodUnitDay,
		Status:       enums.StatusUnActivated,
		Remark:       "test",
	}

	creditOut, _, err := sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}

	// 判断新插入到数据库的数据和待插入的原始值是否一致
	assert.NoError(t, err)
	assert.Equal(t, creditIn.UID, creditOut.UID)
	assert.Equal(t, creditIn.CreditLine, creditOut.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut.PeriodUnit)
	assert.NotEqual(t, 1000, creditOut.InitAt.Year())
	assert.NotEqual(t, 0, creditOut.ID)

	// 构造测试并发插入用到的数据
	tests := []struct {
		name  string
		value model.ManualCredit
	}{
		// 后一次插入会把前一次的 unactivated 设置为 unauthorized
		{
			name: "test case 1",
			value: model.ManualCredit{UID: 1,
				CustomerName: "test create credit 1",
				CreditLine:   100,
				CreditPeriod: 10,
				PeriodUnit:   enums.PeriodUnitDay,
				Status:       enums.StatusUnActivated,
				Remark:       "test",
			},
		},
		{
			name: "test case 2",
			value: model.ManualCredit{UID: 1,
				CustomerName: "test create credit 2",
				CreditLine:   100,
				CreditPeriod: 10,
				PeriodUnit:   enums.PeriodUnitDay,
				Status:       enums.StatusUnActivated,
				Remark:       "test",
			},
		},
	}

	var wg sync.WaitGroup
	for _, tc := range tests {
		wg.Add(1)
		go func(m model.ManualCredit) {
			defer wg.Done()
			_, _, err := sandbox.creditSrv.ManualCredit.CreateCredit(ctx, m)
			if err != nil {
				panic(err)
			}
		}(tc.value)

	}
	wg.Wait()

	credits, _, err := sandbox.creditSrv.ManualCredit.GetCreditsByUID(ctx, &service.GetCreditsByUIDParams{
		UID:    1,
		Status: []enums.CreditStatus{enums.StatusUnAuthorized, enums.StatusUnActivated},
		Offset: 0,
		Limit:  100})
	if err != nil {
		t.Fatal(err)
	}

	if assert.Equal(t, 3, len(credits)) {
		unactivatedCount := 0
		unauthorizedCount := 0
		for _, credit := range credits {
			if credit.Status == enums.StatusUnActivated {
				unactivatedCount++
			}
			if credit.Status == enums.StatusUnAuthorized {
				unauthorizedCount++
			}
		}
		for _, credit := range credits {
			t.Log(credit)
		}
		assert.Equal(t, 1, unactivatedCount)
		assert.Equal(t, 2, unauthorizedCount)
	}

	activatedCredit, err := sandbox.creditSrv.ManualCredit.UpdateCreditStatusByUID(ctx,
		1, enums.EventActivate)

	if err != nil {
		t.Fatal(err)
	}

	assert.Equal(t, enums.StatusValid, activatedCredit.Status)

	creditInNew := model.ManualCredit{
		UID:          1,
		CustomerName: "test create credit",
		CreditLine:   234,
		CreditPeriod: 11,
		PeriodUnit:   enums.PeriodUnitMonth,
		Status:       enums.StatusUnActivated,
		Remark:       "",
	}

	// 插入新的一条，那么原来的一条状态改为 revoked，新插入一条状态同为 activated 的条目
	_, _, err = sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditInNew)
	if err != nil {
		t.Fatal(err)
	}

	statusParam := []enums.CreditStatus{enums.StatusUnAuthorized,
		enums.StatusUnActivated,
		enums.StatusValid,
		enums.StatusRevoked}
	credits, _, err = sandbox.creditSrv.ManualCredit.GetCreditsByUID(
		ctx,
		&service.GetCreditsByUIDParams{
			UID:    1,
			Status: statusParam,
			Offset: 0,
			Limit:  100})
	if err != nil {
		t.Fatal(err)
	}

	if assert.Equal(t, 4, len(credits)) {
		unactivatedCount := 0
		unauthorizedCount := 0
		revokedCount := 0
		validCount := 0
		for _, credit := range credits {
			if credit.Status == enums.StatusUnActivated {
				unactivatedCount++
			}
			if credit.Status == enums.StatusUnAuthorized {
				unauthorizedCount++
			}
			if credit.Status == enums.StatusRevoked {
				revokedCount++
			}
			if credit.Status == enums.StatusValid {
				validCount++
			}
		}
		for _, credit := range credits {
			t.Log(credit)
		}
		assert.Equal(t, 0, unactivatedCount)
		assert.Equal(t, 2, unauthorizedCount)
		assert.Equal(t, 1, revokedCount)
		assert.Equal(t, 1, validCount)
	}

	// 将有效的 credit 条目 revoke 掉
	revokedCredit, err := sandbox.creditSrv.ManualCredit.UpdateCreditStatusByUID(ctx,
		1, enums.EventRevoke)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, enums.StatusRevoked, revokedCredit.Status)
	assert.Equal(t, uint64(1), revokedCredit.UID)

	_, _, err = sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditInNew)
	if err != nil {
		t.Log(creditInNew)
		t.Fatal(err)
	}

	credits, _, err = sandbox.creditSrv.ManualCredit.GetCreditsByUID(
		ctx,
		&service.GetCreditsByUIDParams{
			UID:    1,
			Status: statusParam,
			Offset: 0,
			Limit:  100})
	if err != nil {
		t.Fatal(err)
	}

	if assert.Equal(t, 5, len(credits)) {
		unactivatedCount := 0
		unauthorizedCount := 0
		revokedCount := 0
		validCount := 0
		for _, credit := range credits {
			if credit.Status == enums.StatusUnActivated {
				unactivatedCount++
			}
			if credit.Status == enums.StatusUnAuthorized {
				unauthorizedCount++
			}
			if credit.Status == enums.StatusRevoked {
				revokedCount++
			}
			if credit.Status == enums.StatusValid {
				validCount++
			}
		}
		for _, credit := range credits {
			t.Log(credit)
		}
		assert.Equal(t, 1, unactivatedCount)
		assert.Equal(t, 2, unauthorizedCount)
		assert.Equal(t, 2, revokedCount)
		assert.Equal(t, 0, validCount)
	}
}

func TestManualCreditService_UpdateCreditLineByID(t *testing.T) {
	sandbox := newCreditSrvSandbox(t)
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	creditIn := model.ManualCredit{
		UID:          1,
		CustomerName: "test create credit",
		CreditLine:   100,
		CreditPeriod: 10,
		PeriodUnit:   enums.PeriodUnitDay,
		Status:       enums.StatusUnActivated,
		Remark:       "test",
	}
	creditOut1, _, err := sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}

	creditOut2, err := sandbox.creditSrv.ManualCredit.UpdateCreditLineByID(ctx, creditOut1.ID, 120)
	if err != nil {
		t.Fatal(err)
	}
	assert.NoError(t, err)
	assert.Equal(t, creditIn.UID, creditOut2.UID)
	assert.Equal(t, int64(120), creditOut2.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut2.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut2.PeriodUnit)
	assert.NotEqual(t, 1000, creditOut2.InitAt.Year())
	assert.Equal(t, creditOut1.ID, creditOut2.ID)
}

func TestManualCreditService_UpdateCreditPeriodByID(t *testing.T) {
	sandbox := newCreditSrvSandbox(t)
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	creditIn := model.ManualCredit{
		UID:          1,
		CustomerName: "test create credit",
		CreditLine:   100,
		CreditPeriod: 10,
		PeriodUnit:   enums.PeriodUnitDay,
		Status:       enums.StatusUnActivated,
		Remark:       "test",
	}
	creditOut1, _, err := sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}

	creditOut2, err := sandbox.creditSrv.ManualCredit.UpdateCreditPeriodByID(ctx,
		creditOut1.ID,
		30,
		enums.PeriodUnitDay)
	if err != nil {
		t.Fatal(err)
	}

	assert.NoError(t, err)
	assert.Equal(t, creditIn.UID, creditOut2.UID)
	assert.Equal(t, creditIn.CreditLine, creditOut2.CreditLine)
	assert.Equal(t, uint64(30), creditOut2.CreditPeriod)
	assert.Equal(t, enums.PeriodUnitDay, creditOut2.PeriodUnit)
	assert.NotEqual(t, 1000, creditOut2.InitAt.Year())
	assert.Equal(t, creditOut1.ID, creditOut2.ID)
}

func TestManualCreditService_UpdateCreditStatusByUID(t *testing.T) {
	sandbox := newCreditSrvSandbox(t)
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	creditIn := model.ManualCredit{
		UID:          1,
		CustomerName: "test create credit",
		CreditLine:   100,
		CreditPeriod: 10,
		PeriodUnit:   enums.PeriodUnitDay,
		Status:       enums.StatusUnActivated,
		Remark:       "test",
	}
	creditOut1, _, err := sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}

	creditOut2, err := sandbox.creditSrv.ManualCredit.UpdateCreditStatusByUID(ctx,
		creditOut1.ID,
		enums.EventAdjustLineOrPeriod)
	if err != nil {
		t.Fatal(err)
	}

	assert.NoError(t, err)
	assert.Equal(t, creditIn.UID, creditOut2.UID)
	assert.Equal(t, creditOut1.CreditLine, creditOut2.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut2.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut2.PeriodUnit)
	assert.NotEqual(t, 1000, creditOut2.InitAt.Year())
	assert.Equal(t, enums.StatusUnActivated, creditOut2.Status)
	assert.Equal(t, creditOut1.ID, creditOut2.ID)

	creditOut3, err := sandbox.creditSrv.ManualCredit.UpdateCreditStatusByUID(ctx,
		creditOut1.ID,
		enums.EventActivate)
	if err != nil {
		t.Fatal(err)
	}

	assert.NoError(t, err)
	assert.Equal(t, creditIn.UID, creditOut3.UID)
	assert.Equal(t, creditOut1.CreditLine, creditOut3.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut3.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut3.PeriodUnit)
	assert.NotEqual(t, 1000, creditOut3.InitAt.Year())
	assert.Equal(t, enums.StatusValid, creditOut3.Status)
	assert.Equal(t, creditOut1.ID, creditOut3.ID)
	assert.NotEqual(t, 1000, creditOut3.ActivateAt.Year())
	assert.NotEqual(t, 1000, creditOut3.InitAt.Year())
	assert.Equal(t, 1000, creditOut3.RevokeAt.Year())
	assert.Equal(t, 1000, creditOut3.CancelAt.Year())
	t.Log(creditOut3.ActivateAt)

	creditOut4, err := sandbox.creditSrv.ManualCredit.UpdateCreditStatusByUID(ctx,
		creditOut1.ID,
		enums.EventRevoke)
	if err != nil {
		t.Fatal(err)
	}

	assert.NoError(t, err)
	assert.Equal(t, creditIn.UID, creditOut4.UID)
	assert.Equal(t, creditOut1.CreditLine, creditOut4.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut4.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut4.PeriodUnit)
	assert.NotEqual(t, 1000, creditOut4.InitAt.Year())
	assert.Equal(t, enums.StatusRevoked, creditOut4.Status)
	assert.Equal(t, creditOut1.ID, creditOut4.ID)
	assert.NotEqual(t, 1000, creditOut4.ActivateAt.Year())
	assert.NotEqual(t, 1000, creditOut4.InitAt.Year())
	assert.NotEqual(t, 1000, creditOut4.RevokeAt.Year())
	assert.Equal(t, 1000, creditOut4.CancelAt.Year())

	_, _, err = sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}

	creditOut6, err := sandbox.creditSrv.ManualCredit.UpdateCreditStatusByUID(ctx,
		creditIn.UID,
		enums.EventCancel)
	if err != nil {
		t.Fatal(err)
	}

	assert.NoError(t, err)
	assert.Equal(t, creditIn.UID, creditOut6.UID)
	assert.Equal(t, creditOut1.CreditLine, creditOut6.CreditLine)
	assert.Equal(t, creditIn.CreditPeriod, creditOut6.CreditPeriod)
	assert.Equal(t, creditIn.PeriodUnit, creditOut6.PeriodUnit)
	assert.NotEqual(t, 1000, creditOut6.InitAt.Year())
	assert.Equal(t, enums.StatusUnAuthorized, creditOut6.Status)
	assert.NotEqual(t, creditOut1.ID, creditOut6.ID)
	assert.Equal(t, 1000, creditOut6.ActivateAt.Year())
	assert.NotEqual(t, 1000, creditOut6.InitAt.Year())
	assert.Equal(t, 1000, creditOut6.RevokeAt.Year())
	assert.NotEqual(t, 1000, creditOut6.CancelAt.Year())
}

func TestManualCreditService_GetCreditsByUID(t *testing.T) {
	sandbox := newCreditSrvSandbox(t)
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	creditIn := model.ManualCredit{
		UID:          1,
		CustomerName: "test create credit",
		CreditLine:   100,
		CreditPeriod: 10,
		PeriodUnit:   enums.PeriodUnitDay,
		Status:       enums.StatusUnActivated,
		Remark:       "test",
	}
	creditOut1, _, err := sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}

	params := &service.GetCreditsByUIDParams{
		UID:    1,
		Status: []enums.CreditStatus{enums.StatusUnActivated},
		Offset: 0,
		Limit:  10,
	}

	creditOut2, total, err := sandbox.creditSrv.ManualCredit.GetCreditsByUID(ctx, params)
	if err != nil {
		t.Fatal(err)
	}
	assert.NoError(t, err)
	assert.Equal(t, total, uint64(1), "查询到的该UID 未激活的信用额度应该为 1个")
	if len(creditOut2) == 1 {
		assert.Equal(t, creditIn.UID, creditOut2[0].UID)
		assert.Equal(t, creditOut1.ID, creditOut2[0].ID)
		assert.Equal(t, creditOut1.CreditLine, creditOut2[0].CreditLine)
		assert.Equal(t, uint64(10), creditOut2[0].CreditPeriod)
		assert.Equal(t, enums.PeriodUnitDay, creditOut2[0].PeriodUnit)
	} else {
		t.Fatal("GetByUID 结果为空")
	}
}

func TestManualCreditService_ListAllCredits(t *testing.T) {
	sandbox := newCreditSrvSandbox(t)
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	creditIn := model.ManualCredit{
		UID:          1,
		CustomerName: "test create credit",
		CreditLine:   100,
		CreditPeriod: 10,
		PeriodUnit:   enums.PeriodUnitDay,
		Status:       enums.StatusUnActivated,
		Remark:       "test",
	}
	creditOut1, _, err := sandbox.creditSrv.ManualCredit.CreateCredit(ctx, creditIn)
	if err != nil {
		t.Log(err)
	}

	params := &service.ListAllCreditsParam{
		Status: []enums.CreditStatus{enums.StatusUnActivated, enums.StatusUnAuthorized, enums.StatusRevoked, enums.StatusValid},
		Offset: 0,
		Limit:  10,
	}

	creditOut2, total, err := sandbox.creditSrv.ManualCredit.ListAllCredits(ctx, params)
	if err != nil {
		t.Fatal(err)
	}
	assert.NoError(t, err)
	assert.Equal(t, total, uint64(1), "查询到的所有信用额度应该为 1个")
	if len(creditOut2) == 1 {
		assert.Equal(t, creditIn.UID, creditOut2[0].UID)
		assert.Equal(t, creditOut1.ID, creditOut2[0].ID)
		assert.Equal(t, creditOut1.CreditLine, creditOut2[0].CreditLine)
		assert.Equal(t, uint64(10), creditOut2[0].CreditPeriod)
		assert.Equal(t, enums.PeriodUnitDay, creditOut2[0].PeriodUnit)
	} else {
		t.Fatal("ListAllCredits 结果为空")
	}
}
