package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/service"
)

// BuildPbConsumeCouponResp converts service.ConsumeCouponResp to the pb layer type.
func BuildPbConsumeCouponResp(
	in *service.ConsumeCouponResp,
) (*pb.ConsumeCouponResp, error) {
	return &pb.ConsumeCouponResp{
		Ok:           in.OK,
		ReducedMoney: in.ReducedMoney.ToInt64(),
		Err:          pb.ConsumeCouponError(in.Err),
	}, nil
}
