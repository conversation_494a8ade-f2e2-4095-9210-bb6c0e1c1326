package enums

// PlanLevel plan level
type PlanLevel string

const (
	LevelFree       PlanLevel = "free"
	LevelPro        PlanLevel = "pro"
	LevelBusiness   PlanLevel = "business"
	LevelEnterprise PlanLevel = "enterprise"
)

func (l PlanLevel) String() string {
	return string(l)
}

func (l PlanLevel) Valid() bool {
	switch l {
	case LevelFree, LevelPro, LevelBusiness, LevelEnterprise:
		return true
	default:
		return false
	}
}
func (l PlanLevel) weight() int {
	switch l {
	case LevelFree:
		return 0
	case LevelPro:
		return 1
	case LevelBusiness:
		return 2
	case LevelEnterprise:
		return 3
	default:
		return -1
	}
}

// Compare 判断 l -> targetLevel 是升级(1)、降级(-1)、还是平级(0)
func (l PlanLevel) Compare(targetLevel PlanLevel) int {
	if targetLevel.weight() > l.weight() {
		return 1
	}
	if targetLevel.weight() == l.weight() {
		return 0
	}
	return -1
}
