package adapter

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/payment/model"
)

// BuildUserPaymentSeq converts pb UserPaymentSeq to model UserPaymentSeq
func BuildUserPaymentSeq(pbPaymentSeq *pb.PaymentSeq) *model.UserPaymentSeq {
	if pbPaymentSeq == nil {
		return nil
	}
	return &model.UserPaymentSeq{
		UID:                  pbPaymentSeq.GetUid(),
		SeqType:              enums.PaymentSeqType(pbPaymentSeq.GetSeqType()),
		Operator:             pbPaymentSeq.GetOperator(),
		ShouldImmediatelyPay: &pbPaymentSeq.ShouldImmediatelyPay,
	}
}

// BuildPbUserPaymentSeq converts model UserPaymentSeq to model UserPaymentSeq
func BuildPbUserPaymentSeq(userPaymentSeq *model.UserPaymentSeq) (*pb.PaymentSeq, error) {
	if userPaymentSeq == nil {
		return nil, nil
	}

	var configAt *timestamppb.Timestamp
	if userPaymentSeq.ConfigAt != nil {
		configAt = timestamppb.New(*userPaymentSeq.ConfigAt)
	}

	return &pb.PaymentSeq{
		Uid:                  userPaymentSeq.UID,
		SeqType:              pb.PaymentSeqType(userPaymentSeq.SeqType),
		ConfigAt:             configAt,
		Operator:             userPaymentSeq.Operator,
		ShouldImmediatelyPay: *userPaymentSeq.ShouldImmediatelyPay,
	}, nil
}

// BuildPbUserPaymentSeqList builds pb.PaymentSeq List
func BuildPbUserPaymentSeqList(userPaymentSeqs []*model.UserPaymentSeq) ([]*pb.PaymentSeq, error) {
	if len(userPaymentSeqs) == 0 {
		return nil, nil
	}
	pbPaymentSeqList := make([]*pb.PaymentSeq, len(userPaymentSeqs))
	for i, userPaymentSeq := range userPaymentSeqs {
		pbPaymentSeq, err := BuildPbUserPaymentSeq(userPaymentSeq)
		if err != nil {
			return nil, err
		}
		pbPaymentSeqList[i] = pbPaymentSeq
	}
	return pbPaymentSeqList, nil
}

// BuildPbUserPaymentSeqHistory converts model UserPaymentSeqHistory to model UserPaymentSeq
func BuildPbUserPaymentSeqHistory(userPaymentSeqHistory *model.UserPaymentSeqHistory) (*pb.PaymentSeq, error) {
	if userPaymentSeqHistory == nil {
		return nil, nil
	}

	var configAt *timestamppb.Timestamp
	if userPaymentSeqHistory.ConfigAt != nil {
		configAt = timestamppb.New(*userPaymentSeqHistory.ConfigAt)
	}

	return &pb.PaymentSeq{
		Uid:                  userPaymentSeqHistory.UID,
		SeqType:              pb.PaymentSeqType(userPaymentSeqHistory.SeqType),
		ConfigAt:             configAt,
		Operator:             userPaymentSeqHistory.Operator,
		ShouldImmediatelyPay: *userPaymentSeqHistory.ShouldImmediatelyPay,
	}, nil
}

// BuildPbUserPaymentSeqHistoryList builds pb.PaymentSeq List
func BuildPbUserPaymentSeqHistoryList(userPaymentSeqHistories []*model.UserPaymentSeqHistory) ([]*pb.PaymentSeq, error) {
	if len(userPaymentSeqHistories) == 0 {
		return nil, nil
	}
	pbPaymentSeqList := make([]*pb.PaymentSeq, len(userPaymentSeqHistories))
	for i, userPaymentSeq := range userPaymentSeqHistories {
		pbPaymentSeq, err := BuildPbUserPaymentSeqHistory(userPaymentSeq)
		if err != nil {
			return nil, err
		}

		pbPaymentSeqList[i] = pbPaymentSeq
	}
	return pbPaymentSeqList, nil
}
