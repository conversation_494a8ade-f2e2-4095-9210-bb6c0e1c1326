package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/dictd/model"
)

func TestItemDataTypeUnitRoundTrip(t *testing.T) {
	x := &model.ItemDataTypeUnit{
		ID:            233,
		DataTypeID:    234,
		Name:          "test",
		Power:         3,
		IsPriceAnchor: true,
		CreatedAt:     time.Now().Round(0),
		UpdatedAt:     time.Now().Round(0),
	}

	y, err := BuildPbItemDataTypeUnit(x)
	assert.NoError(t, err)

	z, err := BuildItemDataTypeUnit(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.DataTypeID, z.DataTypeID)
	assert.Equal(t, x.Name, z.Name)
	assert.Equal(t, x.Power, z.Power)
	assert.Equal(t, x.IsPriceAnchor, z.IsPriceAnchor)
	assert.Equal(t, x.CreatedAt.<PERSON><PERSON><PERSON>(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.<PERSON>ano(), z.UpdatedAt.Unix<PERSON>ano())
}
