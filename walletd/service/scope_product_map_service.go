package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/walletd/model"
)

// GetScopeProductMapByID 根据ID获取计费作用范围-产品关联关系
func (s *WalletBizService) GetScopeProductMapByID(
	ctx context.Context,
	id uint64,
) (*model.ScopeProductMap, error) {
	return s.walletDao.ScopeProductMap.GetByID(id, s.cacheExpires)
}

// ListAllScopeProductMaps 分页获取所有的计费范围-产品关系
func (s *WalletBizService) ListAllScopeProductMaps(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.ScopeProductMap, error) {
	return s.walletDao.ScopeProductMap.ListAll(offset, limit, s.cacheExpires)
}

// CountAllScopeProductMaps 获取所有计费范围-产品关系数量
func (s *WalletBizService) CountAllScopeProductMaps(
	ctx context.Context,
) (uint64, error) {
	return s.walletDao.ScopeProductMap.CountAll(s.cacheExpires)
}

// ListScopeProductMapsByScopeID 根据计费项作用范围ID列出所有计费项作用范围和产品的关联关系
func (s *WalletBizService) ListScopeProductMapsByScopeID(
	ctx context.Context,
	scopeID uint64,
	offset int,
	limit int,
) ([]model.ScopeProductMap, error) {
	return s.walletDao.ScopeProductMap.ListByScopeID(scopeID, offset, limit, s.cacheExpires)
}

// CountScopeProductMapsByScopeID 根据计费范围ID获取计费范围-产品关系数量
func (s *WalletBizService) CountScopeProductMapsByScopeID(
	ctx context.Context,
	scopeID uint64,
) (uint64, error) {
	return s.walletDao.ScopeProductMap.CountByScopeID(scopeID, s.cacheExpires)
}

// ListScopeProductMapsByProductID 根据产品ID列出所有计费项作用范围和产品的关联关系
func (s *WalletBizService) ListScopeProductMapsByProductID(
	ctx context.Context,
	productID uint64,
	offset int,
	limit int,
) ([]model.ScopeProductMap, error) {
	return s.walletDao.ScopeProductMap.ListByProductID(productID, offset, limit, s.cacheExpires)
}

// CountScopeProductMapsByProductID 根据产品ID获取计费范围-产品关系数量
func (s *WalletBizService) CountScopeProductMapsByProductID(
	ctx context.Context,
	productID uint64,
) (uint64, error) {
	return s.walletDao.ScopeProductMap.CountByProductID(productID, s.cacheExpires)
}

// CreateScopeProductMap 创建计费作用范围-产品关联关系
func (s *WalletBizService) CreateScopeProductMap(
	ctx context.Context,
	_map *model.ScopeProductMap,
) (*model.ScopeProductMap, error) {
	_map.ID = 0
	err := s.walletDao.ScopeProductMap.Save(_map, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "save scope_product_map failed").
			WithField("scope_product_map", _map)
	}
	return _map, nil
}

// UpdateScopeProductMapByID 根据ID更新计费范围-产品关联关系
func (s *WalletBizService) UpdateScopeProductMapByID(
	ctx context.Context,
	id uint64,
	_map *model.ScopeProductMap,
) (*model.ScopeProductMap, error) {
	_map.ID = id
	err := s.walletDao.ScopeProductMap.Save(_map, s.cacheExpires)
	return _map, err
}

// DeleteScopeProductMapByID 根据ID删除计费范围-产品关联关系
func (s *WalletBizService) DeleteScopeProductMapByID(
	ctx context.Context,
	id uint64,
) (*model.ScopeProductMap, error) {
	_map, err := s.GetScopeProductMapByID(ctx, id)
	if err != nil {
		return nil, errors.Annotate(err, "query scope_product_map failed").WithField("id", id)
	}
	err = s.walletDao.ScopeProductMap.Delete(_map)
	return _map, err
}

// DeleteScopeProductMapsByScopeID 根据计费项作用范围ID删除所有和计费项作用范围和产品的关联关系
func (s *WalletBizService) DeleteScopeProductMapsByScopeID(
	ctx context.Context,
	scopeID uint64,
) (result []model.ScopeProductMap, err error) {
	offset, limit := 0, 100
	for {
		maps, err := s.ListScopeProductMapsByScopeID(ctx, scopeID, offset, limit)
		if err != nil {
			return nil, errors.Annotate(err, "list scope_product_map failed").
				WithField("scope_id", scopeID)
		}
		for _, _map := range maps {
			err = s.walletDao.ScopeProductMap.Delete(&_map)
			if err != nil {
				return nil, err
			}
		}
		result = append(result, maps...)
		if len(maps) < limit {
			break
		}
		offset += limit
	}
	return
}

func (s *WalletBizService) ListScopeIDByProductIDs(
	ctx context.Context,
	productIDs []uint64,
) ([]uint64, error) {
	logger := logging.GetLogger(ctx)
	productIDs, err := s.walletDao.ScopeProductMap.ListScopeIDByProductIDs(productIDs)
	if err != nil {
		logger.Errorf("list scope id by product ids failed. err: %v productIDs: %v", err, productIDs)
		return nil, errors.Annotate(err, "list scope id by product ids failed").
			WithField("productIDs", productIDs)
	}
	return productIDs, err
}

// CalculateUserProductVoucherQuota 计算用户指定产品线和 code 的账单抵用券总金额
func (s *WalletBizService) CalculateUserProductVoucherQuota(
	ctx context.Context,
	uid uint64,
	codes []string,
	productIDs []uint64,
) (model.ProductIDVoucherQuotaList, error) {
	logger := logging.GetLogger(ctx)
	productQuotas, err := s.walletDao.ScopeProductMap.CalculateUserProductVoucherQuota(uid, codes, productIDs)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":        uid,
			"codes":      codes,
			"productIDs": productIDs,
		}).WithError(err).Error("calculate user product voucher total quota failed.")
		return nil, err
	}
	return productQuotas, nil
}
