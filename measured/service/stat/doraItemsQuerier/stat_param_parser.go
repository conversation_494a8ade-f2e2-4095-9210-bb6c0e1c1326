package doraItemsQuerier

import (
	"net/url"
	"regexp"

	"github.com/qbox/bo-base/v4/legacystat/common"
)

// Dora 计费项拉量参数 Query 字符串中用来表示 cmd 的 key
const doraCmdQueryKey = "cmd"

var bo16940KludgeRE = regexp.MustCompile(`:\d+$`)

// parseCmdsFromStatParams 将 Dora 计费项的拉量参数解析为 Dora cmd 列表
//
// - statKey == "merge" 则 statQuery 中的每个 cmd 即为预期格式的 LegacyDoraCmd 表示形式
// - 其他情况，则 statQuery 中的每个 cmd 拼接上 statKey 为要求的 LegacyDoraCmd
func parseCmdsFromStatParams(
	statKey string,
	statQuery string,
) ([]common.LegacyDoraCmd, error) {
	parsedQuery, err := url.ParseQuery(statQuery)
	if err != nil {
		return nil, err
	}
	cmds := parsedQuery[doraCmdQueryKey]
	if len(cmds) == 0 {
		return nil, nil
	}

	result := make([]common.LegacyDoraCmd, len(cmds))
	switch statKey {
	case "merge":
		// 每个 cmd 字符串都是预期的 key:cmd 形式
		for i, cmdStr := range cmds {
			cmd, err := common.ParseLegacyDoraCmd(cmdStr)
			if err != nil {
				return nil, err
			}

			// XXX 解决 BO-16940 的 bug 正常应该改上面的 parse 函数，但现在是个事故，时间紧，先止损
			// 先在此处 workaround 掉，之后把这里的逻辑挪到 parse 函数里
			cmd.Cmd = bo16940KludgeRE.ReplaceAllString(cmd.Cmd, "")

			result[i] = cmd
		}

		return result, nil

	default:
		// 所有 cmd 的 key 都一样，直接构造对象
		for i, cmd := range cmds {
			result[i] = common.LegacyDoraCmd{
				Key: statKey,
				// XXX 解决 BO-16940 的 bug 正常应该改上面的 parse 函数，但现在是个事故，时间紧，先止损
				// 先在此处 workaround 掉，之后把这里的逻辑挪到 parse 函数里
				Cmd: bo16940KludgeRE.ReplaceAllString(cmd, ""),
			}
		}

		return result, nil
	}
}
