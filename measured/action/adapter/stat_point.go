package adapter

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
)

// BuildPbStatPointV1 converts from model.StatPoint to the protobuf type.
func BuildPbStatPointV1(x model.StatPoint) (*pb.StatPointV1, error) {
	return &pb.StatPointV1{
		Time:  timestamppb.New(x.Time),
		Value: uint64(x.Value),
	}, nil
}

// BuildModelStatPointV1 converts from pb.StatPointV1 to the model layer type.
func BuildModelStatPointV1(x *pb.StatPointV1) (model.StatPoint, error) {
	err := x.Time.CheckValid()
	if err != nil {
		return model.StatPoint{}, err
	}
	t := x.Time.AsTime()

	return model.StatPoint{
		Time:  t,
		Value: int64(x.Value),
	}, nil
}
