package action_test

import (
	"context"
	"io"
	"log"
	"net"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"google.golang.org/grpc/keepalive"

	"github.com/golang/mock/gomock"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/bo-base/v4/test"
	"github.com/qbox/pay-sdk/base/account"
	pb "github.com/qbox/pay-sdk/credit"
	"github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/gaea/client/operations"
	"github.com/qbox/pay-sdk/gaea/models"
	gaeaMock "github.com/qbox/pay-sdk/mocks/gaea"
	mockWallet "github.com/qbox/pay-sdk/mocks/wallet"
	walletpb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/creditd/action"
	"qiniu.io/pay/creditd/config"
	creditDao "qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/service"
	"qiniu.io/pay/i18n/intlmockhelper"
)

type sandbox struct {
	testWrap         *test.Wrap
	creditServer     *grpc.Server
	creditClientConn *grpc.ClientConn
	creditClient     pb.PayCreditServiceClient
	uidInfoServer    *httptest.Server
	salesInfoServer  *httptest.Server
	mailServer       *httptest.Server
	creditAction     *action.CreditAction

	listener net.Listener

	cardBound    bool
	currencyType base.CurrencyType
}

type sandboxOption func(sandbox *sandbox)

func withCurrencyType(currency base.CurrencyType) sandboxOption {
	return func(sandbox *sandbox) {
		sandbox.currencyType = currency
	}
}

func withUserCard(hasCard bool) sandboxOption {
	return func(sandbox *sandbox) {
		sandbox.cardBound = hasCard
	}
}

func getMockGaeaService(t *testing.T) operations.ClientService {
	ctrl := gomock.NewController(t)
	mockGaeaService := gaeaMock.NewMockClientService(ctrl)
	mockGaeaService.
		EXPECT().
		GetUserBillBriefList(gomock.Any()).
		Return(&operations.GetUserBillBriefListOK{
			Payload: &models.UserBillBriefListResp{
				Code: http.StatusOK,
				Data: &models.UserBillBriefListRespData{
					Summary: false,
					Bills: []*models.BillBrief{
						{Money: 100000},
						{Money: 200000},
						{Money: 500000},
					},
				},
				Message: "",
			}}, nil,
		).AnyTimes()
	mockGaeaService.
		EXPECT().
		GetDeveloper(gomock.Any()).
		AnyTimes().
		DoAndReturn(func(param *operations.GetDeveloperParams, opts ...any) (*operations.GetDeveloperOK, error) {
			uid, _ := strconv.Atoi(param.UID)
			return &operations.GetDeveloperOK{
				Payload: &models.GetDeveloperResp{
					Code: http.StatusOK,
					Data: &models.UserInfo{
						UID:         uint32(uid),
						Fullname:    "Rufus Shinra",
						CompanyName: "Shinra Electric Power Company",
					},
				},
			}, nil
		})
	mockGaeaService.
		EXPECT().
		TriggerFreeze(gomock.Any()).AnyTimes().
		Return(&operations.TriggerFreezeOK{
			Payload: &models.TriggerFreezeResp{
				Code:    http.StatusOK,
				Message: "",
			},
		}, nil)
	return mockGaeaService
}

func buildSandbox(t *testing.T, options ...sandboxOption) *sandbox {
	ctrl := gomock.NewController(t)
	// no defer needed on go1.14+

	testWrap, err := test.NewTestWrap(
		t,
		test.WithMigrateFuncs(creditDao.RegisterMigrate, creditDao.RegisterMigrateNotice),
	)
	if err != nil {
		t.Fatal("NewTestWrap in creditd action return error", err)
	}

	cDao := creditDao.NewCreditDao(testWrap.BaseDao())

	uidInfoServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		io.WriteString(w, `{"data":{"email":"<EMAIL>","phoneNumber":"17702177777"}}`)
	}))

	salesInfoServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		io.WriteString(w, `{"data":{"email":"<EMAIL>","mobile":"***********"}}`)
	}))

	mailServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	accServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		io.WriteString(w, `{"code":200,"message":""}`)
	}))

	conf := &config.CreditdConfig{
		AccConfig: account.AccConfig{
			Host:     accServer.URL, // oauth2/token will be appended in roundTripper
			UserName: "root",
			Password: "root",
			ClientID: "abcd",
		},
		Services: config.ServiceConfig{
			GaeaAdmin: uidInfoServer.URL,
			Sofa:      salesInfoServer.URL,
			Morse:     mailServer.URL,
		},
		NoticeConfig: config.NoticeConfig{
			DelayedDuration: 48 * time.Hour,
		},
	}

	sandbox := &sandbox{
		testWrap:        testWrap,
		uidInfoServer:   uidInfoServer,
		salesInfoServer: salesInfoServer,
		mailServer:      mailServer,
	}

	for _, option := range options {
		option(sandbox)
	}

	mockPaymentServiceClient := mockWallet.NewMockPaymentServiceClient(ctrl)
	{
		mockPaymentServiceClient.EXPECT().GetSingleCurrency(
			gomock.Any(),
			gomock.Any(),
		).AnyTimes().DoAndReturn(func(ctx context.Context, req *walletpb.UIDParam, opts ...any) (*walletpb.Currency, error) {
			return &walletpb.Currency{
				Uid:          req.Uid,
				CurrencyType: sandbox.currencyType.String(),
			}, nil
		})
	}

	mockWalletServiceClient := mockWallet.NewMockPayWalletServiceClient(ctrl)
	{
		mockWalletServiceClient.EXPECT().ListUserCards(gomock.Any(), gomock.Any()).
			AnyTimes().
			DoAndReturn(func(ctx context.Context, req *walletpb.UIDParam, opts ...any) (*walletpb.UserCards, error) {
				result := &walletpb.UserCards{
					UserCards: make([]*walletpb.UserCard, 0),
				}
				if sandbox.cardBound {
					result.UserCards = []*walletpb.UserCard{
						{
							Uid:          req.GetUid(),
							IsDefault:    true,
							LastDigits:   "4698",
							Brand:        "VISA",
							CardType:     walletpb.CardTypeCredit,
							CardStatus:   walletpb.CardStatusValid,
							PaymentToken: "b4a6c98k",
							CreatedAt:    timestamppb.Now(),
						},
					}
				}
				return result, nil
			})
	}

	l10nProvider := intlmockhelper.NewCommonMockL10nProvider(t)

	mockGaeaClient := &client.Gaea{Operations: getMockGaeaService(t)}

	creditSrv, err := service.NewCreditService(
		l10nProvider,
		cDao,
		mockPaymentServiceClient,
		mockWalletServiceClient,
		mockGaeaClient,
		100000,
		2000000,
		dao.CacheExpiresNoCache)
	if err != nil {
		t.Fatal(err)
	}

	nDao := creditDao.NewNoticeRecordDao(testWrap.BaseDao())
	notificationSrv, err := service.NewNotificationService(conf, cDao, nDao, mockPaymentServiceClient)
	if err != nil {
		t.Fatal(err)
	}

	aDao := creditDao.NewAvailableBalanceThresholdDao(testWrap.BaseDao())
	thresholdSrv, err := service.NewAvailableBalanceThresholdService(aDao, conf.Cache.DefaultExpires)
	if err != nil {
		t.Fatal(err)
	}

	freezeSrv, err := service.NewFreezeService(mockGaeaClient)
	if err != nil {
		t.Fatal(err)
	}

	creditAction := action.NewCreditAction(
		creditSrv,
		notificationSrv,
		thresholdSrv,
		freezeSrv,
		nil,
		10,
		conf.NoticeConfig.DelayedDuration)

	creditServer := grpc.NewServer()
	pb.RegisterPayCreditServiceServer(creditServer, creditAction)
	reflection.Register(creditServer)

	sandbox.creditServer = creditServer
	sandbox.creditAction = creditAction

	sandbox.initCreditServer(t)
	sandbox.initCreditClient(t)
	t.Cleanup(sandbox.cleanup)

	return sandbox
}

func (s *sandbox) initCreditServer(t *testing.T) {
	var err error
	s.listener, err = net.Listen("tcp", ":0")
	if err != nil {
		t.Fatalf("failed to listen: %v", err)
	}

	go func() {
		if err := s.creditServer.Serve(s.listener); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()
}

func (s *sandbox) initCreditClient(t *testing.T) {
	var err error
	s.creditClientConn, err = rpc.GrpcConnectWithName(s.listener.Addr().String(), rpc.ServicePayCredit, keepalive.ClientParameters{})
	if err != nil {
		t.Fatalf("did not connect: %v", err)
	}
	s.creditClient = pb.NewPayCreditServiceClient(s.creditClientConn)
}

func (s *sandbox) cleanup() {
	if s.creditClientConn != nil {
		s.creditClientConn.Close()
	}
	if s.creditServer != nil {
		s.creditServer.Stop()
	}
	s.uidInfoServer.Close()
	s.salesInfoServer.Close()
	s.mailServer.Close()
}
