package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/qvmprice"
	"google.golang.org/protobuf/types/known/emptypb"
	"qiniu.io/pay/qvmprice/action/adapter"
)

// QueryItemPriceForUID 以给定的时刻作为当前时刻，查询给定的 UID 某个计费项在某个区域的当前生效报价（“询价”）
func (a *QVMPriceAction) QueryItemPriceForUID(
	ctx context.Context,
	req *pb.QueryItemPriceParam,
) (*pb.QVMPriceItemDetail, error) {
	resp, err := a.srv.QueryItemPriceForUID(
		ctx,
		req.Uid,
		req.ItemCode,
		int64(req.ZoneCode),
		req.CurrencyType,
		req.When.AsTime(),
		req.Epoch.AsTime(),
	)
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbQVMPriceItemDetail(resp)
}

// BatchQueryItemPricesForUID 以给定的时刻作为当前时刻，查询给定的 UID 某些计费项的当前生效报价
func (a *QVMPriceAction) BatchQueryItemPricesForUID(
	ctx context.Context,
	req *pb.BatchQueryItemPricesForUIDParam,
) (*pb.QVMPriceItemDetailList, error) {
	resp, err := a.srv.QueryItemPricesForUID(
		ctx,
		req.Uid,
		adapter.BuildServiceItemSpecs(req.ItemSpecs),
		req.CurrencyType,
		req.When.AsTime(),
		req.Epoch.AsTime(),
	)
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbQVMPriceItemDetailList(resp)
}

// BatchQueryPublicPrices 以给定的时刻作为当前时刻，查询某些计费项的公开报价
func (a *QVMPriceAction) BatchQueryPublicPrices(
	ctx context.Context,
	req *pb.BatchQueryPublicPricesParam,
) (*pb.QVMPriceItemDetailList, error) {
	resp, err := a.srv.QueryItemPricesForUID(
		ctx,
		0,
		adapter.BuildServiceItemSpecs(req.ItemSpecs),
		req.CurrencyType,
		req.When.AsTime(),
		req.Epoch.AsTime(),
	)
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbQVMPriceItemDetailList(resp)
}

// BatchQueryItemPricesWithTimeRange 以给定的时刻作为当前时刻，批量查询给定的 UID 某些计费项在给定时间范围的生效报价（时间范围批量询价，“出账接口”）
func (a *QVMPriceAction) BatchQueryItemPricesWithTimeRange(
	ctx context.Context,
	req *pb.BatchQueryItemPricesWithTimeRangeParam,
) (*pb.BatchQueryItemPricesWithTimeRangeResp, error) {
	resp, err := a.srv.QueryItemPricesForUIDWithTimeRange(
		ctx,
		req.Uid,
		adapter.BuildServiceItemSpecs(req.ItemSpecs),
		req.CurrencyType,
		req.Epoch.AsTime(),
		req.StartTime.AsTime(),
		req.EndTime.AsTime(),
	)
	if err != nil {
		return nil, err
	}

	data, err := adapter.BuildPbItemPricesResps(resp)
	if err != nil {
		return nil, err
	}

	return &pb.BatchQueryItemPricesWithTimeRangeResp{
		Data: data,
	}, nil
}

// ImportPublicPrices 导入一组 QVM 的公开报价
func (a *QVMPriceAction) ImportPublicPrices(
	ctx context.Context,
	req *pb.ImportPublicPricesParam,
) (*emptypb.Empty, error) {
	priceItems, err := adapter.BuildServiceQVMPriceItemDetails(req.PriceItems)
	if err != nil {
		return nil, err
	}

	err = a.srv.ImportPublicPrices(
		ctx,
		false,
		priceItems,
		req.Remark,
		req.Epoch.AsTime(),
	)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// CreatePriceTableForUID 为某个用户创建一张 QVM 报价单（“报价”）
func (a *QVMPriceAction) CreatePriceTableForUID(
	ctx context.Context,
	req *pb.CreateQVMPriceTableParam,
) (*emptypb.Empty, error) {
	priceItems, err := adapter.BuildServiceQVMPriceItemDetails(req.PriceItems)
	if err != nil {
		return nil, err
	}

	err = a.srv.CreatePriceTableForUID(
		ctx,
		req.Uid,
		req.InitiallyDisabled,
		priceItems,
		adapter.BuildServiceQVMGroupDiscountDetails(req.GroupDiscounts),
		req.Remark,
		req.Epoch.AsTime(),
	)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// TogglePriceTable 整体启用/禁用一张 QVM 报价单
func (a *QVMPriceAction) TogglePriceTable(
	ctx context.Context,
	req *pb.TogglePriceTableParam,
) (*emptypb.Empty, error) {
	err := a.srv.TogglePriceTable(
		ctx,
		req.PriceTableId,
		req.Enable,
	)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// HasVIPPriceWithTimeRange 检查给定时间段内用户是否有 VIP 报价
func (a *QVMPriceAction) HasVIPPriceWithTimeRange(
	ctx context.Context,
	req *pb.HasVIPPriceWithTimeRangeParam,
) (*pb.HasVIPPriceResp, error) {
	panic("TODO")
}
