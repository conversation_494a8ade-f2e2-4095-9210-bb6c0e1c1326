package dao

import (
	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/dao"

	"qiniu.io/pay/creditd/model"
)

type CreditDao struct {
	base                *dao.BaseDao
	ManualCredit        *ManualCreditDao
	SystemCredit        *SystemCreditDao
	SystemCreditHistory *SystemCreditHistoryDao
}

func NewCreditDao(
	base *dao.BaseDao,
) *CreditDao {
	return &CreditDao{
		base:                base,
		ManualCredit:        NewManualCreditDao(base),
		SystemCredit:        NewSystemCreditDao(base),
		SystemCreditHistory: NewSystemCreditHistoryDao(base),
	}
}

// RegisterMigrate migrate credit model
func RegisterMigrate(db *gorm.DB) {
	db.AutoMigrate(&model.ManualCredit{})
	db.AutoMigrate(&model.SystemCredit{})
	db.AutoMigrate(&model.SystemCreditHistory{})
}

// DoTransaction do a transaction
func (d *CreditDao) DoTransaction(fn func(*CreditDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		creditDao := NewCreditDao(base)
		return fn(creditDao)
	})
}
