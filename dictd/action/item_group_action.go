package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/dict"
	"qiniu.io/pay/dictd/action/adapter"
)

// GetItemGroupByID 根据ID获取计费项组
func (a *DictAction) GetItemGroupByID(
	ctx context.Context,
	itemGroupID *pb.IDParam,
) (*pb.ItemGroup, error) {
	itemGroup, err := a.dictBizSrv.GetItemGroupByID(ctx, itemGroupID.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemGroup(itemGroup)
}

// GetItemGroupByCode 根据 Code 获取计费项组
func (a *DictAction) GetItemGroupByCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.ItemGroup, error) {
	itemGroup, err := a.dictBizSrv.GetItemGroupByCode(ctx, param.Code)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemGroup(itemGroup)
}

// GetItemGroupByProductIDAndCode 根据产品ID和计费项组code获取计费项组
func (a *DictAction) GetItemGroupByProductIDAndCode(
	ctx context.Context,
	param *pb.IDCodeParam,
) (*pb.ItemGroup, error) {
	itemGroup, err := a.dictBizSrv.GetItemGroupByProductIDAndCode(ctx, param.GetId(), param.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemGroup(itemGroup)
}

// GetItemGroupByProductAndCode 根据产品 code 和计费项组 code 获取计费项组
func (a *DictAction) GetItemGroupByProductAndCode(
	ctx context.Context,
	param *pb.ProductCodeParam,
) (*pb.ItemGroup, error) {
	itemGroup, err := a.dictBizSrv.GetItemGroupByProductAndCode(ctx, param.GetProduct(), param.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemGroup(itemGroup)
}

// ListItemGroupsByProductID 根据产品ID获取产品的所有计费项组
func (a *DictAction) ListItemGroupsByProductID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ItemGroupList, error) {
	count, err := a.dictBizSrv.CountItemGroupsByProductID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	itemGroups, err := a.dictBizSrv.ListItemGroupsByProductID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemGroupList{
		Count:      count,
		ItemGroups: make([]*pb.ItemGroup, len(itemGroups)),
	}
	for i, itemGroup := range itemGroups {
		g, err := adapter.BuildPbItemGroup(&itemGroup)
		if err != nil {
			return nil, err
		}
		list.ItemGroups[i] = g
	}
	return list, nil
}

// ListItemGroupsByProductCode 根据产品 code 获取产品的所有计费项组
func (a *DictAction) ListItemGroupsByProductCode(
	ctx context.Context,
	param *pb.ProductPagingParam,
) (*pb.ItemGroupList, error) {
	count, err := a.dictBizSrv.CountItemGroupsByProductCode(ctx, param.GetProduct())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	itemGroups, err := a.dictBizSrv.ListItemGroupsByProductCode(ctx, param.GetProduct(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemGroupList{
		Count:      count,
		ItemGroups: make([]*pb.ItemGroup, len(itemGroups)),
	}
	for i, itemGroup := range itemGroups {
		g, err := adapter.BuildPbItemGroup(&itemGroup)
		if err != nil {
			return nil, err
		}
		list.ItemGroups[i] = g
	}
	return list, nil
}

// CountItemGroupsByProductID 根据产品ID获取产品所有计费项组的数量
func (a *DictAction) CountItemGroupsByProductID(
	ctx context.Context,
	productID *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemGroupsByProductID(ctx, productID.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, err
}

// CountItemGroupsByProductCode 根据产品 code 获取产品所有计费项组的数量
func (a *DictAction) CountItemGroupsByProductCode(
	ctx context.Context,
	product *pb.CodeParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemGroupsByProductCode(ctx, product.GetCode())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, err
}

// CreateItemGroup 创建计费项组
func (a *DictAction) CreateItemGroup(
	ctx context.Context,
	itemGroup *pb.ItemGroup,
) (*pb.ItemGroup, error) {
	m, err := adapter.BuildItemGroup(itemGroup)
	if err != nil {
		return nil, err
	}

	g, err := a.dictBizSrv.CreateItemGroup(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemGroup(g)
}

// UpdateItemGroupByID 根据id更新计费项组
func (a *DictAction) UpdateItemGroupByID(
	ctx context.Context,
	param *pb.IDItemGroupParam,
) (*pb.ItemGroup, error) {
	group := param.GetItemGroup()
	m, err := adapter.BuildItemGroup(group)
	if err != nil {
		return nil, err
	}

	g, err := a.dictBizSrv.UpdateItemGroupByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemGroup(g)
}

// UpdateItemGroupByCode 根据 code 更新计费项组
func (a *DictAction) UpdateItemGroupByCode(
	ctx context.Context,
	param *pb.CodeItemGroupParam,
) (*pb.ItemGroup, error) {
	group := param.GetItemGroup()
	m, err := adapter.BuildItemGroup(group)
	if err != nil {
		return nil, err
	}

	g, err := a.dictBizSrv.UpdateItemGroupByCode(ctx, param.GetCode(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemGroup(g)
}

// DeleteItemGroupByID 根据id删除计费项组
func (a *DictAction) DeleteItemGroupByID(
	ctx context.Context,
	groupID *pb.IDParam,
) (*pb.ItemGroup, error) {
	group, err := a.dictBizSrv.DeleteItemGroupByID(ctx, groupID.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemGroup(group)
}
