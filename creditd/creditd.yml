rpc:
  addr: ":9801"
  gatewayaddr: ":9811"
  forwardaddr: "localhost:9801"
  # 命令行 --pprof 参数优先级更高
  enablepprof: false
intl:
  ref_timezone:
    name: CST
    offset: 28800  # 8 * 3600
  default_lang_code: zh
mysql:
  host: "localhost"
  port: 3306
  username: "root"
  password: ""
  db: "pay_credit"
  # utf8 只支持 Unicode BMP 字符，要用 utf8mb4 (MySQL 5.5.3+)
  # https://dev.mysql.com/doc/refman/5.5/en/charset-unicode-utf8mb4.html
  charset: "utf8mb4"
  # 永远不要用 utf8_general_ci: https://stackoverflow.com/a/766996
  collation: "utf8mb4_unicode_ci"
  parse_time: true
  loc: "Local"
  max_open_conn: 100
  max_idle_conn: 100
  max_lifetime: 5m
cache:
  enabled: true
  prefix: "pay-v4-credit"
  redis_config:
    addrs:
      - "localhost:6379"
    mastername: ""
    poolsize: 2000
    readtimeout: 30s  #30 * time.Second
    idletimeout: 240s # 240 * time.Second
    pooltimeout: 240s # 240 * time.Second
  default_expires: 30m
credit:
  default_pagesize: 10
  minimum_system_credit_line: 100000
  maximum_system_credit_line: 5000000
acc:
  host: "http://localhost:9100"
  username: "root"
  password: "root"
  client_id: "59278d3b43c8ce6ad600182e"
  client_secret: ""
services:
  gaea_admin: "http://localhost:9013"
  sofa: "http://localhost:3000"
  morse: "http://localhost:9015"
  notification_v2: "http://notification-backend.notification-env-default:4050"
  wallet_v4: "localhost:9703"
notice:
  delayed_duration: "4m"
