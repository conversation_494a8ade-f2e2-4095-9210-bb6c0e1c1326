package action_test

import (
	"context"
	"testing"

	"github.com/qbox/pay-sdk/dict"
	"github.com/stretchr/testify/assert"
)

func TestItemDataTypeUnitInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	dictClient := sandbox.dictClient

	const dataTypeID uint64 = 1
	us := []dict.ItemDataTypeUnit{
		{
			DataTypeId: dataTypeID,
			Name:       "KB",
			Power:      1,
		},
		{
			DataTypeId: dataTypeID,
			Name:       "MB",
			Power:      2,
		},
	}
	assertFields := func(unit *dict.ItemDataTypeUnit, n int, msgAndArgs ...any) {
		if n >= len(us) {
			return
		}
		assert.Equal(t, us[n].GetDataTypeId(), unit.GetDataTypeId(), msgAndArgs...)
		assert.Equal(t, us[n].GetName(), unit.GetName(), msgAndArgs...)
		assert.Equal(t, us[n].GetPower(), unit.GetPower(), msgAndArgs...)
	}
	ids := make([]uint64, len(us))
	for n, u := range us {
		unit, err := dictClient.CreateItemDataTypeUnit(context.Background(), &u)
		if assert.NoError(t, err, "CreateItemDataTypeUnit") {
			assert.NotZero(t, unit.GetId(), "CreateItemDataTypeUnit")
			ids[n] = unit.GetId()
		}

		unit, err = dictClient.GetItemDataTypeUnitByID(context.Background(), &dict.IDParam{Id: unit.GetId()})
		if assert.NoError(t, err, "GetItemDataTypeUnitByID") {
			assertFields(unit, n, "GetItemDataTypeUnitByID")
		}

		unit.Name += "suffix"
		us[n].Name += "suffix"
		unit, err = dictClient.UpdateItemDataTypeUnitByID(context.Background(), &dict.IDItemDataTypeUnitParam{Id: unit.GetId(), ItemDataTypeUnit: unit})
		if assert.NoError(t, err, "UpdateItemDataTypeUnitByID") {
			assert.Equal(t, us[n].GetName(), unit.GetName(), "UpdateItemDataTypeUnitByID")
		}
	}

	count, err := dictClient.CountItemDataTypeUnitsByItemDataTypeID(context.Background(), &dict.IDParam{Id: dataTypeID})
	if assert.NoError(t, err, "CountItemDataTypeUnitsByItemDataTypeID") {
		assert.Len(t, us, int(count.GetCount()), "CountItemDataTypeUnitsByItemDataTypeID")
	}

	list, err := dictClient.ListItemDataTypeUnitsByItemDataTypeID(context.Background(), &dict.IDPagingParam{Id: dataTypeID, Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListItemDataTypeUnitsByItemDataTypeID") {
		assert.Len(t, us, int(list.GetCount()), "ListItemDataTypeUnitsByItemDataTypeID")
		for i, dtu := range list.GetItemDataTypeUnits() {
			assertFields(dtu, i, "ListItemDataTypeUnitsByItemDataTypeID")
		}
	}

	for _, id := range ids {
		_, err = dictClient.DeleteItemDataTypeUnitByID(context.Background(), &dict.IDParam{Id: id})
		assert.NoError(t, err, "DeleteItemDataTypeUnitByID")
	}
}
