package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"

	"qiniu.io/pay/priced/model"
)

// BuildPbCumulativeType 转换数据层 CumulativeType 为 proto model
func BuildPbCumulativeType(x model.CumulativeType) pb.CumulativeType {
	return pb.CumulativeType(x)
}

// BuildCumulativeType 转换 proto model 为数据层 CumulativeType
func BuildCumulativeType(x pb.CumulativeType) model.CumulativeType {
	return model.CumulativeType(x)
}

// BuildPbBillPeriodType 转换数据层 BillPeriodType 为 proto model
func BuildPbBillPeriodType(x model.BillPeriodType) pb.BillPeriodType {
	return pb.BillPeriodType(x)
}

// BuildBillPeriodType 转换 proto model 为数据层 BillPeriodType
func BuildBillPeriodType(x pb.BillPeriodType) model.BillPeriodType {
	return model.BillPeriodType(x)
}

// BuildPbPriceItem 转换数据层 model PriceItem 为 proto model
func BuildPbPriceItem(priceItem *model.PriceItem) (*pb.PriceItem, error) {
	return &pb.PriceItem{
		Id:                 priceItem.ID,
		PriceId:            priceItem.PriceID,
		ItemId:             priceItem.ItemID,
		ZoneId:             priceItem.ZoneID,
		AlgorithmId:        priceItem.AlgorithmID,
		IsDisabled:         priceItem.IsDisabled,
		IsDefaultAlgorithm: priceItem.IsDefaultAlgorithm,
		StairPriceType:     priceItem.StairPriceType,
		UnitRate:           priceItem.UnitRate,
		Type:               BuildPbPriceType(priceItem.Type),
		EffectTime:         timestamppb.New(priceItem.EffectTime.TimeIn(time.UTC)),
		DeadTime:           timestamppb.New(priceItem.DeadTime.TimeIn(time.UTC)),

		CreatedAt: timestamppb.New(priceItem.CreatedAt),
		UpdatedAt: timestamppb.New(priceItem.UpdatedAt),

		ShouldSkip: priceItem.ShouldSkip,

		CumulativeCycle: BuildPbCumulativeType(priceItem.CumulativeCycle),
		BillPeriodType:  BuildPbBillPeriodType(priceItem.BillPeriodType),
		CurrencyType:    priceItem.CurrencyType.String(),
	}, nil
}

// BuildPriceItem 转换 proto model 为数据层 model PriceItem
func BuildPriceItem(priceItem *pb.PriceItem) (*model.PriceItem, error) {

	err := priceItem.GetEffectTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	effectTime := priceItem.GetEffectTime().AsTime()

	err = priceItem.GetDeadTime().CheckValid()
	if err != nil {
		return nil, errors.Trace(err)
	}
	deadTime := priceItem.GetDeadTime().AsTime()

	var createdAt, updatedAt time.Time
	if priceItem.GetCreatedAt() != nil {
		err := priceItem.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		createdAt = priceItem.GetCreatedAt().AsTime()
	}

	if priceItem.GetUpdatedAt() != nil {
		err := priceItem.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		updatedAt = priceItem.GetUpdatedAt().AsTime()
	}

	return &model.PriceItem{
		ID:                 priceItem.GetId(),
		PriceID:            priceItem.GetPriceId(),
		ItemID:             priceItem.GetItemId(),
		ZoneID:             priceItem.GetZoneId(),
		AlgorithmID:        priceItem.GetAlgorithmId(),
		IsDisabled:         priceItem.GetIsDisabled(),
		IsDefaultAlgorithm: priceItem.GetIsDefaultAlgorithm(),
		StairPriceType:     priceItem.GetStairPriceType(),
		UnitRate:           priceItem.GetUnitRate(),
		Type:               BuildPriceTableType(priceItem.Type),
		EffectTime:         base.NewHNS(effectTime),
		DeadTime:           base.NewHNS(deadTime),
		CreatedAt:          createdAt,
		UpdatedAt:          updatedAt,

		ShouldSkip: priceItem.GetShouldSkip(),

		CumulativeCycle: BuildCumulativeType(priceItem.CumulativeCycle),
		BillPeriodType:  BuildBillPeriodType(priceItem.BillPeriodType),
		CurrencyType:    base.CurrencyType(priceItem.CurrencyType),
	}, nil
}
