package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
)

// BuildPbCouponDiscount converts model.CouponDiscount to the protobuf type.
func BuildPbCouponDiscount(discount *model.CouponDiscount) (*pb.CouponDiscount, error) {
	return &pb.CouponDiscount{
		Id:          discount.ID,
		Code:        discount.Code,
		Type:        discount.Type,
		ScopeId:     discount.ScopeID,
		Name:        discount.Name,
		Description: discount.Description,
		Remark:      discount.Remark,
		Discount:    discount.Discount,
		EffectTime:  timestamppb.New(discount.EffectTime.TimeIn(time.UTC)),
		DeadTime:    timestamppb.New(discount.DeadTime.TimeIn(time.UTC)),
		CreatedAt:   timestamppb.New(discount.CreatedAt),
		UpdatedAt:   timestamppb.New(discount.UpdatedAt),
	}, nil
}

// BuildCouponDiscount converts from pb.CouponDiscount to the model layer type.
func BuildCouponDiscount(discount *pb.CouponDiscount) (*model.CouponDiscount, error) {
	err := discount.GetEffectTime().CheckValid()
	if err != nil {
		return nil, err
	}
	effectTime := discount.GetEffectTime().AsTime()

	err = discount.GetDeadTime().CheckValid()
	if err != nil {
		return nil, err
	}
	deadTime := discount.GetDeadTime().AsTime()

	var createdAt, updatedAt time.Time
	if discount.GetCreatedAt() != nil {
		err = discount.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = discount.GetCreatedAt().AsTime()
	}

	if discount.GetUpdatedAt() != nil {
		err = discount.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = discount.GetUpdatedAt().AsTime()
	}

	return &model.CouponDiscount{
		ID:          discount.GetId(),
		Code:        discount.GetCode(),
		Type:        discount.GetType(),
		ScopeID:     discount.GetScopeId(),
		Name:        discount.GetName(),
		Description: discount.GetDescription(),
		Remark:      discount.GetRemark(),
		Discount:    discount.GetDiscount(),
		EffectTime:  base.NewHNS(effectTime),
		DeadTime:    base.NewHNS(deadTime),
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
	}, nil
}
