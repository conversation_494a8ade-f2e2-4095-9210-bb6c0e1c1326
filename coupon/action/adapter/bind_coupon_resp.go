package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/coupon/service"
)

// BuildPbBindCouponResp converts service.BindCouponResp to the pb layer type.
func BuildPbBindCouponResp(
	in *service.BindCouponResp,
) (*pb.BindCouponResp, error) {
	var coupon *pb.Coupon
	if in.Coupon != nil {
		var err error
		coupon, err = BuildPbCoupon(in.Coupon)
		if err != nil {
			return nil, errors.Trace(err)
		}
	}

	return &pb.BindCouponResp{
		Ok:     in.OK,
		Coupon: coupon,
		Err:    pb.BindCouponError(in.Err),
	}, nil
}
