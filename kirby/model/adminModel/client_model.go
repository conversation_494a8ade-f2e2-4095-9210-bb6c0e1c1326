package adminModel

import (
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// Client model definition
type Client struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// ClientID 供程序使用的 Client 唯一 ID
	ClientID string `gorm:"column:client_id;type:VARCHAR(32);not null;unique_index:idx_client_client_id;comment:'供程序使用的 Client 唯一 ID'"`
	// Name 展示名称
	Name string `gorm:"column:name;type:VARCHAR(64);comment:'展示名称'"`
	// Enabled 启用状态，只有启用的 Client 才能推量
	Enabled bool `gorm:"column:enabled;comment:'启用状态，只有启用的 Client 才能推量'"`

	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);default:now(6)"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME(6);default:now(6) ON UPDATE now(6)"`
}

// <PERSON>lientDao is data access object of Client model
type ClientDao struct {
	base *dao.BaseDao
}

// NewClientDao is constructor of ClientDao
func NewClientDao(base *dao.BaseDao) *ClientDao {
	return &ClientDao{
		base: base,
	}
}

// GetByCID selects a Client by cid
func (d *ClientDao) GetByCID(cid string, epoch time.Time) (*Client, error) {
	model := &Client{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&Client{}).
			Where("`client_id` = ? AND `created_at` <= ?", cid, epoch).
			First(value).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("cid", cid)
	}
	return model, nil
}

// Save inserts or updates a Client by id
func (d *ClientDao) Save(model *Client) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Save(value).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// ListAll returns all records of Client
func (d *ClientDao) ListAll() ([]*Client, error) {
	var result []*Client
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&Client{}).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return result, nil
}
