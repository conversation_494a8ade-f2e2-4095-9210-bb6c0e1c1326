package action

import (
	"context"
	"time"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/bo-base/v4/intl/tz"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// NotifyBalanceInsufficiency notifies users who are prone to get insufficient balance in 7/3/1 days
func (a *WalletAction) NotifyBalanceInsufficiency(
	ctx context.Context, empty *empty.Empty) (*pb.NotifyBalanceInsufficiencyResp, error) {
	now := time.Now()
	total, totalCnt, err := a.balanceInsufficiencySrv.
		NotifyBalanceInsufficiency(
			ctx,
			time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, tz.MustLocationFromCtx(ctx)),
		)
	if err != nil {
		return nil, err
	}

	return adapter.BuildPbBalanceInsufficiencyResp(total, totalCnt)
}
