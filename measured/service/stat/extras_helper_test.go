package stat

import (
	"sort"
	"testing"

	"github.com/qbox/bo-base/v4/expr"

	mppb "github.com/qbox/pay-sdk/measureproxy"
	"github.com/stretchr/testify/assert"
)

func TestParseStatG(t *testing.T) {
	{
		g, err := parseStatG("day")
		assert.NoError(t, err)
		assert.Equal(t, mppb.G_DAY, g)
	}

	{
		g, err := parseStatG("5min")
		assert.NoError(t, err)
		assert.Equal(t, mppb.G_5MIN, g)
	}

	{
		_, err := parseStatG("")
		assert.Error(t, err)
	}

	{
		_, err := parseStatG("1day")
		assert.Error(t, err)
	}
}

func TestParseStatQuery(t *testing.T) {
	{
		expected := []*kv{{K: "baz", V: "test quux"}, {K: "foo", V: "bar"}}
		actual, err := parseStatQuery("foo=bar&baz=test+quux")
		assert.NoError(t, err)

		sort.Slice(actual, func(i int, j int) bool {
			return actual[i].K+actual[i].V < actual[j].K+actual[j].V
		})

		assert.Equal(t, expected, actual)
	}

	{
		expected := []*kv{{K: "foo", V: "bar"}, {K: "foo", V: "baz"}}
		actual, err := parseStatQuery("foo=bar&foo=baz")
		assert.NoError(t, err)

		sort.Slice(actual, func(i int, j int) bool {
			return actual[i].K+actual[i].V < actual[j].K+actual[j].V
		})

		assert.Equal(t, expected, actual)
	}

	{
		_, err := parseStatQuery("foo=bar&baz=test%2gquux")
		assert.Error(t, err)
	}
}

func TestParseExtrasAsStd(t *testing.T) {
	{
		input := map[string]string{
			"stat_g":      "5min",
			"stat_select": "flow",
			"stat_query":  "src=foo&src=bar&ftype=0",
			"stat_expr":   "flow*8/300",
			"stat_key":    "blob_io",
		}
		e, _ := expr.NewMathExpr(input["stat_expr"])
		expected := &parsedStatExtras{
			G:      mppb.G_5MIN,
			Select: []string{"flow"},
			Key:    "blob_io",
			Query: []*kv{
				{K: "ftype", V: "0"},
				{K: "src", V: "bar"},
				{K: "src", V: "foo"},
			},
			Expr: e,
		}

		actual, err := parseExtrasAsStd(input)
		assert.NoError(t, err)

		sort.Slice(actual.Query, func(i int, j int) bool {
			return actual.Query[i].K+actual.Query[i].V < actual.Query[j].K+actual.Query[j].V
		})

		assert.Equal(t, expected, actual)
	}

	{
		input := map[string]string{
			"stat_g": "1day",
		}

		_, err := parseExtrasAsStd(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_expr": "flow*8/",
		}

		_, err := parseExtrasAsStd(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_query": "foo=%2g",
		}

		_, err := parseExtrasAsStd(input)
		assert.Error(t, err)
	}

	{
		input := map[string]string{
			"stat_foo": "bar",
		}

		_, err := parseExtrasAsStd(input)
		assert.Error(t, err)
	}
}
