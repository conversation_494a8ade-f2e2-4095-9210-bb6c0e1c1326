package service

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"

	"qiniu.io/pay/priced/model"
)

// GetPriceInfluenceWithCostPrice 查询报价单与成本价的差异
//
// NOTE: 只计算指定报价单包含的计费项的价格差异, 且查询的是该报价单创建时间点之前的
// 单价成本和当前报价单的价格差异
//
// 步骤：
// 1. 根据 ID 查询报价单详情
// 2. 根据报价单获取要比对价格的 (item, zone) 列表
// 3. 分别计算各 (item, zone) 在报价单提交前的价格与当前报价单的差异
// 3.a. 查询计费项的成本价
// 3.b. 查询当前报价单里对应计费项的价格信息
func (s *PriceBizService) GetPriceInfluenceWithCostPrice(
	ctx context.Context,
	uid uint64,
	priceID uint64,
	scope *PriceScope,
) (*PriceInfluence, error) {
	// 查询该报价单包含的所有 PriceItem
	priceItems, err := s.ListAllPriceItemsByPriceID(ctx, priceID, 0, -1)
	if err != nil {
		return nil, errors.Trace(err)
	}

	beforeItemPriceMap := make(map[uint64][]model.PriceItem)
	afterItemPriceMap := make(map[uint64][]model.PriceItem)
	at := time.Now()
	for _, priceItem := range priceItems {
		// 过滤不在 scope 中的计费项
		included, err := s.PriceScopeCheckByID(ctx, priceItem.ItemID, 0, 0, scope)
		if err != nil {
			return nil, errors.Trace(err)
		}

		if !included {
			continue
		}

		// 有成本价时填充成本价，否则默认填充公开价
		costPriceItem, err := s.QueryItemCostPriceWithDefault(ctx, &priceItem, at)
		if err != nil {
			return nil, errors.Trace(err)
		}
		beforeItemPriceMap[priceItem.ItemID] = append(beforeItemPriceMap[priceItem.ItemID], *costPriceItem)

		// 计费项在当前报价单的价格信息
		afterItemPriceMap[priceItem.ItemID] = append(afterItemPriceMap[priceItem.ItemID], priceItem)
	}

	return &PriceInfluence{
		Before: UserPriceWithTimeRangeResp{
			ItemPrices: beforeItemPriceMap,
		},
		After: UserPriceWithTimeRangeResp{
			ItemPrices: afterItemPriceMap,
		},
	}, nil
}

// QueryItemCostPriceWithDefault 查询计费项成本价，如果成本价不存在，默认用公开价填充
func (s *PriceBizService) QueryItemCostPriceWithDefault(
	ctx context.Context,
	item *model.PriceItem,
	at time.Time,
) (*model.PriceItem, error) {
	// 1. 查询计费项成本价的 price_table_id
	costPriceTable, err := s.QueryCostPriceTable(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 2. 查询当前计费项成本价，匹配 item_id + zone_id + algorithm_id + stair_price_type + currency_type
	costItem, err := s.priceDao.PriceItem.GetItemByPropertiesAtTimePoint(
		costPriceTable.ID,
		item.ItemID,
		item.ZoneID,
		item.AlgorithmID,
		item.StairPriceType,
		base.NewHNS(at),
		item.CurrencyType,
	)
	if err == nil {
		return costItem, nil
	} else if errors.Cause(err) != dao.ErrRecordNotFound {
		return nil, errors.Trace(err)
	}

	// 3. 获取计费项公开价用来填充成本价
	defaultPriceTable, err := s.GetDefaultPriceTable(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}

	getItemPriceByTimePointParam := &model.GetItemPriceByTimePointParam{
		PriceID: defaultPriceTable.ID,
		ItemID:  item.ItemID,
		ZoneID:  item.ZoneID,
		At:      base.NewHNS(at),
	}
	tmpDefaultPriceItem, err := s.priceDao.PriceItem.GetItemPriceByItemIDZoneIDAlgorithmIDAndTimePoint(getItemPriceByTimePointParam)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 4. 用当前报价的属性填充公开价属性
	tmpDefaultPriceItem.AlgorithmID = item.AlgorithmID
	tmpDefaultPriceItem.StairPriceType = item.StairPriceType

	return tmpDefaultPriceItem, nil
}
