package doraItemsQuerier

// XXX 为避免跨服务 import 包，这个文件是 measureproxy/adapter 内容的节选
//
// 有少许改动（方法变为私有，类型变化）

import (
	"github.com/qbox/bo-base/v4/legacystat/common"
	pb "github.com/qbox/pay-sdk/measureproxy"
)

// buildModelLegacyDoraCmd converts from pb.LegacyDoraCmd to the model layer type.
func buildModelLegacyDoraCmd(x *pb.LegacyDoraCmd) common.LegacyDoraCmd {
	return common.LegacyDoraCmd{
		Key: x.Key,
		Cmd: x.Cmd,
	}
}

// buildModelDoraCmdsInUseResp converts from pb.DoraCmdsInUseResp to the model layer type.
func buildModelDoraCmdsInUseResp(x *pb.DoraCmdsInUseResp) []common.LegacyDoraCmd {
	modelCmds := make([]common.LegacyDoraCmd, len(x.Cmds))
	for i, cmd := range x.Cmds {
		modelCmds[i] = buildModelLegacyDoraCmd(cmd)
	}

	return modelCmds
}
