package model

import (
	"context"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// ErrInvalidParam is invalid param error
var ErrInvalidParam = errors.New("invalid parameter")

// ErrGenSQLFailure means failure of generating SQL
var ErrGenSQLFailure = errors.New("generate sql error")

// CouponBatch model definition
type CouponBatch struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// Name 抵用券名称，将对用户展示
	Name string
	// Description 抵用券描述（使用范围等），将对用户展示
	Description string
	// Remark 内部描述，不对用户展示
	Remark string
	// Reason 事由
	Reason CouponReason
	// ReasonDesc 事由描述
	ReasonDesc string
	// State 状态
	//
	// TODO: 看看之后能不能做成完整审计功能，不靠 update
	State CouponBatchState
	// BindMethod 发放形式
	BindMethod CouponBindMethod
	// StartTime 发放时间起始（含）
	StartTime base.HNS
	// EndTime 发放时间截止（不含）
	EndTime base.HNS
	// BatchSize 总发放张数
	BatchSize uint64
	// NumActivated 已领取张数
	NumActivated uint64
	// NumUsed 已使用张数
	NumUsed uint64
	// MaxActivationTimesPerUser 单用户领取上限，张
	MaxActivationTimesPerUser uint64
	// TimePeriodType 生效时间种类（固定天数，固定时间段，etc）
	TimePeriodType CouponTimePeriodType
	// EffectDays 生效天数，仅生效时间种类为【固定天数】时有意义
	EffectDays uint64
	// CouponEffectTime 生效时间起始（含），仅生效时间种类为【固定时间段】时有意义
	CouponEffectTime base.HNS
	// CouponDeadTime 生效时间截止（不含），仅生效时间种类为【固定时间段】时有意义
	CouponDeadTime base.HNS
	// ThresholdMoney 使用金额门槛，万分之一元
	ThresholdMoney base.Money
	// CouponMoney 抵用券面额，万分之一元
	CouponMoney base.Money
	// IsMultipleUse 是否可多次使用
	IsMultipleUse bool
	// ApplicableUserType 适用用户类型（NOTE:目前貌似用不到，先保留）
	ApplicableUserType CouponUserType
	// ApplicablePayMode 适用付费类型（NOTE:目前亦只有预付）
	ApplicablePayMode CouponPayMode
	// ArrearCanUse 欠费是否可使用
	ArrearCanUse bool
	// IsUnlimitedScope 是否通用、无限制
	IsUnlimitedScope bool
	// CouponScopeDesc 适用范围描述
	CouponScopeDesc string
	// 申请人
	Applicant ApplicantType
	// Url 使用地址（portal 上使用抵用券处）
	Url string

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// CouponBatchDao is data access object of CouponBatch model
type CouponBatchDao struct {
	base *dao.BaseDao
}

// NewCouponBatchDao is constructor of CouponBatchDao
func NewCouponBatchDao(base *dao.BaseDao) *CouponBatchDao {
	return &CouponBatchDao{
		base: base,
	}
}

// DoTransaction do a transaction
func (d *CouponBatchDao) DoTransaction(fn func(*CouponBatchDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		couponBatchDao := NewCouponBatchDao(base)
		return fn(couponBatchDao)
	})
}

// GetByID selects a CouponBatch by id
func (d *CouponBatchDao) GetByID(id uint64, expires ...time.Duration) (*CouponBatch, error) {
	model := &CouponBatch{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponBatch{}).
			Where("id = ?", id).
			First(value).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// Save inserts or updates a record of CouponBatch
func (d *CouponBatchDao) Save(m *CouponBatch, expires ...time.Duration) error {
	if m == nil {
		return ErrInvalidParam
	}
	err := d.base.Execute(func(value any) error {
		return d.base.Save(value).Error
	}, m)
	if err != nil {
		return errors.Trace(err).WithField("coupon_batch", m)
	}
	return nil
}

// Update updates a record of couponBatch by id
func (d *CouponBatchDao) Update(m *CouponBatch, expires ...time.Duration) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponBatch{}).Updates(m).Find(value).Error
	}, m)

	if err != nil {
		return errors.Trace(err).WithField("id", m.ID)
	}
	return nil
}

// UpdateWithOmitCol updates a record of couponBatch by id
func (d *CouponBatchDao) UpdateWithOmitCol(batchID uint64, m map[string]any, omits []string, expires ...time.Duration) error {
	if m == nil {
		return errors.New("dao invalid parameter")
	}

	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponBatch{ID: batchID}).Omit(omits...).Updates(value).Error
	}, m)

	if err != nil {
		return errors.Trace(err).WithField("id", batchID)
	}
	return nil
}

// ProgressState 推动状态更新，直到收敛为止
func (d *CouponBatchDao) ProgressState(
	ctx context.Context,
	cb *CouponBatch,
	epoch time.Time,
	expires ...time.Duration,
) (newState CouponBatchState, err error) {
	for {
		prevState := cb.State

		nextState, err := d.progressState(ctx, cb, epoch, expires...)
		if err != nil {
			return CouponBatchStateUnknown, errors.Trace(err)
		}

		if prevState == nextState {
			return nextState, nil
		}
	}
}

// progressState 推动一次状态更新
func (d *CouponBatchDao) progressState(
	ctx context.Context,
	cb *CouponBatch,
	epoch time.Time,
	expires ...time.Duration,
) (newState CouponBatchState, err error) {
	prevState := cb.State
	nextState := ComputeNextStateForCouponBatch(cb, epoch)
	if prevState == nextState {
		return nextState, nil
	}

	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"couponBatchID": cb.ID,
		"prevState":     prevState,
		"nextState":     nextState,
		"epoch":         epoch,
	}).Info("coupon batch state transition triggered")

	cb.State = nextState
	err = d.Update(cb, expires...)
	if err != nil {
		cb.State = prevState
		return prevState, errors.Trace(err).WithFields(errors.Fields{
			"nextState":   nextState,
			"couponBatch": cb,
		})
	}

	return nextState, nil
}

// ListAllCouponBatches lists all records of couponBatch
func (d *CouponBatchDao) ListAllCouponBatches(offset, limit uint64, expires ...time.Duration) (list []CouponBatch, err error) {
	err = d.base.Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": offset,
			"limit":  limit,
		})
	}
	return list, nil
}

// ListByIDs lists records of CouponBatch by ids
func (d *CouponBatchDao) ListByIDs(ids []uint64, expires ...time.Duration) ([]CouponBatch, error) {
	var list []CouponBatch
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponBatch{}).
			Where("id in (?)", ids).
			Find(value).
			Error
	}, &list)
	if err != nil {
		return nil, errors.Trace(err).WithField("ids", ids)
	}
	return list, nil
}

// ListCouponBatchesByCondsParam is parameter for ListCouponBatchesByConds interface
type ListCouponBatchesByCondsParam struct {
	IDs        []uint64
	Name       string
	States     []CouponBatchState
	Reason     []CouponReason
	BindMethod []CouponBindMethod
	StartTime  base.HNS
	EndTime    base.HNS
}

func (param *ListCouponBatchesByCondsParam) toWhereClause() (conditionStr string, args []any, err error) {
	andCond := squirrel.And{}

	if len(param.IDs) > 0 {
		andCond = append(andCond, squirrel.Eq{
			"id": param.IDs,
		})
	}
	if param.Name != "" {
		andCond = append(andCond, squirrel.Eq{
			"name": param.Name,
		})
	}
	if len(param.States) != 0 {
		andCond = append(andCond, squirrel.Eq{
			"state": param.States,
		})
	}
	if len(param.Reason) != 0 {
		andCond = append(andCond, squirrel.Eq{
			"reason": param.Reason,
		})
	}
	if len(param.BindMethod) != 0 {
		andCond = append(andCond, squirrel.Eq{
			"bind_method": param.BindMethod,
		})
	}
	if param.StartTime != 0 {
		andCond = append(andCond, squirrel.GtOrEq{
			"start_time": param.StartTime,
		})
	}
	if param.EndTime != 0 {
		andCond = append(andCond, squirrel.Lt{
			"end_time": param.EndTime,
		})
	}
	return andCond.ToSql()
}

// ListByConds lists records of couponBatch by conditions
func (d *CouponBatchDao) ListByConds(
	param *ListCouponBatchesByCondsParam,
	offset int,
	limit int,
) (list []CouponBatch, err error) {
	conditionStr, args, err := param.toWhereClause()
	if err != nil {
		return nil, ErrGenSQLFailure
	}

	err = d.base.Execute(func(value any) error {
		return d.base.
			Where(conditionStr, args...).
			Offset(offset).
			Limit(limit).
			Order("created_at DESC").
			Find(value).
			Error
	}, &list)

	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"param":         param,
			"condition_str": conditionStr,
			"args":          args,
		})
	}
	return list, nil
}
