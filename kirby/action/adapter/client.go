package adapter

import (
	"time"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/model/adminModel"
)

// BuildClient converts pb.Client to the model layer type.
func BuildClient(
	in *pb.Client,
) (*adminModel.Client, error) {
	return &adminModel.Client{
		ID:        0,
		ClientID:  in.ClientId,
		Name:      in.Name,
		Enabled:   in.Enabled,
		CreatedAt: time.Time{},
		UpdatedAt: time.Time{},
	}, nil
}

// BuildPbClient converts adminModel.Client to the pb layer type.
func BuildPbClient(
	in *adminModel.Client,
) (*pb.Client, error) {
	return &pb.Client{
		ClientId: in.ClientID,
		Name:     in.Name,
		Enabled:  in.Enabled,
	}, nil
}

// BuildPbClients converts from []*adminModel.Client to the pb layer type.
func BuildPbClients(in []*adminModel.Client) (*pb.Clients, error) {
	result := make([]*pb.Client, len(in))
	for i, x := range in {
		obj, err := BuildPbClient(x)
		if err != nil {
			return nil, errors.Trace(err)
		}
		result[i] = obj
	}
	return &pb.Clients{Data: result}, nil
}

// BuildClients converts from []*pb.Client to the model layer type.
func BuildClients(in []*pb.Client) ([]adminModel.Client, error) {
	result := make([]adminModel.Client, len(in))
	for i, x := range in {
		obj, err := BuildClient(x)
		if err != nil {
			return nil, errors.Trace(err)
		}
		result[i] = *obj
	}
	return result, nil
}
