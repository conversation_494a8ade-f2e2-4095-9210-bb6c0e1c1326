package adapter

import (
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/wallet"
	"google.golang.org/protobuf/types/known/timestamppb"
	"qiniu.io/pay/coupon/model"
	"qiniu.io/pay/coupon/service"
)

var ErrParsePbTimestamp = errors.New("parsing pb timestamp failure")

// BuildListCouponBatchesByCondsParam converts from pb.ListCouponBatchesByCondsParam to the model layer type.
func BuildListCouponBatchesByCondsParam(param *pb.ListCouponBatchesByCondsParam) (*model.ListCouponBatchesByCondsParam, error) {
	states := make([]model.CouponBatchState, len(param.GetState()))
	for i, state := range param.GetState() {
		states[i] = model.CouponBatchState(state)
	}

	reason := make([]model.CouponReason, len(param.GetReason()))
	for i, r := range param.GetReason() {
		reason[i] = model.CouponReason(r)
	}

	bindMethod := make([]model.CouponBindMethod, len(param.GetBindMethod()))
	for i, r := range param.GetBindMethod() {
		bindMethod[i] = model.CouponBindMethod(r)
	}

	return &model.ListCouponBatchesByCondsParam{
		IDs:        param.GetIds(),
		Name:       param.GetName(),
		States:     states,
		Reason:     reason,
		BindMethod: bindMethod,
		StartTime:  base.NewHNS(param.StartTime.AsTime()),
		EndTime:    base.NewHNS(param.EndTime.AsTime()),
	}, nil
}

// BuildPbCouponBatchList converts from service.CouponBatchList to the pb layer type.
func BuildPbCouponBatchList(couponBatchList *service.CouponBatchList) (*pb.CouponBatchList, error) {
	pbCouponBatchArr := make([]*pb.CouponBatch, len(couponBatchList.CouponBatches))
	for i, couponBatchAndScope := range couponBatchList.CouponBatches {
		couponBatch := couponBatchAndScope.CouponBatch

		scopeDetail, err := ComposeCouponScopeDetail(couponBatchAndScope.Scope, couponBatch.IsUnlimitedScope)
		if err != nil {
			return nil, errors.Trace(err)
		}

		pbCouponBatch := &pb.CouponBatch{
			Id:                        couponBatch.ID,
			Name:                      couponBatch.Name,
			Description:               couponBatch.Description,
			Remark:                    couponBatch.Remark,
			Reason:                    pb.CouponReason(couponBatch.Reason),
			ReasonDesc:                couponBatch.ReasonDesc,
			State:                     pb.CouponBatchState(couponBatch.State),
			BindMethod:                pb.CouponBindMethod(couponBatch.BindMethod),
			StartTime:                 timestamppb.New(couponBatch.StartTime.TimeIn(time.UTC)),
			EndTime:                   timestamppb.New(couponBatch.EndTime.TimeIn(time.UTC)),
			BatchSize:                 couponBatch.BatchSize,
			NumActivated:              couponBatch.NumActivated,
			NumUsed:                   couponBatch.NumUsed,
			MaxActivationTimesPerUser: couponBatch.MaxActivationTimesPerUser,
			TimePeriodType:            pb.CouponTimePeriodType(couponBatch.TimePeriodType),
			EffectDays:                couponBatch.EffectDays,
			CouponEffectTime:          timestamppb.New(couponBatch.CouponEffectTime.TimeIn(time.UTC)),
			CouponDeadTime:            timestamppb.New(couponBatch.CouponDeadTime.TimeIn(time.UTC)),
			ThresholdMoney:            int64(couponBatch.ThresholdMoney),
			CouponMoney:               int64(couponBatch.CouponMoney),
			IsMultipleUse:             couponBatch.IsMultipleUse,
			UserScope:                 pb.CouponUserScope(couponBatch.ApplicableUserType),
			PayModeScope:              pb.CouponPayModeScope(couponBatch.ApplicablePayMode),
			ArrearCanUse:              couponBatch.ArrearCanUse,
			Scope:                     scopeDetail,
			CouponScopeDesc:           couponBatch.CouponScopeDesc,
			Applicant:                 pb.ApplicantType(couponBatch.Applicant),
			Url:                       couponBatch.Url,
			CreatedAt:                 timestamppb.New(couponBatch.CreatedAt),
			UpdatedAt:                 timestamppb.New(couponBatch.UpdatedAt),
		}

		pbCouponBatchArr[i] = pbCouponBatch
	}

	return &pb.CouponBatchList{
		CouponBatches: pbCouponBatchArr,
	}, nil
}

// BuildCouponBatch converts from pb.CouponBatch to the model layer type.
func BuildCouponBatch(param *pb.CreateCouponBatchParams) (*service.CreateCouponBatchParam, error) {
	if param == nil || param.GetScope() == nil {
		return nil, errors.New("invalid BuildCouponBatch param")
	}
	sellerIdsInclude := param.GetScope().GetSellerIdsInclude()
	productIdsInclude := param.GetScope().GetProductIdsInclude()
	skuIdsInclude := param.GetScope().GetProductIdsInclude()
	packageIdsInclude := param.GetScope().GetPackageIdsInclude()

	sellerIdsExclude := param.GetScope().GetSellerIdsExclude()
	productIdsExclude := param.GetScope().GetProductIdsExclude()
	skuIdsExclude := param.GetScope().GetSkuIdsExclude()
	packageIdsExclude := param.GetScope().GetPackageIdsExclude()

	if param.IsUnlimitedScope {
		sellerIdsInclude = []int64{}
		productIdsInclude = []int64{}
		skuIdsInclude = []int64{}
		packageIdsInclude = []int64{}

		sellerIdsExclude = []int64{}
		productIdsExclude = []int64{}
		skuIdsExclude = []int64{}
		packageIdsExclude = []int64{}
	}

	startTime := param.GetStartTime()
	endTime := param.GetEndTime()
	if startTime == nil || endTime == nil {
		return nil, errors.New("invalid start time or end time")
	}

	if startTime.AsTime().After(endTime.AsTime()) {
		return nil, errors.New("invalid start time and end time")
	}

	if param.GetTimePeriodType() == pb.COUPON_TIME_PERIOD_TYPE_ABSOLUTE {
		if param.GetCouponEffectTime() == nil || param.GetCouponDeadTime() == nil {
			return nil, errors.New("invalid coupon effect or dead time")
		}
		effectTime := param.GetCouponEffectTime().AsTime()
		deadTime := param.GetCouponDeadTime().AsTime()
		if !effectTime.Before(deadTime) {
			return nil, errors.New("invalid coupon effect and dead time")
		}
	}
	if param.GetTimePeriodType() == pb.COUPON_TIME_PERIOD_TYPE_CONST_DURATION {
		if param.GetEffectDays() == 0 {
			return nil, errors.New("invalid effect days")
		}
	}

	return &service.CreateCouponBatchParam{
		CouponBatch: model.CouponBatch{
			Name:                      param.GetName(),
			Description:               param.GetDescription(),
			Remark:                    param.GetRemark(),
			Reason:                    model.CouponReason(param.GetReason()),
			ReasonDesc:                param.GetReasonDesc(),
			BindMethod:                model.CouponBindMethod(param.GetBindMethod()),
			StartTime:                 base.NewHNS(startTime.AsTime()),
			EndTime:                   base.NewHNS(endTime.AsTime()),
			BatchSize:                 param.GetBatchSize(),
			MaxActivationTimesPerUser: param.GetMaxActivationTimesPerUser(),
			TimePeriodType:            model.CouponTimePeriodType(param.GetTimePeriodType()),
			EffectDays:                param.GetEffectDays(),
			CouponEffectTime:          base.NewHNS(param.GetCouponEffectTime().AsTime()),
			CouponDeadTime:            base.NewHNS(param.GetCouponDeadTime().AsTime()),
			ThresholdMoney:            base.Money(param.GetThresholdMoney()),
			CouponMoney:               base.Money(param.GetCouponMoney()),
			IsMultipleUse:             param.GetIsMultipleUse(),
			ApplicableUserType:        model.CouponUserType(param.GetUserScope()),
			ApplicablePayMode:         model.CouponPayMode(param.GetPayModeScope()),
			ArrearCanUse:              param.GetArrearCanUse(),
			IsUnlimitedScope:          param.GetIsUnlimitedScope(),
			CouponScopeDesc:           param.GetCouponScopeDesc(),
			Applicant:                 model.ApplicantType(param.GetApplicant()),
			Url:                       param.GetUrl(),
		},
		Scope: service.CouponScope{
			// include
			SellerIdsInclude:  sellerIdsInclude,
			ProductIdsInclude: productIdsInclude,
			SkuIdsInclude:     skuIdsInclude,
			PackageIdsInclude: packageIdsInclude,
			// exclude
			SellerIdsExclude:  sellerIdsExclude,
			ProductIdsExclude: productIdsExclude,
			SkuIdsExclude:     skuIdsExclude,
			PackageIdsExclude: packageIdsExclude,
		},
	}, nil
}

// BuildPbCouponBatch converts from service.CouponBatchAndScope to the pb layer type.
func BuildPbCouponBatch(couponBatchAndScope *service.CouponBatchAndScope) (*pb.CouponBatch, error) {
	couponBatch := couponBatchAndScope.CouponBatch

	scopeDetail, err := ComposeCouponScopeDetail(couponBatchAndScope.Scope, couponBatch.IsUnlimitedScope)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return &pb.CouponBatch{
		Id:                        couponBatch.ID,
		Name:                      couponBatch.Name,
		Description:               couponBatch.Description,
		Remark:                    couponBatch.Remark,
		Reason:                    pb.CouponReason(couponBatch.Reason),
		ReasonDesc:                couponBatch.ReasonDesc,
		State:                     pb.CouponBatchState(couponBatch.State),
		BindMethod:                pb.CouponBindMethod(couponBatch.BindMethod),
		StartTime:                 timestamppb.New(couponBatch.StartTime.TimeIn(time.UTC)),
		EndTime:                   timestamppb.New(couponBatch.EndTime.TimeIn(time.UTC)),
		BatchSize:                 couponBatch.BatchSize,
		NumActivated:              couponBatch.NumActivated,
		MaxActivationTimesPerUser: couponBatch.MaxActivationTimesPerUser,
		TimePeriodType:            pb.CouponTimePeriodType(couponBatch.TimePeriodType),
		EffectDays:                couponBatch.EffectDays,
		CouponEffectTime:          timestamppb.New(couponBatch.CouponEffectTime.TimeIn(time.UTC)),
		CouponDeadTime:            timestamppb.New(couponBatch.CouponDeadTime.TimeIn(time.UTC)),
		ThresholdMoney:            int64(couponBatch.ThresholdMoney),
		CouponMoney:               int64(couponBatch.CouponMoney),
		IsMultipleUse:             couponBatch.IsMultipleUse,
		UserScope:                 pb.CouponUserScope(couponBatch.ApplicableUserType),
		PayModeScope:              pb.CouponPayModeScope(couponBatch.ApplicablePayMode),
		ArrearCanUse:              couponBatch.ArrearCanUse,
		Scope:                     scopeDetail,
		CouponScopeDesc:           couponBatch.CouponScopeDesc,
		Applicant:                 pb.ApplicantType(couponBatch.Applicant),
		Url:                       couponBatch.Url,
		CreatedAt:                 timestamppb.New(couponBatch.CreatedAt),
		UpdatedAt:                 timestamppb.New(couponBatch.UpdatedAt),
	}, nil
}

// BuildCouponBatchWithID converts from pb.CouponBatch to the model layer type.
func BuildCouponBatchWithID(param *pb.IDCouponBatchParam) (*service.CreateCouponBatchParam, error) {
	createCouponBatchParam, err := BuildCouponBatch(param.CouponBatch)
	if err != nil {
		return nil, err
	}

	createCouponBatchParam.CouponBatch.ID = param.Id
	return createCouponBatchParam, nil
}
