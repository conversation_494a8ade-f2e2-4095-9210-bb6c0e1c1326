package adapter

import (
	"testing"
	"time"

	pb "github.com/qbox/pay-sdk/measureproxy"
	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measureproxyd/model"
)

func TestGetItemsInUseParamsRoundTrip(t *testing.T) {
	x := &model.GetItemsInUseParams{
		UID:      233,
		Begin:    time.Date(2019, 7, 1, 0, 0, 0, 0, time.Local),
		End:      time.Date(2019, 8, 1, 0, 0, 0, 0, time.Local),
		HaveZone: true,
		ZoneCode: 3001,
		Src:      pb.STAT_SRC_FUSION,
		Debug:    true,
	}

	y, err := BuildPbGetItemsInUseParams(x)
	assert.NoError(t, err)

	z, err := BuildModelGetItemsInUseParams(y)
	assert.NoError(t, err)

	assert.Equal(t, x.UID, z.UID)
	assert.Equal(t, x.Begin.<PERSON><PERSON><PERSON>(), z.Begin.UnixNano())
	assert.Equal(t, x.End.<PERSON><PERSON>ano(), z.<PERSON>.<PERSON><PERSON>ano())
	assert.Equal(t, x.Have<PERSON>one, z.HaveZone)
	assert.Equal(t, x.ZoneCode, z.ZoneCode)
	assert.Equal(t, x.Src, z.Src)
	assert.Equal(t, x.Debug, z.Debug)
}
