package action

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"
	"qiniu.io/pay/priced/action/adapter"
)

// DiffCurrentPriceWithCreatedTime 查询报价单提交前的价格与当前报价单的差异
func (a *PriceAction) DiffCurrentPriceWithCreatedTime(
	ctx context.Context,
	param *pb.UIDPriceIDScopeParam,
) (*pb.PriceDiffResp, error) {
	m, err := a.priceBizSrv.DiffCurrentPriceWithCreatedTime(ctx,
		param.GetUid(),
		param.GetPriceId(),
		adapter.BuildPriceScope(param.GetScope()),
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceDiffResp(m)
}

// DiffCostPriceWithCreatedTime 查询报价单提交前的价格与成本价的差异
func (a *PriceAction) DiffCostPriceWithCreatedTime(
	ctx context.Context,
	param *pb.UIDPriceIDScopeParam,
) (*pb.PriceDiffResp, error) {
	m, err := a.priceBizSrv.DiffCostPriceWithCreatedTime(ctx,
		param.GetUid(),
		param.GetPriceId(),
		adapter.BuildPriceScope(param.GetScope()))
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceDiffResp(m)
}
