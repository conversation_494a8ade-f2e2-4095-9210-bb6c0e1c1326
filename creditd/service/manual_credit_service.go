package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/gaea/client/operations"
	"github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

type ManualCreditService struct {
	creditDao     *dao.ManualCreditDao
	cacheExpires  time.Duration
	stateMachine  *CreditStateMachine
	paymentClient wallet.PaymentServiceClient
	walletClient  wallet.PayWalletServiceClient
	gaeaClient    *client.Gaea
}

func NewManualCreditService(
	creditDao *dao.ManualCreditDao,
	expires time.Duration,
	gaeaClient *client.Gaea,
	paymentClient wallet.PaymentServiceClient,
	walletClient wallet.PayWalletServiceClient,
) (*ManualCreditService, error) {
	stateMachine, err := NewCreditStateMachine(setTransitions())
	if err != nil {
		return nil, err
	}
	return &ManualCreditService{
		creditDao:     creditDao,
		cacheExpires:  expires,
		stateMachine:  stateMachine,
		paymentClient: paymentClient,
		walletClient:  walletClient,
		gaeaClient:    gaeaClient,
	}, nil
}

func setTransitions() []Transition {
	trans := []Transition{
		{From: enums.StatusUnActivated, To: enums.StatusValid, Event: enums.EventActivate},
		// TODO 根据需求后面不允许有修改，EventAdjustLineOrPeriod 相关条目会删除
		{From: enums.StatusUnActivated, To: enums.StatusUnActivated, Event: enums.EventAdjustLineOrPeriod},
		{From: enums.StatusUnActivated, To: enums.StatusUnAuthorized, Event: enums.EventCancel},
		{From: enums.StatusValid, To: enums.StatusValid, Event: enums.EventAdjustLineOrPeriod},
		{From: enums.StatusValid, To: enums.StatusRevoked, Event: enums.EventRevoke},
	}
	return trans
}

// DoTransaction do a transaction
func (s *ManualCreditService) DoTransaction(fn func(service *ManualCreditService) error) error {
	return s.creditDao.DoTransaction(func(dao *dao.ManualCreditDao) error {
		newService, err := NewManualCreditService(
			dao, s.cacheExpires, s.gaeaClient, s.paymentClient, s.walletClient,
		)
		if err != nil {
			return err
		}

		return fn(newService)
	})
}

// CreateCredit creates a new credit record
func (s *ManualCreditService) CreateCredit(
	ctx context.Context,
	m model.ManualCredit,
) (*model.ManualCredit, bool, error) {
	m.ID = 0
	if (m.PeriodUnit == enums.PeriodUnitDay && m.CreditPeriod > 365) ||
		(m.PeriodUnit == enums.PeriodUnitMonth && m.CreditPeriod > 12) {
		return nil, false, fmt.Errorf("invalid period value")
	}

	if m.CustomerName == "" {
		resp, err := s.gaeaClient.Operations.GetDeveloper(operations.NewGetDeveloperParamsWithContext(ctx).
			WithUID(strconv.FormatUint(m.UID, 10)))
		if err != nil {
			return nil, false, err
		}
		if resp.GetPayload() == nil {
			return nil, false, errors.New("empty payload")
		}
		if resp.GetPayload().Data == nil {
			return nil, false, errors.New(resp.GetPayload().Message)
		}

		fullName := resp.GetPayload().Data.Fullname
		m.CustomerName = fullName
	}

	currency, err := s.paymentClient.GetSingleCurrency(ctx, &wallet.UIDParam{Uid: m.UID})
	if err != nil {
		return nil, false, err
	}
	m.CurrencyType = currency.GetCurrencyType()

	isUpdateCreditLine := false
	// 若用户已绑卡，人工授信状态默认为已激活
	cards, err := s.walletClient.ListUserCards(ctx, &wallet.UIDParam{Uid: m.UID})
	if err != nil {
		return nil, false, errors.Trace(err)
	}
	if len(cards.GetUserCards()) > 0 {
		m.Status = enums.StatusValid
	} else {
		if m.Status != enums.StatusUnActivated {
			return nil, false, fmt.Errorf("init status must be unactivated")
		}
	}

	err = s.DoTransaction(func(s *ManualCreditService) error {
		// 获取 status == valid || status == unactivated 的条目
		nonTerminalCredit, err := s.creditDao.GetNonTerminalCreditByUIDWithForUpdate(m.UID)
		if err != nil && !gorm.IsRecordNotFoundError(err) {
			return errors.Trace(err)
		}
		// 判断 creditLine 是否有变化
		if gorm.IsRecordNotFoundError(err) || nonTerminalCredit.CreditLine != m.CreditLine {
			isUpdateCreditLine = true
		}
		// 若存在则根据当前状态更新状态
		if err == nil {
			updateStatusAndOpTimeParam := &model.ManualCredit{ID: nonTerminalCredit.ID}
			now := time.Now()
			switch nonTerminalCredit.Status {
			case enums.StatusValid:
				updateStatusAndOpTimeParam.Status = enums.StatusRevoked
				updateStatusAndOpTimeParam.RevokeAt = now
				m.Status = enums.StatusValid
			case enums.StatusUnActivated:
				updateStatusAndOpTimeParam.Status = enums.StatusUnAuthorized
				updateStatusAndOpTimeParam.CancelAt = now
				m.Status = enums.StatusUnActivated
			}
			err := s.creditDao.Update(updateStatusAndOpTimeParam)
			if err != nil {
				return err
			}
		}

		// 创建新的条目
		m.InitAt = time.Now().In(tz.MustLocationFromCtx(ctx))
		err = s.creditDao.Save(&m, s.cacheExpires)
		if err != nil {
			return err
		}
		return nil
	})

	return &m, isUpdateCreditLine, errors.Trace(err)
}

// GetCreditsByUIDParams defines GetCreditsByUID interface's params
type GetCreditsByUIDParams struct {
	UID    uint64
	Status []enums.CreditStatus
	Offset int
	Limit  int
	Start  *time.Time
	End    *time.Time
}

// GetCreditsByUID queries credits by uid
func (s *ManualCreditService) GetCreditsByUID(
	ctx context.Context,
	params *GetCreditsByUIDParams,
) (credits []model.ManualCredit, total uint64, err error) {
	if params.Start != nil &&
		params.End != nil &&
		params.Start.After(*params.End) {
		err = errors.Errorf("invalid start and end value")
		return
	}

	total, err = s.creditDao.CountAllByUID(params.UID, params.Status, params.Start, params.End)
	if err != nil {
		err = errors.Trace(err)
		return
	}

	daoParams := &dao.GetByUIDParams{
		UID:    params.UID,
		Status: params.Status,
		Offset: params.Offset,
		Limit:  params.Limit,
		Start:  params.Start,
		End:    params.End,
	}

	credits, err = s.creditDao.GetByUID(daoParams, s.cacheExpires)
	if err != nil {
		err = errors.Trace(err)
		return
	}
	return
}

// ListAllCreditsParam defines ListAllCredits interface's params
type ListAllCreditsParam struct {
	Status []enums.CreditStatus
	Offset int
	Limit  int
	Start  *time.Time
	End    *time.Time
}

// ListAllCredits list all credit records
func (s *ManualCreditService) ListAllCredits(
	ctx context.Context,
	params *ListAllCreditsParam,
) (credits []model.ManualCredit, total uint64, err error) {
	if params.Start != nil &&
		params.End != nil &&
		params.Start.After(*params.End) {
		err = errors.Errorf("invalid start and end value")
		return
	}

	total, err = s.creditDao.CountAll(params.Status, params.Start, params.End)
	if err != nil {
		return
	}
	daoParams := &dao.ListAllParams{
		Status: params.Status,
		Offset: params.Offset,
		Limit:  params.Limit,
		Start:  params.Start,
		End:    params.End,
	}
	credits, err = s.creditDao.ListAll(daoParams, s.cacheExpires)
	if err != nil {
		return
	}
	return
}

// UpdateCreditLineByID updates credit line by id
func (s *ManualCreditService) UpdateCreditLineByID(
	ctx context.Context,
	id uint64,
	creditLine int64,
) (*model.ManualCredit, error) {
	creditOrig, err := s.creditDao.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// invoke stateMachine
	to, err := s.stateMachine.Process(creditOrig.Status, enums.EventAdjustLineOrPeriod)
	if err != nil {
		return nil, errors.Trace(err)
	}

	creditUpdated := &model.ManualCredit{
		ID:         id,
		CreditLine: creditLine,
		Status:     to,
	}
	err = s.creditDao.Update(creditUpdated, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return creditUpdated, nil
}

// UpdateCreditPeriodByID updates credit period by id
func (s *ManualCreditService) UpdateCreditPeriodByID(
	ctx context.Context,
	id uint64,
	creditPeriod uint64,
	periodUnit enums.PeriodUnit,
) (*model.ManualCredit, error) {
	creditOrig, err := s.creditDao.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// invoke stateMachine
	to, err := s.stateMachine.Process(creditOrig.Status, enums.EventAdjustLineOrPeriod)
	if err != nil {
		return nil, errors.Trace(err)
	}

	creditUpdated := &model.ManualCredit{
		ID:           id,
		CreditPeriod: creditPeriod,
		PeriodUnit:   periodUnit,
		Status:       to,
	}
	err = s.creditDao.Update(creditUpdated, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return creditUpdated, nil
}

// UpdateCreditStatusByUID updates credit status by uid
func (s *ManualCreditService) UpdateCreditStatusByUID(
	ctx context.Context,
	uid uint64,
	event enums.Event,
) (*model.ManualCredit, error) {
	statusAndOpTimeParam := new(model.ManualCredit)

	err := s.creditDao.DoTransaction(func(tx *dao.ManualCreditDao) error {
		creditOrig, err := tx.GetNonTerminalCreditByUIDWithForUpdate(uid)
		if err != nil {
			return errors.Trace(err)
		}

		// invoke stateMachine
		to, err := s.stateMachine.Process(creditOrig.Status, event)
		if err != nil {
			return errors.Trace(err)
		}

		statusParam := &model.ManualCredit{
			ID:     creditOrig.ID,
			Status: to,
		}
		statusAndOpTimeParam = s.recordOpTime(statusParam, event)

		err = tx.Update(statusAndOpTimeParam, s.cacheExpires)
		if err != nil {
			return errors.Trace(err)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return statusAndOpTimeParam, errors.Trace(err)
}

func (s *ManualCreditService) recordOpTime(m *model.ManualCredit, event enums.Event) *model.ManualCredit {
	newCredit := &model.ManualCredit{
		ID:           m.ID,
		CreditLine:   m.CreditLine,
		CreditPeriod: m.CreditPeriod,
		PeriodUnit:   m.PeriodUnit,
		Status:       m.Status,
	}
	now := time.Now()
	switch event {
	case enums.EventActivate:
		newCredit.ActivateAt = now
	case enums.EventCancel:
		newCredit.CancelAt = now
	case enums.EventRevoke:
		newCredit.RevokeAt = now
	}
	return newCredit
}
