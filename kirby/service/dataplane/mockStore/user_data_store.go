package mockStore

import (
	"context"
	"sync"
	"time"

	"github.com/samber/lo"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/dataplaneModel"
)

type mockUserDataStore struct {
	uid uint64

	mu sync.RWMutex

	// map[month.Unix()]*monthStore
	data map[int64]*mockMonthDataStore
}

func newMockUserDataStore(uid uint64) *mockUserDataStore {
	return &mockUserDataStore{
		uid:  uid,
		data: make(map[int64]*mockMonthDataStore),
	}
}

func monthUnixOfPoint(p *kirbypb.StatPoint, loc *time.Location) int64 {
	return base.ThisMonth(p.Time.AsTime().In(loc)).Unix()
}

func (x *mockUserDataStore) ensureMonthShards(
	start time.Time,
	end time.Time,
	loc *time.Location,
) {
	x.mu.Lock()
	defer x.mu.Unlock()

	startMonth := base.ThisMonth(start.In(loc))
	for month := startMonth; month.Before(end); month = month.AddDate(0, 1, 0) {
		monthUnix := month.Unix()
		if _, ok := x.data[monthUnix]; !ok {
			x.data[monthUnix] = newMockMonthDataStore(x.uid)
		}
	}
}

func copySeriesWithoutData(x *dataplaneModel.FlattenedSeries) *dataplaneModel.FlattenedSeries {
	y := *x
	y.Data = nil
	return &y
}

func (x *mockUserDataStore) feedSeries(ctx context.Context, s *dataplaneModel.FlattenedSeries) {
	loc := tz.MustLocationFromCtx(ctx)

	// figure out list of involved months first to minimize this struct's
	// lock contention
	// longer-duration write lock is taken in individual lower-tier stores, not here
	x.ensureMonthShards(s.Start, s.End, loc)

	monthlyData := lo.GroupBy(s.Data, func(x *kirbypb.StatPoint) int64 { return monthUnixOfPoint(x, loc) })

	startMonth := base.ThisMonth(s.Start.In(loc))
	for month := startMonth; month.Before(s.End); month = month.AddDate(0, 1, 0) {
		monthUnix := month.Unix()
		ms := x.data[monthUnix] // guaranteed to exist

		subSeries := copySeriesWithoutData(s)
		subSeries.Data = monthlyData[monthUnix] // zero-value semantics is expected
		ms.feedSeries(subSeries)
	}
}

func (x *mockUserDataStore) persistAllPendingSeries() int {
	x.mu.RLock()
	defer x.mu.RUnlock()

	numSeriesProcessed := 0
	for _, ms := range x.data {
		numSeriesProcessed += ms.persistAllPendingSeries()
	}

	return numSeriesProcessed
}

func (x *mockUserDataStore) runAggregation(
	month time.Time,
	since time.Time,
	epoch time.Time,
) error {
	x.mu.Lock()
	defer x.mu.Unlock()

	monthUnix := month.Unix()
	ms, ok := x.data[monthUnix]
	if !ok {
		return nil
	}

	err := ms.runAggregation(since, epoch)
	if err != nil {
		return err
	}

	return nil
}

func (x *mockUserDataStore) listSeries(
	epoch time.Time,
	dataTimeFrom time.Time,
	dataTimeTo time.Time,
	ctimeFrom time.Time,
	ctimeTo time.Time,
	seriesKeys []string,
	mbUUIDs []string,
	qrns []string,
	rtdCodes []string,
	mms []string,
	spConditions []*dataplaneModel.SPCondition,
) *dataplaneModel.SeriesDetailList {
	x.mu.RLock()
	defer x.mu.RUnlock()

	var allResults []*dataplaneModel.SeriesDetail
	totalCount := uint64(0)
	for _, month := range base.InvolvedMonths(dataTimeFrom, dataTimeTo) {
		monthT, _ := month.TimeIn(dataTimeFrom.Location())
		monthUnix := monthT.Unix()
		ms, ok := x.data[monthUnix]
		if !ok {
			continue
		}

		monthResult := ms.listSeries(
			epoch,
			dataTimeFrom,
			dataTimeTo,
			ctimeFrom,
			ctimeTo,
			seriesKeys,
			mbUUIDs,
			qrns,
			rtdCodes,
			mms,
			spConditions,
		)
		allResults = append(allResults, monthResult.Data...)
		totalCount += monthResult.Count
	}

	return &dataplaneModel.SeriesDetailList{
		Data:  allResults,
		Count: totalCount,
	}
}

func (x *mockUserDataStore) listAggregatedSeries(
	epoch time.Time,
	dataTimeFrom time.Time,
	dataTimeTo time.Time,
	ctimeFrom time.Time,
	ctimeTo time.Time,
	aggSeriesKeys []string,
	itemCodes []string,
) *dataplaneModel.AggregatedSeriesDetailList {
	x.mu.RLock()
	defer x.mu.RUnlock()

	var allResults []*dataplaneModel.AggregatedSeriesDetail
	totalCount := uint64(0)
	for _, month := range base.InvolvedMonths(dataTimeFrom, dataTimeTo) {
		monthT, _ := month.TimeIn(dataTimeFrom.Location())
		monthUnix := monthT.Unix()
		ms, ok := x.data[monthUnix]
		if !ok {
			continue
		}

		monthResult := ms.listAggregatedSeries(
			epoch,
			dataTimeFrom,
			dataTimeTo,
			ctimeFrom,
			ctimeTo,
			aggSeriesKeys,
			itemCodes,
		)
		allResults = append(allResults, monthResult.Data...)
		totalCount += monthResult.Count
	}

	return &dataplaneModel.AggregatedSeriesDetailList{
		Data:  allResults,
		Count: totalCount,
	}
}

func (x *mockUserDataStore) queryAggregatedSeriesData(
	epoch time.Time,
	monthUnix int64,
	iz itemAndZone,
) *storedAggrSeries {
	x.mu.RLock()
	defer x.mu.RUnlock()

	ms := x.data[monthUnix]
	if ms == nil {
		return nil
	}

	return ms.queryAggregatedSeriesData(epoch, iz)
}

func (x *mockUserDataStore) hasItemDataForMonth(
	monthUnix int64,
	itemCodes []string,
	zoneCode int64,
) bool {
	x.mu.RLock()
	defer x.mu.RUnlock()

	ms := x.data[monthUnix]
	if ms == nil {
		return false
	}

	return ms.hasData(itemCodes, zoneCode)
}

func (x *mockUserDataStore) listItemsByMonthAndZone(
	monthUnix int64,
	zoneCode int64,
) []string {
	x.mu.RLock()
	defer x.mu.RUnlock()

	ms := x.data[monthUnix]
	if ms == nil {
		return nil
	}

	return ms.listItemCodesOfZone(zoneCode)
}
