package action

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/measureproxy"
	"qiniu.io/pay/measureproxyd/action/adapter"
)

// ProxyGetUserResourceTags 拉用户资源标签列表
func (a *MeasureProxyAction) ProxyGetUserResourceTags(
	ctx context.Context,
	req *pb.ProxyUserResourceTagsV1Req,
) (*pb.ProxyUserResourceTagsV1Resp, error) {
	resp, err := a.resourceTagsSrv.Query(uint32(req.Uid))
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbProxyUserResourceTagsV1Resp(resp), nil
}
