package service

import (
	"net/http"
	"time"

	"github.com/go-openapi/strfmt"

	"github.com/qbox/bo-base/v4/lock"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/pay-sdk/dict"
	priceshim "github.com/qbox/pay-sdk/v3/priceshim/client"
	wClient "github.com/qbox/pay-sdk/v3/wallet/client"
	walletpb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/config"
	"qiniu.io/pay/walletd/model"
)

// WalletBizService is business of walletd service
type WalletBizService struct {
	walletDao    *model.WalletDao
	cacheExpires time.Duration
	conf         *config.WalletdConfig
	lk           lock.Locker

	httpClient *http.Client

	priceshim  *priceshim.Priceshim
	walletV3   *wClient.Wallet
	dictClient dict.PayDictServiceClient

	// 这个客户端循环依赖本服务进程，不要直接用，请 getPaymentServiceClient 姿势使用
	payment walletpb.PaymentServiceClient
}

// NewWalletBizService is constructor of WalletBizService
func NewWalletBizService(
	conf *config.WalletdConfig,
	walletDao *model.WalletDao,
	httpClient *http.Client,
	userLocker lock.Locker,
	expires time.Duration,
) (*WalletBizService, error) {
	dictConn, err := rpc.GrpcConnectWithName(conf.Services.Dict, rpc.ServicePayV4Dict, conf.RPC.Keepalive.Client)
	if err != nil {
		return nil, err
	}

	walletTr, err := rpc.NewSwaggerTransport(conf.Services.WalletBiz, httpClient)
	if err != nil {
		return nil, err
	}

	priceshimTr, err := rpc.NewSwaggerTransport(conf.Services.PriceShim, httpClient)
	if err != nil {
		return nil, err
	}

	return &WalletBizService{
		walletDao:    walletDao,
		cacheExpires: expires,
		conf:         conf,
		lk:           userLocker,

		httpClient: httpClient,
		priceshim:  priceshim.New(priceshimTr, strfmt.Default),
		walletV3:   wClient.New(walletTr, strfmt.Default),
		dictClient: dict.NewPayDictServiceClient(dictConn),
		payment:    nil, // break cyclic dep
	}, nil
}

// DoTransaction do a transaction
func (s *WalletBizService) DoTransaction(fn func(*WalletBizService) error) error {
	return s.walletDao.DoTransaction(func(dao *model.WalletDao) error {
		newService, err := NewWalletBizService(s.conf, dao, s.httpClient, s.lk, s.cacheExpires)
		if err != nil {
			return err
		}

		return fn(newService)
	})
}

func (s *WalletBizService) GetDictClient() dict.PayDictServiceClient {
	return s.dictClient
}

func (s *WalletBizService) getPaymentServiceClient() (walletpb.PaymentServiceClient, error) {
	if s.payment != nil {
		return s.payment, nil
	}

	// payment service 的提供者是自己，学着 grpc gateway 一样回连
	// 这个动作只能在服务起来之后进行，否则由于循环依赖，会导致服务无法启动
	paymentConn, err := rpc.GrpcConnectWithName(s.conf.RPC.ForwardAddr, rpc.ServicePayV4Wallet, s.conf.RPC.Keepalive.Client)
	if err != nil {
		return nil, err
	}

	s.payment = walletpb.NewPaymentServiceClient(paymentConn)

	return s.payment, nil
}

// SetPaymentServiceClient 设置本 service 的 payment service client，仅供单测使用
func (s *WalletBizService) SetPaymentServiceClient(c walletpb.PaymentServiceClient) {
	s.payment = c
}
