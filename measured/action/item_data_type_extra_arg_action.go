package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/action/adapter"
)

// impl PayMeasureServiceServer for MeasureAction

// GetItemDataTypeExtraArgByID 根据 ID 查询单个计费项数据类型计量查询附加参数
func (a *MeasureAction) GetItemDataTypeExtraArgByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.ItemDataTypeExtraArg, error) {
	obj, err := a.srv.GetItemDataTypeExtraArgByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeExtraArg(obj)
}

// ListItemDataTypeExtraArgsByItemDataTypeID 根据计费项数据类型 ID 列出计量查询附加参数列表
func (a *MeasureAction) ListItemDataTypeExtraArgsByItemDataTypeID(
	ctx context.Context,
	p *pb.IDPagingParam,
) (*pb.ItemDataTypeExtraArgList, error) {
	count, err := a.srv.CountItemDataTypeExtraArgsByItemDataTypeID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(p)
	objs, err := a.srv.ListItemDataTypeExtraArgsByItemDataTypeID(ctx, p.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemDataTypeExtraArgList{
		Count:                 count,
		ItemDataTypeExtraArgs: make([]*pb.ItemDataTypeExtraArg, len(objs)),
	}
	for i, obj := range objs {
		result, err := adapter.BuildPbItemDataTypeExtraArg(&obj)
		if err != nil {
			return nil, err
		}
		list.ItemDataTypeExtraArgs[i] = result
	}
	return list, nil
}

// CountItemDataTypeExtraArgsByItemDataTypeID 根据计费项数据类型 ID 获取计量查询附加参数数量
func (a *MeasureAction) CountItemDataTypeExtraArgsByItemDataTypeID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.srv.CountItemDataTypeExtraArgsByItemDataTypeID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// CreateItemDataTypeExtraArg 创建一个计费项数据类型计量查询附加参数
func (a *MeasureAction) CreateItemDataTypeExtraArg(
	ctx context.Context,
	item *pb.ItemDataTypeExtraArg,
) (*pb.ItemDataTypeExtraArg, error) {
	obj, err := adapter.BuildModelItemDataTypeExtraArg(item)
	if err != nil {
		return nil, err
	}
	obj, err = a.srv.CreateItemDataTypeExtraArg(ctx, obj)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeExtraArg(obj)
}

// SyncItemDataTypeExtraArgs 为给定的计费项数据类型 ID 同步计费项数据类型计量查询附加参数配置
func (a *MeasureAction) SyncItemDataTypeExtraArgs(
	ctx context.Context,
	p *pb.SyncItemDataTypeExtraArgsParam,
) (*empty.Empty, error) {
	obj, err := adapter.BuildServiceSyncItemDataTypeExtraArgsParam(p)
	if err != nil {
		return nil, err
	}
	err = a.srv.SyncItemDataTypeExtraArgs(ctx, obj)
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}

// DeleteItemDataTypeExtraArgByID 根据 ID 删除一个计费项数据类型计量查询附加参数
func (a *MeasureAction) DeleteItemDataTypeExtraArgByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.ItemDataTypeExtraArg, error) {
	obj, err := a.srv.DeleteItemDataTypeExtraArgByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeExtraArg(obj)
}
