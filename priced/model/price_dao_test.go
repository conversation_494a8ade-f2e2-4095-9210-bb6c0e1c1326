package model_test

import (
	"testing"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/priced/model"
)

type sandbox struct {
	testWrap *test.Wrap
	priceDao *model.PriceDao
}

func buildSandbox(t *testing.T) *sandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in price/model return error")
	}

	return &sandbox{
		testWrap: testWrap,
		priceDao: model.NewPriceDao(testWrap.BaseDao()),
	}
}
