package service_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
)

func TestPriceItemCRUD(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	const priceID = 1
	priceItems := []model.PriceItem{
		{
			PriceID:    priceID,
			ItemID:     1,
			ZoneID:     1,
			IsDisabled: true,
			UnitRate:   1000,
			EffectTime: base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local)),
			DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.Local)),
		},
		{
			PriceID:    priceID,
			ItemID:     2,
			ZoneID:     1,
			IsDisabled: true,
			UnitRate:   1024,
			EffectTime: base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local)),
			DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.Local)),
		},
		{
			PriceID:    priceID,
			ItemID:     3,
			ZoneID:     1,
			IsDisabled: false,
			UnitRate:   1000,
			EffectTime: base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local)),
			DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.Local)),
		},
		{
			PriceID:    priceID + 1,
			ItemID:     1,
			ZoneID:     1,
			IsDisabled: false,
			UnitRate:   1024,
			EffectTime: base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local)),
			DeadTime:   base.NewHNS(time.Date(2019, 1, 5, 0, 0, 0, 0, time.Local)),
		},
	}

	assertFields := func(expect, actual *model.PriceItem, msgAndArgs ...any) {
		assert.Equal(t, expect.PriceID, actual.PriceID, msgAndArgs...)
		assert.Equal(t, expect.ItemID, actual.ItemID, msgAndArgs...)
		assert.Equal(t, expect.ZoneID, actual.ZoneID, msgAndArgs...)
		assert.Equal(t, expect.IsDisabled, actual.IsDisabled, msgAndArgs...)
		assert.Equal(t, expect.UnitRate, actual.UnitRate, msgAndArgs...)
	}

	ctx := context.TODO()

	for i, priceItem := range priceItems {
		// create
		m, err := priceService.CreatePriceItem(ctx, &priceItem)
		if assert.NoError(t, err) {
			assert.NotZero(t, m.ID, "CreatePriceItem return zero id")
			assertFields(&priceItem, m, fmt.Sprintf("Unmatched item, item_id:%d", priceItem.ItemID))
		}
		priceItems[i].ID = m.ID

		// query
		priceItemID := m.ID
		m, err = priceService.GetPriceItemByID(ctx, priceItemID)
		if assert.NoError(t, err) {
			assertFields(&priceItem, m, fmt.Sprintf("Unmatched item, item_id:%d", priceItem.ItemID))
		}

		// update
		m.UnitRate = 1025
		updatedPriceItem, err := priceService.UpdatePriceItemByID(ctx, priceItemID, m)
		if assert.NoError(t, err) {
			assertFields(m, updatedPriceItem)
		}
		priceItems[i].UnitRate = m.UnitRate
	}

	// list all price items
	priceItemList, err := priceService.ListAllPriceItems(ctx, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, priceItemList, len(priceItems))
	}

	// list all price items by price_id
	priceItemListWithSamePriceItem, err := priceService.ListAllPriceItemsByPriceID(ctx, priceID, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, priceItemListWithSamePriceItem, len(priceItems)-1)
		for _, item := range priceItemListWithSamePriceItem {
			assert.Equal(t, uint64(priceID), item.PriceID, "price_id should be the same")

			for _, expectedItem := range priceItems {
				if item.ID == expectedItem.ID {
					assertFields(&expectedItem, &item, fmt.Sprintf("Unmatched item, item_id:%d", item.ItemID))
					break
				}
			}
		}
	}

	// delete all price items
	for _, priceItem := range priceItems {
		deletedPriceItem, err := priceService.DeletePriceItemByID(ctx, priceItem.ID)
		if assert.NoError(t, err) {
			assertFields(deletedPriceItem, &priceItem)
		}
	}

	// check price item list, should be empty
	l, err := priceService.ListAllPriceItems(ctx, 0, 10)
	if assert.NoError(t, err) {
		assert.Empty(t, l)
	}

	// de-duplication
	for _, priceItem := range priceItems {
		_, err := priceService.DeletePriceItemByID(ctx, priceItem.ID)
		assert.Error(t, err)
	}
}
