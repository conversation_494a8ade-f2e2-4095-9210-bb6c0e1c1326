package doraItemsQuerier

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/legacystat/common"

	"qiniu.io/pay/measured/model"
)

func TestItemCmdsMapFromItemExtraArgs(t *testing.T) {
	testcases := []struct {
		desc             string
		args             []model.ItemExtraArg
		itemMetadataDict map[uint64]itemEntry
		ok               bool
		expected         map[itemEntry][]common.LegacyDoraCmd
	}{
		{
			desc:             "empty 1",
			args:             nil,
			itemMetadataDict: nil,
			ok:               true,
			expected:         map[itemEntry][]common.LegacyDoraCmd{},
		},
		{
			desc:             "empty 2",
			args:             []model.ItemExtraArg{},
			itemMetadataDict: nil,
			ok:               true,
			expected:         map[itemEntry][]common.LegacyDoraCmd{},
		},
		{
			desc: "one",
			args: []model.ItemExtraArg{
				{ItemID: 1, Key: "stat_key", Value: "xx"},
				{ItemID: 1, Key: "stat_query", Value: "cmd=foo&cmd=foo:20210401"},
			},
			itemMetadataDict: map[uint64]itemEntry{
				1: {ItemCode: "aaa:foo", ProductCode: "aaa"},
			},
			ok: true,
			expected: map[itemEntry][]common.LegacyDoraCmd{
				{ItemCode: "aaa:foo", ProductCode: "aaa"}: {
					{Key: "xx", Cmd: "foo"},
					{Key: "xx", Cmd: "foo"},
				},
			},
		},
		{
			desc: "real world data",
			args: []model.ItemExtraArg{
				{ID: 89808, ItemID: 44913, Key: "stat_key", Value: "fopg"},
				{ID: 89811, ItemID: 44913, Key: "stat_query", Value: "cmd=AVFAST_AVFAST_2K"},
				{ID: 90651, ItemID: 44913, Key: "stat_select", Value: "value"},
				{ID: 90654, ItemID: 44913, Key: "stat_expr", Value: "value"},
				{ID: 89814, ItemID: 44916, Key: "stat_key", Value: "fopg"},
				{ID: 89817, ItemID: 44916, Key: "stat_query", Value: "cmd=AVFAST_AVFAST_HD"},
				{ID: 90657, ItemID: 44916, Key: "stat_select", Value: "value"},
				{ID: 90660, ItemID: 44916, Key: "stat_expr", Value: "value"},
				{ID: 89820, ItemID: 44919, Key: "stat_key", Value: "fopg"},
				{ID: 89823, ItemID: 44919, Key: "stat_query", Value: "cmd=AVFAST_AVFAST_SD"},
				{ID: 90663, ItemID: 44919, Key: "stat_select", Value: "value"},
				{ID: 90666, ItemID: 44919, Key: "stat_expr", Value: "value"},
				{ID: 10068, ItemID: 5271, Key: "stat_key", Value: "merge"},
				{ID: 10647, ItemID: 5271, Key: "stat_query", Value: "cmd=common:AVA-ARGUS-UTIL_PULP-CERTAIN&cmd=common:AVA-ARGUS-UTIL_PULP-DEPEND:20180701&cmd=fopg:IMAGE-CENSOR_CENSOR_PULP&cmd=common:IMAGE-CENSOR_CENSOR_PULP&cmd=fopg:QPULP_CENSOR_PULP&cmd=fopg:VIDEO-CENSOR_CENSOR_PULP:20190901&cmd=common:VIDEO-CENSOR_CENSOR_PULP:20190901&cmd=fopg:IMAGE-CENSOR_CENSOR_PULP_TUPU&cmd=common:IMAGE-CENSOR_CENSOR_PULP_TUPU&cmd=fopg:QPULP_CENSOR_PULP_TUPU&cmd=fopg:VIDEO-CENSOR_CENSOR_PULP_TUPU&cmd=common:VIDEO-CENSOR_CENSOR_PULP_TUPU&cmd=fopg:VIDEO-CENSOR_CENSOR_PULP_ISM&cmd=common:VIDEO-CENSOR_CENSOR_PULP_ISM&cmd=fopg:IMAGE-CENSOR_CENSOR_PULP_ISM&cmd=common:IMAGE-CENSOR_CENSOR_PULP_ISM&cmd=fopg:QPULP_CENSOR_PULP_ISM"},
				{ID: 90111, ItemID: 5271, Key: "stat_select", Value: "value"},
				{ID: 90114, ItemID: 5271, Key: "stat_expr", Value: "value"},
				{ID: 10134, ItemID: 5307, Key: "stat_key", Value: "merge"},
				{ID: 10923, ItemID: 5307, Key: "stat_query", Value: "cmd=common:AVA-ARGUS-UTIL_FACE_SEARCH_POLITICIAN-CERTAIN&cmd=common:AVA-ARGUS-UTIL_FACE_SEARCH_POLITICIAN-DEPEND:20180701&cmd=fopg:IMAGE-CENSOR_CENSOR_POLITICIAN&cmd=common:IMAGE-CENSOR_CENSOR_POLITICIAN&cmd=fopg:QPOLITICIAN_CENSOR_POLITICIAN&cmd=fopg:VIDEO-CENSOR_CENSOR_POLITICIAN:20190901&cmd=common:VIDEO-CENSOR_CENSOR_POLITICIAN:20190901&cmd=fopg:IMAGE-CENSOR_CENSOR_POLITICIAN_TUPU&cmd=common:IMAGE-CENSOR_CENSOR_POLITICIAN_TUPU&cmd=fopg:QPOLITICIAN_CENSOR_POLITICIAN_TUPU&cmd=fopg:VIDEO-CENSOR_CENSOR_POLITICIAN_TUPU&cmd=common:VIDEO-CENSOR_CENSOR_POLITICIAN_TUPU"},
				{ID: 90279, ItemID: 5307, Key: "stat_select", Value: "value"},
				{ID: 90282, ItemID: 5307, Key: "stat_expr", Value: "value"},
				{ID: 10185, ItemID: 5382, Key: "stat_key", Value: "merge"},
				{ID: 10998, ItemID: 5382, Key: "stat_query", Value: "cmd=common:AVA-ARGUS-UTIL_TERROR-CERTAIN&cmd=common:AVA-ARGUS-UTIL_TERROR-DEPEND:20180701&cmd=fopg:IMAGE-CENSOR_CENSOR_TERROR&cmd=common:IMAGE-CENSOR_CENSOR_TERROR&cmd=fopg:QTERROR_CENSOR_TERROR&cmd=fopg:VIDEO-CENSOR_CENSOR_TERROR:20190901&cmd=common:VIDEO-CENSOR_CENSOR_TERROR:20190901&cmd=fopg:IMAGE-CENSOR_CENSOR_TERROR_TUPU&cmd=common:IMAGE-CENSOR_CENSOR_TERROR_TUPU&cmd=fopg:QTERROR_CENSOR_TERROR_TUPU&cmd=fopg:VIDEO-CENSOR_CENSOR_TERROR_TUPU&cmd=common:VIDEO-CENSOR_CENSOR_TERROR_TUPU"},
				{ID: 90321, ItemID: 5382, Key: "stat_select", Value: "value"},
				{ID: 90324, ItemID: 5382, Key: "stat_expr", Value: "value"},
				{ID: 9999, ItemID: 5160, Key: "stat_key", Value: "fopg"},
				{ID: 10623, ItemID: 5160, Key: "stat_query", Value: "cmd=H265-HD-HFR&pipetype=private"},
				{ID: 90285, ItemID: 5160, Key: "stat_select", Value: "value"},
				{ID: 90288, ItemID: 5160, Key: "stat_expr", Value: "value"},
				{ID: 9786, ItemID: 5196, Key: "stat_key", Value: "fopg"},
				{ID: 10608, ItemID: 5196, Key: "stat_query", Value: "cmd=H265-4K-HFR&pipetype=private"},
				{ID: 90225, ItemID: 5196, Key: "stat_select", Value: "value"},
				{ID: 90228, ItemID: 5196, Key: "stat_expr", Value: "value"},
			},
			itemMetadataDict: map[uint64]itemEntry{
				5160:  {ItemCode: "mps:H265-HD-HFR", ProductCode: "mps"},
				5196:  {ItemCode: "mps:H265-4K-HFR", ProductCode: "mps"},
				5271:  {ItemCode: "mps:pulp_certain", ProductCode: "atlab"},
				5307:  {ItemCode: "mps:politician_certain", ProductCode: "atlab"},
				5382:  {ItemCode: "mps:terror_certain", ProductCode: "atlab"},
				44913: {ItemCode: "mps:avfast:2K", ProductCode: "mps"},
				44916: {ItemCode: "mps:avfast:HD", ProductCode: "mps"},
				44919: {ItemCode: "mps:avfast:SD", ProductCode: "mps"},
			},
			ok: true,
			expected: map[itemEntry][]common.LegacyDoraCmd{
				{ItemCode: "mps:H265-HD-HFR", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "H265-HD-HFR"},
				},
				{ItemCode: "mps:H265-4K-HFR", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "H265-4K-HFR"},
				},
				{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}: {
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_PULP-CERTAIN"},
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_PULP-DEPEND"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_PULP"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_PULP"},
					{Key: "fopg", Cmd: "QPULP_CENSOR_PULP"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_PULP"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_PULP"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_PULP_TUPU"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_PULP_TUPU"},
					{Key: "fopg", Cmd: "QPULP_CENSOR_PULP_TUPU"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_PULP_TUPU"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_PULP_TUPU"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_PULP_ISM"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_PULP_ISM"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_PULP_ISM"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_PULP_ISM"},
					{Key: "fopg", Cmd: "QPULP_CENSOR_PULP_ISM"},
				},
				{ItemCode: "mps:politician_certain", ProductCode: "atlab"}: {
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_FACE_SEARCH_POLITICIAN-CERTAIN"},
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_FACE_SEARCH_POLITICIAN-DEPEND"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_POLITICIAN"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_POLITICIAN"},
					{Key: "fopg", Cmd: "QPOLITICIAN_CENSOR_POLITICIAN"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_POLITICIAN"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_POLITICIAN"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_POLITICIAN_TUPU"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_POLITICIAN_TUPU"},
					{Key: "fopg", Cmd: "QPOLITICIAN_CENSOR_POLITICIAN_TUPU"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_POLITICIAN_TUPU"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_POLITICIAN_TUPU"},
				},
				{ItemCode: "mps:terror_certain", ProductCode: "atlab"}: {
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_TERROR-CERTAIN"},
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_TERROR-DEPEND"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_TERROR"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_TERROR"},
					{Key: "fopg", Cmd: "QTERROR_CENSOR_TERROR"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_TERROR"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_TERROR"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_TERROR_TUPU"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_TERROR_TUPU"},
					{Key: "fopg", Cmd: "QTERROR_CENSOR_TERROR_TUPU"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_TERROR_TUPU"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_TERROR_TUPU"},
				},
				{ItemCode: "mps:avfast:2K", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "AVFAST_AVFAST_2K"},
				},
				{ItemCode: "mps:avfast:HD", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "AVFAST_AVFAST_HD"},
				},
				{ItemCode: "mps:avfast:SD", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "AVFAST_AVFAST_SD"},
				},
			},
		},
		{
			desc: "缺 item 元数据",
			args: []model.ItemExtraArg{
				{ID: 89808, ItemID: 44913, Key: "stat_key", Value: "fopg"},
				{ID: 89811, ItemID: 44913, Key: "stat_query", Value: "cmd=AVFAST_AVFAST_2K"},
				{ID: 90651, ItemID: 44913, Key: "stat_select", Value: "value"},
				{ID: 90654, ItemID: 44913, Key: "stat_expr", Value: "value"},
				{ID: 89814, ItemID: 44916, Key: "stat_key", Value: "fopg"},
				{ID: 89817, ItemID: 44916, Key: "stat_query", Value: "cmd=AVFAST_AVFAST_HD"},
				{ID: 90657, ItemID: 44916, Key: "stat_select", Value: "value"},
				{ID: 90660, ItemID: 44916, Key: "stat_expr", Value: "value"},
			},
			itemMetadataDict: map[uint64]itemEntry{
				44913: {ItemCode: "mps:avfast:2K", ProductCode: "mps"},
				44919: {ItemCode: "mps:avfast:SD", ProductCode: "mps"},
			},
			ok: false,
		},
		{
			desc: "缺 stat_key",
			args: []model.ItemExtraArg{
				{ID: 89811, ItemID: 44913, Key: "stat_query", Value: "cmd=AVFAST_AVFAST_2K"},
				{ID: 90651, ItemID: 44913, Key: "stat_select", Value: "value"},
				{ID: 90654, ItemID: 44913, Key: "stat_expr", Value: "value"},
			},
			itemMetadataDict: map[uint64]itemEntry{
				44913: {ItemCode: "mps:avfast:2K", ProductCode: "mps"},
			},
			ok: false,
		},
		{
			desc: "缺 stat_query",
			args: []model.ItemExtraArg{
				{ID: 89808, ItemID: 44913, Key: "stat_key", Value: "fopg"},
				{ID: 90651, ItemID: 44913, Key: "stat_select", Value: "value"},
				{ID: 90654, ItemID: 44913, Key: "stat_expr", Value: "value"},
			},
			itemMetadataDict: map[uint64]itemEntry{
				44913: {ItemCode: "mps:avfast:2K", ProductCode: "mps"},
			},
			ok: false,
		},
		{
			desc: "stat_query 格式非法",
			args: []model.ItemExtraArg{
				{ID: 89808, ItemID: 44913, Key: "stat_key", Value: "fopg"},
				{ID: 89811, ItemID: 44913, Key: "stat_query", Value: "cmd=AVFAST_AVFAST_2K%2g"},
				{ID: 90651, ItemID: 44913, Key: "stat_select", Value: "value"},
				{ID: 90654, ItemID: 44913, Key: "stat_expr", Value: "value"},
			},
			itemMetadataDict: map[uint64]itemEntry{
				44913: {ItemCode: "mps:avfast:2K", ProductCode: "mps"},
			},
			ok: false,
		},
	}

	for _, tc := range testcases {
		actual, err := itemCmdsMapFromItemExtraArgs(tc.args, tc.itemMetadataDict)
		if tc.ok {
			assert.NoError(t, err, tc.desc)
			assert.Equal(t, itemCmdsMap(tc.expected), actual, tc.desc)
		} else {
			assert.Error(t, err, tc.desc)
			assert.Nil(t, actual, tc.desc)
		}
	}
}

func TestDoraCmdsItemsDictFromItemCmdsMap(t *testing.T) {
	testcases := []struct {
		desc     string
		input    itemCmdsMap
		expected doraCmdsItemsDict
	}{
		{
			desc:     "empty",
			input:    itemCmdsMap{},
			expected: doraCmdsItemsDict{},
		},
		{
			desc: "real world data",
			input: itemCmdsMap{
				{ItemCode: "mps:H265-HD-HFR", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "H265-HD-HFR"},
				},
				{ItemCode: "mps:H265-4K-HFR", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "H265-4K-HFR"},
				},
				{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}: {
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_PULP-CERTAIN"},
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_PULP-DEPEND"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_PULP"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_PULP"},
					{Key: "fopg", Cmd: "QPULP_CENSOR_PULP"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_PULP"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_PULP"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_PULP_TUPU"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_PULP_TUPU"},
					{Key: "fopg", Cmd: "QPULP_CENSOR_PULP_TUPU"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_PULP_TUPU"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_PULP_TUPU"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_PULP_ISM"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_PULP_ISM"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_PULP_ISM"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_PULP_ISM"},
					{Key: "fopg", Cmd: "QPULP_CENSOR_PULP_ISM"},
				},
				{ItemCode: "mps:politician_certain", ProductCode: "atlab"}: {
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_FACE_SEARCH_POLITICIAN-CERTAIN"},
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_FACE_SEARCH_POLITICIAN-DEPEND"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_POLITICIAN"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_POLITICIAN"},
					{Key: "fopg", Cmd: "QPOLITICIAN_CENSOR_POLITICIAN"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_POLITICIAN"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_POLITICIAN"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_POLITICIAN_TUPU"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_POLITICIAN_TUPU"},
					{Key: "fopg", Cmd: "QPOLITICIAN_CENSOR_POLITICIAN_TUPU"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_POLITICIAN_TUPU"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_POLITICIAN_TUPU"},
				},
				{ItemCode: "mps:terror_certain", ProductCode: "atlab"}: {
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_TERROR-CERTAIN"},
					{Key: "common", Cmd: "AVA-ARGUS-UTIL_TERROR-DEPEND"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_TERROR"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_TERROR"},
					{Key: "fopg", Cmd: "QTERROR_CENSOR_TERROR"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_TERROR"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_TERROR"},
					{Key: "fopg", Cmd: "IMAGE-CENSOR_CENSOR_TERROR_TUPU"},
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_TERROR_TUPU"},
					{Key: "fopg", Cmd: "QTERROR_CENSOR_TERROR_TUPU"},
					{Key: "fopg", Cmd: "VIDEO-CENSOR_CENSOR_TERROR_TUPU"},
					{Key: "common", Cmd: "VIDEO-CENSOR_CENSOR_TERROR_TUPU"},
				},
				{ItemCode: "mps:avfast:2K", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "AVFAST_AVFAST_2K"},
				},
				{ItemCode: "mps:avfast:HD", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "AVFAST_AVFAST_HD"},
				},
				{ItemCode: "mps:avfast:SD", ProductCode: "mps"}: {
					{Key: "fopg", Cmd: "AVFAST_AVFAST_SD"},
				},
			},
			expected: doraCmdsItemsDict{
				"common:AVA-ARGUS-UTIL_FACE_SEARCH_POLITICIAN-CERTAIN": {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"common:AVA-ARGUS-UTIL_FACE_SEARCH_POLITICIAN-DEPEND":  {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"common:AVA-ARGUS-UTIL_PULP-CERTAIN":                   {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"common:AVA-ARGUS-UTIL_PULP-DEPEND":                    {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"common:AVA-ARGUS-UTIL_TERROR-CERTAIN":                 {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"common:AVA-ARGUS-UTIL_TERROR-DEPEND":                  {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"common:IMAGE-CENSOR_CENSOR_POLITICIAN":                {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"common:IMAGE-CENSOR_CENSOR_POLITICIAN_TUPU":           {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"common:IMAGE-CENSOR_CENSOR_PULP":                      {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"common:IMAGE-CENSOR_CENSOR_PULP_ISM":                  {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"common:IMAGE-CENSOR_CENSOR_PULP_TUPU":                 {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"common:IMAGE-CENSOR_CENSOR_TERROR":                    {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"common:IMAGE-CENSOR_CENSOR_TERROR_TUPU":               {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"common:VIDEO-CENSOR_CENSOR_POLITICIAN":                {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"common:VIDEO-CENSOR_CENSOR_POLITICIAN_TUPU":           {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"common:VIDEO-CENSOR_CENSOR_PULP":                      {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"common:VIDEO-CENSOR_CENSOR_PULP_ISM":                  {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"common:VIDEO-CENSOR_CENSOR_PULP_TUPU":                 {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"common:VIDEO-CENSOR_CENSOR_TERROR":                    {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"common:VIDEO-CENSOR_CENSOR_TERROR_TUPU":               {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"fopg:AVFAST_AVFAST_2K":                                {{ItemCode: "mps:avfast:2K", ProductCode: "mps"}},
				"fopg:AVFAST_AVFAST_HD":                                {{ItemCode: "mps:avfast:HD", ProductCode: "mps"}},
				"fopg:AVFAST_AVFAST_SD":                                {{ItemCode: "mps:avfast:SD", ProductCode: "mps"}},
				"fopg:H265-4K-HFR":                                     {{ItemCode: "mps:H265-4K-HFR", ProductCode: "mps"}},
				"fopg:H265-HD-HFR":                                     {{ItemCode: "mps:H265-HD-HFR", ProductCode: "mps"}},
				"fopg:IMAGE-CENSOR_CENSOR_POLITICIAN":                  {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"fopg:IMAGE-CENSOR_CENSOR_POLITICIAN_TUPU":             {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"fopg:IMAGE-CENSOR_CENSOR_PULP":                        {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"fopg:IMAGE-CENSOR_CENSOR_PULP_ISM":                    {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"fopg:IMAGE-CENSOR_CENSOR_PULP_TUPU":                   {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"fopg:IMAGE-CENSOR_CENSOR_TERROR":                      {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"fopg:IMAGE-CENSOR_CENSOR_TERROR_TUPU":                 {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"fopg:QPOLITICIAN_CENSOR_POLITICIAN":                   {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"fopg:QPOLITICIAN_CENSOR_POLITICIAN_TUPU":              {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"fopg:QPULP_CENSOR_PULP":                               {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"fopg:QPULP_CENSOR_PULP_ISM":                           {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"fopg:QPULP_CENSOR_PULP_TUPU":                          {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"fopg:QTERROR_CENSOR_TERROR":                           {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"fopg:QTERROR_CENSOR_TERROR_TUPU":                      {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"fopg:VIDEO-CENSOR_CENSOR_POLITICIAN":                  {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"fopg:VIDEO-CENSOR_CENSOR_POLITICIAN_TUPU":             {{ItemCode: "mps:politician_certain", ProductCode: "atlab"}},
				"fopg:VIDEO-CENSOR_CENSOR_PULP":                        {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"fopg:VIDEO-CENSOR_CENSOR_PULP_ISM":                    {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"fopg:VIDEO-CENSOR_CENSOR_PULP_TUPU":                   {{ItemCode: "mps:pulp_certain", ProductCode: "atlab"}},
				"fopg:VIDEO-CENSOR_CENSOR_TERROR":                      {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
				"fopg:VIDEO-CENSOR_CENSOR_TERROR_TUPU":                 {{ItemCode: "mps:terror_certain", ProductCode: "atlab"}},
			},
		},
	}

	for _, tc := range testcases {
		actual := doraCmdsItemsDictFromItemCmdsMap(tc.input)
		assert.Equal(t, tc.expected, actual, tc.desc)
	}
}
