package action

import (
	"github.com/qbox/bo-base/v4/action"
	pb "github.com/qbox/pay-sdk/qvmprice"

	"qiniu.io/pay/qvmprice/service"
)

type QVMPriceAction struct {
	*action.BaseAction
	pb.UnimplementedQVMPriceServiceServer

	srv *service.QVMPriceService
}

var _ pb.QVMPriceServiceServer = (*QVMPriceAction)(nil)

func NewQVMPriceAction(
	defaultPageSize uint64,
	srv *service.QVMPriceService,
) *QVMPriceAction {
	return &QVMPriceAction{
		BaseAction: action.NewBaseAction(defaultPageSize),
		srv:        srv,
	}
}
