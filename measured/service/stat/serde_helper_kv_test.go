package stat

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/vmihailenco/msgpack"
)

func TestKVMsgpackRoundTrip(t *testing.T) {
	a := []*kv{
		{K: "foo", V: "bar"},
		{K: "233", V: "quux"},
	}

	b, err := msgpack.Marshal(a)
	assert.NoError(t, err)

	// verify apparent structure of serialized KV pair is 2-tuple
	{
		var c any
		err := msgpack.Unmarshal(b, &c)
		assert.NoError(t, err)

		apparent := []any{
			[]any{"foo", "bar"},
			[]any{"233", "quux"},
		}
		assert.Equal(t, apparent, c)
	}

	// verify round-trip
	{
		var c []*kv
		err := msgpack.Unmarshal(b, &c)
		assert.NoError(t, err)
		assert.Equal(t, a, c)
	}
}
