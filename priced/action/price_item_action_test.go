package action_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/pay-sdk/price"

	"github.com/stretchr/testify/assert"
)

func TestPriceItemAction(t *testing.T) {
	sandbox := buildSandbox(t)

	priceClient := sandbox.priceClient

	const priceId = 1
	effectTime := timestamppb.New(time.Date(2018, 1, 5, 0, 0, 0, 0, time.Local))
	deadTime := timestamppb.New(time.Date(2019, 1, 5, 0, 0, 0, 0, time.Local))
	priceItems := []price.PriceItem{
		{
			PriceId:    priceId,
			ItemId:     1,
			IsDisabled: true,
			UnitRate:   1000,
			EffectTime: effectTime,
			DeadTime:   deadTime,
		},
		{
			PriceId:    priceId,
			ItemId:     2,
			IsDisabled: true,
			UnitRate:   1024,
			EffectTime: effectTime,
			DeadTime:   deadTime,
		},
		{
			PriceId:    priceId,
			ItemId:     3,
			IsDisabled: false,
			UnitRate:   1000,
			EffectTime: effectTime,
			DeadTime:   deadTime,
		},
	}

	assertFields := func(expect, actual *price.PriceItem, msgAndArgs ...any) {
		assert.Equal(t, expect.PriceId, actual.PriceId, msgAndArgs...)
		assert.Equal(t, expect.ItemId, actual.ItemId, msgAndArgs...)
		assert.Equal(t, expect.IsDisabled, actual.IsDisabled, msgAndArgs...)
		assert.Equal(t, expect.UnitRate, actual.UnitRate, msgAndArgs...)
	}

	for i, priceItem := range priceItems {
		// create
		m, err := priceClient.CreatePriceItem(context.Background(), &priceItem)
		if assert.NoError(t, err) {
			assert.NotZero(t, m.Id, "CreatePriceItem return zero id")
			assertFields(&priceItem, m, fmt.Sprintf("Unmatched item, item_id:%d", priceItem.ItemId))
		}
		priceItems[i].Id = m.Id

		// query
		priceItemId := m.Id
		m, err = priceClient.GetPriceItemByID(context.Background(), &price.IDParam{Id: priceItemId})
		if assert.NoError(t, err) {
			assertFields(&priceItem, m, fmt.Sprintf("Unmatched item, item_id:%d", priceItem.ItemId))
		}

		// update
		m.UnitRate = 1025
		updatedPriceItem, err := priceClient.UpdatePriceItemByID(context.Background(), &price.IDPriceItemParam{
			Id:        priceItemId,
			PriceItem: m,
		})
		if assert.NoError(t, err) {
			assertFields(m, updatedPriceItem)
			priceItems[i].UnitRate = m.UnitRate
		}
	}

	// list all price items
	list, err := priceClient.ListAllPriceItems(context.Background(), &price.PagingParam{
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Len(t, priceItems, int(list.GetCount()))
		for _, item := range list.GetPriceItems() {
			for _, expectedItem := range priceItems {
				if item.Id == expectedItem.Id {
					assertFields(&expectedItem, item)
					break
				}
			}
		}
	}

	// list all price items by price_id
	list, err = priceClient.ListAllPriceItemsByPriceID(context.Background(), &price.IDPagingParam{
		Id:       priceId,
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Len(t, priceItems, int(list.GetCount()))
		for _, item := range list.GetPriceItems() {
			for _, expectedItem := range priceItems {
				if item.Id == expectedItem.Id {
					assertFields(&expectedItem, item, fmt.Sprintf("Unmatched item, item_id:%d", item.ItemId))
					break
				}
			}
		}
	}

	// delete all price items
	for _, priceItem := range priceItems {
		deletedPriceItem, err := priceClient.DeletePriceItemByID(context.Background(), &price.IDParam{Id: priceItem.Id})
		if assert.NoError(t, err) {
			assertFields(deletedPriceItem, &priceItem)
		}
	}

	// check price item list, should be empty
	list, err = priceClient.ListAllPriceItems(context.Background(), &price.PagingParam{
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Zero(t, list.GetCount())
		assert.Empty(t, list.GetPriceItems())
	}

	// de-duplication
	for _, priceItem := range priceItems {
		_, err := priceClient.DeletePriceItemByID(context.Background(), &price.IDParam{Id: priceItem.Id})
		assert.Error(t, err)
	}
}
