package service

import (
	"context"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/pay-sdk/dict"
	"github.com/qbox/pay-sdk/wallet"
)

// Contains checks if scope contains specific id
func (s PriceScopeMap) Contains(id uint64) (included, hit bool) {
	if s != nil {
		if included, ok := s[id]; ok {
			return included, true
		}
	}
	return false, false
}

// PriceScopeCheckByID checks scope by item_id、group_id、product_id as follows:
// 1. scope is nil, return true
// 2. item is empty, check group, same for group、product
// 3. item is not empty, return true if scope.Items contain item
// 4. use item.Group() override group, return true if scope.Groups constain group
// 5. use item.Product() override product, return true if scope.Products constain product
// 6. use false as default, return
func (s *PriceBizService) PriceScopeCheckByID(
	ctx context.Context,
	itemID, groupID, productID uint64, scope *PriceScope,
) (included bool, err error) {
	if scope == nil {
		return true, nil
	}

	if itemID != 0 {
		if result, hit := scope.Items.Contains(itemID); hit {
			return result, nil
		}

		// use item.group_id override param.group_id
		item, err := s.dictClient.GetItemByID(ctx, &dict.IDParam{Id: itemID})
		if err != nil {
			return false, err
		}

		groupID = item.GroupId
	}

	if groupID != 0 {
		if result, hit := scope.Groups.Contains(groupID); hit {
			return result, nil
		}

		// use group.product_id override param.product_id
		group, err := s.dictClient.GetItemGroupByID(ctx, &dict.IDParam{Id: groupID})
		if err != nil {
			return false, err
		}
		productID = group.ProductId
	}

	if productID != 0 {
		if result, hit := scope.Products.Contains(productID); hit {
			return result, nil
		}
	}

	return false, nil
}

func (s *PriceBizService) getSingleCurrencyForUID(
	ctx context.Context,
	uid uint64,
) (base.CurrencyType, error) {
	resp, err := s.paymentServiceClient.GetSingleCurrency(
		ctx,
		&wallet.UIDParam{Uid: uid},
	)
	if err != nil {
		return "", err
	}

	return base.CurrencyType(resp.CurrencyType), nil
}
