package service

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	dictpb "github.com/qbox/pay-sdk/dict"
	walletpb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qvmprice/model"
)

var errNotFound = errors.New("price record not found")

// 确保 epoch 取值有意义：如果 epoch 是 gRPC 零值（UNIX 时间戳的零值）或 Go time.Time 的零值，
// 都返回 time.Now()，否则维持不变。
func ensureEpoch(epoch time.Time, loc *time.Location) time.Time {
	if epoch.IsZero() || epoch.UnixNano() == 0 {
		return time.Now().In(loc)
	}
	return epoch.In(loc)
}

// QueryItemPriceForUID 以给定的时刻作为当前时刻，查询给定的 UID 某个计费项在某个区域的当前生效报价（“询价”）
func (a *QVMPriceService) QueryItemPriceForUID(
	ctx context.Context,
	uid uint64,
	itemCode string,
	zoneCode int64,
	currencyType string,
	when time.Time,
	epoch time.Time,
) (*QVMPriceItemDetail, error) {
	resp, err := a.QueryItemPricesForUID(
		ctx,
		uid,
		[]ItemSpec{
			{ItemCode: itemCode, ZoneCode: zoneCode},
		},
		currencyType,
		when,
		epoch,
	)
	if err != nil {
		return nil, err
	}

	if len(resp) == 0 {
		return nil, errNotFound
	}

	return resp[0], nil
}

func (a *QVMPriceService) ensureCurrencyTypeForUID(
	ctx context.Context,
	uid uint64,
	inputCurrencyType string,
) (string, error) {
	if inputCurrencyType != "" {
		return inputCurrencyType, nil
	}

	resp, err := a.paymentSrv.GetSingleCurrency(ctx, &walletpb.UIDParam{Uid: uid})
	if err != nil {
		return "", err
	}

	return resp.CurrencyType, nil
}

// QueryItemPricesForUID 以给定的时刻作为当前时刻，查询给定的 UID 某些 (item, zone) 在某时刻的生效报价（“询价”）
//
// 如 uid == 0 则为查询公开报价
func (a *QVMPriceService) QueryItemPricesForUID(
	ctx context.Context,
	uid uint64,
	itemSpecs []ItemSpec,
	currencyType string,
	when time.Time,
	epoch time.Time,
) ([]*QVMPriceItemDetail, error) {
	loc := tz.MustLocationFromCtx(ctx)
	when = when.In(loc)
	epoch = ensureEpoch(epoch, loc)

	currencyType, err := a.ensureCurrencyTypeForUID(ctx, uid, currencyType)
	if err != nil {
		return nil, err
	}

	var eg errgroup.Group

	var vipPriceRecords *fetchedItemPriceRecords
	if uid != 0 {
		eg.Go(func() error {
			resp, err := a.fetchItemPriceRecordsForUID(
				ctx,
				uid,
				itemSpecs,
				currencyType,
				epoch,
				nil,
			)
			if errors.Is(err, errNotFound) {
				err = nil
			}
			if err != nil {
				// 真错误
				return err
			}

			vipPriceRecords = resp
			return nil
		})
	}

	var publicPriceRecords *fetchedItemPriceRecords
	eg.Go(func() error {
		resp, err := a.fetchItemPriceRecordsForUID(
			ctx,
			0,
			itemSpecs,
			currencyType,
			epoch,
			nil,
		)
		if errors.Is(err, errNotFound) {
			err = nil
		}
		if err != nil {
			// 真错误
			return err
		}

		publicPriceRecords = resp
		return nil
	})

	err = eg.Wait()
	if err != nil {
		return nil, err
	}

	var result []*QVMPriceItemDetail
	for _, is := range itemSpecs {
		var groupCode string
		if vipPriceRecords != nil && vipPriceRecords.groupCodesMap != nil {
			groupCode = vipPriceRecords.groupCodesMap[is.ItemCode]
		}

		calculatedPrice, err := calculateItemPrice(
			uid,
			is.ItemCode,
			groupCode,
			is.ZoneCode,
			currencyType,
			when,
			epoch,
			nil,
			vipPriceRecords,
			publicPriceRecords,
		)
		if err != nil {
			return nil, err
		}

		result = append(result, calculatedPrice)
	}

	return result, nil
}

func calculateItemPrice(
	uid uint64,
	itemCode string,
	groupCode string,
	zoneCode int64,
	currencyType string,
	when time.Time,
	epoch time.Time,
	overrideTimeRange *base.TimeRange,
	vipRecords *fetchedItemPriceRecords,
	publicRecords *fetchedItemPriceRecords,
) (*QVMPriceItemDetail, error) {
	// 使用 epoch 的时区
	loc := epoch.Location()

	// 如果有按组改价操作，那么应该以改价时刻为当前时刻去拉报价，所以先处理 QVMGroupDiscounts
	var effectiveGroupDiscount *model.QVMGroupDiscount
	discountAdjustedEpoch := epoch
	discountRatio := 100
	if groupCode != "" && vipRecords != nil {
		for _, gd := range vipRecords.groupDiscounts {
			if shouldUpdateEffectiveGroupDiscount(
				effectiveGroupDiscount,
				gd,
				groupCode,
				zoneCode,
				when,
				overrideTimeRange,
				epoch,
			) {
				effectiveGroupDiscount = gd
			}
		}

		if effectiveGroupDiscount != nil {
			discountAdjustedEpoch = effectiveGroupDiscount.CreatedAt
			discountRatio = int(effectiveGroupDiscount.Discount)
		}
	}

	var effectivePriceItem *model.QVMPriceItem
	hasVIPPriceItem := false
	if vipRecords != nil {
		effectivePriceItem = findEffectivePriceItem(
			vipRecords.priceItems,
			itemCode,
			zoneCode,
			currencyType,
			when,
			overrideTimeRange,
			discountAdjustedEpoch,
			epoch,
		)
		hasVIPPriceItem = effectivePriceItem != nil
	}

	if effectivePriceItem == nil {
		if publicRecords == nil {
			return nil, errNotFound
		}

		// fallback 到公开报价
		effectivePriceItem = findEffectivePriceItem(
			publicRecords.priceItems,
			itemCode,
			zoneCode,
			currencyType,
			when,
			overrideTimeRange,
			discountAdjustedEpoch,
			epoch,
		)
	}

	if effectivePriceItem == nil {
		return nil, errNotFound
	}

	// 有按组改价记录或者 VIP 报价，都算 VIP 报价
	isVIPPrice := hasVIPPriceItem || effectiveGroupDiscount != nil

	// 产生改价操作的 ID
	var vipOP string
	if hasVIPPriceItem {
		vipOP = fmt.Sprintf(
			"QVMPRICE:Table:%d:Item:%d",
			effectivePriceItem.QVMPriceTableID,
			effectivePriceItem.ID,
		)
	}
	if effectiveGroupDiscount != nil {
		vipOP = fmt.Sprintf(
			"QVMPRICE:Table:%d:Group:%d",
			effectiveGroupDiscount.QVMPriceTableID,
			effectiveGroupDiscount.ID,
		)
	}

	// 产生本条记录的创建时刻
	// 对于按组改价的价格，都是以 QVMGroupDiscount 的创建时刻为准，否则不管是 VIP 报价还是
	// 公开报价，都可以取 effectivePriceItem 的创建时刻
	var priceItemCreatedAt time.Time
	if effectiveGroupDiscount != nil {
		priceItemCreatedAt = effectiveGroupDiscount.CreatedAt.In(loc)
	} else {
		priceItemCreatedAt = effectivePriceItem.CreatedAt.In(loc)
	}

	// 先构造响应，然后检查是否需要应用 group discount
	var result QVMPriceItemDetail
	{
		var stairRecordsToUse []*model.QVMPriceItemStair
		if hasVIPPriceItem {
			stairRecordsToUse = vipRecords.priceItemStairs
		} else {
			stairRecordsToUse = publicRecords.priceItemStairs
		}

		stairs := filter(func(x *model.QVMPriceItemStair) bool {
			return x.QVMPriceItemID == effectivePriceItem.ID
		}, stairRecordsToUse)

		sort.Slice(stairs, func(i int, j int) bool {
			return stairs[i].Order < stairs[j].Order
		})

		stairDetails := mapSlice(
			func(x *model.QVMPriceItemStair) *QVMItemPriceStairDetail {
				return &QVMItemPriceStairDetail{Quantity: x.Quantity, Price: x.Price}
			},
			stairs,
		)

		var tr base.TimeRange
		if overrideTimeRange != nil {
			tr = *overrideTimeRange
		} else {
			if effectiveGroupDiscount != nil {
				tr = base.TimeRange{
					Start: effectiveGroupDiscount.EffectTime.TimeIn(loc),
					End:   effectiveGroupDiscount.DeadTime.TimeIn(loc),
				}
			} else {
				tr = base.TimeRange{
					Start: effectivePriceItem.EffectTime.TimeIn(loc),
					End:   effectivePriceItem.DeadTime.TimeIn(loc),
				}
			}
		}

		result = QVMPriceItemDetail{
			UID:            uid,
			ItemCode:       effectivePriceItem.ItemCode,
			ZoneCode:       effectivePriceItem.ZoneCode,
			StairPriceType: effectivePriceItem.StairPriceType,
			CurrencyType:   effectivePriceItem.CurrencyType,
			Stairs:         stairDetails,
			EffectTime:     tr.Start,
			DeadTime:       tr.End,
			CreatedAt:      priceItemCreatedAt,
			IsVIP:          isVIPPrice,
			VIPOp:          vipOP,
		}
	}

	if discountRatio != 100 {
		result.Stairs = applyDiscountToPriceStairs(result.Stairs, discountRatio)
	}

	return &result, nil
}

func applyDiscountToPriceStairs(
	x []*QVMItemPriceStairDetail,
	ratio int,
) []*QVMItemPriceStairDetail {
	y := make([]*QVMItemPriceStairDetail, len(x))
	for i, s := range x {
		y[i] = &QVMItemPriceStairDetail{
			Quantity: s.Quantity,
			// NOTE: 等各种地方（订单、账单报价）支持 8 位小数之后，可以把 TruncToMoney 去了
			Price: s.Price.MulI64(int64(ratio)).QuoI64(100).TruncToMoney(),
		}
	}
	return y
}

// 用于找到最新一条生效的对应本 (item, zone) 及币种的 QVMPriceItem
// 如果 tr != nil 则判断 tr 时间范围内生效的，否则判断 when 时刻生效的
//
// NOTE: (BO-19850) 考虑对于一个 QVMGroupDiscount 所关联的 group，在该条 QVMGroupDiscount
// 记录创建之后，又向该组内新增对接了计费项：在 QVMGroupDiscount 创建时刻，该计费项的报价不存在，
// 按说应该返回失败。但实际业务上会希望能够有一个返回。
//
// 为此，引入 preferredEpoch 的概念，这种情况下：
//
// - preferredEpoch = QVMGroupDiscount.CreatedAt
// - epoch = req.Epoch
//
// 如果截至 preferredEpoch 时刻有报价，则使用截至该时刻的 *最新* 一条报价，否则使用
// 截至 epoch 时刻能找到的 *最早* 一条报价。
func findEffectivePriceItem(
	l []*model.QVMPriceItem,
	itemCode string,
	zoneCode int64,
	currencyType string,
	when time.Time,
	tr *base.TimeRange,
	preferredEpoch time.Time,
	epoch time.Time,
) *model.QVMPriceItem {
	var result *model.QVMPriceItem

	// 先找截至 preferredEpoch 最新一条，如果有就返回了
	for _, pi := range l {
		if shouldUpdateEffectivePriceItem(
			result,
			pi,
			itemCode,
			zoneCode,
			currencyType,
			when,
			tr,
			preferredEpoch,
			true,
		) {
			result = pi
		}
	}
	if result != nil {
		return result
	}

	// 没找到，属于 BO-19850 的情况，再找截至 epoch 的最早一条
	for _, pi := range l {
		if shouldUpdateEffectivePriceItem(
			result,
			pi,
			itemCode,
			zoneCode,
			currencyType,
			when,
			tr,
			epoch,
			false,
		) {
			result = pi
		}
	}

	return result
}

func shouldUpdateEffectivePriceItem(
	prevEffectivePriceItem *model.QVMPriceItem,
	pi *model.QVMPriceItem,
	itemCode string,
	zoneCode int64,
	currencyType string,
	when time.Time,
	tr *base.TimeRange,
	epoch time.Time,
	findNewest bool,
) bool {
	if prevEffectivePriceItem != nil {
		if findNewest {
			// 找符合条件的最新一条，因此如果 this.CreatedAt < prev.CreatedAt 则返回假（不要更新结果）
			if pi.CreatedAt.Before(prevEffectivePriceItem.CreatedAt) {
				return false
			}
		} else {
			// 找符合条件的最早一条，因此与上面的判断正好相反
			if pi.CreatedAt.After(prevEffectivePriceItem.CreatedAt) {
				return false
			}
		}
	}

	if pi.ItemCode != itemCode || pi.ZoneCode != zoneCode || pi.CurrencyType != currencyType {
		return false
	}

	// 此处使用的 epoch 可能与查询时的 epoch 不同（例如涉及按组改价操作的情况），因此仍然要按 epoch 过滤
	if pi.CreatedAt.After(epoch) {
		return false
	}

	if tr != nil {
		// effectTime >= tr.End || deadTime <= tr.Start
		// !effectTime < tr.End || !deadTime > tr.Start
		if !pi.EffectTime.TimeIn(time.UTC).Before(tr.End) || !pi.DeadTime.TimeIn(time.UTC).After(tr.Start) {
			return false
		}
	} else {
		// 不在生效时间段内: !(effectTime <= when && when < deadTime)
		//             -> effectTime > when || when >= deadTime
		//             -> effectTime > when || !(when < deadTime)
		if pi.EffectTime.TimeIn(time.UTC).After(when) || !(when.Before(pi.DeadTime.TimeIn(time.UTC))) {
			return false
		}
	}

	return true
}

// 用于找到最新一条生效的对应本 (group, zone) 的 QVMGroupDiscount
// 如果 tr != nil 则判断 tr 时间范围内生效的，否则判断 when 时刻生效的
func shouldUpdateEffectiveGroupDiscount(
	prevEffectiveGroupDiscount *model.QVMGroupDiscount,
	gd *model.QVMGroupDiscount,
	groupCode string,
	zoneCode int64,
	when time.Time,
	tr *base.TimeRange,
	epoch time.Time,
) bool {
	if prevEffectiveGroupDiscount != nil && prevEffectiveGroupDiscount.CreatedAt.After(gd.CreatedAt) {
		return false
	}

	if gd.GroupCode != groupCode || gd.ZoneCode != zoneCode {
		return false
	}

	// 此处使用的 epoch 可能与查询时的 epoch 不同（例如涉及按组改价操作的情况），因此仍然要按 epoch 过滤
	if gd.CreatedAt.After(epoch) {
		return false
	}

	if tr != nil {
		// effectTime >= tr.End || deadTime <= tr.Start
		// !effectTime < tr.End || !deadTime > tr.Start
		if !gd.EffectTime.TimeIn(time.UTC).Before(tr.End) || !gd.DeadTime.TimeIn(time.UTC).After(tr.Start) {
			return false
		}
	} else {
		// 不在生效时间段内: !(effectTime <= when && when < deadTime)
		//             -> effectTime > when || when >= deadTime
		//             -> effectTime > when || !(when < deadTime)
		if gd.EffectTime.TimeIn(time.UTC).After(when) || !(when.Before(gd.DeadTime.TimeIn(time.UTC))) {
			return false
		}
	}

	return true
}

type fetchedItemPriceRecords struct {
	priceItems      []*model.QVMPriceItem
	priceItemStairs []*model.QVMPriceItemStair

	// 以下两个字段如果 uid == 0 则不存在

	// map[itemCode]groupCode
	groupCodesMap  map[string]string
	groupDiscounts []*model.QVMGroupDiscount
}

// 根据给定的 (item, zone) 列表，批量请求询价所需的基础数据，并做基本的清洗与过滤
// 如果 timeRange != nil 则为时间范围查询，否则为 epoch 时刻查询
func (a *QVMPriceService) fetchItemPriceRecordsForUID(
	ctx context.Context,
	uid uint64,
	itemSpecs []ItemSpec,
	currencyType string,
	epoch time.Time,
	timeRange *base.TimeRange,
) (*fetchedItemPriceRecords, error) {
	// 如果需要查询某个用户的报价，那么需要检查这用户是否有相应的按组改价记录，公开报价则不需要
	considerGroupDiscounts := uid != 0

	itemCodes := distinctSlice(func(x ItemSpec) string {
		return x.ItemCode
	}, itemSpecs)

	// map[itemCode]groupCode
	var groupCodesMap map[string]string
	var groupSpecs []model.GroupSpec
	if considerGroupDiscounts {
		resp, err := a.getGroupCodesFromItemSpecs(ctx, itemCodes)
		if err != nil {
			return nil, err
		}
		groupCodesMap = resp

		groupSpecs = distinctSliceWithKey(
			func(x ItemSpec) string {
				groupCode := groupCodesMap[x.ItemCode]
				return fmt.Sprintf("%s:%d", groupCode, x.ZoneCode)
			},
			func(x ItemSpec) model.GroupSpec {
				return model.GroupSpec{
					GroupCode: groupCodesMap[x.ItemCode],
					ZoneCode:  x.ZoneCode,
				}
			},
			itemSpecs,
		)
	}

	var eg errgroup.Group

	var priceItems []*model.QVMPriceItem
	eg.Go(func() error {
		modelItemSpecs := mapSlice(
			func(x ItemSpec) model.ItemSpec {
				return model.ItemSpec{
					ItemCode: x.ItemCode,
					ZoneCode: x.ZoneCode,
				}
			},
			itemSpecs,
		)

		var err error
		if timeRange != nil {
			priceItems, err = a.dao.QVMPriceItem.ListByUIDItemSpecsTimeRange(
				uid,
				modelItemSpecs,
				base.NewHNS(timeRange.Start),
				base.NewHNS(timeRange.End),
			)
		} else {
			priceItems, err = a.dao.QVMPriceItem.ListByUIDItemSpecsTimePoint(
				uid,
				modelItemSpecs,
				base.NewHNS(epoch),
			)
		}
		if err != nil {
			return err
		}

		return nil
	})

	var priceItemStairs []*model.QVMPriceItemStair
	eg.Go(func() error {
		resp, err := a.dao.QVMPriceItemStair.ListByUIDItemCode(
			uid,
			itemCodes,
		)
		if err != nil {
			return err
		}

		priceItemStairs = resp
		return nil
	})

	var groupDiscounts []*model.QVMGroupDiscount
	if considerGroupDiscounts {
		eg.Go(func() error {
			var err error
			if timeRange != nil {
				groupDiscounts, err = a.dao.QVMGroupDiscount.ListByUIDGroupSpecsTimeRange(
					uid,
					groupSpecs,
					base.NewHNS(timeRange.Start),
					base.NewHNS(timeRange.End),
				)
			} else {
				groupDiscounts, err = a.dao.QVMGroupDiscount.ListByUIDGroupSpecsTimePoint(
					uid,
					groupSpecs,
					base.NewHNS(epoch),
				)
			}
			if err != nil {
				return err
			}

			return nil
		})
	}

	err := eg.Wait()
	if err != nil {
		return nil, err
	}

	distinctPriceTableIDs := mergeDistinctSlice(
		mapSlice(func(x *model.QVMPriceItem) uint64 { return x.QVMPriceTableID }, priceItems),
		mapSlice(func(x *model.QVMGroupDiscount) uint64 { return x.QVMPriceTableID }, groupDiscounts),
	)

	if len(distinctPriceTableIDs) == 0 {
		// 一定是没有查到任何东西
		if groupCodesMap != nil {
			return &fetchedItemPriceRecords{
				priceItems:      nil,
				priceItemStairs: nil,
				groupCodesMap:   groupCodesMap,
				groupDiscounts:  nil,
			}, nil
		}
		return nil, nil
	}

	priceTableDisabledStatuses, err := a.dao.QVMPriceTable.ListDisabledStatusesByIDs(distinctPriceTableIDs)
	if err != nil {
		return nil, err
	}

	filteredPriceItems, filteredGroupDiscounts := filterDisabledRecords(
		priceTableDisabledStatuses,
		priceItems,
		groupDiscounts,
	)

	filteredPriceItems = filter(func(x *model.QVMPriceItem) bool {
		// CreatedAt <= epoch
		return !x.CreatedAt.After(epoch)
	}, filteredPriceItems)

	filteredGroupDiscounts = filter(func(x *model.QVMGroupDiscount) bool {
		// CreatedAt <= epoch
		return !x.CreatedAt.After(epoch)
	}, filteredGroupDiscounts)

	filteredPriceItemStairs := filterQVMPriceItemStairsClientside(
		filteredPriceItems,
		priceItemStairs,
	)

	filteredPriceItemStairs = filter(func(x *model.QVMPriceItemStair) bool {
		// CreatedAt <= epoch
		return !x.CreatedAt.After(epoch)
	}, filteredPriceItemStairs)

	return &fetchedItemPriceRecords{
		priceItems:      filteredPriceItems,
		priceItemStairs: filteredPriceItemStairs,
		groupCodesMap:   groupCodesMap,
		groupDiscounts:  filteredGroupDiscounts,
	}, nil
}

func (a *QVMPriceService) getGroupCodesFromItemSpecs(
	ctx context.Context,
	itemCodes []string,
) (map[string]string, error) {

	v4Items, err := a.dictV4.MultiGetItemByCodes(ctx, &dictpb.MultiCodeParam{Codes: itemCodes})
	if err != nil {
		return nil, err
	}

	itemCodeGroupIDMap := listToMap(
		func(x *dictpb.Item) (string, uint64) {
			return x.Code, x.GroupId
		},
		v4Items.Items,
		false, // 正常不会有重复
	)

	distinctV4GroupIDs := distinctSlice(func(x *dictpb.Item) uint64 {
		return x.GroupId
	}, v4Items.Items)

	// dict 缺一个 ListItemGroupsByIDs 接口
	type groupInfo struct {
		id   uint64
		code string
	}

	// 最大并发 20 随便写的
	groupInfos, err := resultgroup.ThrottledParallelMap(distinctV4GroupIDs, 20, func(id uint64) (groupInfo, error) {
		v4Group, err := a.dictV4.GetItemGroupByID(ctx, &dictpb.IDParam{Id: id})
		if err != nil {
			return groupInfo{}, err
		}

		return groupInfo{
			id:   v4Group.Id,
			code: v4Group.Code,
		}, nil
	})
	if err != nil {
		return nil, err
	}

	groupCodeMap := listToMap(
		func(x groupInfo) (uint64, string) { return x.id, x.code },
		groupInfos,
		false, // 正常不会有重复
	)

	return listToMap(
		func(itemCode string) (string, string) {
			groupID := itemCodeGroupIDMap[itemCode]
			return itemCode, groupCodeMap[groupID]
		},
		itemCodes,
		false, // 不会有重复
	), nil
}
