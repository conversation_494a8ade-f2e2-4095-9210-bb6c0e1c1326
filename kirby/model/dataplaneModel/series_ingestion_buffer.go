package dataplaneModel

import (
	"context"
	"sync"
	"time"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/bo-base/v4/uuid"
)

func generateSeriesKey() string {
	return "QKSK" + uuid.New()
}

type SeriesIngestionBuffer struct {
	dao *KirbyDataplaneDao
	loc *time.Location

	mu                    sync.Mutex
	pendingMetadataShards map[time.Time][]seriesMetadataRow
	pendingDataShards     map[computedShardInfo][]dataRowAndKey
}

func (d *KirbyDataplaneDao) NewSeriesIngestionBuffer(
	loc *time.Location,
) *SeriesIngestionBuffer {
	return &SeriesIngestionBuffer{
		dao:                   d,
		loc:                   loc,
		mu:                    sync.Mutex{},
		pendingMetadataShards: make(map[time.Time][]seriesMetadataRow),
		pendingDataShards:     make(map[computedShardInfo][]dataRowAndKey),
	}
}

type bufferedSeries struct {
	fs        *FlattenedSeries
	seriesKey string
	rows      []dataRow
}

func (b *SeriesIngestionBuffer) Add(series []*FlattenedSeries) error {
	var bs []bufferedSeries
	for _, s := range series {
		rows, err := rowsFromSparseReprAndTimespan(
			s.G,
			s.Start,
			s.End,
			b.loc,
			s.Data,
		)
		if err != nil {
			return err
		}
		seriesKey := generateSeriesKey()
		bs = append(bs, bufferedSeries{
			fs:        s,
			seriesKey: seriesKey,
			rows:      rows,
		})
	}

	b.mu.Lock()
	defer b.mu.Unlock()

	var eg errgroup.Group
	eg.Go(func() error {
		b.addMetadataLocked(bs)
		return nil
	})
	eg.Go(func() error {
		b.addDataLocked(bs)
		return nil
	})

	err := eg.Wait()
	if err != nil {
		return err
	}

	return nil
}

func (b *SeriesIngestionBuffer) addMetadataLocked(bs []bufferedSeries) {
	mdRows := lo.Map(bs, func(x bufferedSeries, _ int) seriesMetadataRow {
		sp := SP(x.fs.MergedProps)
		return seriesMetadataRow{
			QRN:         x.fs.QRN,
			MM:          x.fs.MM,
			SP:          &sp,
			Start:       x.fs.Start.In(b.loc),
			End:         x.fs.End.In(b.loc),
			MBUUID:      x.fs.MBUUID,
			UID:         x.fs.UID,
			SPHash:      SPHasher(sp).Hash(),
			RT:          x.fs.RTDCode,
			AggR:        x.fs.AggRCode,
			MeasureUnit: x.fs.MeasureUnit,
			G:           x.fs.G,
			SeriesKey:   x.seriesKey,
		}
	})

	sharded := lo.GroupBy(mdRows, func(x seriesMetadataRow) time.Time {
		return base.ThisMonth(x.Start)
	})

	for shardMonth, data := range sharded {
		b.pendingMetadataShards[shardMonth] = append(b.pendingMetadataShards[shardMonth], data...)
	}
}

func (b *SeriesIngestionBuffer) addDataLocked(bs []bufferedSeries) {
	dataByMonth := lo.GroupBy(bs, func(x bufferedSeries) time.Time {
		return base.ThisMonth(x.fs.Start.In(b.loc))
	})

	for month, monthBSes := range dataByMonth {
		sharded := lo.GroupBy(monthBSes, func(x bufferedSeries) computedShardInfo {
			return computeDataTableShard(
				dataTableKindSeries,
				x.fs.G,
				month,
				b.loc,
				x.fs.QRN,
			)
		})

		for shardName, data := range sharded {
			for _, bs := range data {
				seriesRows := lo.Map(bs.rows, func(y dataRow, _ int) dataRowAndKey {
					return dataRowAndKey{
						SeriesKey: bs.seriesKey,
						T0:        y.T0,
						P:         y.P,
					}
				})
				b.pendingDataShards[shardName] = append(b.pendingDataShards[shardName], seriesRows...)
			}
		}
	}
}

func (b *SeriesIngestionBuffer) Commit(ctx context.Context) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	// ensure shards
	// this has to be done outside of DB transaction because of DDL

	for month := range b.pendingMetadataShards {
		err := b.dao.SeriesMD.EnsureShard(month, month.Location())
		if err != nil {
			return err
		}
	}

	for shardInfo := range b.pendingDataShards {
		err := b.dao.DataMgr.getShard(&shardInfo).Ensure()
		if err != nil {
			return err
		}
	}

	// do the inserts
	return b.dao.DoTransaction(func(txnDao *KirbyDataplaneDao) error {
		var eg errgroup.Group
		eg.Go(func() error { return b.commitMetadataLocked(ctx, txnDao) })
		eg.Go(func() error { return b.commitDataLocked(ctx, txnDao) })
		return eg.Wait()
	})
}

func (b *SeriesIngestionBuffer) commitMetadataLocked(
	ctx context.Context,
	txnDao *KirbyDataplaneDao,
) error {
	type shardParam struct {
		month time.Time
		data  []seriesMetadataRow
	}
	jobs := lo.MapToSlice(b.pendingMetadataShards, func(k time.Time, v []seriesMetadataRow) shardParam {
		return shardParam{month: k, data: v}
	})

	const concurrency = 2 // 随便取的大概不太会打挂数据库的并发，要和 data 部分共同考虑
	_, err := resultgroup.ThrottledParallelMap(jobs, concurrency, func(p shardParam) (struct{}, error) {
		return struct{}{}, txnDao.SeriesMD.GetShard(p.month, b.loc).BulkInsert(ctx, p.data)
	})

	return err
}

func (b *SeriesIngestionBuffer) commitDataLocked(
	ctx context.Context,
	txnDao *KirbyDataplaneDao,
) error {
	type shardParam struct {
		si   computedShardInfo
		data []dataRowAndKey
	}
	jobs := lo.MapToSlice(b.pendingDataShards, func(k computedShardInfo, v []dataRowAndKey) shardParam {
		return shardParam{si: k, data: v}
	})

	const concurrency = 2 // 随便取的大概不太会打挂数据库的并发，要和 metadata 部分共同考虑
	_, err := resultgroup.ThrottledParallelMap(jobs, concurrency, func(p shardParam) (struct{}, error) {
		shard := txnDao.DataMgr.getShard(&p.si)
		return struct{}{}, shard.bulkInsert(p.data)
	})

	return err
}
