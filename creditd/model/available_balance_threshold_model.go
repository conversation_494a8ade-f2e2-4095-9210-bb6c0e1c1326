package model

import (
	"time"

	"qiniu.io/pay/creditd/model/enums"
)

// AvailableBalanceThreshold model definition
type AvailableBalanceThreshold struct {
	ID        uint64                `gorm:"primary_key"`
	UID       uint64                // 用户账号
	Threshold int64                 // 可用额度阈值
	Status    enums.ThresholdStatus // 状态（是否启用）
	Remark    string                `gorm:"type:varchar(512)"`

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`

	IsManager bool `gorm:"-"` // 是否是管理员可用额度预警
}

func (a *AvailableBalanceThreshold) GetTableName() string {
	if a.IsManager {
		return "manager_available_balance_thresholds"
	}
	return "available_balance_thresholds"
}
