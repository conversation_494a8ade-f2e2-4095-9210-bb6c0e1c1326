package service_test

import (
	"context"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/test"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestNewDefaultUserPreference(t *testing.T) {
	test.RunWithUTCAndCST(t, testNewDefaultUserPreference)
}

func testNewDefaultUserPreference(t *testing.T, loc *time.Location) {
	sandbox := buildSandbox(t)

	ctx := tz.WithRefLocation(context.TODO(), loc)

	dictService := sandbox.dictService

	defaultItemCode := "sms:count:system:smgp"
	_, err := sandbox.dictService.CreateItem(context.TODO(), &model.Item{
		Code: defaultItemCode,
	})
	assert.Nil(t, err)

	zonePreferences := map[int64]bool{
		3007: true,
	}
	registerTime := time.Date(2022, 1, 1, 12, 0, 0, 0, loc)

	defaultStartTime := time.Date(2010, 1, 1, 0, 0, 0, 0, loc)
	defaultEndTime := time.Date(2099, 12, 31, 23, 59, 59, 59, loc)

	err = dictService.CreateDefaultUserPreferences(ctx, &model.DefaultUserPreference{
		ItemCode:          defaultItemCode,
		ZonePreferences:   zonePreferences,
		RegisterStartTime: &defaultStartTime,
		RegisterEndTime:   &defaultEndTime,
	})
	assert.Nil(t, err)

	newZonePreferences := map[int64]bool{
		3007: false,
		3001: true,
	}

	err = dictService.UpdateDefaultUserPreferences(ctx, []string{defaultItemCode}, newZonePreferences, registerTime)
	assert.Nil(t, err)

	preference, err := dictService.GetZonePreferenceByItemCode(ctx, defaultItemCode, registerTime)
	assert.Nil(t, err)
	assert.NotNil(t, preference)
	assert.Equal(t, newZonePreferences, preference.ZonePreferences)

	newItemCode := "sms:count:system:sgip"
	_, err = sandbox.dictService.CreateItem(context.TODO(), &model.Item{
		Code: newItemCode,
	})
	assert.Nil(t, err)

	newItemZonePreferences := map[int64]bool{
		3007: false,
		3003: true,
	}
	err = dictService.CreateDefaultUserPreferences(ctx, &model.DefaultUserPreference{
		ItemCode:          newItemCode,
		ZonePreferences:   newItemZonePreferences,
		RegisterStartTime: &defaultStartTime,
		RegisterEndTime:   &defaultEndTime,
	})
	assert.Nil(t, err)

	zp, err := dictService.GetZonePreferenceByItemCode(ctx, newItemCode, registerTime)
	assert.Nil(t, err)
	assert.NotNil(t, zp)
	assert.Equal(t, newItemZonePreferences, zp.ZonePreferences)

	lzps, err := dictService.ListDefaultUserPreferences(ctx, registerTime, 0, 10)
	assert.Nil(t, err)
	assert.Equal(t, 2, len(lzps))
	assert.Contains(t, []string{defaultItemCode, newItemCode}, lzps[0].ItemCode)
	assert.Contains(t, []string{defaultItemCode, newItemCode}, lzps[1].ItemCode)
}
