package service

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/intl/tz"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/test"

	aDao "qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

type thresholdSrvSandbox struct {
	testWrap     *test.Wrap
	thresholdSrv *AvailableBalanceThresholdService
}

func newThresholdSrvSandbox(t *testing.T) *thresholdSrvSandbox {
	testWrap, err := test.NewTestWrap(
		t,
		test.WithMigrateFuncs(aDao.RegisterMigrateThreshold, aDao.RegisterMigrateLock),
	)
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in availableBalanceThreshold service return error")
	}

	thresholdDao := aDao.NewAvailableBalanceThresholdDao(testWrap.BaseDao())
	lockDao := aDao.NewLockDao(testWrap.BaseDao())

	lock := &model.Lock{
		LockName: "available_balance_threshold",
	}
	err = lockDao.Save(lock)
	if err != nil {
		t.Fatal(err)
	}

	thresholdSrv, err := NewAvailableBalanceThresholdService(thresholdDao, dao.CacheExpiresNoCache)
	if err != nil {
		t.Fatal(err)
	}

	return &thresholdSrvSandbox{
		testWrap:     testWrap,
		thresholdSrv: thresholdSrv,
	}
}

func TestAvailableBalanceThresholdService_CreateThreshold(t *testing.T) {
	sandbox := newThresholdSrvSandbox(t)

	thresholdIn := &model.AvailableBalanceThreshold{
		UID:       1,
		Threshold: 200,
		Status:    enums.StatusThresholdEnabled,
		Remark:    "only for test",
	}
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	thresholdOut, err := sandbox.thresholdSrv.CreateThreshold(ctx, thresholdIn)
	if err != nil {
		t.Fatal(err)
	}

	assert.NotZero(t, thresholdOut.ID)
	assert.Equal(t, thresholdIn.Threshold, thresholdOut.Threshold)
	assert.Equal(t, thresholdIn.UID, thresholdOut.UID)
	assert.Equal(t, thresholdIn.Status, thresholdOut.Status)

	thresholdIn2 := &model.AvailableBalanceThreshold{
		UID:       2,
		Threshold: 50,
		Status:    enums.StatusThresholdEnabled,
		Remark:    "only for test",
	}
	_, err = sandbox.thresholdSrv.CreateThreshold(ctx, thresholdIn2)
	if err != nil {
		t.Fatal(err)
	}

	thresholdInConcurrent1 := &model.AvailableBalanceThreshold{
		UID:       2,
		Threshold: 300,
		Status:    enums.StatusThresholdEnabled,
		Remark:    "only for test",
	}

	thresholdInConcurrent2 := &model.AvailableBalanceThreshold{
		UID:       2,
		Threshold: 400,
		Status:    enums.StatusThresholdEnabled,
		Remark:    "only for test",
	}

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()
		_, err := sandbox.thresholdSrv.CreateThreshold(ctx, thresholdInConcurrent1)
		if err != nil {
			panic(err)
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		_, err := sandbox.thresholdSrv.CreateThreshold(ctx, thresholdInConcurrent2)
		if err != nil {
			panic(err)
		}
	}()

	wg.Wait()
	thresholds, err := sandbox.thresholdSrv.thresholdDao.ListByUID(2, false)
	if err != nil {
		t.Fatal(err)
	}

	if assert.Equal(t, 3, len(thresholds)) {
		enabledCount := 0
		for _, threshold := range thresholds {
			if threshold.Status == enums.StatusThresholdEnabled {
				enabledCount++
			}
		}
		for _, threshold := range thresholds {
			fmt.Println(threshold)
		}
		assert.Equal(t, 1, enabledCount)
	}
}

func TestAvailableBalanceThresholdService_CreateThresholdParallel(t *testing.T) {
	sandbox := newThresholdSrvSandbox(t)

	tests := []struct {
		name  string
		value *model.AvailableBalanceThreshold
	}{
		{
			name: "test 1",
			value: &model.AvailableBalanceThreshold{
				UID:       2,
				Threshold: 50,
				Status:    enums.StatusThresholdEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 2",
			value: &model.AvailableBalanceThreshold{
				UID:       2,
				Threshold: 51,
				Status:    enums.StatusThresholdEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 3",
			value: &model.AvailableBalanceThreshold{
				UID:       2,
				Threshold: 52,
				Status:    enums.StatusThresholdEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 4",
			value: &model.AvailableBalanceThreshold{
				UID:       2,
				Threshold: 53,
				Status:    enums.StatusThresholdEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 5",
			value: &model.AvailableBalanceThreshold{
				UID:       2,
				Threshold: 63,
				Status:    enums.StatusThresholdEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 6",
			value: &model.AvailableBalanceThreshold{
				UID:       2,
				Threshold: 777,
				Status:    enums.StatusThresholdEnabled,
				Remark:    "only for test",
			},
		},
	}

	t.Parallel()
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Log(tc.value)
			_, err := sandbox.thresholdSrv.CreateThreshold(ctx, tc.value)
			if err != nil {
				t.Fatal(err)
			}
		})
	}

	thresholds, err := sandbox.thresholdSrv.thresholdDao.ListByUID(2, false)
	if err != nil {
		t.Fatal(err)
	}

	if assert.Equal(t, 6, len(thresholds)) {
		enabledCount := 0
		for _, threshold := range thresholds {
			if threshold.Status == enums.StatusThresholdEnabled {
				enabledCount++
			}
		}
		for _, threshold := range thresholds {
			t.Log(threshold)
		}
		assert.Equal(t, 1, enabledCount)
	}
}

func TestAvailableBalanceThresholdService_GetByStatus(t *testing.T) {
	sandbox := newThresholdSrvSandbox(t)

	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	thresholds := []struct {
		name  string
		value *model.AvailableBalanceThreshold
	}{
		{
			name: "test 1",
			value: &model.AvailableBalanceThreshold{
				UID:       3,
				Threshold: 50,
				Status:    enums.StatusThresholdEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 2",
			value: &model.AvailableBalanceThreshold{
				UID:       1,
				Threshold: 51,
				Status:    enums.StatusThresholdNotEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 3",
			value: &model.AvailableBalanceThreshold{
				UID:       4,
				Threshold: 52,
				Status:    enums.StatusThresholdEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 4",
			value: &model.AvailableBalanceThreshold{
				UID:       5,
				Threshold: 53,
				Status:    enums.StatusThresholdNotEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 5",
			value: &model.AvailableBalanceThreshold{
				UID:       2,
				Threshold: 63,
				Status:    enums.StatusThresholdNotEnabled,
				Remark:    "only for test",
			},
		},
		{
			name: "test 6",
			value: &model.AvailableBalanceThreshold{
				UID:       2,
				Threshold: 777,
				Status:    enums.StatusThresholdEnabled,
				Remark:    "only for test",
			},
		},
	}

	for _, threshold := range thresholds {
		_, err := sandbox.thresholdSrv.CreateThreshold(ctx, threshold.value)
		if err != nil {
			t.Fatal(err)
		}
	}

	gotThresholds, count, err := sandbox.thresholdSrv.GetByStatus(
		ctx,
		enums.StatusThresholdEnabled,
		false,
		0,
		100,
	)
	if err != nil {
		t.Fatalf("GetByStatus() error = %v", err)
		return
	}

	assert.Equal(t, 3, len(gotThresholds))
	assert.Equal(t, uint64(3), count)
}
