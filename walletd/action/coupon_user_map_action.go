package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// GetCouponUserMapByID query coupon_user_map by id
func (a *WalletAction) GetCouponUserMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CouponUserMap, error) {
	m, err := a.walletBizSrv.GetCouponUserMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponUserMap(m)
}

// CreateCouponUserMap create a new coupon_user_map
func (a *WalletAction) CreateCouponUserMap(
	ctx context.Context,
	param *pb.CouponUserMap,
) (*pb.CouponUserMap, error) {
	_m, err := adapter.BuildCouponUserMap(param)
	if err != nil {
		return nil, err
	}
	_m, err = a.walletBizSrv.CreateCouponUserMap(ctx, _m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponUserMap(_m)
}

// UpdateCouponUserMapByID update coupon_user_map by id
func (a *WalletAction) UpdateCouponUserMapByID(
	ctx context.Context,
	param *pb.IDCouponUserMapParam,
) (*pb.CouponUserMap, error) {
	_m, err := adapter.BuildCouponUserMap(param.GetCouponUserMap())
	if err != nil {
		return nil, err
	}
	_m, err = a.walletBizSrv.UpdateCouponUserMapByID(ctx, param.GetId(), _m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponUserMap(_m)
}

// DeleteCouponUserMapByID delete coupon_user_map by id
func (a *WalletAction) DeleteCouponUserMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CouponUserMap, error) {
	_m, err := a.walletBizSrv.DeleteCouponUserMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponUserMap(_m)
}

// ListCouponUserMapByUID list all coupon_user_maps by uid
func (a *WalletAction) ListCouponUserMapByUID(
	ctx context.Context,
	param *pb.UIDPagingParam,
) (*pb.CouponUserMapList, error) {
	count, err := a.walletBizSrv.CountCouponUserMapsByUID(ctx, param.GetUid())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	ms, err := a.walletBizSrv.ListCouponUserMapsByUID(ctx, param.GetUid(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponUserMapList{
		Count:          count,
		CouponUserMaps: make([]*pb.CouponUserMap, len(ms)),
	}
	for i, m := range ms {
		_m, err := adapter.BuildPbCouponUserMap(&m)
		if err != nil {
			return nil, err
		}
		list.CouponUserMaps[i] = _m
	}
	return list, nil
}

// CountCouponUserMapByUID count all coupon_user_maps by uid
func (a *WalletAction) CountCouponUserMapByUID(
	ctx context.Context,
	param *pb.UIDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountCouponUserMapsByUID(ctx, param.GetUid())
	return &pb.CountParam{Count: count}, err
}
