package model

import (
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// Package model definition
type Package struct {
	ID          uint64 `gorm:"primary_key"`
	Code        string `gorm:"type:varchar(128);unique_index:idx_package_code"`
	Type        string `gorm:"type:varchar(128)"`
	Name        string `gorm:"type:varchar(128)"`
	Description string `gorm:"type:varchar(512)"`
	Remark      string `gorm:"type:varchar(512)"`
	Price       uint64
	CreatedAt   time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt   time.Time `sql:"type:DATETIME(6)"`
	Items       []PackageItem
}

// PackageDao is data access object of Package model
type PackageDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

func (d *PackageDao) GetCachePrefix() string {
	return "measure:package:"
}

func (d *PackageDao) GetCacheRefs() []dao.CacheLayer {
	return []dao.CacheLayer{
		(*PackageItemDao)(nil),
		(*PackageDao)(nil),
		(*PackageUserMapDao)(nil),
	}
}

// NewPackageDao is constructor of PackageDao
func NewPackageDao(base *dao.BaseDao) *PackageDao {
	cachePrefix := (*PackageDao)(nil).GetCachePrefix()
	return &PackageDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of Package by id
func (d *PackageDao) GetByID(id uint64, expires ...time.Duration) (*Package, error) {
	model := &Package{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// GetByCode select a record of Package by code
func (d *PackageDao) GetByCode(code string, expires ...time.Duration) (*Package, error) {
	model := &Package{}
	keys := dao.NewCacheKeysFmt(
		"%s:code:%s",
		d.cachePrefix, code,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where("`code` = ?", code).First(value).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("code", code)
	}
	return model, nil
}

type ListPackageConds struct {
	// CodePattern Package Code 包含的子串
	CodePattern string

	Offset int
	Limit  int
}

func (p *ListPackageConds) toWhereClause() (conditionStr string, args []any, err error) {
	andCond := squirrel.And{}

	if p.CodePattern != "" {
		andCond = append(andCond, squirrel.Like{
			"code": fmt.Sprintf("%%%s%%", dao.EscapeForMySQLLike(p.CodePattern)),
		})
	}

	return andCond.ToSql()
}

// ListByConds select records of Package
func (d *PackageDao) ListByConds(
	p *ListPackageConds,
	expires ...time.Duration,
) (data []Package, err error) {
	where, args, err := p.toWhereClause()
	if err != nil {
		return nil, err
	}

	keys := dao.NewCacheKeysFmt(
		"%s:list:pat=%s&offset=%d&limit=%d",
		d.cachePrefix,
		p.CodePattern,
		p.Offset,
		p.Limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&Package{}).
			Where(where, args...).
			Offset(p.Offset).
			Limit(p.Limit).
			Order("created_at DESC").
			Find(value).
			Error
	}, keys, &data, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"code_pattern": p.CodePattern,
			"offset":       p.Offset,
			"limit":        p.Limit,
		})
	}
	return
}

// CountAll select count of Package
func (d *PackageDao) CountAll(
	expires ...time.Duration,
) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count",
		d.cachePrefix,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&Package{}).Count(value).Error
	}, keys, &count, expires...)
	return
}

// Save insert or update a record of Package by id
func (d *PackageDao) Save(model *Package, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// Delete delete a record of Package
func (d *PackageDao) Delete(model *Package) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
}

// DeleteByID delete a record of Package by id
func (d *PackageDao) DeleteByID(id uint64) error {
	model := &Package{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}

// ListByType query package by type
func (d *PackageDao) ListByType(uid uint64, tpe string) (data []Package, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:type:uid:%d:%s",
		d.cachePrefix, uid, tpe,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&Package{}).
			Joins("LEFT JOIN `package_user_maps` ON `packages`.`id`=`package_user_maps`.`package_id`").
			Where(
				"`packages`.`type` = ? AND `package_user_maps`.`uid` = ?",
				tpe, uid,
			).
			Find(value).
			Order("`package_user_maps`.`id` ASC", true).
			Error
	}, keys, &data)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid": uid,
		})
	}

	return data, nil
}
