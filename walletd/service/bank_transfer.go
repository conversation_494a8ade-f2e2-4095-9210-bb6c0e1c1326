package service

import (
	"context"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"

	"qiniu.io/pay/walletd/model"
)

// BankTransferService service
type BankTransferService struct {
	dao *model.BankTransferDao
}

func NewBankTransferService(
	dao *model.BankTransferDao,
) *BankTransferService {
	return &BankTransferService{
		dao: dao,
	}
}

// ListBankTransfer 查询银行转账记录列表
func (s *BankTransferService) ListBankTransfer(
	ctx context.Context,
	query *model.BankTransferQuery,
	offset int,
	limit int,
) ([]*model.BankTransfer, uint64, error) {
	// 不记录info日志，避免与action层重复

	// 设置分页参数
	query.Offset = int64(offset)
	query.Limit = int64(limit)

	// 创建等待组和错误通道
	var wg sync.WaitGroup
	var transfers []*model.BankTransfer
	var total uint64
	var transfersErr, totalErr error

	// 并行执行查询
	wg.Add(2)
	go func() {
		defer wg.Done()
		transfers, transfersErr = s.dao.ListByQuery(ctx, query)
	}()

	go func() {
		defer wg.Done()
		total, totalErr = s.dao.CountByQuery(ctx, query)
	}()

	wg.Wait()

	// 检查错误
	if transfersErr != nil {
		return nil, 0, errors.Trace(transfersErr).WithField("query", query)
	}

	if totalErr != nil {
		return nil, 0, errors.Trace(totalErr).WithField("query", query)
	}

	return transfers, total, nil
}

// GetBankTransfer 获取银行转账记录详情
func (s *BankTransferService) GetBankTransfer(
	ctx context.Context,
	sn string,
) (*model.BankTransfer, error) {
	// 不记录info日志，避免与action层重复

	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return nil, errors.Trace(err).WithField("sn", sn)
	}

	return transfer, nil
}

// ApproveBankTransfer 审批通过银行转账申请
func (s *BankTransferService) ApproveBankTransfer(
	ctx context.Context,
	sn string,
) error {
	// 获取转账记录
	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	// 检查状态是否允许审批
	if transfer.Status != model.BankTransferStatusPendingReview &&
		transfer.Status != model.BankTransferStatusVoid {
		return errors.New("bank transfer status not allowed to approve").
			WithFields(errors.Fields{
				"sn":     sn,
				"status": transfer.Status,
			})
	}

	// 更新状态为销售未确认
	err = s.dao.UpdateStatus(sn, model.BankTransferStatusSalesUnconfirmed)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	return nil
}

// InvalidateBankTransfer 作废银行转账申请
func (s *BankTransferService) InvalidateBankTransfer(
	ctx context.Context,
	sn string,
) error {
	// 获取转账记录
	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}
	// 已经是作废状态了
	if transfer.Status == model.BankTransferStatusVoid {
		return nil
	}

	// 检查状态是否允许作废
	if transfer.Status != model.BankTransferStatusPendingReview {
		return errors.New("bank transfer status not allowed to invalidate").
			WithFields(errors.Fields{
				"sn":     sn,
				"status": transfer.Status,
			})
	}

	// 更新状态为已作废
	err = s.dao.UpdateStatus(sn, model.BankTransferStatusVoid)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	return nil
}

// RechargeBankTransfer 执行银行转账充值操作
func (s *BankTransferService) RechargeBankTransfer(
	ctx context.Context,
	sn string,
) error {

	// 获取转账记录
	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	// 检查状态是否允许充值
	if transfer.Status != model.BankTransferStatusSalesUnconfirmed {
		return errors.New("bank transfer status not allowed to recharge").
			WithFields(errors.Fields{
				"sn":     sn,
				"status": transfer.Status,
			})
	}
	// 已确认，无需重复执行
	if transfer.Status == model.BankTransferStatusSalesConfirmed {
		return nil
	}

	// 更新状态为已确认
	err = s.dao.UpdateStatus(sn, model.BankTransferStatusSalesConfirmed)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	// 查询相关的记录，确认是否都已经确认完成，如果确认完成了，则开始充值
	ctx = context.WithoutCancel(ctx)
	go func() {
		time.Sleep(3 * time.Second)

		if transfer.ParentSN != "" {
			// 查询关联的记录
			relatedTransfers, err1 := s.dao.ListByQuery(ctx, &model.BankTransferQuery{
				Offset:   0,
				Limit:    1000,
				ParentSN: &transfer.ParentSN,
			})
			if err1 != nil {
				logging.GetLogger(ctx).
					WithError(err1).
					WithFields(logrus.Fields{
						"transfer": transfer,
					}).Error("list by query failed")
				return
			}
			if len(relatedTransfers) == 0 {
				logging.GetLogger(ctx).
					WithError(errors.New("missing related transfers")).
					WithFields(logrus.Fields{
						"transfer": transfer,
					}).Error("no related transfers found")
				return
			}
			for _, relatedTransfer := range relatedTransfers {
				if relatedTransfer.Status != model.BankTransferStatusSalesConfirmed {
					// 还存在没有确认的记录
					return
				}
			}
		}
		// 更新状态为充值中
		err = s.dao.UpdateStatus(sn, model.BankTransferStatusRecharging)
		if err != nil {
			return
		}
		// todo 开始充值

		// 更新状态为已充值
		err = s.dao.UpdateStatus(sn, model.BankTransferStatusRecharged)
		if err != nil {
			return
		}
	}()

	return nil
}

// RevokeBankTransfer 撤销银行转账申请
func (s *BankTransferService) RevokeBankTransfer(
	ctx context.Context,
	param *model.BankTransferRevokeParam,
) error {
	// 获取转账记录
	transfer, err := s.dao.GetBySN(param.SN)
	if err != nil {
		return errors.Trace(err).WithField("param", param)
	}
	// 已撤销或者正在撤销中
	if transfer.Status == model.BankTransferStatusRevoked ||
		transfer.Status == model.BankTransferStatusRevoking {
		return nil
	}
	if len(param.Reallocates) > 0 {
		// 如果需要重新分配，则检查金额是否和原纪录匹配
		reallocatedAmount := base.NewNMoney(0)
		for _, reallocate := range param.Reallocates {
			if reallocate.Uid == 0 {
				return errors.New("bank transfer reallocate uid invalid").
					WithFields(errors.Fields{
						"param":    param,
						"transfer": transfer,
					})
			}
			reallocatedAmount = reallocatedAmount.Add(reallocate.Amount)
		}
		if reallocatedAmount.MustCmp(transfer.Amount) != 0 {
			return errors.New("bank transfer amount sum not match").
				WithFields(errors.Fields{
					"param":    param,
					"transfer": transfer,
				})
		}
	}
	// 检查状态是否允许撤销
	if transfer.Status != model.BankTransferStatusRecharged {
		return errors.New("bank transfer status not allowed to revoke").
			WithFields(errors.Fields{
				"param":    param,
				"transfer": transfer,
			})
	}
	// 更新状态为撤销中
	err = s.dao.UpdateStatus(param.SN, model.BankTransferStatusRevoking)
	if err != nil {
		return errors.Trace(err).WithField("param", param)
	}
	ctx = context.WithoutCancel(ctx)
	go func() {
		// 仅撤销，不重新分配
		if len(param.Reallocates) == 0 {
			// todo 判断关账状态决定作废流水还是生成recharge_cancel流水
			// todo 调用 gaea、钱包接口
			// 更新状态为已撤销
			err = s.dao.UpdateStatus(param.SN, model.BankTransferStatusRevoked)
			if err != nil {
				return
			}
			return
		}
		err = s.reallocate(ctx, transfer, param)
		if err != nil {
			return
		}
	}()
	return nil
}

// reallocate 重新分配
func (s *BankTransferService) reallocate(
	ctx context.Context,
	transfer *model.BankTransfer,
	params *model.BankTransferRevokeParam,
) error {
	reallocated := make([]*model.BankTransfer, len(params.Reallocates))
	for i := range params.Reallocates {
		t := transfer
		t.ParentSN = transfer.SN
		t.SN = ""
		reallocated[i] = t
		s.dao.Save()
	}

	return nil
}

// SearchPaymentAccount 搜索支付账户信息
func (s *BankTransferService) SearchPaymentAccount(
	ctx context.Context,
	paymentAccount string,
) ([]string, error) {
	// 不记录info日志，避免与action层重复

	// 调用 DAO 层方法模糊查询打款账户
	accounts, err := s.dao.SearchPaymentAccounts(ctx, paymentAccount)
	if err != nil {
		return nil, errors.Trace(err).WithField("payment_account", paymentAccount)
	}

	return accounts, nil
}

// CreateBankTransfer 创建银行转账记录
func (s *BankTransferService) CreateBankTransfer(
	ctx context.Context,
	transfer *model.BankTransfer,
) error {
	// 不记录info日志，避免与action层重复

	// 设置初始状态为待审核
	transfer.Status = model.BankTransferStatusPendingReview

	// 保存记录
	err := s.dao.Save(transfer)
	if err != nil {
		return errors.Trace(err).WithField("transfer", transfer)
	}

	return nil
}

// EditBankTransfer 编辑银行转账记录
func (s *BankTransferService) EditBankTransfer(
	ctx context.Context,
	transfer *model.BankTransfer,
) error {
	// 不记录info日志，避免与action层重复

	// 获取原记录
	original, err := s.dao.GetBySN(transfer.SN)
	if err != nil {
		return errors.Trace(err).WithField("sn", transfer.SN)
	}

	// 检查状态是否允许编辑
	if original.Status != model.BankTransferStatusPendingReview &&
		original.Status != model.BankTransferStatusSalesUnconfirmed {
		return errors.New("bank transfer status not allowed to edit").WithFields(errors.Fields{
			"sn":     transfer.SN,
			"status": original.Status,
		})
	}

	// 保留原记录的ID和状态
	transfer.ID = original.ID
	transfer.Status = original.Status

	// 保存更新
	err = s.dao.Save(transfer)
	if err != nil {
		return errors.Trace(err).WithField("transfer", transfer)
	}

	return nil
}

// ReturnBankTransfer 退回银行转账记录
func (s *BankTransferService) ReturnBankTransfer(
	ctx context.Context,
	sn string,
) error {
	// 获取转账记录
	transfer, err := s.dao.GetBySN(sn)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}
	// 已经是退回状态了
	if transfer.Status == model.BankTransferStatusPendingReview {
		return nil
	}

	// 检查状态是否允许取消
	if transfer.Status != model.BankTransferStatusSalesUnconfirmed &&
		transfer.Status != model.BankTransferStatusSalesConfirmed {
		return errors.New("bank transfer status not allowed to return").WithFields(errors.Fields{
			"sn":     sn,
			"status": transfer.Status,
		})
	}

	// 更新状态为已撤销
	err = s.dao.UpdateStatus(sn, model.BankTransferStatusPendingReview)
	if err != nil {
		return errors.Trace(err).WithField("sn", sn)
	}

	return nil
}
