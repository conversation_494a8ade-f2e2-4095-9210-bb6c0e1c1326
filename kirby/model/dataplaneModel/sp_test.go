package dataplaneModel_test

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/kirby/model/dataplaneModel"
)

func TestSPORM(t *testing.T) {
	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	type testTable struct {
		Foo *dataplaneModel.SP `gorm:"type:VARCHAR(4096);not null"`
	}
	testWrap.DB().AutoMigrate(&testTable{})

	table := testWrap.DB().Model(&testTable{})

	x := testTable{
		Foo: &dataplaneModel.SP{
			"foo": "111",
			"bar": "233",
			"baz": "",
		},
	}

	err = table.Save(&x).Error
	assert.NoError(t, err)

	var val testTable
	err = table.First(&val).Error
	assert.NoError(t, err)
	assert.Equal(t, x.Foo, val.Foo)
}
