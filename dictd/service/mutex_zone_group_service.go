package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/samber/lo"
	"qiniu.io/pay/dictd/model"
)

func (s *DictBizService) ListMutexZoneGroupByItemCode(
	ctx context.Context,
	itemCodes []string,
) ([]*model.MutexZoneGroup, error) {
	logger := logging.GetLogger(ctx)
	if len(itemCodes) == 0 {
		return nil, errors.New("empty item codes")
	}
	mutexZoneGroupTables, err := s.dictDao.MutexZoneGroup.ListByItemCode(itemCodes, s.cacheExpires)
	if err != nil {
		logger.WithField("itemCodes", itemCodes).
			WithError(err).
			Error("GetByItemCode failed to get mutexZoneGroups by item code")
		return nil, err
	}
	result := make([]*model.MutexZoneGroup, 0)
	for _, table := range mutexZoneGroupTables {
		groupsStr := table.MutexGroups
		var mutexGroups [][]int64
		e := json.Unmarshal([]byte(groupsStr), &mutexGroups)
		if e != nil {
			logger.WithField("itemCodes", itemCodes).
				WithError(err).
				Error("unmarshal mutex groups failed")
			return nil, e
		}
		result = append(result, &model.MutexZoneGroup{
			ID:          table.ID,
			ItemCode:    table.ItemCode,
			MutexGroups: mutexGroups,
			UpdatedAt:   table.UpdatedAt,
		})
	}
	return result, nil
}

func (s *DictBizService) CreateOrUpdateMutexZoneGroup(ctx context.Context, group *model.MutexZoneGroup) error {
	logger := logging.GetLogger(ctx)
	trimmedItemCode := strings.TrimSpace(group.ItemCode)
	if trimmedItemCode == "" {
		return errors.New("empty item code")
	}

	err := s.validateZoneCodes(group)
	if err != nil {
		logger.WithError(err).
			WithField("mutexZoneGroup", group).
			Error("invalid zone code")
		return err
	}

	mutexGroupsByte, err := json.Marshal(group.MutexGroups)
	if err != nil {
		logger.WithError(err).
			WithField("mutexZoneGroup", group).
			Error("marshal mutex groups failed")
		return err
	}
	mutexZoneGroupTable := model.MutexZoneGroupTable{
		ID:          group.ID,
		ItemCode:    group.ItemCode,
		MutexGroups: string(mutexGroupsByte),
		IsDeleted:   false,
	}
	err = s.dictDao.MutexZoneGroup.Save(&mutexZoneGroupTable)
	if err != nil {
		logger.WithError(err).
			WithField("mutexZoneGroup", group).
			Error("save mutexZoneGroup failed")
		return err
	}
	return nil
}

func (s *DictBizService) ListMutexZoneGroups(ctx context.Context) ([]*model.MutexZoneGroup, error) {
	logger := logging.GetLogger(ctx)
	mutexZoneGroupTables, err := s.dictDao.MutexZoneGroup.List(s.cacheExpires)
	if err != nil {
		logger.WithError(err).
			Error("list mutex zone groups failed")
		return nil, err
	}
	result := make([]*model.MutexZoneGroup, 0)
	for _, table := range mutexZoneGroupTables {
		groupsStr := table.MutexGroups
		var mutexGroups [][]int64
		e := json.Unmarshal([]byte(groupsStr), &mutexGroups)
		if e != nil {
			logger.WithError(err).
				Error("unmarshal mutex groups failed")
			return nil, e
		}
		result = append(result, &model.MutexZoneGroup{
			ID:          table.ID,
			ItemCode:    table.ItemCode,
			MutexGroups: mutexGroups,
			UpdatedAt:   table.UpdatedAt,
		})
	}
	return result, nil
}

func (s *DictBizService) validateZoneCodes(group *model.MutexZoneGroup) error {
	if len(group.MutexGroups) == 0 {
		return errors.New("zone mutex groups cannot be empty")
	}
	if strings.TrimSpace(group.ItemCode) == "" {
		return errors.New("item code cannot be empty")
	}
	_, err := s.dictDao.Item.GetByCode(group.ItemCode)
	if err != nil {
		return err
	}
	// 判断指定 item 的 zone 是否存在
	zoneItemMap, err := s.dictDao.ZoneItemMap.ListByItemCode(group.ItemCode, 0, 100)
	if err != nil {
		return err
	}

	zoneIDs := make([]uint64, 0)
	for _, itemMap := range zoneItemMap {
		zoneIDs = append(zoneIDs, itemMap.ZoneID)
	}

	zones, errs := resultgroup.ParallelMapWithErrors(zoneIDs, func(param uint64) (*model.Zone, error) {
		return s.dictDao.Zone.GetByID(param)
	})

	for _, e := range errs {
		if e != nil {
			return e
		}
	}

	validZoneCodes := make([]int64, 0)
	for _, zone := range zones {
		validZoneCodes = append(validZoneCodes, zone.Code)
	}

	// 判断配置的 zone code 是否全部都是该计费项的
	difference, _ := lo.Difference(lo.Flatten(group.MutexGroups), validZoneCodes)
	if len(difference) > 0 {
		return fmt.Errorf("zone codes %+v not valid for item %s", difference, group.ItemCode)
	}

	return nil
}
