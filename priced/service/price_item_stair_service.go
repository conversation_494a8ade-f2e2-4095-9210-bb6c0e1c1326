package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/priced/model"
)

// GetPriceItemStairByID 根据 ID 查询阶梯价格
func (s *PriceBizService) GetPriceItemStairByID(
	ctx context.Context,
	id uint64,
) (*model.PriceItemStair, error) {
	return s.priceDao.PriceItemStair.GetByID(id, s.cacheExpires)
}

// CreatePriceItemStair 创建阶梯价格
func (s *PriceBizService) CreatePriceItemStair(
	ctx context.Context,
	stair *model.PriceItemStair,
) (*model.PriceItemStair, error) {
	stair.ID = 0
	err := s.priceDao.PriceItemStair.Save(stair, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return stair, err
}

// UpdatePriceItemStairByID 根据 ID 更新阶梯价格
func (s *PriceBizService) UpdatePriceItemStairByID(
	ctx context.Context,
	id uint64,
	stair *model.PriceItemStair,
) (*model.PriceItemStair, error) {
	stair.ID = id
	err := s.priceDao.PriceItemStair.Save(stair, s.cacheExpires)
	return stair, err
}

// DeletePriceItemStairByID 根据 ID 删除阶梯价格
func (s *PriceBizService) DeletePriceItemStairByID(
	ctx context.Context,
	id uint64,
) (*model.PriceItemStair, error) {
	stair, err := s.GetPriceItemStairByID(ctx, id)
	if err != nil {
		return nil, errors.Trace(err)
	}

	err = s.priceDao.PriceItemStair.Delete(stair)
	return stair, err
}

// ListAllPriceItemStairs 查询所有的阶梯价格
func (s *PriceBizService) ListAllPriceItemStairs(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.PriceItemStair, error) {
	return s.priceDao.PriceItemStair.List(offset, limit, s.cacheExpires)
}

// CountAllPriceItemStairs 查询所有阶梯数量
func (s *PriceBizService) CountAllPriceItemStairs(
	ctx context.Context,
) (uint64, error) {
	return s.priceDao.PriceItemStair.Count(s.cacheExpires)
}

// ListAllPriceItemStairsByPriceItemID 根据计费项 ID 查询所有相关的阶梯价格
func (s *PriceBizService) ListAllPriceItemStairsByPriceItemID(
	ctx context.Context,
	priceItemID uint64,
	offset int,
	limit int,
) ([]model.PriceItemStair, error) {
	return s.priceDao.PriceItemStair.ListByPriceItemID(priceItemID, offset, limit, s.cacheExpires)
}

// CountAllPriceItemStairsByPriceID 根据计费项 ID 查询所有相关的阶梯数量
func (s *PriceBizService) CountAllPriceItemStairsByPriceID(
	ctx context.Context,
	priceItemID uint64,
) (uint64, error) {
	return s.priceDao.PriceItemStair.CountByPriceItemID(priceItemID, s.cacheExpires)
}
