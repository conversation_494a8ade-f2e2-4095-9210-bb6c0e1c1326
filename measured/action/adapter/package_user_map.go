package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
)

// BuildPbPackageUserMapList covert slice pkgUserMaps to protobuf type
func BuildPbPackageUserMapList(
	objs []model.PackageUserMap,
	count uint64,
) (*pb.PackageUserMapList, error) {

	list := make([]*pb.PackageUserMap, len(objs))
	for i := range objs {
		result, err := BuildPbPackageUserMap(&objs[i])
		if err != nil {
			return nil, err
		}
		list[i] = result
	}

	return &pb.PackageUserMapList{
		Count:           count,
		PackageUserMaps: list,
	}, nil
}

// BuildPbPackageUserMap converts from model.PackageUserMap to the protobuf type.
func BuildPbPackageUserMap(x *model.PackageUserMap) (*pb.PackageUserMap, error) {
	return &pb.PackageUserMap{
		Id:         x.ID,
		Uid:        x.UID,
		PackageId:  x.PackageID,
		Excode:     x.Excode,
		EffectTime: timestamppb.New(x.EffectTime.TimeIn(time.UTC)),
		DeadTime:   timestamppb.New(x.DeadTime.TimeIn(time.UTC)),
		CreatedAt:  timestamppb.New(x.CreatedAt),
		UpdatedAt:  timestamppb.New(x.UpdatedAt),
	}, nil
}

// BuildModelPackageUserMap converts from pb.PackageUserMap to the model layer type.
func BuildModelPackageUserMap(x *pb.PackageUserMap) (*model.PackageUserMap, error) {
	var effectTime, deadTime time.Time
	if x.GetEffectTime() != nil {
		err := x.GetEffectTime().CheckValid()
		if err != nil {
			return nil, err
		}
		effectTime = x.GetEffectTime().AsTime()
	}

	if x.GetDeadTime() != nil {
		err := x.GetDeadTime().CheckValid()
		if err != nil {
			return nil, err
		}
		deadTime = x.GetDeadTime().AsTime()
	}

	var createdAt, updatedAt time.Time
	if x.GetCreatedAt() != nil {
		err := x.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = x.GetCreatedAt().AsTime()
	}

	if x.GetUpdatedAt() != nil {
		err := x.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = x.GetUpdatedAt().AsTime()
	}

	return &model.PackageUserMap{
		ID:         x.GetId(),
		UID:        x.GetUid(),
		PackageID:  x.GetPackageId(),
		EffectTime: base.NewHNS(effectTime),
		DeadTime:   base.NewHNS(deadTime),
		CreatedAt:  createdAt,
		UpdatedAt:  updatedAt,
	}, nil
}
