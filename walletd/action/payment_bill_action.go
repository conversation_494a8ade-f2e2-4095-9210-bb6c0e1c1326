package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"

	"github.com/qbox/pay-sdk/middleware/logging"

	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/walletd/action/adapter"
)

// DeductBill deduct bill
func (a *PaymentAction) DeductBill(ctx context.Context, request *pb.BillPaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : DeductBill")

	req, err := adapter.BuildBillPaymentRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildBillPaymentRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	txn, err := a.billPaymentService.Deduct(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("deduct bill failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}
	return pbTransaction, nil
}

func (a *PaymentAction) DeductDirectly(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : DeductDirectly")
	//TODO 如果今后真有这个需求再实现

	return &pb.PaymentTransaction{}, nil
}

func (a *PaymentAction) TriggerDeduct(ctx context.Context, request *pb.TriggerDeductRequest) (*empty.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"uid":    request.Uid,
		"payUid": request.PayUid,
	}).Info("New-Wallet : TriggerDeduct")

	err := a.genericService.TriggerDeduct(ctx, request.Uid, request.PayUid, nil)

	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":    request.Uid,
			"payUid": request.PayUid,
			"Method": "TriggerDeduct",
			"Impl":   "PaymentAction",
		}).Error("DeductTrigger failed", err)
		return &empty.Empty{}, err
	}

	return &empty.Empty{}, nil
}
