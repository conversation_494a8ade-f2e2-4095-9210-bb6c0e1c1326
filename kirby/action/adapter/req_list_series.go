package adapter

import (
	pb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/model/dataplaneModel"
)

func BuildReqListSeries(
	in *pb.ReqListSeries,
) *dataplaneModel.ReqListSeries {
	return &dataplaneModel.ReqListSeries{
		Epoch:        in.Epoch.AsTime(),
		Page:         in.Page,
		PageSize:     in.PageSize,
		DataTimeFrom: in.DataTimeFrom.AsTime(),
		DataTimeTo:   in.DataTimeTo.AsTime(),
		CtimeFrom:    in.CtimeFrom.AsTime(),
		CtimeTo:      in.CtimeTo.AsTime(),
		SeriesKeys:   in.SeriesKeys,
		MBUUIDs:      in.MbUuids,
		QRNs:         in.Qrns,
		RTDCodes:     in.RtdCodes,
		UIDs:         in.Uids,
		MMs:          in.Mms,
		SPConditions: buildSPConditions(in.SpConditions),
	}
}
