package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/qpay/wallet/model"
	"qiniu.io/pay/walletd/action/adapter"
)

func (a *PaymentAction) AddCurrency(ctx context.Context, request *pb.AddCurrencyRequest) (*pb.Currency, error) {
	currency, err := a.factory.CurrencyService.AddCurrency(ctx, request.Uid, request.CurrencyType, request.IsDefault)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCurrency(currency)
}

func (a *PaymentAction) HasCurrency(ctx context.Context, request *pb.HasCurrencyRequest) (*pb.HasCurrencyResponse, error) {
	hasCurrency, err := a.factory.CurrencyService.HasCurrency(ctx, request.Uid, request.CurrencyType)
	if err != nil {
		return nil, err
	}
	return &pb.HasCurrencyResponse{
		HasCurrency: hasCurrency,
	}, err
}

func (a *PaymentAction) DeleteCurrency(ctx context.Context, request *pb.DeleteCurrencyRequest) (*pb.DeleteCurrencyResponse, error) {
	err := a.factory.CurrencyService.DeleteCurrency(ctx, request.Uid, request.CurrencyType)
	return &pb.DeleteCurrencyResponse{}, err
}

func (a *PaymentAction) GetUserCurrencies(ctx context.Context, request *pb.GetUserCurrenciesRequest) (*pb.GetUserCurrenciesResponse, error) {
	currencies, err := a.factory.CurrencyService.GetUserCurrency(ctx, request.Uid)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbGetUserCurrenciesResponse([]*model.Currency{currencies})
}

func (a *PaymentAction) GetSingleCurrency(ctx context.Context, request *pb.UIDParam) (*pb.Currency, error) {

	// todo getOnly.... should use ctx , also should return a model.Currency instead of type enum
	// todo can getOnly rename to getSingleCurrency ?

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, request.GetUid())

	if err != nil {
		return nil, err
	}

	return adapter.BuildPbCurrency(&model.Currency{
		CurrencyType: currencyType,
	})
}
