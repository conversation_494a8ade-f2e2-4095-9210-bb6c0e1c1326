package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/bo-base/v4/errors"

	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// GetScopeByID 根据ID获取计费范围
func (a *WalletAction) GetScopeByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.Scope, error) {
	scope, err := a.walletBizSrv.GetScopeByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScope(scope)
}

// CreateScope 创建计费范围
func (a *WalletAction) CreateScope(
	ctx context.Context,
	scope *pb.Scope,
) (*pb.Scope, error) {
	m, err := adapter.BuildScope(scope)
	if err != nil {
		return nil, err
	}

	s, err := a.walletBizSrv.CreateScope(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScope(s)
}

// UpdateScopeByID 根据ID更新计费范围
func (a *WalletAction) UpdateScopeByID(
	ctx context.Context,
	param *pb.IDScopeParam,
) (*pb.Scope, error) {
	m, err := adapter.BuildScope(param.GetScope())
	if err != nil {
		return nil, err
	}

	scope, err := a.walletBizSrv.UpdateScopeByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScope(scope)
}

// DeleteScopeByID 根据ID删除计费范围
func (a *WalletAction) DeleteScopeByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.Scope, error) {
	scope, err := a.walletBizSrv.DeleteScopeByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScope(scope)
}

// ListAllScopes 分页获取所有计费范围
func (a *WalletAction) ListAllScopes(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.ScopeList, error) {
	count, err := a.walletBizSrv.CountAllScopes(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	scopes, err := a.walletBizSrv.ListAllScopes(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeList{
		Count:  count,
		Scopes: make([]*pb.Scope, len(scopes)),
	}
	for i, scope := range scopes {
		_s, err := adapter.BuildPbScope(&scope)
		if err != nil {
			return nil, err
		}
		list.Scopes[i] = _s
	}
	return list, nil
}

// CountAllScopes 获取所有计费范围的数量
func (a *WalletAction) CountAllScopes(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountAllScopes(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// CreateScopeDetail 创建计费范围详情
func (a *WalletAction) CreateScopeDetail(
	ctx context.Context,
	detail *pb.ScopeDetail,
) (*pb.ScopeDetail, error) {
	m, err := adapter.BuildScopeDetail(detail)
	if err != nil {
		return nil, err
	}

	d, err := a.walletBizSrv.CreateScopeDetail(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeDetail(d)
}

// GetScopeDetailByID 根据ID获取计费范围详情
func (a *WalletAction) GetScopeDetailByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeDetail, error) {
	detail, err := a.walletBizSrv.GetScopeDetailByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeDetail(detail)
}

// ListAllScopeDetails 分页获取所有的计费范围详情
func (a *WalletAction) ListAllScopeDetails(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.ScopeDetailList, error) {
	count, err := a.walletBizSrv.CountAllScopes(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	details, err := a.walletBizSrv.ListAllScopeDetails(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeDetailList{
		Count:        count,
		ScopeDetails: make([]*pb.ScopeDetail, len(details)),
	}
	for i, detail := range details {
		_detail, err := adapter.BuildPbScopeDetail(&detail)
		if err != nil {
			return nil, err
		}
		list.ScopeDetails[i] = _detail
	}
	return list, nil
}

// ScopeCheckLegacy 用 v3 规则检查一个给定的计费项/计费项组/产品是否属于给定的 Scope
func (a *WalletAction) ScopeCheckLegacy(
	ctx context.Context,
	param *pb.ScopeCheckParam,
) (*pb.ScopeCheckResult, error) {
	ok, err := a.walletBizSrv.ScopeCheckLegacyInlineByCode(
		ctx, param.GetScope(), param.GetItemCode(), param.GetGroupCode(), param.GetProductCode())
	if err != nil {
		return nil, err
	}
	return &pb.ScopeCheckResult{
		Ok: ok,
	}, nil
}

// ScopeCheckByItemCode 检查 code 所指定的计费项是否属于给定的 Scope
func (a *WalletAction) ScopeCheckByItemCode(
	ctx context.Context,
	param *pb.IDCodeParam,
) (*pb.ScopeCheckResult, error) {
	ok, err := a.walletBizSrv.ScopeCheckByItemCode(ctx, param.GetId(), param.GetCode())
	if err != nil {
		return nil, err
	}
	return &pb.ScopeCheckResult{
		Ok: ok,
	}, nil
}

// ScopeCheckByItemID 检查 scope 是否包含指定 item_id
func (a *WalletAction) ScopeCheckByItemID(
	ctx context.Context,
	param *pb.ScopeCheckIDParam,
) (*pb.ScopeCheckResult, error) {
	ok, err := a.walletBizSrv.ScopeCheckLegacyByID(ctx, param.GetScopeId(), param.GetId(), 0, 0)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.ScopeCheckResult{
		Ok: ok,
	}, nil
}

// ScopeCheckByGroupID 检查 scope 是否包含指定 group_id
func (a *WalletAction) ScopeCheckByGroupID(
	ctx context.Context,
	param *pb.ScopeCheckIDParam,
) (*pb.ScopeCheckResult, error) {
	ok, err := a.walletBizSrv.ScopeCheckLegacyByID(ctx, param.GetScopeId(), 0, param.GetId(), 0)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.ScopeCheckResult{
		Ok: ok,
	}, nil
}

// ScopeCheckByProductID 检查 scope 是否包含指定 product_id
func (a *WalletAction) ScopeCheckByProductID(
	ctx context.Context,
	param *pb.ScopeCheckIDParam,
) (*pb.ScopeCheckResult, error) {
	ok, err := a.walletBizSrv.ScopeCheckLegacyByID(ctx, param.GetScopeId(), 0, 0, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &pb.ScopeCheckResult{
		Ok: ok,
	}, nil
}
