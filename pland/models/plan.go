package models

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	pb "github.com/qbox/pay-sdk/plan"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"

	"qiniu.io/pay/pland/models/enums"
)

// PlanDao plan dao
type PlanDao struct {
	base *dao.BaseDao

	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *PlanDao) GetCachePrefix() string {
	return "sufy-plan:plan:"
}

// GetCacheRefs get cache refs
func (d *PlanDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewPlanDao new plan dao
func NewPlanDao(base *dao.BaseDao) *PlanDao {
	cachePrefix := (*PlanDao)(nil).GetCachePrefix()
	return &PlanDao{
		base:        base,
		cachePrefix: cachePrefix,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:code={Code}", cachePrefix),
		),
	}
}

// Plan mysql model
type Plan struct {
	ID           uint64            `json:"id" gorm:"primaryKey"`
	Level        enums.PlanLevel   `json:"level" gorm:"level"`
	Version      uint32            `json:"version" gorm:"version"`
	Description  string            `json:"description" gorm:"description"`
	Comment      string            `json:"comment" gorm:"comment"`
	Status       enums.PlanStatus  `json:"status" gorm:"status"`
	Price        *base.NMoney      `json:"price" gorm:"price"`
	CurrencyType base.CurrencyType `json:"currency_type" gorm:"currency_type"`
	EffectTime   int64             `json:"effect_time" gorm:"effect_time"`
	TimerFlag    bool              `json:"timer_flag" gorm:"timer_flag"`
	CreatedAt    time.Time         `json:"created_at" sql:"type:DATETIME(6)"`
	Packs        *PlanPackModel    `json:"packs" gorm:"packs"`
	// 用于辅助计算status，无需落库,
	Draft bool `json:"-" gorm:"-"`
}

var (
	_ driver.Valuer = (*PlanPackModel)(nil)
	_ sql.Scanner   = (*PlanPackModel)(nil)
)

// PlanPackModel plan 的套餐整体信息
// 这里使用 struct 是为了 json 序列化后是 object，这样可以方便的添加其他字段,为将来需求迭代留下扩展空间
type PlanPackModel struct {
	Packs []PlanPacks `json:"packs"`
}

// Value implements driver.Valuer
func (p *PlanPackModel) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan implements sql.Scanner
func (p *PlanPackModel) Scan(src any) error {
	if src == nil {
		return nil
	}
	if x, ok := src.([]byte); ok {
		return json.Unmarshal(x, p)
	}
	return errors.New("packs data format was wrong")
}

// SearchPacks search pack in packs
// enterprise 没有 packs, 返回 false
func (p *Plan) SearchPacks(
	packCode string,
) (*PlanPacks, bool) {
	if packCode == "" {
		return nil, false
	}
	if p == nil ||
		p.Packs == nil ||
		len(p.Packs.Packs) <= 0 {
		return nil, false
	}
	for i, pack := range p.Packs.Packs {
		if pack.Code == packCode {
			return &p.Packs.Packs[i], true
		}
	}
	return nil, false
}

// FetchFirstPacks 获取 plan 内的第一个套餐
// enterprise 没有 packs, 返回 空结构体默认值
func (p *Plan) FetchFirstPacks() PlanPacks {
	if p == nil ||
		p.Packs == nil ||
		len(p.Packs.Packs) <= 0 {
		return PlanPacks{}
	}
	return p.Packs.Packs[0]
}

// PlanPacks 套餐信息
type PlanPacks struct {
	// code 套餐 code
	Code string `json:"code"`
	// name 套餐名称
	Name string `json:"name"`
	// 描述
	Description string `json:"description"`
	// rights 套餐内权益
	Rights []PackRights `json:"rights"`
}

// PackRights 套餐内权益
type PackRights struct {
	// code 权益 code
	Code string `json:"code"`
	// name 权益对外展示名称
	Name string `json:"name"`
	// type 权益类型：价格折扣、免费额度
	Type enums.RightsType `json:"type"`
	// item_code 计费项
	ItemCode string `json:"item_code"`
	// 是否是融合计费项
	IsCombo bool `json:"is_combo"`
	// 区域
	Zone uint32 `json:"zone"`
	// 区域名称
	ZoneName string `json:"zone_name"`
	// unit_str 单位, type 为免费额度时有值
	UnitStr string `json:"unit_str"`
	// quota 免费额度, type 为免费额度时有值
	Quota uint64 `json:"quota"`
	// discount 价格折扣, type 为价格折扣时有值
	Discount uint32 `json:"discount"`

	// 此字段由 AttachPriceOnRights() 即时补充，不落库
	Price *Price `json:"-"`
	// 此字段由 AttachUsageOnRights() 即时补充，不落库
	Used int64 `json:"-"`
	// 此字段由 AttachUsageOnRights() 即时补充，不落库
	DeductDetails *pb.DeductDetail `json:"-"`
}

// Price of item
type Price struct {
	Type string `json:"-"`
	// 价格阶梯
	Ranges []*Range `json:"-"`
}

// Range range
type Range struct {
	From    uint64 `json:"-"`
	FromStr string `json:"-"`
	// 阶梯上限
	To    uint64 `json:"-"`
	ToStr string `json:"-"`
	// 价格
	Price string `json:"-"`
}

// GenFreeRespackID 生成免费资源包的 id
func (r *PackRights) GenFreeRespackID() string {
	if r.Type == enums.RightsTypePriceDiscount {
		// 折扣权益,不是免费额度
		return ""
	}
	return fmt.Sprintf("plan:%s:%d", r.ItemCode, r.Quota)
}

// Insert insert
func (d *PlanDao) Insert(m *Plan) error {

	err := d.base.ExecuteWithSyncDelCache(func(value interface{}) error {
		return d.base.Save(value).Error
	}, d.keys, m, d)

	if err != nil {
		return err
	}
	return nil
}

// List query plans by code
func (d *PlanDao) List(
	ctx context.Context,
	level string,
	offset int,
	limit int,
) (plans []*Plan, err error) {
	err = d.base.Model(&Plan{}).
		Where("level = ?", level).
		Offset(offset).
		Limit(limit).
		Order("version DESC").
		Find(&plans).
		Error
	if err != nil {
		return nil, err
	}
	return plans, nil
}

// ListOnlinePlans 查询生效中的 plan
func (d *PlanDao) ListOnlinePlans(
	ctx context.Context,
) (plans []*Plan, err error) {
	err = d.base.Model(&Plan{}).
		Where("status = ?", enums.PlanStatusOnline).
		Find(&plans).
		Error
	if err != nil {
		return nil, err
	}
	return plans, nil
}

// GetByLevelAndVersion 获取特定版本的 plan
func (d *PlanDao) GetByLevelAndVersion(
	ctx context.Context,
	level enums.PlanLevel,
	version uint32,
) (*Plan, error) {
	var plan Plan
	err := d.base.Model(&Plan{}).
		Where(
			"level = ? AND version = ?",
			level.String(), version,
		).
		Take(&plan).
		Error
	if err != nil {
		return nil, err
	}
	return &plan, nil
}

// GetOnlineByLevel 获取特定 level 在线的 plan (有且仅有一个)
func (d *PlanDao) GetOnlineByLevel(
	ctx context.Context,
	level enums.PlanLevel,
) (*Plan, error) {
	var res Plan
	err := d.base.Model(&Plan{}).
		Where(
			"level = ? AND status = ? ",
			level.String(), enums.PlanStatusOnline,
		).
		Offset(0).
		Limit(1).
		Order("version DESC").
		Take(&res).
		Error
	if err != nil {
		return nil, err
	}
	return &res, err
}

// GetLatestPlanByLevel 获取特定 level 的最新版本 plan
func (d *PlanDao) GetLatestPlanByLevel(
	ctx context.Context,
	level enums.PlanLevel,
) (*Plan, error) {
	var res Plan
	err := d.base.Model(&Plan{}).
		Where("level = ? ", level).
		Order("version DESC").
		Offset(0).
		Limit(1).
		Take(&res).
		Error
	if err != nil {
		return nil, err
	}
	return &res, nil
}

// GetExistsTimingPlan 获取某等级的已有计划中 plan
func (d *PlanDao) GetExistsTimingPlan(
	ctx context.Context,
	level enums.PlanLevel,
	effectTime int64,
) (*Plan, error) {
	var res Plan
	err := d.base.Model(&Plan{}).
		Where(
			"level = ? AND effect_time = ? AND timer_flag = true",
			level, effectTime,
		).
		Offset(0).
		Limit(1).
		Take(&res).
		Error
	if err != nil {
		return nil, err
	}
	return &res, nil
}

// ResetTimingFlag 重置定时标记
func (d *PlanDao) ResetTimingFlag(
	ctx context.Context,
	id uint64,
) error {
	return d.base.Model(&Plan{}).
		Where("id = ? ", id).
		Updates(map[string]any{
			"timer_flag": false,
		}).Error
}

// SetOffline 下线 plan
func (d *PlanDao) SetOffline(
	ctx context.Context,
	level enums.PlanLevel,
) error {
	return d.base.Model(&Plan{}).
		Where("level = ? AND status = ?", level.String(), enums.PlanStatusOnline).
		Updates(map[string]any{
			"status":     enums.PlanStatusOffline,
			"timer_flag": false,
		}).Error
}

// UpdateStatusByID 更新具体id plan 状态
func (d *PlanDao) UpdateStatusByID(
	ctx context.Context,
	id uint64,
	status enums.PlanStatus,
) error {
	return d.base.Model(&Plan{}).
		Where("id = ?", id).
		Update("status", status).
		Error
}

// ListWaitingOnline 获取临近 now 待上线的 plan 列表
func (d *PlanDao) ListWaitingOnline(
	ctx context.Context,
	level enums.PlanLevel,
	now time.Time,
) (plans []*Plan, err error) {
	// select * from plans
	// where level=? and status==offline
	// and effect_time<=now and timer_flag = true
	// order by effect_time asc
	err = d.base.Model(&Plan{}).
		Where(
			"level = ? AND status = ? AND effect_time <= ? AND timer_flag = true",
			level.String(),
			enums.PlanStatusOffline,
			now.Unix(),
		).
		Find(&plans).
		Offset(0).
		Limit(1000). // 同一个 level 下，不会存在太多要临近上线的记录
		Error
	return
}
