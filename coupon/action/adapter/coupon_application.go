package adapter

import (
	"errors"

	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/model"
	"qiniu.io/pay/coupon/service"
)

// BuildLaunchCouponApplication converts from pb.LaunchCouponApplicationParams to the model layer type.
func BuildLaunchCouponApplication(param *pb.LaunchCouponApplicationParams, applicantEmail string) (*service.LaunchCouponApplicationParams, error) {
	if param == nil || param.CreateCouponBatchParams == nil {
		return nil, errors.New("invalid param")
	}
	pbCreateCouponBatchParams := param.CreateCouponBatchParams

	if pbCreateCouponBatchParams.GetScope() == nil {
		return nil, errors.New("invalid param")
	}

	sellerIdsInclude := pbCreateCouponBatchParams.GetScope().GetSellerIdsInclude()
	productIdsInclude := pbCreateCouponBatchParams.GetScope().GetProductIdsInclude()
	skuIdsInclude := pbCreateCouponBatchParams.GetScope().GetSkuIdsInclude()
	if pbCreateCouponBatchParams.GetIsUnlimitedScope() {
		sellerIdsInclude = []int64{}
		productIdsInclude = []int64{}
		skuIdsInclude = []int64{}
	}
	startTime := param.CreateCouponBatchParams.GetStartTime()
	endTime := param.CreateCouponBatchParams.GetEndTime()
	if startTime == nil || endTime == nil {
		return nil, errors.New("invalid start time or end time")
	}
	if startTime.AsTime().After(endTime.AsTime()) {
		return nil, errors.New("invalid start time and end time")
	}

	if param.CreateCouponBatchParams.GetTimePeriodType() == pb.COUPON_TIME_PERIOD_TYPE_CONST_DURATION {
		if param.CreateCouponBatchParams.EffectDays == 0 {
			return nil, errors.New("invalid coupon effect days")
		}
	}

	if param.CreateCouponBatchParams.GetTimePeriodType() == pb.COUPON_TIME_PERIOD_TYPE_ABSOLUTE {
		couponEffectTime := param.GetCreateCouponBatchParams().GetCouponEffectTime()
		couponDeadTime := param.GetCreateCouponBatchParams().GetCouponDeadTime()
		if couponEffectTime == nil || couponDeadTime == nil {
			return nil, errors.New("invalid coupon effect or dead time")
		}
		if couponEffectTime.AsTime().After(couponDeadTime.AsTime()) {
			return nil, errors.New("invalid coupon effect time and dead time")
		}
	}

	createCouponBatchParams := &service.LaunchCreateCouponBatchParams{
		Name:                      pbCreateCouponBatchParams.GetName(),
		Description:               pbCreateCouponBatchParams.GetDescription(),
		Remark:                    pbCreateCouponBatchParams.GetRemark(),
		Reason:                    model.CouponReason(pbCreateCouponBatchParams.GetReason()),
		ReasonDesc:                pbCreateCouponBatchParams.GetReasonDesc(),
		BindMethod:                model.CouponBindMethod(pbCreateCouponBatchParams.GetBindMethod()),
		StartTime:                 pbCreateCouponBatchParams.StartTime.AsTime(),
		EndTime:                   pbCreateCouponBatchParams.EndTime.AsTime(),
		BatchSize:                 pbCreateCouponBatchParams.GetBatchSize(),
		MaxActivationTimesPerUser: pbCreateCouponBatchParams.GetMaxActivationTimesPerUser(),
		TimePeriodType:            model.CouponTimePeriodType(pbCreateCouponBatchParams.GetTimePeriodType()),
		EffectDays:                pbCreateCouponBatchParams.EffectDays,
		CouponEffectTime:          pbCreateCouponBatchParams.CouponEffectTime.AsTime(),
		CouponDeadTime:            pbCreateCouponBatchParams.CouponDeadTime.AsTime(),
		ThresholdMoney:            pbCreateCouponBatchParams.ThresholdMoney,
		CouponMoney:               pbCreateCouponBatchParams.CouponMoney,
		IsMultipleUse:             pbCreateCouponBatchParams.IsMultipleUse,
		UserScope:                 model.CouponUserType(pbCreateCouponBatchParams.GetUserScope()),
		PayModeScope:              model.CouponPayMode(pbCreateCouponBatchParams.GetPayModeScope()),
		ArrearCanUse:              pbCreateCouponBatchParams.GetArrearCanUse(),
		IsUnlimitedScope:          pbCreateCouponBatchParams.GetIsUnlimitedScope(),
		Scope: &service.CouponScope{
			SellerIdsInclude:  sellerIdsInclude,
			ProductIdsInclude: productIdsInclude,
			SkuIdsInclude:     skuIdsInclude,
		},
		CouponScopeDesc: pbCreateCouponBatchParams.GetCouponScopeDesc(),
		Applicant:       model.ApplicantType(pbCreateCouponBatchParams.GetApplicant()),
	}
	return &service.LaunchCouponApplicationParams{
		UID:                     param.GetUid(),
		CreateCouponBatchParams: createCouponBatchParams,
		ApplicantEmail:          applicantEmail,
	}, nil
}
