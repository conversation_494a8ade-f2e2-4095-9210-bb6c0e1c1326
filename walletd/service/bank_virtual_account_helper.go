package service

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/http"
	"time"
)

//nolint:unused,varcheck // returnCodeSucceed 和 returnCodeUnknown 暂时不用，以后可能会用到
const (
	returnCodeFailed  = "66666" // 失败
	returnCodeSucceed = "55555" // 成功
	returnCodeUnknown = "77777" // 不确定
)

// BankReqMessage 银行请求报文带 checksum 的公共部分
type BankReqMessage[T any] struct {
	// Packet 报文
	Packet T `json:"packet"`
	// CheckSum 签名串
	CheckSum string `json:"checkSum"`
}

// BankReqPacket 银行请求报文公共部分
type BankReqPacket[T any] struct {
	// TranCode 交易代码
	TranCode string `json:"tranCode"`
	// ActorCode 参与者代码，参与者代码，12位，由浦发清结算平台发放。
	ActorCode string `json:"actorCode"`
	// SystemCode 系统编码，接入系统的编码，由浦发清结算平台发放。
	SystemCode string `json:"systemCode"`
	// UserCode 用户代码，参与者使用浦发清结算平台的虚拟用户名，由浦发清结算平台发放。
	UserCode string `json:"userCode"`
	// UserPass 用户密码，参与者使用浦发清结算平台的虚拟用户密码，由浦发清结算平台发放。
	UserPass string `json:"userPass"`
	// PacketNo 报文编号，[actorCode][yyyymmdd][12位顺序号]，顺序号参与者当天不重复
	PacketNo string `json:"packetNo"`
	// OrionNo 原报文编号
	OrionNo string `json:"orionNo"`
	// VerNo 版本号，接口规范版本号
	VerNo string `json:"verNo"`
	// CreatedDate 创建时间，[yyyy-mm-ddThh24:mi:ss]
	CreatedDate string `json:"createdDate"`
	// Body 报文体
	Body T `json:"body"`
}

// GenVirtAccReqBody 批量生成虚账号 req body
type GenVirtAccReqBody struct {
	// BusiSerial 业务编号；用于标识一笔业务，全局唯一，用于结果查询。以 [参与者]+[yyyyymmdd]+[8位流水号] 规则产生
	BusiSerial string `json:"busiSerial"`
	// AccName 实账户户名
	AccName string `json:"accName"`
	// AccCode 实账户账号
	AccCode string `json:"accCode"`
	// OpenNum 开户数量
	OpenNum int `json:"openNum"`
	// reserver1 预留字段 1
	Reserver1 string `json:"reserver1"`
	// reserver2 预留字段 2
	Reserver2 string `json:"reserver2"`
	// reserver3 预留字段 3
	Reserver3 string `json:"reserver3"`
	// reserver4 预留字段 4
	Reserver4 string `json:"reserver4"`
	// reserver7 预留字段 7
	Reserver7 string `json:"reserver7"`
	// BusiTime 业务时间戳（格式 2006-01-02T15:04:05）
	BusiTime string `json:"busiTime"`
}

// BankRespMessage 银行响应体带 checksum 的公共部分
type BankRespMessage[T any] struct {
	// Packet 报文
	Packet BankRespPacket[T] `json:"packet"`
	// CheckSum 签名串
	CheckSum string `json:"checkSum"`
}

// BankRespPacket 银行响应体公共部分
type BankRespPacket[T any] struct {
	// Body 报文体
	Body T `json:"body"`
	// CreatedDate 创建时间
	CreatedDate string `json:"createdDate"`
	// OrionNo 原报文编号
	OrionNo string `json:"orionNo"`
	// PacketNo 报文编号，yymmdd[10位顺序号];不能重复。userCode+packetNo不能重复
	PacketNo string `json:"packetNo"`
	// ReturnCode 返回码，55555-成功；77777-不确定；66666-失败。表示该交易处理结果，业务结果在body中体现。
	ReturnCode string `json:"returnCode"`
	// ReturnMess 返回值
	ReturnMess string `json:"returnMess"`
	// VerNo 版本号，接口规范版本号
	VerNo string `json:"verNo"`
}

// VirtAccInfoRespItem 银行返回值虚账号信息
type VirtAccInfoRespItem struct {
	// VAcctName 虚账户名称
	VAcctName string `json:"vAcctName"`
	// VAcctNo 虚账户账号
	VAcctNo string `json:"vAcctNo"`
}

// VirtAccInfoRespBody 获取虚拟账号 resp body
type VirtAccInfoRespBody struct {
	// Lists 返回值列表
	Lists []VirtAccInfoRespItem `json:"lists"`
	CommonBankRespPacketBody
}

// CommonBankRespPacketBody 浦发银行返回体公共字段
type CommonBankRespPacketBody struct {
	// RstCode 结果代码
	RstCode string `json:"rstCode"`
	// RstCode 结果信息
	RstMess string `json:"rstMess"`
}

// TransferCurrencyPacketBody 浦发银行支付接口 body
type TransferCurrencyPacketBody struct {
	// PacketID 批次号，确保全局唯一
	PacketID string `json:"packetid"`
	// TotalNumber 支付总笔数，与下面的 len(Lists) 相同
	TotalNumber string `json:"totalNumber"`
	// TotalAmount 本次支付的总金额，等于 sum(Lists[i].Amount)
	TotalAmount string `json:"totalAmount"`
	// BusiTime 发起该笔业务时的业务时间戳，like "2017-10-09T14:29:30"
	BusiTime string `json:"busiTime"`
	// Lists 支付流水明细
	Lists []TransferCurrencyItem `json:"lists"`
}

type TransferCurrencyItem = struct {
	// SerialNo 流水号，确保全局唯一
	SerialNo string `json:"serialNo"`
	// TradeDate 交易发起日期，like "2017-06-01"
	TradeDate string `json:"tradeDate"`
	// 交易金额，like 0.12
	Amount string `json:"amount"`
	// PayAccName 付方账户户名
	PayAccName string `json:"payAccName"`
	// PayAccCode 账号/卡号
	PayAccCode string `json:"payAccCode"`
	// PayBankCode 账户银行代码
	PayBankCode string `json:"payBankCode"`
	// PayHouseBankName 开户行全称
	PayHouseBankName string `json:"payHouseBankName"`
	// PayHouseBankCode 开户行人行号
	PayHouseBankCode string `json:"payHouseBankCode"`
	// PayReserver1 预留字段1
	PayReserver1 string `json:"payReserver1"`
	PayReserver2 string `json:"payReserver2"`
	PayReserver3 string `json:"payReserver3"`
	// RecAccName 收方账户户名
	RecAccName string `json:"recAccName"`
	// RecAccCode 账号/卡号
	RecAccCode string `json:"recAccCode"`
	// RecAccType 0-对公账户,1-一卡通
	RecAccType string `json:"recAccType"`
	// RecBankCode 账户银行代码
	RecBankCode string `json:"recBankCode"`
	// RecHouseBankName 开户行全称
	RecHouseBankName string `json:"recHouseBankName"`
	// RecHouseBankCode 账户开户行人行号
	RecHouseBankCode string `json:"recHouseBankCode"`
	RecReserver1     string `json:"recReserver1"`
	RecReserver2     string `json:"recReserver2"`
	RecReserver3     string `json:"recReserver3"`
	CurrencyCode     string `json:"currencyCode"`
	// Purpose 订单用途
	Purpose   string `json:"purpose"`
	Reserver1 string `json:"reserver1"`
	Reserver2 string `json:"reserver2"`
	Reserver3 string `json:"reserver3"`
	Reserver4 string `json:"reserver4"`
	Reserver5 string `json:"reserver5"`
}

// QueryBalanceReqBody 浦发银行查询余额接口 req body
type QueryBalanceReqBody struct {
	// AccCode 要查询余额的账号
	AccCode string `json:"accCode"`
	// BusiTime 发起该笔业务时的业务时间戳 like "2017-10-09T14:29:30"
	BusiTime string `json:"busiTime"`
}

// QueryBalanceRespBody 浦发银行查询余额接口 resp body
type QueryBalanceRespBody struct {
	// AccCode 账号
	AccCode string `json:"accCode"`
	// CurrencyCode 币种编码，遵循ISO币种标准，人民币-CNY
	CurrencyCode string `json:"currencyCode"`
	// Balance 账户余额
	Balance string `json:"balance"`
	// ReserveBalance 保留余额
	ReserveBalance string `json:"reserveBalance"`
	// FreezeBalance 冻结余额
	FreezeBalance string `json:"freezeBalance"`
	// CortrolBalance 控制余额
	CortrolBalance string `json:"cortrolBalance"`
	// CanUseBalance 可用金额
	CanUseBalance string `json:"canUseBalance"`
}

// QueryTransferCurrencyResultReqBody 浦发银行查询支付结果 req body
type QueryTransferCurrencyResultReqBody struct {
	// PacketID 批次号, 需要保持与 dpe.400.090 接口的批次号一致
	PacketID string `json:"packetid"`
	// BusiTime 发起该笔业务时的业务时间戳，形如 "2017-10-09T14:29:30"
	BusiTime string `json:"busiTime"`
	// Lists 查询的流水列表，最大支持100笔
	Lists []QueryTransferCurrencyResultReqItem `json:"lists"`
}

type QueryTransferCurrencyResultReqItem struct {
	// SerialNo 流水号， dpe.400.090 支付时的流水号（如流水号送空则默认查询批次号下所有单据的支付结果）
	SerialNo string `json:"serialNo"`
}

// QueryTransferCurrencyResultRespBody 浦发银行查询支付结果 resp body
type QueryTransferCurrencyResultRespBody struct {
	// PacketID 批次号
	PacketID string `json:"packetid"`
	// Lists 查询的流水列表的返回结果数组
	Lists []QueryTransferCurrencyResultRespItem `json:"lists"`
}

type QueryTransferCurrencyResultRespItem struct {
	// SerialNo 流水号，支付时的流水号
	SerialNo string `json:"serialNo"`
	CommonBankRespPacketBody
	SplitNumber string           `json:"splitNumber"`
	SplitLists  []SplitListsItem `json:"splitLists"`
}

type SplitListsItem struct {
	SplitSerialNo string `json:"splitSerialNo"`
	CommonBankRespPacketBody
}

// genRandomNumber 生成指定长度的随机数
func genRandomNumber(numLen int) string {
	fmtStr := fmt.Sprintf("%%0%dv", numLen)
	return fmt.Sprintf(fmtStr, rand.New(rand.NewSource(time.Now().UnixNano())).Int63n(int64(math.Pow10(numLen))))
}

type bankHTTPClient struct {
	*http.Client
}

// newBankHTTPClient 生成调用银行接口使用的 httpClient
func newBankHTTPClient() *bankHTTPClient {
	return &bankHTTPClient{
		&http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
		},
	}
}

func (b *bankHTTPClient) send(reqURL string, msg []byte) ([]byte, error) {
	resp, err := b.Post(reqURL, "application/json", bytes.NewBuffer(msg))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return body, nil
}
