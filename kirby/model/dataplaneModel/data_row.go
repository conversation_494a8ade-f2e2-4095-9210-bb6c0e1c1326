package dataplaneModel

import (
	"time"
)

type dataRow struct {
	// T0 是此行数据所代表计量点的最早时刻，即对应 P[0] 这一计量点的时刻
	T0 time.Time
	// P 是此行数据的所有计量值
	P []uint64
}

func makeRowForScanning(numPoints int) dataRow {
	return dataRow{
		T0: time.Time{},
		P:  make([]uint64, numPoints),
	}
}

func (r *dataRow) scanArgsForT0AndPoints() []any {
	result := make([]any, len(r.P)+1)
	result[0] = &r.T0
	for i := range r.P {
		result[i+1] = &r.P[i]
	}
	return result
}

type dataRowAndKey struct {
	SeriesKey string
	// T0 是此行数据所代表计量点的最早时刻，即对应 P[0] 这一计量点的时刻
	T0 time.Time
	// P 是此行数据的所有计量值
	P []uint64
}

func makeRowWithSeriesKeyForScanning(numPoints int) dataRowAndKey {
	return dataRowAndKey{
		SeriesKey: "",
		T0:        time.Time{},
		P:         make([]uint64, numPoints),
	}
}

func (r *dataRowAndKey) scanArgsForSKT0AndPoints() []any {
	result := make([]any, len(r.P)+2)
	result[0] = &r.SeriesKey
	result[1] = &r.T0
	for i := range r.P {
		result[i+2] = &r.P[i]
	}
	return result
}

func (r *dataRowAndKey) valuesForInsert() []any {
	result := make([]any, len(r.P)+2)
	result[0] = r.SeriesKey
	result[1] = r.T0
	for i, p := range r.P {
		result[i+2] = p
	}
	return result
}

// TODO: make functions accept both dataRow and dataRowAndKey with generics
func (r *dataRowAndKey) intoDataRow() dataRow {
	return dataRow{
		T0: r.T0,
		P:  r.P,
	}
}
