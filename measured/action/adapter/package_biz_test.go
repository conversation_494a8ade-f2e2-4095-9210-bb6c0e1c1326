package adapter

import (
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/measure"

	"github.com/qbox/bo-base/v4/uuid"

	"github.com/qbox/bo-base/v4/base"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service"
)

func TestAdapterPackageItemParam(t *testing.T) {
	expect := &model.PackageItem{
		ItemID:      3,
		ZoneID:      1,
		UnitID:      1000,
		Name:        "test",
		Description: "test description",
		Remark:      "test remark",
		CarryType:   model.CarryTypeMonthly,
		Period:      model.PeriodLifeTime,
		Quantity:    10,
		Price:       2000,
		EffectTime:  base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:    base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbPackageItemParam(expect)
	assert.NoError(t, err)

	actual, err := BuildPackageItemParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterPackageWithItems(t *testing.T) {
	expect := &service.PackageWithItems{
		Code:        "test:package",
		Type:        "FREE",
		Name:        "test package",
		Description: "test description",
		Remark:      "test remark",
		PackageItems: []model.PackageItem{
			{
				ItemID:      3,
				ZoneID:      1,
				UnitID:      1000,
				Name:        "test",
				Description: "test description",
				Remark:      "test remark",
				CarryType:   model.CarryTypeMonthly,
				Period:      model.PeriodLifeTime,
				Quantity:    10,
				Price:       2000,
				EffectTime:  base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
				DeadTime:    base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC)),
			},
			{
				ItemID:      3,
				ZoneID:      1,
				UnitID:      1000,
				Name:        "test",
				Description: "test description",
				Remark:      "test remark",
				CarryType:   model.CarryTypeMonthly,
				Period:      model.PeriodLifeTime,
				Quantity:    10,
				Price:       2000,
				EffectTime:  base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
				DeadTime:    base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC)),
			},
		},
	}

	m, err := BuildPbPackageWithItems(expect)
	assert.NoError(t, err)

	actual, err := BuildPackageWithItems(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterPackageWithItemsResp(t *testing.T) {
	expect := &service.PackageWithItemsResp{
		Package: model.Package{
			ID:          1,
			Code:        "free:package",
			Type:        "FREE",
			Name:        "free package",
			Description: "free package desc",
			Remark:      "test remark",
			CreatedAt:   time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC),
			UpdatedAt:   time.Date(2018, 1, 4, 0, 0, 0, 0, time.UTC),
		},
		PackageItems: []model.PackageItem{
			{
				ID:          1,
				PackageID:   2,
				ItemID:      3,
				ZoneID:      1,
				UnitID:      1000,
				Name:        "test",
				Description: "test description",
				Remark:      "test remark",
				CarryType:   model.CarryTypeMonthly,
				Period:      model.PeriodLifeTime,
				Quantity:    10,
				Price:       2000,
				EffectTime:  base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
				DeadTime:    base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC)),
			},
			{
				ID:          3,
				PackageID:   2,
				ItemID:      3,
				ZoneID:      1,
				UnitID:      1000,
				Name:        "test",
				Description: "test description",
				Remark:      "test remark",
				CarryType:   model.CarryTypeMonthly,
				Period:      model.PeriodLifeTime,
				Quantity:    10,
				Price:       2000,
				EffectTime:  base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
				DeadTime:    base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC)),
			},
		},
	}

	m, err := BuildPbPackageWithItemsResp(expect)
	assert.NoError(t, err)

	actual, err := BuildPackageWithItemsResp(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterPackageItemWithTimePoint(t *testing.T) {
	expect := &service.UIDTimePointQuery{
		UID: 1,
		At:  base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbUIDTimePointQuery(expect)
	assert.NoError(t, err)

	actual, err := BuildUIDTimePointQuery(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterUIDTimeRangeQuery(t *testing.T) {
	expect := &service.UIDTimeRangeQuery{
		UID:       1,
		StartTime: base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		EndTime:   base.NewHNS(time.Date(2018, 1, 5, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbUIDTimeRangeQuery(expect)
	assert.NoError(t, err)

	actual, err := BuildUIDTimeRangeQuery(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestBindPackageParam(t *testing.T) {
	m := &pb.BindPackageParam{
		Uid:        1,
		Code:       "free-package",
		Excode:     uuid.New(),
		EffectTime: timestamppb.New(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:   timestamppb.New(time.Date(2018, 2, 1, 0, 0, 0, 0, time.UTC)),
		Idempotent: false,
	}

	_, err := BuildBindPackageParam(m)
	assert.NoError(t, err)
}

func TestUpgradePackageParam(t *testing.T) {
	m := &pb.UpgradePackageParam{
		Uid:        1,
		SrcCode:    "free-package",
		DstCode:    "free-package-new",
		Excode:     uuid.New(),
		EffectTime: timestamppb.New(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:   timestamppb.New(time.Date(2018, 2, 1, 0, 0, 0, 0, time.UTC)),
	}

	_, err := BuildUpgradePackageParam(m)
	assert.NoError(t, err)
}
