package service

import (
	"context"

	"qiniu.io/pay/measured/model"
)

// 这个文件完全是从 item_data_type_extra_arg_update_helper.go 复制粘贴，查找替换得来的
// fuck Golang

func itemExtraArgsToMap(
	l []model.ItemExtraArg,
) map[string]model.ItemExtraArg {
	result := make(map[string]model.ItemExtraArg, len(l))
	for _, x := range l {
		result[x.Key] = x
	}
	return result
}

type SyncItemExtraArgsParam struct {
	ItemID uint64
	Args   []model.ItemExtraArg
}

// SyncItemExtraArgs 为给定的计费项 ID 同步计费项计量查询附加参数配置
func (s *MeasureBizService) SyncItemExtraArgs(
	ctx context.Context,
	params *SyncItemExtraArgsParam,
) error {
	// 拉出原先这个 Item 的全量 ItemExtraArg 列表
	orig, err := s.ListItemExtraArgsByItemID(ctx, params.ItemID, 0, -1)
	if err != nil {
		return err
	}

	// 因为这边的两个列表按价格表 v3 语义，都是从 map 生成的，所以不会有 key 重复的问题
	// 傻逼的是，为了实现 diff，这边还要再把 list 转回 map。。。
	origMap := itemExtraArgsToMap(orig)
	targetMap := itemExtraArgsToMap(params.Args)

	// 创建原先没有的
	for k, newObj := range targetMap {
		if _, ok := origMap[k]; ok {
			// 原先已经有了，这一波不处理
			continue
		}

		_, err := s.CreateItemExtraArg(ctx, &newObj)
		if err != nil {
			return err
		}
	}

	// 删除应该删除的
	for k, origObj := range origMap {
		if _, ok := targetMap[k]; ok {
			// 这个 key 的记录还在
			continue
		}

		_, err := s.DeleteItemExtraArgByID(ctx, origObj.ID)
		if err != nil {
			return err
		}
	}

	// 更新变化了的
	for k, newObj := range targetMap {
		origObj, ok := origMap[k]
		if !ok {
			// 一上来已经处理过了
			continue
		}

		err := s.syncOneItemExtraArg(ctx, &origObj, &newObj)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *MeasureBizService) syncOneItemExtraArg(
	ctx context.Context,
	orig *model.ItemExtraArg,
	target *model.ItemExtraArg,
) error {
	// 因为调用这个 helper 的外层逻辑的更新姿势不会动到 key，所以只检查 value
	if orig.Value == target.Value {
		return nil
	}

	newObj := *orig
	newObj.Value = target.Value
	err := s.dao.ItemExtraArg.Save(&newObj)
	if err != nil {
		return err
	}

	return nil
}
