package service

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/go-openapi/strfmt"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/bo-base/v4/test"
	"github.com/qbox/pay-sdk/base/account"
	notificationClient "github.com/qbox/pay-sdk/notification-v2/client"
	walletClient "github.com/qbox/pay-sdk/v3/walletEx/client"

	"qiniu.io/pay/walletd/config"
	"qiniu.io/pay/walletd/model"
)

type balanceInsufficiencySrvSandbox struct {
	testWrap                *test.Wrap
	balanceInsufficiencySrv *BalanceInsufficiencyService
	mockServer              *httptest.Server
}

func newBalanceInsufficiencySrvSandbox(t *testing.T) *balanceInsufficiencySrvSandbox {
	mux := http.NewServeMux()
	mux.Handle("/api/message/send", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		io.WriteString(w, `"5d7f5b734159a00008000039"`)
	}))

	mux.Handle("/v3/wallet/overview", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		io.WriteString(w, `{"cash":100000,"all_undeduct_bills_money":9999}`)
	}))

	mockServer := httptest.NewServer(mux)
	t.Cleanup(mockServer.Close)

	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in credit service return error")
	}

	accServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		io.WriteString(w, `{"code":200,"message":""}`)
	}))
	conf := &config.WalletdConfig{
		Acc: account.AccConfig{
			// TODO use mock acc
			Host:     accServer.URL, // oauth2/token will be appended in roundTripper
			UserName: "root",
			Password: "root",
			ClientID: "",
		},
		Services: config.ServiceConfig{
			NotificationV2: mockServer.Listener.Addr().String(),
			Wallet:         mockServer.Listener.Addr().String(),
		},
	}
	bDao := model.NewBalanceInsufficiencyPredictionDao(testWrap.BaseDao())

	accTransport, err := account.NewTransport(&conf.Acc)
	if err != nil {
		logrus.WithError(err).Fatalln("init accTransport error in test")
	}
	clientTransport, err := rpc.NewSwaggerTransport("http://"+conf.Services.NotificationV2, http.DefaultClient)
	if err != nil {
		logrus.WithError(err).Fatalln("init notificationClient error in test")
	}
	noticeClient := notificationClient.New(clientTransport, strfmt.Default)
	walletV3Client := walletClient.NewHTTPClientWithConfig(
		strfmt.Default,
		&walletClient.TransportConfig{
			Host:    conf.Services.Wallet,
			Schemes: []string{"http"},
		},
	)

	balanceInsufficiencySrv, err := NewBalanceInsufficiencyService(
		conf,
		bDao,
		noticeClient,
		walletV3Client,
		accTransport,
	)
	if err != nil {
		t.Fatal(err)
	}

	return &balanceInsufficiencySrvSandbox{
		testWrap:                testWrap,
		balanceInsufficiencySrv: balanceInsufficiencySrv,
		mockServer:              mockServer,
	}
}

func TestBalanceInsufficiencyService_NotifyBalanceInsufficiency(t *testing.T) {
	test.RunWithUTCAndCST(t, testBalanceInsufficiencyService_NotifyBalanceInsufficiency)
}

func testBalanceInsufficiencyService_NotifyBalanceInsufficiency(t *testing.T, loc *time.Location) {
	sandbox := newBalanceInsufficiencySrvSandbox(t)
	now := time.Date(2019, 9, 1, 0, 0, 0, 0, loc)
	predictions := []model.BalanceInsufficiencyPrediction{
		{
			UID:                         1,
			LeastUnaffordableDays:       1,
			IsMergeAccount:              false,
			ParentID:                    11,
			ParentLeastUnaffordableDays: 1,
			DataDate:                    now,
		}, {
			UID:                         2,
			LeastUnaffordableDays:       7,
			IsMergeAccount:              false,
			ParentID:                    11,
			ParentLeastUnaffordableDays: 3,
			DataDate:                    now,
		},
		{
			UID:                         3,
			LeastUnaffordableDays:       8,
			IsMergeAccount:              false,
			ParentID:                    11,
			ParentLeastUnaffordableDays: 3,
			DataDate:                    now,
		},
		{
			UID:                         2,
			LeastUnaffordableDays:       2,
			IsMergeAccount:              false,
			ParentID:                    11,
			ParentLeastUnaffordableDays: 3,
			DataDate:                    now.AddDate(0, 0, -1),
		},
	}

	for _, prediction := range predictions {
		err := sandbox.balanceInsufficiencySrv.balanceInsufficiencyDao.Save(&prediction)
		if err != nil {
			t.Fatal(err)
		}
	}

	ctx := tz.WithRefLocation(context.Background(), loc)
	total, failureCnt, err := sandbox.balanceInsufficiencySrv.NotifyBalanceInsufficiency(ctx, now)
	assert.Equal(t, 2, total)
	assert.Equal(t, 0, failureCnt)
	assert.NoError(t, err)
}
