package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
	"qiniu.io/pay/walletd/service"
)

// BuildPbCouponRebate converts model.CouponRebate to the protobuf type.
func BuildPbCouponRebate(rebate *model.CouponRebate) (*pb.CouponRebate, error) {
	return &pb.CouponRebate{
		Id:           rebate.ID,
		Code:         rebate.Code,
		Type:         rebate.Type,
		ScopeId:      rebate.ScopeID,
		Name:         rebate.Name,
		Description:  rebate.Description,
		Remark:       rebate.Remark,
		Threshold:    rebate.Threshold,
		Money:        rebate.Money,
		EffectTime:   timestamppb.New(rebate.EffectTime.TimeIn(time.UTC)),
		DeadTime:     timestamppb.New(rebate.DeadTime.TimeIn(time.UTC)),
		CreatedAt:    timestamppb.New(rebate.CreatedAt),
		UpdatedAt:    timestamppb.New(rebate.UpdatedAt),
		CurrencyType: rebate.CurrencyType.String(),
	}, nil
}

// BuildPbCouponRebateWithIsBound converts model.CouponRebate to the protobuf type.
func BuildPbCouponRebateWithIsBound(rebateWithIsBound *service.CouponRebateWithIsBound) (*pb.CouponRebate, error) {
	pbCouponRebate, err := BuildPbCouponRebate(&rebateWithIsBound.CouponRebate)
	if err != nil {
		return nil, errors.Trace(err)
	}

	pbCouponRebate.IsBound = rebateWithIsBound.IsBound

	return pbCouponRebate, nil
}

// BuildCouponRebate converts from pb.CouponRebate to the model layer type.
func BuildCouponRebate(rebate *pb.CouponRebate) (*model.CouponRebate, error) {
	err := rebate.GetEffectTime().CheckValid()
	if err != nil {
		return nil, err
	}
	effectTime := rebate.GetEffectTime().AsTime()

	err = rebate.GetDeadTime().CheckValid()
	if err != nil {
		return nil, err
	}
	deadTime := rebate.GetDeadTime().AsTime()

	var createdAt, updatedAt time.Time
	if rebate.GetCreatedAt() != nil {
		err = rebate.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = rebate.GetCreatedAt().AsTime()
	}

	if rebate.GetUpdatedAt() != nil {
		err = rebate.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = rebate.GetUpdatedAt().AsTime()
	}

	return &model.CouponRebate{
		ID:           rebate.GetId(),
		Code:         rebate.GetCode(),
		Type:         rebate.GetType(),
		ScopeID:      rebate.GetScopeId(),
		Name:         rebate.GetName(),
		Description:  rebate.GetDescription(),
		Remark:       rebate.GetRemark(),
		Threshold:    rebate.GetThreshold(),
		Money:        rebate.GetMoney(),
		EffectTime:   base.NewHNS(effectTime),
		DeadTime:     base.NewHNS(deadTime),
		CreatedAt:    createdAt,
		UpdatedAt:    updatedAt,
		CurrencyType: base.CurrencyType(rebate.GetCurrencyType()),
	}, nil
}
