package doraItemsQuerier

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMapDoraCmdKeysToItems(t *testing.T) {
	dictFixture := doraCmdsItemsDict{
		"foo": {
			{ItemCode: "itemA", ProductCode: "mps"},
		},
		"bar": {
			{ItemCode: "itemA", ProductCode: "mps"},
			{ItemCode: "itemB", ProductCode: "market"},
		},
		"baz": {
			{ItemCode: "itemB", ProductCode: "market"},
		},
		"quux": {
			{ItemCode: "itemC", ProductCode: "atlab"},
		},
		"frob": {
			{ItemCode: "itemD", ProductCode: "mps"},
		},
	}

	testcases := []struct {
		desc        string
		dict        doraCmdsItemsDict
		productCode string
		keys        []string
		expected    []string
	}{
		{
			desc:        "trivial case - empty",
			dict:        dictFixture,
			productCode: "mps",
			keys:        nil,
			expected:    nil,
		},
		{
			desc:        "trivial case - one",
			dict:        dictFixture,
			productCode: "mps",
			keys:        []string{"foo"},
			expected:    []string{"itemA"},
		},
		{
			desc:        "trivial case - key 不匹配",
			dict:        dictFixture,
			productCode: "mps",
			keys:        []string{"f00"},
			expected:    nil,
		},
		{
			desc:        "normal case 1 - mps",
			dict:        dictFixture,
			productCode: "mps",
			keys:        []string{"frob", "foo", "bar", "quux"},
			expected:    []string{"itemD", "itemA"},
		},
		{
			desc:        "normal case 1 - market",
			dict:        dictFixture,
			productCode: "market",
			keys:        []string{"frob", "foo", "bar", "quux"},
			expected:    []string{"itemB"},
		},
		{
			desc:        "normal case 1 - atlab",
			dict:        dictFixture,
			productCode: "atlab",
			keys:        []string{"frob", "foo", "bar", "quux"},
			expected:    []string{"itemC"},
		},
		{
			desc:        "normal case 2 - 混入不匹配的 key",
			dict:        dictFixture,
			productCode: "atlab",
			keys:        []string{"quux", "xxx"},
			expected:    []string{"itemC"},
		},
	}

	for _, tc := range testcases {
		actual := mapDoraCmdKeysToItems(tc.dict, tc.productCode, tc.keys)
		assert.Equal(t, tc.expected, actual, tc.desc)
	}
}
