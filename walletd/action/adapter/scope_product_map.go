package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/walletd/model"
)

// BuildPbScopeProductMap converts model.ScopeProductMap to the protobuf type.
func BuildPbScopeProductMap(_map *model.ScopeProductMap) (*pb.ScopeProductMap, error) {

	return &pb.ScopeProductMap{
		Id:         _map.ID,
		ScopeId:    _map.ScopeID,
		ProductId:  _map.ProductID,
		IsExcluded: _map.IsExcluded,
		CreatedAt:  timestamppb.New(_map.CreatedAt),
		UpdatedAt:  timestamppb.New(_map.UpdatedAt),
	}, nil
}

// BuildScopeProductMap converts from pb.ScopeProductMap to the model layer type.
func BuildScopeProductMap(_map *pb.ScopeProductMap) (*model.ScopeProductMap, error) {
	var createdAt, updatedAt time.Time
	if _map.GetCreatedAt() != nil {
		err := _map.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = _map.GetCreatedAt().AsTime()
	}

	if _map.GetUpdatedAt() != nil {
		err := _map.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = _map.GetUpdatedAt().AsTime()
	}

	return &model.ScopeProductMap{
		ID:          _map.GetId(),
		ScopeID:     _map.GetScopeId(),
		ProductID:   _map.GetProductId(),
		ProductCode: _map.GetProductCode(),
		IsExcluded:  _map.GetIsExcluded(),
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
	}, nil
}

// BuildPbScopeProductMaps converts from []model.ScopeProductMap to the protobuf type.
func BuildPbScopeProductMaps(x []model.ScopeProductMap) ([]*pb.ScopeProductMap, error) {
	y := make([]*pb.ScopeProductMap, len(x))
	for i, obj := range x {
		result, err := BuildPbScopeProductMap(&obj)
		if err != nil {
			return nil, err
		}
		y[i] = result
	}
	return y, nil
}

// BuildScopeProductMaps converts from []*pb.ScopeProductMap to the model layer type.
func BuildScopeProductMaps(x []*pb.ScopeProductMap) ([]model.ScopeProductMap, error) {
	y := make([]model.ScopeProductMap, len(x))
	for i, obj := range x {
		result, err := BuildScopeProductMap(obj)
		if err != nil {
			return nil, err
		}
		y[i] = *result
	}
	return y, nil
}

// BuildPbUserProductVoucherQuotaResponse converts ProductIDVoucherQuotaList to *pb.UserProductVoucherQuotaResponse
func BuildPbUserProductVoucherQuotaResponse(list model.ProductIDVoucherQuotaList) *pb.UserProductVoucherQuotaResponse {
	var productVoucherQuotaList []*pb.ProductIDQuotaMap
	for _, productIDQuota := range list {
		currencyType := base.CurrencyTypeCNY
		if productIDQuota.AssetType == enums.AssetTypeUSDVoucher {
			currencyType = base.CurrencyTypeUSD
		}
		quotaMap := pb.ProductIDQuotaMap{
			ProductId:    productIDQuota.ProductID,
			Quota:        productIDQuota.Quota,
			CurrencyType: currencyType.String(),
		}
		productVoucherQuotaList = append(productVoucherQuotaList, &quotaMap)
	}
	return &pb.UserProductVoucherQuotaResponse{
		ProductQuotaList: productVoucherQuotaList,
	}
}
