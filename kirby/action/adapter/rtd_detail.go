package adapter

import (
	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/qrn"
)

func BuildRTDDetail(in *pb.ResourceTypeDefinitionDetail) *adminModel.ResourceTypeDefinitionDetail {
	return &adminModel.ResourceTypeDefinitionDetail{
		Code: in.Code,
		QRNMatches: qrn.MatchExprArray(
			lo.Map(in.QrnMatches, func(x string, _ int) qrn.MatchExpr { return qrn.MatchExpr(x) }),
		),
		EffectTime: in.EffectTime.AsTime(),
		MMDs:       buildMMDDetails(in.Mmds),
		SPDs:       buildSPDDetails(in.Spds),
	}
}

func BuildPbRTDDetail(in *adminModel.ResourceTypeDefinitionDetail) *pb.ResourceTypeDefinitionDetail {
	return &pb.ResourceTypeDefinitionDetail{
		Code:       in.Code,
		QrnMatches: lo.Map(in.QRNMatches, func(x qrn.MatchExpr, _ int) string { return string(x) }),
		EffectTime: timestamppb.New(in.EffectTime),
		Mmds:       buildPbMMDDetails(in.MMDs),
		Spds:       buildPbSPDDetails(in.SPDs),
	}
}
