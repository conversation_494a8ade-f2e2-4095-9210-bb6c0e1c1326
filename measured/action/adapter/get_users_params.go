package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
)

// BuildPbGetUsersParamsV1 converts from model.GetUsersParams to the protobuf type.
func BuildPbGetUsersParamsV1(x *model.GetUsersParams) (*pb.GetUsersParamsV1, error) {
	return &pb.GetUsersParamsV1{
		ProductCode: x.ProductCode,
		Begin:       timestamppb.New(x.Begin),
		End:         timestamppb.New(x.End),
		ZoneCode:    x.ZoneCode,
		Debug:       x.Debug,
	}, nil
}

// BuildModelGetUsersParamsV1 converts from pb.GetUsersParamsV1 to the model layer type.
func BuildModelGetUsersParamsV1(x *pb.GetUsersParamsV1) (*model.GetUsersParams, error) {
	var begin time.Time
	var end time.Time
	if x.Begin != nil {
		err := x.Begin.CheckValid()
		if err != nil {
			return nil, err
		}
		begin = x.Begin.AsTime()
	}

	if x.End != nil {
		err := x.End.CheckValid()
		if err != nil {
			return nil, err
		}
		end = x.End.AsTime()
	}

	return &model.GetUsersParams{
		ProductCode: x.ProductCode,
		Begin:       begin,
		End:         end,
		ZoneCode:    x.ZoneCode,
		Debug:       x.Debug,
	}, nil
}
