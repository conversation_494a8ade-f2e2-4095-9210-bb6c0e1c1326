package stat

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
)

type cacheable interface {
	ToCacheKey() string
}

// makeCacheableQuery 进行一个可缓存的查询
//
// 如果开了缓存，优先查缓存，没有命中就算出来并存入缓存
//
// 由于 go1.18 泛型的限制，此函数无法挂在 measuredStatClient 上，只能单独写
func makeCacheableQuery[T cacheable, U any](
	uncachedFn func(context.Context, T) (*U, error),
	cacheDuration time.Duration,
	that *measuredStatClient,
	ctx context.Context,
	params T,
) (*U, error) {
	if that.cacheStore == nil {
		// cache is disabled
		return uncachedFn(ctx, params)
	}

	cacheKey := params.ToCacheKey()

	var cachedResult U
	err := that.cacheStore.Get(cacheKey, &cachedResult)
	if err != nil && err != dao.ErrCacheMiss {
		// 真错误
		return nil, errors.Trace(err)
	}
	if err == nil {
		// cache hit
		return &cachedResult, nil
	}

	// cache miss
	result, err := uncachedFn(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// store cache
	err = that.cacheStore.Set(cacheKey, result, cacheDuration)
	if err != nil {
		logger := logging.GetLogger(ctx)
		// cache set 失败不算错
		logger.WithError(err).WithField("cache_key", cacheKey).Warn("store cache failed")
	}

	return result, nil
}
