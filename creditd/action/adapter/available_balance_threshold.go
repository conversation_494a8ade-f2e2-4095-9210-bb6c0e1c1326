package adapter

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/credit"

	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

// BuildModelThreshold converts from protobuf type to model.Credit .
func BuildModelThreshold(threshold *pb.CreateAvailableBalanceThresholdReq) *model.AvailableBalanceThreshold {
	return &model.AvailableBalanceThreshold{
		UID:       threshold.GetUid(),
		Threshold: threshold.GetAvailableBalanceThreshold(),
		Status:    enums.ThresholdStatus(threshold.GetStatus()),
		Remark:    threshold.GetRemark(),
	}
}

// BuildPbThreshold converts from model.AvailableBalanceThreshold to the protobuf type.
func BuildPbThreshold(threshold *model.AvailableBalanceThreshold) (*pb.AvailableBalanceThresholdResp, error) {
	return &pb.AvailableBalanceThresholdResp{
		Uid:                       threshold.UID,
		AvailableBalanceThreshold: threshold.Threshold,
		Status:                    pb.ThresholdStatus(threshold.Status),
		Remark:                    threshold.Remark,
		CreatedAt:                 timestamppb.New(threshold.CreatedAt),
		UpdatedAt:                 timestamppb.New(threshold.UpdatedAt),
	}, nil
}

// BuildPbListThresholdsByStatusResp converts from []model.AvailableBalanceThreshold to the protobuf type.
func BuildPbListThresholdsByStatusResp(thresholds []model.AvailableBalanceThreshold, count uint64) (*pb.ListThresholdsByStatusResp, error) {

	pbThresholdResp := make([]*pb.AvailableBalanceThresholdResp, len(thresholds))
	for i, threshold := range thresholds {
		pbThresholdResp[i] = &pb.AvailableBalanceThresholdResp{
			Uid:                       threshold.UID,
			AvailableBalanceThreshold: threshold.Threshold,
			Status:                    pb.ThresholdStatus(threshold.Status),
			Remark:                    threshold.Remark,
			CreatedAt:                 timestamppb.New(threshold.CreatedAt),
			UpdatedAt:                 timestamppb.New(threshold.UpdatedAt),
		}
	}

	return &pb.ListThresholdsByStatusResp{
		Thresholds: pbThresholdResp,
		Count:      count,
	}, nil
}
