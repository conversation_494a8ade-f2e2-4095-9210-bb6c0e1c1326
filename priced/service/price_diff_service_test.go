package service_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
	"qiniu.io/pay/priced/service"
)

func TestGroupPriceItemWithItemIDAndZoneID(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	type TestCase struct {
		title              string
		priceItems         []model.PriceItem
		expectPriceItemMap map[service.ItemZoneKey][]model.PriceItem
	}

	testCases := []TestCase{
		{
			title:              "nil priceItems",
			priceItems:         nil,
			expectPriceItemMap: map[service.ItemZoneKey][]model.PriceItem{},
		},
		{
			title:              "empty priceItems",
			priceItems:         []model.PriceItem{},
			expectPriceItemMap: map[service.ItemZoneKey][]model.PriceItem{},
		},
		{
			title: "normal case",
			priceItems: []model.PriceItem{
				{
					ID:     1,
					ItemID: 1,
					ZoneID: 1,
				},
				{
					ID:     2,
					ItemID: 2,
					ZoneID: 2,
				},
				{
					ID:     3,
					ItemID: 1,
					ZoneID: 2,
				},
				{
					ID:     4,
					ItemID: 2,
					ZoneID: 2,
				},
			},
			expectPriceItemMap: map[service.ItemZoneKey][]model.PriceItem{
				{
					ItemID: 1,
					ZoneID: 1,
				}: {
					{
						ID:     1,
						ItemID: 1,
						ZoneID: 1,
					},
				},
				{
					ItemID: 2,
					ZoneID: 2,
				}: {
					{
						ID:     2,
						ItemID: 2,
						ZoneID: 2,
					},
					{
						ID:     4,
						ItemID: 2,
						ZoneID: 2,
					},
				},
				{
					ItemID: 1,
					ZoneID: 2,
				}: {
					{
						ID:     3,
						ItemID: 1,
						ZoneID: 2,
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		priceItemMap := priceService.GroupPriceItemWithItemIDAndZoneID(testCase.priceItems)
		for key, priceItems := range priceItemMap {
			expectPriceItems := testCase.expectPriceItemMap[key]
			assert.Equal(t, expectPriceItems, priceItems, testCase.title)
		}
	}
}
