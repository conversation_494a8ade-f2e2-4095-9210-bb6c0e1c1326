package service

import (
	"context"
	"encoding/json"
	"time"

	"qiniu.io/pay/coupon/model"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/pay-sdk/middleware/logging"
	qbpmRuntimes "github.com/qbox/pay-sdk/qbpm/client/runtimes"
	qbpmModels "github.com/qbox/pay-sdk/qbpm/models"
)

// 创建一个抵用券发放批次的参数
type LaunchCreateCouponBatchParams struct {
	// 抵用券名称，将对用户展示
	Name string `json:"name"`
	// 抵用券描述（使用范围等），将对用户展示
	Description string `json:"description"`
	// 内部描述，不对用户展示
	Remark string `json:"remark"`
	// 事由
	Reason model.CouponReason `json:"reason"`
	// 事由描述
	ReasonDesc string `json:"reason_desc"`
	// 发放形式
	BindMethod model.CouponBindMethod `json:"bind_method"`
	// 发放时间起始（含）
	StartTime time.Time `json:"start_time"`
	// 发放时间截止（不含）
	EndTime time.Time `json:"end_time"`
	// 总发放张数
	BatchSize uint64 `json:"batch_size"`
	// 单用户领取上限，张
	MaxActivationTimesPerUser uint64 `json:"max_activation_times_per_user"`
	// 生效时间种类（固定天数，固定时间段，etc）
	TimePeriodType model.CouponTimePeriodType `json:"time_period_type"`
	// 生效天数，仅生效时间种类为【固定天数】时有意义
	EffectDays uint64 `json:"effect_days"`
	// 生效时间起始（含），仅生效时间种类为【固定时间段】时有意义
	CouponEffectTime time.Time `json:"coupon_effect_time"`
	// 生效时间截止（不含），仅生效时间种类为【固定时间段】时有意义
	CouponDeadTime time.Time `json:"coupon_dead_time"`
	// 使用金额门槛，万分之一元
	ThresholdMoney int64 `json:"threshold_money"`
	// 抵用券面额，万分之一元
	CouponMoney int64 `json:"coupon_money"`
	// 是否可多次使用
	IsMultipleUse bool `json:"is_multiple_use"`
	// 用户类型
	UserScope model.CouponUserType `json:"user_scope"`
	// 适用付费类型
	PayModeScope model.CouponPayMode `json:"pay_mode_scope"`
	// 欠费是否可使用
	ArrearCanUse bool `json:"arrear_can_use"`
	// 是否为通用不限制适用范围情况
	IsUnlimitedScope bool `json:"is_unlimited_scope"`
	// 适用范围（scope）
	Scope *CouponScope `json:"scope"`
	// 适用范围描述（前端生成描述信息），目前将该字段信息保存到 coupon_batch 表中，后期按需求可以重构到 scope 中以期复用。
	CouponScopeDesc string `json:"coupon_scope_desc"`
	// 申请方 （产品线）
	Applicant model.ApplicantType `json:"applicant"`
}

type LaunchCouponApplicationParams struct {
	UID                     uint64                         `json:"uid"`
	CreateCouponBatchParams *LaunchCreateCouponBatchParams `json:"create_coupon_batch_params"`
	ApplicantEmail          string                         `json:"applicant_email"`
}

// LaunchCouponApplication 发起申请 coupon 审批
func (s *CouponService) LaunchCouponApplication(
	ctx context.Context,
	req *LaunchCouponApplicationParams,
) (excode string, err error) {
	l := logging.GetLogger(ctx)
	loc := tz.MustLocationFromCtx(ctx)

	req.CreateCouponBatchParams.StartTime = req.CreateCouponBatchParams.StartTime.In(loc)
	req.CreateCouponBatchParams.EndTime = req.CreateCouponBatchParams.EndTime.In(loc)
	req.CreateCouponBatchParams.CouponEffectTime = req.CreateCouponBatchParams.CouponEffectTime.In(loc)
	req.CreateCouponBatchParams.CouponDeadTime = req.CreateCouponBatchParams.CouponDeadTime.In(loc)

	applicationJson, err := json.Marshal(req)
	if err != nil {
		err = errors.Trace(err)
		return
	}

	generatedExcode := genCouponApplicationCode()
	data := &qbpmModels.StartProcessInput{
		// generate unique code
		Excode: generatedExcode,
		// 根据事由分类，填对应的模板
		Key: "apply-coupon",
		// 操作人
		Operator: req.ApplicantEmail,
		Params:   string(applicationJson),
		UID:      int64(req.UID),
	}
	l.Infoln("LaunchCouponApplication data", data)

	// invoke qbpm service
	params := qbpmRuntimes.
		NewStartProcessParams().
		WithData(data)
	ret, err := s.qbpmClient.Runtimes.StartProcess(params)
	if err != nil {
		err = errors.Trace(err)
		return
	}

	l.Infof("LaunchCouponApplication invoke StartProcess return value:%+v", ret)
	return ret.GetPayload().Excode, nil
}
