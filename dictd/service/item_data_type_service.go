package service

import (
	"context"

	"qiniu.io/pay/dictd/model"
)

// GetItemDataTypeByID 根据ID获取计费项数据类型
func (s *DictBizService) GetItemDataTypeByID(
	ctx context.Context,
	id uint64,
) (*model.ItemDataType, error) {
	return s.dictDao.ItemDataType.GetByID(id, s.cacheExpires)
}

// GetItemDataTypeByItemAndType 根据 Item 和 Type 获取计费项数据类型
func (s *DictBizService) GetItemDataTypeByItemAndType(
	ctx context.Context,
	item string,
	typ string,
) (*model.ItemDataType, error) {
	return s.dictDao.ItemDataType.GetByItemAndType(item, typ, s.cacheExpires)
}

// ListItemDataTypesByItemID 根据计费项ID获取计费项数据类型列表
func (s *DictBizService) ListItemDataTypesByItemID(
	ctx context.Context,
	itemID uint64,
	offset int,
	limit int,
) ([]model.ItemDataType, error) {
	return s.dictDao.ItemDataType.ListByItemID(itemID, offset, limit, s.cacheExpires)
}

// ListItemDataTypesByItemCode 根据计费项 Code 获取计费项数据类型列表
func (s *DictBizService) ListItemDataTypesByItemCode(
	ctx context.Context,
	item string,
	offset int,
	limit int,
) ([]model.ItemDataType, error) {
	return s.dictDao.ItemDataType.ListByItemCode(item, offset, limit, s.cacheExpires)
}

// CountItemDataTypesByItemID 根据计费项ID获取计费项数据类型数量
func (s *DictBizService) CountItemDataTypesByItemID(
	ctx context.Context,
	itemID uint64,
) (uint64, error) {
	return s.dictDao.ItemDataType.CountByItemID(itemID, s.cacheExpires)
}

// CountItemDataTypesByItemCode 根据计费项 Code 获取计费项数据类型数量
func (s *DictBizService) CountItemDataTypesByItemCode(
	ctx context.Context,
	item string,
) (uint64, error) {
	return s.dictDao.ItemDataType.CountByItemCode(item, s.cacheExpires)
}

// CreateItemDataType 创建计费项数据类型
func (s *DictBizService) CreateItemDataType(
	ctx context.Context,
	dataType *model.ItemDataType,
) (*model.ItemDataType, error) {
	dataType.ID = 0
	err := s.dictDao.ItemDataType.Save(dataType, s.cacheExpires)
	return dataType, err
}

// UpdateItemDataTypeByID 更新计费项数据类型
func (s *DictBizService) UpdateItemDataTypeByID(
	ctx context.Context,
	id uint64,
	dataType *model.ItemDataType,
) (*model.ItemDataType, error) {
	dataType.ID = id
	err := s.dictDao.ItemDataType.Save(dataType, s.cacheExpires)
	return dataType, err
}

// DeleteItemDataTypeByID 根据ID删除计费项数据类型
func (s *DictBizService) DeleteItemDataTypeByID(
	ctx context.Context,
	id uint64,
) (*model.ItemDataType, error) {
	dataType, err := s.GetItemDataTypeByID(ctx, id)
	if err != nil {
		return nil, err
	}
	err = s.dictDao.ItemDataType.Delete(dataType)
	return dataType, err
}
