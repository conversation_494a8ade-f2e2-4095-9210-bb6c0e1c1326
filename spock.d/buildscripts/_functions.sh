#!/bin/bash

# TODO: 这里散落的 $WORKSPACE 最好想个办法全部都重构掉，全部用参数传递

# 为 bo-pay 库设置编译环境
#
# NOTE: 有环境副作用
do_bo_pay_envsetup() {
    export GO_VERSION=go1.15

    export GOPATH="$WORKSPACE"
    export GOPRIVATE=github.com/qbox
    # dogfooding is awesome: https://goproxy.cn
    # 这样就不用科学上网了
    export GOPROXY=https://goproxy.cn,direct
    # 把增量编译、测试缓存放进工作区
    export GOCACHE="$WORKSPACE/go-build-cache"

    # workaround Spock only configuring SSH key but not netrc
    git config --global url."**************:qbox/".insteadOf "https://github.com/qbox/"
}

# 编译 bo-pay 库服务
#
# go mod 自动解决依赖
do_bo_pay_install() {
    # dist.sh 自己会设置合适的工作目录，不用 pushd/popd
    "${WORKSPACE}/src/qiniu.io/pay/ci/build/dist.sh" "$DIST_DIR/$PKG_FILE" "$ARTIFACT_NAME"
    exitcode=$?

    return $exitcode
}

# bo-pay 库单个服务构建镜像
#
# Usage: do_bo_pay_image service artifact_name image
#
# 效果: 去 spock.d/dockerfiles/$service 构建该服务的 Spock 部署镜像，并 push
do_bo_pay_image() {
    local service="$1"
    local artifact_name="$2"
    local image="$3"
    local sufy_image="$4"

    local build_path="${WORKSPACE}/src/qiniu.io/pay/spock.d/dockerfiles/${service}"
    pushd "${build_path}"

    # TODO: 重构这里的 service 名字逻辑，不要重复
    cp "${GOPATH}/bin/${artifact_name}" ./resources && \
		docker buildx build --rm --builder=kube -t "${image}" . --push && \
		docker buildx build --rm --builder=kube -t "${sufy_image}" . --push
    exitcode=$?

    popd

    return $exitcode
}
