package dataplaneModel

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// SP 序列属性
type SP map[string]string

// DB adapters for SP

var _ driver.Valuer = (*SP)(nil)

func (x *SP) Value() (driver.Value, error) {
	payload, err := json.Marshal(x)
	if err != nil {
		return nil, err
	}
	return string(payload), nil
}

var _ sql.Scanner = (*SP)(nil)

func (x *SP) Scan(src interface{}) error {
	ns := sql.NullString{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	return json.Unmarshal([]byte(ns.String), x)
}
