package model

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// UserBankVirtualAccount model definition
type UserBankVirtualAccount struct {
	ID  uint64 `gorm:"primary_key"`
	UID uint64
	// 银行虚拟账号
	BankVirtualAccount string `gorm:"type:varchar(128)"`

	IsDeleted bool      `sql:"type:tinyint"`
	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// UserBankVirtualAccountDao is data access object of UserBankVirtualAccount model
type UserBankVirtualAccountDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *UserBankVirtualAccountDao) GetCachePrefix() string {
	return "wallet:user_bank_virtual_account:"
}

// GetCacheRefs get cache refs
func (d *UserBankVirtualAccountDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewUserBankVirtualAccountDao is constructor of UserBankVirtualAccountDao
func NewUserBankVirtualAccountDao(base *dao.BaseDao) *UserBankVirtualAccountDao {
	cachePrefix := (*UserBankVirtualAccountDao)(nil).GetCachePrefix()
	return &UserBankVirtualAccountDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// Save 插入新 UserBankVirtualAccount 条目
func (d *UserBankVirtualAccountDao) Save(model *UserBankVirtualAccount, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// GetByID 通过 id 查询
func (d *UserBankVirtualAccountDao) GetByID(id uint64, expires ...time.Duration) (*UserBankVirtualAccount, error) {
	model := &UserBankVirtualAccount{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where("id=? and is_deleted=false", id).First(value).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// GetByUID 通过 uid 查询
func (d *UserBankVirtualAccountDao) GetByUID(uid uint64) (*UserBankVirtualAccount, error) {
	userBankVirtualAccount := &UserBankVirtualAccount{UID: uid}
	err := d.base.
		Model(&UserBankVirtualAccount{}).
		Where("uid = ? and is_deleted=false", uid).
		First(userBankVirtualAccount).
		Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return nil, err
	}
	return userBankVirtualAccount, nil
}

// GetByBankVirtAccount 通过 bankVirtAccount 查询
func (d *UserBankVirtualAccountDao) GetByBankVirtAccount(bankVirtAccount string) (*UserBankVirtualAccount, error) {
	userBankVirtualAccount := &UserBankVirtualAccount{}
	err := d.base.
		Model(&UserBankVirtualAccount{}).
		Where("bank_virtual_account = ? and is_deleted=false", bankVirtAccount).
		First(userBankVirtualAccount).
		Error
	if err != nil {
		return nil, errors.Trace(err)
	}
	return userBankVirtualAccount, nil
}

// GetByBankVirtAccounts 通过 bankVirtAccount 数组查询
func (d *UserBankVirtualAccountDao) GetByBankVirtAccounts(bankVirtAccounts []string) (userBankVirtualAccounts []*UserBankVirtualAccount, err error) {
	err = d.base.
		Model(&UserBankVirtualAccount{}).
		Where("bank_virtual_account in (?) and is_deleted=false", bankVirtAccounts).
		Find(&userBankVirtualAccounts).
		Error
	if err != nil {
		return nil, errors.Trace(err)
	}
	return userBankVirtualAccounts, nil
}
