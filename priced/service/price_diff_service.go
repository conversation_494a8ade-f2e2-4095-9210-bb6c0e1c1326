package service

import (
	"context"
	"fmt"
	"math"
	"sort"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
	"qiniu.io/pay/priced/model"

	"github.com/qbox/pay-sdk/dict"
)

// DiffCurrentPriceWithCreatedTime 查询报价单提交前的价格与当前报价单的差异
func (s *PriceBizService) DiffCurrentPriceWithCreatedTime(
	ctx context.Context,
	uid uint64,
	priceID uint64,
	scope *PriceScope,
) (*PriceDiffResp, error) {
	priceInfluence, err := s.GetPriceInfluenceWithCurrentPrice(
		ctx, uid, priceID, scope,
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return s.ConvertPriceInfluenceToPriceDiffResp(ctx, priceInfluence)
}

// ConvertPriceInfluenceToPriceDiffResp 将 PriceInfluence 转换为 PriceDiffResp
//
// 步骤：
// 1. 根据报价单获取要比对价格的 (item, zone) 列表
// 2. 分别计算各 (item, zone) 在报价单提交前的价格与当前报价单的差异
// 2.a. 将两个阶梯价格切分成相同的阶梯
// 2.b. 分别比较相同阶梯时的价格差异
func (s *PriceBizService) ConvertPriceInfluenceToPriceDiffResp(
	ctx context.Context,
	priceInfluence *PriceInfluence,
) (*PriceDiffResp, error) {
	// 以 (item_id, zone_id) 为 key 聚合 PriceItems
	logger := logging.GetLogger(ctx)

	beforePriceItemMap, afterPriceItemMap := s.GroupPriceItemFromPriceInfluence(priceInfluence)

	beforeDiffItemPriceMap := make(map[uint64]DiffPriceItemList)
	afterDiffItemPriceMap := make(map[uint64]DiffPriceItemList)

	// 比较 item_id、zone_id 相同的两个 PriceItem
	for itemZoneKey, beforePriceItems := range beforePriceItemMap {
		afterPriceItems := afterPriceItemMap[itemZoneKey]

		// NOTE: 这里比对的是当前价格与指定报价单的差异，因此每个 (item_id, zone_id)
		// 只能对应一个 PriceItem
		if len(beforePriceItems) != 1 || len(afterPriceItems) != 1 {
			logger.Errorf(
				"multi price_item exists,itemZoneKey:%+v,before:%+v,after:%+v",
				itemZoneKey, beforePriceItems, afterPriceItems,
			)
			return nil, errors.New("only one price_item should exist")
		}
		lhs, rhs, err := s.DiffPriceItem(ctx, beforePriceItems[0], afterPriceItems[0])
		if err != nil {
			return nil, errors.Trace(err)
		}

		// diff 结果写入返回值
		if _, ok := beforeDiffItemPriceMap[itemZoneKey.ItemID]; !ok {
			beforeDiffItemPriceMap[itemZoneKey.ItemID] = DiffPriceItemList{
				PriceItems: []DiffPriceItem{},
			}
		}
		l := beforeDiffItemPriceMap[itemZoneKey.ItemID]
		l.PriceItems = append(l.PriceItems, lhs)
		beforeDiffItemPriceMap[itemZoneKey.ItemID] = l

		if _, ok := afterDiffItemPriceMap[itemZoneKey.ItemID]; !ok {
			afterDiffItemPriceMap[itemZoneKey.ItemID] = DiffPriceItemList{
				PriceItems: []DiffPriceItem{},
			}
		}
		l = afterDiffItemPriceMap[itemZoneKey.ItemID]
		l.PriceItems = append(l.PriceItems, rhs)
		afterDiffItemPriceMap[itemZoneKey.ItemID] = l
	}

	return &PriceDiffResp{
		Before: DiffItemPrices{
			ItemPriceMap: beforeDiffItemPriceMap,
		},
		After: DiffItemPrices{
			ItemPriceMap: afterDiffItemPriceMap,
		},
	}, nil
}

// ItemZoneKey contains params for gen key
type ItemZoneKey struct {
	ItemID uint64
	ZoneID uint64
}

// String encode k to string
func (k ItemZoneKey) String() string {
	return fmt.Sprintf("item_id=%d:zone_id=%d", k.ItemID, k.ZoneID)
}

// ItemZoneKeyToPriceItemMap 以 ItemID 和 ZoneID 为 Key, []PriceItem 为 Value
type ItemZoneKeyToPriceItemMap map[ItemZoneKey][]model.PriceItem

// GroupPriceItemWithItemIDAndZoneID 以 ItemID 和 ZoneID 为 Key对 PriceItems 进行分组
func (s *PriceBizService) GroupPriceItemWithItemIDAndZoneID(
	priceItems []model.PriceItem,
) ItemZoneKeyToPriceItemMap {
	m := make(ItemZoneKeyToPriceItemMap)
	itemIDToPriceItemsMap := s.GroupPriceItemsByItemID(priceItems)
	for itemID, itemPriceItems := range itemIDToPriceItemsMap {
		zoneIDToPriceItemsMap := s.GroupPriceItemsByZoneID(itemPriceItems)
		for zoneID, zonePriceItems := range zoneIDToPriceItemsMap {
			key := ItemZoneKey{
				ItemID: itemID,
				ZoneID: zoneID,
			}

			if _, ok := m[key]; !ok {
				m[key] = []model.PriceItem{}
			}
			m[key] = append(m[key], zonePriceItems...)
		}
	}
	return m
}

// GroupPriceItemFromPriceInfluence 以 ItemID 和 ZoneID 为 Key, 聚合 PriceInfluence 数据
func (s *PriceBizService) GroupPriceItemFromPriceInfluence(
	pi *PriceInfluence,
) (before ItemZoneKeyToPriceItemMap, after ItemZoneKeyToPriceItemMap) {

	beforePriceItems := make([]model.PriceItem, 0)
	for _, priceItems := range pi.Before.ItemPrices {
		beforePriceItems = append(beforePriceItems, priceItems...)
	}
	before = s.GroupPriceItemWithItemIDAndZoneID(beforePriceItems)

	afterPriceItems := make([]model.PriceItem, 0)
	for _, priceItems := range pi.After.ItemPrices {
		afterPriceItems = append(afterPriceItems, priceItems...)
	}
	after = s.GroupPriceItemWithItemIDAndZoneID(afterPriceItems)

	return before, after
}

// DiffPriceItem 比较两个 PriceItem, 生成差异数据
func (s *PriceBizService) DiffPriceItem(
	ctx context.Context,
	lhs model.PriceItem,
	rhs model.PriceItem,
) (lhsDiff, rhsDiff DiffPriceItem, err error) {
	lhsDiff.Stairs = make([]DiffPriceItemStair, 0)
	rhsDiff.Stairs = make([]DiffPriceItemStair, 0)

	// 取点算法 or 币种不同时，不作比较
	if !IsSameAlgorithm(lhs, rhs) || lhs.CurrencyType != rhs.CurrencyType {
		lhsDiff, err := s.BuildDiffPriceItemWithType(ctx, &lhs, PriceDiffTypeIncomparable)
		if err != nil {
			return lhsDiff, rhsDiff, errors.Trace(err)
		}

		rhsDiff, err := s.BuildDiffPriceItemWithType(ctx, &rhs, PriceDiffTypeIncomparable)
		if err != nil {
			return lhsDiff, rhsDiff, errors.Trace(err)
		}
		return lhsDiff, rhsDiff, nil
	}

	lhsAlgorithm, err := s.dictClient.GetItemDataTypeAlgorithmByID(ctx, &dict.IDParam{
		Id: lhs.AlgorithmID,
	})
	if err != nil {
		return lhsDiff, rhsDiff, errors.Trace(err)
	}
	dataTypeID := lhsAlgorithm.DataTypeId

	// 找出原有的阶梯价格
	lhsPriceItemStairs, err := s.ListAllPriceItemStairsByPriceItemID(ctx, lhs.ID, 0, -1)
	if err != nil {
		return lhsDiff, rhsDiff, errors.Trace(err)
	}

	rhsPriceItemStairs, err := s.ListAllPriceItemStairsByPriceItemID(ctx, rhs.ID, 0, -1)
	if err != nil {
		return lhsDiff, rhsDiff, errors.Trace(err)
	}

	// 相同数据类型时，把两个价格的阶梯对齐再作比较
	alignedLHSStairs, alignedRHSStairs, err := s.AlignPriceStair(
		ctx, dataTypeID, lhsPriceItemStairs, rhsPriceItemStairs)
	if err != nil {
		return lhsDiff, rhsDiff, errors.Trace(err)
	}

	lhsDiff.PriceItem = lhs
	rhsDiff.PriceItem = rhs
	for i, lhsStair := range alignedLHSStairs {
		rhsStair := alignedRHSStairs[i]
		lhsDiffStair := DiffPriceItemStair{
			Stair: lhsStair,
		}

		rhsDiffStair := DiffPriceItemStair{
			Stair: rhsStair,
		}

		r, err1 := lhsStair.Price.Cmp(rhsStair.Price)
		if err1 != nil {
			return DiffPriceItem{}, DiffPriceItem{}, err1
		}

		switch r {
		case -1:
			lhsDiffStair.Type = PriceDiffTypeLT
			rhsDiffStair.Type = PriceDiffTypeGT
		case 0:
			lhsDiffStair.Type = PriceDiffTypeEQ
			rhsDiffStair.Type = PriceDiffTypeEQ
		case 1:
			lhsDiffStair.Type = PriceDiffTypeGT
			rhsDiffStair.Type = PriceDiffTypeLT
		}

		lhsDiff.Stairs = append(lhsDiff.Stairs, lhsDiffStair)
		rhsDiff.Stairs = append(rhsDiff.Stairs, rhsDiffStair)
	}
	return lhsDiff, rhsDiff, nil
}

// IsSameDataType 判断两个 PriceItem 是否是相同类型的 DataType
func (s *PriceBizService) IsSameDataType(ctx context.Context, lhs, rhs model.PriceItem) (bool, error) {
	lhsAlgorithm, err := s.dictClient.GetItemDataTypeAlgorithmByID(ctx, &dict.IDParam{
		Id: lhs.AlgorithmID,
	})
	if err != nil {
		return false, errors.Trace(err)
	}

	rhsAlgorithm, err := s.dictClient.GetItemDataTypeAlgorithmByID(ctx, &dict.IDParam{
		Id: rhs.AlgorithmID,
	})
	if err != nil {
		return false, errors.Trace(err)
	}

	return lhsAlgorithm.DataTypeId == rhsAlgorithm.DataTypeId, nil
}

// BuildDiffPriceItemWithType 根据 diffType 生成相应的 DiffPriceItem
func (s *PriceBizService) BuildDiffPriceItemWithType(
	ctx context.Context,
	priceItem *model.PriceItem,
	diffType PriceDiffType,
) (diff DiffPriceItem, err error) {
	stairs, err := s.ListAllPriceItemStairsByPriceItemID(ctx, priceItem.ID, 0, -1)
	if err != nil {
		return diff, errors.Trace(err)
	}

	diff.PriceItem = *priceItem
	diff.Stairs = make([]DiffPriceItemStair, 0)
	for _, stair := range stairs {
		diff.Stairs = append(diff.Stairs, DiffPriceItemStair{
			Stair: stair,
			Type:  diffType,
		})
	}

	return diff, nil
}

// AlignPriceStair 将两个阶梯价格对齐，前提条件是 DataType 相同, UnitRate 相同
func (s *PriceBizService) AlignPriceStair(
	ctx context.Context,
	dataTypeID uint64,
	lhs []model.PriceItemStair,
	rhs []model.PriceItemStair,
) (lhsAlignedStairs, rhsAlignedStairs []model.PriceItemStair, err error) {
	// 阶梯价格需要对 quantity = 0 的记录特殊处理
	// 阶梯的表达方式如下：
	// 1. 只有一个阶梯，且 quantity = 0, 则表示的是阶梯是 [0, ∞] 的区间
	// 2. [0, A] [A, A] 表示有两个区间，第一个区间 quantity = A, 第二个区间也是 quantity = A,
	//    表示 [A, ∞]
	if len(lhs) == 1 && len(rhs) == 1 && lhs[0].Quantity == 0 && rhs[0].Quantity == 0 {
		return lhs, rhs, nil
	}

	// 1. 找出 DataType 对应的 UnitRate
	dataType, err := s.dictClient.GetItemDataTypeByID(ctx, &dict.IDParam{Id: dataTypeID})
	if err != nil {
		return nil, nil, errors.Trace(err)
	}

	units, err := s.dictClient.ListItemDataTypeUnitsByItemDataTypeID(ctx, &dict.IDPagingParam{
		Id:       dataTypeID,
		Page:     1,
		PageSize: 1000,
	})
	if err != nil {
		return nil, nil, errors.Trace(err)
	}

	// 2. 找出所有的阶梯分段点
	stairUnitMap := make(map[uint64]uint64)
	segmentPoints := make([]uint64, 0)
	m := make(map[uint64]struct{})
	allStairs := append(lhs, rhs...)
	for _, stair := range allStairs {
		unit, err := s.dictClient.GetItemDataTypeUnitByID(ctx, &dict.IDParam{Id: stair.UnitID})
		if err != nil {
			return lhsAlignedStairs, rhsAlignedStairs, errors.Trace(err)
		}

		v := uint64(math.Pow(float64(dataType.UnitRate), float64(unit.Power))) * stair.Quantity
		m[v] = struct{}{}
		stairUnitMap[stair.ID] = v
	}

	for v := range m {
		segmentPoints = append(segmentPoints, v)
	}
	sort.Stable(base.Uint64Slice(segmentPoints))
	if len(segmentPoints) >= 1 && segmentPoints[len(segmentPoints)-1] != 0 {
		segmentPoints = append(segmentPoints, segmentPoints[len(segmentPoints)-1])
	}

	// 3. 按照分段点将原有阶梯价格切分
	for i, segmentPoint := range segmentPoints {
		// 当有多个阶梯时，忽略 quantity = 0 的阶梯
		if segmentPoint == 0 && len(segmentPoints) > 1 {
			continue
		}

		// 找出一个合适的 power
		var power, unitID uint64
		for _, unit := range units.GetItemDataTypeUnits() {
			v := uint64(math.Pow(float64(dataType.UnitRate), float64(unit.Power)))
			if (segmentPoint == 0 && unit.IsPriceAnchor) ||
				(segmentPoint != 0 && segmentPoint >= v && (segmentPoint%v == 0)) {
				power = unit.Power
				unitID = unit.Id
			}
		}

		if power == 0 {
			return nil, nil, errors.New("no valid unit")
		}

		quantity := segmentPoint / uint64(math.Pow(float64(dataType.UnitRate), float64(power)))

		// 找出原有的价格中包含该分段点的价格
		var originLHSStair *model.PriceItemStair
		var originRHSStair *model.PriceItemStair

		for j, stair := range lhs {
			v := stairUnitMap[stair.ID]
			// 1. 阶梯值为 0 时直接命中
			// 2. 最后一个分段点时, 命中最后一个阶梯
			// 2. 不是最后一个分段点时:
			//    a. 分段点小于等于阶梯值时，命中
			//    b. 分段点大于等于阶梯值且是最后一个阶梯时，命中
			if v == 0 ||
				(i == len(segmentPoints)-1 && j == len(lhs)-1) {
				originLHSStair = &lhs[j]
				break
			}

			if i != len(segmentPoints)-1 {
				if j == len(lhs)-1 && segmentPoint >= v {
					originLHSStair = &lhs[j]
					break
				}

				if segmentPoint <= v {
					originLHSStair = &lhs[j]
					break
				}
			}
		}

		for j, stair := range rhs {
			v := stairUnitMap[stair.ID]
			// 1. 阶梯值为 0 时直接命中
			// 2. 最后一个分段点时, 命中最后一个阶梯
			// 2. 不是最后一个分段点时:
			//    a. 分段点小于阶梯值时，命中
			//    b. 分段点大于等于阶梯值且是最后一个阶梯时，命中
			if v == 0 ||
				(i == len(segmentPoints)-1 && j == len(rhs)-1) {
				originRHSStair = &rhs[j]
				break
			}

			if i != len(segmentPoints)-1 {
				if j == len(rhs)-1 && segmentPoint >= v {
					originRHSStair = &rhs[j]
					break
				}

				if segmentPoint <= v {
					originRHSStair = &rhs[j]
					break
				}
			}
		}

		if originLHSStair == nil || originRHSStair == nil {
			return nil, nil, errors.New("invalid price stairs")
		}

		newLHSStair := model.PriceItemStair{
			ID:          originLHSStair.ID,
			PriceItemID: originLHSStair.PriceItemID,
			UnitID:      unitID,
			Quantity:    quantity,
			Price:       originLHSStair.Price,
			Order:       uint64(i),
			CreatedAt:   originLHSStair.CreatedAt,
			UpdatedAt:   originLHSStair.UpdatedAt,
		}
		newRHSStair := model.PriceItemStair{
			ID:          originRHSStair.ID,
			PriceItemID: originRHSStair.PriceItemID,
			UnitID:      unitID,
			Quantity:    quantity,
			Price:       originRHSStair.Price,
			Order:       uint64(i),
			CreatedAt:   originRHSStair.CreatedAt,
			UpdatedAt:   originRHSStair.UpdatedAt,
		}
		lhsAlignedStairs = append(lhsAlignedStairs, newLHSStair)
		rhsAlignedStairs = append(rhsAlignedStairs, newRHSStair)
	}

	return lhsAlignedStairs, rhsAlignedStairs, nil
}

// DiffCostPriceWithCreatedTime 查询报价单提交前的价格与成本价的差异
//
// NOTE: 只计算指定报价单包含的计费项的价格差异, 且查询的是该报价单创建时间点之前的
// 价格信息和当前报价单的价格差异
//
// 步骤：
// 1. 查询当前生效的价格及指定报价单详情
// 2. 根据报价单获取要比对价格的 (item, zone) 列表
// 3. 分别计算各 (item, zone) 在报价单提交前的价格与当前成本价的差异
// 3.a. 将两个阶梯价格切分成相同的阶梯
// 3.b. 分别比较相同阶梯时的价格差异
func (s *PriceBizService) DiffCostPriceWithCreatedTime(
	ctx context.Context,
	uid uint64,
	priceID uint64,
	scope *PriceScope,
) (*PriceDiffResp, error) {
	priceInfluence, err := s.GetPriceInfluenceWithCostPrice(ctx, uid, priceID, scope)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return s.ConvertPriceInfluenceToPriceDiffResp(ctx, priceInfluence)
}
