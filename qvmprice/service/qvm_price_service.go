package service

import (
	"context"
	"errors"
	"time"

	"github.com/qbox/bo-base/v4/intl/tz"
	dictpb "github.com/qbox/pay-sdk/dict"
	walletpb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qvmprice/model"
)

type QVMPriceService struct {
	dao *model.QVMPriceDao

	dictV4     dictpb.PayDictServiceClient
	paymentSrv walletpb.PaymentServiceClient
}

func NewQVMPriceService(
	dao *model.QVMPriceDao,
	dictV4 dictpb.PayDictServiceClient,
	paymentSrv walletpb.PaymentServiceClient,
) *QVMPriceService {
	return &QVMPriceService{
		dao:        dao,
		dictV4:     dictV4,
		paymentSrv: paymentSrv,
	}
}

// ImportPublicPrices 导入一组 QVM 的公开报价
func (a *QVMPriceService) ImportPublicPrices(
	ctx context.Context,
	initiallyDisabled bool,
	priceItems []*QVMPriceItemDetail,
	remark string,
	epoch time.Time,
) error {
	if len(priceItems) == 0 {
		return errors.New("ImportPublicPrices price items must not be empty")
	}

	epoch = ensureEpoch(epoch, tz.MustLocationFromCtx(ctx))

	err := validatePriceItemsForInsertion(priceItems, true)
	if err != nil {
		return err
	}

	return a.createPriceTable(
		ctx,
		0,
		remark,
		initiallyDisabled,
		priceItems,
		nil,
		epoch,
	)
}

// CreatePriceTableForUID 为某个用户创建一张 QVM 报价单（“报价”）
func (a *QVMPriceService) CreatePriceTableForUID(
	ctx context.Context,
	uid uint64,
	initiallyDisabled bool,
	priceItems []*QVMPriceItemDetail,
	groupDiscounts []*QVMGroupDiscountDetail,
	remark string,
	epoch time.Time,
) error {
	if uid == 0 {
		return errors.New("CreatePriceTableForUID uid must not be 0")
	}

	epoch = ensureEpoch(epoch, tz.MustLocationFromCtx(ctx))

	err := validatePriceItemsForInsertion(priceItems, false)
	if err != nil {
		return err
	}

	err = validateGroupDiscountsForInsertion(groupDiscounts)
	if err != nil {
		return err
	}

	return a.createPriceTable(
		ctx,
		uid,
		remark,
		initiallyDisabled,
		priceItems,
		groupDiscounts,
		epoch,
	)
}

// TogglePriceTable 整体启用/禁用一张 QVM 报价单
func (a *QVMPriceService) TogglePriceTable(
	ctx context.Context,
	priceTableID uint64,
	enable bool,
) error {
	if priceTableID == 0 {
		return errors.New("TogglePriceTable price table ID must not be 0")
	}

	return a.dao.QVMPriceTable.ToggleByID(priceTableID, enable)
}
