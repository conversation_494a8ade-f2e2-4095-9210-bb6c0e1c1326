package service

import (
	"fmt"

	"qiniu.io/pay/creditd/model/enums"
)

type Transition struct {
	From  enums.CreditStatus
	To    enums.CreditStatus
	Event enums.Event
}

type ActionHandler func()

type CreditStateMachine struct {
	transitions []Transition
}

// NewCreditStateMachine
func NewCreditStateMachine(trans []Transition) (*CreditStateMachine, error) {
	err := checkTransitions(trans)
	if err != nil {
		return nil, err
	}
	return &CreditStateMachine{trans}, nil
}

func (m *CreditStateMachine) Process(currentState enums.CreditStatus, event enums.Event) (to enums.CreditStatus, err error) {
	for _, tran := range m.transitions {
		if tran.From == currentState && tran.Event == event {
			to = tran.To
			return
		}
	}
	err = fmt.Errorf("status:%s can not apply event:%s", currentState, event)
	return
}

// checkTransitions checks if transitions having invalid state
func checkTransitions(trans []Transition) (err error) {
	transitionMap := make(map[string]enums.CreditStatus)
	keyFmt := "from:%s-event:%s"
	for _, tran := range trans {
		key := fmt.Sprintf(keyFmt, tran.From, tran.Event)
		if _, ok := transitionMap[key]; !ok {
			transitionMap[key] = tran.To
		} else {
			// 可能重复或指向不同的 to
			return fmt.Errorf("state machine got invalid states, %s", key)
		}
	}
	return
}
