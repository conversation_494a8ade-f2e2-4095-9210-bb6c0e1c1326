package service

import (
	"context"

	"qiniu.io/pay/dictd/model"

	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"
)

// FindPayBanksByCityAndBankCode 根据 city_code 和 bank_code 获取所有 paybank 记录
func (s *DictBizService) FindPayBanksByCityAndBankCode(
	ctx context.Context,
	cityCode string,
	bankCode string,
) ([]*model.PayBank, error) {
	l := logging.GetLogger(ctx)
	result, err := s.dictDao.PayBank.FindByCityAndBankCode(cityCode, bankCode)
	if err != nil {
		l.WithFields(logrus.Fields{
			"cityCode": cityCode,
			"bankCode": bankCode,
		}).WithError(err).Error("FindByCityAndBankCode failed")
		return nil, err
	}
	return result, nil
}

// FindPayBankByName 根据支行名字获取支行信息
func (s *DictBizService) FindPayBankByName(
	ctx context.Context,
	name string,
) (*model.PayBank, error) {
	l := logging.GetLogger(ctx)
	result, err := s.dictDao.PayBank.FindByName(name)
	if err != nil {
		l.WithFields(logrus.Fields{
			"name": name,
		}).WithError(err).Error("FindPayBankByName failed")
		return nil, err
	}
	return result, nil
}

func (s *DictBizService) FindPayBankNamesByCond(
	ctx context.Context,
	name string,
	cityCode string,
	bankCode string,
) ([]string, error) {
	l := logging.GetLogger(ctx)

	req := &model.FindPayBankNamesReq{
		Name:     name,
		CityCode: cityCode,
		BankCode: bankCode,
	}
	list, err := s.dictDao.PayBank.FindNamesByCond(req)
	if err != nil {
		l.WithFields(logrus.Fields{
			"name":     name,
			"cityCode": cityCode,
			"bankCode": bankCode,
		}).WithError(err).Error("FindNamesByCond failed")
		return nil, err
	}
	return list, nil
}
