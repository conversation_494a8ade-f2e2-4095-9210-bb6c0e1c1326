package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
)

// BuildPbScopeItemMap converts model.ScopeItemMap to the protobuf type.
func BuildPbScopeItemMap(_map *model.ScopeItemMap) (*pb.ScopeItemMap, error) {
	return &pb.ScopeItemMap{
		Id:         _map.ID,
		ScopeId:    _map.ScopeID,
		ItemId:     _map.ItemID,
		IsExcluded: _map.IsExcluded,
		CreatedAt:  timestamppb.New(_map.CreatedAt),
		UpdatedAt:  timestamppb.New(_map.UpdatedAt),
	}, nil
}

// BuildScopeItemMap converts from pb.ScopeItemMap to the model layer type.
func BuildScopeItemMap(_map *pb.ScopeItemMap) (*model.ScopeItemMap, error) {
	var createdAt, updatedAt time.Time
	if _map.GetCreatedAt() != nil {
		err := _map.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = _map.GetCreatedAt().AsTime()
	}

	if _map.GetUpdatedAt() != nil {
		err := _map.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = _map.GetUpdatedAt().AsTime()
	}

	return &model.ScopeItemMap{
		ID:         _map.GetId(),
		ScopeID:    _map.GetScopeId(),
		ItemID:     _map.GetItemId(),
		ItemCode:   _map.GetItemCode(),
		IsExcluded: _map.GetIsExcluded(),
		CreatedAt:  createdAt,
		UpdatedAt:  updatedAt,
	}, nil
}

// BuildPbScopeItemMaps converts from []model.ScopeItemMap to the protobuf type.
func BuildPbScopeItemMaps(x []model.ScopeItemMap) ([]*pb.ScopeItemMap, error) {
	y := make([]*pb.ScopeItemMap, len(x))
	for i, obj := range x {
		result, err := BuildPbScopeItemMap(&obj)
		if err != nil {
			return nil, err
		}
		y[i] = result
	}
	return y, nil
}

// BuildScopeItemMaps converts from []*pb.ScopeItemMap to the model layer type.
func BuildScopeItemMaps(x []*pb.ScopeItemMap) ([]model.ScopeItemMap, error) {
	y := make([]model.ScopeItemMap, len(x))
	for i, obj := range x {
		result, err := BuildScopeItemMap(obj)
		if err != nil {
			return nil, err
		}
		y[i] = *result
	}
	return y, nil
}
