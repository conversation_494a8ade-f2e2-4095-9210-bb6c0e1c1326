package adapter

import (
	"time"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/wallet"
	"google.golang.org/protobuf/types/known/timestamppb"

	"qiniu.io/pay/coupon/model"
	"qiniu.io/pay/coupon/service"
)

// ComposeCouponDetail 用抵用券和抵用券批次记录拼接抵用券详情
func ComposeCouponDetail(
	c *model.Coupon,
	cb *model.CouponBatch,
	scope *service.CouponScope,
) (*pb.CouponDetail, error) {
	pbScope, err := ComposeCouponScopeDetail(scope, cb.IsUnlimitedScope)
	if err != nil {
		return nil, err
	}

	return &pb.CouponDetail{
		// Coupon
		Uid:        c.UID,
		BatchId:    c.BatchID,
		CouponCode: c.Code,
		BindMethod: pb.CouponBindMethod(c.BindMethod),
		State:      pb.CouponState(c.State),
		Money:      c.Money.ToInt64(),
		Balance:    c.Balance.ToInt64(),
		BoundAt:    timestamppb.New(c.BoundAt.TimeIn(time.UTC)),
		EffectTime: timestamppb.New(c.EffectTime.TimeIn(time.UTC)),
		DeadTime:   timestamppb.New(c.DeadTime.TimeIn(time.UTC)),

		// CouponBatch
		Name:            cb.Name,
		Description:     cb.Description,
		PayModeScope:    pb.CouponPayModeScope(cb.ApplicablePayMode),
		UserScope:       pb.CouponUserScope(cb.ApplicableUserType),
		ThresholdMoney:  cb.ThresholdMoney.ToInt64(),
		IsMultipleUse:   cb.IsMultipleUse,
		ArrearCanUse:    cb.ArrearCanUse,
		Scope:           pbScope,
		CouponScopeDesc: cb.CouponScopeDesc,
		Applicant:       pb.ApplicantType(cb.Applicant),
		Url:             cb.Url,
	}, nil
}

// BuildPbCouponDetail converts *service.CouponAndBatch to the pb layer type.
func BuildPbCouponDetail(in *service.CouponAndBatch) (*pb.CouponDetail, error) {
	return ComposeCouponDetail(in.Coupon, in.Batch.CouponBatch, in.Batch.Scope)
}

// BuildPbCouponDetails converts []*service.CouponAndBatch to the pb layer type.
func BuildPbCouponDetails(in []*service.CouponAndBatch) ([]*pb.CouponDetail, error) {
	result := make([]*pb.CouponDetail, len(in))
	for i, x := range in {
		obj, err := BuildPbCouponDetail(x)
		if err != nil {
			return nil, errors.Trace(err)
		}
		result[i] = obj
	}
	return result, nil
}
