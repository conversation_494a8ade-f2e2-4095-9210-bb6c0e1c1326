run:
    go: 1.24
    # default is not enough
    timeout: 5m
issues:
  exclude-dirs:
    - ci
    - prow.d
    - spock.d
linters:
    disable-all: false
    enable:
        - bodyclose
        - dogsled
        - gochecknoinits
        - goimports
        - gosmopolitan
        - misspell
        - nakedret
        - unused
    disable:
        - whitespace  # too many false positives
        # 以下的 linter 我们很想打开，修起来比较容易
        - gosec  # 可能有一些误报
        - lll
        - prealloc
        # 以下的 linter 我们也很想打开，但是需要一个勇士来做这件事情
        - errcheck
        - gochecknoglobals
        - goconst
        # 以下的 linter 真的很难搞，真的需要勇士
        - dupl

linters-settings:
    gosmopolitan:
        escape-hatches:
            - 'github.com/nicksnyder/go-i18n/v2/i18n.Message'
    nakedret:
        max-func-lines: 50
    govet:
      disable:
        - printf
