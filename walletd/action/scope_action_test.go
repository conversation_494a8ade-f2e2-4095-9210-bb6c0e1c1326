package action_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/stretchr/testify/assert"
)

func TestScopeDetailInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	walletClient := sandbox.walletClient

	sd := &pb.ScopeDetail{
		Products: []*pb.ScopeProductMap{
			{
				ProductId: 1,
			},
			{
				ProductId: 2,
			},
		},
		Groups: []*pb.ScopeItemGroupMap{
			{
				GroupId: 1,
			},
			{
				GroupId: 2,
			},
		},
		Items: []*pb.ScopeItemMap{
			{
				ItemId: 1,
			},
		},
	}
	assertScopeDetails := func(d1, d2 *pb.ScopeDetail, msgAndArgs ...any) {
		productMap := make(map[uint64]*pb.ScopeProductMap)
		for _, product := range d1.GetProducts() {
			productMap[product.GetProductId()] = product
		}
		for _, p := range d2.GetProducts() {
			if product, ok := productMap[p.GetProductId()]; assert.True(t, ok, msgAndArgs...) {
				assert.Equal(t, product.GetIsExcluded(), p.GetIsExcluded(), msgAndArgs...)
			}
		}
		groupMap := make(map[uint64]*pb.ScopeItemGroupMap)
		for _, group := range d1.GetGroups() {
			groupMap[group.GetGroupId()] = group
		}
		for _, g := range d2.GetGroups() {
			if group, ok := groupMap[g.GetGroupId()]; assert.True(t, ok, msgAndArgs...) {
				assert.Equal(t, group.GetIsExcluded(), g.GetIsExcluded(), msgAndArgs...)
			}
		}
		itemMap := make(map[uint64]*pb.ScopeItemMap)
		for _, item := range d1.GetItems() {
			itemMap[item.GetItemId()] = item
		}
		for _, i := range d2.GetItems() {
			if item, ok := itemMap[i.GetItemId()]; assert.True(t, ok, msgAndArgs...) {
				assert.Equal(t, item.GetIsExcluded(), i.GetIsExcluded(), msgAndArgs...)
			}
		}
	}
	{
		scope, err := walletClient.CreateScope(context.Background(), &pb.Scope{
			Remark: "测试计费范围1",
		})
		if assert.NoError(t, err, "CreateScope") {
			assert.NotZero(t, scope.GetId(), "CreateScope")
		}
		sd.Id = scope.GetId()
		scopeDetail, err := walletClient.CreateScopeDetail(context.Background(), sd)
		if assert.NoError(t, err, "CreateScopeDetail") {
			assert.NotZero(t, scopeDetail.GetId(), "CreateScopeDetail")
			assertScopeDetails(sd, scopeDetail, "CreateScopeDetail")
		}
	}

	list, err := walletClient.ListAllScopeDetails(context.Background(), &pb.PagingParam{PageSize: 10, Page: 1})
	if assert.NoError(t, err, "ListAllScopeDetails") {
		assert.Equal(t, 1, int(list.GetCount()), "ListAllScopeDetails")
		for _, scopeDetail := range list.GetScopeDetails() {
			assert.NotZero(t, scopeDetail.GetId(), "ListAllScopeDetails")
			assertScopeDetails(sd, scopeDetail, "ListAllScopeDetails")
			detail, err := walletClient.GetScopeDetailByID(context.Background(), &pb.IDParam{Id: scopeDetail.GetId()})
			if assert.NoError(t, err, "CountAllScopes") {
				assertScopeDetails(scopeDetail, detail, "CountAllScopes")
			}
		}
	}

	// do some cleanup
	{
		_, err = walletClient.DeleteScopeByID(context.Background(), &pb.IDParam{Id: sd.GetId()})
		assert.NoError(t, err, "DeleteScopeByID")
		{
			list, err := walletClient.DeleteScopeProductMapsByScopeID(context.Background(), &pb.IDParam{Id: sd.GetId()})
			if assert.NoError(t, err, "DeleteScopeProductMapsByScopeID") {
				assert.Zero(t, list.GetCount(), "DeleteScopeProductMapsByScopeID")
			}
		}
		{
			list, err := walletClient.DeleteScopeItemGroupMapsByScopeID(context.Background(), &pb.IDParam{Id: sd.GetId()})
			if assert.NoError(t, err, "DeleteScopeItemGroupMapsByScopeID") {
				assert.Zero(t, list.GetCount(), "DeleteScopeItemGroupMapsByScopeID")
			}
		}
		{
			list, err := walletClient.DeleteScopeItemMapsByScopeID(context.Background(), &pb.IDParam{Id: sd.GetId()})
			if assert.NoError(t, err, "DeleteScopeItemMapsByScopeID") {
				assert.Zero(t, list.GetCount(), "DeleteScopeItemMapsByScopeID")
			}
		}
	}
}
func TestScopeInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	walletClient := sandbox.walletClient

	// lock.Lock()
	// defer lock.Unlock()
	ss := []pb.Scope{
		{
			Remark: "测试计费范围1",
		},
		{
			Remark: "测试计费范围2",
		},
		{
			Remark: "测试计费范围3",
		},
	}
	assertFields := func(scope *pb.Scope, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(ss) }) {
			assert.Equal(t, ss[n].GetRemark(), scope.GetRemark(), msgAndArgs...)
		}
	}
	ids := make(map[uint64]int, len(ss))
	for n, s := range ss {
		scope, err := walletClient.CreateScope(context.Background(), &s)
		if assert.NoError(t, err, "CreateScope") {
			assert.NotZero(t, scope.GetId(), "CreateScope")
			ids[scope.GetId()] = n
		}

		scope, err = walletClient.GetScopeByID(context.Background(), &pb.IDParam{Id: scope.GetId()})
		if assert.NoError(t, err, "GetScopeByID") {
			assertFields(scope, n, "GetScopeByID")
		}

		scope.Remark += s.GetRemark()
		ss[n].Remark += s.GetRemark()
		scope, err = walletClient.UpdateScopeByID(context.Background(), &pb.IDScopeParam{Id: scope.GetId(), Scope: scope})
		if assert.NoError(t, err, "UpdateScopeByID") {
			assert.Equal(t, ss[n].GetRemark(), scope.GetRemark(), "UpdateScopeByID")
		}
	}

	count, err := walletClient.CountAllScopes(context.Background(), &empty.Empty{})
	if assert.NoError(t, err, "CountAllScopes") {
		assert.Len(t, ss, int(count.GetCount()), "CountAllScopes")
	}

	list, err := walletClient.ListAllScopes(context.Background(), &pb.PagingParam{Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListAllScopes") {
		assert.Len(t, ss, int(list.GetCount()), "ListAllScopes")
		for _, scope := range list.GetScopes() {
			if assert.Contains(t, ids, scope.GetId(), "ListAllScopes") {
				assertFields(scope, ids[scope.GetId()], "ListAllScopes")
			}
		}
	}

	for id, n := range ids {
		scope, err := walletClient.DeleteScopeByID(context.Background(), &pb.IDParam{Id: id})
		if assert.NoError(t, err, "DeleteScopeByID") {
			assertFields(scope, n, "DeleteScopeByID")
		}
	}
}
