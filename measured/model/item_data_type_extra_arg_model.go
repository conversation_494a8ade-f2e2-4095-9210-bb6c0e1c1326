package model

import (
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// ItemDataTypeExtraArg model definition
type ItemDataTypeExtraArg struct {
	ID             uint64    `gorm:"primary_key"`
	ItemDataTypeID uint64    `gorm:"index:idx_data_type_extra_arg_item_data_type_id"`
	Key            string    `gorm:"type:varchar(64)"`
	Value          string    `gorm:"type:varchar(128)"`
	CreatedAt      time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt      time.Time `sql:"type:DATETIME(6)"`
}

// ItemDataTypeExtraArgDao is data access object of ItemDataTypeExtraArg model
type ItemDataTypeExtraArgDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *ItemDataTypeExtraArgDao) GetCachePrefix() string {
	return "measure:item_data_type_extra_arg:"
}

// GetCacheRefs get cache refs
func (d *ItemDataTypeExtraArgDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewItemDataTypeExtraArgDao is constructor of ItemDataTypeExtraArgDao
func NewItemDataTypeExtraArgDao(base *dao.BaseDao) *ItemDataTypeExtraArgDao {
	cachePrefix := (*ItemDataTypeExtraArgDao)(nil).GetCachePrefix()
	return &ItemDataTypeExtraArgDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of ItemDataTypeExtraArg by id
func (d *ItemDataTypeExtraArgDao) GetByID(id uint64, expires ...time.Duration) (*ItemDataTypeExtraArg, error) {
	model := &ItemDataTypeExtraArg{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:extra_arg_id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// ListByItemDataTypeID select records of ItemDataTypeExtraArg by item_data_type_id
func (d *ItemDataTypeExtraArgDao) ListByItemDataTypeID(
	idtID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (data []ItemDataTypeExtraArg, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:item_data_type_id=%d&offset=%d&limit=%d",
		d.cachePrefix,
		idtID,
		offset,
		limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&ItemDataTypeExtraArg{ItemDataTypeID: idtID}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &data, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"item_data_type_id": idtID,
			"offset":            offset,
			"limit":             limit,
		})
	}
	return
}

// CountByItemDataTypeID select count of ItemDataTypeExtraArg by item_data_type_id
func (d *ItemDataTypeExtraArgDao) CountByItemDataTypeID(
	idtID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:item_data_type_id=%d:count",
		d.cachePrefix,
		idtID,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&ItemDataTypeExtraArg{}).Where(&ItemDataTypeExtraArg{ItemDataTypeID: idtID}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"item_data_type_id": idtID,
		})
	}
	return
}

// Save insert or update a record of ItemDataTypeExtraArg by id
func (d *ItemDataTypeExtraArgDao) Save(model *ItemDataTypeExtraArg, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// Delete delete a record of ItemDataTypeExtraArg
func (d *ItemDataTypeExtraArgDao) Delete(model *ItemDataTypeExtraArg) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
}

// DeleteByID delete a record of ItemDataTypeExtraArg by id
func (d *ItemDataTypeExtraArgDao) DeleteByID(id uint64) error {
	model := &ItemDataTypeExtraArg{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}
