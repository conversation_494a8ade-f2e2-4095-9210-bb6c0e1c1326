package adapter

import (
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/model"
)

// BuildPbUsersRespV1 converts from model.UsersResp to the protobuf type.
func BuildPbUsersRespV1(x model.UsersResp) (*pb.UsersRespV1, error) {
	uids := make([]uint64, len(x))
	_ = copy(uids, x)

	return &pb.UsersRespV1{
		Uids: uids,
	}, nil
}

// BuildModelUsersRespV1 converts from pb.UsersRespV1 to the model layer type.
func BuildModelUsersRespV1(x *pb.UsersRespV1) (model.UsersResp, error) {
	uids := make([]uint64, len(x.Uids))
	_ = copy(uids, x.Uids)

	return model.UsersResp(uids), nil
}
