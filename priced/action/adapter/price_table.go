package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"

	"qiniu.io/pay/priced/model"
)

// BuildPbPriceType 转换数据层 model PriceType 为 proto model
func BuildPbPriceType(typ model.PriceTableType) pb.PriceType {
	return pb.PriceType(pb.PriceType_value[string(typ)])
}

// BuildPriceTableType 转换 proto model 为数据层 model PriceTableType
func BuildPriceTableType(typ pb.PriceType) model.PriceTableType {
	return model.PriceTableType(typ.String())
}

// BuildPbPriceTable 转换数据层 model PriceTable 为 proto model
func BuildPbPriceTable(priceTable *model.PriceTable) (*pb.PriceTable, error) {
	return &pb.PriceTable{
		Id:         priceTable.ID,
		Type:       BuildPbPriceType(priceTable.Type),
		Remark:     priceTable.Remark,
		IsDisabled: priceTable.IsDisabled,

		CreatedAt: timestamppb.New(priceTable.CreatedAt),
		UpdatedAt: timestamppb.New(priceTable.UpdatedAt),
	}, nil
}

// BuildPriceTable 转换 proto model 为数据层 model PriceTable
func BuildPriceTable(priceTable *pb.PriceTable) (*model.PriceTable, error) {
	var createdAt, updatedAt time.Time
	if priceTable.GetCreatedAt() != nil {
		err := priceTable.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		createdAt = priceTable.GetCreatedAt().AsTime()
	}

	if priceTable.GetUpdatedAt() != nil {
		err := priceTable.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		updatedAt = priceTable.GetUpdatedAt().AsTime()
	}

	return &model.PriceTable{
		ID:         priceTable.GetId(),
		Type:       BuildPriceTableType(priceTable.Type),
		Remark:     priceTable.GetRemark(),
		IsDisabled: priceTable.IsDisabled,
		CreatedAt:  createdAt,
		UpdatedAt:  updatedAt,
	}, nil
}
