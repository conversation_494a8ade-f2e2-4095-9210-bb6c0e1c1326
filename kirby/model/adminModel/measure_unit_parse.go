package adminModel

import (
	"errors"
	"strconv"
	"strings"
)

var errInvalidFactor = errors.New("invalid factor")

var cachedMeasureUnitReprs = map[string]ParsedMeasureUnit{
	"1": {Kind: MeasureUnitKindDimensionless, classifier: "", premultipliedFactor: 1},

	"B":   {Kind: MeasureUnitKindData, premultipliedFactor: 1},
	"KB":  {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorK},
	"MB":  {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorM},
	"GB":  {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorG},
	"TB":  {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorT},
	"PB":  {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorP},
	"KiB": {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorKi},
	"MiB": {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorMi},
	"GiB": {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorGi},
	"TiB": {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorTi},
	"PiB": {Kind: MeasureUnitKindData, premultipliedFactor: scalingFactorPi},

	"bps": {Kind: MeasureUnitKindBandwidth, premultipliedFactor: 1},

	"ns":  {Kind: MeasureUnitKindDuration, premultipliedFactor: scalingFactorNanosecond},
	"us":  {Kind: MeasureUnitKindDuration, premultipliedFactor: scalingFactorMicrosecond},
	"ms":  {Kind: MeasureUnitKindDuration, premultipliedFactor: scalingFactorMillisecond},
	"s":   {Kind: MeasureUnitKindDuration, premultipliedFactor: scalingFactorSecond},
	"min": {Kind: MeasureUnitKindDuration, premultipliedFactor: scalingFactorMinute},
	"hr":  {Kind: MeasureUnitKindDuration, premultipliedFactor: scalingFactorHour},
	"d":   {Kind: MeasureUnitKindDuration, premultipliedFactor: scalingFactorDay},
}

func ParseMeasureUnitRepr(x string) (*ParsedMeasureUnit, error) {
	// fast path
	if cachedResult, ok := cachedMeasureUnitReprs[x]; ok {
		return &cachedResult, nil
	}

	// 目前此处只需要处理无量纲数了
	// 格式: ^(\d+)(?:-(.*))$
	// 人话: {{预乘系数}} or {{预乘系数}}-{{量词}}
	// 此处 strings.Cut 在输入不包含分隔符时的语义满足我们的需求，因此不需要取出 found flag 了
	factorStr, classifier, _ := strings.Cut(x, "-")
	factor, err := strconv.ParseInt(factorStr, 10, 64)
	if err != nil {
		return nil, errInvalidFactor
	}

	return &ParsedMeasureUnit{
		Kind:                MeasureUnitKindDimensionless,
		classifier:          classifier,
		premultipliedFactor: ScalingFactor(factor),
	}, nil
}
