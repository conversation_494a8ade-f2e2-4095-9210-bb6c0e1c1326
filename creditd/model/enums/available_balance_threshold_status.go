package enums

// ThresholdStatus defines available balance threshold status type
type ThresholdStatus uint32

const (
	// UnknownThresholdStatus is a unknown status
	UnknownThresholdStatus = ThresholdStatus(iota) // 0
	// StatusThresholdNotEnabled 未启用
	StatusThresholdNotEnabled // 1
	// StatusThresholdEnabled 启用
	StatusThresholdEnabled // 2
)

func (s ThresholdStatus) String() string {
	switch s {
	case UnknownThresholdStatus:
		return "UNKNOWN"
	case StatusThresholdNotEnabled:
		return "NOT_ENABLED"
	case StatusThresholdEnabled:
		return "ENABLED"
	}
	return "UNKNOWN"
}
