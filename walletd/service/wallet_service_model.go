package service

import (
	"github.com/qbox/bo-base/v4/base"

	"qiniu.io/pay/walletd/model"
)

// BindCouponRebateParam params for bind rebate onto user
type BindCouponRebateParam struct {
	UID           uint64
	Code          string
	TransactionID uint64
	ZoneID        uint64
	EffectTime    base.HNS
	DeadTime      base.HNS
	Idempotent    bool
}

// BindCouponRebateResp is response for rebate bind/upgrades.
type BindCouponRebateResp struct {
	Coupon        model.CouponRebate
	CouponUserMap model.CouponUserMap
}

// UpgradeCouponRebateParam params for upgrade rebate for user
type UpgradeCouponRebateParam struct {
	UID           uint64
	ZoneID        uint64
	TransactionID uint64
	SrcCode       string
	DstCode       string
	EffectTime    base.HNS
	DeadTime      base.HNS
}

// BindCouponParam params for bind couponDiscount/Rebate onto user
type BindCouponParam struct {
	CouponID      uint64
	CouponType    model.CouponType
	UID           uint64
	Code          string
	TransactionID uint64
	ZoneID        uint64
	EffectTime    base.HNS
	DeadTime      base.HNS
}
