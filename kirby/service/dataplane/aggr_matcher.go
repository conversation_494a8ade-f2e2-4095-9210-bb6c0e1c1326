package dataplane

import (
	"errors"
	"fmt"
	"sync"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/dataplaneModel"
)

var warningNoAggregationRuleMatch = errors.New("warning: no aggregation rule match")
var errMultipleAggregationRulesMatched = errors.New("error: multiple aggregation rules matched")

type aggregationRuleMatcher struct {
	updateMu sync.RWMutex
	// 当前认识的所有聚合规则
	// map[aggrCode]aggr
	aggrs map[string]*adminModel.AggregationRule
	// 反查索引
	idx map[matcherIdxKey][]matcherIdxEntry
}

type matcherIdxEntry struct {
	spMatchExpr adminModel.SPMatchExpr
	aggrCode    string
}

type matcherIdxKey string

func getMatcherIdxKey(rtdCode string, mmCode string) matcherIdxKey {
	return matcherIdxKey(fmt.Sprintf("%s::%s", rtdCode, mmCode))
}

func newAggregationRuleMatcher() *aggregationRuleMatcher {
	return &aggregationRuleMatcher{
		updateMu: sync.RWMutex{},
		aggrs:    make(map[string]*adminModel.AggregationRule),
		idx:      make(map[matcherIdxKey][]matcherIdxEntry),
	}
}

func makeMatcherIdxEntryFromAggRule(aggr *adminModel.AggregationRule) (matcherIdxKey, matcherIdxEntry) {
	return getMatcherIdxKey(aggr.RTDCode, aggr.MMCode), matcherIdxEntry{
		spMatchExpr: aggr.SPMatches,
		aggrCode:    aggr.Code,
	}
}

func (m *aggregationRuleMatcher) refreshCachedDefs(
	aggrs []*adminModel.AggregationRule,
) {
	m.updateMu.Lock()
	defer m.updateMu.Unlock()

	m.aggrs = map[string]*adminModel.AggregationRule{}
	m.idx = map[matcherIdxKey][]matcherIdxEntry{}
	for _, aggr := range aggrs {
		m.aggrs[aggr.Code] = aggr
		k, v := makeMatcherIdxEntryFromAggRule(aggr)
		m.idx[k] = append(m.idx[k], v)
	}
}

func (m *aggregationRuleMatcher) match(fs *dataplaneModel.FlattenedSeries) error {
	matches := m.allMatches(fs)
	if len(matches) == 0 {
		// TODO: 放开
		return warningNoAggregationRuleMatch
	}
	if len(matches) > 1 {
		// TODO: 引入优先级之类机制来解决那些实际没有业务问题的多重匹配
		return errMultipleAggregationRulesMatched
	}

	aggr := matches[0]
	fs.AggRCode = aggr.Code
	fs.ItemCode = aggr.DestItemCode
	fs.ZoneCode = aggr.DestZoneCode

	return nil
}

func (m *aggregationRuleMatcher) allMatches(fs *dataplaneModel.FlattenedSeries) []*adminModel.AggregationRule {
	m.updateMu.RLock()
	defer m.updateMu.RUnlock()

	k := getMatcherIdxKey(fs.RTDCode, fs.MM)
	rulesToCheck := m.idx[k]
	if len(rulesToCheck) == 0 {
		return nil
	}

	var matchedRules []*adminModel.AggregationRule
	for _, rule := range rulesToCheck {
		if rule.spMatchExpr.IsMatch(fs.MergedProps) {
			matchedRules = append(matchedRules, m.aggrs[rule.aggrCode])
		}
	}

	return matchedRules
}
