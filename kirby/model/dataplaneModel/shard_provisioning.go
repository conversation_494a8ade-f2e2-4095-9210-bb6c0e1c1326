package dataplaneModel

import (
	"time"

	"github.com/samber/lo"
	"qiniu.io/pay/kirby/model/adminModel"
)

// 预创建 shards 的处理
//
// 由于 DDL 比较耗费时间，不希望在生产环境下接到计量时需要先等待建表操作完成才能开始写入。
// 因此需要支持为给定的月份预先创建好所有可能用到的表。

func (d *KirbyDataplaneDao) ProvisionShardsForMonth(t time.Time, loc *time.Location) error {
	err := d.AggSeriesMD.EnsureShard(t, loc)
	if err != nil {
		return err
	}
	err = d.SeriesMD.EnsureShard(t, loc)
	if err != nil {
		return err
	}
	for _, s := range computeAllDataShardsForMonth(t, loc) {
		err = d.DataMgr.getShard(&s).Ensure()
		if err != nil {
			return err
		}
	}
	return nil
}

func computeAllDataShardsForMonth(t time.Time, loc *time.Location) []computedShardInfo {
	result := computeAllAggSeriesShardsForMonth(t, loc)
	result = append(result, computeAllSeriesShardsForMonth(t, loc)...)
	return result
}

func computeAllAggSeriesShardsForMonth(t time.Time, loc *time.Location) []computedShardInfo {
	return lo.Map([]adminModel.Granularity{
		adminModel.Granularity5Min,
		adminModel.GranularityDay,
		// adminModel.GranularityMonth, // 目前没有月粒度数据的 RTD
	}, func(g adminModel.Granularity, _ int) computedShardInfo {
		// NOTE: 可以把 shardIdx 写死为 0 是因为预先知道了每个月每种 G 只有一张汇总序列表
		return newDataTableShardInfo(dataTableKindAggregatedSeries, g, t, loc, 0)
	})
}

func computeAllSeriesShardsForMonth(t time.Time, loc *time.Location) []computedShardInfo {
	return lo.Flatten(lo.Map([]adminModel.Granularity{
		adminModel.Granularity5Min,
		adminModel.GranularityDay,
		// adminModel.GranularityMonth, // 目前没有月粒度数据的 RTD
	}, func(g adminModel.Granularity, _ int) []computedShardInfo {
		// NOTE: 预先知道了数据表的 shardIdx 范围是 [0x00, 0xff] 所以才能这么写
		result := make([]computedShardInfo, 0x100)
		for i := range result {
			result[i] = newDataTableShardInfo(dataTableKindSeries, g, t, loc, i)
		}
		return result
	}))
}
