package adapter

import (
	"testing"
	"time"

	pb "github.com/qbox/pay-sdk/measureproxy"
	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measureproxyd/model"
)

func TestStatSrcQueryParamsRoundTrip(t *testing.T) {
	x := &model.StatSrcQueryParams{
		UID:      233,
		Begin:    time.Date(2019, 7, 1, 0, 0, 0, 0, time.Local),
		End:      time.Date(2019, 8, 1, 0, 0, 0, 0, time.Local),
		HaveZone: true,
		ZoneCode: 3001,
		Key:      "fusion:transfer:http:ov",
		G:        pb.G_5MIN,
		Select:   []string{"123", "234"},
		Query: []model.KV{
			{K: "foo", V: "bar"},
			{K: "baz", V: "quux"},
		},
		Src:   pb.STAT_SRC_FUSION,
		Debug: true,
	}

	y, err := BuildPbStatSrcQueryParams(x)
	assert.NoError(t, err)

	z, err := BuildModelStatSrcQueryParams(y)
	assert.NoError(t, err)

	assert.Equal(t, x.UID, z.UID)
	assert.Equal(t, x.Begin.UnixNano(), z.Begin.UnixNano())
	assert.Equal(t, x.End.UnixNano(), z.End.UnixNano())
	assert.Equal(t, x.HaveZone, z.HaveZone)
	assert.Equal(t, x.ZoneCode, z.ZoneCode)
	assert.Equal(t, x.Key, z.Key)
	assert.Equal(t, x.G, z.G)
	assert.Equal(t, x.Select, z.Select)
	assert.Equal(t, x.Query, z.Query)
	assert.Equal(t, x.Src, z.Src)
	assert.Equal(t, x.Debug, z.Debug)
}
