package adapter

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measured/model"
)

func TestItemStatConfigRoundTrip(t *testing.T) {
	x := &model.ItemStatConfig{
		ID:          233,
		ItemID:      234,
		StatSrcType: 12,
	}

	y, err := BuildPbItemStatConfig(x)
	assert.NoError(t, err)

	z, err := BuildModelItemStatConfig(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.ItemID, z.ItemID)
	assert.Equal(t, x.StatSrcType, z.StatSrcType)
}
