package dataplaneModel

import (
	"context"
	"errors"
	"time"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
)

func (d *KirbyDataplaneDao) ListRelatedSeriesSince(
	epoch time.Time,
	month time.Time,
	since time.Time,
	loc *time.Location,
) ([]*SeriesDetail, error) {
	mdShard := d.SeriesMD.GetShard(month, loc)
	affectedUAs, err := mdShard.listAffectedUIDAndAggRsSince(epoch, month, since, loc)
	if err != nil {
		return nil, err
	}

	as := compactAffectedScope(affectedUAs)
	affectedMetadataRows, err := mdShard.listByAffectedScope(epoch, month, loc, &as)
	if err != nil {
		return nil, err
	}

	seriesKeysByShards := lo.GroupBy(affectedMetadataRows, func(row *seriesMetadataRow) computedShardInfo {
		return computeDataTableShard(dataTableKindSeries, row.G, month, loc, row.QRN)
	})

	type queryJob struct {
		si   computedShardInfo
		keys []string
	}

	jobs := lo.MapToSlice(seriesKeysByShards, func(k computedShardInfo, v []*seriesMetadataRow) queryJob {
		return queryJob{si: k, keys: lo.Map(v, func(x *seriesMetadataRow, _ int) string { return x.SeriesKey })}
	})

	const maxConcurrency = 8 // 随便定的比写入并发稍高的一个值
	jobResults, err := resultgroup.ThrottledParallelMap(jobs, maxConcurrency, func(param queryJob) ([]dataRowAndKey, error) {
		dataShard := d.DataMgr.getShard(&param.si)
		return dataShard.listSeriesByKeys(param.keys)
	})
	if err != nil {
		return nil, err
	}

	flattenedResults := lo.Flatten(jobResults)
	resultsBySeriesKey := lo.GroupBy(flattenedResults, func(x dataRowAndKey) string { return x.SeriesKey })

	return lo.Map(affectedMetadataRows, func(x *seriesMetadataRow, _ int) *SeriesDetail {
		// okay to have no data for this series
		data := resultsBySeriesKey[x.SeriesKey]
		sparseData, err := sparseReprFromRows(x.G, loc, lo.Map(data, func(r dataRowAndKey, _ int) dataRow { return r.intoDataRow() }))
		if err != nil {
			// only error case is when x.G is invalid
			// let's return garbage in this case because it's
			// difficult to indicate errors at this stage
			return nil
		}

		return &SeriesDetail{
			SeriesKey: x.SeriesKey,
			QRN:       x.QRN,
			MM:        x.MM,
			Props:     *x.SP,
			Start:     x.Start.In(loc),
			End:       x.End.In(loc),
			MBUUID:    x.MBUUID,
			UID:       x.UID,
			RTDCode:   x.RT,
			AggRCode:  x.AggR,
			Unit:      string(x.MeasureUnit),
			G:         x.G,
			Data:      sparseData,
			Ctime:     x.CreatedAt.In(loc),
		}
	}), nil
}

func (d *KirbyDataplaneDao) ListSeriesByConds(
	ctx context.Context,
	req *ReqListSeries,
) (*SeriesDetailList, error) {
	loc := tz.MustLocationFromCtx(ctx)

	// 暂时不支持不指定数据月份的查询
	if timeIsZero(req.DataTimeFrom) || timeIsZero(req.DataTimeTo) {
		return nil, errors.New("data time span is required")
	}
	// 暂时不给跨月查询
	dataTimeFrom := req.DataTimeFrom.In(loc)
	dataTimeTo := req.DataTimeTo.In(loc)
	dataTimeFromMonth := base.ThisMonth(dataTimeFrom)
	dataTimeToMonth := base.ThisMonth(dataTimeTo)
	if !base.NextMonth(dataTimeFromMonth).Equal(dataTimeToMonth) {
		return nil, errors.New("dataTimeFrom and dataTimeTo cover >1 months")
	}

	mdShard := d.SeriesMD.GetShard(dataTimeFromMonth, loc)

	var eg errgroup.Group

	var mdRows []*seriesMetadataRow
	eg.Go(func() error {
		var err1 error
		mdRows, err1 = mdShard.ListByConds(req, loc)
		if err1 != nil {
			return err1
		}
		return nil
	})

	var rowCount uint64
	eg.Go(func() error {
		var err1 error
		rowCount, err1 = mdShard.CountByConds(req, loc)
		if err1 != nil {
			return err1
		}
		return nil
	})

	err := eg.Wait()
	if err != nil {
		return nil, err
	}

	if len(mdRows) == 0 {
		return &SeriesDetailList{
			Data:  nil,
			Count: rowCount,
		}, nil
	}

	seriesKeysByShards := lo.GroupBy(mdRows, func(row *seriesMetadataRow) computedShardInfo {
		return computeDataTableShard(dataTableKindSeries, row.G, base.ThisMonth(row.Start.In(loc)), loc, row.QRN)
	})

	type queryJob struct {
		si   computedShardInfo
		keys []string
	}

	jobs := lo.MapToSlice(seriesKeysByShards, func(k computedShardInfo, v []*seriesMetadataRow) queryJob {
		return queryJob{si: k, keys: lo.Map(v, func(x *seriesMetadataRow, _ int) string { return x.SeriesKey })}
	})

	const maxConcurrency = 8 // 随便定的比写入并发稍高的一个值
	jobResults, err := resultgroup.ThrottledParallelMap(jobs, maxConcurrency, func(param queryJob) ([]dataRowAndKey, error) {
		dataShard := d.DataMgr.getShard(&param.si)
		return dataShard.listSeriesByKeys(param.keys)
	})
	if err != nil {
		return nil, err
	}

	flattenedResults := lo.Flatten(jobResults)
	resultsBySeriesKey := lo.GroupBy(flattenedResults, func(x dataRowAndKey) string { return x.SeriesKey })

	result := lo.Map(mdRows, func(x *seriesMetadataRow, _ int) *SeriesDetail {
		// okay to have no data for this series
		data := resultsBySeriesKey[x.SeriesKey]
		sparseData, err := sparseReprFromRows(x.G, loc, lo.Map(data, func(r dataRowAndKey, _ int) dataRow { return r.intoDataRow() }))
		if err != nil {
			// only error case is when x.G is invalid
			// let's return garbage in this case because it's
			// difficult to indicate errors at this stage
			return nil
		}

		return &SeriesDetail{
			SeriesKey: x.SeriesKey,
			QRN:       x.QRN,
			MM:        x.MM,
			Props:     *x.SP,
			Start:     x.Start.In(loc),
			End:       x.End.In(loc),
			MBUUID:    x.MBUUID,
			UID:       x.UID,
			RTDCode:   x.RT,
			AggRCode:  x.AggR,
			Unit:      string(x.MeasureUnit),
			G:         x.G,
			Data:      sparseData,
			Ctime:     x.CreatedAt.In(loc),
		}
	})

	return &SeriesDetailList{
		Data:  result,
		Count: rowCount,
	}, nil
}

func (d *KirbyDataplaneDao) ListSeriesDistinctUIDsByConds(
	ctx context.Context,
	req *ReqListSeries,
) ([]uint64, error) {
	loc := tz.MustLocationFromCtx(ctx)

	// 暂时不支持不指定数据月份的查询
	if timeIsZero(req.DataTimeFrom) || timeIsZero(req.DataTimeTo) {
		return nil, errors.New("data time span is required")
	}
	// 暂时不给跨月查询
	dataTimeFrom := req.DataTimeFrom.In(loc)
	dataTimeTo := req.DataTimeTo.In(loc)
	dataTimeFromMonth := base.ThisMonth(dataTimeFrom)
	dataTimeToMonth := base.ThisMonth(dataTimeTo)
	if !base.NextMonth(dataTimeFromMonth).Equal(dataTimeToMonth) {
		return nil, errors.New("dataTimeFrom and dataTimeTo cover >1 months")
	}

	mdShard := d.SeriesMD.GetShard(dataTimeFromMonth, loc)
	return mdShard.ListDistinctUIDsByConds(req, loc)
}
