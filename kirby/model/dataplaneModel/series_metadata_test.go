package dataplaneModel

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/kirby/model/adminModel"
)

func TestSeriesMetadataSharding(t *testing.T) {
	test.RunWithUTCAndCST(t, testSeriesMetadataSharding)
}

func testSeriesMetadataSharding(t *testing.T, loc *time.Location) {
	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	dao := NewSeriesMetadataDao(testWrap.BaseDao())

	testRow := seriesMetadataRow{
		QRN: "qrn:kodo:z0:1234567890:bucket/testtest",
		MM:  "space",
		SP: &SP{
			"$qrnRegion":   "z0",
			"$qrnResource": "bucket/testtest",
			"spaceKind":    "storage",
			"ftype":        "archive",
		},
		Start:       time.Date(2022, 11, 3, 0, 0, 0, 0, loc),
		End:         time.Date(2022, 11, 4, 0, 0, 0, 0, loc),
		MBUUID:      "QKMB01GN9FRGYVJFEK8NWPZPC87QBZ",
		UID:         1234567890,
		SPHash:      0, // will be filled by Save()
		RT:          "qrtd:kodo:bucket:v202211",
		AggR:        "QKARfoo",
		MeasureUnit: "B",
		G:           adminModel.GranularityDay,
		SeriesKey:   "QKSK01GN9FXSQEFM8VFQKM39779H8N",
		CreatedAt:   time.Date(2022, 11, 4, 0, 30, 31, 323334000, loc),
	}

	err = dao.EnsureShard(testRow.Start, loc)
	assert.NoError(t, err)

	// repeated ensure ops shouldn't error out
	err = dao.EnsureShard(testRow.Start, loc)
	assert.NoError(t, err)

	err = dao.GetShard(testRow.Start, loc).Save(&testRow)
	assert.NoError(t, err)
	assert.Equal(t, uint32(0x38929fa0), testRow.SPHash)

	l, err := dao.GetShard(testRow.Start, loc).ListByMonthUIDRTMMAndSPHashes(
		time.Date(2022, 11, 4, 1, 30, 0, 0, loc),
		time.Date(2022, 11, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"qrtd:kodo:bucket:v202211",
		"space",
		[]uint32{0x38929fa0, 0x123},
	)
	assert.NoError(t, err)
	assert.Len(t, l, 1)

	// query with an epoch before creation of the row
	l, err = dao.GetShard(time.Date(2022, 11, 1, 0, 0, 0, 0, loc), loc).ListByMonthUIDRTMMAndSPHashes(
		time.Date(2022, 11, 2, 1, 30, 0, 0, loc),
		time.Date(2022, 11, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"qrtd:kodo:bucket:v202211",
		"space",
		[]uint32{0x38929fa0, 0x123},
	)
	assert.NoError(t, err)
	assert.Empty(t, l)

	// try to query a non-existent past shard
	l, err = dao.GetShard(time.Date(2022, 10, 1, 0, 0, 0, 0, loc), loc).ListByMonthUIDRTMMAndSPHashes(
		time.Date(2022, 11, 10, 0, 0, 0, 0, loc),
		time.Date(2022, 10, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"qrtd:kodo:bucket:v202211",
		"space",
		[]uint32{0x38929fa0, 0x123},
	)
	assert.NoError(t, err)
	assert.Empty(t, l)

	// try to query a non-existent future shard
	l, err = dao.GetShard(time.Date(2023, 10, 1, 0, 0, 0, 0, loc), loc).ListByMonthUIDRTMMAndSPHashes(
		time.Date(2022, 11, 10, 0, 0, 0, 0, loc),
		time.Date(2023, 10, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"qrtd:kodo:bucket:v202211",
		"space",
		[]uint32{0x38929fa0, 0x123},
	)
	assert.NoError(t, err)
	assert.Empty(t, l)
	// TODO: assert that no DB query has taken place at all
}
