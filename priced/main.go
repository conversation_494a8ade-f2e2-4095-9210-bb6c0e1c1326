package main

import (
	"context"
	"flag"

	"github.com/grpc-ecosystem/grpc-gateway/runtime"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/qiniu/version/v2"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"github.com/qbox/bo-base/v4/cli"
	"github.com/qbox/bo-base/v4/dao"
	hook "github.com/qbox/bo-base/v4/errors/logrus"
	"github.com/qbox/bo-base/v4/eventbus"
	"github.com/qbox/bo-base/v4/intl"
	baselog "github.com/qbox/bo-base/v4/log"
	"github.com/qbox/bo-base/v4/rpc"
	dictpb "github.com/qbox/pay-sdk/dict"
	pb "github.com/qbox/pay-sdk/price"
	qvmpricepb "github.com/qbox/pay-sdk/qvmprice"
	walletpb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/priced/action"
	pricedCfg "qiniu.io/pay/priced/config"
	"qiniu.io/pay/priced/i18n"
	"qiniu.io/pay/priced/model"
	"qiniu.io/pay/priced/service"
	qvmAction "qiniu.io/pay/qvmprice/action"
	qvmModel "qiniu.io/pay/qvmprice/model"
	qvmService "qiniu.io/pay/qvmprice/service"
)

func main() {
	var confPath string
	var enableHTTPPprof bool
	var runMigration bool
	flag.StringVar(&confPath, "conf", "priced.yml", "config file path")
	flag.BoolVar(&enableHTTPPprof, "pprof", false, "enable net/http/pprof under /debug/pprof paths")
	flag.BoolVar(&runMigration, "automigrate", false, "enable auto migration of db schema")
	_ = flag.Bool("version", false, "print version info and exit")
	flag.Parse()
	cli.InitFlagMap()

	if cli.IsFlagProvided("version") {
		version.Print()
		return
	}

	conf, err := pricedCfg.LoadPricedConfig(confPath)
	if err != nil {
		log.WithField("err", err).Fatal("failed to load config")
	}

	_, err = intl.Init(&conf.Intl, i18n.L10nFS, i18n.RelativePath)
	if err != nil {
		log.WithError(err).Fatal("failed to init l10n mechanism")
		return
	}

	// command-line --pprof switch has higher priority over config settings
	if cli.IsFlagProvided("pprof") {
		log.WithFields(log.Fields{
			"configValue": conf.RPC.EnablePprof,
			"cliValue":    enableHTTPPprof,
		}).Info("overriding pprof option with command-line flag")
		conf.RPC.EnablePprof = enableHTTPPprof
	}

	log.AddHook(hook.NewHook(hook.WithKeys("reqid")))
	log.SetFormatter(baselog.NewFlattenJSONFormatter())
	loggerEntry := rpc.NewLoggerEntry(log.StandardLogger())
	rpc.InitLogging(loggerEntry)

	baseDao, err := dao.InitMysqlDao(&conf.MySQL, &conf.Cache)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init dao layer")
	}
	bus, err := eventbus.NewRabbitMQEventBus(&conf.Eventbus, nil)
	if err != nil {
		log.Error("initialize eventbus failed:", err)
		return
	}

	if runMigration {
		model.RegisterMigrate(baseDao.DB)
		qvmModel.RegisterMigrate(baseDao.DB)
	}

	dictConn, err := rpc.GrpcConnectWithName(conf.Services.Dict, rpc.ServicePayV4Dict, conf.RPC.Keepalive.Client)
	if err != nil {
		log.WithField("err", err).Fatal("failed to connect to dict server")
	}
	dictClient := dictpb.NewPayDictServiceClient(dictConn)

	walletV4Conn, err := rpc.GrpcConnectWithName(conf.Services.WalletV4, rpc.ServicePayV4Wallet, conf.RPC.Keepalive.Client)
	if err != nil {
		log.WithField("err", err).Fatal("failed to connect to wallet v4 server")
	}
	paymentSrvClient := walletpb.NewPaymentServiceClient(walletV4Conn)

	pricedao := model.NewPriceDao(baseDao)
	priceService, err := service.NewPriceBizService(
		conf,
		dictClient,
		paymentSrvClient,
		pricedao,
		conf.Cache.DefaultExpires,
		bus,
	)
	if err != nil {
		log.WithError(err).Fatal("init price service failed")
		return
	}

	priceAction := action.NewPriceAction(priceService, conf.Price.DefaultPageSize)

	qvmPriceDao := qvmModel.NewQVMPriceDao(baseDao)
	qvmPriceService := qvmService.NewQVMPriceService(qvmPriceDao, dictClient, paymentSrvClient)
	qvmPriceAction := qvmAction.NewQVMPriceAction(conf.Price.DefaultPageSize, qvmPriceService)

	if err := rpc.Serve(
		&conf.RPC,
		loggerEntry,
		func(s *grpc.Server) {
			pb.RegisterPayPriceServiceServer(s, priceAction)
			qvmpricepb.RegisterQVMPriceServiceServer(s, qvmPriceAction)
		},
		func(ctx context.Context, sm *runtime.ServeMux, s string, do []grpc.DialOption) error {
			err := pb.RegisterPayPriceServiceHandlerFromEndpoint(ctx, sm, s, do)
			if err != nil {
				return err
			}

			err = qvmpricepb.RegisterQVMPriceServiceHandlerFromEndpoint(ctx, sm, s, do)
			if err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		log.WithField("err", err).Fatal("failed to serve")
	}
}
