// Code generated by MockGen. DO NOT EDIT.
// Source: creditd/model/notification_model.go

package model

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockNotificationToInfo is a mock of NotificationToInfo interface.
type MockNotificationToInfo struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationToInfoMockRecorder
}

// MockNotificationToInfoMockRecorder is the mock recorder for MockNotificationToInfo.
type MockNotificationToInfoMockRecorder struct {
	mock *MockNotificationToInfo
}

// NewMockNotificationToInfo creates a new mock instance.
func NewMockNotificationToInfo(ctrl *gomock.Controller) *MockNotificationToInfo {
	mock := &MockNotificationToInfo{ctrl: ctrl}
	mock.recorder = &MockNotificationToInfoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationToInfo) EXPECT() *MockNotificationToInfoMockRecorder {
	return m.recorder
}

// GetCompanyName mocks base method.
func (m *MockNotificationToInfo) GetCompanyName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCompanyName")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetCompanyName indicates an expected call of GetCompanyName.
func (mr *MockNotificationToInfoMockRecorder) GetCompanyName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCompanyName", reflect.TypeOf((*MockNotificationToInfo)(nil).GetCompanyName))
}

// GetEmail mocks base method.
func (m *MockNotificationToInfo) GetEmail() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmail")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetEmail indicates an expected call of GetEmail.
func (mr *MockNotificationToInfoMockRecorder) GetEmail() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmail", reflect.TypeOf((*MockNotificationToInfo)(nil).GetEmail))
}

// GetPhoneNumber mocks base method.
func (m *MockNotificationToInfo) GetPhoneNumber() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPhoneNumber")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetPhoneNumber indicates an expected call of GetPhoneNumber.
func (mr *MockNotificationToInfoMockRecorder) GetPhoneNumber() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPhoneNumber", reflect.TypeOf((*MockNotificationToInfo)(nil).GetPhoneNumber))
}
