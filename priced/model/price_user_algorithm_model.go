package model

import (
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"

	"github.com/qbox/bo-base/v4/base"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// ErrGenSQLFailure means failure of generating SQL
var ErrGenSQLFailure = errors.New("generate sql error")

// PriceUserAlgorithm model definition
// 用户针对特定计费项的公开价，采用的计费方式
// price_item 表设计为 （item_id, zone_id）<--1:N--> (algorithm_id)
// 即同一个 item_id, zone_id 可以配置对应多个 algorithm_id / price_item_id
// uid 在同一时段只能选择其中一个
type PriceUserAlgorithm struct {
	ID  uint64 `gorm:"primary_key"`
	UID uint64 `gorm:"uid;not null;default:0"`
	// ItemID 计费项 id
	ItemID uint64 `gorm:"item_id;not null;default:0"`
	// ZoneID 机房区域 id
	ZoneID uint64 `gorm:"zone_id;not null;default:0;comment:'计费区域 id'"`
	// AlgorithmID 用户选择的计费方式 id
	AlgorithmID uint64 `gorm:"algorithm_id;not null;default:0;comment:'用户采用的计费方式 id'"`
	// AlgorithmCode 用户选择的计费方式 code
	AlgorithmCode string `gorm:"-"`
	// EffectTime 生效时间
	EffectTime base.HNS `gorm:"not null;default:0;comment:'生效时间'"`
	// DeadTime 失效时间
	DeadTime base.HNS `gorm:"not null;default:0;comment:'失效时间'"`
	// IsDisabled 该条是否禁用
	IsDisabled  bool      `gorm:"not null;default:0;comment:'该条是否禁用'"`
	Description string    `gorm:"type:varchar(255);not null;default:''"`
	CreatedAt   time.Time `gorm:"type:DATETIME(6);not null;default:'1000-01-01 00:00:00.000000'"`
	UpdatedAt   time.Time `gorm:"type:DATETIME(6);not null;default:'1000-01-01 00:00:00.000000'"`
}

// PriceUserAlgorithmDao is data access object of PriceUserAlgorithm model
type PriceUserAlgorithmDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

func (d *PriceUserAlgorithmDao) GetCachePrefix() string {
	return "price:price_user_algorithm:"
}

func (d *PriceUserAlgorithmDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewPriceUserAlgorithmDao is constructor of PriceUserAlgorithmDao
func NewPriceUserAlgorithmDao(base *dao.BaseDao) *PriceUserAlgorithmDao {
	cachePrefix := (*PriceUserAlgorithmDao)(nil).GetCachePrefix()
	return &PriceUserAlgorithmDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id:{ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// DoTransaction do a transaction
func (d *PriceUserAlgorithmDao) DoTransaction(fn func(*PriceUserAlgorithmDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		priceUserAlgorithmDao := NewPriceUserAlgorithmDao(base)
		return fn(priceUserAlgorithmDao)
	})
}

// GetByID select a record of PriceUserAlgorithm by id
func (d *PriceUserAlgorithmDao) GetByID(id uint64, expires ...time.Duration) (*PriceUserAlgorithm, error) {
	model := &PriceUserAlgorithm{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, err
}

// Save insert or update a record of PriceUserAlgorithm by id
func (d *PriceUserAlgorithmDao) Save(model *PriceUserAlgorithm, expires ...time.Duration) error {
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}

	return nil
}

// Delete delete a record of PriceUserAlgorithm
func (d *PriceUserAlgorithmDao) Delete(model *PriceUserAlgorithm) error {
	if model.ID == 0 {
		return errors.New("invalid args")
	}
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// List select records of PriceUserAlgorithm
func (d *PriceUserAlgorithmDao) List(offset, limit int, expires ...time.Duration) (priceUserAlgorithms []PriceUserAlgorithm, err error) {
	err = d.base.Offset(offset).Limit(limit).Find(&priceUserAlgorithms).Error
	if err != nil {
		return nil, errors.Trace(err)
	}
	return
}

// Count select count of PriceUserAlgorithm
func (d *PriceUserAlgorithmDao) Count(expires ...time.Duration) (count uint64, err error) {
	err = d.base.Model(&PriceUserAlgorithm{}).Count(&count).Error
	if err != nil {
		return 0, errors.Trace(err)
	}
	return
}

// ListByUID select records of PriceUserAlgorithm by uid
func (d *PriceUserAlgorithmDao) ListByUID(uid uint64, offset, limit int, expires ...time.Duration) (priceUserAlgorithms []PriceUserAlgorithm, err error) {
	err = d.base.Where(&PriceUserAlgorithm{UID: uid}).Offset(offset).Limit(limit).Find(&priceUserAlgorithms).Error
	if err != nil {
		return nil, errors.Trace(err)
	}
	return
}

// CountByUID select count of PriceUserAlgorithm by uid
func (d *PriceUserAlgorithmDao) CountByUID(uid uint64, expires ...time.Duration) (count uint64, err error) {
	err = d.base.Model(&PriceUserAlgorithm{}).Where(&PriceUserAlgorithm{UID: uid}).Count(&count).Error
	if err != nil {
		return 0, errors.Trace(err).WithField("uid", uid)
	}
	return
}

// ListByCondsParam 条件查询参数
type ListByCondsParam struct {
	UID       uint64
	ItemIDs   []uint64
	ZoneID    uint64
	StartTime base.HNS
	EndTime   base.HNS
}

func (p *ListByCondsParam) toWhereClause() (conditionStr string, args []any, err error) {
	andCond := squirrel.And{}

	andCond = append(andCond, squirrel.Eq{
		"uid":     p.UID,
		"item_id": p.ItemIDs,
		"zone_id": p.ZoneID,
	})

	// effect_time <= param.EndTime && dead_time > param.StartTime
	if p.StartTime != 0 {
		andCond = append(andCond, squirrel.LtOrEq{
			"effect_time": p.EndTime,
		})
	}

	if p.EndTime != 0 {
		andCond = append(andCond, squirrel.Gt{
			"dead_time": p.StartTime,
		})
	}

	return andCond.ToSql()
}

// ListByConds list PriceUserAlgorithm by given conditions
func (d *PriceUserAlgorithmDao) ListByConds(param *ListByCondsParam) (priceUserAlgorithms []*PriceUserAlgorithm, err error) {
	conditionStr, args, err := param.toWhereClause()
	if err != nil {
		return nil, ErrGenSQLFailure
	}
	err = d.base.Model(&PriceUserAlgorithm{}).
		Where(conditionStr, args...).
		Order("effect_time").
		Find(&priceUserAlgorithms).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithField("param", param)
	}
	return
}

// GetLastByCondsParam  获取该用户该计费项最后一条配置查询参数
type GetLastByCondsParam struct {
	UID    uint64
	ItemID uint64
	ZoneID uint64
}

func (p *GetLastByCondsParam) toWhereClause() (conditionStr string, args []any, err error) {
	andCond := squirrel.And{}
	andCond = append(andCond, squirrel.Eq{
		"uid":     p.UID,
		"item_id": p.ItemID,
		"zone_id": p.ZoneID,
	})

	return andCond.ToSql()
}

// GetLastByConds get last inserted PriceUserAlgorithm
func (d *PriceUserAlgorithmDao) GetLastByConds(param *GetLastByCondsParam) (priceUserAlgorithm *PriceUserAlgorithm, err error) {
	conditionStr, args, err := param.toWhereClause()
	if err != nil {
		return nil, ErrGenSQLFailure
	}

	priceUserAlgorithm = &PriceUserAlgorithm{}
	err = d.base.Model(&PriceUserAlgorithm{}).
		Where(conditionStr, args...).
		Order("created_at DESC").
		First(priceUserAlgorithm).
		Error
	if err != nil {
		return nil, err
	}
	return
}

// UpdateGivenFields 根据 id 更新指定字段值
func (d *PriceUserAlgorithmDao) UpdateGivenFields(
	id uint64,
	updateFieldMap map[string]any) error {
	if id == 0 {
		return errors.New("invalid parameter")
	}

	priceUserAlgorithm := &PriceUserAlgorithm{
		ID: id,
	}

	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Model(value).Update(updateFieldMap).Error
	}, d.keys, priceUserAlgorithm, d)
	if err != nil {
		return err
	}

	return nil
}
