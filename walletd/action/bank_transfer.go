package action

import (
	"context"

	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/action/adapter"
)

// ListBankTransfer 查询银行转账记录列表
func (a *WalletAction) ListBankTransfer(
	ctx context.Context,
	param *pb.BankTransferListParam,
) (*pb.BankTransferListResp, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"param": param,
	}).Info("ListBankTransfer called")

	// 使用 adapter 转换参数
	query := adapter.BuildBankTransferQuery(param)

	// 处理分页参数
	offset, limit := a.Paging(param)

	// 查询记录
	transfers, total, err := a.bankTransferSrv.ListBankTransfer(
		ctx, query, offset, limit,
	)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"query":  query,
			"offset": offset,
			"limit":  limit,
		}).Error("ListBankTransfer failed")
		return nil, err
	}

	return adapter.BuildPbBankTransferListResp(transfers, total), nil
}

// ApproveBankTransfer 审批通过银行转账申请
//
// 该方法用于审批通过一个银行转账申请。
// 参数 param 包含转账记录的序列号(SN)。
// 操作成功返回空响应。
func (a *WalletAction) ApproveBankTransfer(
	ctx context.Context,
	param *pb.SNParam,
) (*emptypb.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"sn": param.Sn,
	}).Info("ApproveBankTransfer called")

	err := a.bankTransferSrv.ApproveBankTransfer(ctx, param.Sn)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"sn": param.Sn,
		}).Error("ApproveBankTransfer failed")
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

// InvalidateBankTransfer 作废银行转账申请
//
// 该方法用于将一个银行转账申请标记为无效或作废。
// 参数 param 包含转账记录的序列号(SN)。
// 操作成功返回空响应。
func (a *WalletAction) InvalidateBankTransfer(
	ctx context.Context,
	param *pb.SNParam,
) (*emptypb.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"sn": param.Sn,
	}).Info("InvalidateBankTransfer called")

	err := a.bankTransferSrv.InvalidateBankTransfer(ctx, param.Sn)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"sn": param.Sn,
		}).Error("InvalidateBankTransfer failed")
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

// GetBankTransfer 获取银行转账记录详情
//
// 该方法用于获取指定序列号的银行转账记录的详细信息。
// 参数 param 包含转账记录的序列号(SN)。
// 返回指定转账记录的详细信息。
func (a *WalletAction) GetBankTransfer(
	ctx context.Context,
	param *pb.SNParam) (*pb.BankTransfer, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"sn": param.Sn,
	}).Info("GetBankTransfer called")

	// 调用 service 层方法
	transfer, err := a.bankTransferSrv.GetBankTransfer(ctx, param.Sn)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"sn": param.Sn,
		}).Error("GetBankTransfer failed")
		return nil, err
	}

	// 使用 adapter 转换为 pb 对象
	return adapter.BuildPbBankTransfer(transfer), nil
}

// RechargeBankTransfer 执行银行转账充值操作
//
// 该方法用于执行已审批通过的银行转账充值操作。
// 参数 param 包含转账记录的序列号(SN)。
// 操作成功返回空响应。
func (a *WalletAction) RechargeBankTransfer(
	ctx context.Context,
	param *pb.SNParam,
) (*emptypb.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"sn": param.Sn,
	}).Info("RechargeBankTransfer called")

	err := a.bankTransferSrv.RechargeBankTransfer(ctx, param.Sn)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"sn": param.Sn,
		}).Error("RechargeBankTransfer failed")
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

// RevokeBankTransfer 撤销及重新分配银行转账申请
//
// 该方法用于撤销一个银行转账申请。
// 参数 param 包含转账记录的序列号(SN)。
// 操作成功返回空响应。
func (a *WalletAction) RevokeBankTransfer(
	ctx context.Context,
	param *pb.SNParam,
) (*emptypb.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"sn": param.Sn,
	}).Info("RevokeBankTransfer called")

	err := a.bankTransferSrv.RevokeBankTransfer(ctx, param.Sn)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"sn": param.Sn,
		}).Error("RevokeBankTransfer failed")
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

// SearchPaymentAccount 搜索支付账户信息
//
// 该方法用于根据提供的搜索条件查询支付账户信息。
// 参数 param 包含搜索条件。
// 返回符合条件的支付账户列表。
func (a *WalletAction) SearchPaymentAccount(
	ctx context.Context,
	param *pb.PaymentAccountSearchParam,
) (*pb.PaymentAccountList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"payment_account": param.PaymentAccount,
	}).Info("SearchPaymentAccount called")

	// 调用 service 层方法
	accounts, err := a.bankTransferSrv.SearchPaymentAccount(
		ctx, param.PaymentAccount)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"payment_account": param.PaymentAccount,
		}).Error("SearchPaymentAccount failed")
		return nil, err
	}

	// 使用 adapter 转换为 pb 对象
	return adapter.BuildPbPaymentAccountList(accounts), nil
}

// CreateBankTransfer 创建银行转账记录
//
// 该方法用于创建新的银行转账记录。
// 参数 transfer 包含转账记录的详细信息。
// 操作成功返回空响应。
func (a *WalletAction) CreateBankTransfer(
	ctx context.Context,
	transfer *pb.BankTransfer,
) (*emptypb.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"transfer": transfer,
	}).Info("CreateBankTransfer called")

	// 使用 adapter 转换为 model 对象
	modelTransfer, err := adapter.BuildModelBankTransfer(transfer)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"transfer": transfer,
		}).Error("BuildModelBankTransfer failed")
		return nil, err
	}

	// 调用 service 层方法
	err = a.bankTransferSrv.CreateBankTransfer(ctx, modelTransfer)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"transfer": modelTransfer,
		}).Error("CreateBankTransfer failed")
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// EditBankTransfer 编辑银行转账记录
//
// 该方法用于编辑现有的银行转账记录。
// 参数 transfer 包含转账记录的更新信息。
// 操作成功返回空响应。
func (a *WalletAction) EditBankTransfer(
	ctx context.Context,
	transfer *pb.BankTransfer,
) (*emptypb.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"transfer": transfer,
	}).Info("EditBankTransfer called")

	// 检查序列号是否存在
	if transfer.GetSn() == "" {
		err := errors.New("bank transfer SN is required")
		logger.WithError(err).WithFields(logrus.Fields{
			"transfer": transfer,
		}).Error("EditBankTransfer failed: missing SN")
		return nil, err
	}

	// 使用 adapter 转换为 model 对象
	modelTransfer, err := adapter.BuildModelBankTransfer(transfer)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"transfer": transfer,
		}).Error("BuildModelBankTransfer failed")
		return nil, err
	}

	// 调用 service 层方法
	err = a.bankTransferSrv.EditBankTransfer(ctx, modelTransfer)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"transfer": modelTransfer,
		}).Error("EditBankTransfer failed")
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// ReturnBankTransfer 银行转账记录退回
//
// 该方法用于退回一个银行转账记录。
// 参数 param 包含转账记录的序列号(SN)。
// 操作成功返回空响应。
func (a *WalletAction) ReturnBankTransfer(
	ctx context.Context,
	param *pb.SNParam,
) (*emptypb.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"sn": param.Sn,
	}).Info("ReturnBankTransfer called")

	// 调用 service 层方法
	err := a.bankTransferSrv.ReturnBankTransfer(ctx, param.Sn)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"sn": param.Sn,
		}).Error("ReturnBankTransfer failed")
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
