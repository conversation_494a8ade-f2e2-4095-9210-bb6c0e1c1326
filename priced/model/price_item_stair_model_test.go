package model_test

import (
	"testing"

	"github.com/qbox/bo-base/v4/base"
	basedao "github.com/qbox/bo-base/v4/dao"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
)

func TestModelPriceItemStairCRUD(t *testing.T) {
	sandbox := buildSandbox(t)

	dao := sandbox.priceDao.PriceItemStair

	stairs := []model.PriceItemStair{
		{
			PriceItemID: 1,
			UnitID:      1,
			Quantity:    1,
			Price:       base.NewNMoneyWithHighAccuracyI64(100),
			Order:       0,
		},
		{
			PriceItemID: 1,
			UnitID:      2,
			Quantity:    2,
			Price:       base.NewNMoneyWithHighAccuracyI64(200),
			Order:       0,
		},
		{
			PriceItemID: 3,
			UnitID:      3,
			Quantity:    3,
			Price:       base.NewNMoneyWithHighAccuracyI64(300),
			Order:       0,
		},
	}

	assertFields := func(expect, actual *model.PriceItemStair, msgAndArgs ...any) {
		assert.Equal(t, expect.PriceItemID, actual.PriceItemID, msgAndArgs...)
		assert.Equal(t, expect.UnitID, actual.UnitID, msgAndArgs...)
		assert.Equal(t, expect.Quantity, actual.Quantity, msgAndArgs...)
		assert.Equal(t, expect.Price, actual.Price, msgAndArgs...)
		assert.Equal(t, expect.Order, actual.Order, msgAndArgs...)
	}

	for i, stair := range stairs {
		// create
		err := dao.Save(&stair)
		if assert.NoError(t, err) {
			assert.NotZero(t, stair.ID, "CreatePriceItemStair return zero id")
		}
		stairs[i].ID = stair.ID

		// query
		stairID := stair.ID
		m, err := dao.GetByID(stairID)
		if assert.NoError(t, err) {
			assertFields(&stair, m, "Unmatched stair")
		}
	}

	// list all price item stairs
	stairList, err := dao.List(0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, stairList, len(stairs))
	}

	// list all price item stair by price_item_id
	stairWithSamePriceItemID, err := dao.ListByPriceItemID(1, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, stairWithSamePriceItemID, 2)
		for _, stair := range stairWithSamePriceItemID {
			for _, expectedStair := range stairs {
				if stair.ID == expectedStair.ID {
					assertFields(&expectedStair, &stair)
					break
				}
			}
		}
	}

	// list all price item stair by price_item_ids
	allStairs, err := dao.BatchListByPriceItemIDs([]uint64{1, 2, 3})
	if assert.NoError(t, err) {
		assert.Len(t, allStairs, 3)
		for _, stair := range allStairs {
			for _, expectedStair := range stairs {
				if stair.ID == expectedStair.ID {
					assertFields(&expectedStair, &stair)
					break
				}
			}
		}
	}

	// delete all price items
	for _, stair := range stairs {
		err := dao.DeleteByID(stair.ID)
		assert.NoError(t, err)
	}

	// check price item list, should be empty
	l, err := dao.List(0, 10, basedao.CacheExpiresNoCache)
	if assert.NoError(t, err) {
		assert.Empty(t, l)
	}

	// de-duplication
	for _, stair := range stairs {
		err := dao.DeleteByID(stair.ID)
		assert.Error(t, err)
	}
}
