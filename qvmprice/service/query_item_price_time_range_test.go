package service

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"

	"qiniu.io/pay/qvmprice/model"
)

func TestCalculateItemPricesWithTimeRangePublicPriceOnly(t *testing.T) {
	//             2001.01.01                                        2200.01.01
	// 2019.01.10: └───────────────────────────────────────────────A─┘
	//                        2019.01.01
	// 2022.03.10:            └────────────────────────────────────B─┘
	//                          2022.03.01
	// 2022.03.28:                └────────────────────────────────C─┘
	//                                   2022.05.01
	// 2022.04.10:                       └─────────────────────────D─┘
	//
	// 查询: 当前时刻为 2022.04.03，查询 2018.04.01 到 2022.04.01
	//                   2018-04     2022-04
	// 2022.04.03:       └───────────┘

	loc := time.UTC

	epoch := time.Date(2022, 4, 3, 12, 34, 56, 0, loc)
	t20010101 := time.Date(2001, 1, 1, 0, 0, 0, 0, loc)
	t20180401 := time.Date(2018, 4, 1, 0, 0, 0, 0, loc)
	t20190101 := time.Date(2019, 1, 1, 0, 0, 0, 0, loc)
	t20190110 := time.Date(2019, 1, 10, 0, 0, 0, 0, loc)
	t20220301 := time.Date(2022, 3, 1, 0, 0, 0, 0, loc)
	t20220310 := time.Date(2022, 3, 10, 0, 0, 0, 0, loc)
	t20220328 := time.Date(2022, 3, 28, 0, 0, 0, 0, loc)
	t20220401 := time.Date(2022, 4, 1, 0, 0, 0, 0, loc)
	t20220410 := time.Date(2022, 4, 10, 0, 0, 0, 0, loc)
	t20220501 := time.Date(2022, 5, 1, 0, 0, 0, 0, loc)
	t22000101 := time.Date(2200, 1, 1, 0, 0, 0, 0, loc)

	publicPriceRecords := &fetchedItemPriceRecords{
		priceItems: []*model.QVMPriceItem{
			// A
			{
				ID:              1234,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20010101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20190110,
				UpdatedAt:       t20190110,
			},
			// A 干扰项（item 不同）
			{
				ID:              1235,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20010101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20190110,
				UpdatedAt:       t20190110,
			},
			// A 干扰项（zone 不同）
			{
				ID:              1236,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20010101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20190110,
				UpdatedAt:       t20190110,
			},
			// B
			{
				ID:              2345,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20190101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220310,
				UpdatedAt:       t20220310,
			},
			// B 干扰项（item 不同）
			{
				ID:              2346,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20190101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220310,
				UpdatedAt:       t20220310,
			},
			// B 干扰项（zone 不同）
			{
				ID:              2347,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20190101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220310,
				UpdatedAt:       t20220310,
			},
			// C
			{
				ID:              3456,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20220301),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220328,
				UpdatedAt:       t20220328,
			},
			// C 干扰项（币种不同）
			{
				ID:              3457,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "USD",
				EffectTime:      base.NewHNS(t20220301),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220328,
				UpdatedAt:       t20220328,
			},
			// D
			{
				ID:              4567,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20220501),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220410,
				UpdatedAt:       t20220410,
			},
		},
		priceItemStairs: []*model.QVMPriceItemStair{
			{ID: 11111, QVMPriceItemID: 1234, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(66)},
			{ID: 11112, QVMPriceItemID: 1235, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(77)},
			{ID: 11113, QVMPriceItemID: 1236, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(88)},
			{ID: 22222, QVMPriceItemID: 2345, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(666)},
			{ID: 22223, QVMPriceItemID: 2346, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(777)},
			{ID: 22224, QVMPriceItemID: 2347, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(888)},
			{ID: 33333, QVMPriceItemID: 3456, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(6666)},
			{ID: 33334, QVMPriceItemID: 3457, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(7777)},
			{ID: 33335, QVMPriceItemID: 4567, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(66666)},
		},
	}

	expected := &ItemPricesResp{
		ItemCode: "qvm:ip:233m",
		ZoneCode: 4001,
		Prices: []*QVMPriceItemDetail{
			{
				UID:            233,
				ItemCode:       "qvm:ip:233m",
				ZoneCode:       4001,
				StairPriceType: "UNITPRICE",
				CurrencyType:   "CNY",
				Stairs: []*QVMItemPriceStairDetail{
					{Quantity: 0, Price: base.NewNMoney(66)},
				},
				EffectTime: t20180401,
				DeadTime:   t20190101,
				CreatedAt:  t20190110,
				IsVIP:      false,
				VIPOp:      "",
			},
			{
				UID:            233,
				ItemCode:       "qvm:ip:233m",
				ZoneCode:       4001,
				StairPriceType: "UNITPRICE",
				CurrencyType:   "CNY",
				Stairs: []*QVMItemPriceStairDetail{
					{Quantity: 0, Price: base.NewNMoney(666)},
				},
				EffectTime: t20190101,
				DeadTime:   t20220301,
				CreatedAt:  t20220310,
				IsVIP:      false,
				VIPOp:      "",
			},
			{
				UID:            233,
				ItemCode:       "qvm:ip:233m",
				ZoneCode:       4001,
				StairPriceType: "UNITPRICE",
				CurrencyType:   "CNY",
				Stairs: []*QVMItemPriceStairDetail{
					{Quantity: 0, Price: base.NewNMoney(6666)},
				},
				EffectTime: t20220301,
				DeadTime:   t20220401,
				CreatedAt:  t20220328,
				IsVIP:      false,
				VIPOp:      "",
			},
		},
	}

	actual, err := calculateItemPricesWithTimeRange(
		233,
		"qvm:ip:233m",
		"qvm:ip",
		4001,
		"CNY",
		epoch,
		base.TimeRange{
			Start: t20180401,
			End:   t20220401,
		},
		&fetchedItemPriceRecords{},
		publicPriceRecords,
	)
	assert.NoError(t, err)
	assert.Equal(t, expected, actual)
}

func TestCalculateItemPricesWithTimeRangePublicPriceWithDiscount(t *testing.T) {
	//             2001.01.01                                        2200.01.01
	// 2019.01.10: └───────────────────────────────────────────────A─┘
	//                        2019.01.01
	// 2022.03.10:            └────────────────────────────────────B─┘
	//                          2022.03.01
	// 2022.03.28:                └────────────────────────────────C─┘
	//                                   2022.05.01
	// 2022.04.10:                       └─────────────────────────D─┘
	//
	// 按组改价操作:
	//                          2022.02.01   2022.06.01
	// 2022.02.28:              └──────────E─┘
	//
	// 查询 A: 当前时刻为 2022.04.03，查询 2018.04.01 到 2022.04.01
	//                   2018-04     2022-04
	// 2022.04.03:       └───────────┘
	//
	// 预期:
	//
	// 1. 2018-04 - 2019-01 A，不打折
	// 2. 2019-01 - 2022-02 B，不打折
	// 3. 2022-02 - 2022-04 A，按 E 打折
	//
	// 查询 B: 当前时刻为 2022.07.03，查询 2022.03.01 到 2022.07.01
	//                            2022-03        2022-07
	// 2022.07.03:                └──────────────┘
	//
	// 预期:
	//
	// 1. 2022-03 - 2022-06 A，按 E 打折
	// 2. 2022-06 - 2022-07 D，不打折

	loc := time.UTC

	t20010101 := time.Date(2001, 1, 1, 0, 0, 0, 0, loc)
	t20180401 := time.Date(2018, 4, 1, 0, 0, 0, 0, loc)
	t20190101 := time.Date(2019, 1, 1, 0, 0, 0, 0, loc)
	t20190110 := time.Date(2019, 1, 10, 0, 0, 0, 0, loc)
	t20220201 := time.Date(2022, 2, 1, 0, 0, 0, 0, loc)
	t20220228 := time.Date(2022, 2, 28, 0, 0, 0, 0, loc)
	t20220301 := time.Date(2022, 3, 1, 0, 0, 0, 0, loc)
	t20220310 := time.Date(2022, 3, 10, 0, 0, 0, 0, loc)
	t20220328 := time.Date(2022, 3, 28, 0, 0, 0, 0, loc)
	t20220401 := time.Date(2022, 4, 1, 0, 0, 0, 0, loc)
	t20220403 := time.Date(2022, 4, 3, 0, 0, 0, 0, loc)
	t20220410 := time.Date(2022, 4, 10, 0, 0, 0, 0, loc)
	t20220501 := time.Date(2022, 5, 1, 0, 0, 0, 0, loc)
	t20220601 := time.Date(2022, 6, 1, 0, 0, 0, 0, loc)
	t20220701 := time.Date(2022, 7, 1, 0, 0, 0, 0, loc)
	t20220703 := time.Date(2022, 7, 3, 0, 0, 0, 0, loc)
	t22000101 := time.Date(2200, 1, 1, 0, 0, 0, 0, loc)

	vipPriceRecords := &fetchedItemPriceRecords{
		priceItems:      nil,
		priceItemStairs: nil,
		groupCodesMap: map[string]string{
			"qvm:disk:ssd": "qvm:disk",
			"qvm:ip:233m":  "qvm:ip",
		},
		groupDiscounts: []*model.QVMGroupDiscount{
			{
				ID:              67,
				QVMPriceTableID: 1122,
				UID:             233,
				GroupCode:       "qvm:ip",
				ZoneCode:        4001,
				IsDisabled:      false,
				EffectTime:      base.NewHNS(t20220201),
				DeadTime:        base.NewHNS(t20220601),
				Discount:        92,
				CreatedAt:       t20220228,
				UpdatedAt:       t20220228,
			},
		},
	}

	publicPriceRecords := &fetchedItemPriceRecords{
		priceItems: []*model.QVMPriceItem{
			// A
			{
				ID:              1234,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20010101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20190110,
				UpdatedAt:       t20190110,
			},
			// A 干扰项（item 不同）
			{
				ID:              1235,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20010101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20190110,
				UpdatedAt:       t20190110,
			},
			// A 干扰项（zone 不同）
			{
				ID:              1236,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20010101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20190110,
				UpdatedAt:       t20190110,
			},
			// B
			{
				ID:              2345,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20190101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220310,
				UpdatedAt:       t20220310,
			},
			// B 干扰项（item 不同）
			{
				ID:              2346,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20190101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220310,
				UpdatedAt:       t20220310,
			},
			// B 干扰项（zone 不同）
			{
				ID:              2347,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20190101),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220310,
				UpdatedAt:       t20220310,
			},
			// C
			{
				ID:              3456,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20220301),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220328,
				UpdatedAt:       t20220328,
			},
			// C 干扰项（币种不同）
			{
				ID:              3457,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "USD",
				EffectTime:      base.NewHNS(t20220301),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220328,
				UpdatedAt:       t20220328,
			},
			// D
			{
				ID:              4567,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(t20220501),
				DeadTime:        base.NewHNS(t22000101),
				CreatedAt:       t20220410,
				UpdatedAt:       t20220410,
			},
		},
		priceItemStairs: []*model.QVMPriceItemStair{
			{ID: 11111, QVMPriceItemID: 1234, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(66)},
			{ID: 11112, QVMPriceItemID: 1235, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(77)},
			{ID: 11113, QVMPriceItemID: 1236, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(88)},
			{ID: 22222, QVMPriceItemID: 2345, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(666)},
			{ID: 22223, QVMPriceItemID: 2346, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(777)},
			{ID: 22224, QVMPriceItemID: 2347, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(888)},
			{ID: 33333, QVMPriceItemID: 3456, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(6666)},
			{ID: 33334, QVMPriceItemID: 3457, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(7777)},
			{ID: 33335, QVMPriceItemID: 4567, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(66666)},
		},
	}

	expectedA := &ItemPricesResp{
		ItemCode: "qvm:ip:233m",
		ZoneCode: 4001,
		Prices: []*QVMPriceItemDetail{
			{
				UID:            233,
				ItemCode:       "qvm:ip:233m",
				ZoneCode:       4001,
				StairPriceType: "UNITPRICE",
				CurrencyType:   "CNY",
				Stairs: []*QVMItemPriceStairDetail{
					{Quantity: 0, Price: base.NewNMoney(66)},
				},
				EffectTime: t20180401,
				DeadTime:   t20190101,
				CreatedAt:  t20190110,
				IsVIP:      false,
				VIPOp:      "",
			},
			{
				UID:            233,
				ItemCode:       "qvm:ip:233m",
				ZoneCode:       4001,
				StairPriceType: "UNITPRICE",
				CurrencyType:   "CNY",
				Stairs: []*QVMItemPriceStairDetail{
					{Quantity: 0, Price: base.NewNMoney(666)},
				},
				EffectTime: t20190101,
				DeadTime:   t20220201,
				CreatedAt:  t20220310,
				IsVIP:      false,
				VIPOp:      "",
			},
			{
				UID:            233,
				ItemCode:       "qvm:ip:233m",
				ZoneCode:       4001,
				StairPriceType: "UNITPRICE",
				CurrencyType:   "CNY",
				Stairs: []*QVMItemPriceStairDetail{
					{Quantity: 0, Price: base.NewNMoney(60)},
				},
				EffectTime: t20220201,
				DeadTime:   t20220401,
				CreatedAt:  t20220228,
				IsVIP:      true,
				VIPOp:      "QVMPRICE:Table:1122:Group:67",
			},
		},
	}

	actualA, err := calculateItemPricesWithTimeRange(
		233,
		"qvm:ip:233m",
		"qvm:ip",
		4001,
		"CNY",
		t20220403,
		base.TimeRange{
			Start: t20180401,
			End:   t20220401,
		},
		vipPriceRecords,
		publicPriceRecords,
	)
	assert.NoError(t, err)
	assert.Equal(t, expectedA, actualA)

	expectedB := &ItemPricesResp{
		ItemCode: "qvm:ip:233m",
		ZoneCode: 4001,
		Prices: []*QVMPriceItemDetail{
			{
				UID:            233,
				ItemCode:       "qvm:ip:233m",
				ZoneCode:       4001,
				StairPriceType: "UNITPRICE",
				CurrencyType:   "CNY",
				Stairs: []*QVMItemPriceStairDetail{
					{Quantity: 0, Price: base.NewNMoney(60)},
				},
				EffectTime: t20220301,
				DeadTime:   t20220601,
				CreatedAt:  t20220228,
				IsVIP:      true,
				VIPOp:      "QVMPRICE:Table:1122:Group:67",
			},
			{
				UID:            233,
				ItemCode:       "qvm:ip:233m",
				ZoneCode:       4001,
				StairPriceType: "UNITPRICE",
				CurrencyType:   "CNY",
				Stairs: []*QVMItemPriceStairDetail{
					{Quantity: 0, Price: base.NewNMoney(66666)},
				},
				EffectTime: t20220601,
				DeadTime:   t20220701,
				CreatedAt:  t20220410,
				IsVIP:      false,
				VIPOp:      "",
			},
		},
	}

	actualB, err := calculateItemPricesWithTimeRange(
		233,
		"qvm:ip:233m",
		"qvm:ip",
		4001,
		"CNY",
		t20220703,
		base.TimeRange{
			Start: t20220301,
			End:   t20220701,
		},
		vipPriceRecords,
		publicPriceRecords,
	)
	assert.NoError(t, err)
	assert.Equal(t, expectedB, actualB)
}
