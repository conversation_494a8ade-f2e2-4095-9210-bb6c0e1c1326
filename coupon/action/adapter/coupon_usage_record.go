package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/coupon/service"
)

// BuildPbCouponUsageRecord converts service.CouponUsageRecord to the pb layer type.
func BuildPbCouponUsageRecord(
	in *service.CouponUsageRecord,
) (*pb.CouponUsageRecord, error) {
	return &pb.CouponUsageRecord{
		Id:         in.ID,
		CouponCode: in.CouponCode,
		Uid:        in.UID,
		Money:      in.Money.ToInt64(),
		OrderHash:  in.OrderHash,
		PoId:       in.PoID,
		BatchId:    in.BatchID,
		CreatedAt:  timestamppb.New(in.CreatedAt),
		UpdatedAt:  timestamppb.New(in.UpdatedAt),
	}, nil
}

// BuildPbCouponUsageRecords converts from []service.CouponUsageRecord to the pb layer type.
func BuildPbCouponUsageRecords(
	in []service.CouponUsageRecord,
) ([]*pb.CouponUsageRecord, error) {
	result := make([]*pb.CouponUsageRecord, len(in))
	for i, x := range in {
		obj, err := BuildPbCouponUsageRecord(&x)
		if err != nil {
			return nil, errors.Trace(err)
		}
		result[i] = obj
	}
	return result, nil
}
