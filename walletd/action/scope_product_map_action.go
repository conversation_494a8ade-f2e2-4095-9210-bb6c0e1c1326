package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// GetScopeProductMapByID 根据ID获取计费范围-产品关系
func (a *WalletAction) GetScopeProductMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeProductMap, error) {
	_map, err := a.walletBizSrv.GetScopeProductMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeProductMap(_map)
}

// CreateScopeProductMap 创建计费范围-产品关系
func (a *WalletAction) CreateScopeProductMap(
	ctx context.Context,
	_map *pb.ScopeProductMap,
) (*pb.ScopeProductMap, error) {
	scopeProductMap, err := adapter.BuildScopeProductMap(_map)
	if err != nil {
		return nil, err
	}

	m, err := a.walletBizSrv.CreateScopeProductMap(ctx, scopeProductMap)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeProductMap(m)
}

// UpdateScopeProductMapByID 根据ID更新计费范围-产品关系
func (a *WalletAction) UpdateScopeProductMapByID(
	ctx context.Context,
	param *pb.IDScopeProductMapParam,
) (*pb.ScopeProductMap, error) {
	m, err := adapter.BuildScopeProductMap(param.GetScopeProductMap())
	if err != nil {
		return nil, err
	}

	_map, err := a.walletBizSrv.UpdateScopeProductMapByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeProductMap(_map)
}

// DeleteScopeProductMapByID 根据ID删除计费范围-产品关系
func (a *WalletAction) DeleteScopeProductMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeProductMap, error) {
	_map, err := a.walletBizSrv.DeleteScopeProductMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeProductMap(_map)
}

// DeleteScopeProductMapsByScopeID 根据计费范围ID删除计费范围-产品关系
func (a *WalletAction) DeleteScopeProductMapsByScopeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeProductMapList, error) {
	result, err := a.walletBizSrv.DeleteScopeProductMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	count, err := a.walletBizSrv.CountScopeProductMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeProductMapList{
		Count:            count,
		ScopeProductMaps: make([]*pb.ScopeProductMap, len(result)),
	}
	for i, _map := range result {
		m, err := adapter.BuildPbScopeProductMap(&_map)
		if err != nil {
			return nil, err
		}
		list.ScopeProductMaps[i] = m
	}
	return list, nil
}

// ListAllScopeProductMaps 列举所有的计费范围-产品关系
func (a *WalletAction) ListAllScopeProductMaps(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.ScopeProductMapList, error) {
	count, err := a.walletBizSrv.CountAllScopeProductMaps(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	maps, err := a.walletBizSrv.ListAllScopeProductMaps(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeProductMapList{
		Count:            count,
		ScopeProductMaps: make([]*pb.ScopeProductMap, len(maps)),
	}
	for i, _map := range maps {
		m, err := adapter.BuildPbScopeProductMap(&_map)
		if err != nil {
			return nil, err
		}
		list.ScopeProductMaps[i] = m
	}
	return list, nil
}

// CountAllScopeProductMaps 获取所有计费范围-产品关系数量
func (a *WalletAction) CountAllScopeProductMaps(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountAllScopeProductMaps(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListScopeProductMapsByProductID 通过产品ID分页列举计费范围-产品关系
func (a *WalletAction) ListScopeProductMapsByProductID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ScopeProductMapList, error) {
	count, err := a.walletBizSrv.CountScopeProductMapsByProductID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	maps, err := a.walletBizSrv.ListScopeProductMapsByProductID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeProductMapList{
		Count:            count,
		ScopeProductMaps: make([]*pb.ScopeProductMap, len(maps)),
	}
	for i, _map := range maps {
		m, err := adapter.BuildPbScopeProductMap(&_map)
		if err != nil {
			return nil, err
		}
		list.ScopeProductMaps[i] = m
	}
	return list, nil
}

// CountScopeProductMapsByProductID 根据产品ID列举计费范围-产品关系数量
func (a *WalletAction) CountScopeProductMapsByProductID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountScopeProductMapsByProductID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListScopeProductMapsByScopeID 根据计费范围ID列举
func (a *WalletAction) ListScopeProductMapsByScopeID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ScopeProductMapList, error) {
	count, err := a.walletBizSrv.CountScopeProductMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	maps, err := a.walletBizSrv.ListScopeProductMapsByScopeID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeProductMapList{
		Count:            count,
		ScopeProductMaps: make([]*pb.ScopeProductMap, len(maps)),
	}
	for i, _map := range maps {
		m, err := adapter.BuildPbScopeProductMap(&_map)
		if err != nil {
			return nil, err
		}
		list.ScopeProductMaps[i] = m
	}
	return list, nil
}

// CountScopeProductMapsByScopeID 根据计费范围ID获取计费范围-产品关系数量
func (a *WalletAction) CountScopeProductMapsByScopeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountScopeProductMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListScopeIDByProductIDs 批量获取 productID 列表对应的 scopeID 列表
func (a *WalletAction) ListScopeIDByProductIDs(ctx context.Context, param *pb.IDListParam) (*pb.IDListParam, error) {
	productIDs := param.Ids
	scopeIDs, err := a.walletBizSrv.ListScopeIDByProductIDs(ctx, productIDs)
	if err != nil {
		return nil, err
	}
	return &pb.IDListParam{Ids: scopeIDs}, nil
}

func (a *WalletAction) CalculateUserProductVoucherQuota(
	ctx context.Context,
	param *pb.UserProductVoucherQuotaRequest,
) (*pb.UserProductVoucherQuotaResponse, error) {
	userProductVoucherQuota, err := a.walletBizSrv.CalculateUserProductVoucherQuota(ctx, param.GetUid(), param.GetCodes(), param.GetProductIds())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbUserProductVoucherQuotaResponse(userProductVoucherQuota), nil
}
