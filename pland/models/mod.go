package models

import "github.com/qbox/bo-base/v4/dao"

// Dao plan dao
type Dao struct {
	base              *dao.BaseDao
	PlanDao           *PlanDao
	UserPlanDao       *UserPlanDao
	UserSubscribeDao  *UserSubscribeDao
	UserPlanChangeDao *UserPlanChangeDao
}

// NewDao new respack dao
func NewDao(baseDao *dao.BaseDao) *Dao {
	return &Dao{
		base:              baseDao,
		PlanDao:           NewPlanDao(baseDao),
		UserPlanDao:       NewUserPlanDao(baseDao),
		UserSubscribeDao:  NewUserSubscribeDao(baseDao),
		UserPlanChangeDao: NewUserPlanChangeDao(baseDao),
	}
}
