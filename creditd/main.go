package main

import (
	"context"
	"flag"
	"net/http"

	"github.com/go-openapi/strfmt"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/qiniu/version/v2"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"github.com/qbox/bo-base/v4/cli"
	"github.com/qbox/bo-base/v4/dao"
	hook "github.com/qbox/bo-base/v4/errors/logrus"
	"github.com/qbox/bo-base/v4/eventbus"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/bo-base/v4/intl/tz"
	baselog "github.com/qbox/bo-base/v4/log"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/pay-sdk/base/account"
	pb "github.com/qbox/pay-sdk/credit"
	gaeaClient "github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/creditd/action"
	creditdCfg "qiniu.io/pay/creditd/config"
	creditDao "qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/i18n"
	"qiniu.io/pay/creditd/service"
)

func main() {
	var confPath string
	var enableHTTPPprof bool
	var runMigration bool
	flag.StringVar(&confPath, "conf", "creditd.yml", "config file path")
	flag.BoolVar(&enableHTTPPprof, "pprof", false, "enable net/http/pprof under /debug/pprof paths")
	flag.BoolVar(&runMigration, "automigrate", false, "enable auto migration of db schema")
	_ = flag.Bool("version", false, "print version info and exit")
	flag.Parse()
	cli.InitFlagMap()

	if cli.IsFlagProvided("version") {
		version.Print()
		return
	}

	conf, err := creditdCfg.LoadCreditdConfig(confPath)
	if err != nil {
		log.WithField("err", err).Fatal("failed to load config")
	}

	l10nProvider, err := intl.Init(&conf.Intl, i18n.L10nFS, i18n.RelativePath)
	if err != nil {
		log.WithError(err).Fatal("failed to init l10n mechanism")
		return
	}

	// command-line --pprof switch has higher priority over config settings
	if cli.IsFlagProvided("pprof") {
		log.WithFields(log.Fields{
			"configValue": conf.RPC.EnablePprof,
			"cliValue":    enableHTTPPprof,
		}).Info("overriding pprof option with command-line flag")
		conf.RPC.EnablePprof = enableHTTPPprof
	}

	log.AddHook(hook.NewHook(hook.WithKeys("reqid")))
	log.SetFormatter(baselog.NewFlattenJSONFormatter())
	loggerEntry := rpc.NewLoggerEntry(log.StandardLogger())
	rpc.InitLogging(loggerEntry)

	baseDao, err := dao.InitMysqlDao(&conf.MySQL, &conf.Cache)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init dao layer")
	}

	if runMigration {
		dao.RegisterMigrate(baseDao.DB)
	}

	walletV4Conn, err := rpc.GrpcConnectWithName(
		conf.Services.WalletV4, rpc.ServicePayV4Wallet, conf.RPC.Keepalive.Client,
	)
	if err != nil {
		log.WithError(err).Fatal("connect to wallet v4 service failed")
		return
	}
	paymentCl := wallet.NewPaymentServiceClient(walletV4Conn)
	walletCl := wallet.NewPayWalletServiceClient(walletV4Conn)

	generalTransport, err := account.NewTransport(&conf.AccConfig)
	if err != nil {
		log.WithError(err).Fatal("new acc transport failed")
	}
	clientTransport, err := rpc.NewSwaggerTransport(conf.Services.GaeaAdmin, &http.Client{
		Transport: generalTransport,
	})
	if err != nil {
		log.WithError(err).Fatal("connect to gaea admin service failed")
		return
	}
	gaeaSrv := gaeaClient.New(clientTransport, strfmt.Default)

	cDao := creditDao.NewCreditDao(baseDao)
	creditService, err := service.NewCreditService(
		l10nProvider,
		cDao,
		paymentCl,
		walletCl,
		gaeaSrv,
		conf.Credit.MinimumSystemCreditLine,
		conf.Credit.MaximumSystemCreditLine,
		conf.Cache.DefaultExpires,
	)
	if err != nil {
		log.WithError(err).Fatal("init credit service failed")
		return
	}

	nDao := creditDao.NewNoticeRecordDao(baseDao)
	notificationSrv, err := service.NewNotificationService(conf, cDao, nDao, paymentCl)
	if err != nil {
		log.WithError(err).Fatal("init notification service failed")
		return
	}

	aDao := creditDao.NewAvailableBalanceThresholdDao(baseDao)
	thresholdSrv, err := service.NewAvailableBalanceThresholdService(aDao, conf.Cache.DefaultExpires)
	if err != nil {
		log.WithError(err).Fatal("init threshold service failed")
		return
	}

	freezeSrv, err := service.NewFreezeService(gaeaSrv)
	if err != nil {
		log.WithError(err).Fatal("init freeze service failed")
		return
	}

	eventBus, err := eventbus.NewRabbitMQEventBus(&conf.EventBus, log.New())
	if err != nil {
		log.WithError(err).Fatal("init eventbus failed")
		return
	}

	creditAction := action.NewCreditAction(
		creditService,
		notificationSrv,
		thresholdSrv,
		freezeSrv,
		eventBus,
		conf.Credit.DefaultPageSize,
		conf.NoticeConfig.DelayedDuration,
	)

	creditAction.EventbusDaemon(tz.MustWithGlobalRefLocation(context.Background()))

	if err := rpc.Serve(
		&conf.RPC,
		loggerEntry,
		func(s *grpc.Server) {
			pb.RegisterPayCreditServiceServer(s, creditAction)
		},
		pb.RegisterPayCreditServiceHandlerFromEndpoint,
	); err != nil {
		log.WithField("err", err).Fatal("failed to serve")
	}
}
