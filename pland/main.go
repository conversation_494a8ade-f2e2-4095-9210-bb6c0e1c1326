package main

import (
	"flag"
	"net/http"

	"github.com/go-openapi/strfmt"
	"github.com/go-redis/redis/v8"
	"github.com/qbox/bo-base/v4/cli"
	"github.com/qbox/bo-base/v4/dao"
	hook "github.com/qbox/bo-base/v4/errors/logrus"
	"github.com/qbox/bo-base/v4/intl"
	baselog "github.com/qbox/bo-base/v4/log"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/pay-sdk/base/account"
	"github.com/qbox/pay-sdk/dict"
	gaeaClient "github.com/qbox/pay-sdk/gaea/client"
	pb "github.com/qbox/pay-sdk/plan"
	"github.com/qbox/pay-sdk/price"
	"github.com/qbox/pay-sdk/respack"
	priceClient "github.com/qbox/pay-sdk/v3/price/client"
	walletClient "github.com/qbox/pay-sdk/v3/walletEx/client"
	"github.com/qiniu/version/v2"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"qiniu.io/pay/pland/action"
	"qiniu.io/pay/pland/config"
	"qiniu.io/pay/pland/i18n"
	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/service"
)

func main() {
	var confPath string
	var enableHTTPPprof bool
	flag.StringVar(&confPath, "conf", "pland.yml", "config file path")
	flag.BoolVar(&enableHTTPPprof, "pprof", false, "enable net/http/pprof under /debug/pprof paths")
	_ = flag.Bool("version", false, "print version info and exit")
	flag.Parse()
	cli.InitFlagMap()

	if cli.IsFlagProvided("version") {
		version.Print()
		return
	}

	conf, err := config.LoadPlandConfig(confPath)
	if err != nil {
		log.WithField("err", err).Fatal("failed to load config")
	}

	_, err = intl.Init(&conf.Intl, i18n.L10nFS, i18n.RelativePath)
	if err != nil {
		log.WithError(err).Fatal("failed to init l10n mechanism")
		return
	}

	// command-line --pprof switch has higher priority over config settings
	if cli.IsFlagProvided("pprof") {
		log.WithFields(log.Fields{
			"configValue": conf.RPC.EnablePprof,
			"cliValue":    enableHTTPPprof,
		}).Info("overriding pprof option with command-line flag")
		conf.RPC.EnablePprof = enableHTTPPprof
	}

	log.AddHook(hook.NewHook(hook.WithKeys("reqid")))
	log.SetReportCaller(true)
	log.SetFormatter(baselog.NewFlattenJSONFormatter())
	loggerEntry := rpc.NewLoggerEntry(log.StandardLogger())
	rpc.InitLogging(loggerEntry)

	baseDao, err := dao.InitMysqlDao(&conf.MySQL, &conf.Cache)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init mysql dao layer")
		return
	}
	httpClient, err := initHTTPClient(conf)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init http client")
		return
	}
	gaea, err := initGaeaClient(conf, httpClient)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init gaea client")
		return
	}

	price, err := initPriceClient(conf, httpClient)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init price client")
		return
	}

	wallet, err := initWalletClient(conf, httpClient)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init wallet client")
		return
	}
	dictClient, err := initDictV4Service(conf)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init dict client")
		return
	}
	respackClient, err := initRespackService(conf)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init respack client")
		return
	}
	priceV4Client, err := initPriceV4Service(conf)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init priceV4 client")
		return
	}

	redisClient := initRedisClient(conf)

	a := action.NewPlanAction(service.NewPlanService(
		service.WithMySQLDao(models.NewDao(baseDao)),
		service.WithGaeaClient(gaea),
		service.WithPriceClient(price),
		service.WithWalletClient(wallet),
		service.WithRedisClient(redisClient),
		service.WithDictClient(dictClient),
		service.WithRespackClient(respackClient),
		service.WithPriceV4Client(priceV4Client),
	), conf)

	if err = rpc.Serve(
		&conf.RPC,
		loggerEntry,
		func(s *grpc.Server) {
			pb.RegisterPlanServiceServer(s, a)
		},
		pb.RegisterPlanServiceHandlerFromEndpoint,
	); err != nil {
		log.WithField("err", err).Fatal("failed to serve")
	}
}
func initHTTPClient(conf *config.PlandConfig) (*http.Client, error) {
	transport, err := account.NewTransport(&conf.Acc)
	if err != nil {
		return nil, err
	}
	return &http.Client{Transport: transport}, nil
}
func initGaeaClient(conf *config.PlandConfig, client *http.Client) (*gaeaClient.Gaea, error) {
	clientTransport, err := rpc.NewSwaggerTransport(conf.HttpService.Gaea, client)
	if err != nil {
		return nil, err
	}
	return gaeaClient.New(clientTransport, strfmt.Default), nil
}

func initPriceClient(conf *config.PlandConfig, client *http.Client) (*priceClient.Price, error) {
	clientTransport, err := rpc.NewSwaggerTransport(conf.HttpService.Price, client)
	if err != nil {
		return nil, err
	}
	return priceClient.New(clientTransport, strfmt.Default), nil
}

func initWalletClient(conf *config.PlandConfig, client *http.Client) (*walletClient.WalletEx, error) {
	clientTransport, err := rpc.NewSwaggerTransport(conf.HttpService.Wallet, client)
	if err != nil {
		return nil, err
	}
	return walletClient.New(clientTransport, strfmt.Default), nil
}

func initRedisClient(conf *config.PlandConfig) redis.UniversalClient {
	return redis.NewUniversalClient(&conf.Cache.RedisConfig)
}

func initDictV4Service(conf *config.PlandConfig) (dict.PayDictServiceClient, error) {
	conn, err := rpc.GrpcConnectWithName(conf.RpcService.Dict, rpc.ServicePayV4Dict, conf.RPC.Keepalive.Client)
	if err != nil {
		return nil, err
	}
	return dict.NewPayDictServiceClient(conn), nil
}

func initRespackService(conf *config.PlandConfig) (respack.RespackServiceClient, error) {
	conn, err := rpc.GrpcConnectWithName(conf.RpcService.Respack, rpc.ServiceRespack, conf.RPC.Keepalive.Client)
	if err != nil {
		return nil, err
	}
	return respack.NewRespackServiceClient(conn), nil
}

func initPriceV4Service(conf *config.PlandConfig) (price.PayPriceServiceClient, error) {
	conn, err := rpc.GrpcConnectWithName(conf.RpcService.Price, rpc.ServicePayV4Price, conf.RPC.Keepalive.Client)
	if err != nil {
		return nil, err
	}
	return price.NewPayPriceServiceClient(conn), nil
}
