package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"qiniu.io/pay/qvmprice/model"
)

func validatePriceItemsForInsertion(
	priceItems []*QVMPriceItemDetail,
	isPublicPrice bool,
) error {
	for _, pi := range priceItems {
		if isPublicPrice && pi.UID != 0 {
			return errors.New("uid must be 0 for public prices")
		}
		if !isPublicPrice && pi.UID == 0 {
			return errors.New("uid must not be 0 for VIP prices")
		}
		if pi.ItemCode == "" {
			return errors.New("price items must have ItemCode")
		}
		if pi.StairPriceType == "" {
			return errors.New("price items must have StairPriceType")
		}
		if pi.CurrencyType == "" {
			return errors.New("price items must have CurrencyType")
		}
		if err := validatePriceItemStairs(pi.<PERSON>air<PERSON>); err != nil {
			return err
		}
	}

	return nil
}

func validatePriceItemStairs(x []*QVMItemPriceStairDetail) error {
	if len(x) == 0 {
		return errors.New("every price item must have stair")
	}

	if len(x) == 1 && x[0].Quantity != 0 {
		return errors.New("single-stair price's qty must be 0")
	}

	lastQty := uint64(0)
	for i, s := range x {
		if s.Quantity < uint64(lastQty) {
			return errors.New("stair qty must be monotonically non-decreasing")
		}
		if s.Quantity == lastQty && i != len(x)-1 {
			return errors.New("only the last 2 stairs may have equal qty")
		}
		lastQty = s.Quantity
	}

	return nil
}

func validateGroupDiscountsForInsertion(x []*QVMGroupDiscountDetail) error {
	for _, gd := range x {
		if gd.GroupCode == "" {
			return errors.New("group discounts must have GroupCode")
		}
	}

	return nil
}

// createPriceTable 创建一张 QVM 价格表
func (a *QVMPriceService) createPriceTable(
	ctx context.Context,
	uid uint64,
	remark string,
	isDisabled bool,
	priceItems []*QVMPriceItemDetail,
	groupDiscounts []*QVMGroupDiscountDetail,
	epoch time.Time,
) error {
	if len(priceItems) == 0 && len(groupDiscounts) == 0 {
		return errors.New("cannot create empty price table")
	}

	return a.dao.DoTransaction(func(qd *model.QVMPriceDao) error {
		// QVMPriceTable
		pt := model.QVMPriceTable{
			ID:         0,
			UID:        uid,
			Remark:     remark,
			IsDisabled: isDisabled,
			CreatedAt:  epoch,
			UpdatedAt:  epoch,
		}
		err := qd.QVMPriceTable.Create(&pt)
		if err != nil {
			return err
		}
		ptID := pt.ID

		// QVMPriceItem
		if len(priceItems) > 0 {
			pis := mapSlice(
				func(x *QVMPriceItemDetail) model.QVMPriceItem {
					return priceItemDetailToModelForInsertion(x, ptID, epoch)
				},
				priceItems,
			)
			err = qd.QVMPriceItem.BulkInsert(ctx, pis)
			if err != nil {
				return err
			}

			insertedPIs, err := qd.QVMPriceItem.ListByPriceTableID(ptID)
			if err != nil {
				return err
			}

			piIDsMap := listToMap(
				func(x *model.QVMPriceItem) (string, uint64) {
					return modelPriceItemKey(x), x.ID
				},
				insertedPIs,
				false,
			)

			// QVMPriceItemStair
			stairs, err := mapAndFlattenSliceFallible(
				func(x *QVMPriceItemDetail) ([]model.QVMPriceItemStair, error) {
					key := priceItemDetailKey(x)
					piid := piIDsMap[key]
					if piid == 0 {
						return nil, fmt.Errorf("QVMPriceItem keyed '%s' not present in DB after insertion", key)
					}

					return priceItemStairDetailsToModelForInsertion(x, piid, x.Stairs, epoch), nil
				},
				priceItems,
			)
			if err != nil {
				return err
			}

			err = qd.QVMPriceItemStair.BulkInsert(ctx, stairs)
			if err != nil {
				return err
			}
		}

		// QVMGroupDiscount
		if len(groupDiscounts) > 0 {
			gds := mapSlice(
				func(x *QVMGroupDiscountDetail) model.QVMGroupDiscount {
					return groupDiscountDetailToModelForInsertion(x, ptID, uid, epoch)
				},
				groupDiscounts,
			)
			err = qd.QVMGroupDiscount.BulkInsert(ctx, gds)
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// 必须和 modelPriceItemKey 行为保持一致
func priceItemDetailKey(x *QVMPriceItemDetail) string {
	return fmt.Sprintf(
		"%d:%s:%d:%s:%d:%d",
		x.UID,
		x.ItemCode,
		x.ZoneCode,
		x.CurrencyType,
		base.NewHNS(x.EffectTime),
		base.NewHNS(x.DeadTime),
	)
}

// 必须和 priceItemDetailKey 行为保持一致
func modelPriceItemKey(x *model.QVMPriceItem) string {
	return fmt.Sprintf(
		"%d:%s:%d:%s:%d:%d",
		x.UID,
		x.ItemCode,
		x.ZoneCode,
		x.CurrencyType,
		x.EffectTime,
		x.DeadTime,
	)
}

func priceItemDetailToModelForInsertion(
	x *QVMPriceItemDetail,
	ptid uint64,
	epoch time.Time,
) model.QVMPriceItem {
	return model.QVMPriceItem{
		ID:              0,
		QVMPriceTableID: ptid,
		UID:             x.UID,
		ItemCode:        x.ItemCode,
		ZoneCode:        x.ZoneCode,
		IsDisabled:      false,
		StairPriceType:  x.StairPriceType,
		CurrencyType:    x.CurrencyType,
		EffectTime:      base.NewHNS(x.EffectTime),
		DeadTime:        base.NewHNS(x.DeadTime),
		CreatedAt:       epoch,
		UpdatedAt:       epoch,
	}
}

func priceItemStairDetailsToModelForInsertion(
	pi *QVMPriceItemDetail,
	piid uint64,
	l []*QVMItemPriceStairDetail,
	epoch time.Time,
) []model.QVMPriceItemStair {
	return enumerateMapSlice(
		func(idx int, x *QVMItemPriceStairDetail) model.QVMPriceItemStair {
			return model.QVMPriceItemStair{
				ID:             0,
				QVMPriceItemID: piid,
				UID:            pi.UID,
				ItemCode:       pi.ItemCode,
				Quantity:       x.Quantity,
				Price:          x.Price,
				Order:          uint64(idx),
				CreatedAt:      epoch,
				UpdatedAt:      epoch,
			}
		},
		l,
	)
}

func groupDiscountDetailToModelForInsertion(
	x *QVMGroupDiscountDetail,
	ptid uint64,
	uid uint64,
	epoch time.Time,
) model.QVMGroupDiscount {
	return model.QVMGroupDiscount{
		ID:              0,
		QVMPriceTableID: ptid,
		UID:             uid,
		GroupCode:       x.GroupCode,
		ZoneCode:        x.ZoneCode,
		IsDisabled:      false,
		EffectTime:      base.NewHNS(x.EffectTime),
		DeadTime:        base.NewHNS(x.DeadTime),
		Discount:        x.Discount,
		CreatedAt:       epoch,
		UpdatedAt:       epoch,
	}
}
