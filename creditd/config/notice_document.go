//nolint:gosmopolitan // 信用额度用先不国际化（后面考虑别的方式）
package config

// salesCreatedNoticeEmailContent is email content for creation(send to salesman)
const salesCreatedNoticeEmailContent = `
<p>您好，</p>
<p>您为客户 UID：{{.UID}} | 客户名：{{.CustomerName}} 申请的 {{.CurrencyType.Symbol}} {{.CreditLine}} 信用额度已审批通过，请尽快提醒用户前往「管理控制台-财务中心-财务概况」激活信用额度，激活成功后将开启使用。</p>
`

// userCreatedNoticeEmailContent is email content for creation(send to user)
const userCreatedNoticeEmailContent = `
<p>尊敬的七牛云用户，</p>
<p>您好，您账号 {{.Email}} 申请的 {{.CurrencyType.Symbol}} {{.CreditLine}} 信用额度已审核通过，请您尽快前往「管理控制台-财务中心-财务概况」激活信用额度，激活成功后将开启使用。</p>
<p>点击立即前往激活：<a href="https://portal.qiniu.com/financial/overview">https://portal.qiniu.com/financial/overview</a></p>
`

// createdNoticeEmailSubject is email subject for creation
const createdNoticeEmailSubject = "信用额度申请通过"

// createdNoticeSMSContent is sms content for creation
const createdNoticeSMSContent = "尊敬的七牛云用户，您好，您账号 {{.Email}} 申请的 {{.CurrencyType.Symbol}} {{.CreditLine}} 信用额度已审核通过，" +
	"请您尽快前往「管理控制台-财务中心-财务概况」激活信用额度，激活成功后将开启使用。"

// salesUpdatedNoticeEmailContent is email content for updates
// 用户之前有生效中的信用额度，更改了之后，生成新的信用额度记录，并通知用户
const salesUpdatedNoticeEmailContent = `
<p>您好，</p>
<p>客户 UID：{{.UID}} | 客户名：{{.CustomerName}} 的信用额度已调整为 {{.CurrencyType.Symbol}} {{.CreditLine}} ，您可以在 「portal.io-财务管理-费用界面」查看。</p>
`

// userUpdatedNoticeEmailContent is email content for updates
const userUpdatedNoticeEmailContent = `
<p>尊敬的七牛云用户，</p>
<p>您好，您账号 {{.Email}} 的信用额度已调整为 {{.CurrencyType.Symbol}} {{.CreditLine}} ，您可以在「管理控制台-财务中心-财务概况」查看您的信用额度。感谢您对七牛云的支持。</p>
<p>点击查看信用额度：<a href="https://portal.qiniu.com/financial/overview">https://portal.qiniu.com/financial/overview</a></p>
`

// salesUpdatedNoticeEmailSubject is email subject for updates
const salesUpdatedNoticeEmailSubject = "信用额度调整"

// userUpdatedNoticeEmailSubject is email subject for updates
const userUpdatedNoticeEmailSubject = "信用额度调整"

// updatedNoticeSMSContent is sms content for updates
const updatedNoticeSMSContent = "尊敬的七牛云用户，" +
	"您好，您账号 {{.Email}} 的信用额度已调整为 {{.CurrencyType.Symbol}} {{.CreditLine}} ，您可以在「管理控制台-财务中心-财务概况」查看您的信用额度。感谢您对七牛云的支持。"

// salesNeedActivationNoticeEmailContent is email content for delayed notice
// 超时未激活通知
const salesNeedActivationNoticeEmailContent = `
<p>您好，</p>
<p>您为客户 UID：{{.UID}} | 客户名：{{.CustomerName}} 申请的信用额度尚未成功激活，请尽快联系客户帮助激活，激活成功后将开启使用。</p>
<p>激活方式：前往「管理控制台-财务中心-财务概况」点击「立即激活」。</p>
`

// userNeedActivationNoticeEmailContent is email content for delayed notice
const userNeedActivationNoticeEmailContent = `
<p>尊敬的七牛云用户，</p>
<p>您好，您账号 {{.Email}} 申请的 {{.CurrencyType.Symbol}} {{.CreditLine}} 信用额度尚未成功激活，请您尽快前往「管理控制台-财务中心-财务概况」激活信用额度，激活成功后将开启使用。</p>
<p>点击立即前往激活：<a href="https://portal.qiniu.com/financial/overview">https://portal.qiniu.com/financial/overview</a></p>
`

// needActivationNoticeEmailSubject is email subject for delayed notice
const needActivationNoticeEmailSubject = "信用额度激活提醒"

// needActivationNoticeSMSContent is sms subject for delayed notice
const needActivationNoticeSMSContent = "尊敬的七牛云用户，您好，您账号 {{.Email}} 申请的 {{.CurrencyType.Symbol}} {{.CreditLine}} 信用额度尚未成功激活，" +
	"请您尽快前往「管理控制台-财务中心-财务概况」激活信用额度，激活成功后将开启使用。"

// 激活成功通知
const salesActivatedNoticeEmailContent = `
<p>您好，</p>
<p>您为客户 UID：{{.UID}} | 客户名：{{.CustomerName}} 申请的信用额度客户已成功激活。</p>
`

const userActivatedNoticeEmailContent = `
<p>尊敬的七牛云用户，</p>
<p>您好，您账号 {{.Email}} 申请的 {{.CurrencyType.Symbol}} {{.CreditLine}} 信用额度已成功激活，您可以开始使用。</p>
`

const activatedNoticeEmailSubject = "信用额度激活成功"

const activatedNoticeSMSContent = "您好，您账号 {{.Email}} 申请的 {{.CurrencyType.Symbol}} {{.CreditLine}} 信用额度已成功激活，您可以开始使用。"

// 信用额度被撤销
const salesCanceledNoticeEmailContent = `
<p>您好，</p>
<p>您为客户 UID：{{.UID}} | 客户名：{{.CustomerName}} 申请的信用额度已被撤销。</p>
`

const userCanceledNoticeEmailContent = `
<p>尊敬的七牛云用户，</p>
<p>您好，您账号 {{.Email}} 的信用额度已被撤销，很抱歉给您造成了不便。</p>
`

const canceledNoticeEmailSubject = "撤销信用额度"

const canceledNoticeSMSContent = "尊敬的七牛云用户，您好，您账号 {{.Email}} 的信用额度已被撤销，很抱歉给您造成了不便。"

// 信用额度被停用
const salesRevokedNoticeEmailContent = `
<p>您好，</p>
<p>您为客户 UID：{{.UID}} | 客户名：{{.CustomerName}} 申请的信用额度已被停用。</p>
`

const userRevokedNoticeEmailContent = `
<p>尊敬的七牛云用户，</p>
<p>您好，您账号 {{.Email}} 的信用额度已被停用。 若有疑问，可咨询您的商务经理。</p>
`

const revokedNoticeEmailSubject = "停用信用额度"

const revokedNoticeSMSContent = "尊敬的七牛云用户，您好，您账号 {{.Email}} 的信用额度已被停用。" +
	"很抱歉给您造成了不便。 若有疑问，请咨询您的商务经理。"

// notification 服务中定义模板名称

const userCreatedTmpl = "user_create_credit"

const userUpdatedTmpl = "user_update_credit"

const userNeedActivationTmpl = "user_need_activate_credit"

const userActivatedTmpl = "user_activate_credit"

const userCanceledTmpl = "user_cancel_credit"

const userRevokedTmpl = "user_revoke_credit"

// ChannelID defines credit-used channel in bo-notification service
const ChannelID = 23

// TemplatesUnit defines a unit that used to render email / sms
type TemplatesUnit struct {
	UserEmailTmpl     string
	SalesEmailTmpl    string
	SMSTmpl           string
	UserEmailSubject  string
	SalesEmailSubject string
	TemplateName      string
}

// CreatedTmplUnit is for creation
var CreatedTmplUnit = TemplatesUnit{
	UserEmailTmpl:     userCreatedNoticeEmailContent,
	SalesEmailTmpl:    salesCreatedNoticeEmailContent,
	SMSTmpl:           createdNoticeSMSContent,
	UserEmailSubject:  createdNoticeEmailSubject,
	SalesEmailSubject: createdNoticeEmailSubject,
	TemplateName:      userCreatedTmpl,
}

// UpdatedTmplUnit is for updates
var UpdatedTmplUnit = TemplatesUnit{
	UserEmailTmpl:     userUpdatedNoticeEmailContent,
	SalesEmailTmpl:    salesUpdatedNoticeEmailContent,
	SMSTmpl:           updatedNoticeSMSContent,
	UserEmailSubject:  userUpdatedNoticeEmailSubject,
	SalesEmailSubject: salesUpdatedNoticeEmailSubject,
	TemplateName:      userUpdatedTmpl,
}

// NeedActivationTmplUnit is for delayed notices
var NeedActivationTmplUnit = TemplatesUnit{
	UserEmailTmpl:     userNeedActivationNoticeEmailContent,
	SalesEmailTmpl:    salesNeedActivationNoticeEmailContent,
	SMSTmpl:           needActivationNoticeSMSContent,
	UserEmailSubject:  needActivationNoticeEmailSubject,
	SalesEmailSubject: needActivationNoticeEmailSubject,
	TemplateName:      userNeedActivationTmpl,
}

// ActivatedTmplUnit is for activation
var ActivatedTmplUnit = TemplatesUnit{
	UserEmailTmpl:     userActivatedNoticeEmailContent,
	SalesEmailTmpl:    salesActivatedNoticeEmailContent,
	SMSTmpl:           activatedNoticeSMSContent,
	UserEmailSubject:  activatedNoticeEmailSubject,
	SalesEmailSubject: activatedNoticeEmailSubject,
	TemplateName:      userActivatedTmpl,
}

// CanceledTmplUnit is for cancellation
var CanceledTmplUnit = TemplatesUnit{
	UserEmailTmpl:     userCanceledNoticeEmailContent,
	SalesEmailTmpl:    salesCanceledNoticeEmailContent,
	SMSTmpl:           canceledNoticeSMSContent,
	UserEmailSubject:  canceledNoticeEmailSubject,
	SalesEmailSubject: canceledNoticeEmailSubject,
	TemplateName:      userCanceledTmpl,
}

// RevokedTmplUnit is for revoke
var RevokedTmplUnit = TemplatesUnit{
	UserEmailTmpl:     userRevokedNoticeEmailContent,
	SalesEmailTmpl:    salesRevokedNoticeEmailContent,
	SMSTmpl:           revokedNoticeSMSContent,
	UserEmailSubject:  revokedNoticeEmailSubject,
	SalesEmailSubject: revokedNoticeEmailSubject,
	TemplateName:      userRevokedTmpl,
}
