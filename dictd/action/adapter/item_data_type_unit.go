package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/dict"

	"qiniu.io/pay/dictd/model"
)

// BuildPbItemDataTypeUnit converts model.ItemDataTypeUnit to the protobuf type.
func BuildPbItemDataTypeUnit(unit *model.ItemDataTypeUnit) (*pb.ItemDataTypeUnit, error) {
	return &pb.ItemDataTypeUnit{
		Id:            unit.ID,
		DataTypeId:    unit.DataTypeID,
		Name:          unit.Name,
		Power:         unit.Power,
		IsPriceAnchor: unit.IsPriceAnchor,
		CreatedAt:     timestamppb.New(unit.CreatedAt),
		UpdatedAt:     timestamppb.New(unit.UpdatedAt),
	}, nil
}

// BuildItemDataTypeUnit converts from pb.ItemDataTypeUnit to the model layer type.
func BuildItemDataTypeUnit(unit *pb.ItemDataTypeUnit) (*model.ItemDataTypeUnit, error) {
	var createdAt, updatedAt time.Time
	if unit.GetCreatedAt() != nil {
		err := unit.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = unit.GetCreatedAt().AsTime()
	}

	if unit.GetUpdatedAt() != nil {
		err := unit.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = unit.GetUpdatedAt().AsTime()
	}

	return &model.ItemDataTypeUnit{
		ID:            unit.GetId(),
		DataTypeID:    unit.GetDataTypeId(),
		Name:          unit.GetName(),
		Power:         unit.GetPower(),
		IsPriceAnchor: unit.GetIsPriceAnchor(),
		CreatedAt:     createdAt,
		UpdatedAt:     updatedAt,
	}, nil
}
