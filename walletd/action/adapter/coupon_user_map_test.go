package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"qiniu.io/pay/walletd/model"
)

func TestCouponTypeRoundTrip(t *testing.T) {
	xs := []model.CouponType{
		model.CouponTypeCash,
		model.CouponTypeDiscount,
		model.CouponTypeRebate,
	}

	for _, x := range xs {
		y := BuildPbCouponType(x)
		z := BuildCouponType(y)
		assert.Equal(t, x, z)
	}
}

func TestCouponUserMapRoundTrip(t *testing.T) {
	x := &model.CouponUserMap{
		ID:            233,
		CouponID:      2333,
		UID:           1,
		ZoneID:        2,
		TransactionID: 0,
		Type:          model.CouponTypeRebate,
		EffectTime:    base.NewHNS(time.Now().Round(0)),
		DeadTime:      base.NewHNS(time.Now().Round(0)),
		CreatedAt:     time.Now().Round(0),
		UpdatedAt:     time.Now().Round(0),
	}

	y, err := BuildPbCouponUserMap(x)
	assert.NoError(t, err)

	z, err := BuildCouponUserMap(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.CouponID, z.CouponID)
	assert.Equal(t, x.UID, z.UID)
	assert.Equal(t, x.ZoneID, z.ZoneID)
	assert.Equal(t, x.TransactionID, z.TransactionID)
	assert.Equal(t, x.Type, z.Type)
	assert.Equal(t, x.EffectTime, z.EffectTime)
	assert.Equal(t, x.DeadTime, z.DeadTime)
	assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
}
