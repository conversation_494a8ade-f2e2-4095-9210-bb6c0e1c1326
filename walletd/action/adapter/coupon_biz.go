package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
	"qiniu.io/pay/walletd/service"
)

// BuildPbBindCouponRebateParam convert service.BindCouponRebateParam to proto.BindCouponRebateParam
func BuildPbBindCouponRebateParam(x *service.BindCouponRebateParam) (*pb.BindCouponRebateParam, error) {
	return &pb.BindCouponRebateParam{
		Uid:           x.UID,
		Code:          x.Code,
		TransactionId: x.TransactionID,
		ZoneId:        x.ZoneID,
		EffectTime:    timestamppb.New(x.EffectTime.TimeIn(time.UTC)),
		DeadTime:      timestamppb.New(x.DeadTime.TimeIn(time.UTC)),
		Idempotent:    x.Idempotent,
	}, nil
}

// BuildBindCouponRebateParam convert proto.BindCouponRebateParam to service.BindCouponRebateParam
func BuildBindCouponRebateParam(x *pb.BindCouponRebateParam) (*service.BindCouponRebateParam, error) {
	var effectTime, deadTime time.Time
	if x.GetEffectTime() != nil {
		err := x.GetEffectTime().CheckValid()
		if err != nil {
			return nil, err
		}
		effectTime = x.GetEffectTime().AsTime()
	}

	if x.GetDeadTime() != nil {
		err := x.GetDeadTime().CheckValid()
		if err != nil {
			return nil, err
		}
		deadTime = x.GetDeadTime().AsTime()
	}

	return &service.BindCouponRebateParam{
		UID:           x.Uid,
		Code:          x.Code,
		TransactionID: x.TransactionId,
		ZoneID:        x.ZoneId,
		EffectTime:    base.NewHNS(effectTime),
		DeadTime:      base.NewHNS(deadTime),
		Idempotent:    x.Idempotent,
	}, nil
}

// BuildPbBindCouponRebateResp converts service.BindCouponRebateResp to the protobuf type.
func BuildPbBindCouponRebateResp(x *service.BindCouponRebateResp) (*pb.BindCouponRebateResp, error) {
	c, err := BuildPbCouponRebate(&x.Coupon)
	if err != nil {
		return nil, err
	}

	um, err := BuildPbCouponUserMap(&x.CouponUserMap)
	if err != nil {
		return nil, err
	}

	return &pb.BindCouponRebateResp{
		Coupon:        c,
		CouponUserMap: um,
	}, nil
}

// BuildPbUpgradeCouponRebateParam convert service.UpgradeCouponRebateParam to proto.UpgradeCouponRebateParam
func BuildPbUpgradeCouponRebateParam(x *service.UpgradeCouponRebateParam) (*pb.UpgradeCouponRebateParam, error) {
	return &pb.UpgradeCouponRebateParam{
		Uid:           x.UID,
		ZoneId:        x.ZoneID,
		TransactionId: x.TransactionID,
		SrcCode:       x.SrcCode,
		DstCode:       x.DstCode,
		EffectTime:    timestamppb.New(x.EffectTime.TimeIn(time.UTC)),
		DeadTime:      timestamppb.New(x.DeadTime.TimeIn(time.UTC)),
	}, nil
}

// BuildUpgradeCouponRebateParam convert proto.UpgradeCouponRebateParam to service.UpgradeCouponRebateParam
func BuildUpgradeCouponRebateParam(x *pb.UpgradeCouponRebateParam) (*service.UpgradeCouponRebateParam, error) {
	var effectTime, deadTime time.Time
	if x.GetEffectTime() != nil {
		err := x.GetEffectTime().CheckValid()
		if err != nil {
			return nil, err
		}
		effectTime = x.GetEffectTime().AsTime()
	}

	if x.GetDeadTime() != nil {
		err := x.GetDeadTime().CheckValid()
		if err != nil {
			return nil, err
		}
		deadTime = x.GetDeadTime().AsTime()
	}

	return &service.UpgradeCouponRebateParam{
		UID:           x.Uid,
		ZoneID:        x.ZoneId,
		TransactionID: x.TransactionId,
		SrcCode:       x.SrcCode,
		DstCode:       x.DstCode,
		EffectTime:    base.NewHNS(effectTime),
		DeadTime:      base.NewHNS(deadTime),
	}, nil
}

// BuildCreateDiscountRebate converts pb.CreateDiscountRebateAndRegenBillsReq to model
func BuildCreateDiscountRebate(params *pb.CreateDiscountRebateAndRegenBillsReq) (
	couponDiscounts []*service.CouponDiscountWithScope,
	couponRebates []*service.CouponRebateWithScope,
	err error) {

	couponDiscounts = make([]*service.CouponDiscountWithScope, 0)
	for _, pbCouponDiscount := range params.CouponDiscounts {
		err1 := pbCouponDiscount.GetEffectTime().CheckValid()
		if err1 != nil {
			return nil, nil, err1
		}
		effectTime := pbCouponDiscount.GetEffectTime().AsTime()

		err1 = pbCouponDiscount.GetDeadTime().CheckValid()
		if err1 != nil {
			return nil, nil, err1
		}
		deadTime := pbCouponDiscount.GetDeadTime().AsTime()

		scopeProductArr, err1 := BuildScopeProductMaps(pbCouponDiscount.Scope.GetProducts())
		if err1 != nil {
			return nil, nil, err1
		}

		scopeItemGroupArr, err1 := BuildScopeItemGroupMaps(pbCouponDiscount.Scope.GetGroups())
		if err1 != nil {
			return nil, nil, err1
		}

		scopeItemArr, err1 := BuildScopeItemMaps(pbCouponDiscount.Scope.GetItems())
		if err1 != nil {
			return nil, nil, err1
		}

		couponDiscount := &service.CouponDiscountWithScope{
			CouponDiscount: &model.CouponDiscount{
				Code:        pbCouponDiscount.GetCode(),
				Type:        pbCouponDiscount.GetType(),
				Name:        pbCouponDiscount.GetName(),
				Description: pbCouponDiscount.GetDescription(),
				Remark:      pbCouponDiscount.GetRemark(),
				Discount:    pbCouponDiscount.GetDiscount(),
				EffectTime:  base.NewHNS(effectTime),
				DeadTime:    base.NewHNS(deadTime),
			},
			Scope: &service.ScopeDetail{
				IsAll:      pbCouponDiscount.Scope.GetIsAll(),
				Products:   scopeProductArr,
				ItemGroups: scopeItemGroupArr,
				Items:      scopeItemArr,
			},
			Zones: BuildZoneInfos(pbCouponDiscount.GetZones()),
		}
		couponDiscounts = append(couponDiscounts, couponDiscount)
	}

	couponRebates = make([]*service.CouponRebateWithScope, 0)
	for _, pbCouponRebate := range params.CouponRebates {
		err1 := pbCouponRebate.GetEffectTime().CheckValid()
		if err1 != nil {
			return nil, nil, err1
		}
		effectTime := pbCouponRebate.GetEffectTime().AsTime()

		err1 = pbCouponRebate.GetDeadTime().CheckValid()
		if err1 != nil {
			return nil, nil, err1
		}
		deadTime := pbCouponRebate.GetDeadTime().AsTime()

		scopeProductArr, err1 := BuildScopeProductMaps(pbCouponRebate.Scope.GetProducts())
		if err1 != nil {
			return nil, nil, err1
		}

		scopeItemGroupArr, err1 := BuildScopeItemGroupMaps(pbCouponRebate.Scope.GetGroups())
		if err1 != nil {
			return nil, nil, err1
		}

		scopeItemArr, err1 := BuildScopeItemMaps(pbCouponRebate.Scope.GetItems())
		if err1 != nil {
			return nil, nil, err1
		}

		couponRebate := &service.CouponRebateWithScope{
			CouponRebate: &model.CouponRebate{
				Code:        pbCouponRebate.GetCode(),
				Type:        pbCouponRebate.GetType(),
				Name:        pbCouponRebate.GetName(),
				Description: pbCouponRebate.GetDescription(),
				Remark:      pbCouponRebate.GetRemark(),
				Threshold:   pbCouponRebate.GetThreshold(),
				Money:       pbCouponRebate.GetMoney(),
				EffectTime:  base.NewHNS(effectTime),
				DeadTime:    base.NewHNS(deadTime),
			},
			Scope: &service.ScopeDetail{
				IsAll:      pbCouponRebate.Scope.GetIsAll(),
				Products:   scopeProductArr,
				ItemGroups: scopeItemGroupArr,
				Items:      scopeItemArr,
			},
			Zones: BuildZoneInfos(pbCouponRebate.GetZones()),
		}
		couponRebates = append(couponRebates, couponRebate)
	}
	return couponDiscounts, couponRebates, nil
}

// BuildZoneInfos converts []*pb.ZoneInfo into []*service.ZoneInfo
func BuildZoneInfos(pbZoneInfos []*pb.ZoneInfo) []*service.ZoneInfo {
	zoneInfos := make([]*service.ZoneInfo, len(pbZoneInfos))
	for i, pbZoneInfo := range pbZoneInfos {
		zoneInfos[i] = &service.ZoneInfo{
			ZoneID:   pbZoneInfo.GetZoneId(),
			ZoneCode: pbZoneInfo.GetZoneCode(),
		}
	}
	return zoneInfos
}
