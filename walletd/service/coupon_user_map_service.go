package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/walletd/model"
)

// GetCouponUserMapByID query coupon_user_map by id
func (s *WalletBizService) GetCouponUserMapByID(
	ctx context.Context,
	id uint64,
) (*model.CouponUserMap, error) {
	return s.walletDao.CouponUserMap.GetByID(id, s.cacheExpires)
}

// CreateCouponUserMap create a new coupon_user_map record
func (s *WalletBizService) CreateCouponUserMap(
	ctx context.Context,
	m *model.CouponUserMap,
) (*model.CouponUserMap, error) {
	m.ID = 0
	err := s.walletDao.CouponUserMap.Save(m, s.cacheExpires)
	return m, err
}

// UpdateCouponUserMapByID update coupon_user_map record by id
func (s *WalletBizService) UpdateCouponUserMapByID(
	ctx context.Context,
	id uint64,
	m *model.CouponUserMap,
) (*model.CouponUserMap, error) {
	m.ID = id
	err := s.walletDao.CouponUserMap.Save(m, s.cacheExpires)
	return m, err
}

// DeleteCouponUserMapByID delete coupon_user_map record by id
func (s *WalletBizService) DeleteCouponUserMapByID(
	ctx context.Context,
	id uint64,
) (*model.CouponUserMap, error) {
	m, err := s.GetCouponUserMapByID(ctx, id)
	if err != nil {
		return nil, errors.Annotate(err, "query coupon_user_map failed").WithField("id", id)
	}
	err = s.walletDao.CouponUserMap.Delete(m)
	return m, err
}

// ListCouponUserMapsByUID list all coupon_user_map records by uid
func (s *WalletBizService) ListCouponUserMapsByUID(
	ctx context.Context,
	uid uint64,
	offset int,
	limit int,
) ([]model.CouponUserMap, error) {
	return s.walletDao.CouponUserMap.ListByUID(uid, "", offset, limit, s.cacheExpires)
}

// CountCouponUserMapsByUID count all coupon_user_map records by uid
func (s *WalletBizService) CountCouponUserMapsByUID(
	ctx context.Context,
	uid uint64,
) (uint64, error) {
	return s.walletDao.CouponUserMap.CountByUID(uid, "", s.cacheExpires)
}

// isCouponBoundToUserByCouponID 检查某个 coupon 是否绑定到了用户
//
// * uid == 0 则不检查指定用户，只要有用户绑定了就返回真
// * uid != 0 只有指定的用户绑定了才返回真
func (s *WalletBizService) isCouponBoundToUserByCouponID(
	ctx context.Context,
	couponID uint64,
	couponType model.CouponType,
	uid uint64,
) (bool, error) {

	bindingIDs, err := s.walletDao.CouponUserMap.
		GetBoundCouponByCouponIDs([]uint64{couponID}, couponType, uid)
	if err != nil {
		return false, errors.Trace(err)
	}

	return len(bindingIDs) > 0, nil
}
