package model

import (
	"net/url"
	"strconv"

	pb "github.com/qbox/pay-sdk/measureproxy"
)

type KodoQueryReq struct {
	UID    int
	Limit  int
	Marker string
}

const (
	path              = "/admin"
	kodoNoValueParams = "?bucketTagging&"
)

func (k *KodoQueryReq) ToPathAndQueryParams() string {
	u := url.Values{}
	if k.UID != 0 {
		u.Add("uid", strconv.Itoa(k.UID))
	}
	if k.Limit != 0 {
		u.Add("limit", strconv.Itoa(k.Limit))
	}
	if k.Marker != "" {
		u.Add("marker", k.<PERSON>er)
	}
	return path + kodoNoValueParams + u.Encode()
}

type KodoResp struct {
	Marker     string    `json:"marker"`
	NextMarker string    `json:"next_marker"`
	Buckets    []*Bucket `json:"buckets"`
}

type Bucket struct {
	Name string `json:"name"`
	Tags []*Tag `json:"tags"`
}

type Tag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type ResourceTag struct {
	ResourceName string          `json:"resource_name"`
	ResourceType pb.ResourceType `json:"resource_type"`
	Tags         []*Tag          `json:"tags"`
}
