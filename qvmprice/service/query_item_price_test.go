package service

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"

	"qiniu.io/pay/qvmprice/model"
)

func TestCalculateItemPricePublicPriceOnly(t *testing.T) {
	loc := time.UTC

	epoch := time.Date(2022, 3, 31, 12, 34, 56, 0, loc)
	when := time.Date(2022, 3, 31, 0, 0, 0, 0, loc)
	timeA := time.Date(2001, 1, 1, 0, 0, 0, 0, loc)
	timeB := time.Date(2019, 1, 1, 0, 0, 0, 0, loc)
	ctime1 := time.Date(2019, 1, 10, 0, 0, 0, 0, loc)
	timeC := time.Date(2022, 5, 1, 0, 0, 0, 0, loc)
	ctime2 := time.Date(2022, 3, 10, 0, 0, 0, 0, loc)
	timeD := time.Date(2200, 1, 1, 0, 0, 0, 0, loc)

	publicPriceRecords := &fetchedItemPriceRecords{
		priceItems: []*model.QVMPriceItem{
			{
				ID:              1234,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeA),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime1,
				UpdatedAt:       ctime1,
			},
			{
				ID:              1235,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeA),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime1,
				UpdatedAt:       ctime1,
			},
			{
				ID:              1236,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeA),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime1,
				UpdatedAt:       ctime1,
			},

			{
				ID:              2345,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeB),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
			{
				ID:              2346,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeB),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
			{
				ID:              2347,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeB),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},

			{
				ID:              3456,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeC),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
			{
				ID:              3457,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeC),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
			{
				ID:              3458,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeC),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
		},
		priceItemStairs: []*model.QVMPriceItemStair{
			{ID: 11111, QVMPriceItemID: 1234, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(66)},
			{ID: 11112, QVMPriceItemID: 1235, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(77)},
			{ID: 11113, QVMPriceItemID: 1236, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(88)},
			{ID: 22222, QVMPriceItemID: 2345, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(666)},
			{ID: 22223, QVMPriceItemID: 2346, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(777)},
			{ID: 22224, QVMPriceItemID: 2347, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(888)},
			{ID: 33333, QVMPriceItemID: 3456, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(6666)},
			{ID: 33334, QVMPriceItemID: 3457, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(7777)},
			{ID: 33335, QVMPriceItemID: 3458, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(8888)},
		},
	}

	expected := &QVMPriceItemDetail{
		UID:            233,
		ItemCode:       "qvm:ip:233m",
		ZoneCode:       4001,
		StairPriceType: "UNITPRICE",
		CurrencyType:   "CNY",
		Stairs: []*QVMItemPriceStairDetail{
			{Quantity: 0, Price: base.NewNMoney(666)},
		},
		EffectTime: timeB,
		DeadTime:   timeD,
		CreatedAt:  ctime2,
		IsVIP:      false,
		VIPOp:      "",
	}

	actual, err := calculateItemPrice(
		233,
		"qvm:ip:233m",
		"qvm:ip",
		4001,
		"CNY",
		when,
		epoch,
		nil,
		&fetchedItemPriceRecords{},
		publicPriceRecords,
	)
	assert.NoError(t, err)
	assert.Equal(t, expected, actual)

	// 查询公开报价时 vipRecords == nil，行为也应该一样的
	expected2 := &QVMPriceItemDetail{
		UID:            0,
		ItemCode:       "qvm:ip:233m",
		ZoneCode:       4001,
		StairPriceType: "UNITPRICE",
		CurrencyType:   "CNY",
		Stairs: []*QVMItemPriceStairDetail{
			{Quantity: 0, Price: base.NewNMoney(666)},
		},
		EffectTime: timeB,
		DeadTime:   timeD,
		CreatedAt:  ctime2,
		IsVIP:      false,
		VIPOp:      "",
	}
	actual2, err := calculateItemPrice(
		0,
		"qvm:ip:233m",
		"qvm:ip",
		4001,
		"CNY",
		when,
		epoch,
		nil,
		nil,
		publicPriceRecords,
	)
	assert.NoError(t, err)
	assert.Equal(t, expected2, actual2)
}

func TestCalculateItemPricePublicPriceWithDiscount(t *testing.T) {
	loc := time.UTC

	epoch := time.Date(2022, 3, 31, 12, 34, 56, 0, loc)
	when := time.Date(2022, 3, 31, 0, 0, 0, 0, loc)
	timeA := time.Date(2001, 1, 1, 0, 0, 0, 0, loc)
	timeB := time.Date(2019, 1, 1, 0, 0, 0, 0, loc)
	ctime1 := time.Date(2019, 1, 10, 0, 0, 0, 0, loc)
	timeC := time.Date(2022, 3, 1, 0, 0, 0, 0, loc)
	ctime2 := time.Date(2022, 3, 20, 0, 0, 0, 0, loc)
	timeD := time.Date(2200, 1, 1, 0, 0, 0, 0, loc)

	dtime1 := time.Date(2022, 3, 1, 0, 0, 0, 0, loc)
	dtime2 := time.Date(2024, 3, 1, 0, 0, 0, 0, loc)
	dctime1 := time.Date(2022, 3, 12, 13, 45, 6, 0, loc)

	vipPriceRecords := &fetchedItemPriceRecords{
		priceItems:      nil,
		priceItemStairs: nil,
		groupCodesMap: map[string]string{
			"qvm:disk:ssd": "qvm:disk",
			"qvm:ip:233m":  "qvm:ip",
		},
		groupDiscounts: []*model.QVMGroupDiscount{
			{
				ID:              66,
				QVMPriceTableID: 1122,
				UID:             233,
				GroupCode:       "qvm:disk",
				ZoneCode:        4001,
				IsDisabled:      false,
				EffectTime:      base.NewHNS(dtime1),
				DeadTime:        base.NewHNS(dtime2),
				Discount:        87,
				CreatedAt:       dctime1,
				UpdatedAt:       dctime1,
			},
			{
				ID:              67,
				QVMPriceTableID: 1122,
				UID:             233,
				GroupCode:       "qvm:ip",
				ZoneCode:        4001,
				IsDisabled:      false,
				EffectTime:      base.NewHNS(dtime1),
				DeadTime:        base.NewHNS(dtime2),
				Discount:        92,
				CreatedAt:       dctime1,
				UpdatedAt:       dctime1,
			},
		},
	}

	publicPriceRecords := &fetchedItemPriceRecords{
		priceItems: []*model.QVMPriceItem{
			{
				ID:              1234,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeA),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime1,
				UpdatedAt:       ctime1,
			},
			{
				ID:              1235,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeA),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime1,
				UpdatedAt:       ctime1,
			},
			{
				ID:              1236,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeA),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime1,
				UpdatedAt:       ctime1,
			},

			{
				ID:              2345,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeB),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
			{
				ID:              2346,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeB),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
			{
				ID:              2347,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeB),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},

			{
				ID:              3456,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeC),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
			{
				ID:              3457,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:234m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeC),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
			{
				ID:              3458,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:233m",
				ZoneCode:        4005,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeC),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
			{
				ID:              4567,
				QVMPriceTableID: 1,
				UID:             0,
				ItemCode:        "qvm:ip:2333m",
				ZoneCode:        4001,
				IsDisabled:      false,
				StairPriceType:  "UNITPRICE",
				CurrencyType:    "CNY",
				EffectTime:      base.NewHNS(timeA),
				DeadTime:        base.NewHNS(timeD),
				CreatedAt:       ctime2,
				UpdatedAt:       ctime2,
			},
		},
		priceItemStairs: []*model.QVMPriceItemStair{
			{ID: 11111, QVMPriceItemID: 1234, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(12000)},
			{ID: 11112, QVMPriceItemID: 1235, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(13000)},
			{ID: 11113, QVMPriceItemID: 1236, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(14000)},
			{ID: 22222, QVMPriceItemID: 2345, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(22000)},
			{ID: 22223, QVMPriceItemID: 2346, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(23000)},
			{ID: 22224, QVMPriceItemID: 2347, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(24000)},
			{ID: 33333, QVMPriceItemID: 3456, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(32000)},
			{ID: 33334, QVMPriceItemID: 3457, UID: 0, ItemCode: "qvm:ip:234m", Quantity: 0, Price: base.NewNMoney(33000)},
			{ID: 33335, QVMPriceItemID: 3458, UID: 0, ItemCode: "qvm:ip:233m", Quantity: 0, Price: base.NewNMoney(34000)},
			{ID: 44444, QVMPriceItemID: 4567, UID: 0, ItemCode: "qvm:ip:2333m", Quantity: 100, Price: base.NewNMoney(111111), Order: 1},
			{ID: 44445, QVMPriceItemID: 4567, UID: 0, ItemCode: "qvm:ip:2333m", Quantity: 100, Price: base.NewNMoney(222222), Order: 0},
		},
	}

	// 预期：虽然用户改价后公开价更新了，但仍然应该以改价当时的报价计算价格、折扣
	// 应该取 12000 的 92 折，时间段应该是改价操作的时间段
	expected := &QVMPriceItemDetail{
		UID:            233,
		ItemCode:       "qvm:ip:233m",
		ZoneCode:       4001,
		StairPriceType: "UNITPRICE",
		CurrencyType:   "CNY",
		Stairs: []*QVMItemPriceStairDetail{
			{Quantity: 0, Price: base.NewNMoney(11040)},
		},
		EffectTime: dtime1,
		DeadTime:   dtime2,
		CreatedAt:  dctime1,
		IsVIP:      true,
		VIPOp:      "QVMPRICE:Table:1122:Group:67",
	}

	actual, err := calculateItemPrice(
		233,
		"qvm:ip:233m",
		"qvm:ip",
		4001,
		"CNY",
		when,
		epoch,
		nil,
		vipPriceRecords,
		publicPriceRecords,
	)
	assert.NoError(t, err)
	assert.Equal(t, expected, actual)

	// BO-19850

	// 预期：用户改价后，被改的组里添加了新计费项，虽然改价当时这个计费项没有报价，但仍然应该以这个计费项最早的一版价格计算折扣
	// 应该取 222222、111111 的 92 折
	expected2 := &QVMPriceItemDetail{
		UID:            233,
		ItemCode:       "qvm:ip:2333m",
		ZoneCode:       4001,
		StairPriceType: "UNITPRICE",
		CurrencyType:   "CNY",
		Stairs: []*QVMItemPriceStairDetail{
			{Quantity: 100, Price: base.NewNMoney(204444)},
			{Quantity: 100, Price: base.NewNMoney(102222)},
		},
		EffectTime: dtime1,
		DeadTime:   dtime2,
		CreatedAt:  dctime1,
		IsVIP:      true,
		VIPOp:      "QVMPRICE:Table:1122:Group:67",
	}

	actual2, err := calculateItemPrice(
		233,
		"qvm:ip:2333m",
		"qvm:ip",
		4001,
		"CNY",
		when,
		epoch,
		nil,
		vipPriceRecords,
		publicPriceRecords,
	)
	assert.NoError(t, err)
	assert.Equal(t, expected2, actual2)
}
