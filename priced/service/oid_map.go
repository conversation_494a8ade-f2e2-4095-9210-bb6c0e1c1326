package service

import (
	"context"
	"fmt"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"

	po "github.com/qbox/pay-sdk/v3/priceshim/client/operations"
	pm "github.com/qbox/pay-sdk/v3/priceshim/models"
)

func (s *PriceBizService) saveBasePriceOidmapRecord(
	ctx context.Context,
	zoneCode int64,
	itemCode string,
	priceItemID uint64,
) (err error) {

	// 写入相关 oidmap
	key := fmt.Sprintf("default_%d_%s", zoneCode, itemCode)

	randomID := base.GenRandomString(24)
	oid := fmt.Sprintf("%d:%s", zoneCode, randomID)

	req := &pm.ReqTypeIDOIDKey{
		ID:      int64(priceItemID),
		Key:     key,
		Oid:     oid,
		ObjType: "base",
	}

	params := po.NewSaveOidmapWithOIDKeyParamsWithContext(ctx)
	params.SetReqTypeIDOIDKey(req)
	_, err = s.priceshim.Operations.SaveOidmapWithOIDKey(params)
	if err != nil {
		return errors.Trace(err)
	}

	return
}
