package service

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"

	"github.com/qbox/bo-base/v4/base"
	gaeaClient "github.com/qbox/pay-sdk/gaea/client"
	gaeaOperations "github.com/qbox/pay-sdk/gaea/client/operations"
	gaeaModels "github.com/qbox/pay-sdk/gaea/models"

	"qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

type SystemCreditAssignee interface {
	NeedAssign(credit model.SystemCredit) (bool, error)
	Calculate(credit model.SystemCredit) (int64, error)
}

// DefaultAssignee 会将用户的系统授信调整为默认最小值
type DefaultAssignee struct {
	min        int64
	historyDao *dao.SystemCreditHistoryDao
}

func (a DefaultAssignee) NeedAssign(credit model.SystemCredit) (bool, error) {
	histories, err := a.historyDao.GetUserSystemCreditHistories(credit.UID, enums.AssignReasonBindFirstCard)
	if err != nil {
		return false, err
	}
	if len(histories) == 0 && credit.AssignReason != enums.AssignReasonBindFirstCard {
		return false, fmt.Errorf("user %d does not bind any card", credit.UID)
	}
	return true, nil
}

func (a DefaultAssignee) Calculate(credit model.SystemCredit) (int64, error) {
	return a.min, nil
}

// BillIssuerAssignee 会根据用户前三个月的账单金额调整用户自动授信金额
type BillIssuerAssignee struct {
	min        int64
	max        int64
	historyDao *dao.SystemCreditHistoryDao
	dao        *dao.SystemCreditDao
	gaeaSrv    *gaeaClient.Gaea
}

func (a BillIssuerAssignee) NeedAssign(credit model.SystemCredit) (bool, error) {
	histories, err := a.historyDao.GetUserSystemCreditHistories(credit.UID, enums.AssignReasonBindFirstCard)
	if err != nil {
		return false, err
	}
	if len(histories) == 0 {
		return false, fmt.Errorf("user %d does not bind any card", credit.UID)
	}
	return true, nil
}

func (a BillIssuerAssignee) Calculate(credit model.SystemCredit) (int64, error) {
	ctx := context.Background()
	merge := false
	start := base.NewMonth(credit.Month.AddDate(0, -2, 0)).String()
	end := base.NewMonth(credit.Month).String()

	param := gaeaOperations.NewGetUserBillBriefListParamsWithContext(ctx)
	param.SetUID(credit.UID)
	param.SetMerge(&merge)
	param.SetStart(&start)
	param.SetEnd(&end)

	billListResp, err := a.gaeaSrv.Operations.GetUserBillBriefList(param)
	if err != nil {
		return 0, err
	}
	if billListResp.GetPayload() == nil {
		return 0, errors.New("payload empty")
	}
	if billListResp.GetPayload().Data == nil {
		return 0, errors.New(billListResp.GetPayload().Message)
	}
	bills := billListResp.GetPayload().Data.Bills
	maxBilling := lo.MaxBy(bills, func(a *gaeaModels.BillBrief, b *gaeaModels.BillBrief) bool {
		if a == nil || b == nil {
			return true
		}
		return a.Money > b.Money
	})
	currentCredit, err := a.dao.GetSystemCredit(credit.UID)
	if err != nil {
		return 0, err
	}
	creditLine := lo.Min([]int64{maxBilling.Money, currentCredit.CreditLine})
	return lo.Clamp(creditLine, a.min, a.max), nil
}

// CardChargeAssignee 会根据用户扣费金额调整用户的系统授信
type CardChargeAssignee struct {
	min        int64
	max        int64
	amount     int64
	historyDao *dao.SystemCreditHistoryDao
}

func (a CardChargeAssignee) NeedAssign(credit model.SystemCredit) (bool, error) {
	// 更新信用额度时扣费金额必然大于等于信用额度的最小值
	// 走到这肯定是不符合条件就调用了
	if a.amount < a.min {
		return false, errors.New("charging amount must be greater than the minimum of credit line")
	}
	histories, err := a.historyDao.GetUserSystemCreditHistories(credit.UID, enums.AssignReasonBindFirstCard)
	if err != nil {
		return false, err
	}
	if len(histories) == 0 {
		return false, fmt.Errorf("user %d does not bind any card", credit.UID)
	}
	return true, nil
}

func (a CardChargeAssignee) Calculate(credit model.SystemCredit) (int64, error) {
	creditHistory, err := a.historyDao.GetLatestSystemCreditHistory(credit.UID)
	if err != nil {
		return 0, err
	}
	creditLine := a.amount * 2
	if creditHistory.AssignReason == enums.AssignReasonChargeCard {
		creditLine = creditHistory.CreditLine + a.amount*2
	}
	return lo.Clamp(creditLine, a.min, a.max), nil
}
