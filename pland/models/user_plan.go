package models

import (
	"context"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/fatih/structs"

	"github.com/qbox/bo-base/v4/dao"
	"qiniu.io/pay/pland/models/enums"
)

// UserPlanDao user plan dao
type UserPlanDao struct {
	base *dao.BaseDao

	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *UserPlanDao) GetCachePrefix() string {
	return "sufy-plan:user-plan:"
}

// GetCacheRefs get cache refs
func (d *UserPlanDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewUserPlanDao new user plan dao
func NewUserPlanDao(base *dao.BaseDao) *UserPlanDao {
	cachePrefix := (*UserPlanDao)(nil).GetCachePrefix()
	return &UserPlanDao{
		base:        base,
		cachePrefix: cachePrefix,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:code={Code}", cachePrefix),
		),
	}
}

type UserPlan struct {
	ID                   uint64          `json:"id" gorm:"primaryKey"`
	Month                time.Time       `json:"month" gorm:"month" sql:"type:DATETIME(6)"`
	UID                  uint64          `json:"uid" gorm:"uid"`
	Version              uint32          `json:"version" gorm:"version"`
	Level                enums.PlanLevel `json:"level" gorm:"level"`
	PacksCode            string          `json:"packs_code" gorm:"packs_code"`
	RemainingSwitchCount uint32          `json:"remaining_switch_count" gorm:"remaining_switch_count"`
	EndTime              time.Time       `json:"end_time" gorm:"end_time" sql:"type:DATETIME(6)"`
	ChangeCode           string          `json:"change_code" gorm:"change_code"`
	PaymentSN            *string         `json:"payment_sn" gorm:"payment_sn"`
	PriceTableID         uint64          `json:"price_table_id" gorm:"price_table_id"`
	UpdatedAt            time.Time       `json:"updated_at" gorm:"updated_at" sql:"type:DATETIME(6)"`
	CreatedAt            time.Time       `json:"created_at" sql:"type:DATETIME(6)"`
}

// GenRightsBindingExcode 生成权益绑定关联关系excode
func (u *UserPlan) GenRightsBindingExcode() string {
	return fmt.Sprintf(
		"plan:%d:%d:%s:%d:%s",
		u.UID,
		u.Month.Unix(),
		u.Level,
		u.Version,
		u.PacksCode,
	)
}

// GetCurrentPlan 查询用户当前订阅的 plan
func (d *UserPlanDao) GetCurrentPlan(
	ctx context.Context,
	uid uint64,
	month time.Time,
) (userPlan *UserPlan, err error) {

	// uid = $uid and month = $month
	cond := squirrel.And{}
	cond = append(cond, squirrel.Eq{
		"uid": uid,
	})
	cond = append(cond, squirrel.Eq{
		"month": month,
	})
	where, args, err := cond.ToSql()
	if err != nil {
		return nil, err
	}
	userPlan = &UserPlan{}
	err = d.base.Model(&UserPlan{}).
		Where(where, args...).
		First(userPlan).
		Error
	if err != nil {
		return nil, err
	}
	return userPlan, nil
}

// List 查询用户 plan 列表
func (d *UserPlanDao) List(
	ctx context.Context,
	uid uint64,
	level enums.PlanLevel,
	month time.Time,
	offset int,
	limit int,
) (plans []*UserPlan, err error) {
	cond := squirrel.And{}
	if uid > 0 {
		cond = append(cond, squirrel.Eq{
			"uid": uid,
		})
	}
	if level != "" {
		cond = append(cond, squirrel.Eq{
			"level": level.String(),
		})
	}
	if !month.IsZero() {
		cond = append(cond, squirrel.Eq{
			"month": month,
		})
	}

	where, args, err := cond.ToSql()
	if err != nil {
		return nil, err
	}

	err = d.base.Model(&UserPlan{}).
		Where(where, args...).
		Offset(offset).
		Limit(limit).
		Find(&plans).
		Error
	if err != nil {
		return nil, err
	}
	return plans, nil
}

// Update update userPlan
func (d *UserPlanDao) Update(
	ctx context.Context,
	usrPlan *UserPlan,
) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		db := d.base.Model(&UserPlan{}).
			Where("id = ?", usrPlan.ID)

		if usrPlan.PaymentSN == nil {
			db = db.Omit("payment_sn")
		}

		return db.Updates(structs.Map(usrPlan)).Error
	}, d.keys, usrPlan, d)
}

// Insert insert a usrPlan
func (d *UserPlanDao) Insert(
	ctx context.Context,
	usrPlan *UserPlan,
) error {
	err := d.base.Model(&UserPlan{}).
		Save(usrPlan).
		Error
	if err != nil {
		return err
	}
	return nil
}
