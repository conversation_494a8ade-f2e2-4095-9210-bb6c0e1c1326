package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/walletd/model"
)

// GetScopeItemGroupMapByID 根据ID获取计费范围-计费项组关系记录
func (s *WalletBizService) GetScopeItemGroupMapByID(
	ctx context.Context,
	id uint64,
) (*model.ScopeItemGroupMap, error) {
	return s.walletDao.ScopeItemGroupMap.GetByID(id, s.cacheExpires)
}

// CreateScopeItemGroupMap 创建获取计费范围-计费项组关系记录
func (s *WalletBizService) CreateScopeItemGroupMap(
	ctx context.Context,
	itemGroupMap *model.ScopeItemGroupMap,
) (*model.ScopeItemGroupMap, error) {
	itemGroupMap.ID = 0
	err := s.walletDao.ScopeItemGroupMap.Save(itemGroupMap, s.cacheExpires)
	return itemGroupMap, err
}

// UpdateScopeItemGroupMapByID 根据ID更新计费范围-计费项组关系记录
func (s *WalletBizService) UpdateScopeItemGroupMapByID(
	ctx context.Context,
	id uint64,
	itemGroupMap *model.ScopeItemGroupMap,
) (*model.ScopeItemGroupMap, error) {
	itemGroupMap.ID = id
	err := s.walletDao.ScopeItemGroupMap.Save(itemGroupMap, s.cacheExpires)
	return itemGroupMap, err
}

// DeleteScopeItemGroupMapByID 根据ID删除计费范围-计费项组关系记录
func (s *WalletBizService) DeleteScopeItemGroupMapByID(
	ctx context.Context,
	id uint64,
) (*model.ScopeItemGroupMap, error) {
	itemGroupMap, err := s.GetScopeItemGroupMapByID(ctx, id)
	if err != nil {
		return nil, errors.Annotate(err, "query scope_item_group_map failed").
			WithField("id", id)
	}
	err = s.walletDao.ScopeItemGroupMap.Delete(itemGroupMap)
	return itemGroupMap, err
}

// DeleteScopeItemGroupMapByScopeID 根据计费范围ID删除计费范围-计费项组关系记录
func (s *WalletBizService) DeleteScopeItemGroupMapByScopeID(
	ctx context.Context,
	scopeID uint64,
) (result []model.ScopeItemGroupMap, err error) {
	offset, limit := 0, 100
	for {
		maps, err := s.ListScopeItemGroupMapsByScopeID(ctx, scopeID, offset, limit)
		if err != nil {
			return nil, errors.Annotate(err, "list scope_item_group_map failed").
				WithField("scope_id", scopeID)
		}
		for _, _map := range maps {
			err = s.walletDao.ScopeItemGroupMap.Delete(&_map)
			if err != nil {
				return nil, err
			}
		}
		result = append(result, maps...)
		if len(maps) < limit {
			break
		}
		offset += limit
	}
	return
}

// ListAllScopeItemGroupMaps 分页列出计费范围-计费项组关系记录
func (s *WalletBizService) ListAllScopeItemGroupMaps(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.ScopeItemGroupMap, error) {
	return s.walletDao.ScopeItemGroupMap.ListAll(offset, limit, s.cacheExpires)
}

// CountAllScopeItemGroupMaps 分页列出计费范围-计费项组关系记录
func (s *WalletBizService) CountAllScopeItemGroupMaps(
	ctx context.Context,
) (uint64, error) {
	return s.walletDao.ScopeItemGroupMap.CountAll(s.cacheExpires)
}

// ListScopeItemGroupMapsByGroupID 根据计费项组ID分页列出计费范围-计费项组关系记录
func (s *WalletBizService) ListScopeItemGroupMapsByGroupID(
	ctx context.Context,
	groupID uint64,
	offset int,
	limit int,
) ([]model.ScopeItemGroupMap, error) {
	return s.walletDao.ScopeItemGroupMap.ListByGroupID(groupID, offset, limit, s.cacheExpires)
}

// CountScopeItemGroupMapsByGroupID 根据计费项组ID分页列出计费范围-计费项组关系记录
func (s *WalletBizService) CountScopeItemGroupMapsByGroupID(
	ctx context.Context,
	groupID uint64,
) (uint64, error) {
	return s.walletDao.ScopeItemGroupMap.CountByGroupID(groupID, s.cacheExpires)
}

// ListScopeItemGroupMapsByScopeID 根据计费范围ID分页列出计费范围-计费项组关系记录
func (s *WalletBizService) ListScopeItemGroupMapsByScopeID(
	ctx context.Context,
	scopeID uint64,
	offset int,
	limit int,
) ([]model.ScopeItemGroupMap, error) {
	return s.walletDao.ScopeItemGroupMap.ListByScopeID(scopeID, offset, limit, s.cacheExpires)
}

// CountScopeItemGroupMapsByScopeID 根据计费范围ID分页列出计费范围-计费项组关系记录
func (s *WalletBizService) CountScopeItemGroupMapsByScopeID(
	ctx context.Context,
	scopeID uint64,
) (uint64, error) {
	return s.walletDao.ScopeItemGroupMap.CountByScopeID(scopeID, s.cacheExpires)
}
