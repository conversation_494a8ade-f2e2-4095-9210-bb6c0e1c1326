package dataplaneModel

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/kirby/model/adminModel"
)

func TestAggSeriesMetadataSharding(t *testing.T) {
	test.RunWithUTCAndCST(t, testAggSeriesMetadataSharding)
}

func testAggSeriesMetadataSharding(t *testing.T, loc *time.Location) {
	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	dao := NewAggSeriesMetadataDao(testWrap.BaseDao())

	testRow := aggSeriesMetadataRow{
		UID:          1234567890,
		Start:        time.Date(2022, 11, 1, 0, 0, 0, 0, loc),
		End:          time.Date(2022, 12, 1, 0, 0, 0, 0, loc),
		ItemCode:     "fusion:transfer:https:ov",
		ZoneCode:     3001,
		AggR:         "QKARbar",
		MeasureUnit:  "B",
		G:            adminModel.Granularity5Min,
		AggSeriesKey: "QKASK01GN9T45K0VY8ZJQHNA82YDJW0",
		CreatedAt:    time.Date(2022, 12, 1, 0, 12, 34, 567890000, loc),
	}

	err = dao.EnsureShard(testRow.Start, loc)
	assert.NoError(t, err)

	// repeated ensure ops shouldn't error out
	err = dao.EnsureShard(testRow.Start, loc)
	assert.NoError(t, err)

	err = dao.GetShard(testRow.Start, loc).Save(&testRow)
	assert.NoError(t, err)

	e, err := dao.GetShard(time.Date(2022, 11, 1, 0, 0, 0, 0, loc), loc).GetByMonthUIDItemZone(
		time.Date(2022, 12, 1, 12, 30, 0, 0, loc),
		time.Date(2022, 11, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"fusion:transfer:https:ov",
		3001,
	)
	assert.NoError(t, err)
	assert.NotNil(t, e)

	e, err = dao.GetShard(time.Date(2022, 11, 1, 0, 0, 0, 0, loc), loc).GetByMonthUIDItemZone(
		time.Date(2022, 12, 1, 12, 30, 0, 0, loc),
		time.Date(2022, 11, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"fusion:transfer:https:ov",
		3002,
	)
	assert.NoError(t, err)
	assert.Nil(t, e)

	e, err = dao.GetShard(time.Date(2022, 11, 1, 0, 0, 0, 0, loc), loc).GetByMonthUIDItemZone(
		time.Date(2022, 12, 1, 12, 30, 0, 0, loc),
		time.Date(2022, 11, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"fusion:transfer:http:ov",
		3001,
	)
	assert.NoError(t, err)
	assert.Nil(t, e)

	e, err = dao.GetShard(time.Date(2022, 11, 1, 0, 0, 0, 0, loc), loc).GetByMonthUIDItemZone(
		time.Date(2022, 12, 1, 12, 30, 0, 0, loc),
		time.Date(2022, 11, 1, 0, 0, 0, 0, loc),
		loc,
		1234567891,
		"fusion:transfer:https:ov",
		3001,
	)
	assert.NoError(t, err)
	assert.Nil(t, e)

	// query with an epoch before creation of the row
	e, err = dao.GetShard(time.Date(2022, 11, 1, 0, 0, 0, 0, loc), loc).GetByMonthUIDItemZone(
		time.Date(2022, 12, 1, 0, 1, 2, 0, loc),
		time.Date(2022, 11, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"fusion:transfer:https:ov",
		3001,
	)
	assert.NoError(t, err)
	assert.Nil(t, e)

	// try to query a non-existent past shard
	e, err = dao.GetShard(time.Date(2022, 10, 1, 0, 0, 0, 0, loc), loc).GetByMonthUIDItemZone(
		time.Date(2022, 11, 10, 0, 0, 0, 0, loc),
		time.Date(2022, 10, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"fusion:transfer:https:ov",
		3001,
	)
	assert.NoError(t, err)
	assert.Nil(t, e)

	// try to query a non-existent future shard
	e, err = dao.GetShard(time.Date(2023, 10, 1, 0, 0, 0, 0, loc), loc).GetByMonthUIDItemZone(
		time.Date(2022, 11, 10, 0, 0, 0, 0, loc),
		time.Date(2023, 10, 1, 0, 0, 0, 0, loc),
		loc,
		1234567890,
		"fusion:transfer:https:ov",
		3001,
	)
	assert.NoError(t, err)
	assert.Nil(t, e)
	// TODO: assert that no DB query has taken place at all
}
