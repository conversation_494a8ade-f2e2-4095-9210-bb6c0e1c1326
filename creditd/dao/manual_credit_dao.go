package dao

import (
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"

	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

// ManualCreditDao is data access object of ManualCredit model
type ManualCreditDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *ManualCreditDao) GetCachePrefix() string {
	return "credit:manual_credit:"
}

// GetCacheRefs get cache refs
func (d *ManualCreditDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewManualCreditDao is constructor of ManualCreditDao
func NewManualCreditDao(base *dao.BaseDao) *ManualCreditDao {
	cachePrefix := (*ManualCreditDao)(nil).GetCachePrefix()
	return &ManualCreditDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// DoTransaction do a transaction
func (d *ManualCreditDao) DoTransaction(fn func(*ManualCreditDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		creditDao := NewManualCreditDao(base)
		return fn(creditDao)
	})
}

// Save inserts or updates a record of ManualCredit by id
func (d *ManualCreditDao) Save(m *model.ManualCredit, expires ...time.Duration) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, m, d)

	if err != nil {
		return errors.Trace(err).WithField("model", m)
	}

	return nil
}

// Update updates a record of ManualCredit by id
func (d *ManualCreditDao) Update(m *model.ManualCredit, expires ...time.Duration) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Model(&model.ManualCredit{}).Updates(value).Find(value).Error
	}, d.keys, m, d)

	if err != nil {
		return errors.Trace(err).WithField("id", m.ID)
	}
	return nil
}

// GetNonTerminalCreditByUIDWithForUpdate selects a record of ManualCredit of status valid or unactivated by uid
// 获取非终结状态的 credit 记录（非终结状态同一时间只允许存在一个）
// WithForUpdate 请结合事务使用，给匹配的行加行锁，直到事务结束再释放锁
func (d *ManualCreditDao) GetNonTerminalCreditByUIDWithForUpdate(uid uint64) (m *model.ManualCredit, err error) {
	m = &model.ManualCredit{UID: uid}
	// nonTerminalStatus := []enums.CreditStatus{enums.StatusValid, enums.StatusUnActivated}
	err = d.base.Set("gorm:query_option", "FOR UPDATE").
		Where("uid = ? and (`status` = 2 or `status` = 3)", uid). //
		First(m).
		Error
	if err != nil {
		return nil, err
	}
	return
}

// GetByUIDParams defines GetByUID interface's params
type GetByUIDParams struct {
	UID    uint64
	Status []enums.CreditStatus
	Offset int
	Limit  int
	Start  *time.Time
	End    *time.Time
}

// GetByUID select a record of ManualCredit by uid
func (d *ManualCreditDao) GetByUID(params *GetByUIDParams, expires ...time.Duration) (list []model.ManualCredit, err error) {
	// 即 uid = ? and status in (?)
	condition := squirrel.And{squirrel.Eq{"uid": params.UID, "status": params.Status}}
	// 即 and created_at >= ? and created_at <?
	condition = append(condition, d.buildTimeCondition(params.Start, params.End))
	conditionStr, args, err := condition.ToSql()
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid":    params.UID,
			"offset": params.Offset,
			"limit":  params.Limit,
		})
	}
	err = d.base.Offset(params.Offset).
		Limit(params.Limit).
		Where(conditionStr, args...).
		Order("created_at DESC").
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid":    params.UID,
			"offset": params.Offset,
			"limit":  params.Limit,
		})
	}
	return
}

// GetByID select a record of ManualCredit by id
func (d *ManualCreditDao) GetByID(id uint64, expires ...time.Duration) (*model.ManualCredit, error) {
	m := &model.ManualCredit{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(m, id).Error
	}, keys, m, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return m, nil
}

// ListAllParams defines ListAll interface's params
type ListAllParams struct {
	Status []enums.CreditStatus
	Offset int
	Limit  int
	Start  *time.Time
	End    *time.Time
}

// ListAll select all records
func (d *ManualCreditDao) ListAll(params *ListAllParams, expires ...time.Duration) (list []model.ManualCredit, err error) {
	// 即 status in (?)
	condition := squirrel.And{squirrel.Eq{"status": params.Status}}
	// 即 and created_at >= ? and created_at <?
	condition = append(condition, d.buildTimeCondition(params.Start, params.End))

	conditionStr, args, err := condition.ToSql()
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": params.Offset,
			"limit":  params.Limit,
			"status": params.Status,
		})
	}
	err = d.base.Offset(params.Offset).Limit(params.Limit).
		Where(conditionStr, args...).
		Order("created_at DESC").
		Find(&list).Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": params.Offset,
			"limit":  params.Limit,
			"status": params.Status,
		})
	}
	return
}

// CountAll select count of all records
func (d *ManualCreditDao) CountAll(status []enums.CreditStatus, start, end *time.Time, expires ...time.Duration) (count uint64, err error) {
	// 即 status in (?)
	condition := squirrel.And{squirrel.Eq{"status": status}}
	// 即 and created_at >= ? and created_at <?
	condition = append(condition, d.buildTimeCondition(start, end))
	conditionStr, args, err := condition.ToSql()
	if err != nil {
		return
	}
	err = d.base.Model(&model.ManualCredit{}).Where(conditionStr, args...).Count(&count).Error
	return
}

// CountAllByUID select count of all records
func (d *ManualCreditDao) CountAllByUID(uid uint64, status []enums.CreditStatus, start, end *time.Time, expires ...time.Duration) (count uint64, err error) {
	// 即 uid = ? and status in (?)
	condition := squirrel.And{squirrel.Eq{"uid": uid, "status": status}}
	// 即 and created_at >= ? and created_at <?
	condition = append(condition, d.buildTimeCondition(start, end))
	conditionStr, args, err := condition.ToSql()
	if err != nil {
		return
	}
	err = d.base.Model(&model.ManualCredit{}).Where(conditionStr, args...).Count(&count).Error
	return
}

// Delete delete a record of ManualCredit
func (d *ManualCreditDao) Delete(m *model.ManualCredit) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, m, d)

	if err != nil {
		return errors.Trace(err).WithField("model", m)
	}
	return nil
}

func (d *ManualCreditDao) buildTimeCondition(start, end *time.Time) squirrel.And {
	andCond := squirrel.And{}
	if start != nil {
		andCond = append(andCond, squirrel.GtOrEq{
			"created_at": start,
		})
	}
	if end != nil {
		andCond = append(andCond, squirrel.Lt{
			"created_at": end,
		})
	}

	return andCond
}
