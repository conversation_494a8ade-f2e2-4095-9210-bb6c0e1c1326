package action

import (
	"context"
	"encoding/json"

	"github.com/qbox/bo-base/v4/eventbus"
	"github.com/qbox/pay-sdk/middleware/logging"

	"qiniu.io/pay/creditd/config"
	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
	"qiniu.io/pay/creditd/service"
)

func (c *CreditAction) EventbusDaemon(ctx context.Context) {
	logger := logging.GetLogger(ctx)
	c.eventContext = ctx
	subscribers := []struct {
		Topic    string
		Queue    string
		Handlers []eventbus.Handler
	}{
		{
			// 用户首次绑卡
			Topic:    eventbus.GaeaUserFirstCardBoundTopic,
			Queue:    eventbus.CreditSystemUpdateOnUserFirstCardBoundQueue,
			Handlers: []eventbus.Handler{eventbus.HandleFunc(c.HandleFirstCardBoundEvent)},
		},
		{
			// 用户更换默认支付方式
			Topic:    eventbus.GaeaUserDefaultCardChangedTopic,
			Queue:    eventbus.CreditSystemUpdateOnUserDefaultCardChangedQueue,
			Handlers: []eventbus.Handler{eventbus.HandleFunc(c.HandleDefaultCardChangedEvent)},
		},
		{
			// 用户额度不足扣费
			Topic:    eventbus.GaeaUserCardChargedTopic,
			Queue:    eventbus.CreditSystemUpdateOnUserCardChargedQueue,
			Handlers: []eventbus.Handler{eventbus.HandleFunc(c.HandleChargeOnInsufficientBalanceEvent)},
		},
		{
			// 出用户月账单
			Topic:    eventbus.WalletMonthstatementGenTopic,
			Queue:    eventbus.CreditSystemUpdateOnMonthstatementGenQueue,
			Handlers: []eventbus.Handler{eventbus.HandleFunc(c.HandleMonthstatementGenEvent)},
		},
	}
	go func() {
		for _, sub := range subscribers {
			if err := c.eventbus.Subscribe(sub.Topic, sub.Queue, sub.Handlers...); err != nil {
				logger.Printf(
					"<EventBusDaemon> subscribe topic: %s queue: %s failed with error: %s",
					sub.Topic, sub.Queue,
					err,
				)
				return
			}
			logger.Printf("subscribe topic %s, queue %s", sub.Topic, sub.Queue)
		}
	}()
}

func (c *CreditAction) HandleFirstCardBoundEvent(message []byte) {
	logger := logging.GetLogger(c.eventContext)
	var event eventbus.UserFirstCardBoundMessage
	err := json.Unmarshal(message, &event)
	if err != nil {
		logger.WithError(err).Error("unmarshal monthstatement gen message failed")
		return
	}
	if event.UID == 0 {
		return
	}
	// 判断用户是否有人工授信
	manualCredits, total, err := c.creditSrv.ManualCredit.GetCreditsByUID(c.eventContext, &service.GetCreditsByUIDParams{
		UID:    event.UID,
		Status: []enums.CreditStatus{enums.StatusValid, enums.StatusUnActivated},
		Offset: 0,
		Limit:  2,
	})
	if err != nil {
		logger.WithError(err).WithField("uid", event.UID).
			Error("<HandleFirstCardBoundEvent>creditSrv.ManualCredit.GetCreditsByUID failed")
		return
	}
	if total == 1 {
		status := manualCredits[0].Status
		// 如果有未激活的人工授信，激活之
		if status == enums.StatusUnActivated {
			_, err := c.creditSrv.ManualCredit.UpdateCreditStatusByUID(c.eventContext, event.UID, enums.EventActivate)
			if err != nil {
				logger.WithError(err).WithField("uid", event.UID).
					Error("<HandleFirstCardBoundEvent>creditSrv.ManualCredit.UpdateCreditStatusByUID failed")
				return
			}
		}
	}
	_, _, err = c.creditSrv.SystemCredit.UpsertSystemCredit(c.eventContext, model.SystemCredit{
		UID:          event.UID,
		AssignReason: enums.AssignReasonBindFirstCard,
	})
	if err != nil {
		logger.WithError(err).WithField("uid", event.UID).
			Error("<HandleFirstCardBoundEvent>creditSrv.SystemCredit.UpsertSystemCredit failed")
		return
	}
	go func() {
		if total == 1 {
			c.notificationSrv.SendInstantNotice(c.eventContext, event.UID,
				enums.EventActivate,
				c.notificationSrv.BuildNotification(config.ActivatedTmplUnit))
			return
		}
		c.notificationSrv.SendInstantNotice(c.eventContext, event.UID,
			enums.EventAdjustLineOrPeriod,
			c.notificationSrv.BuildNotification(config.UpdatedTmplUnit))
	}()
}

func (c *CreditAction) HandleDefaultCardChangedEvent(message []byte) {
	logger := logging.GetLogger(c.eventContext)
	var event eventbus.UserDefaultCardChangedMessage
	err := json.Unmarshal(message, &event)
	if err != nil {
		logger.WithError(err).Error("unmarshal monthstatement gen message failed")
		return
	}
	if event.UID == 0 {
		return
	}
	_, isUpdated, err := c.creditSrv.SystemCredit.UpsertSystemCredit(c.eventContext, model.SystemCredit{
		UID:          event.UID,
		AssignReason: enums.AssignReasonChangeDefaultCard,
	})
	if err != nil {
		logger.WithError(err).WithField("uid", event.UID).
			Error("<HandleDefaultCardChangedEvent>creditSrv.SystemCredit.UpsertSystemCredit failed")
		return
	}
	go func() {
		_, total, err := c.creditSrv.ManualCredit.GetCreditsByUID(c.eventContext, &service.GetCreditsByUIDParams{
			UID:    event.UID,
			Status: []enums.CreditStatus{enums.StatusUnActivated, enums.StatusValid},
			Offset: 0,
			Limit:  1,
		})
		if err != nil {
			return
		}
		if total > 0 || !isUpdated {
			return
		}
		c.notificationSrv.SendInstantNotice(c.eventContext, event.UID,
			enums.EventAdjustLineOrPeriod,
			c.notificationSrv.BuildNotification(config.UpdatedTmplUnit))
	}()
}

func (c *CreditAction) HandleChargeOnInsufficientBalanceEvent(message []byte) {
	logger := logging.GetLogger(c.eventContext)
	var event eventbus.UserCardChargedMessage
	err := json.Unmarshal(message, &event)
	if err != nil {
		logger.WithError(err).Error("unmarshal monthstatement gen message failed")
		return
	}
	if event.UID == 0 || event.Amount == 0 {
		return
	}
	_, isUpdated, err := c.creditSrv.SystemCredit.UpsertSystemCredit(c.eventContext, model.SystemCredit{
		UID:          event.UID,
		AssignReason: enums.AssignReasonChargeCard,
		CreditLine:   event.Amount.ToInt64(),
	})
	if err != nil {
		logger.WithError(err).WithField("uid", event.UID).
			Error("<HandleChargeOnInsufficientBalanceEvent>creditSrv.SystemCredit.UpsertSystemCredit failed")
		return
	}
	go func() {
		_, total, err := c.creditSrv.ManualCredit.GetCreditsByUID(c.eventContext, &service.GetCreditsByUIDParams{
			UID:    event.UID,
			Status: []enums.CreditStatus{enums.StatusUnActivated, enums.StatusValid},
			Offset: 0,
			Limit:  1,
		})
		if err != nil {
			return
		}
		if total > 0 || !isUpdated {
			return
		}
		c.notificationSrv.SendInstantNotice(c.eventContext, event.UID,
			enums.EventAdjustLineOrPeriod,
			c.notificationSrv.BuildNotification(config.UpdatedTmplUnit))
	}()
}

func (c *CreditAction) HandleMonthstatementGenEvent(message []byte) {
	logger := logging.GetLogger(c.eventContext)
	var event eventbus.MonthstatementGenMessage
	err := json.Unmarshal(message, &event)
	if err != nil {
		logger.WithError(err).Error("unmarshal monthstatement gen message failed")
		return
	}
	if event.UID == 0 {
		return
	}
	_, isUpdated, err := c.creditSrv.SystemCredit.UpsertSystemCredit(c.eventContext, model.SystemCredit{
		UID:          event.UID,
		AssignReason: enums.AssignReasonGenMonthStatement,
		Month:        event.Month,
	})
	if err != nil {
		logger.WithError(err).WithField("uid", event.UID).
			Error("<HandleMonthstatementGenEvent>creditSrv.SystemCredit.UpsertSystemCredit failed")
		return
	}
	go func() {
		_, total, err := c.creditSrv.ManualCredit.GetCreditsByUID(c.eventContext, &service.GetCreditsByUIDParams{
			UID:    event.UID,
			Status: []enums.CreditStatus{enums.StatusUnActivated, enums.StatusValid},
			Offset: 0,
			Limit:  1,
		})
		if err != nil {
			return
		}
		if total > 0 || !isUpdated {
			return
		}
		c.notificationSrv.SendInstantNotice(c.eventContext, event.UID,
			enums.EventAdjustLineOrPeriod,
			c.notificationSrv.BuildNotification(config.UpdatedTmplUnit))
	}()
}
