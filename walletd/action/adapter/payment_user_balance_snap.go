package adapter

import (
	"time"

	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/qpay/payment/model"
)

// BuildListUserBalanceSnapshotParam converts pb.ListUserBalanceSnapshotParam to service.ListSnapshotParam
func BuildListUserBalanceSnapshotParam(pbParam *pb.ListUserBalanceSnapshotParam) *model.ListSnapshotParam {
	var startTime *time.Time
	if pbParam.StartTime != nil {
		// FIXME: 此处需要先转换到业务参考时区
		t := pbParam.GetStartTime().AsTime()
		startTime = &t
	}

	var endTime *time.Time
	if pbParam.EndTime != nil {
		// FIXME: 此处需要先转换到业务参考时区
		t := pbParam.GetEndTime().AsTime()
		endTime = &t
	}

	return &model.ListSnapshotParam{
		UID:        pbParam.GetUid(),
		PaymentIDs: pbParam.GetPaymentIds(),
		StartTime:  startTime,
		EndTime:    endTime,
		Offset:     pbParam.GetOffset(),
		Limit:      pbParam.GetLimit(),
	}
}

// BuildPbUserBalanceSnapshot converts []*model.UserBalanceSnap to *pb.UserBalanceSnapshotList
func BuildPbUserBalanceSnapshot(param *model.UserBalanceSnap) *pb.UserBalanceSnapshot {
	return &pb.UserBalanceSnapshot{
		Sn:               param.SN,
		Id:               param.ID,
		Uid:              param.UID,
		CashBalance:      int64(param.CashBalance),
		NiucoinBalance:   int64(param.NiuCoinBalance),
		Arrears:          int64(param.Arrears),
		AvailableBalance: int64(param.AvailableBalance),
		Description:      param.Description,
		PaymentId:        param.PaymentID,
	}
}

// BuildPbUserBalanceSnapshotList converts []*model.UserBalanceSnap to *pb.UserBalanceSnapshotList
func BuildPbUserBalanceSnapshotList(userBalanceSnaps []*model.UserBalanceSnap) *pb.UserBalanceSnapshotList {
	pbUserBalanceSnapshots := make([]*pb.UserBalanceSnapshot, len(userBalanceSnaps))
	for i, snapshot := range userBalanceSnaps {
		pbUserBalanceSnapshots[i] = BuildPbUserBalanceSnapshot(snapshot)
	}
	return &pb.UserBalanceSnapshotList{
		UserBalanceSnapshot: pbUserBalanceSnapshots,
	}
}
