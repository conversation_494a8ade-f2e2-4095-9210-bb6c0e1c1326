CREATE TABLE `plans`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `level`         varchar(20)         NOT NULL DEFAULT '' COMMENT '等级',
    `version`       int unsigned        NOT NULL DEFAULT 0 COMMENT '版本',
    `description`   varchar(2000)       NOT NULL DEFAULT '' COMMENT '描述',
    `price`         decimal(20, 8)      NOT NULL DEFAULT '0' COMMENT '价格',
    `currency_type` varchar(10)         NOT NULL DEFAULT 'USD' COMMENT '价格币种',
    `status`        tinyint unsigned    NOT NULL DEFAULT 1 COMMENT '状态,1:在线2:下线',
    `effect_time`   bigint(20)          NOT NULL DEFAULT '0' COMMENT '开始时间',
    `timer_flag`    tinyint unsigned    NOT NULL DEFAULT 0 COMMENT '是否开启定时生效',
    `comment`       varchar(1000)       NOT NULL DEFAULT '' COMMENT '备注',
    `created_at`    datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '创建时间',
    `packs`         varchar(5000)       NOT NULL DEFAULT '{}' COMMENT '套餐信息',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uniq_code_version` (`level`, `version`),
    KEY `idx_status` (`status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `user_plans`
(
    `id`                     bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `month`                  datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '订阅月份',
    `uid`                    bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT 'uid',
    `version`                int unsigned        NOT NULL DEFAULT 0 COMMENT '版本',
    `level`                  varchar(20)         NOT NULL DEFAULT '' COMMENT '等级',
    `packs_code`             varchar(32)         NOT NULL DEFAULT '' COMMENT 'packs.code',
    `remaining_switch_count` tinyint unsigned    NOT NULL DEFAULT 0 COMMENT '剩余切换次数',
    `change_code`            varchar(32)         NOT NULL DEFAULT '' COMMENT 'changes.code',
    `payment_sn`             varchar(32)         DEFAULT '' COMMENT 'payment_sn',
    `price_table_id`         bigint(20)          DEFAULT 0  COMMENT '折扣权益价格表 id',
    `end_time`               datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '结束时间',
    `updated_at`             datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '更新时间',
    `created_at`             datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uniq_uid_month` (`uid`, `month`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `user_subscribes`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `month`         datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '订阅月份',
    `uid`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT 'uid',
    `level`         varchar(20)         NOT NULL DEFAULT '' COMMENT '等级',
    `packs_code`    varchar(32)         NOT NULL DEFAULT '' COMMENT 'packs.code',
    `status`        tinyint unsigned    NOT NULL DEFAULT 0 COMMENT '订阅状态',
    `subscribe_src` varchar(20)         NOT NULL DEFAULT '' COMMENT '订阅来源',
    `updated_at`    datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '更新时间',
    `created_at`    datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uniq_uid_month` (`uid`, `month`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `user_plan_changes`
(
    `id`                bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `code`              varchar(32)         NOT NULL DEFAULT '' COMMENT '记录编号',
    `change_type`       tinyint unsigned    NOT NULL DEFAULT 0 COMMENT '变更类型',
    `uid`               bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT 'uid',
    `version`           int unsigned                 DEFAULT 0 COMMENT '当前版本,初次订阅,可能为空',
    `level`             varchar(20)                  DEFAULT '' COMMENT '当前等级,初次订阅,可能为空',
    `packs_code`        varchar(32)                  DEFAULT '' COMMENT '当前packs.code,初次订阅,可能为空',
    `target_version`    int unsigned        NOT NULL DEFAULT 0 COMMENT '目标版本',
    `target_level`      varchar(20)         NOT NULL DEFAULT '' COMMENT '目标等级',
    `target_packs_code` varchar(32)         NOT NULL DEFAULT '' COMMENT '目标packs.code',
    `actual_version`    int unsigned        NOT NULL DEFAULT 0 COMMENT '实际版本',
    `actual_level`      varchar(20)         NOT NULL DEFAULT '' COMMENT '实际等级',
    `actual_packs_code` varchar(32)         NOT NULL DEFAULT '' COMMENT '实际packs.code',
    `effect_month`      datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '生效月份',
    `payment_status`    tinyint unsigned    NOT NULL DEFAULT 0 COMMENT '支付状态',
    `currency_type`     varchar(10)         NOT NULL DEFAULT 'USD' COMMENT '支付币种',
    `payment_sn`        varchar(32)         DEFAULT '' COMMENT '支付的变更的那条 changes.code',
    `payment_amount`    decimal(20, 8)      NOT NULL DEFAULT '0' COMMENT '支付金额',
    `payment_time`      datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '支付时间',
    `refund_time`       datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '退款时间',
    `updated_at`        datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '更新时间',
    `created_at`        datetime(6)         NOT NULL DEFAULT '0000-01-01 00:00:00.000000' COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_uid` (`uid`),
    KEY `idx_code` (`code`),
    KEY `idx_change_type` (`change_type`),
    KEY `idx_payment_status` (`payment_status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4;
