package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
)

// BuildPbCouponCash converts model.CouponCash to the protobuf type.
func BuildPbCouponCash(cash *model.CouponCash) (*pb.CouponCash, error) {
	return &pb.CouponCash{
		Id:          cash.ID,
		Code:        cash.Code,
		Type:        cash.Type,
		ScopeId:     cash.ScopeID,
		Name:        cash.Name,
		Description: cash.Description,
		Remark:      cash.Remark,
		Money:       cash.Money,
		EffectTime:  timestamppb.New(cash.EffectTime.TimeIn(time.UTC)),
		DeadTime:    timestamppb.New(cash.DeadTime.TimeIn(time.UTC)),
		CreatedAt:   timestamppb.New(cash.CreatedAt),
		UpdatedAt:   timestamppb.New(cash.UpdatedAt),
	}, nil
}

// BuildCouponCash converts from pb.CouponCash to the model layer type.
func BuildCouponCash(cash *pb.CouponCash) (*model.CouponCash, error) {
	err := cash.GetEffectTime().CheckValid()
	if err != nil {
		return nil, err
	}
	effectTime := cash.GetEffectTime().AsTime()

	err = cash.GetDeadTime().CheckValid()
	if err != nil {
		return nil, err
	}
	deadTime := cash.GetDeadTime().AsTime()

	var createdAt, updatedAt time.Time
	if cash.GetCreatedAt() != nil {
		err = cash.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = cash.GetCreatedAt().AsTime()
	}

	if cash.GetUpdatedAt() != nil {
		err = cash.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = cash.GetUpdatedAt().AsTime()
	}

	return &model.CouponCash{
		ID:          cash.GetId(),
		Code:        cash.GetCode(),
		Type:        cash.GetType(),
		ScopeID:     cash.GetScopeId(),
		Name:        cash.GetName(),
		Description: cash.GetDescription(),
		Remark:      cash.GetRemark(),
		Money:       cash.GetMoney(),
		EffectTime:  base.NewHNS(effectTime),
		DeadTime:    base.NewHNS(deadTime),
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
	}, nil
}
