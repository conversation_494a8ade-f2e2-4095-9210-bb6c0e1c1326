package dataplane

import (
	"context"
	"time"

	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/dataplaneModel"
	"qiniu.io/pay/kirby/service/dataplane/common"
)

// pogDataplaneImpl is the dataplane implementation entirely written in "plain old Go".
type pogDataplaneImpl struct {
	rtdm  *rtdMatcher
	aggrm *aggregationRuleMatcher
	eng   *pogDataplaneEngine
}

var _ IKirbyDataplane = (*pogDataplaneImpl)(nil)

func NewPogDataplaneImpl(
	ctx context.Context,
	store common.IDataStore,
	maxNumSeriesBeforeFlush int,
	debug bool,
) (IKirbyDataplane, error) {
	engine := newPogDataplaneEngine(ctx, store, maxNumSeriesBeforeFlush, debug)
	go engine.run()

	return &pogDataplaneImpl{
		rtdm:  newRTDMatcher(),
		aggrm: newAggregationRuleMatcher(),
		eng:   engine,
	}, nil
}

// DetachAndWait 通知数据面实现，本服务进程的当前连接即将结束，等待必要的清理工作（如有）完成后返回
func (x *pogDataplaneImpl) DetachAndWait() {
	x.eng.stop()
}

// RefreshRTDs 通知数据面实现，刷新 RTD 相关状态、缓存等
func (x *pogDataplaneImpl) RefreshRTDs(
	n []*adminModel.ResourceTypeDefinitionDetail,
) {
	x.rtdm.refreshCachedDefs(n)
}

// RefreshAggregationRules 通知数据面实现，刷新聚合规则相关状态、缓存等
func (x *pogDataplaneImpl) RefreshAggregationRules(
	n []*adminModel.AggregationRule,
) {
	x.aggrm.refreshCachedDefs(n)
}

func (x *pogDataplaneImpl) MakeBundleProcessCtx(
	ctx context.Context,
	cm pb.CompressionMethod,
) (*BundleProcessCtx, error) {
	return newBundleProcessCtx(cm, x.rtdm, x.aggrm, x.eng.bundleProcessCtxCallback)
}

func (x *pogDataplaneImpl) RunAggregations(
	ctx context.Context,
	month time.Time,
	since time.Time,
	epoch time.Time,
) error {
	_ = x.eng.store.RunAggregations(ctx, month, since, epoch)
	return nil
}

func (x *pogDataplaneImpl) ProvisionShards(
	ctx context.Context,
	month time.Time,
) error {
	return x.eng.store.ProvisionShards(ctx, month)
}

func (x *pogDataplaneImpl) ListSeries(
	ctx context.Context,
	req *dataplaneModel.ReqListSeries,
) (*dataplaneModel.SeriesDetailList, error) {
	return x.eng.store.ListSeries(ctx, req)
}

func (x *pogDataplaneImpl) ListSeriesDistinctUIDs(
	ctx context.Context,
	req *dataplaneModel.ReqListSeries,
) ([]uint64, error) {
	return x.eng.store.ListSeriesDistinctUIDs(ctx, req)
}

func (x *pogDataplaneImpl) ListAggregatedSeries(
	ctx context.Context,
	req *dataplaneModel.ReqListAggregatedSeries,
) (*dataplaneModel.AggregatedSeriesDetailList, error) {
	return x.eng.store.ListAggregatedSeries(ctx, req)
}

func (x *pogDataplaneImpl) ListAggregatedSeriesDistinctUIDs(
	ctx context.Context,
	req *dataplaneModel.ReqListAggregatedSeries,
) ([]uint64, error) {
	return x.eng.store.ListAggregatedSeriesDistinctUIDs(ctx, req)
}

func (x *pogDataplaneImpl) QueryAggregatedSeriesData(
	ctx context.Context,
	uid uint64,
	month time.Time,
	itemCode string,
	zoneCode int64,
	dataType string,
	desiredG adminModel.Granularity,
	epoch time.Time,
) ([]*pb.StatPoint, error) {
	return x.eng.store.QueryAggregatedSeriesData(
		ctx,
		uid,
		month,
		itemCode,
		zoneCode,
		desiredG,
		epoch,
	)
}

func (x *pogDataplaneImpl) ListItemUIDsByMonthAndZone(
	ctx context.Context,
	itemCodes []string,
	month time.Time,
	zoneCode int64,
	epoch time.Time,
) ([]uint64, error) {
	return x.eng.store.ListItemUIDsByMonthAndZone(
		ctx,
		itemCodes,
		month,
		zoneCode,
		epoch,
	)
}

func (x *pogDataplaneImpl) ListUserItemsByMonthAndZone(
	ctx context.Context,
	uid uint64,
	month time.Time,
	zoneCode int64,
	epoch time.Time,
) ([]string, error) {
	return x.eng.store.ListUserItemsByMonthAndZone(
		ctx,
		uid,
		month,
		zoneCode,
		epoch,
	)
}
