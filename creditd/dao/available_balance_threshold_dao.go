package dao

import (
	"fmt"

	"qiniu.io/pay/creditd/model/enums"

	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/creditd/model"
)

// AvailableBalanceThresholdDao is data access object of AvailableBalanceThreshold model
type AvailableBalanceThresholdDao struct {
	base *dao.BaseDao
}

// NewAvailableBalanceThresholdDao is constructor of AvailableBalanceThresholdDao
func NewAvailableBalanceThresholdDao(base *dao.BaseDao) *AvailableBalanceThresholdDao {
	return &AvailableBalanceThresholdDao{
		base: base,
	}
}

// DoTransaction do a transaction
func (d *AvailableBalanceThresholdDao) DoTransaction(fn func(*AvailableBalanceThresholdDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		thresholdDao := NewAvailableBalanceThresholdDao(base)
		return fn(thresholdDao)
	})
}

// RegisterMigrateThreshold migrates availableBalanceThresholdDao model
func RegisterMigrateThreshold(db *gorm.DB) {
	db.AutoMigrate(&model.AvailableBalanceThreshold{})
}

// Save inserts or updates a record of Credit by id
func (d *AvailableBalanceThresholdDao) Save(m *model.AvailableBalanceThreshold) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.Execute(func(value any) error {
		return d.base.Table(m.GetTableName()).Save(value).Error
	}, m)

	if err != nil {
		return errors.Trace(err).WithField("model", m)
	}

	return nil
}

// Update updates a record of AvailableBalanceThreshold by id
func (d *AvailableBalanceThresholdDao) Update(m *model.AvailableBalanceThreshold) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.Execute(func(value any) error {
		return d.base.Table(m.GetTableName()).Model(&model.AvailableBalanceThreshold{}).Updates(value).Find(value).Error
	}, m)

	if err != nil {
		return errors.Trace(err).WithField("id", m.ID)
	}
	return nil
}

// GetByUIDWithForUpdate selects a record of AvailableBalanceThreshold by uid with for update
// 结合事务使用，对 uid，status 索引加锁，事务结束释放锁，（普通查询请使用非 WithForUpdate 版本）
func (d *AvailableBalanceThresholdDao) GetByUIDWithForUpdate(uid uint64, isManager bool) (*model.AvailableBalanceThreshold, error) {
	m := &model.AvailableBalanceThreshold{UID: uid, IsManager: isManager}
	err := d.base.Table(m.GetTableName()).Set("gorm:query_option", "FOR UPDATE").
		Where("uid = ? and status = ?", uid, enums.StatusThresholdEnabled).
		First(m).
		Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

// GetByUID selects a record of AvailableBalanceThreshold by uid
func (d *AvailableBalanceThresholdDao) GetByUID(uid uint64, isManager bool) (*model.AvailableBalanceThreshold, error) {
	m := &model.AvailableBalanceThreshold{UID: uid, IsManager: isManager}
	err := d.base.Execute(func(value any) error {
		return d.base.Table(m.GetTableName()).
			Where("uid = ? and status = ?", uid, enums.StatusThresholdEnabled).
			Find(value).
			Error
	}, m)
	if err != nil {
		return nil, err
	}
	return m, nil
}

// ListByUID selects records of AvailableBalanceThreshold by uid
func (d *AvailableBalanceThresholdDao) ListByUID(uid uint64, isManager bool) ([]model.AvailableBalanceThreshold, error) {
	m := make([]model.AvailableBalanceThreshold, 0)
	err := d.base.Execute(func(value any) error {
		return d.base.Table(
			(&model.AvailableBalanceThreshold{IsManager: isManager}).GetTableName(),
		).
			Where("uid = ?", uid).
			Find(value).
			Error
	}, &m)
	if err != nil {
		return nil, err
	}
	return m, nil
}

// GetByStatus selects records of AvailableBalanceThreshold by status
func (d *AvailableBalanceThresholdDao) GetByStatus(status enums.ThresholdStatus, offset, limit int, isManager bool) (thresholds []model.AvailableBalanceThreshold, err error) {
	thresholds = make([]model.AvailableBalanceThreshold, 0)

	err = d.base.Execute(func(value any) error {
		return d.base.Table(
			(&model.AvailableBalanceThreshold{IsManager: isManager}).GetTableName(),
		).
			Where("status = ?", status).
			Offset(offset).
			Limit(limit).
			Find(value).
			Error
	}, &thresholds)

	if err != nil {
		return nil, err
	}
	return thresholds, nil
}

// CountByStatus counts number by status
func (d *AvailableBalanceThresholdDao) CountByStatus(status enums.ThresholdStatus, isManager bool) (count uint64, err error) {
	m := &model.AvailableBalanceThreshold{IsManager: isManager}
	err = d.base.Table(m.GetTableName()).
		Model(m).
		Where("status = ?", status).
		Count(&count).
		Error

	if err != nil {
		return
	}
	return count, nil
}
