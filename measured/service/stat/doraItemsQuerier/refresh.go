package doraItemsQuerier

import (
	"context"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/qbox/bo-base/v4/legacystat/common"

	"qiniu.io/pay/measured/model"
)

// Dora cmd 映射关系缓存的有效时间
//
// 默认取 6hr，这样既不会太久，也可以确保极端情况下（1 号凌晨 2:00 预估账单刷新）大概率不会取到旧数据
// 因为很高概率上个月月末 20:00 之前这个月的计费项变更已经对接完成了
const cacheDuration = 6 * time.Hour

const refreshTimeWindow = 30 * time.Minute

func (c *querierImpl) ensureCache(ctx context.Context) (doraCmdsItemsDict, error) {
	c.cacheMu.Lock()
	defer c.cacheMu.Unlock()

	if c.shouldRefreshCache() {
		err := c.RefreshCache(ctx)
		if err != nil {
			return nil, err
		}
	}

	return c.dict, nil
}

// RefreshCache 刷新 Dora cmd 映射关系缓存
func (c *querierImpl) RefreshCache(ctx context.Context) error {
	newDict, err := c.queryAndMakeDoraCmdsItemsDict(ctx)
	if err != nil {
		return err
	}

	c.dict = newDict
	c.cacheExpires = time.Now().Add(cacheDuration)

	return nil
}

func (c *querierImpl) shouldRefreshCache() bool {
	if c.dict == nil {
		// no data yet
		return true
	}

	// 提前一个时间窗口刷新
	return time.Now().Add(refreshTimeWindow).After(c.cacheExpires)
}

func (c *querierImpl) queryAndMakeDoraCmdsItemsDict(ctx context.Context) (doraCmdsItemsDict, error) {
	// NOTE: 迄今 Dora 计费项都不会有多个 ItemDataType，因此拉量参数只会在 item_extra_args 表
	// 因此此处可以只查询 item IDs 即可
	// 如果某一天 Dora 有计费项支持多种数据类型了，拉量参数还不一定一样的话，此处要改（需要再从 dictd 拉到 ItemDataType 的 IDs）
	doraItemIDs, err := c.dao.ItemStatConfig.ListItemIDsByStatSrcType(uint64(common.V3StatSrcDora))
	if err != nil {
		return nil, err
	}
	if len(doraItemIDs) == 0 {
		return doraCmdsItemsDict{}, nil
	}

	var eg errgroup.Group

	var itemMetadataDict map[uint64]itemEntry
	eg.Go(func() error {
		resp, err := queryItemMetadataDict(ctx, c.dictClient, doraItemIDs)
		if err != nil {
			return err
		}
		itemMetadataDict = resp
		return nil
	})

	var itemExtraArgs []model.ItemExtraArg
	eg.Go(func() error {
		resp, err := c.dao.ItemExtraArg.ListByItemIDs(doraItemIDs, 0, -1)
		if err != nil {
			return err
		}
		itemExtraArgs = resp
		return nil
	})

	err = eg.Wait()
	if err != nil {
		return nil, err
	}

	itemCmdsIndex, err := itemCmdsMapFromItemExtraArgs(itemExtraArgs, itemMetadataDict)
	if err != nil {
		return nil, err
	}

	return doraCmdsItemsDictFromItemCmdsMap(itemCmdsIndex), nil
}
