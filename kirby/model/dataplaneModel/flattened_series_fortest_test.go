package dataplaneModel

import (
	"bytes"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/test"
	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
)

func TestFlattenedSeriesForTestRoundTrip(t *testing.T) {
	test.RunWithUTCAndCST(t, testFlattenedSeriesForTestRoundTrip)
}

func testFlattenedSeriesForTestRoundTrip(t *testing.T, loc *time.Location) {
	obj := FlattenedSeries{
		QRN: "qrn:kodo:z0:1380469261:bucket/timezone-bucket01-z0-kodo-qa",
		MM:  "",
		MergedProps: map[string]string{
			"countKind":    "put",
			"ftype":        "STANDARD",
			"$qrnRegion":   "z0",
			"$qrnResource": "bucket/timezone-bucket01-z0-kodo-qa",
		},
		MBUUID: "QKMB01H0FDBCRM7ZH6AH4FKC6MW53V",
		Start:  time.Date(2023, 5, 13, 0, 0, 0, 0, loc),
		End:    time.Date(2023, 5, 14, 0, 0, 0, 0, loc),
		Data: []*pb.StatPoint{
			{Time: timestamppb.New(time.Date(2023, 5, 13, 2, 10, 0, 0, loc)), Value: 61},
		},
		RTDCode:     "qrtd:kodo:bucket:v202305",
		AggRCode:    "QKAR01GZK5G8X7BJ828V1WWF3AV529",
		MeasureUnit: "1",
		G:           adminModel.Granularity5Min,
		UID:         1380469261,
		ItemCode:    "api_put",
		ZoneCode:    0,
	}

	str, err := obj.MarshalAsTestFixture(loc)
	assert.NoError(t, err)
	assert.Equal(
		t,
		`{"qrn":"qrn:kodo:z0:1380469261:bucket/timezone-bucket01-z0-kodo-qa",`+
			`"mm":"",`+
			`"mp":{"$qrnRegion":"z0","$qrnResource":"bucket/timezone-bucket01-z0-kodo-qa","countKind":"put","ftype":"STANDARD"},`+
			`"mbu":"QKMB01H0FDBCRM7ZH6AH4FKC6MW53V",`+
			`"s":20230513000000,`+
			`"e":20230514000000,`+
			`"d":[{"t":20230513021000,"v":61}],`+
			`"rtd":"qrtd:kodo:bucket:v202305",`+
			`"aggr":"QKAR01GZK5G8X7BJ828V1WWF3AV529",`+
			`"mu":"1",`+
			`"g":2,`+
			`"uid":1380469261,`+
			`"item":"api_put",`+
			`"zone":0}`,
		str,
	)

	b := bytes.NewBuffer([]byte(str))
	objs2 := loadFlattenedSeriesForTest(b, loc)
	assert.Len(t, objs2, 1)
	assert.Equal(t, &obj, objs2[0])
}
