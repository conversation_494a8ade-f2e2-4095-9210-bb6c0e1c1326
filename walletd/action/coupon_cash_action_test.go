package action_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/pay-sdk/wallet"
)

func TestCouponCashInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	walletClient := sandbox.walletClient

	const scopeID uint64 = 1
	cs := []wallet.CouponCash{
		{
			Code:        "foo",
			Type:        "RECHARGE",
			ScopeId:     scopeID,
			Name:        "现金抵用券1元",
			Description: "赠送现金抵用券1元",
			Remark:      "xx故障赔付",
			Money:       10000,
			EffectTime:  timestamppb.Now(),
			DeadTime:    timestamppb.Now(),
		},
		{
			Code:        "bar",
			Type:        "RECHARGE",
			ScopeId:     scopeID,
			Name:        "现金抵用券2元",
			Description: "赠送现金抵用券2元",
			Remark:      "xx故障赔付",
			Money:       20000,
			EffectTime:  timestamppb.Now(),
			DeadTime:    timestamppb.Now(),
		},
		{
			Code:        "baz",
			Type:        "RECHARGE",
			ScopeId:     scopeID,
			Name:        "现金抵用券3元",
			Description: "赠送现金抵用券3元",
			Remark:      "xx故障赔付",
			Money:       30000,
			EffectTime:  timestamppb.Now(),
			DeadTime:    timestamppb.Now(),
		},
	}
	assertFields := func(cash *wallet.CouponCash, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(cs) }) {
			assert.Equal(t, cs[n].GetCode(), cash.GetCode(), msgAndArgs...)
			assert.Equal(t, cs[n].GetType(), cash.GetType(), msgAndArgs...)
			assert.Equal(t, cs[n].GetScopeId(), cash.GetScopeId(), msgAndArgs...)
			assert.Equal(t, cs[n].GetName(), cash.GetName(), msgAndArgs...)
			assert.Equal(t, cs[n].GetDescription(), cash.GetDescription(), msgAndArgs...)
			assert.Equal(t, cs[n].GetRemark(), cash.GetRemark(), msgAndArgs...)
			assert.Equal(t, cs[n].GetMoney(), cash.GetMoney(), msgAndArgs...)
		}
	}
	ids := make(map[uint64]int, len(cs)+1)
	{
		for n, c := range cs {
			cash, err := walletClient.CreateCouponCash(context.Background(), &c)
			if assert.NoError(t, err, "CreateCouponCash") {
				assert.NotZero(t, cash.GetId(), "CreateCouponCash")
				ids[cash.GetId()] = n
			}

			cash, err = walletClient.GetCouponCashByID(context.Background(), &wallet.IDParam{Id: cash.GetId()})
			if assert.NoError(t, err, "GetCouponCashByID") {
				assertFields(cash, n, "GetCouponCashByID")
			}

			cash, err = walletClient.GetCouponCashByCode(context.Background(), &wallet.CodeParam{Code: cash.GetCode()})
			if assert.NoError(t, err, "GetCouponCashByCode") {
				assertFields(cash, n, "GetCouponCashByCode")
			}
		}

		count, err := walletClient.CountAllCouponCash(context.Background(), &emptypb.Empty{})
		if assert.NoError(t, err, "CountAllCouponCash") {
			assert.Len(t, cs, int(count.GetCount()), "CountAllCouponCash")
		}
	}

	list, err := walletClient.ListAllCouponCash(context.Background(), &wallet.PagingParam{Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListAllCouponCash") {
		assert.Len(t, cs, int(list.GetCount()), "ListAllCouponCash")
		for _, cash := range list.GetCouponCash() {
			if assert.Contains(t, ids, cash.GetId(), "ListAllCouponCash") {
				assertFields(cash, ids[cash.GetId()], "ListAllCouponCash")
			}
		}
	}
	{
		list, err := walletClient.ListCouponCashByScopeID(context.Background(), &wallet.IDPagingParam{Id: scopeID, PageSize: 10, Page: 1})
		if assert.NoError(t, err, "ListCouponCashByScopeID") {
			assert.Len(t, cs, int(list.GetCount()), "ListCouponCashByScopeID")
			for _, cash := range list.GetCouponCash() {
				if assert.Contains(t, ids, cash.GetId(), "ListCouponCashByScopeID") {
					assertFields(cash, ids[cash.GetId()], "ListCouponCashByScopeID")
				}
			}
		}
		count, err := walletClient.CountCouponCashByScopeID(context.Background(), &wallet.IDParam{Id: scopeID})
		if assert.NoError(t, err, "CountCouponCashByScopeID") {
			assert.Len(t, cs, int(count.GetCount()), "CountCouponCashByScopeID")
		}
	}
	for id, n := range ids {
		cash, err := walletClient.GetCouponCashByID(context.Background(), &wallet.IDParam{Id: id})
		if assert.NoError(t, err, "GetCouponCashByID") {
			assertFields(cash, n, "GetCouponCashByID")
		}

		cs[n].Name += cash.GetRemark()
		cash.Name += cash.GetRemark()
		cash, err = walletClient.UpdateCouponCashByID(context.Background(), &wallet.IDCouponCashParam{Id: cash.GetId(), CouponCash: cash})
		if assert.NoError(t, err, "UpdateCouponCashByID") {
			assert.Equal(t, cs[n].GetName(), cash.GetName(), "UpdateCouponCashByID")
		}

		cs[n].Name += cash.GetRemark()
		cash.Name += cash.GetRemark()
		cash, err = walletClient.UpdateCouponCashByCode(context.Background(), &wallet.CodeCouponCashParam{Code: cash.GetCode(), CouponCash: cash})
		if assert.NoError(t, err, "UpdateCouponCashByCode") {
			assert.Equal(t, cs[n].GetName(), cash.GetName(), "UpdateCouponCashByCode")
		}
	}
	for id, n := range ids {
		if n%2 == 0 {
			cash, err := walletClient.DeleteCouponCashByID(context.Background(), &wallet.IDParam{Id: id})
			if assert.NoError(t, err, "DeleteCouponCashByID") {
				assertFields(cash, n, "DeleteCouponCashByID")
			}
		} else {
			cash, err := walletClient.DeleteCouponCashByCode(context.Background(), &wallet.CodeParam{Code: cs[n].GetCode()})
			if assert.NoError(t, err, "DeleteCouponCashByCode") {
				assertFields(cash, n, "DeleteCouponCashByCode")
			}
		}
	}
}
