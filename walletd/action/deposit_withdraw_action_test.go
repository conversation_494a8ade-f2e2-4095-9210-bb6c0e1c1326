package action

import (
	"context"
	"testing"

	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/qpay/common"
	"qiniu.io/pay/qpay/enums"
)

func TestPaymentAction_DepositCash(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  10000,
			Desc:   "测试充值一笔,今年发大财",
			Excode: common.GetUUID(),
		},
	}

	ctx := context.Background()
	_, err := sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	wallets, err := sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYCash, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 1, len(wallets))
	wallet := wallets[0]
	assert.EqualValues(t, 10000, wallet.Amount)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallet.AssetType)
	assert.EqualValues(t, sandbox.UID, wallet.UID)

	// 参数校验 excode == ""
	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  100,
			Desc:   "测试,充值一笔",
			Excode: "",
		},
	}
	_, err = sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.Error(t, err, "Excode 为空,应该报错")

	// 参数校验 Money < 0
	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  -100,
			Desc:   "测试,充值一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err = sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.Error(t, err, "Money 小于0,应该报错")

	// 参数校验 uid == 0
	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    0,
			Money:  100,
			Desc:   "测试,充值一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err = sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.Error(t, err, "Uid 为0,应该报错")
}

func prepareCurrency(t *testing.T, sandbox *TestSandbox, uid uint64) {
	currency, err := sandbox.Factory.CurrencyService.AddCurrency(context.Background(), uid, string(enums.CurrencyTypeCNY), true)
	assert.NoError(t, err)
	assert.NotNil(t, currency)
	assert.Equal(t, currency.UID, uid)
}

func TestPaymentAction_DepositNiuCoin(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)
	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  500,
			Desc:   "测试充值一笔,今年发大财",
			Excode: common.GetUUID(),
		},
	}

	ctx := context.Background()
	_, err := sandbox.PaymentAction.DepositNiuCoin(ctx, &request)
	assert.NoError(t, err)

	wallets, err := sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYNiuCoin, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 1, len(wallets))
	wallet := wallets[0]
	assert.EqualValues(t, 500, wallet.Amount)
	assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallet.AssetType)
	assert.EqualValues(t, sandbox.UID, wallet.UID)

	// 参数校验 excode == ""
	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  100,
			Desc:   "测试,充值一笔",
			Excode: "",
		},
	}
	_, err = sandbox.PaymentAction.DepositNiuCoin(ctx, &request)
	assert.Error(t, err, "Excode 为空,应该报错")

	// 参数校验 Money < 0
	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  -100,
			Desc:   "测试,充值一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err = sandbox.PaymentAction.DepositNiuCoin(ctx, &request)
	assert.Error(t, err, "Money 小于0,应该报错")

	// 参数校验 uid == 0
	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    0,
			Money:  100,
			Desc:   "测试,充值一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err = sandbox.PaymentAction.DepositNiuCoin(ctx, &request)
	assert.Error(t, err, "Uid 为0,应该报错")
}

func TestPaymentAction_DesignatedWithdraw_WithdrawCash(t *testing.T) {
	sandbox := BuildSandbox(t)

	prepareCurrency(t, sandbox, sandbox.UID)

	ctx := context.Background()
	paymentAction := sandbox.PaymentAction

	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  1000,
			Desc:   "测试充值一笔,今年发大财1",
			Excode: common.GetUUID(),
		},
	}
	_, err := paymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2000,
			Desc:   "测试充值一笔,今年发大财2",
			Excode: common.GetUUID(),
		},
	}
	pt2, err := paymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  3000,
			Desc:   "测试充值一笔,今年发大财3",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	paymentID := pt2.PaymentId

	wallets, err := sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYCash, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 3, len(wallets))

	assert.EqualValues(t, 1000, wallets[0].Amount)
	assert.EqualValues(t, 1000, wallets[0].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[0].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[0].UID)

	assert.EqualValues(t, 2000, wallets[1].Amount)
	assert.EqualValues(t, 2000, wallets[1].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[1].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[1].UID)

	assert.EqualValues(t, 3000, wallets[2].Amount)
	assert.EqualValues(t, 3000, wallets[2].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[2].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[2].UID)
	// case2:现金充足情况下,提现一笔现金
	wRequest := pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:                         sandbox.UID,
			Money:                       500,
			Desc:                        "提现一笔",
			Excode:                      common.GetUUID(),
			Type:                        enums.T_PRESENT_WITHDRAW,
			DesignatedWithdrawPaymentId: paymentID,
		},
	}
	_, err = paymentAction.WithdrawCash(ctx, &wRequest)
	assert.NoError(t, err)

	wallets, err = sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYCash, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 3, len(wallets))

	assert.EqualValues(t, 1000, wallets[0].Amount)
	assert.EqualValues(t, 1000, wallets[0].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[0].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[0].UID)

	assert.EqualValues(t, 2000, wallets[1].Amount)
	assert.EqualValues(t, 1500, wallets[1].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[1].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[1].UID)

	assert.EqualValues(t, 3000, wallets[2].Amount)
	assert.EqualValues(t, 3000, wallets[2].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[2].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[2].UID)

	wts, err := sandbox.Factory.GetWalletFactory().WalletService.QueryTransactionByUID(ctx, sandbox.UID)
	assert.NoError(t, err)
	assert.EqualValues(t, 4, len(wts))

	assert.EqualValues(t, 1000, wts[0].Money)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wts[0].AssetType)
	assert.EqualValues(t, enums.TransTypeDeposit, wts[0].TransType)

	assert.EqualValues(t, 2000, wts[1].Money)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wts[1].AssetType)
	assert.EqualValues(t, enums.TransTypeDeposit, wts[1].TransType)

	assert.EqualValues(t, 3000, wts[2].Money)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wts[2].AssetType)
	assert.EqualValues(t, enums.TransTypeDeposit, wts[2].TransType)

	assert.EqualValues(t, 500, wts[3].Money)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wts[3].AssetType)
	assert.EqualValues(t, enums.TransTypeWithdraw, wts[3].TransType)
	assert.EqualValues(t, enums.T_PRESENT_WITHDRAW, wts[3].Type)

	// case1:现金不足情况下,提现一笔现金
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:                         sandbox.UID,
			Money:                       25000,
			Desc:                        "指定提现现金不足情况下,提现一笔现金",
			Excode:                      common.GetUUID(),
			Type:                        enums.T_PRESENT_WITHDRAW,
			DesignatedWithdrawPaymentId: paymentID,
		},
	}
	_, err = paymentAction.WithdrawCash(ctx, &wRequest)
	assert.Error(t, err, "指定提现现金不足的情况下,提现一笔现金,应该报错")
}

func TestPaymentAction_WithdrawCash(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	//sandbox.Factory.

	ctx := context.Background()
	paymentAction := sandbox.PaymentAction

	// case1:没有充值现金情况下,提现现金
	wRequest := pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2500,
			Desc:   "没有充值现金情况下,提现一笔现金",
			Excode: common.GetUUID(),
		},
	}
	_, err := paymentAction.WithdrawCash(ctx, &wRequest)
	assert.Error(t, err, "没有充值现金的情况下,提现一笔现金,应该报错")

	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  1000,
			Desc:   "测试充值一笔,今年发大财",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2000,
			Desc:   "测试充值一笔,今年发大财",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	wallets, err := sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYCash, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 2, len(wallets))

	assert.EqualValues(t, 1000, wallets[0].Amount)
	assert.EqualValues(t, 1000, wallets[0].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[0].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[0].UID)

	assert.EqualValues(t, 2000, wallets[1].Amount)
	assert.EqualValues(t, 2000, wallets[1].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[1].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[1].UID)

	// case2:现金充足情况下,提现一笔现金
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2500,
			Desc:   "提现一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.WithdrawCash(ctx, &wRequest)
	assert.NoError(t, err)

	wallets, err = sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYCash, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 2, len(wallets))
	assert.EqualValues(t, 1000, wallets[0].Amount)
	assert.EqualValues(t, 0, wallets[0].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[0].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[0].UID)

	assert.EqualValues(t, 2000, wallets[1].Amount)
	assert.EqualValues(t, 500, wallets[1].Balance)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[1].AssetType)
	assert.EqualValues(t, sandbox.UID, wallets[1].UID)

	wts, err := sandbox.Factory.GetWalletFactory().WalletService.QueryTransactionByUID(ctx, sandbox.UID)
	assert.NoError(t, err)
	assert.EqualValues(t, 4, len(wts))

	assert.EqualValues(t, 1000, wts[0].Money)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wts[0].AssetType)
	assert.EqualValues(t, enums.TransTypeDeposit, wts[0].TransType)

	assert.EqualValues(t, 2000, wts[1].Money)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wts[1].AssetType)
	assert.EqualValues(t, enums.TransTypeDeposit, wts[1].TransType)

	assert.EqualValues(t, 1000, wts[2].Money)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wts[2].AssetType)
	assert.EqualValues(t, enums.TransTypeWithdraw, wts[2].TransType)

	assert.EqualValues(t, 1500, wts[3].Money)
	assert.EqualValues(t, enums.AssetTypeCNYCash, wts[3].AssetType)
	assert.EqualValues(t, enums.TransTypeWithdraw, wts[3].TransType)

	// case1:现金不足情况下,提现一笔现金
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2500,
			Desc:   "现金不足情况下,提现一笔现金",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.WithdrawCash(ctx, &wRequest)
	assert.Error(t, err, "现金不足的情况下,提现一笔现金,应该报错")

	// 参数校验 excode == ""
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  100,
			Desc:   "测试,提现一笔",
			Excode: "",
		},
	}
	_, err = paymentAction.WithdrawCash(ctx, &wRequest)
	assert.Error(t, err, "Excode 为空,应该报错")

	// 参数校验 Money < 0(这里有个bug:BO-14968)
	// TODO:待 issue 修复，再去掉注释
	// wRequest = pb.WithdrawRequest{
	// 	PaymentRequest: &pb.PaymentRequest{
	// 		Uid:    sandbox.UID,
	// 		Money:  -100,
	// 		Desc:   "测试,提现一笔",
	// 		Excode: common.GetUUID(),
	// 	},
	// }
	// _, err = paymentAction.WithdrawCash(ctx, &wRequest)
	// assert.Error(t, err, "Money 小于0,应该报错")

	// 参数校验 uid == 0
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    0,
			Money:  100,
			Desc:   "测试,提现一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.WithdrawCash(ctx, &wRequest)
	assert.Error(t, err, "Uid 为0,应该报错")
}

func TestPaymentAction_WithdrawNb(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	ctx := context.Background()
	paymentAction := sandbox.PaymentAction

	// case1:没有充值牛币情况下,提现牛币
	wRequest := pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2500,
			Desc:   "没有充值牛币情况下,提现一笔牛币",
			Excode: common.GetUUID(),
		},
	}
	_, err := paymentAction.WithdrawNb(ctx, &wRequest)
	assert.Error(t, err, "没有充值牛币的情况下,提现一笔牛币,应该报错")

	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  1000,
			Desc:   "测试充值一笔牛币:1000",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.DepositNiuCoin(ctx, &request)
	assert.NoError(t, err)

	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2000,
			Desc:   "测试充值一笔牛币:2000",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.DepositNiuCoin(ctx, &request)
	assert.NoError(t, err)

	wallets, err := sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYNiuCoin, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 2, len(wallets), "应该有2笔牛币充值")
	if len(wallets) == 2 {
		assert.EqualValues(t, 1000, wallets[0].Amount)
		assert.EqualValues(t, 1000, wallets[0].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[0].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[0].UID)

		assert.EqualValues(t, 2000, wallets[1].Amount)
		assert.EqualValues(t, 2000, wallets[1].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[1].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[1].UID)
	} else {
		t.Fatal("QueryWallet 结果不预期")
	}

	// case2:牛币充足情况下,提现牛币
	// 现在总共有1000牛币+2000牛币，提现2500，第一笔牛币使用完，第二笔使用1500，最后还剩500牛币
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2500,
			Desc:   "牛币充足情况下,提现一笔牛币",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.WithdrawNb(ctx, &wRequest)
	assert.NoError(t, err, "牛币充足情况下,提现一笔牛币,应该成功")

	wallets, err = sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYNiuCoin, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 2, len(wallets))
	if len(wallets) == 2 {
		assert.EqualValues(t, 1000, wallets[0].Amount)
		assert.EqualValues(t, 0, wallets[0].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[0].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[0].UID)

		assert.EqualValues(t, 2000, wallets[1].Amount)
		assert.EqualValues(t, 500, wallets[1].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[1].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[1].UID)
	} else {
		t.Fatal("QueryWallet 结果不预期")
	}

	wts, err := sandbox.Factory.GetWalletFactory().WalletService.QueryTransactionByUID(ctx, sandbox.UID)
	assert.NoError(t, err)
	assert.EqualValues(t, 4, len(wts), "应该有4笔流水,2笔牛币充值,2笔牛币扣费")
	if len(wts) == 4 {
		assert.EqualValues(t, 1000, wts[0].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[0].AssetType)
		assert.EqualValues(t, enums.TransTypeDeposit, wts[0].TransType)

		assert.EqualValues(t, 2000, wts[1].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[1].AssetType)
		assert.EqualValues(t, enums.TransTypeDeposit, wts[1].TransType)

		assert.EqualValues(t, 1000, wts[2].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[2].AssetType)
		assert.EqualValues(t, enums.TransTypeWithdraw, wts[2].TransType)

		assert.EqualValues(t, 1500, wts[3].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[3].AssetType)
		assert.EqualValues(t, enums.TransTypeWithdraw, wts[3].TransType)
	} else {
		t.Fatal("QueryTransactionByUID 结果不预期")
	}

	// case3:牛币不够情况下,提现牛币
	// 现在只剩余500牛币，提现2500，牛币不够
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2500,
			Desc:   "牛币不够情况下,提现一笔牛币",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.WithdrawNb(ctx, &wRequest)
	assert.Error(t, err, "牛币不够情况下,提现一笔牛币,应该报错")

	// 参数校验 excode == ""
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  100,
			Desc:   "测试,提现一笔",
			Excode: "",
		},
	}
	_, err = paymentAction.WithdrawNb(ctx, &wRequest)
	assert.Error(t, err, "Excode 为空,应该报错")

	// 参数校验 Money < 0(这里有个bug:BO-14968)
	// TODO:待 issue 修复，再去掉注释
	// wRequest = pb.WithdrawRequest{
	// 	PaymentRequest: &pb.PaymentRequest{
	// 		Uid:    sandbox.UID,
	// 		Money:  -100,
	// 		Desc:   "测试,提现一笔",
	// 		Excode: common.GetUUID(),
	// 	},
	// }
	// _, err = paymentAction.WithdrawNb(ctx, &wRequest)
	// assert.Error(t, err, "Money 小于0,应该报错")

	// 参数校验 uid == 0
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    0,
			Money:  100,
			Desc:   "测试,提现一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.WithdrawNb(ctx, &wRequest)
	assert.Error(t, err, "Uid 为0,应该报错")
}

func TestPaymentAction_WithdrawNbAndCash(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	ctx := context.Background()
	paymentAction := sandbox.PaymentAction

	// case1:没有牛币和现金情况下,提现一笔
	wRequest := pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2500,
			Desc:   "没有牛币和现金情况下,提现一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err := paymentAction.WithdrawNbAndCash(ctx, &wRequest)
	assert.Error(t, err, "没有牛币和现金的情况下,提现一笔,应该报错")

	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  1000,
			Desc:   "测试充值一笔牛币:1000",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.DepositNiuCoin(ctx, &request)
	assert.NoError(t, err)

	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2000,
			Desc:   "测试充值一笔牛币:2000",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.DepositNiuCoin(ctx, &request)
	assert.NoError(t, err)

	wallets, err := sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYNiuCoin, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 2, len(wallets), "应该有2笔牛币充值")
	if len(wallets) == 2 {
		assert.EqualValues(t, 1000, wallets[0].Amount)
		assert.EqualValues(t, 1000, wallets[0].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[0].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[0].UID)

		assert.EqualValues(t, 2000, wallets[1].Amount)
		assert.EqualValues(t, 2000, wallets[1].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[1].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[1].UID)
	} else {
		t.Fatal("QueryWallet 结果不预期")
	}

	request = pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2000,
			Desc:   "测试充值一笔现金:2000",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	wallets, err = sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYCash, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 1, len(wallets), "应该有1笔现金充值")
	if len(wallets) == 1 {
		assert.EqualValues(t, 2000, wallets[0].Amount)
		assert.EqualValues(t, 2000, wallets[0].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[0].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[0].UID)
	} else {
		t.Fatal("QueryWallet 结果不预期")
	}

	// case2:牛币充足情况下,提现一笔(仅用牛币就能足够支付)
	// 现在有 1000牛币+2000牛币+2000现金，提现2500，应该会把第一笔充值的牛币用完，第二笔充值的牛币用掉1500，还剩500牛币，2000现金
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  2500,
			Desc:   "牛币充足情况下,提现一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.WithdrawNbAndCash(ctx, &wRequest)
	assert.NoError(t, err, "牛币充足情况下,提现一笔,应该成功")

	wallets, err = sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYNiuCoin, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 2, len(wallets))
	if len(wallets) == 2 {
		assert.EqualValues(t, 1000, wallets[0].Amount)
		assert.EqualValues(t, 0, wallets[0].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[0].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[0].UID)

		assert.EqualValues(t, 2000, wallets[1].Amount)
		assert.EqualValues(t, 500, wallets[1].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[1].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[1].UID)
	} else {
		t.Fatal("QueryWallet 结果不预期")
	}

	wts, err := sandbox.Factory.GetWalletFactory().WalletService.QueryTransactionByUID(ctx, sandbox.UID)
	assert.NoError(t, err)
	assert.EqualValues(t, 5, len(wts), "应该有5笔流水，2笔牛币充值，1笔现金充值，2笔牛币扣费")
	if len(wts) == 5 {
		assert.EqualValues(t, 1000, wts[0].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[0].AssetType)
		assert.EqualValues(t, enums.TransTypeDeposit, wts[0].TransType)

		assert.EqualValues(t, 2000, wts[1].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[1].AssetType)
		assert.EqualValues(t, enums.TransTypeDeposit, wts[1].TransType)

		assert.EqualValues(t, 2000, wts[2].Money)
		assert.EqualValues(t, enums.AssetTypeCNYCash, wts[2].AssetType)
		assert.EqualValues(t, enums.TransTypeDeposit, wts[2].TransType)

		assert.EqualValues(t, 1000, wts[3].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[3].AssetType)
		assert.EqualValues(t, enums.TransTypeWithdraw, wts[3].TransType)

		assert.EqualValues(t, 1500, wts[4].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[4].AssetType)
		assert.EqualValues(t, enums.TransTypeWithdraw, wts[4].TransType)
	} else {
		t.Fatal("QueryTransactionByUID 结果不预期")
	}

	// case3:牛币不够情况下,提现(先用牛币,再用现金)
	// 现在还有500牛币+2000现金，提现1000，应该先用500牛币，再用500现金。之后牛币不剩，现金还剩1500
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  1000,
			Desc:   "牛币不够情况下,提现一笔(会先提现牛币,再提现现金)",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.WithdrawNbAndCash(ctx, &wRequest)
	assert.NoError(t, err)

	wallets, err = sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYNiuCoin, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 2, len(wallets))
	if len(wallets) == 2 {
		assert.EqualValues(t, 1000, wallets[0].Amount)
		assert.EqualValues(t, 0, wallets[0].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[0].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[0].UID)

		assert.EqualValues(t, 2000, wallets[1].Amount)
		assert.EqualValues(t, 0, wallets[1].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wallets[1].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[1].UID)
	} else {
		t.Fatal("QueryWallet 结果不预期")
	}

	wallets, err = sandbox.Factory.GetWalletFactory().WalletService.QueryWallet(ctx, sandbox.UID, enums.AssetTypeCNYCash, "")
	assert.NoError(t, err)
	assert.EqualValues(t, 1, len(wallets))
	if len(wallets) == 1 {
		assert.EqualValues(t, 2000, wallets[0].Amount)
		assert.EqualValues(t, 1500, wallets[0].Balance)
		assert.EqualValues(t, enums.AssetTypeCNYCash, wallets[0].AssetType)
		assert.EqualValues(t, sandbox.UID, wallets[0].UID)
	} else {
		t.Fatal("QueryWallet 结果不预期")
	}

	wts, err = sandbox.Factory.GetWalletFactory().WalletService.QueryTransactionByUID(ctx, sandbox.UID)
	assert.NoError(t, err)
	assert.EqualValues(t, 7, len(wts), "应该有7笔流水，2笔牛币充值，1笔现金充值，3笔牛币扣费，1笔现金扣费")
	if len(wts) == 7 {
		assert.EqualValues(t, 1000, wts[0].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[0].AssetType)
		assert.EqualValues(t, enums.TransTypeDeposit, wts[0].TransType)

		assert.EqualValues(t, 2000, wts[1].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[1].AssetType)
		assert.EqualValues(t, enums.TransTypeDeposit, wts[1].TransType)

		assert.EqualValues(t, 2000, wts[2].Money)
		assert.EqualValues(t, enums.AssetTypeCNYCash, wts[2].AssetType)
		assert.EqualValues(t, enums.TransTypeDeposit, wts[2].TransType)

		assert.EqualValues(t, 1000, wts[3].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[3].AssetType)
		assert.EqualValues(t, enums.TransTypeWithdraw, wts[3].TransType)

		assert.EqualValues(t, 1500, wts[4].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[4].AssetType)
		assert.EqualValues(t, enums.TransTypeWithdraw, wts[4].TransType)

		assert.EqualValues(t, 500, wts[5].Money)
		assert.EqualValues(t, enums.AssetTypeCNYNiuCoin, wts[5].AssetType)
		assert.EqualValues(t, enums.TransTypeWithdraw, wts[5].TransType)

		assert.EqualValues(t, 500, wts[6].Money)
		assert.EqualValues(t, enums.AssetTypeCNYCash, wts[6].AssetType)
		assert.EqualValues(t, enums.TransTypeWithdraw, wts[6].TransType)
	} else {
		t.Fatal("QueryTransactionByUID 结果不预期")
	}

	// 参数校验 excode == ""
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    sandbox.UID,
			Money:  100,
			Desc:   "测试,提现一笔",
			Excode: "",
		},
	}
	_, err = paymentAction.WithdrawNbAndCash(ctx, &wRequest)
	assert.Error(t, err, "Excode 为空,应该报错")

	// 参数校验 Money < 0(这里有个bug:BO-14968)
	// TODO:待 issue 修复，再去掉注释
	// wRequest = pb.WithdrawRequest{
	// 	PaymentRequest: &pb.PaymentRequest{
	// 		Uid:    sandbox.UID,
	// 		Money:  -100,
	// 		Desc:   "测试,提现一笔",
	// 		Excode: common.GetUUID(),
	// 	},
	// }
	// _, err = paymentAction.WithdrawNbAndCash(ctx, &wRequest)
	// assert.Error(t, err, "Money 小于0,应该报错")

	// 参数校验 uid == 0
	wRequest = pb.WithdrawRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:    0,
			Money:  100,
			Desc:   "测试,提现一笔",
			Excode: common.GetUUID(),
		},
	}
	_, err = paymentAction.WithdrawNbAndCash(ctx, &wRequest)
	assert.Error(t, err, "Uid 为0,应该报错")
}
