package stat

import (
	"net/url"

	mppb "github.com/qbox/pay-sdk/measureproxy"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/expr"
)

var errInvalidStatExtraKey = errors.New("invalid stat extra key")
var errInvalidGranularity = errors.New("invalid stat_g value")

const (
	statExtraKeyExpr   = "stat_expr"
	statExtraKeyG      = "stat_g"
	statExtraKeyKey    = "stat_key"
	statExtraKeyQuery  = "stat_query"
	statExtraKeySelect = "stat_select"
)

// parsedStatExtras 解析过的老式计量源拉量参数
type parsedStatExtras struct {
	G mppb.Granularity
	// NOTE: 此处 Select 给到数组类型的唯一原因是 `distribution:bandwidth` 这个计费项
	Select []string
	Key    string
	Query  []*kv
	Expr   *expr.MathExpr
}

type kv mppb.KV

// parseExtrasAsStd 按标准计量源风格解析 v3 风格的拉量 extras 参数
func parseExtrasAsStd(
	mergedExtras map[string]string,
) (*parsedStatExtras, error) {
	// 默认值
	result := &parsedStatExtras{
		G: mppb.G_DAY,
	}

	for k, v := range mergedExtras {
		switch k {
		case statExtraKeyG:
			g, err := parseStatG(v)
			if err != nil {
				return nil, errors.Trace(err)
			}
			result.G = g

		case statExtraKeySelect:
			// NOTE: 原先 stat_std.go 实际上会按 "," 分隔；别的计量源有的不看这个字段，Pili 处理逻辑完全不同
			// 然而线上数据就没一个 stat_select 取值有逗号。。。
			// 这里就不支持逗号分隔了。。
			result.Select = []string{v}

		case statExtraKeyQuery:
			q, err := parseStatQuery(v)
			if err != nil {
				return nil, errors.Trace(err)
			}
			result.Query = q

		case statExtraKeyKey:
			result.Key = v

		case statExtraKeyExpr:
			e, err := expr.NewMathExpr(v)
			if err != nil {
				return nil, errors.Trace(err)
			}
			result.Expr = e

		default:
			return nil, errors.Trace(errInvalidStatExtraKey)
		}
	}

	return result, nil
}

func parseStatG(v string) (mppb.Granularity, error) {
	switch v {
	case "day":
		return mppb.G_DAY, nil
	case "5min":
		return mppb.G_5MIN, nil
	case "month":
		return mppb.G_MONTH, nil
	default:
		return mppb.G_UNKNOWN, errors.Trace(errInvalidGranularity)
	}
}

func parseStatQuery(v string) ([]*kv, error) {
	vals, err := url.ParseQuery(v)
	if err != nil {
		return nil, errors.Trace(err)
	}

	result := make([]*kv, 0)
	for k, vs := range vals {
		for _, v := range vs {
			result = append(result, &kv{
				K: k,
				V: v,
			})
		}
	}

	return result, nil
}
