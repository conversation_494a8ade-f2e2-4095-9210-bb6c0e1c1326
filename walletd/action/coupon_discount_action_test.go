package action_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/pay-sdk/wallet"
)

func TestCouponDiscountInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	walletClient := sandbox.walletClient

	const scopeID uint64 = 1
	ds := []wallet.CouponDiscount{
		{
			Code:        "foo",
			Type:        "DISCOUNT",
			ScopeId:     scopeID,
			Name:        "折扣抵用券1折",
			Description: "赠送折扣抵用券1折",
			Remark:      "xx活动优惠",
			Discount:    100,
			EffectTime:  timestamppb.Now(),
			DeadTime:    timestamppb.Now(),
		},
		{
			Code:        "bar",
			Type:        "DISCOUNT",
			ScopeId:     scopeID,
			Name:        "折扣抵用券2折",
			Description: "赠送折扣抵用券2折",
			Remark:      "xx活动优惠",
			Discount:    200,
			EffectTime:  timestamppb.Now(),
			DeadTime:    timestamppb.Now(),
		},
		{
			Code:        "baz",
			Type:        "DISCOUNT",
			ScopeId:     scopeID,
			Name:        "折扣抵用券3折",
			Description: "赠送折扣抵用券3折",
			Remark:      "xx活动优惠",
			Discount:    300,
			EffectTime:  timestamppb.Now(),
			DeadTime:    timestamppb.Now(),
		},
	}
	assertFields := func(discount *wallet.CouponDiscount, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(ds) }) {
			assert.Equal(t, ds[n].GetCode(), discount.GetCode(), msgAndArgs...)
			assert.Equal(t, ds[n].GetType(), discount.GetType(), msgAndArgs...)
			assert.Equal(t, ds[n].GetScopeId(), discount.GetScopeId(), msgAndArgs...)
			assert.Equal(t, ds[n].GetName(), discount.GetName(), msgAndArgs...)
			assert.Equal(t, ds[n].GetDescription(), discount.GetDescription(), msgAndArgs...)
			assert.Equal(t, ds[n].GetRemark(), discount.GetRemark(), msgAndArgs...)
			assert.Equal(t, ds[n].GetDiscount(), discount.GetDiscount(), msgAndArgs...)
		}
	}
	ids := make(map[uint64]int, len(ds)+1)
	{
		for n, c := range ds {
			discount, err := walletClient.CreateCouponDiscount(context.Background(), &c)
			if assert.NoError(t, err, "CreateCouponDiscount") {
				assert.NotZero(t, discount.GetId(), "CreateCouponDiscount")
				ids[discount.GetId()] = n
			}

			discount, err = walletClient.GetCouponDiscountByID(context.Background(), &wallet.IDParam{Id: discount.GetId()})
			if assert.NoError(t, err, "GetCouponDiscountByID") {
				assertFields(discount, n, "GetCouponDiscountByID")
			}

			discount, err = walletClient.GetCouponDiscountByCode(context.Background(), &wallet.CodeParam{Code: discount.GetCode()})
			if assert.NoError(t, err, "GetCouponDiscountByCode") {
				assertFields(discount, n, "GetCouponDiscountByCode")
			}
		}

		count, err := walletClient.CountAllCouponDiscounts(context.Background(), &empty.Empty{})
		if assert.NoError(t, err, "CountAllCouponDiscounts") {
			assert.Len(t, ds, int(count.GetCount()), "CountAllCouponDiscounts")
		}
	}

	list, err := walletClient.ListAllCouponDiscounts(context.Background(), &wallet.PagingParam{Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListAllCouponDiscounts") {
		assert.Len(t, ds, int(list.GetCount()), "ListAllCouponDiscounts")
		for _, discount := range list.GetCouponDiscounts() {
			if assert.Contains(t, ids, discount.GetId(), "ListAllCouponDiscounts") {
				assertFields(discount, ids[discount.GetId()], "ListAllCouponDiscounts")
			}
		}
	}
	{
		list, err := walletClient.ListCouponDiscountsByScopeID(context.Background(), &wallet.IDPagingParam{Id: scopeID, PageSize: 10, Page: 1})
		if assert.NoError(t, err, "ListCouponDiscountsByScopeID") {
			assert.Len(t, ds, int(list.GetCount()), "ListCouponDiscountsByScopeID")
			for _, discount := range list.GetCouponDiscounts() {
				if assert.Contains(t, ids, discount.GetId(), "ListCouponDiscountsByScopeID->Recv") {
					assertFields(discount, ids[discount.GetId()], "ListCouponDiscountsByScopeID->Recv")
				}
			}
		}
		count, err := walletClient.CountCouponDiscountsByScopeID(context.Background(), &wallet.IDParam{Id: scopeID})
		if assert.NoError(t, err, "CountCouponDiscountsByScopeID") {
			assert.Len(t, ds, int(count.GetCount()), "CountCouponDiscountsByScopeID")
		}
	}
	for id, n := range ids {
		discount, err := walletClient.GetCouponDiscountByID(context.Background(), &wallet.IDParam{Id: id})
		if assert.NoError(t, err, "GetCouponDiscountByID") {
			assertFields(discount, n, "GetCouponDiscountByID")
		}

		ds[n].Name += discount.GetRemark()
		discount.Name += discount.GetRemark()
		discount, err = walletClient.UpdateCouponDiscountByID(context.Background(), &wallet.IDCouponDiscountParam{Id: discount.GetId(), CouponDiscount: discount})
		if assert.NoError(t, err, "UpdateCouponDiscountByID") {
			assert.Equal(t, ds[n].GetName(), discount.GetName(), "UpdateCouponDiscountByID")
		}

		ds[n].Name += discount.GetRemark()
		discount.Name += discount.GetRemark()
		discount, err = walletClient.UpdateCouponDiscountByCode(context.Background(), &wallet.CodeCouponDiscountParam{Code: discount.GetCode(), CouponDiscount: discount})
		if assert.NoError(t, err, "UpdateCouponDiscountByCode") {
			assert.Equal(t, ds[n].GetName(), discount.GetName(), "UpdateCouponDiscountByCode")
		}
	}
	for id, n := range ids {
		if n%2 == 0 {
			discount, err := walletClient.DeleteCouponDiscountByID(context.Background(), &wallet.IDParam{Id: id})
			if assert.NoError(t, err, "DeleteCouponDiscountByID") {
				assertFields(discount, n, "DeleteCouponDiscountByID")
			}
		} else {
			discount, err := walletClient.DeleteCouponDiscountByCode(context.Background(), &wallet.CodeParam{Code: ds[n].GetCode()})
			if assert.NoError(t, err, "DeleteCouponDiscountByCode") {
				assertFields(discount, n, "DeleteCouponDiscountByCode")
			}
		}
	}
}
