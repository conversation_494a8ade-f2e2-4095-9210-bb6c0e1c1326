package action

import (
	validator "gopkg.in/go-playground/validator.v9"

	"github.com/qbox/bo-base/v4/action"
	pb "github.com/qbox/pay-sdk/price"

	"qiniu.io/pay/priced/service"
)

// PriceAction implements the server definition in protobuffer
type PriceAction struct {
	*action.BaseAction
	pb.UnimplementedPayPriceServiceServer

	priceBizSrv     *service.PriceBizService
	defaultPageSize uint64
	validate        *validator.Validate
}

// NewPriceAction is constructor of PriceAction
func NewPriceAction(priceBizSrv *service.PriceBizService, defaultPageSize uint64) *PriceAction {
	return &PriceAction{
		BaseAction:      action.NewBaseAction(defaultPageSize),
		priceBizSrv:     priceBizSrv,
		defaultPageSize: defaultPageSize,
		validate:        validator.New(),
	}
}
