package adapter

import (
	"qiniu.io/pay/measured/model"

	pb "github.com/qbox/pay-sdk/measure"
)

func BuildPbUserResourceTagsV1Resp(m *model.UserResourceTags) *pb.UserResourceTagsV1Resp {
	result := &pb.UserResourceTagsV1Resp{
		List: make([]*pb.UserResourceTags, 0, len(m.List)),
	}

	for _, obj := range m.List {
		tmp := &pb.UserResourceTags{
			ResourceName: obj.ResourceName,
			ResourceType: string(obj.ResourceType),
			Tags:         make([]*pb.Tag, 0, len(obj.Tags)),
		}
		for _, tag := range obj.Tags {
			tmp.Tags = append(tmp.Tags, &pb.Tag{
				Key:   tag.Key,
				Value: tag.Value,
			})
		}
		result.List = append(result.List, tmp)
	}
	return result
}
