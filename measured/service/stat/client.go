package stat

import (
	"context"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/pay-sdk/dict"
	"github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/kirby/service/dataplane"
	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service/stat/doraItemsQuerier"
)

// Client 计量中心拉量客户端
type Client interface {
	// GetStat 拉量
	GetStat(ctx context.Context, params *model.StatReq) (*model.StatResp, error)
	// GetUsers 拉某产品线某区域有量用户列表
	GetUsers(ctx context.Context, params *model.GetUsersParams) (model.UsersResp, error)
	// GetItemsInUse 拉单个用户有量计费项列表
	GetItemsInUse(ctx context.Context, params *model.GetItemsInUseParams) (model.ItemsInUseResp, error)
	// GetUserResourceTags 获取用户资源标签列表
	GetUserResourceTags(ctx context.Context, uid uint64) (*model.UserResourceTags, error)
	// SyncCustomBills 同步外部第三方自定义账单数据
	SyncCustomBills(ctx context.Context, params *model.SyncCustomBillParams) (any, error)
}

type measuredStatClient struct {
	dao        *model.MeasureDao
	cacheStore dao.CacheStore

	dictClient          dict.PayDictServiceClient
	mpClient            measureproxy.PayMeasureProxyServiceClient
	doraItemsQuerier    doraItemsQuerier.Querier
	kirbyDP             dataplane.IKirbyDataplane
	enableKirbyDatapath bool
}

// NewClient 构造一个计量中心拉量客户端的实例
func NewClient(
	mDao *model.MeasureDao,
	dictClient dict.PayDictServiceClient,
	mpClient measureproxy.PayMeasureProxyServiceClient,
	kirbyDP dataplane.IKirbyDataplane,
	enableKirbyDatapath bool,
	cacheCfg *dao.CacheConfig,
) Client {
	var cacheStore dao.CacheStore
	if cacheCfg.Enabled {
		cacheStore = dao.NewDefaultCacheStore(cacheCfg)
	}

	diq := doraItemsQuerier.New(mDao, dictClient, mpClient)

	return &measuredStatClient{
		dao:        mDao,
		cacheStore: cacheStore,

		dictClient:          dictClient,
		mpClient:            mpClient,
		doraItemsQuerier:    diq,
		kirbyDP:             kirbyDP,
		enableKirbyDatapath: enableKirbyDatapath,
	}
}
