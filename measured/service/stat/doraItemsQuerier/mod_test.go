package doraItemsQuerier

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/intl/tz"
	pb "github.com/qbox/pay-sdk/measureproxy"
	mockdict "github.com/qbox/pay-sdk/mocks/dict"
	mockmp "github.com/qbox/pay-sdk/mocks/measureproxy"

	"qiniu.io/pay/measured/model"
)

func TestDoraItemsQuerier_DoraItemsInUse(t *testing.T) {
	type dummyKey struct{}
	ctx := context.WithValue(context.Background(), dummyKey{}, 1)

	loc := time.FixedZone("CST", 8*3600)
	ctx = tz.WithRefLocation(ctx, loc)

	ctrl := gomock.NewController(t)

	mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)

	mockMeasureProxyCl := mockmp.NewMockPayMeasureProxyServiceClient(ctrl)
	mockMeasureProxyCl.EXPECT().
		ProxyGetDoraCmdsInUse(ctx, &pb.GetItemsInUseParams{
			Uid:      233,
			Begin:    timestamppb.New(time.Date(2021, 4, 1, 0, 0, 0, 0, loc)),
			End:      timestamppb.New(time.Date(2021, 5, 1, 0, 0, 0, 0, loc)),
			Src:      pb.STAT_SRC_DORA,
			HaveZone: true,
		}).
		Times(1).
		Return(
			&pb.DoraCmdsInUseResp{
				Cmds: []*pb.LegacyDoraCmd{
					{Key: "common", Cmd: "IMAGE-CENSOR_CENSOR_TERROR"},      // 这个对应 atlab 产品线的计费项
					{Key: "common", Cmd: "LABEL_LABEL_VIDEO"},               // 这个要返回
					{Key: "fopg", Cmd: "AVCONVERT_TIMS"},                    // 这个没对接计费
					{Key: "fusion", Cmd: "IMAGE-WATERMARK-REQ_IMAGE_FSIZE"}, // 这个要返回
					{Key: "fopg", Cmd: "QHASH-FSIZE"},                       // 这个对应两个计费项，都要返回
				},
			},
			nil,
		)

	querier := New(nil, mockDictCl, mockMeasureProxyCl)

	// 本用例不测试刷新索引缓存的部分，因此直接注入预制的索引
	{
		concreteObj := querier.(*querierImpl)
		concreteObj.dict = doraCmdsItemsDict{
			"common:IMAGE-CENSOR_CENSOR_TERROR": {
				{ItemCode: "mps:terror_certain", ProductCode: "atlab"},
			},
			"common:LABEL_LABEL_VIDEO": {
				{ItemCode: "mps:label:video", ProductCode: "mps"},
			},
			"fusion:IMAGE-WATERMARK-REQ_IMAGE_FSIZE": {
				{ItemCode: "mps:image:watermark", ProductCode: "mps"},
			},
			"fopg:QHASH-FSIZE": {
				{ItemCode: "mps:qhash:fsize", ProductCode: "mps"},
				{ItemCode: "mps:file-op:qhash", ProductCode: "mps"},
			},
		}
		// 希望不会出现开始跑这个用例，跑到一半先休眠 10000hr，之后继续跑的情况 🌚
		concreteObj.cacheExpires = time.Now().Add(10000 * time.Hour)
	}

	resp, err := querier.DoraItemsInUse(ctx, &model.GetItemsInUseParams{
		UID:         233,
		ProductCode: "mps",
		Begin:       time.Date(2021, 4, 1, 0, 0, 0, 0, loc),
		End:         time.Date(2021, 5, 1, 0, 0, 0, 0, loc),
		ZoneCode:    0,
	})
	assert.NoError(t, err)

	assert.Equal(t, model.ItemsInUseResp{
		"mps:label:video",
		"mps:image:watermark",
		"mps:qhash:fsize",
		"mps:file-op:qhash",
	}, resp)
}
