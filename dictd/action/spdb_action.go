package action

import (
	"context"

	"qiniu.io/pay/dictd/action/adapter"

	"github.com/qbox/pay-sdk/dict"
	"google.golang.org/protobuf/types/known/emptypb"
)

func (a *DictAction) GetAllBanks(
	ctx context.Context,
	empty *emptypb.Empty,
) (*dict.BanksResp, error) {
	banks, err := a.dictBizSrv.GetAllBanks(ctx)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbBanksResp(banks)
}

func (a *DictAction) GetAllProvinces(
	ctx context.Context,
	empty *emptypb.Empty,
) (*dict.ProvincesResp, error) {
	provinces, err := a.dictBizSrv.GetAllProvinces(ctx)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbProvincesResp(provinces)
}

func (a *DictAction) GetCitiesByProvinceCode(
	ctx context.Context,
	req *dict.SPDBCodeParam,
) (*dict.CitiesResp, error) {
	cities, err := a.dictBizSrv.FindCitiesByProvinceCode(ctx, req.Code)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCitiesResp(cities)
}

func (a *DictAction) GetPayBanksBy(
	ctx context.Context,
	req *dict.GetPayBanksReq,
) (*dict.PayBanksResp, error) {
	payBanks, err := a.dictBizSrv.FindPayBanksByCityAndBankCode(ctx, req.CityCode, req.BankCode)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPayBanksResp(payBanks)
}

func (a *DictAction) GetPayBankByName(
	ctx context.Context,
	req *dict.GetPayBankByNameReq,
) (*dict.PayBank, error) {
	payBank, err := a.dictBizSrv.FindPayBankByName(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPayBank(payBank)
}

func (a *DictAction) GetPayBankNamesByCond(ctx context.Context, req *dict.PayBankNamesReq) (*dict.PayBankNamesResp, error) {
	list, err := a.dictBizSrv.FindPayBankNamesByCond(ctx, req.Name, req.CityCode, req.BankCode)
	if err != nil {
		return nil, err
	}
	return &dict.PayBankNamesResp{
		List: list,
	}, nil
}

func (a *DictAction) GetProvinceCitiesList(ctx context.Context, empty *emptypb.Empty) (*dict.ProvinceCitiesResp, error) {
	provinceCitiesList, err := a.dictBizSrv.GetProvinceCitiesList(ctx)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbProvinceCitiesResp(provinceCitiesList)
}
