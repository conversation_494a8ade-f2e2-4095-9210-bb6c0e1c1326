package adapter

import (
	"testing"
	"time"

	pb "github.com/qbox/pay-sdk/measureproxy"
	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measureproxyd/model"
)

func TestGetUsersParamsRoundTrip(t *testing.T) {
	x := &model.GetUsersParams{
		Begin:    time.Date(2019, 7, 1, 0, 0, 0, 0, time.Local),
		End:      time.Date(2019, 8, 1, 0, 0, 0, 0, time.Local),
		HaveZone: true,
		ZoneCode: 3001,
		Src:      pb.STAT_SRC_FUSION,
		Debug:    true,
	}

	y, err := BuildPbGetUsersParams(x)
	assert.NoError(t, err)

	z, err := BuildModelGetUsersParams(y)
	assert.NoError(t, err)

	assert.Equal(t, x.Begin.<PERSON>(), z.<PERSON>gin.<PERSON><PERSON><PERSON>())
	assert.Equal(t, x.End.<PERSON><PERSON><PERSON>(), z.<PERSON>.<PERSON><PERSON>())
	assert.Equal(t, x.<PERSON>, z.<PERSON>)
	assert.Equal(t, x.<PERSON><PERSON><PERSON>, z.ZoneCode)
	assert.Equal(t, x.Src, z.Src)
	assert.Equal(t, x.Debug, z.Debug)
}
