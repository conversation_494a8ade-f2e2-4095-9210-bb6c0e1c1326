package action

import (
	"context"
	"time"

	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/qpay/common"
	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/payment/model"
	"qiniu.io/pay/walletd/action/adapter"
)

// ReverseBill reverse bill
func (a *PaymentAction) Reverse(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : Reverse")

	req, err := adapter.BuildPaymentRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildPaymentRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	txn, err := a.reverseService.ReverseByEntryID(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("reverse by entry id failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}
	return pbTransaction, nil
}

func (a *PaymentAction) ReverseForRevised(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : ReverseForRevised")

	req, err := adapter.BuildPaymentRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildPaymentRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.ReverseForRevised GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	txn, err := a.reverseService.ReverseForRevised(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("ReverseForRevised failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}
	return pbTransaction, nil
}

func (a *PaymentAction) ReverseForAbandoned(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : ReverseForAbandoned")

	req, err := adapter.BuildPaymentRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildPaymentRequest failed")
		return nil, err
	}

	txn, err := a.reverseService.ReverseForAbandoned(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("ReverseForAbandoned failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}
	return pbTransaction, nil
}

// ReverseForRecharge reverse for recharge
func (a *PaymentAction) ReverseForRecharge(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"PaymentID": request.PaymentId,
		"Method":    "ReverseForRecharge",
		"Impl":      "PaymentAction",
	}).Info("New-Wallet : ReverseForRecharge")

	req, err := adapter.BuildPaymentRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
			"Method":  "ReverseForRecharge",
			"Impl":    "PaymentAction",
		}).WithError(err).Error("BuildPaymentRequest failed")
		return nil, err
	}

	txn, err := a.reverseService.ReverseForRecharge(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"PaymentID": request.PaymentId,
			"Method":    "ReverseForRecharge",
			"Impl":      "PaymentAction",
		}).WithError(err).Error("ReverseForRecharge failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
			"Method":     "ReverseForRecharge",
			"Impl":       "PaymentAction",
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}
	return pbTransaction, nil
}

func (a *PaymentAction) ReverseDeductThenWithdraw(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"PaymentID": request.PaymentId,
		"Method":    "ReverseDeductThenWithdraw",
		"Impl":      "PaymentAction",
	}).Info("New-Wallet : ReverseDeductThenWithdraw")

	req, err := adapter.BuildPaymentRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
			"Method":  "ReverseDeductAndWithdraw",
			"Impl":    "PaymentAction",
		}).WithError(err).Error("BuildPaymentRequest failed")
		return nil, err
	}
	txn, err := a.reverseService.ReverseForWithdraw(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"PaymentID": request.PaymentId,
			"Method":    "ReverseDeductThenWithdraw",
			"Impl":      "PaymentAction",
		}).WithError(err).Error("ReverseForWithdraw failed")
		return nil, err
	}
	now := time.Now()
	paymentTxn, err := a.depositService.WithdrawCash(ctx, &model.DepositRequest{
		PaymentRequest: model.PaymentRequest{
			UID:          txn.UID,
			EntryDesc:    txn.EntryDesc,
			EntryType:    txn.EntryType,
			EntryID:      common.GetEntryId(request.Excode, string(txn.Prefix), txn.Type, int64(txn.UID)),
			Excode:       request.Excode,
			Details:      txn.Details,
			BusinessAt:   &now,
			Prefix:       txn.Prefix,
			Type:         txn.Type,
			Money:        -txn.Money,
			CurrencyType: txn.CurrencyType,
		},
		DepositType:                 enums.DepositTypeWithdraw,
		DesignatedWithdrawPaymentID: txn.PaymentID,
	})
	if err != nil {
		logger.WithFields(logrus.Fields{
			"PaymentID": request.PaymentId,
			"Method":    "ReverseDeductThenWithdraw",
			"Impl":      "PaymentAction",
		}).WithError(err).Error("WithdrawCash failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*paymentTxn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
			"Method":     "ReverseDeductThenWithdraw",
			"Impl":       "PaymentAction",
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}
	return pbTransaction, nil
}
