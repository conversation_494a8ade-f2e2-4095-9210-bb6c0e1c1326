package model_test

import (
	"testing"

	basedao "github.com/qbox/bo-base/v4/dao"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
)

func TestModelPriceUserMapCRUD(t *testing.T) {
	sandbox := buildSandbox(t)

	dao := sandbox.priceDao.PriceUserMap

	pums := []model.PriceUserMap{
		{
			UID:     1,
			PriceID: 1,
		},
		{
			UID:     2,
			PriceID: 2,
		},
		{
			UID:     3,
			PriceID: 3,
		},
		{
			UID:     4,
			PriceID: 4,
		},
	}

	assertFields := func(expect, actual *model.PriceUserMap, msgAndArgs ...any) {
		assert.Equal(t, expect.UID, actual.UID, msgAndArgs...)
		assert.Equal(t, expect.PriceID, actual.PriceID, msgAndArgs...)
	}

	for i, pum := range pums {
		// create
		err := dao.Save(&pum)
		if assert.NoError(t, err) {
			assert.NotZero(t, pum.ID, "Save return zero id")
		}
		pums[i].ID = pum.ID

		// query
		pumID := pum.ID
		m, err := dao.GetByID(pumID)
		if assert.NoError(t, err) {
			assertFields(&pum, m, "Unmatched pum")
		}
	}

	// list all price item pums
	pumList, err := dao.List(0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, pumList, len(pums))
	}

	// list all price item pum by price_item_id
	pumWithSameUID, err := dao.ListByUID(1, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, pumWithSameUID, 1)
		for _, pum := range pumWithSameUID {
			for _, expectedPum := range pums {
				if pum.ID == expectedPum.ID {
					assertFields(&expectedPum, &pum)
					break
				}
			}
		}
	}

	// delete all price items
	for _, pum := range pums {
		err := dao.DeleteByID(pum.ID)
		assert.NoError(t, err)
	}

	// check price item list, should be empty
	l, err := dao.List(0, 10, basedao.CacheExpiresNoCache)
	if assert.NoError(t, err) {
		assert.Empty(t, l)
	}

	// de-duplication
	for _, pum := range pums {
		err := dao.DeleteByID(pum.ID)
		assert.Error(t, err)
	}

}
