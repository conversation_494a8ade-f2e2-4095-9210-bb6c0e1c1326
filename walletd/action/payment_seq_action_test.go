package action

import (
	"context"
	"testing"

	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestPaymentAction_GetPaymentSeqByUID(t *testing.T) {
	sandbox := BuildSandbox(t)
	ctx := context.Background()

	uid := sandbox.UID
	operator := "john"
	paymentSeq := pb.PaymentSeq{
		Uid:      uid,
		SeqType:  pb.PaymentSeqTypeByMonth,
		Operator: operator,
	}
	param := pb.SetPaymentSeqParam{
		PaymentSeq:     &paymentSeq,
		IsSameAsParent: false,
	}
	_, err := sandbox.PaymentAction.SetUserPaymentSeq(ctx, &param)
	assert.NoError(t, err)

	uidParam := pb.UIDParam{Uid: uid}
	getSeq, err := sandbox.PaymentAction.GetPaymentSeqByUID(ctx, &uidParam)
	assert.NoError(t, err)
	assert.Equal(t, paymentSeq.SeqType, getSeq.GetSeqType())
	assert.Equal(t, paymentSeq.Operator, getSeq.Operator)
}

func TestPaymentAction_ListPaymentSeqHistoriesByUID(t *testing.T) {
	sandbox := BuildSandbox(t)
	ctx := context.Background()

	paymentSeqs := []*pb.PaymentSeq{
		{
			Uid:      sandbox.UID,
			SeqType:  pb.PaymentSeqTypeByMonth,
			ConfigAt: timestamppb.Now(),
			Operator: "John",
		},
		{
			Uid:      sandbox.UID,
			SeqType:  pb.PaymentSeqTypeByTxnTime,
			ConfigAt: timestamppb.Now(),
			Operator: "Jane",
		},
	}

	paymentSeqParams := []*pb.SetPaymentSeqParam{
		{
			PaymentSeq:     paymentSeqs[0],
			IsSameAsParent: false,
		},
		{
			PaymentSeq:     paymentSeqs[1],
			IsSameAsParent: false,
		},
	}

	for _, paymentSeqParam := range paymentSeqParams {
		_, err := sandbox.PaymentAction.SetUserPaymentSeq(ctx, paymentSeqParam)
		assert.NoError(t, err)
	}

	seqHistories, err := sandbox.PaymentAction.ListPaymentSeqHistoriesByUID(ctx, &pb.UIDPagingParam{Uid: sandbox.UID, Page: 2, PageSize: 1})
	assert.NoError(t, err)
	assert.Equal(t, 1, len(seqHistories.PaymentSeqs))
	assert.Equal(t, paymentSeqs[1].Operator, (*seqHistories).PaymentSeqs[0].Operator)
	assert.Equal(t, paymentSeqs[1].SeqType, (*seqHistories).PaymentSeqs[0].SeqType)
}
