package model_test

import (
	"testing"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/qvmprice/model"
)

type sandbox struct {
	testWrap *test.Wrap
	dao      *model.QVMPriceDao
}

func buildSandbox(t *testing.T) *sandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in qvmprice/model return error")
	}

	return &sandbox{
		testWrap: testWrap,
		dao:      model.NewQVMPriceDao(testWrap.BaseDao()),
	}
}
