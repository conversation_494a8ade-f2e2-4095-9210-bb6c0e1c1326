package service

import (
	"context"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/priced/model"
)

// GetPriceItemByID 根据 ID 查询计费项价格
func (s *PriceBizService) GetPriceItemByID(
	ctx context.Context,
	id uint64,
) (*model.PriceItem, error) {
	return s.priceDao.PriceItem.GetByID(id, s.cacheExpires)
}

// CreatePriceItem 创建计费项价格
func (s *PriceBizService) CreatePriceItem(
	ctx context.Context,
	priceItemModel *model.PriceItem,
) (*model.PriceItem, error) {
	priceItemModel.ID = 0
	err := s.priceDao.PriceItem.Save(priceItemModel, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return priceItemModel, err
}

// UpdatePriceItemByID 根据 ID 更新计费项价格
func (s *PriceBizService) UpdatePriceItemByID(
	ctx context.Context,
	id uint64,
	priceItemModel *model.PriceItem,
) (*model.PriceItem, error) {
	priceItemModel.ID = id
	err := s.priceDao.PriceItem.Save(priceItemModel, s.cacheExpires)
	return priceItemModel, err
}

// DeletePriceItemByID 根据 ID 删除计费项价格
func (s *PriceBizService) DeletePriceItemByID(
	ctx context.Context,
	id uint64,
) (*model.PriceItem, error) {
	priceItemModel, err := s.GetPriceItemByID(ctx, id)
	if err != nil {
		return nil, errors.Trace(err)
	}

	err = s.priceDao.PriceItem.Delete(priceItemModel)
	return priceItemModel, err
}

// ListAllPriceItems 查询所有的计费项价格
func (s *PriceBizService) ListAllPriceItems(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.PriceItem, error) {
	return s.priceDao.PriceItem.List(offset, limit, s.cacheExpires)
}

// CountAllPriceItems 获取所有的计费项价格数量
func (s *PriceBizService) CountAllPriceItems(
	ctx context.Context,
) (uint64, error) {
	return s.priceDao.PriceItem.Count(s.cacheExpires)
}

// ListAllPriceItemsByPriceID 根据价格 ID 查询所有的计费项价格
func (s *PriceBizService) ListAllPriceItemsByPriceID(
	ctx context.Context,
	priceID uint64,
	offset int,
	limit int,
) ([]model.PriceItem, error) {
	return s.priceDao.PriceItem.ListByPriceID(priceID, offset, limit, s.cacheExpires)
}

// ListAllPriceItemsByPriceIDs 根据价格 IDs 查询所有的计费项价格
func (s *PriceBizService) ListAllPriceItemsByPriceIDs(
	ctx context.Context,
	priceIDs []uint64,
	offset int,
	limit int,
) ([]model.PriceItem, error) {
	return s.priceDao.PriceItem.ListByPriceIDs(
		priceIDs,
		offset, limit,
		s.cacheExpires,
	)
}

// CountAllPriceItemsByPriceID 根据价格ID获取所有的计费项价格数量
func (s *PriceBizService) CountAllPriceItemsByPriceID(
	ctx context.Context,
	priceID uint64,
) (uint64, error) {
	return s.priceDao.PriceItem.CountByPriceID(priceID, s.cacheExpires)
}

// ListPriceItemsByPriceIDItemID 根据价格 ID 查询所有的计费项价格
func (s *PriceBizService) ListPriceItemsByPriceIDItemID(
	ctx context.Context,
	priceID, zoneID uint64,
	itemIDs []uint64,
	at base.HNS,
	offset, limit int,
) ([]model.PriceItem, error) {
	return s.priceDao.PriceItem.ListByPriceIDItemsAtTimePoint(
		priceID, zoneID, itemIDs, at, offset, limit,
	)
}

// CountPriceItemsByPriceIDItems 根据价格ID获取所有的计费项价格数量
func (s *PriceBizService) CountPriceItemsByPriceIDItems(
	ctx context.Context,
	priceID, zoneID uint64,
	itemIDs []uint64,
	at base.HNS,
) (uint64, error) {
	return s.priceDao.PriceItem.CountByPriceIDItemsAtTimePoint(
		priceID, zoneID, itemIDs, at,
	)
}
