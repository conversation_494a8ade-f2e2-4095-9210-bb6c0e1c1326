package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// GetScopeItemMapByID 通过ID获取计费范围-计费项关系
func (a *WalletAction) GetScopeItemMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeItemMap, error) {
	_map, err := a.walletBizSrv.GetScopeItemMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeItemMap(_map)
}

// CreateScopeItemMap 创建计费范围-计费项关系
func (a *WalletAction) CreateScopeItemMap(
	ctx context.Context,
	_map *pb.ScopeItemMap,
) (*pb.ScopeItemMap, error) {
	scopeItemMap, err := adapter.BuildScopeItemMap(_map)
	if err != nil {
		return nil, err
	}

	m, err := a.walletBizSrv.CreateScopeItemMap(ctx, scopeItemMap)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeItemMap(m)
}

// UpdateScopeItemMapByID 通过ID更新计费范围-计费项关系
func (a *WalletAction) UpdateScopeItemMapByID(
	ctx context.Context,
	param *pb.IDScopeItemMapParam,
) (*pb.ScopeItemMap, error) {
	m, err := adapter.BuildScopeItemMap(param.GetScopeItemMap())
	if err != nil {
		return nil, err
	}

	_map, err := a.walletBizSrv.UpdateScopeItemMapByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeItemMap(_map)
}

// DeleteScopeItemMapByID 通过ID删除计费范围-计费项关系
func (a *WalletAction) DeleteScopeItemMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeItemMap, error) {
	_map, err := a.walletBizSrv.DeleteScopeItemMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeItemMap(_map)
}

// DeleteScopeItemMapsByScopeID 根据计费范围ID删除计费范围-计费项关系
func (a *WalletAction) DeleteScopeItemMapsByScopeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeItemMapList, error) {
	result, err := a.walletBizSrv.DeleteScopeItemMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	count, err := a.walletBizSrv.CountScopeItemMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeItemMapList{
		Count:         count,
		ScopeItemMaps: make([]*pb.ScopeItemMap, len(result)),
	}
	for i, _map := range result {
		m, err := adapter.BuildPbScopeItemMap(&_map)
		if err != nil {
			return nil, err
		}
		list.ScopeItemMaps[i] = m
	}
	return list, nil
}

// ListAllScopeItemMaps 列举所有计费范围-计费项关系
func (a *WalletAction) ListAllScopeItemMaps(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.ScopeItemMapList, error) {
	count, err := a.walletBizSrv.CountAllScopeItemMaps(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	maps, err := a.walletBizSrv.ListAllScopeItemMaps(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeItemMapList{
		Count:         count,
		ScopeItemMaps: make([]*pb.ScopeItemMap, len(maps)),
	}
	for i, _map := range maps {
		m, err := adapter.BuildPbScopeItemMap(&_map)
		if err != nil {
			return nil, err
		}
		list.ScopeItemMaps[i] = m
	}
	return list, nil
}

// CountAllScopeItemMaps 获取所有计费范围-计费项关系数量
func (a *WalletAction) CountAllScopeItemMaps(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountAllScopeItemMaps(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListScopeItemMapsByItemID 通过计费项ID列举计费范围-计费项关系
func (a *WalletAction) ListScopeItemMapsByItemID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ScopeItemMapList, error) {
	count, err := a.walletBizSrv.CountScopeItemMapsByItemID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	maps, err := a.walletBizSrv.ListScopeItemMapsByItemID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeItemMapList{
		Count:         count,
		ScopeItemMaps: make([]*pb.ScopeItemMap, len(maps)),
	}
	for i, _map := range maps {
		m, err := adapter.BuildPbScopeItemMap(&_map)
		if err != nil {
			return nil, err
		}
		list.ScopeItemMaps[i] = m
	}
	return list, nil
}

// CountScopeItemMapsByItemID 通过计费项ID获取所有计费范围-计费项关系
func (a *WalletAction) CountScopeItemMapsByItemID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountScopeItemMapsByItemID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListScopeItemMapsByScopeID 通过计费范围ID列举计费范围-计费项关系
func (a *WalletAction) ListScopeItemMapsByScopeID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ScopeItemMapList, error) {
	count, err := a.walletBizSrv.CountScopeItemMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	maps, err := a.walletBizSrv.ListScopeItemMapsByScopeID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeItemMapList{
		Count:         count,
		ScopeItemMaps: make([]*pb.ScopeItemMap, len(maps)),
	}
	for i, _map := range maps {
		m, err := adapter.BuildPbScopeItemMap(&_map)
		if err != nil {
			return nil, err
		}
		list.ScopeItemMaps[i] = m
	}
	return list, nil
}

// CountScopeItemMapsByScopeID 通过计费范围ID获取计费范围-计费项关系数量
func (a *WalletAction) CountScopeItemMapsByScopeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountScopeItemMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}
