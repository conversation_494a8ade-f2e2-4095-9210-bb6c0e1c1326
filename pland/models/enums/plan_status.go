package enums

// PlanStatus plan状态
type PlanStatus int

const (
	// PlanStatusOnline 在线
	PlanStatusOnline PlanStatus = 1
	// PlanStatusOffline 下线
	PlanStatusOffline PlanStatus = 2
	// PlanStatusDraft 草稿
	PlanStatusDraft PlanStatus = 3
)

// Valid check effect_type valid
func (s PlanStatus) Valid() bool {
	switch s {
	case PlanStatusOnline, PlanStatusOffline, PlanStatusDraft:
		return true
	default:
		return false
	}
}
