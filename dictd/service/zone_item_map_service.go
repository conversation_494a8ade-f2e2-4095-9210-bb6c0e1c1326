package service

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/errors"
	product "github.com/qbox/bo-base/v4/zone"
	"qiniu.io/pay/dictd/model"
)

// GetZoneItemMapByID 通过ID获取机房-计费项关联关系记录
func (s *DictBizService) GetZoneItemMapByID(
	ctx context.Context,
	id uint64,
) (*model.ZoneItemMap, error) {
	return s.dictDao.ZoneItemMap.GetByID(id, s.cacheExpires)
}

// ListZoneItemMapsByZoneID 通过机房ID列举机房-计费项关联关系记录
func (s *DictBizService) ListZoneItemMapsByZoneID(
	ctx context.Context,
	zoneID uint64,
	offset int,
	limit int,
) ([]model.ZoneItemMap, error) {
	return s.dictDao.ZoneItemMap.ListByZoneID(zoneID, offset, limit, s.cacheExpires)
}

// ListZoneItemMapsByZoneCode 通过机房 Code 列举机房-计费项关联关系记录
func (s *DictBizService) ListZoneItemMapsByZoneCode(
	ctx context.Context,
	zone int64,
	offset int,
	limit int,
) ([]model.ZoneItemMap, error) {
	return s.dictDao.ZoneItemMap.ListByZoneCode(zone, offset, limit, s.cacheExpires)
}

// CountZoneItemMapsByZoneID 通过机房ID获取机房-计费项关联关系记录条数
func (s *DictBizService) CountZoneItemMapsByZoneID(
	ctx context.Context,
	zoneID uint64,
) (uint64, error) {
	return s.dictDao.ZoneItemMap.CountByZoneID(zoneID, s.cacheExpires)
}

// CountZoneItemMapsByZoneCode 通过机房 Code 获取机房-计费项关联关系记录条数
func (s *DictBizService) CountZoneItemMapsByZoneCode(
	ctx context.Context,
	zone int64,
) (uint64, error) {
	return s.dictDao.ZoneItemMap.CountByZoneCode(zone, s.cacheExpires)
}

// ListZoneItemMapsByItemID 通过计费项ID列举机房-计费项关联关系记录
func (s *DictBizService) ListZoneItemMapsByItemID(
	ctx context.Context,
	itemID uint64,
	offset int,
	limit int,
) ([]model.ZoneItemMap, error) {
	return s.dictDao.ZoneItemMap.ListByItemID(itemID, offset, limit, s.cacheExpires)
}

// ListZoneItemMapsByItemCode 通过计费项 Code 列举机房-计费项关联关系记录
func (s *DictBizService) ListZoneItemMapsByItemCode(
	ctx context.Context,
	item string,
	offset int,
	limit int,
) ([]model.ZoneItemMap, error) {
	return s.dictDao.ZoneItemMap.ListByItemCode(item, offset, limit, s.cacheExpires)
}

// CountZoneItemMapsByItemID 通过计费项ID获取机房-计费项关联关系记录条数
func (s *DictBizService) CountZoneItemMapsByItemID(
	ctx context.Context,
	itemID uint64,
) (uint64, error) {
	return s.dictDao.ZoneItemMap.CountByItemID(itemID, s.cacheExpires)
}

// CountZoneItemMapsByItemCode 通过计费项 Code 获取机房-计费项关联关系记录条数
func (s *DictBizService) CountZoneItemMapsByItemCode(
	ctx context.Context,
	item string,
) (uint64, error) {
	return s.dictDao.ZoneItemMap.CountByItemCode(item, s.cacheExpires)
}

// CreateZoneItemMap 创建机房-计费项关联关系记录
func (s *DictBizService) CreateZoneItemMap(
	ctx context.Context,
	ziMap *model.ZoneItemMap,
) (*model.ZoneItemMap, error) {
	ziMap.ID = 0
	err := s.dictDao.ZoneItemMap.Save(ziMap, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "save zone_item_map failed").
			WithField("zone_item_map", ziMap)
	}
	item, err := s.GetItemByID(ctx, ziMap.ItemID)
	if err != nil {
		return nil, errors.Trace(err)
	}
	zone, err := s.GetZoneByID(ctx, ziMap.ZoneID)
	if err != nil {
		return nil, errors.Trace(err)
	}
	// QVM 计费项直接跳过
	if product.IsQVMItem(item.Code) {
		return ziMap, nil
	}
	preference, err := s.GetZonePreferenceByItemCode(ctx, item.Code, time.Now())
	if err != nil {
		return nil, errors.Trace(err)
	}
	if preference.ID != 0 {
		preference.ZonePreferences[zone.Code] = true
		err = s.UpdateDefaultUserPreferences(ctx, []string{item.Code}, preference.ZonePreferences, time.Now())
		if err != nil {
			return nil, errors.Trace(err)
		}
	} else {
		err = s.CreateDefaultUserPreferences(ctx, &model.DefaultUserPreference{
			ItemCode:        item.Code,
			ZonePreferences: map[int64]bool{zone.Code: true},
		})
		if err != nil {
			return nil, errors.Trace(err)
		}
	}

	return ziMap, nil
}

// UpdateZoneItemMapByID 通过ID更新机房-计费项关联关系记录
func (s *DictBizService) UpdateZoneItemMapByID(
	ctx context.Context,
	id uint64,
	ziMap *model.ZoneItemMap,
) (*model.ZoneItemMap, error) {
	ziMap.ID = id
	err := s.dictDao.ZoneItemMap.Save(ziMap, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "update zone_item_map failed").
			WithField("zone_item_map", ziMap)
	}
	return ziMap, nil
}

// DeleteZoneItemMapByID 通过ID删除机房-计费项关联关系记录
func (s *DictBizService) DeleteZoneItemMapByID(
	ctx context.Context,
	id uint64,
) (*model.ZoneItemMap, error) {
	ziMap, err := s.dictDao.ZoneItemMap.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	err = s.dictDao.ZoneItemMap.Delete(ziMap)
	if err != nil {
		return nil, errors.Annotate(err, "delete zone_item_map failed").WithField("id", id)
	}
	// 删除机房-计费项关系时，需要更新计费项区域偏好，QVM 计费项除外
	item, err := s.GetItemByID(ctx, ziMap.ItemID)
	if err != nil {
		return nil, errors.Trace(err).WithField("item_id", ziMap.ItemID)
	}
	if product.IsQVMItem(item.Code) {
		return ziMap, nil
	}
	preference, err := s.GetZonePreferenceByItemCode(ctx, item.Code, time.Now())
	if err != nil {
		return nil, errors.Trace(err).WithField("item_code", item.Code)
	}
	zone, err := s.GetZoneByID(ctx, ziMap.ZoneID)
	if err != nil {
		return nil, errors.Trace(err).WithField("zone_id", ziMap.ZoneID)
	}
	zoneToBeDeleted := zone.Code
	newPreference := make(map[int64]bool)
	for zoneCode, enable := range preference.ZonePreferences {
		if zoneCode == zoneToBeDeleted {
			continue
		}
		newPreference[zoneCode] = enable
	}
	err = s.UpdateDefaultUserPreferences(ctx, []string{item.Code}, newPreference, time.Now())
	if err != nil {
		return nil, errors.Trace(err).WithField("updated preference", newPreference)
	}
	return ziMap, nil
}
