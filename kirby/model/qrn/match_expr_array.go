package qrn

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type MatchExprArray []MatchExpr

// DB adapters for StringArray

var _ driver.Valuer = MatchExprArray{}

func (x MatchExprArray) Value() (driver.Value, error) {
	payload, err := json.Marshal(x)
	if err != nil {
		return nil, err
	}
	return string(payload), nil
}

var _ sql.Scanner = (*MatchExprArray)(nil)

func (x *MatchExprArray) Scan(src any) error {
	ns := sql.NullString{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	return json.Unmarshal([]byte(ns.String), x)
}
