package dataplaneModel

import (
	"embed"
	"strings"
	"text/template"
)

//go:embed templates/*
var sqlTemplatesFS embed.FS

var sqlTemplates = template.Must(template.ParseFS(sqlTemplatesFS, "**/*.sql"))

func renderSQLTemplate(templateName string, param any) (string, error) {
	var x strings.Builder
	err := sqlTemplates.ExecuteTemplate(&x, templateName, param)
	if err != nil {
		return "", err
	}

	return x.String(), nil
}

type createDataTableParams struct {
	TableName string
	// PointsSlice works around the fact that text/template can't even do
	// counted loops
	PointsSlice []struct{}
}

func makeCreateDataTableStmt(tableName string, numPoints int) (string, error) {
	return renderSQLTemplate("create_data_table.sql", createDataTableParams{
		TableName:   tableName,
		PointsSlice: make([]struct{}, numPoints),
	})
}

type tableNameParam struct {
	TableName string
}

func makeCreateAggSeriesMetadataTableStmt(tableName string) (string, error) {
	return renderSQLTemplate("create_agg_series_md_table.sql", tableNameParam{
		TableName: tableName,
	})
}

func makeCreateSeriesMetadataTableStmt(tableName string) (string, error) {
	return renderSQLTemplate("create_series_md_table.sql", tableNameParam{
		TableName: tableName,
	})
}

type bulkInsertQueryTemplateParams struct {
	TableName string
	// PointsSlice works around the fact that text/template can't even do
	// counted loops
	PointsSlice []struct{}
	// same for RowsSlice
	RowsSlice []struct{}
}

func makeBulkInsertQueryTemplate(
	tableName string,
	numPoints int,
	numRows int,
) (string, error) {
	return renderSQLTemplate("bulk_insert_query_template.sql", bulkInsertQueryTemplateParams{
		TableName:   tableName,
		PointsSlice: make([]struct{}, numPoints),
		RowsSlice:   make([]struct{}, numRows),
	})
}
