package config

import (
	"os"
	"time"

	"gopkg.in/yaml.v2"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/db"
	"github.com/qbox/bo-base/v4/eventbus"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/pay-sdk/base/account"
)

// CreditConfig configs for credit business
type CreditConfig struct {
	DefaultPageSize         uint64 `yaml:"default_pagesize"`
	MinimumSystemCreditLine int64  `yaml:"minimum_system_credit_line"`
	MaximumSystemCreditLine int64  `yaml:"maximum_system_credit_line"`
}

// ServiceConfig contain host of service dependencies
type ServiceConfig struct {
	GaeaAdmin      string `yaml:"gaea_admin"`      // 获取用户相关 email、phone 信息
	Sofa           string `yaml:"sofa"`            // 获取用户对应销售相关 email、phone 信息
	Morse          string `yaml:"morse"`           // 用于给销售发邮件、短信
	NotificationV2 string `yaml:"notification_v2"` // 新版通知中心，用于给客户发邮件、短信、站内信
	WalletV4       string `yaml:"wallet_v4"`       // 钱包服务，用于查询用户币种信息
}

// NoticeConfig defines configuration for notice
type NoticeConfig struct {
	DelayedDuration time.Duration `yaml:"delayed_duration"` // 延迟多少小时通知用户仍未激活
}

// CreditdConfig is config for creditd service
type CreditdConfig struct {
	RPC          rpc.Config        `yaml:"rpc"`
	Intl         intl.Config       `yaml:"intl"`
	MySQL        db.MySQLConfig    `yaml:"mysql"`
	Cache        dao.CacheConfig   `yaml:"cache"`
	Credit       CreditConfig      `yaml:"credit"`
	AccConfig    account.AccConfig `yaml:"acc"`
	Services     ServiceConfig     `yaml:"services"`
	EventBus     eventbus.Config   `yaml:"eventbus"`
	NoticeConfig NoticeConfig      `yaml:"notice"`
}

// LoadCreditdConfig loads config from yaml file
func LoadCreditdConfig(yamlPath string) (*CreditdConfig, error) {
	byts, err := os.ReadFile(yamlPath)
	if err != nil {
		return nil, err
	}
	conf := &CreditdConfig{}
	err = yaml.UnmarshalStrict(byts, conf)
	if err != nil {
		return nil, err
	}
	return conf, nil
}
