package adapter

import (
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qpay/common"
	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/payment/model"
)

func feedBusinessAt(input *timestamp.Timestamp) (*time.Time, error) {
	var output *time.Time
	now := time.Now()
	if input != nil {
		err := input.CheckValid()
		if err != nil {
			return nil, err
		}
		at := input.AsTime()
		if at.Unix() == 0 {
			output = &now
		} else {
			output = &at
		}
	} else {
		output = &now
	}
	return output, nil
}

func BuildPaymentRequest(request *pb.PaymentRequest) (*model.PaymentRequest, error) {
	businessAt, err := feedBusinessAt(request.BusinessAt)
	if err != nil {
		return nil, err
	}

	return &model.PaymentRequest{
		SnapID:     request.SnapId,
		EntryID:    request.EntryId,
		EntryType:  enums.EntryType(request.EntryType),
		EntryDesc:  request.EntryDesc,
		UID:        request.Uid,
		Money:      base.Money(request.Money),
		Excode:     request.Excode,
		Desc:       request.Desc,
		PaymentID:  request.PaymentId,
		Type:       request.Type,
		Details:    request.Details,
		Prefix:     enums.NewPaymentPrefix(request.Prefix),
		BusinessAt: businessAt,
		PayUid:     request.PayUid,
		Sync:       request.Sync,
		Remark:     request.Remark,

		SkipConfirm: request.SkipConfirm,
	}, nil
}

// BuildBillPaymentRequest build bill payment request
func BuildBillPaymentRequest(request *pb.BillPaymentRequest) (*model.PaymentRequest, error) {
	var relatedMonth *time.Time
	if request.RelatedMonth != nil {
		err := request.RelatedMonth.CheckValid()
		if err != nil {
			return nil, err
		}
		rm := request.RelatedMonth.AsTime()
		relatedMonth = &rm
	}

	businessAt, err := feedBusinessAt(request.PaymentRequest.BusinessAt)
	if err != nil {
		return nil, err
	}

	// 如果外面没有传入entryId, 就帮忙生成一个
	entryId := request.PaymentRequest.EntryId
	if entryId == "" {
		entryId = common.GetUUID()
	}

	return &model.PaymentRequest{
		SnapID:       request.PaymentRequest.SnapId,
		EntryID:      entryId,
		EntryType:    enums.EntryType(request.PaymentRequest.EntryType),
		EntryDesc:    request.PaymentRequest.EntryDesc,
		UID:          request.PaymentRequest.Uid,
		Money:        base.Money(request.PaymentRequest.Money),
		Excode:       request.PaymentRequest.Excode,
		Desc:         request.PaymentRequest.Desc,
		PaymentID:    request.PaymentRequest.PaymentId,
		Type:         request.PaymentRequest.Type,
		Details:      request.PaymentRequest.Details,
		RelatedMonth: relatedMonth,
		Product:      request.Product,
		Group:        request.Group,
		Item:         request.Item,
		Prefix:       enums.NewPaymentPrefix(request.PaymentRequest.Prefix),
		BusinessAt:   businessAt,
		PayUid:       request.PaymentRequest.PayUid,
		Sync:         request.PaymentRequest.Sync,
		SkipConfirm:  request.PaymentRequest.SkipConfirm,
	}, nil
}

// BuildDepositRequest build deposit request
func BuildDepositRequest(request *pb.DepositRequest) (*model.DepositRequest, error) {
	businessAt, err := feedBusinessAt(request.PaymentRequest.BusinessAt)
	if err != nil {
		return nil, err
	}
	return &model.DepositRequest{
		PaymentRequest: model.PaymentRequest{
			EntryID:    request.PaymentRequest.EntryId,
			EntryType:  enums.EntryType(request.PaymentRequest.EntryType),
			EntryDesc:  request.PaymentRequest.EntryDesc,
			UID:        request.PaymentRequest.Uid,
			Money:      base.Money(request.PaymentRequest.Money),
			Excode:     request.PaymentRequest.Excode,
			Desc:       request.PaymentRequest.Desc,
			Type:       request.PaymentRequest.Type,
			Details:    request.PaymentRequest.Details,
			Prefix:     enums.NewPaymentPrefix(request.PaymentRequest.Prefix),
			BusinessAt: businessAt,
			PayUid:     request.PaymentRequest.PayUid,
			Sync:       request.PaymentRequest.Sync,
		},
		RewardCost:         base.Money(request.Cost),
		RewardType:         enums.RewardType(request.RewardType),
		DepositType:        enums.GetDepositType(int(request.DepositType)),
		RewardID:           request.RewardId,
		TxnIDs:             request.TxnIds,
		TransferCash:       base.Money(request.TransferCash),
		TransferNb:         base.Money(request.TransferNb),
		IsHide:             request.IsHide,
		SkipTransferCoupon: request.GetSkipTransferCoupon(),
		RelatedPaymentID:   request.GetRelatedPaymentId(),
		IsTransferRetrieve: request.IsTransferRetrieve,
	}, nil
}

// BuildWithdrawRequest build withdraw request
func BuildWithdrawRequest(request *pb.WithdrawRequest) (*model.DepositRequest, error) {
	businessAt, err := feedBusinessAt(request.PaymentRequest.BusinessAt)
	if err != nil {
		return nil, err
	}
	return &model.DepositRequest{
		PaymentRequest: model.PaymentRequest{
			EntryID:    request.PaymentRequest.EntryId,
			EntryType:  enums.EntryType(request.PaymentRequest.EntryType),
			EntryDesc:  request.PaymentRequest.EntryDesc,
			UID:        request.PaymentRequest.Uid,
			Money:      base.Money(request.PaymentRequest.Money),
			Excode:     request.PaymentRequest.Excode,
			Desc:       request.PaymentRequest.Desc,
			Type:       request.PaymentRequest.Type,
			Details:    request.PaymentRequest.Details,
			Prefix:     enums.NewPaymentPrefix(request.PaymentRequest.Prefix),
			BusinessAt: businessAt,
			PayUid:     request.PaymentRequest.PayUid,
		},

		DesignatedWithdrawPaymentID: request.PaymentRequest.DesignatedWithdrawPaymentId,
		DepositType:                 enums.GetDepositType(int(request.DepositType)),
		IsHide:                      request.IsHide,
	}, nil
}

// BuildRefundRequest build refund request
func BuildRefundRequest(request *pb.PaymentRequest) (*model.RefundRequest, error) {
	paymentRequest, err := BuildPaymentRequest(request)
	return &model.RefundRequest{
		PaymentRequest: *paymentRequest,
	}, err
}
