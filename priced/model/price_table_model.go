package model

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// PriceTableType enum type definition
type PriceTableType string

const (
	// PriceTableTypeDefault 公开报价
	PriceTableTypeDefault PriceTableType = "DEFAULT"
	// PriceTableTypeVIP 自定义价格
	PriceTableTypeVIP PriceTableType = "VIP"
	// PriceTableTypeFinal 最终价格表
	PriceTableTypeFinal PriceTableType = "FINAL"
	// PriceTableTypeCost 成本价格表
	PriceTableTypeCost PriceTableType = "COST"
	// PriceTableTypeRefCost 参考成本价格表，用于销售考核场景下的成本计算
	PriceTableTypeRefCost PriceTableType = "REF_COST"
)

// IsValid check whether the price type is valid
func (t PriceTableType) IsValid() bool {
	switch t {
	case PriceTableTypeDefault,
		PriceTableTypeVIP,
		PriceTableTypeFinal,
		PriceTableTypeCost,
		PriceTableTypeRefCost:
		return true
	}
	return false
}

// cache 中的 key 名称
func (t PriceTableType) cacheKey() string {
	// 所有用户共享一份的价格表，cache key 只和类型有关
	switch t {
	case PriceTableTypeDefault:
		return "price:price_table_x:public_price"
	case PriceTableTypeCost:
		return "price:price_table_x:cost_price"
	case PriceTableTypeRefCost:
		return "price:price_table_x:ref_cost_price"
	}
	// VIP 和 FINAL 是用户级别的，还和 uid 有关，不在这里定义 cache key
	return ""
}

// Value implements the driver.Valuer interface
func (t PriceTableType) Value() (driver.Value, error) {
	return string(t), nil
}

// Scan implements the sql.Scanner interface
func (t *PriceTableType) Scan(src any) error {
	if src == nil {
		*t = ""
	} else {
		*t = PriceTableType(string(src.([]uint8)))
	}
	return nil
}

// PriceTable model definition
type PriceTable struct {
	ID         uint64         `gorm:"primary_key"`
	Type       PriceTableType `gorm:"type:varchar(64)"`
	Remark     string         `gorm:"type:varchar(512)"`
	IsDisabled bool
	CreatedAt  time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt  time.Time `sql:"type:DATETIME(6)"`
	Items      []PriceItem
}

// Clone new a instance of PriceTable same as m
func (m *PriceTable) Clone() PriceTable {
	replica := *m

	if len(m.Items) > 0 {
		replica.Items = make([]PriceItem, len(m.Items))
		copy(replica.Items, m.Items)
	}

	return replica
}

// PriceTableDao is data access object of Price model
type PriceTableDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *PriceTableDao) GetCachePrefix() string {
	return "price:price_table:"
}

// GetCacheRefs get cache refs
func (d *PriceTableDao) GetCacheRefs() []dao.CacheLayer {
	return []dao.CacheLayer{
		(*PriceTableDao)(nil),
		(*PriceUserMapDao)(nil),
	}
}

// NewPriceTableDao is constructor of PriceTableDao
func NewPriceTableDao(base *dao.BaseDao) *PriceTableDao {
	cachePrefix := (*PriceTableDao)(nil).GetCachePrefix()
	return &PriceTableDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id:{ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetAllByTypeAndIsDisabled get by price type and whether disabled
func (d *PriceTableDao) GetAllByTypeAndIsDisabled(priceType PriceTableType, isDisabled bool, expires ...time.Duration) (prices []PriceTable, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:all:type=%s&disabled=%t",
		d.cachePrefix, priceType, isDisabled,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Find(value, "`type`=? AND `is_disabled`=?", priceType, isDisabled).Error
	}, keys, &prices, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("type", priceType).WithField("is_disabled", isDisabled)
	}
	return
}

// GetByID select a record of PriceTable by id
func (d *PriceTableDao) GetByID(id uint64, expires ...time.Duration) (*PriceTable, error) {
	model := &PriceTable{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, err
}

// ListByIDs list price table by ids
func (d *PriceTableDao) ListByIDs(ids []uint64) (pts []*PriceTable, err error) {

	err = d.base.Model(&PriceTable{}).Where("`id` IN (?)", ids).Find(&pts).Error

	if err != nil {
		return nil, errors.Trace(err).WithField("ids", ids)
	}
	return pts, nil
}

// GetWithItemsByID is the same as GetByID but with items filled in.
func (d *PriceTableDao) GetWithItemsByID(id uint64, expires ...time.Duration) (*PriceTable, error) {
	price, err := d.GetByID(id, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	keys := dao.NewCacheKeysFmt(
		"%s:item:%d:items", d.cachePrefix, id,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceItem{}).Find(value, "price_id = ?", id).Error
	}, keys, &price.Items, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("price", price)
	}
	return price, err
}

// Save insert or update a record of PriceTable by id
func (d *PriceTableDao) Save(model *PriceTable, expires ...time.Duration) error {
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// Delete delete a record of PriceTable
func (d *PriceTableDao) Delete(model *PriceTable) error {
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// DeleteByID delete a record of PriceTable by id
func (d *PriceTableDao) DeleteByID(id uint64) error {
	model := &PriceTable{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}

// List select records of PriceTable
func (d *PriceTableDao) List(offset, limit int, expires ...time.Duration) (priceTables []PriceTable, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:offset=%d&limit=%d",
		d.cachePrefix, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Offset(offset).Limit(limit).Find(value).Error
	}, keys, &priceTables, expires...)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return
}

// Count select count of PriceTable
func (d *PriceTableDao) Count(expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeys(
		"%s:count",
		d.cachePrefix,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceTable{}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err)
	}
	return
}

// GetCostPriceTable query cost price table, currently only 1 cost table exists
func (d *PriceTableDao) GetCostPriceTable(expires ...time.Duration) (*PriceTable, error) {
	return d.getOnePriceTableByPriceTableType(PriceTableTypeCost, expires...)
}

// GetRefCostPriceTable query reference cost price table, currently only 1 ref cost table exists
func (d *PriceTableDao) GetRefCostPriceTable(expires ...time.Duration) (*PriceTable, error) {
	return d.getOnePriceTableByPriceTableType(PriceTableTypeRefCost, expires...)
}

// GetPublicPriceTable query public price table, currently only 1 public table exists
func (d *PriceTableDao) GetPublicPriceTable(expires ...time.Duration) (*PriceTable, error) {
	return d.getOnePriceTableByPriceTableType(PriceTableTypeDefault, expires...)
}

func (d *PriceTableDao) getOnePriceTableByPriceTableType(t PriceTableType, expires ...time.Duration) (*PriceTable, error) {
	priceTableType, _ := t.Value() // t.Value() 的实现，err 只会返回 nil，这里忽略 err

	model := &PriceTable{}
	keys := dao.NewCacheKeysFmt(t.cacheKey())
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceTable{}).Where("`type` = ?", priceTableType).First(value).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return model, err
}

// GetAllPriceTablesByUID query public price table
func (d *PriceTableDao) GetAllPriceTablesByUID(uid uint64, expires ...time.Duration) (priceTables []PriceTable, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:uid=%d",
		d.cachePrefix, uid,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceTable{}).Where("`uid` = ?", uid).Find(value).Error
	}, keys, &priceTables, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("uid", uid)
	}
	return
}

// EnablePriceTable make price_table to take effect
func (d *PriceTableDao) EnablePriceTable(id uint64) (err error) {
	err = d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Model(PriceTable{}).Where("id = ?", id).Update("is_disabled", false).Error
	}, d.keys, &PriceTable{ID: id}, d)
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}

	return
}

// DisablePriceTable make price_table disabled
func (d *PriceTableDao) DisablePriceTable(id uint64) (err error) {
	err = d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Model(PriceTable{}).Where("id = ?", id).Update("is_disabled", true).Error
	}, d.keys, &PriceTable{ID: id}, d)
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}

	return
}

// GetFinalPriceTableByUID query user's final price_talbe currently in effect
func (d *PriceTableDao) GetFinalPriceTableByUID(uid uint64, expires ...time.Duration) (*PriceTable, error) {
	model := &PriceTable{}
	keys := dao.NewCacheKeysFmt(
		"%s:curr_final_price:uid=%d",
		d.cachePrefix, uid,
	)

	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceTable{}).
			Joins("LEFT JOIN `price_user_maps` ON `price_user_maps`.`price_id` =`price_tables`.`id`").
			Where("`price_user_maps`.`uid` = ?", uid).
			Where("`price_tables`.`is_disabled` = ?", false).
			Where("`price_tables`.`type` = ?", PriceTableTypeFinal).
			// BO-12111 万一出现多张 FINAL 表生效，确保查出来是最后一条记录
			// 因为有缓存，所以不方便直接用 All 替换（返回类型变了）
			Order("`price_tables`.`id` DESC").
			First(value).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return model, nil
}

// DelPriceTableCachesByUID 移除指定用户的报价单缓存数据
func (d *PriceTableDao) DelPriceTableCachesByUID(uid uint64) (err error) {
	keys := []string{
		fmt.Sprintf("%s:curr_final_price:uid=%d", d.cachePrefix, uid),
		fmt.Sprintf("%s:uid=%d", d.cachePrefix, uid),
	}

	for _, key := range keys {
		err1 := d.base.CacheDel(key)
		if err1 != nil && err1 != dao.ErrCacheMiss {
			err = err1
		}
	}

	return err
}
