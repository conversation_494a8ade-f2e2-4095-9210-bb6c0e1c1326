package dataplaneModel

import (
	"hash/fnv"
	"sort"

	"github.com/samber/lo"
	"qiniu.io/pay/kirby/model/adminModel"
)

type SPHasher map[string]string

func (x SPHasher) Hash() uint32 {
	h := fnv.New32()
	if len(x) == 0 {
		return h.Sum32()
	}

	keys := lo.Keys(x)
	sort.Strings(keys)

	sep := false
	for _, k := range keys {
		if k == adminModel.BuiltinSPKeyQRNResource {
			continue
		}
		if len(x[k]) == 0 {
			continue
		}

		if sep {
			h.Write([]byte{'&'})
		} else {
			sep = true
		}
		h.Write([]byte(k))
		h.Write(([]byte{'='}))
		h.Write([]byte(x[k]))
	}

	return h.Sum32()
}
