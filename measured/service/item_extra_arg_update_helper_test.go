package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service"
	measureTest "qiniu.io/pay/measured/test"
)

func TestSyncItemExtraArgs(t *testing.T) {
	sandbox := measureTest.BuildSandbox(t)

	srv := sandbox.MeasureService
	ctx := context.Background()
	itemID := uint64(233)

	resp, err := srv.ListItemExtraArgsByItemID(ctx, itemID, 0, -1)
	assert.NoError(t, err)
	assert.Empty(t, resp)

	err = srv.SyncItemExtraArgs(
		ctx,
		&service.SyncItemExtraArgsParam{
			ItemID: 233,
			Args: []model.ItemExtraArg{
				{
					ItemID: 233,
					Key:    "stat_g",
					Value:  "5min",
				},
				{
					ItemID: 233,
					Key:    "stat_expr",
					Value:  "v",
				},
				{
					ItemID: 233,
					Key:    "stat_key",
					Value:  "foo",
				},
			},
		},
	)
	assert.NoError(t, err)

	resp, err = srv.ListItemExtraArgsByItemID(ctx, itemID, 0, -1)
	assert.NoError(t, err)
	assert.Len(t, resp, 3)
	assert.EqualValues(t, map[string]string{
		"stat_expr": "v",
		"stat_key":  "foo",
		"stat_g":    "5min",
	}, makeMapFromItemExtraArgs(resp))

	err = srv.SyncItemExtraArgs(
		ctx,
		&service.SyncItemExtraArgsParam{
			ItemID: 233,
			Args: []model.ItemExtraArg{
				{
					ItemID: 233,
					Key:    "stat_expr",
					Value:  "v*8/300",
				},
				{
					ItemID: 233,
					Key:    "stat_key",
					Value:  "foo",
				},
				{
					ItemID: 233,
					Key:    "stat_select",
					Value:  "flow",
				},
			},
		},
	)
	assert.NoError(t, err)

	resp, err = srv.ListItemExtraArgsByItemID(ctx, itemID, 0, -1)
	assert.NoError(t, err)
	assert.Len(t, resp, 3)
	assert.EqualValues(t, map[string]string{
		"stat_expr":   "v*8/300",
		"stat_key":    "foo",
		"stat_select": "flow",
	}, makeMapFromItemExtraArgs(resp))
}

func makeMapFromItemExtraArgs(resp []model.ItemExtraArg) map[string]string {
	respMap := make(map[string]string)
	for _, kv := range resp {
		respMap[kv.Key] = kv.Value
	}
	return respMap
}
