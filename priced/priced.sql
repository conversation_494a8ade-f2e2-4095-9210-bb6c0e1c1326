-- <PERSON>DB dump 10.19  Distrib 10.7.3-MariaDB, for osx10.17 (arm64)
--
-- Host: ************    Database: pay_price
-- ------------------------------------------------------
-- Server version	5.7.20

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `pay_price`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `pay_price` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `pay_price`;

--
-- Table structure for table `price_item_stairs`
--

DROP TABLE IF EXISTS `price_item_stairs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `price_item_stairs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `price_item_id` bigint(20) unsigned DEFAULT NULL,
  `unit_id` bigint(20) unsigned DEFAULT NULL,
  `quantity` bigint(20) unsigned DEFAULT NULL,
  `price` decimal(20, 8) unsigned DEFAULT NULL,
  `order` bigint(20) unsigned DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_price_item_stair_price_item_id_quantity_order` (`price_item_id`,`quantity`,`order`),
  KEY `idx_price_item_stair_unit_id` (`unit_id`),
  KEY `idx_price_item_stair_created_at` (`created_at`),
  KEY `idx_price_item_stair_updated_at` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=454539138 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `price_items`
--

DROP TABLE IF EXISTS `price_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `price_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `price_id` bigint(20) unsigned DEFAULT NULL,
  `item_id` bigint(20) unsigned DEFAULT NULL,
  `zone_id` bigint(20) unsigned DEFAULT NULL,
  `algorithm_id` bigint(20) unsigned DEFAULT NULL,
  `stair_price_type` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_disabled` tinyint(1) DEFAULT NULL,
  `unit_rate` bigint(20) unsigned DEFAULT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `effect_time` bigint(20) DEFAULT NULL,
  `dead_time` bigint(20) DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `should_skip` tinyint(1) DEFAULT NULL,
  `cumulative_cycle` bigint(20) DEFAULT '0',
  `bill_period_type` bigint(20) DEFAULT '0',
  `is_default_algorithm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为该计费项公开价的默认计费方式',
  `currency_type` VARCHAR(10) NOT NULL COMMENT '币种（如CNY USD）',
  PRIMARY KEY (`id`),
  KEY `idx_price_item_algorithm_id` (`algorithm_id`),
  KEY `idx_price_item_stair_price_type` (`stair_price_type`),
  KEY `idx_price_item_price_id_item_id_zone_id_effect_dead_time` (`price_id`,`item_id`,`zone_id`,`effect_time`,`dead_time`),
  KEY `idx_price_item_created_at` (`created_at`),
  KEY `idx_price_item_updated_at` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=454042535 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `price_tables`
--

DROP TABLE IF EXISTS `price_tables`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `price_tables` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(8192) COLLATE utf8mb4_unicode_ci DEFAULT '',
  `is_disabled` tinyint(1) DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_price_table_created_at` (`created_at`),
  KEY `idx_price_table_updated_at` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=40042493 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `price_user_algorithms`
--

DROP TABLE IF EXISTS `price_user_algorithms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `price_user_algorithms` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) unsigned NOT NULL DEFAULT '0',
  `item_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `zone_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计费区域 id',
  `algorithm_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户采用的计费方式 id',
  `effect_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '生效时间',
  `dead_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '失效时间',
  `is_disabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '该条是否禁用',
  `description` varchar(255) NOT NULL DEFAULT '',
  `created_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
  `updated_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `price_user_maps`
--

DROP TABLE IF EXISTS `price_user_maps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `price_user_maps` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) unsigned DEFAULT NULL,
  `price_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_price_user_map_uid_price_id` (`uid`,`price_id`),
  KEY `idx_price_id` (`price_id`)
) ENGINE=InnoDB AUTO_INCREMENT=32642 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qvm_group_discounts`
--

DROP TABLE IF EXISTS `qvm_group_discounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qvm_group_discounts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `qvm_price_table_id` bigint(20) unsigned NOT NULL COMMENT '关联的 QVMPriceTable ID',
  `uid` bigint(20) unsigned NOT NULL COMMENT '冗余的 QVMPriceTable UID 字段',
  `group_code` varchar(256) NOT NULL COMMENT '计费项 code',
  `zone_code` bigint(20) NOT NULL COMMENT '区域 code',
  `is_disabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '该条报价是否被禁用',
  `effect_time` bigint(20) NOT NULL COMMENT '生效时刻（含），百纳秒',
  `dead_time` bigint(20) NOT NULL COMMENT '失效时刻（不含），百纳秒',
  `discount` int(10) unsigned NOT NULL COMMENT '折扣的百分比，100 意为不打折',
  `created_at` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_qvmgd_qvmptid_group_zone_effect_dead_ctime` (`qvm_price_table_id`,`group_code`,`zone_code`,`effect_time`,`dead_time`,`created_at`),
  KEY `idx_qvmgd_uid_group_zone_effect_dead_ctime` (`uid`,`group_code`,`zone_code`,`effect_time`,`dead_time`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qvm_price_item_stairs`
--

DROP TABLE IF EXISTS `qvm_price_item_stairs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qvm_price_item_stairs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `qvm_price_item_id` bigint(20) unsigned NOT NULL COMMENT '关联的 QVMPriceItem ID',
  `uid` bigint(20) unsigned NOT NULL COMMENT '冗余的 QVMPriceTable uid 字段',
  `item_code` varchar(256) NOT NULL COMMENT '冗余的 QVMPriceItem item_code 字段',
  `quantity` bigint(20) unsigned NOT NULL COMMENT '阶梯的量',
  `price` decimal(20,8) NOT NULL COMMENT '阶梯价格，8 位小数的定点数',
  `order` bigint(20) unsigned NOT NULL COMMENT '本条记录在阶梯中的顺序。将同一 QVMPriceItem 关联的所有本类型记录升序排序，即得正确的价格表 v3 样式阶梯。',
  `created_at` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_qvmpis_qvmpiid_order` (`qvm_price_item_id`,`order`),
  KEY `idx_qvmpis_uid_item` (`uid`,`item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qvm_price_items`
--

DROP TABLE IF EXISTS `qvm_price_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qvm_price_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `qvm_price_table_id` bigint(20) unsigned NOT NULL COMMENT '关联的 QVMPriceTable ID',
  `uid` bigint(20) unsigned NOT NULL COMMENT '冗余的 QVMPriceTable UID 字段',
  `item_code` varchar(256) NOT NULL COMMENT '计费项 code',
  `zone_code` bigint(20) NOT NULL COMMENT '区域 code',
  `is_disabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '该条报价是否被禁用',
  `stair_price_type` varchar(64) NOT NULL COMMENT '阶梯类型，语义同价格表 v3',
  `currency_type` varchar(10) NOT NULL COMMENT '币种信息（如 CNY USD）',
  `effect_time` bigint(20) NOT NULL COMMENT '生效时刻（含），百纳秒',
  `dead_time` bigint(20) NOT NULL COMMENT '失效时刻（不含），百纳秒',
  `created_at` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_qvmpi_qvmptid_item_zone_effect_dead_curr_ctime` (`qvm_price_table_id`,`item_code`,`zone_code`,`effect_time`,`dead_time`,`currency_type`,`created_at`),
  KEY `idx_qvmpi_uid_item_zone_effect_dead_curr_ctime` (`uid`,`item_code`,`zone_code`,`effect_time`,`dead_time`,`currency_type`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qvm_price_tables`
--

DROP TABLE IF EXISTS `qvm_price_tables`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qvm_price_tables` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) unsigned NOT NULL COMMENT 'UID，为 0 代表公开报价，不为 0 代表改价。目前 uid = 0 的价格表应该只有一张，但不排除之后会改变',
  `remark` varchar(8192) NOT NULL DEFAULT '' COMMENT '内部描述，用户不可见',
  `is_disabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '本报价单是否被整体禁用',
  `created_at` datetime(6) DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_qvmpt_uid` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2022-03-30 22:09:03
