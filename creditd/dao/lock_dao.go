package dao

import (
	"fmt"

	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/creditd/model"
)

// Lock<PERSON>ao is data access object of Lock model
type LockDao struct {
	base *dao.BaseDao
}

// NewLockDao is constructor of LockDao
func NewLockDao(base *dao.BaseDao) *LockDao {
	return &LockDao{
		base: base,
	}
}

// RegisterMigrateLock migrates Lock model
func RegisterMigrateLock(db *gorm.DB) {
	db.AutoMigrate(&model.Lock{})
}

// Save inserts or updates a record of lock by id
func (d *LockDao) Save(m *model.Lock) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.Save(m).Error

	if err != nil {
		return errors.Trace(err).WithField("model", m)
	}

	return nil
}
