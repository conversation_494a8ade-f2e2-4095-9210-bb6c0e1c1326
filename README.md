# Projects of pay system in Qiniu [![codecov](https://codecov.io/gh/qbox/bo-pay/branch/develop/graph/badge.svg?token=LkZcYVrABE)](https://codecov.io/gh/qbox/bo-pay)

<!-- NOTE: 用 doctoc --notitle README.md 刷新下面这个 -->
<!-- START doctoc generated TOC please keep comment here to allow auto update -->
<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->

- [项目说明](#%E9%A1%B9%E7%9B%AE%E8%AF%B4%E6%98%8E)
- [本地开发](#%E6%9C%AC%E5%9C%B0%E5%BC%80%E5%8F%91)
  - [下载代码](#%E4%B8%8B%E8%BD%BD%E4%BB%A3%E7%A0%81)
  - [运行服务](#%E8%BF%90%E8%A1%8C%E6%9C%8D%E5%8A%A1)
  - [运行测试](#%E8%BF%90%E8%A1%8C%E6%B5%8B%E8%AF%95)
  - [注意事项](#%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9)

<!-- END doctoc generated TOC please keep comment here to allow auto update -->

## 项目说明

|包名|业务含义|独立进程？|归属进程|说明|
|:--:|:--:|:--------:|:------:|:---|
|`coupon`|新版抵用券|:x:|`walletd`|[新版抵用券项目][new-coupon-project]的后端服务。|
|`creditd`|信用额度服务|:white\_check\_mark:|-|[信用风控一期项目][credit-project]的后端服务。|
|`dictd`|数据字典服务|:white\_check\_mark:|-|提供包括机房、计费项组、计费项、计费项数据类型及其单位和算法等基础数据服务。|
|`jm`|任务队列服务|:x:|`walletd`|为了能在 [portal.io](https://portal.qiniu.io) 界面上方便重试失败的出账任务，而临时自制的 `qboxwalletBizd` 任务状态持久化服务。|
|`kirby`|计量中心 feat. 星のカービィ (2022 version)|:x:|`measured`|[计量中心][measure-center-project]的 2022 年建设部分。|
|`measured`|计量中心|:white\_check\_mark:|-|<ul><li>**价格表部分**：提供计量相关的服务，包括用户持有的资源包的额度数据，计量时所用到的附加参数等。</li><li>**非价格表部分**：[计量中心][measure-center-project] 2022 年之前建设完成的部分后端。</li></ul>|
|`measureproxyd`|[拉量代理][measureproxy-doc]|:white\_check\_mark:|-|现有 HTTP 接口计量源的 gRPC adapter。|
|`pland`|sufy plan|:white\_check\_mark:|-|sufy 国际站 plan 订阅相关服务。|
|`priced`|价格表 v4|:white\_check\_mark:|-|提供除 QVM 之外其他产品线的公开报价以及用户 VIP 价格的读取、修改，以及最终价格表的读取服务。|
|`qpay`|新钱包（new wallet）|:x:|`walletd`|基于 MySQL 表结构实现的（顾名思义）新钱包系统。<br />此处只有业务，接口层代码在 `walletd/action`。|
|`qvmprice`|QVM 价格表|:x:|`priced`|[为优化性能与储存空间，专为 QVM 设计][qvm-price-doc]的价格表服务。|
|`walletd`|wallet v4|:white\_check\_mark:|-|<ul><li>**价格表部分**：提供与用户账户余额、抵用券、折扣券、返利券等财产状态数据的读取、修改服务。</li><li>**非价格表部分**：主要提供新的钱包服务，和塞进来的其他几个小服务。</li></ul>|

传统意义上的“Pay V4”是指实现了[价格表 V4 数据模型][pay-v4-models]的以下四个服务（进程），也是本库中年龄最大的四个包：

[new-coupon-project]: https://cf.qiniu.io/pages/viewpage.action?pageId=33735683
[credit-project]: https://cf.qiniu.io/pages/viewpage.action?pageId=26911192
[jm-epic]: https://jira.qiniu.io/browse/BO-9943
[measure-center-project]: https://cf.qiniu.io/pages/viewpage.action?pageId=21004891
[measureproxy-doc]: https://cf.qiniu.io/pages/viewpage.action?pageId=23794713
[qvm-price-doc]: https://cf.qiniu.io/pages/viewpage.action?pageId=89892428
[pay-v4-models]: https://cf.qiniu.io/pages/viewpage.action?pageId=58269438

- `dictd`
- `measured`
- `walletd`
- `priced`

彼时（2018）本库只有这四个服务（进程），它们也都仅仅包含价格表相关的表和功能，即会被 [`priceshimd`][priceshim-src] 用到的那部分接口和功能。
但由于业务发展，以及对技术架构的理解发生变化等原因，目前（2022.03）除 `dictd` 和 `priced` 以外的两个服务都获得了一些不属于“价格表部分”的新功能，还新增了一些别的服务进程。目前，“Pay V4”的提法经常默认泛指本库中的所有业务了，有些时候在表达和理解上需要注意。

[priceshim-src]: https://github.com/qbox/pay/tree/develop/priceshim

可以构建出独立可执行文件，以独立进程形式运行的那些包名都以 `d` 结尾，包名同时也是可执行文件名、进程名。

## 本地开发

为避免踩坑，开发前请阅读[开发指南与注意事项](HACKING.md)。

### 下载代码

**bo-pay** 使用 go mod 来管理依赖，不依赖 [qbox/base](https://github.com/qbox/base) 库。可以放在任何地方。与所有 go mod 管理的代码库相同，依赖的软件包会在构建时自动被下载。

注意：**bo-pay** 按照社区标准的做法管理源码，没有 env.sh 来设置 `$GOPATH`，和七牛其他年代较早项目的管理方式不同。

### 运行服务

```shell
make dictd
make priced
make measured
make measureproxyd
make walletd
make pland
```

### 运行测试

需要有 MySQL (MariaDB) 和 Redis 服务，并设置环境变量指向它们。推荐使用
`direnv` 等环境变量管理工具：

```shell
export MYSQL_HOST=localhost
export MYSQL_PORT=3306
export MYSQL_USERNAME=root
export MYSQL_PASSWORD=

export REDIS_ADDRS=127.0.0.1:6379

# 集成测试还需要额外设置 TEST_HOST 到运行有 Docker 的机器
export TEST_HOST=localhost
```

单元测试：

```shell
# 在项目根目录下，运行全部单元测试
make test

# 或者在相应包目录下
go test -v ./...
```

集成测试：每个 PR 提上去就会自动跑，维护在 [qbox/qtest 的 bo 子目录](https://github.com/qbox/qtest/tree/develop/bo)下。

如果没权限，找 QA 要

## 注意事项
- 因国际化的时区信息需要从 context 中获取，所以业务中不要使用 context.Background()/TODO() 等不含时区信息的 ctx，而是一路透传 ctx
