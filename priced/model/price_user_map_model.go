package model

import (
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// PriceUserMap model definition
type PriceUserMap struct {
	ID        uint64    `gorm:"primary_key"`
	UID       uint64    `gorm:"unique_index:idx_price_user_map_uid_price_id"`
	PriceID   uint64    `gorm:"unique_index:idx_price_user_map_uid_price_id;index:idx_price_id"`
	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// PriceUserMapDao is data access object of PriceUserMap model
type PriceUserMapDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *PriceUserMapDao) GetCachePrefix() string {
	return "price:price_user_map:"
}

// GetCacheRefs get cache refs
func (d *PriceUserMapDao) GetCacheRefs() []dao.CacheLayer {
	return []dao.CacheLayer{
		(*PriceTableDao)(nil),
		(*PriceUserMapDao)(nil),
	}
}

// NewPriceUserMapDao is constructor of PriceUserMapDao
func NewPriceUserMapDao(base *dao.BaseDao) *PriceUserMapDao {
	cachePrefix := (*PriceUserMapDao)(nil).GetCachePrefix()
	return &PriceUserMapDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id:{ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of PriceUserMap by id
func (d *PriceUserMapDao) GetByID(id uint64, expires ...time.Duration) (*PriceUserMap, error) {
	model := &PriceUserMap{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, err
}

// Save insert or update a record of PriceUserMap by id
func (d *PriceUserMapDao) Save(model *PriceUserMap, expires ...time.Duration) error {
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}

	// NOTE: 缓存依赖管理可用之前，先手动更新关键数据的缓
	return d.DelPriceUserMapCachesByUID(model.UID)
}

// Delete delete a record of PriceUserMap
func (d *PriceUserMapDao) Delete(model *PriceUserMap) error {
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// DeleteByID delete a record of PriceUserMap by id
func (d *PriceUserMapDao) DeleteByID(id uint64) error {
	model := &PriceUserMap{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}

// List select records of PriceUserMap
func (d *PriceUserMapDao) List(offset, limit int, expires ...time.Duration) (priceUserMaps []PriceUserMap, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:offset=%d&limit=%d",
		d.cachePrefix, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Offset(offset).Limit(limit).Find(value).Error
	}, keys, &priceUserMaps, expires...)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return
}

// Count select count of PriceUserMap
func (d *PriceUserMapDao) Count(expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeys(
		"%s:count",
		d.cachePrefix,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceUserMap{}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err)
	}
	return
}

// ListByUID select records of PriceUserMap by uid
func (d *PriceUserMapDao) ListByUID(uid uint64, offset, limit int, expires ...time.Duration) (priceUserMaps []PriceUserMap, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:uid=%d&offset=%d&limit=%d",
		d.cachePrefix, uid, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&PriceUserMap{UID: uid}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &priceUserMaps, expires...)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return
}

// CountByUID select count of PriceUserMap by uid
func (d *PriceUserMapDao) CountByUID(uid uint64, expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:uid=%d:count",
		d.cachePrefix, uid,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceUserMap{}).Where(&PriceUserMap{UID: uid}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithField("uid", uid)
	}
	return
}

// ListByPriceID select records of PriceUserMap by price_id
func (d *PriceUserMapDao) ListByPriceID(priceID uint64, offset, limit int, expires ...time.Duration) (priceUserMaps []PriceUserMap, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:price_id=%d&offset=%d&limit=%d",
		d.cachePrefix, priceID, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&PriceUserMap{PriceID: priceID}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &priceUserMaps, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("price_id", priceID)
	}
	return
}

// CountByPriceID select count of PriceUserMap by price_id
func (d *PriceUserMapDao) CountByPriceID(priceID uint64, expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:price_id=%d:count",
		d.cachePrefix, priceID,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceUserMap{}).Where(&PriceUserMap{PriceID: priceID}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithField("price_id", priceID)
	}
	return
}

// DelPriceUserMapCachesByUID 移除指定用户的 PriceUserMap 缓存数据
func (d *PriceUserMapDao) DelPriceUserMapCachesByUID(uid uint64) (err error) {
	keys := []string{
		fmt.Sprintf("%s:uid=%d:count", d.cachePrefix, uid),
		fmt.Sprintf("%s:uid=%d&offset=%d&limit=%d", d.cachePrefix, uid, 0, -1),
	}

	for _, key := range keys {
		err1 := d.base.CacheDel(key)
		if err1 != nil && err1 != dao.ErrCacheMiss {
			err = err1
		}
	}
	return err
}
