package service

import (
	"context"
	"errors"
	"net/http"

	"github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/gaea/client/operations"
	"github.com/qbox/pay-sdk/gaea/models"
	"github.com/qbox/pay-sdk/middleware/logging"
)

const (
	FreezeSourceCredit = 8
)

// FreezeService is service handler of freeze
type FreezeService struct {
	gaeaClient *client.Gaea
}

// NewFreezeService is constructor of FreezeService
func NewFreezeService(client *client.Gaea) (*FreezeService, error) {
	return &FreezeService{
		gaeaClient: client,
	}, nil
}

// TriggerUnfreeze invokes freeze service's TriggerFreeze interface with unfreeze only
func (f *FreezeService) TriggerUnfreeze(ctx context.Context, uid uint64) (err error) {
	l := logging.GetLogger(ctx)
	resp, err := f.gaeaClient.Operations.TriggerFreeze(operations.NewTriggerFreezeParams().
		WithUID(uid).WithTriggerFreezeIn(&models.TriggerFreezeReq{
		Source: FreezeSourceCredit,
		Option: 2, // 仅解冻
	}))
	if err != nil {
		l.WithError(err).WithField("uid", uid).Error("FreezeService.TriggerUnfreeze Post failed")
		return err
	}
	payload := resp.GetPayload()
	if payload == nil {
		err = errors.New("response payload is nil")
		l.WithError(err).WithField("uid", uid).Errorf("invoke triggerFreeze failed")
		return err
	}
	if payload.Code != http.StatusOK {
		err = errors.New(resp.GetPayload().Message)
		l.WithError(err).WithField("uid", uid).Error("invoke triggerFreeze failed")
		return err
	}
	l.Infof("FreezeService.TriggerUnfreeze success, uid:%d", uid)

	return nil
}
