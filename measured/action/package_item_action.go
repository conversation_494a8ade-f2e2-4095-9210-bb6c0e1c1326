package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/action/adapter"
)

// impl PayMeasureServiceServer for MeasureAction

// GetPackageItemByID 根据 ID 查询单个资源包项目
func (a *MeasureAction) GetPackageItemByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.PackageItem, error) {
	obj, err := a.srv.GetPackageItemByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageItem(obj)
}

// ListPackageItemsByPackageID 根据资源包 ID 列出资源包项目列表
func (a *MeasureAction) ListPackageItemsByPackageID(
	ctx context.Context,
	p *pb.IDPagingParam,
) (*pb.PackageItemList, error) {
	count, err := a.srv.CountPackageItemsByPackageID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(p)
	objs, err := a.srv.ListPackageItemsByPackageID(ctx, p.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.PackageItemList{
		Count:        count,
		PackageItems: make([]*pb.PackageItem, len(objs)),
	}
	for i, obj := range objs {
		result, err := adapter.BuildPbPackageItem(&obj)
		if err != nil {
			return nil, err
		}
		list.PackageItems[i] = result
	}
	return list, nil
}

// CountPackageItemsByPackageID 根据资源包 ID 获取资源包项目数量
func (a *MeasureAction) CountPackageItemsByPackageID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.srv.CountPackageItemsByPackageID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// CreatePackageItem 创建一个资源包项目
func (a *MeasureAction) CreatePackageItem(
	ctx context.Context,
	p *pb.PackageItem,
) (*pb.PackageItem, error) {
	obj, err := adapter.BuildModelPackageItem(p)
	if err != nil {
		return nil, err
	}
	obj, err = a.srv.CreatePackageItem(ctx, obj)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageItem(obj)
}

// DeletePackageItemByID 根据 ID 删除一个资源包项目
func (a *MeasureAction) DeletePackageItemByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.PackageItem, error) {
	obj, err := a.srv.DeletePackageItemByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageItem(obj)
}
