# 对应镜像 aslan-spock-register.qiniu.io/qa/bo-base:latest 的 Dockerfile
# 更新基础镜像步骤：
# 第一步: 修改本 dockerfile 内容
# 第二步: docker build --rm -f base.Dockerfile --platform linux/amd64 --no-cache -t aslan-spock-register.qiniu.io/qa/bo-base:latest .
# 第三步: docker push aslan-spock-register.qiniu.io/qa/bo-base:latest
FROM ubuntu:14.04

RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN ln -s /usr/bin/python2.7 /usr/bin/python

RUN sed -i s:archive.ubuntu.com:mirrors.aliyun.com:g /etc/apt/sources.list

RUN apt-get clean && apt-get update && \
      apt-get install -y \
      mysql-client \
      mongodb-clients \
      python2.7 \
      ca-certificates \
      apache2-utils \
      httpie \
      jq \
      dnsutils \
      curl \
      tree \
      lsof \
      vim \
      wget
