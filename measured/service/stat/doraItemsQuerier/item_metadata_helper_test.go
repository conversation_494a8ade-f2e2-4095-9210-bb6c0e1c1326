package doraItemsQuerier

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	dictpb "github.com/qbox/pay-sdk/dict"
	mockdict "github.com/qbox/pay-sdk/mocks/dict"
)

func TestQueryItemMetadataDict(t *testing.T) {
	// 为了能检查 ctx 被正确透传了
	type dummyKey struct{}
	ctx := context.WithValue(context.Background(), dummyKey{}, 1)

	ctrl := gomock.NewController(t)

	// 空列表不应该有 RPC 调用
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)

		actual, err := queryItemMetadataDict(ctx, mockDictCl, nil)
		assert.NoError(t, err)
		assert.Empty(t, actual)

		actual, err = queryItemMetadataDict(ctx, mockDictCl, []uint64{})
		assert.NoError(t, err)
		assert.Empty(t, actual)
	}

	// 正常查询
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)

		// Items
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 233}).
			Times(1).
			Return(&dictpb.Item{Id: 233, GroupId: 2, Code: "foo:bar:a"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 234}).
			Times(1).
			Return(&dictpb.Item{Id: 234, GroupId: 3, Code: "foo:baz:b"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 235}).
			Times(1).
			Return(&dictpb.Item{Id: 235, GroupId: 2, Code: "foo:bar:c"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 236}).
			Times(1).
			Return(&dictpb.Item{Id: 236, GroupId: 4, Code: "baz:quux:d"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 237}).
			Times(1).
			Return(&dictpb.Item{Id: 237, GroupId: 3, Code: "foo:baz:e"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 238}).
			Times(1).
			Return(&dictpb.Item{Id: 238, GroupId: 4, Code: "baz:quux:f"}, nil)

		// ItemGroups
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 2}).
			Times(1).
			Return(&dictpb.ItemGroup{Id: 2, ProductId: 100, Code: "foo:bar"}, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 3}).
			Times(1).
			Return(&dictpb.ItemGroup{Id: 3, ProductId: 100, Code: "foo:baz"}, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 4}).
			Times(1).
			Return(&dictpb.ItemGroup{Id: 4, ProductId: 200, Code: "baz:quux"}, nil)

		// Products
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: 100}).
			Times(1).
			Return(&dictpb.Product{Id: 100, Code: "foo"}, nil)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: 200}).
			Times(1).
			Return(&dictpb.Product{Id: 200, Code: "baz"}, nil)

		expected := map[uint64]itemEntry{
			233: {ItemCode: "foo:bar:a", ProductCode: "foo"},
			234: {ItemCode: "foo:baz:b", ProductCode: "foo"},
			235: {ItemCode: "foo:bar:c", ProductCode: "foo"},
			236: {ItemCode: "baz:quux:d", ProductCode: "baz"},
			237: {ItemCode: "foo:baz:e", ProductCode: "foo"},
			238: {ItemCode: "baz:quux:f", ProductCode: "baz"},
		}

		actual, err := queryItemMetadataDict(ctx, mockDictCl, []uint64{233, 235, 237, 234, 236, 238})
		assert.NoError(t, err)
		assert.Equal(t, expected, actual)
	}

	// 脏数据 - 缺 Item
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)

		// Items
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 233}).
			Times(1).
			Return(&dictpb.Item{Id: 233, GroupId: 2, Code: "foo:bar:a"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 234}).
			Times(1).
			Return((*dictpb.Item)(nil), assert.AnError)

		actual, err := queryItemMetadataDict(ctx, mockDictCl, []uint64{233, 234})
		assert.Error(t, err)
		assert.Nil(t, actual)
	}

	// 脏数据 - 缺 ItemGroup
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)

		// Items
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 233}).
			Times(1).
			Return(&dictpb.Item{Id: 233, GroupId: 2, Code: "foo:bar:a"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 234}).
			Times(1).
			Return(&dictpb.Item{Id: 234, GroupId: 3, Code: "foo:baz:b"}, nil)

		// ItemGroups
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 2}).
			Times(1).
			Return(&dictpb.ItemGroup{Id: 2, ProductId: 100, Code: "foo:bar"}, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 3}).
			Times(1).
			Return((*dictpb.ItemGroup)(nil), assert.AnError)

		actual, err := queryItemMetadataDict(ctx, mockDictCl, []uint64{233, 234})
		assert.Error(t, err)
		assert.Nil(t, actual)
	}

	// 脏数据 - 缺 Product
	{
		mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)

		// Items
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 233}).
			Times(1).
			Return(&dictpb.Item{Id: 233, GroupId: 2, Code: "foo:bar:a"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 234}).
			Times(1).
			Return(&dictpb.Item{Id: 234, GroupId: 3, Code: "quux:baz:b"}, nil)

		// ItemGroups
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 2}).
			Times(1).
			Return(&dictpb.ItemGroup{Id: 2, ProductId: 100, Code: "foo:bar"}, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 3}).
			Times(1).
			Return(&dictpb.ItemGroup{Id: 3, ProductId: 300, Code: "quux:baz"}, nil)

		// Products
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: 100}).
			Times(1).
			Return(&dictpb.Product{Id: 100, Code: "foo"}, nil)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: 300}).
			Times(1).
			Return((*dictpb.Product)(nil), assert.AnError)

		actual, err := queryItemMetadataDict(ctx, mockDictCl, []uint64{233, 234})
		assert.Error(t, err)
		assert.Nil(t, actual)
	}
}
