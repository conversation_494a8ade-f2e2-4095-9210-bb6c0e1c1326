package service

import (
	"context"
	stdErr "errors"

	"github.com/jinzhu/gorm"

	"time"

	baseDao "github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/model/enums"

	"qiniu.io/pay/creditd/model"
)

// AvailableBalanceThresholdService is service handler of AvailableBalanceThreshold
type AvailableBalanceThresholdService struct {
	thresholdDao *dao.AvailableBalanceThresholdDao
	cacheExpires time.Duration
}

// NewAvailableBalanceThresholdService is constructor of AvailableBalanceThresholdService
func NewAvailableBalanceThresholdService(thresholdDao *dao.AvailableBalanceThresholdDao,
	expires time.Duration) (*AvailableBalanceThresholdService, error) {
	return &AvailableBalanceThresholdService{
		thresholdDao: thresholdDao,
		cacheExpires: expires,
	}, nil
}

// DoTransaction do a transaction
func (s *AvailableBalanceThresholdService) DoTransaction(fn func(*AvailableBalanceThresholdService) error) error {
	return s.thresholdDao.DoTransaction(func(dao *dao.AvailableBalanceThresholdDao) error {
		newService, err := NewAvailableBalanceThresholdService(dao, s.cacheExpires)
		if err != nil {
			return err
		}

		return fn(newService)
	})
}

// CreateThreshold creates a new threshold record
func (s *AvailableBalanceThresholdService) CreateThreshold(
	ctx context.Context,
	m *model.AvailableBalanceThreshold,
) (*model.AvailableBalanceThreshold, error) {
	if m.Threshold == 0 && m.Status != enums.StatusThresholdNotEnabled {
		return nil, errors.Errorf("status should be not_enabled when threshold = 0")
	}
	m.ID = 0
	uid := m.UID
	// 为保证接口并发时调用数据不乱，引入以下事务 & 数据库行锁
	err := s.thresholdDao.DoTransaction(func(tx *dao.AvailableBalanceThresholdDao) error {
		// （通过 for update 给 uid, status 索引加行锁，直到事务结束锁再释放）
		// 获取 status = 2（enabled） 的 threshold 值
		enabledThreshold, err := tx.GetByUIDWithForUpdate(uid, false)
		if err != nil && !gorm.IsRecordNotFoundError(err) {
			return errors.Annotate(err, "create threshold - get threshold failed").WithField("uid", uid)
		}
		// 更新旧条目 status 为 1（not enabled）
		if err == nil {
			enabledThresholdUpdated := &model.AvailableBalanceThreshold{
				ID:     enabledThreshold.ID,
				Status: enums.StatusThresholdNotEnabled,
			}
			err = tx.Update(enabledThresholdUpdated)
			if err != nil {
				return errors.Annotate(err, "create threshold - update old threshold status failed").WithField("uid", uid)
			}
		}

		// 插入新条目
		err = tx.Save(m)
		if err != nil {
			return errors.Annotate(err, "create threshold - save new threshold failed").WithField("uid", uid)
		}

		return nil
	})

	return m, errors.Trace(err)
}

// GetEnabledThresholdByUID queries threshold by uid
func (s *AvailableBalanceThresholdService) GetEnabledThresholdByUID(
	ctx context.Context,
	uid uint64,
	isManager bool,
) (threshold *model.AvailableBalanceThreshold, err error) {
	threshold, err = s.thresholdDao.GetByUID(uid, isManager)
	if err != nil {
		if stdErr.Is(errors.Cause(err), baseDao.ErrRecordNotFound) {
			return &model.AvailableBalanceThreshold{
				UID:       uid,
				Threshold: 0,
				Status:    enums.StatusThresholdNotEnabled,
				Remark:    "not found record, set zero",
			}, nil
		}
		err = errors.Trace(err)
		return
	}
	return
}

// GetByStatus queries thresholds by status
func (s *AvailableBalanceThresholdService) GetByStatus(
	ctx context.Context,
	status enums.ThresholdStatus,
	isManager bool,
	offset, limit int,
) (thresholds []model.AvailableBalanceThreshold, count uint64, err error) {
	thresholds, err = s.thresholdDao.GetByStatus(status, offset, limit, isManager)
	if err != nil {
		err = errors.Trace(err)
		return
	}

	count, err = s.thresholdDao.CountByStatus(status, false)
	if err != nil {
		err = errors.Trace(err)
		return
	}

	return
}

func (s *AvailableBalanceThresholdService) SetManagerThreshold(
	ctx context.Context,
	m *model.AvailableBalanceThreshold,
) (*model.AvailableBalanceThreshold, error) {

	if m.Threshold == 0 && m.Status != enums.StatusThresholdNotEnabled {
		return nil, errors.Errorf("status should be not_enabled when threshold = 0")
	}
	m.IsManager = true
	m.ID = 0
	uid := m.UID

	// 为保证接口并发时调用数据不乱，引入以下事务 & 数据库行锁
	err := s.thresholdDao.DoTransaction(func(tx *dao.AvailableBalanceThresholdDao) error {
		// （通过 for update 给 uid, status 索引加行锁，直到事务结束锁再释放）
		// 获取 status = 2（enabled） 的 threshold 值
		enabledThreshold, err := tx.GetByUIDWithForUpdate(uid, true)
		isNotFound := gorm.IsRecordNotFoundError(err)
		if err != nil && !isNotFound {
			return errors.Annotate(err, "create threshold - get threshold failed").WithField("uid", uid)
		}

		// 创建新记录
		if isNotFound {
			// 插入新条目
			err1 := tx.Save(m)
			if err1 != nil {
				return errors.Annotate(err1, "create threshold - save new threshold failed").WithField("uid", uid)
			}
			return nil
		}

		// 更新旧条目
		if err == nil {
			remark := enabledThreshold.Remark
			if len(m.Remark) > 0 {
				remark = m.Remark
			}
			enabledThresholdUpdated := &model.AvailableBalanceThreshold{
				ID:        enabledThreshold.ID,
				Status:    m.Status,
				UID:       m.UID,
				Threshold: m.Threshold,
				Remark:    remark,
				UpdatedAt: time.Now(),
				IsManager: true,
			}
			err = tx.Update(enabledThresholdUpdated)
			if err != nil {
				return errors.Annotate(err, "create threshold - update old threshold status failed").WithField("uid", uid)
			}
		}
		return nil
	})

	return m, errors.Trace(err)
}
