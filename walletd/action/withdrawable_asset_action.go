package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// ListWithdrawableAssets 根据条件列出可提现资产列表
func (a *PaymentAction) ListWithdrawableAssets(ctx context.Context, req *pb.ListWithdrawableAssetsRequest) (*pb.ListWithdrawableAssetsResponse, error) {
	r := adapter.BuildPbListWithdrawableAssetsRequest(req)
	withdrawableAssets, err := a.depositService.ListWithdrawableAssets(ctx, r)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbListWithdrawableAssetsResponse(withdrawableAssets), nil
}
