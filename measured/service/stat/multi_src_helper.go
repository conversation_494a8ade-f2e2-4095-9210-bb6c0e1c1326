package stat

import (
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"github.com/qbox/bo-base/v4/errors"
)

const multiSrcStatZoneKey = "__stat_zone__"

var errInvalidMultiSrcStatQuery = errors.New("invalid stat query for multi src")
var errMalformedNewFormatZoneAxesSpec = errors.New("malformed new-format zone axes spec")
var errInvalidPostprocessStrategy = errors.New("invalid postprocess strategy")

// multiSrcItemSpec 计费项 MULTI 合量规格: map[该计费项区域]合量规格
type multiSrcItemSpec = map[int64]multiSrcItemZoneSpec

type multiSrcItemZoneSpec struct {
	PostprocessStrategy postprocessStrategy
	Axes                []multiSrcAxisSpec
}

// multiSrcAxisSpec 单条 MULTI 计量源原始量轴
type multiSrcAxisSpec struct {
	ItemCode string
	ZoneCode int64
}

// parseMultiSrcItemSpec 解析 MULTI 计量源的合量配置参数
//
// NOTE: 该方法基本按照 pay 的 stat_multi.go 移植，但不支持 `__stat_zone__` 不传的默认情形!
func parseMultiSrcItemSpec(mergedExtras map[string]string) (multiSrcItemSpec, error) {
	v, err := url.ParseQuery(mergedExtras[statExtraKeyQuery])
	if err != nil {
		return nil, errors.Trace(err)
	}

	spec := make(multiSrcItemSpec)

	// 解析
	ruleStrList, ok := v[multiSrcStatZoneKey]
	if !ok {
		return nil, errors.Trace(errInvalidMultiSrcStatQuery)
	}
	ruleStr := ruleStrList[0]

	// stat_zone=3001@xxx|3002@xxx|...
	zoneRules := strings.Split(ruleStr, "|")
	for _, zoneRule := range zoneRules {
		vs := strings.Split(zoneRule, "@")
		if len(vs) != 2 {
			return nil, errors.Trace(fmt.Errorf("invalid rule of zone: %s", zoneRule))
		}

		zoneStr := vs[0]
		rule := vs[1]

		keyZone, err := strconv.ParseInt(zoneStr, 10, 64)
		if err != nil {
			return nil, errors.Trace(fmt.Errorf("invalid zone: %s", zoneStr))
		}

		// 自从 BO-15835 之后，每个 zone 的定义均支持自定义多条轴的合量方式，此处的 rule
		// 有两种可能的形式：
		//
		// - rule = "fusion:transfer:all:ov~3001,pili:transfer:down:cn~0"
		//   此为兼容形式，默认与先前语义保持一致，即以求和方式合量。
		// - rule = "max(foo~10001,bar~10001)"
		//   此为 BO-15835 之后支持的新形式，目前允许的策略有 max 与 sum 两种，大小写敏感
		//
		// 实现细节：假定 item code 不含半角括号，则可以通过判断 rule 最后一个字符是否为 ')'
		// 区分两种情况。
		var postprocessStrat postprocessStrategy
		var itemZoneRulesStr string
		if strings.HasSuffix(rule, ")") {
			// BO-15835 形式

			// 找到 '('
			leftParenIdx := strings.Index(rule, "(")
			if leftParenIdx == -1 {
				// 没有找到
				return nil, errors.Trace(errMalformedNewFormatZoneAxesSpec)
			}

			// 切出两部分单独的子串
			postprocessStratStr := rule[:leftParenIdx]
			itemZoneRulesStr = rule[leftParenIdx+1 : len(rule)-1]

			// parse strategy
			postprocessStrat, err = parsePostprocessStrategy(postprocessStratStr)
			if err != nil {
				return nil, errors.Trace(err)
			}
		} else {
			// 兼容形式
			postprocessStrat = postprocessStrategySum
			itemZoneRulesStr = rule
		}

		itemZoneRules := strings.Split(itemZoneRulesStr, ",")

		zoneSpec := make([]multiSrcAxisSpec, 0)
		for _, itemZoneRule := range itemZoneRules {
			itemZone := strings.Split(itemZoneRule, "~")
			if len(itemZone) != 2 {
				return nil, errors.Trace(fmt.Errorf("invalid rule of item zone: %s", itemZoneRule))
			}

			itemCode := itemZone[0]

			targetZone, err := strconv.ParseInt(itemZone[1], 10, 64)
			if err != nil {
				return nil, errors.Trace(fmt.Errorf("invalid zone: %s", itemZone[1]))
			}

			axisSpec := multiSrcAxisSpec{
				ItemCode: itemCode,
				ZoneCode: targetZone,
			}
			zoneSpec = append(zoneSpec, axisSpec)
		}

		spec[keyZone] = multiSrcItemZoneSpec{
			PostprocessStrategy: postprocessStrat,
			Axes:                zoneSpec,
		}
	}

	return spec, nil
}

var postprocessStrategiesMap = map[string]postprocessStrategy{
	"sum": postprocessStrategySum,
	"max": postprocessStrategyMax,
}

func parsePostprocessStrategy(x string) (postprocessStrategy, error) {
	if y, ok := postprocessStrategiesMap[x]; ok {
		return y, nil
	}
	return postprocessStrategyUnknown, errInvalidPostprocessStrategy
}
