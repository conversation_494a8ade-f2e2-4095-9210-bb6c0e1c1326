package stat

import (
	"context"
	"fmt"
	"sync"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/legacystat/common"
	"github.com/qbox/pay-sdk/dict"
	mppb "github.com/qbox/pay-sdk/measureproxy"

	kirbyAdminModel "qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/measured/model"
)

var errInvalidDataType = errors.New("invalid data type for the specified item")
var errZoneNotIncludedInMultiSpec = errors.New("requested zone not included in MULTI source spec")

// prepareStatQueryContext 准备拉量上下文
func (s *measuredStatClient) prepareStatQueryContext(
	ctx context.Context,
	params *model.StatReq,
	includeChildren bool,
) (*statQueryContext, error) {
	itemAxesParams, err := s.prepareItemAxesParams(ctx, &itemAxesParamsReq{
		Epoch:    params.Begin,
		ItemCode: params.ItemCode,
		DataType: params.DataType,
		ZoneCode: params.ZoneCode,

		IsForIdleRespack: params.IsForIdleRespack,
	})
	if err != nil {
		return nil, errors.Trace(err)
	}

	// NOTE: 这段逻辑不要移动到 prepareItemAxesParams 中，因为 prepareItemAxesParams 有缓存逻辑，而出账不需要资源粒度计费
	// 不能缓存 ResourceName 参数
	if params.ResourceName != "" {
		for i, obj := range itemAxesParams.AxesParams {
			var key mppb.ResourceType
			switch obj.Src {
			case mppb.STAT_SRC_MERGEAPI, mppb.STAT_SRC_KODOBILL:
				key = mppb.ResourceType_BUCKET
			case mppb.STAT_SRC_FUSION:
				key = mppb.ResourceType_DOMAINS
			default:
				return nil, fmt.Errorf(
					"prepareStatQueryContext with unsupported resourceType, src=%d, resourceName=%s",
					obj.Src,
					params.ResourceName,
				)
			}
			itemAxesParams.AxesParams[i].Resource = resource{
				ResourceType: key,
				ResourceName: params.ResourceName,
			}
		}
	}

	uids := []uint32{uint32(params.UID)}
	if includeChildren {
		// TODO: 子账号列表
		panic("unimplemented")
	}

	axes := make([]*axisQueryParams, 0, len(uids)*len(itemAxesParams.AxesParams))
	for _, uid := range uids {
		for _, a := range itemAxesParams.AxesParams {
			ap := &axisQueryParams{
				uid:    uid,
				begin:  timestamppb.New(params.Begin),
				end:    timestamppb.New(params.End),
				params: a,
			}
			axes = append(axes, ap)
		}
	}

	scx := &statQueryContext{
		debug:            params.Debug,
		axes:             axes,
		postprocessStrat: itemAxesParams.PostprocessStrategy,

		dataMu:   &sync.RWMutex{},
		axesData: nil,
	}

	return scx, nil
}

// itemAxesParamsReq 计费项拉量参数的计算所需的入参
//
// NOTE: 单独拉出来做一个结构体，主要为了方便实现缓存
type itemAxesParamsReq struct {
	Epoch    time.Time
	ItemCode string
	DataType string
	ZoneCode int64

	IsForIdleRespack bool
}

// itemAxesParamsResp 计费项所有轴的拉量参数计算结果
//
// NOTE: 单独拉出来做一个结构体，主要为了方便实现缓存
type itemAxesParamsResp struct {
	PostprocessStrategy postprocessStrategy
	AxesParams          []*itemAxisParams
}

// ToCacheKey 返回这组参数对应的 cache key
func (r *itemAxesParamsReq) ToCacheKey() string {
	return fmt.Sprintf(
		"measure:stat:item_axes_params:%s:%s:%d:%v:%d",
		r.ItemCode,
		r.DataType,
		r.ZoneCode,
		r.IsForIdleRespack,
		r.Epoch.UnixNano(),
	)
}

// prepareItemAxesParams 准备计费项所有轴的拉量参数
//
// 走缓存，没有就算出来并存入缓存
func (s *measuredStatClient) prepareItemAxesParams(
	ctx context.Context,
	params *itemAxesParamsReq,
) (*itemAxesParamsResp, error) {
	return makeCacheableQuery(
		s.prepareItemAxesParamsUncached,
		5*time.Second,
		s,
		ctx,
		params,
	)
}

// prepareItemAxesParamsUncached 实际准备计费项所有轴的拉量参数，不走缓存
//
// TODO: 把数据库查询、RPC 等等副作用跟接下来的纯函数部分不要放在一起
func (s *measuredStatClient) prepareItemAxesParamsUncached(
	ctx context.Context,
	params *itemAxesParamsReq,
) (*itemAxesParamsResp, error) {
	item, err := s.dictClient.GetItemByCode(ctx, &dict.CodeParam{
		Code: params.ItemCode,
	})
	if err != nil {
		return nil, errors.Trace(err)
	}
	itemID := item.Id

	haveZone, err := s.getHaveZoneForV4Item(ctx, item.GetCode())
	if err != nil {
		return nil, errors.Trace(err)
	}

	itemDataTypes, err := s.dictClient.ListItemDataTypesByItemID(ctx, &dict.IDPagingParam{
		Id:       itemID,
		Page:     1,
		PageSize: 1000,
	})
	if err != nil {
		return nil, errors.Trace(err)
	}

	var idtID uint64
	for _, idt := range itemDataTypes.ItemDataTypes {
		if idt.Type == params.DataType {
			idtID = idt.Id
			break
		}
	}
	if idtID == 0 {
		return nil, errors.Trace(errInvalidDataType)
	}

	statConfig, err := s.dao.ItemStatConfig.GetByItemID(itemID)
	if err != nil {
		return nil, errors.Trace(err)
	}
	statSrcType := common.V3StatSrc(statConfig.StatSrcType)

	itemExtras, err := s.dao.ItemExtraArg.ListByItemID(itemID, 0, -1)
	if err != nil {
		return nil, errors.Trace(err)
	}
	itemDataTypeExtras, err := s.dao.ItemDataTypeExtraArg.ListByItemDataTypeID(idtID, 0, -1)
	if err != nil {
		return nil, errors.Trace(err)
	}

	mergedExtras := mergeExtras(itemExtras, itemDataTypeExtras)

	// MULTI 计量源逻辑
	if shouldEnableMultiSrcLogic(statSrcType) {
		spec, err := parseMultiSrcItemSpec(mergedExtras)
		if err != nil {
			return nil, errors.Trace(err)
		}

		// 查找本次拉量用的 zone code 对应的 spec
		zoneSpec, ok := spec[params.ZoneCode]
		if !ok {
			return nil, errors.Trace(errZoneNotIncludedInMultiSpec)
		}

		// 把所有要合量的轴生成到一起
		allParams := make([]*itemAxisParams, 0)
		for _, axisSpec := range zoneSpec.Axes {
			// 递归生成拉量参数
			// NOTE: 一旦进入这个函数就完全不走缓存，减少一些 surprises
			params, err := s.prepareItemAxesParamsUncached(ctx, &itemAxesParamsReq{
				Epoch:            params.Epoch,
				ItemCode:         axisSpec.ItemCode,
				DataType:         params.DataType,
				ZoneCode:         axisSpec.ZoneCode,
				IsForIdleRespack: params.IsForIdleRespack,
			})
			if err != nil {
				return nil, errors.Trace(err)
			}

			allParams = append(allParams, params.AxesParams...)
		}

		return &itemAxesParamsResp{
			PostprocessStrategy: zoneSpec.PostprocessStrategy,
			AxesParams:          allParams,
		}, nil
	}

	statSrcKind, err := statSrcKindFromV3Extras(statSrcType, mergedExtras[statExtraKeyQuery])
	if err != nil {
		return nil, errors.Trace(err)
	}

	var allItemExtras []*parsedStatExtras
	switch statSrcKind {
	case mppb.STAT_SRC_PILI,
		mppb.STAT_SRC_DISTRIBUTION,
		mppb.STAT_SRC_LINKING,
		mppb.STAT_SRC_QVS,
		mppb.STAT_SRC_RTC:

		var err error
		allItemExtras, err = parseExtrasAsPili(params.DataType, mergedExtras)
		if err != nil {
			return nil, errors.Trace(err)
		}

	default:
		parsedExtras, err := parseExtrasAsStd(mergedExtras)
		if err != nil {
			return nil, errors.Trace(err)
		}

		// KODO 计量源的 stat_query 有 `__host__` 特殊字段，要去掉
		switch statSrcKind {
		case mppb.STAT_SRC_MERGEAPI, mppb.STAT_SRC_KODOBILL:
			stripKodoHostParamInplace(parsedExtras)
		}

		allItemExtras = []*parsedStatExtras{parsedExtras}
	}

	// 闲时包的拉量强制 g=5min
	// NOTE: 目前只是取点逻辑没有下沉进计量中心的过渡方案，之后会换用更优雅的形式
	if params.IsForIdleRespack {
		for i := range allItemExtras {
			allItemExtras[i].G = mppb.G_5MIN
		}
	}

	result := make([]*itemAxisParams, len(allItemExtras))
	for i, itemExtras := range allItemExtras {
		var kirbyParams *itemKirbyAxisParams
		if s.enableKirbyDatapath && shouldEnableKirbyDatapath(statSrcKind) {
			kirbyParams = &itemKirbyAxisParams{
				ItemCode: params.ItemCode,
				ZoneCode: params.ZoneCode,
				DataType: params.DataType,
				G:        kirbyAdminModel.Granularity(itemExtras.G), // 俩类型故意做得语义兼容
				Epoch:    params.Epoch,
			}
		}

		result[i] = &itemAxisParams{
			KirbyParams: kirbyParams,
			Src:         statSrcKind,
			HaveZone:    haveZone,
			ZoneCode:    uint32(params.ZoneCode),
			ItemExtras:  itemExtras,
		}
	}

	return &itemAxesParamsResp{
		PostprocessStrategy: postprocessStrategySum,
		AxesParams:          result,
	}, nil
}

// mergeExtras 合并 ItemDataType 与 Item 级别的 extras
//
// ItemDataType 级别的值优先
func mergeExtras(
	itemExtras []model.ItemExtraArg,
	itemDataTypeExtras []model.ItemDataTypeExtraArg,
) map[string]string {
	result := make(map[string]string)

	// 直接写两次，这样优先的值如果存在的话，就会覆盖不优先的
	for _, kv := range itemExtras {
		result[kv.Key] = kv.Value
	}
	for _, kv := range itemDataTypeExtras {
		result[kv.Key] = kv.Value
	}

	return result
}

func (s *measuredStatClient) getHaveZoneForV4Item(
	_ context.Context,
	itemCode string,
) (bool, error) {
	return common.V3IsMultiZoneFromV4Item(itemCode), nil
}

func stripKodoHostParamInplace(x *parsedStatExtras) {
	// 此处内存管理:
	// oldQuery = [&a, &b, &c] 其中 b 是 __host__ 字段
	// newQuery = [&a, &c] 不做拷贝
	newQuery := make([]*kv, 0, len(x.Query)-1)
	for _, kv := range x.Query {
		if kv.K == magicFieldHost {
			continue
		}
		// 每次循环这里的 append 都立即执行，因此这里没有 `go func(){}` 后循环变量地址一样，
		// 取值也变成一样的问题
		newQuery = append(newQuery, kv)
	}
	// 就地替换掉原来的 query 字段，这样 __host__ 字段对应的对象就等待 GC
	x.Query = newQuery
}
