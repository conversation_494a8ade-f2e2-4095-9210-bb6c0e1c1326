package dataplaneModel

import (
	"encoding/json"
	"io"
	"time"

	pb "github.com/qbox/bo-sdk/kirbypb"
	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"
	"qiniu.io/pay/kirby/model/adminModel"
)

// ymdhms is an int64 like 20060102150405, that represents a time.Time in the
// same format.
type ymdhms int64

func (x ymdhms) toTime(loc *time.Location) time.Time {
	y := int(x / 1_00_00_000000)
	m := time.Month((x / 1_00_000000) % 100)
	d := int((x / 1_000000) % 100)
	h := int((x / 1_0000) % 100)
	min := int((x / 1_00) % 100)
	s := int(x % 100)
	return time.Date(y, m, d, h, min, s, 0, loc)
}

func newYMDHMS(t time.Time) ymdhms {
	x := ymdhms(t.Year())
	x = 100*x + ymdhms(t.Month())
	x = 100*x + ymdhms(t.Day())
	x = 100*x + ymdhms(t.Hour())
	x = 100*x + ymdhms(t.Minute())
	x = 100*x + ymdhms(t.Second())
	return x
}

type ymdhmsPoint struct {
	T ymdhms `json:"t"`
	V uint64 `json:"v"`
}

func (x ymdhmsPoint) toPb(loc *time.Location) *pb.StatPoint {
	return &pb.StatPoint{
		Time:  timestamppb.New(x.T.toTime(loc)),
		Value: x.V,
	}
}

// flattenedSeriesForTest corresponds 1:1 to the real FlattenedSeries, but has a
// compact JSON encoding that makes it suitable for test fixtures.
type flattenedSeriesForTest struct {
	QRN         string                 `json:"qrn"`
	MM          string                 `json:"mm"`
	MergedProps map[string]string      `json:"mp"`
	MBUUID      string                 `json:"mbu"`
	Start       ymdhms                 `json:"s"`
	End         ymdhms                 `json:"e"`
	Data        []ymdhmsPoint          `json:"d"`
	RTDCode     string                 `json:"rtd"`
	AggRCode    string                 `json:"aggr"`
	MeasureUnit adminModel.MeasureUnit `json:"mu"`
	G           adminModel.Granularity `json:"g"`
	UID         uint64                 `json:"uid"`
	ItemCode    string                 `json:"item"`
	ZoneCode    int64                  `json:"zone"`
}

func (x *FlattenedSeries) MarshalAsTestFixture(loc *time.Location) (string, error) {
	t := flattenedSeriesForTest{
		QRN:         x.QRN,
		MM:          x.MM,
		MergedProps: x.MergedProps,
		MBUUID:      x.MBUUID,
		Start:       newYMDHMS(x.Start.In(loc)),
		End:         newYMDHMS(x.End.In(loc)),
		Data: lo.Map(x.Data, func(p *pb.StatPoint, _ int) ymdhmsPoint {
			return ymdhmsPoint{T: newYMDHMS(p.Time.AsTime().In(loc)), V: p.Value}
		}),
		RTDCode:     x.RTDCode,
		AggRCode:    x.AggRCode,
		MeasureUnit: x.MeasureUnit,
		G:           x.G,
		UID:         x.UID,
		ItemCode:    x.ItemCode,
		ZoneCode:    x.ZoneCode,
	}

	result, err := json.Marshal(t)
	if err != nil {
		return "", err
	}

	return string(result), nil
}

func (x *flattenedSeriesForTest) toFlattenedSeries(loc *time.Location) *FlattenedSeries {
	return &FlattenedSeries{
		QRN:         x.QRN,
		MM:          x.MM,
		MergedProps: x.MergedProps,
		MBUUID:      x.MBUUID,
		Start:       x.Start.toTime(loc),
		End:         x.End.toTime(loc),
		Data:        lo.Map(x.Data, func(p ymdhmsPoint, _ int) *pb.StatPoint { return p.toPb(loc) }),
		RTDCode:     x.RTDCode,
		AggRCode:    x.AggRCode,
		MeasureUnit: x.MeasureUnit,
		G:           x.G,
		UID:         x.UID,
		ItemCode:    x.ItemCode,
		ZoneCode:    x.ZoneCode,
	}
}

func loadFlattenedSeriesForTest(r io.Reader, loc *time.Location) []*FlattenedSeries {
	var result []*FlattenedSeries

	d := json.NewDecoder(r)
	for d.More() {
		var t flattenedSeriesForTest
		err := d.Decode(&t)
		if err != nil {
			panic(err)
		}
		result = append(result, t.toFlattenedSeries(loc))
	}

	return result
}
