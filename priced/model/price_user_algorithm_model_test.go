package model_test

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/priced/model"
)

func TestPriceUserAlgorithmDao_GetLastByConds(t *testing.T) {
	sandbox := buildSandbox(t)

	getLastByCondsParam := &model.GetLastByCondsParam{
		UID:    1,
		ItemID: 1,
		ZoneID: 1,
	}

	priceUserAlgorithm, err := sandbox.priceDao.PriceUserAlgorithm.GetLastByConds(getLastByCondsParam)
	assert.Error(t, err)
	assert.Nil(t, priceUserAlgorithm)

}
