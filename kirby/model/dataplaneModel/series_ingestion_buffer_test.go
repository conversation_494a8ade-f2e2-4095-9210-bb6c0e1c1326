package dataplaneModel

import (
	"bytes"
	"context"
	_ "embed"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/test"
)

//go:embed testdata/sample_flattened_series.ndjson
var sampleFlattenedSeries []byte

func TestSeriesIngestionBuffer(t *testing.T) {
	t.Parallel()
	test.RunWithUTCAndCST(t, testSeriesIngestionBuffer)
}

func testSeriesIngestionBuffer(t *testing.T, loc *time.Location) {
	t.Parallel()

	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	ctx := tz.WithRefLocation(context.Background(), loc)

	dao := NewKirbyDataplaneDao(testWrap.BaseDao(), true)
	sib := dao.NewSeriesIngestionBuffer(loc)

	series := loadFlattenedSeriesForTest(bytes.NewBuffer(sampleFlattenedSeries), loc)
	err = sib.Add(series)
	assert.NoError(t, err)

	err = sib.Commit(ctx)
	assert.NoError(t, err)
}
