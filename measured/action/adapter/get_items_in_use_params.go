package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
)

// BuildPbGetItemsInUseParamsV1 converts from model.GetItemsInUseParams to the protobuf type.
func BuildPbGetItemsInUseParamsV1(x *model.GetItemsInUseParams) (*pb.GetItemsInUseParamsV1, error) {
	return &pb.GetItemsInUseParamsV1{
		Uid:         x.UID,
		ProductCode: x.ProductCode,
		Begin:       timestamppb.New(x.Begin),
		End:         timestamppb.New(x.End),
		ZoneCode:    x.ZoneCode,
		Debug:       x.Debug,
	}, nil
}

// BuildModelGetItemsInUseParamsV1 converts from pb.GetItemsInUseParamsV1 to the model layer type.
func BuildModelGetItemsInUseParamsV1(x *pb.GetItemsInUseParamsV1) (*model.GetItemsInUseParams, error) {
	var begin time.Time
	var end time.Time
	if x.Begin != nil {
		err := x.Begin.CheckValid()
		if err != nil {
			return nil, err
		}
		begin = x.Begin.AsTime()
	}

	if x.End != nil {
		err := x.End.CheckValid()
		if err != nil {
			return nil, err
		}
		end = x.End.AsTime()
	}

	return &model.GetItemsInUseParams{
		UID:         x.Uid,
		ProductCode: x.ProductCode,
		Begin:       begin,
		End:         end,
		ZoneCode:    x.ZoneCode,
		Debug:       x.Debug,
	}, nil
}
