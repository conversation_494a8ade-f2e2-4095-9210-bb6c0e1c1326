package model

import (
	"reflect"
	"testing"
)

func TestBalanceInsufficiencyPredictionDao_align2GivenDay(t *testing.T) {
	balancePredictionDao := BalanceInsufficiencyPredictionDao{}
	type args struct {
		predictions []GetBalanceInsufficiencyUIDsResult
	}

	tests := []struct {
		name string
		args args
		want []GetBalanceInsufficiencyUIDsResult
	}{
		{
			name: "least_unaffordable_days > 3 case",
			args: args{
				predictions: []GetBalanceInsufficiencyUIDsResult{
					{
						UID:                       1,
						LeastUnaffordableDays:     6,
						LastLeastUnaffordableDays: 6,
					},
				},
			},
			want: []GetBalanceInsufficiencyUIDsResult{
				{
					UID:                       1,
					LeastUnaffordableDays:     7,
					LastLeastUnaffordableDays: 7,
				},
			},
		}, {
			name: "least_unaffordable_days = 3 case",
			args: args{
				predictions: []GetBalanceInsufficiencyUIDsResult{
					{
						UID:                       2,
						LeastUnaffordableDays:     3,
						LastLeastUnaffordableDays: 8,
					},
				},
			},
			want: []GetBalanceInsufficiencyUIDsResult{
				{
					UID:                       2,
					LeastUnaffordableDays:     3,
					LastLeastUnaffordableDays: undefinedPredictionDays,
				},
			},
		}, {
			name: "1< least_unaffordable_days < 3 case",
			args: args{
				predictions: []GetBalanceInsufficiencyUIDsResult{
					{
						UID:                       3,
						LeastUnaffordableDays:     2,
						LastLeastUnaffordableDays: 2,
					},
				},
			},
			want: []GetBalanceInsufficiencyUIDsResult{
				{
					UID:                       3,
					LeastUnaffordableDays:     3,
					LastLeastUnaffordableDays: 3,
				},
			},
		}, {
			name: "least_unaffordable_days = 1 case",
			args: args{
				predictions: []GetBalanceInsufficiencyUIDsResult{
					{
						UID:                       4,
						LeastUnaffordableDays:     1,
						LastLeastUnaffordableDays: 1,
					},
				},
			},
			want: []GetBalanceInsufficiencyUIDsResult{
				{
					UID:                       4,
					LeastUnaffordableDays:     1,
					LastLeastUnaffordableDays: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := balancePredictionDao.processDays(tt.args.predictions); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("align2GivenDay() = %v, want %v", got, tt.want)
			}
		})
	}
}
