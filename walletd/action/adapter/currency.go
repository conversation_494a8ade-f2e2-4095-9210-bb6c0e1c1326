package adapter

import (
	tspb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/qpay/wallet/model"
)

func BuildPbCurrency(currency *model.Currency) (*pb.Currency, error) {
	if currency == nil {
		return nil, errors.New("can't build nil currency")
	}
	var obj = pb.Currency{
		Sn:           currency.SN,
		Uid:          currency.UID,
		CurrencyType: string(currency.CurrencyType),
		IsDefault:    currency.IsDefault,
	}
	if !currency.UpdatedAt.IsZero() {
		obj.UpdatedAt = tspb.New(currency.UpdatedAt)
	}
	if !currency.CreatedAt.IsZero() {
		obj.CreatedAt = tspb.New(currency.CreatedAt)
	}
	return &obj, nil
}

func BuildPbGetUserCurrenciesResponse(currencies []*model.Currency) (*pb.GetUserCurrenciesResponse, error) {
	if len(currencies) == 0 {
		return &pb.GetUserCurrenciesResponse{}, nil
	}
	var list = pb.GetUserCurrenciesResponse{
		List: make([]*pb.Currency, len(currencies)),
	}
	for i, c := range currencies {
		x, err := BuildPbCurrency(c)
		if err != nil {
			return nil, err
		}
		list.List[i] = x
	}
	return &list, nil
}
