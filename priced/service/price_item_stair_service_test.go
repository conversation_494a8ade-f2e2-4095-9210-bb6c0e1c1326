package service_test

import (
	"context"
	"testing"

	"github.com/qbox/bo-base/v4/base"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
)

func TestPriceItemStairCRUD(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	stairs := []model.PriceItemStair{
		{
			PriceItemID: 1,
			UnitID:      1,
			Quantity:    1,
			Price:       base.NewNMoneyWithHighAccuracyI64(100),
			Order:       0,
		},
		{
			PriceItemID: 1,
			UnitID:      2,
			Quantity:    2,
			Price:       base.NewNMoneyWithHighAccuracyI64(200),
			Order:       0,
		},
		{
			PriceItemID: 3,
			UnitID:      3,
			Quantity:    3,
			Price:       base.NewNMoneyWithHighAccuracyI64(300),
			Order:       0,
		},
	}

	assertFields := func(expect, actual *model.PriceItemStair, msgAndArgs ...any) {
		assert.Equal(t, expect.PriceItemID, actual.PriceItemID, msgAndArgs...)
		assert.Equal(t, expect.UnitID, actual.UnitID, msgAndArgs...)
		assert.Equal(t, expect.Quantity, actual.Quantity, msgAndArgs...)
		assert.Equal(t, expect.Price, actual.Price, msgAndArgs...)
		assert.Equal(t, expect.Order, actual.Order, msgAndArgs...)
	}

	ctx := context.TODO()

	for i, stair := range stairs {
		// create
		m, err := priceService.CreatePriceItemStair(ctx, &stair)
		if assert.NoError(t, err) {
			assert.NotZero(t, m.ID, "CreatePriceItemStair return zero id")
			assertFields(&stair, m, "Unmatched stair")
		}
		stairs[i].ID = m.ID

		// query
		stairID := m.ID
		m, err = priceService.GetPriceItemStairByID(ctx, stairID)
		if assert.NoError(t, err) {
			assertFields(&stair, m, "Unmatched stair")
		}

		// update
		m.Price = base.NewNMoneyWithHighAccuracyI64(1000)
		updatedStair, err := priceService.UpdatePriceItemStairByID(ctx, stairID, m)
		if assert.NoError(t, err) {
			assertFields(m, updatedStair)
		}
		stairs[i].Price = m.Price
	}

	// list all price item stairs
	stairList, err := priceService.ListAllPriceItemStairs(ctx, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, stairList, len(stairs))
	}

	// list all price item stair by price_item_id
	stairWithSamePriceItemID, err := priceService.ListAllPriceItemStairsByPriceItemID(ctx, 1, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, stairWithSamePriceItemID, 2)
		for _, stair := range stairWithSamePriceItemID {
			for _, expectedStair := range stairs {
				if stair.ID == expectedStair.ID {
					assertFields(&expectedStair, &stair)
					break
				}
			}
		}
	}

	// delete all price items
	for _, stair := range stairs {
		deletedStair, err := priceService.DeletePriceItemStairByID(ctx, stair.ID)
		if assert.NoError(t, err) {
			assertFields(deletedStair, &stair)
		}
	}

	// check price item list, should be empty
	l, err := priceService.ListAllPriceItemStairs(ctx, 0, 10)
	if assert.NoError(t, err) {
		assert.Empty(t, l)
	}

	// de-duplication
	for _, stair := range stairs {
		_, err := priceService.DeletePriceItemStairByID(ctx, stair.ID)
		assert.Error(t, err)
	}
}
