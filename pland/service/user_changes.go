package service

import (
	"context"

	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/models/enums"
)

// ListUserChanges 获取用户变更记录
func (s *PlanService) ListUserChanges(
	ctx context.Context,
	uid uint64,
	code string,
	changeType enums.ChangeType,
	status enums.PaymentStatus,
	offset int,
	limit int,
) ([]*models.UserPlanChange, uint64, map[string]*models.Plan, error) {
	records, total, err := s.dao.UserPlanChangeDao.DescList(
		ctx,
		uid,
		code,
		changeType,
		status,
		offset,
		limit,
	)
	if err != nil {
		return nil, 0, nil, err
	}

	// 查询涉及的各个版本的 plan
	indices := make([]planVersionIdx, len(records))
	for i, record := range records {
		indices[i] = planVersionIdx{
			Level:   record.ActualLevel,
			Version: record.ActualVersion,
		}
	}
	planMap, err := s.fetchPlanMap(ctx, indices)
	if err != nil {
		return nil, 0, nil, err
	}
	return records, total, planMap, nil
}
