package config

import (
	"os"

	"gopkg.in/yaml.v2"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/db"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/bo-base/v4/rpc"
)

// DictConfig config for dict business
type DictConfig struct {
	DefaultPageSize uint64 `yaml:"default_pagesize"`
}

// DictdConfig is config for priced service
type DictdConfig struct {
	RPC   rpc.Config
	Intl  intl.Config
	MySQL db.MySQLConfig
	Cache dao.CacheConfig
	Dict  DictConfig
}

// LoadDictdConfig load config from yaml file
func LoadDictdConfig(yamlPath string) (*DictdConfig, error) {
	byts, err := os.ReadFile(yamlPath)
	if err != nil {
		return nil, err
	}
	conf := &DictdConfig{}
	err = yaml.UnmarshalStrict(byts, conf)
	if err != nil {
		return nil, err
	}
	return conf, nil
}
