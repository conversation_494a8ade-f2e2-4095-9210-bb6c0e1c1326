package stat

import (
	"fmt"
	"strings"

	"github.com/qbox/bo-base/v4/expr"

	mppb "github.com/qbox/pay-sdk/measureproxy"

	"github.com/qbox/bo-base/v4/errors"
)

var errInvalidPiliDataType = errors.New("unsupported data type for Pili data source")

// parseExtrasAsPili 按 Pili 风格解析 v3 风格的拉量 extras 参数
//
// Pili 风格:
//
// * `stat_key`: 形如 `foo+bar+baz`
// * `stat_select`: 形如 `upflow+downflow`
// * `stat_expr`: 若不存在，则以 `stat_select` 作为表达式
// * `stat_g`: 若不存在，则隐式指定；default 数据类型为 `day`，其他（带宽、并发数）为 `5min`
//
// 按 `+` 分隔 `stat_key`，对每一个 key 分别拉量；拉完之后相加以合并为一条轴。
//
// NOTE: 实际上 pay 的 stat_pili.go 对 `stat_g` 的处理是放松的，其逻辑为带宽、并发数为 `5min`，
// 其余情况都为 `day`；这里的实现更为严格。
func parseExtrasAsPili(
	dataType string,
	mergedExtras map[string]string,
) ([]*parsedStatExtras, error) {
	selectV := mergedExtras[statExtraKeySelect]
	selectList := strings.Split(selectV, "+")
	exprStr, ok := mergedExtras[statExtraKeyExpr]
	if !ok {
		exprStr = selectV
	}

	var g mppb.Granularity
	if gStr, ok := mergedExtras[statExtraKeyG]; ok {
		// 显式指定了 stat_g，以此为准
		parsedG, err := parseStatG(gStr)
		if err != nil {
			return nil, errors.Trace(err)
		}
		g = parsedG
	} else {
		// 没有 stat_g 配置项，根据数据类型选择默认值
		switch dataType {
		case "default":
			g = mppb.G_DAY
		case "bandwidth", "concurrent":
			g = mppb.G_5MIN
		default:
			return nil, errors.Trace(errInvalidPiliDataType)
		}
	}

	switch dataType {
	case "bandwidth":
		// PILI 计量源的带宽转换是在计量源代码里完成的。。。
		exprStr = fmt.Sprintf("(%s)*8/300", exprStr)
	}

	parsedExpr, err := expr.NewMathExpr(exprStr)
	if err != nil {
		return nil, errors.Trace(err)
	}

	query, err := parseStatQuery(mergedExtras[statExtraKeyQuery])
	if err != nil {
		return nil, errors.Trace(err)
	}

	keys := strings.Split(mergedExtras[statExtraKeyKey], "+")
	result := make([]*parsedStatExtras, len(keys))
	for i, k := range keys {
		result[i] = &parsedStatExtras{
			G:      g,
			Select: selectList,
			Key:    k,
			Query:  query,
			Expr:   parsedExpr,
		}
	}

	return result, nil
}
