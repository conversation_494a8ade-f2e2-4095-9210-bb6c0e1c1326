package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measureproxyd/model"
)

// BuildPbGetUsersParams converts from model.GetUsersParams to the protobuf type.
func BuildPbGetUsersParams(x *model.GetUsersParams) (*pb.GetUsersParams, error) {
	return &pb.GetUsersParams{
		Begin:    timestamppb.New(x.Begin),
		End:      timestamppb.New(x.End),
		HaveZone: x.HaveZone,
		ZoneCode: x.ZoneCode,
		Src:      x.Src,
		Debug:    x.Debug,
	}, nil
}

// BuildModelGetUsersParams converts from pb.GetUsersParams to the model layer type.
func BuildModelGetUsersParams(x *pb.GetUsersParams) (*model.GetUsersParams, error) {
	var begin time.Time
	var end time.Time
	if x.Begin != nil {
		err := x.Begin.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		begin = x.Begin.AsTime()
	}

	if x.End != nil {
		err := x.End.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		end = x.End.AsTime()
	}

	return &model.GetUsersParams{
		Begin:    begin,
		End:      end,
		HaveZone: x.HaveZone,
		ZoneCode: x.ZoneCode,
		Src:      x.Src,
		Debug:    x.Debug,
	}, nil
}
