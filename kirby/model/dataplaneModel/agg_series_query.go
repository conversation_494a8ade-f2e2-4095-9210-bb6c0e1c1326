package dataplaneModel

import (
	"context"
	"errors"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
)

func timeIsZero(t time.Time) bool {
	return t.IsZero() || t.UnixNano() == 0
}

func (d *KirbyDataplaneDao) ListAggregatedSeries(
	ctx context.Context,
	req *ReqListAggregatedSeries,
) (*AggregatedSeriesDetailList, error) {
	loc := tz.MustLocationFromCtx(ctx)

	// 暂时不支持不指定数据月份的查询
	if timeIsZero(req.DataTimeFrom) || timeIsZero(req.DataTimeTo) {
		return nil, errors.New("data time span is required")
	}
	// 暂时不给跨月查询
	dataTimeFrom := req.DataTimeFrom.In(loc)
	dataTimeTo := req.DataTimeTo.In(loc)
	dataTimeFromMonth := base.ThisMonth(dataTimeFrom)
	dataTimeToMonth := base.ThisMonth(dataTimeTo)
	if !base.NextMonth(dataTimeFromMonth).Equal(dataTimeToMonth) {
		return nil, errors.New("dataTimeFrom and dataTimeTo cover >1 months")
	}

	shard := d.AggSeriesMD.GetShard(dataTimeFromMonth, loc)

	var eg errgroup.Group

	var mdRows []*aggSeriesMetadataRow
	eg.Go(func() error {
		var err1 error
		mdRows, err1 = shard.ListByConds(req, loc)
		if err1 != nil {
			return err1
		}
		return nil
	})

	var rowCount uint64
	eg.Go(func() error {
		var err1 error
		rowCount, err1 = shard.CountByConds(req, loc)
		if err1 != nil {
			return err1
		}
		return nil
	})

	err := eg.Wait()
	if err != nil {
		return nil, err
	}

	if len(mdRows) == 0 {
		return &AggregatedSeriesDetailList{
			Data:  nil,
			Count: rowCount,
		}, nil
	}

	aggSeriesKeysByShards := lo.GroupBy(mdRows, func(row *aggSeriesMetadataRow) computedShardInfo {
		return computeDataTableShard(dataTableKindAggregatedSeries, row.G, base.ThisMonth(row.Start.In(loc)), loc, "")
	})

	type queryJob struct {
		si   computedShardInfo
		keys []string
	}

	jobs := lo.MapToSlice(aggSeriesKeysByShards, func(k computedShardInfo, v []*aggSeriesMetadataRow) queryJob {
		return queryJob{si: k, keys: lo.Map(v, func(x *aggSeriesMetadataRow, _ int) string { return x.AggSeriesKey })}
	})

	const maxConcurrency = 8 // 随便定的比写入并发稍高的一个值
	jobResults, err := resultgroup.ThrottledParallelMap(jobs, maxConcurrency, func(param queryJob) ([]dataRowAndKey, error) {
		dataShard := d.DataMgr.getShard(&param.si)
		return dataShard.listSeriesByKeys(param.keys)
	})
	if err != nil {
		return nil, err
	}

	flattenedResults := lo.Flatten(jobResults)
	resultsBySeriesKey := lo.GroupBy(flattenedResults, func(x dataRowAndKey) string { return x.SeriesKey })

	result := lo.Map(mdRows, func(x *aggSeriesMetadataRow, _ int) *AggregatedSeriesDetail {
		// okay to have no data for this series
		data := resultsBySeriesKey[x.AggSeriesKey]
		sparseData, err := sparseReprFromRows(x.G, loc, lo.Map(data, func(r dataRowAndKey, _ int) dataRow { return r.intoDataRow() }))
		if err != nil {
			// only error case is when x.G is invalid
			// let's return garbage in this case because it's
			// difficult to indicate errors at this stage
			return nil
		}

		return &AggregatedSeriesDetail{
			AggSeriesKey: x.AggSeriesKey,
			UID:          x.UID,
			Start:        x.Start.In(loc),
			End:          x.End.In(loc),
			ItemCode:     x.ItemCode,
			ZoneCode:     x.ZoneCode,
			AggRCode:     x.AggR,
			Unit:         string(x.MeasureUnit),
			G:            x.G,
			Data:         sparseData,
			Ctime:        x.CreatedAt.In(loc),
			Sources:      nil, // TODO
		}
	})

	return &AggregatedSeriesDetailList{
		Data:  result,
		Count: rowCount,
	}, nil
}

func (d *KirbyDataplaneDao) ListDistinctUIDsByConds(
	ctx context.Context,
	month time.Time,
	itemCodes []string,
	haveZone bool,
	zoneCode int64,
	epoch time.Time,
) ([]uint64, error) {
	loc := tz.MustLocationFromCtx(ctx)
	month = month.In(loc)
	epoch = epoch.In(loc)

	return d.AggSeriesMD.GetShard(month, loc).ListDistinctUIDsByItemCodesAndZoneCode(
		itemCodes,
		haveZone,
		zoneCode,
		epoch,
		loc,
	)
}

func (d *KirbyDataplaneDao) ListDistinctItemCodesByConds(
	ctx context.Context,
	month time.Time,
	uids []uint64,
	haveZone bool,
	zoneCode int64,
	epoch time.Time,
) ([]string, error) {
	loc := tz.MustLocationFromCtx(ctx)
	month = month.In(loc)
	epoch = epoch.In(loc)

	return d.AggSeriesMD.GetShard(month, loc).ListDistinctItemCodesByUIDsAndZoneCode(
		uids,
		haveZone,
		zoneCode,
		epoch,
		loc,
	)
}

func (d *KirbyDataplaneDao) ListAggregatedSeriesDistinctUIDsByConds(
	ctx context.Context,
	req *ReqListAggregatedSeries,
) ([]uint64, error) {
	loc := tz.MustLocationFromCtx(ctx)

	// 暂时不支持不指定数据月份的查询
	if timeIsZero(req.DataTimeFrom) || timeIsZero(req.DataTimeTo) {
		return nil, errors.New("data time span is required")
	}
	// 暂时不给跨月查询
	dataTimeFrom := req.DataTimeFrom.In(loc)
	dataTimeTo := req.DataTimeTo.In(loc)
	dataTimeFromMonth := base.ThisMonth(dataTimeFrom)
	dataTimeToMonth := base.ThisMonth(dataTimeTo)
	if !base.NextMonth(dataTimeFromMonth).Equal(dataTimeToMonth) {
		return nil, errors.New("dataTimeFrom and dataTimeTo cover >1 months")
	}

	shard := d.AggSeriesMD.GetShard(dataTimeFromMonth, loc)
	return shard.ListDistinctUIDsByConds(req, loc)
}
