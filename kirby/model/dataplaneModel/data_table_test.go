package dataplaneModel

import (
	"math/rand"
	"sort"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/kirby/model/adminModel"
)

func TestDataTableRoundTrip(t *testing.T) {
	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	dtm := NewDataTableManager(testWrap.DB(), true)

	runOnInstance := func(t *testing.T, d time.Time, dti *DataTableInstance) {
		err := dti.Ensure()
		assert.NoError(t, err)

		key := t.Name()

		// initially empty
		rows, err := dti.getSeries(key)
		assert.NoError(t, err)
		assert.Empty(t, rows)

		// insert something
		data := makeSome5MinRowData(d)
		err = dti.setSeries(key, data)
		assert.NoError(t, err)

		// the data should come back intact
		rows, err = dti.getSeries(key)
		assert.NoError(t, err)
		// cannot trivially assert.ElementsMatch(t, data, rows)
		{
			assert.Len(t, rows, len(data))

			sort.Slice(rows, func(i int, j int) bool {
				return rows[i].T0.Before(rows[j].T0)
			})

			for i, expectedRow := range data {
				actualRow := rows[i]

				assert.True(t, expectedRow.T0.Equal(actualRow.T0))
				assert.Equal(t, expectedRow.P, actualRow.P)
			}
		}
	}

	t.Run("Series", func(t *testing.T) {
		d := time.Date(2022, 12, 1, 0, 0, 0, 0, time.UTC)
		runOnInstance(
			t,
			d,
			dtm.GetShardForQRN(
				adminModel.Granularity5Min,
				d,
				time.UTC,
				"qrn:fusion::1381234567:domain/test.example.com",
			),
		)
	})

	cst := time.FixedZone("CST", 8*3600)
	t.Run("AggregatedSeries", func(t *testing.T) {
		d := time.Date(2022, 11, 30, 16, 0, 0, 0, time.UTC) // 2022-12-01 00:00:00 CST
		runOnInstance(
			t,
			d,
			dtm.GetShardForQRN(
				adminModel.Granularity5Min,
				d,
				cst,
				"qrn:fusion::1381234567:domain/test.example.com",
			),
		)
	})
}

func makeSome5MinRowData(
	startDay time.Time,
) []dataRow {
	startDay = base.Today(startDay)

	// generate some data with a few gaps in between
	var result []dataRow
	for _, dDelta := range []int{0, 1, 2, 4, 6, 8, 10, 11, 12, 13, 14, 15} {
		t0 := startDay.AddDate(0, 0, dDelta)
		rng := rand.New(rand.NewSource(t0.UnixNano()))
		points := make([]uint64, 288)

		startIdx := rng.Intn(288)
		endIdx := rng.Intn(288)
		if startIdx > endIdx {
			startIdx, endIdx = endIdx, startIdx
		}
		if endIdx-startIdx < 50 {
			endIdx = startIdx + 50
		}
		if endIdx > 287 {
			endIdx = 287
		}
		for i := startIdx; i < endIdx; i++ {
			points[i] = rng.Uint64()
		}

		result = append(result, dataRow{
			T0: t0,
			P:  points,
		})
	}

	return result
}
