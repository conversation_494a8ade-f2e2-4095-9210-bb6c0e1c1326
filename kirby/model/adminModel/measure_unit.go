package adminModel

import (
	"database/sql"
	"database/sql/driver"
	"errors"
)

// MeasureUnit 原始计量单位，字符串形式表示
type MeasureUnit string

type MeasureUnitKind int

const (
	// MeasureUnitKindUnknown 未知原始计量单位种类
	MeasureUnitKindUnknown MeasureUnitKind = 0
	// MeasureUnitKindDimensionless 原始计量单位种类：无量纲数（个/张/条/次 etc.）
	MeasureUnitKindDimensionless MeasureUnitKind = 1
	// MeasureUnitKindData 原始计量单位种类：数据量
	MeasureUnitKindData MeasureUnitKind = 2
	// MeasureUnitKindBandwidth 原始计量单位种类：带宽
	MeasureUnitKindBandwidth MeasureUnitKind = 3
	// MeasureUnitKindDuration 原始计量单位种类：时长
	MeasureUnitKindDuration MeasureUnitKind = 4
)

// ScalingFactor 度量换算因子，用来标记/区分某个具体种类的原始计量属于哪个具体单位
type ScalingFactor int64

const (
	scalingFactorK ScalingFactor = 1000
	scalingFactorM               = 1000 * scalingFactorK
	scalingFactorG               = 1000 * scalingFactorM
	scalingFactorT               = 1000 * scalingFactorG
	scalingFactorP               = 1000 * scalingFactorT

	scalingFactorKi ScalingFactor = 1024
	scalingFactorMi               = 1024 * scalingFactorKi
	scalingFactorGi               = 1024 * scalingFactorMi
	scalingFactorTi               = 1024 * scalingFactorGi
	scalingFactorPi               = 1024 * scalingFactorTi

	scalingFactorNanosecond  ScalingFactor = 1
	scalingFactorMicrosecond               = 1000 * scalingFactorNanosecond
	scalingFactorMillisecond               = 1000 * scalingFactorMicrosecond
	scalingFactorSecond                    = 1000 * scalingFactorMillisecond
	scalingFactorMinute                    = 60 * scalingFactorSecond
	scalingFactorHour                      = 60 * scalingFactorMinute
	scalingFactorDay                       = 24 * scalingFactorHour
)

// DB adapters for MeasureUnit

var _ driver.Valuer = MeasureUnit("")

func (x MeasureUnit) Value() (driver.Value, error) {
	return string(x), nil
}

var _ sql.Scanner = (*MeasureUnit)(nil)

func (x *MeasureUnit) Scan(src any) error {
	ns := sql.NullString{}
	if err := ns.Scan(src); err != nil {
		return err
	}

	if !ns.Valid {
		return errors.New("column is not nullable")
	}

	*x = MeasureUnit(ns.String)
	return nil
}
