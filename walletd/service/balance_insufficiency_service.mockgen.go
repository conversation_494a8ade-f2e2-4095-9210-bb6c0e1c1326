// Code generated by MockGen. DO NOT EDIT.
// Source: walletd/service/balance_insufficiency_service.go

package service

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
)

// MockIBalanceInsufficiencyService is a mock of IBalanceInsufficiencyService interface.
type MockIBalanceInsufficiencyService struct {
	ctrl     *gomock.Controller
	recorder *MockIBalanceInsufficiencyServiceMockRecorder
}

// MockIBalanceInsufficiencyServiceMockRecorder is the mock recorder for MockIBalanceInsufficiencyService.
type MockIBalanceInsufficiencyServiceMockRecorder struct {
	mock *MockIBalanceInsufficiencyService
}

// NewMockIBalanceInsufficiencyService creates a new mock instance.
func NewMockIBalanceInsufficiencyService(ctrl *gomock.Controller) *MockIBalanceInsufficiencyService {
	mock := &MockIBalanceInsufficiencyService{ctrl: ctrl}
	mock.recorder = &MockIBalanceInsufficiencyServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBalanceInsufficiencyService) EXPECT() *MockIBalanceInsufficiencyServiceMockRecorder {
	return m.recorder
}

// NotifyBalanceInsufficiency mocks base method.
func (m *MockIBalanceInsufficiencyService) NotifyBalanceInsufficiency(ctx context.Context, now time.Time) (int, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NotifyBalanceInsufficiency", ctx, now)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// NotifyBalanceInsufficiency indicates an expected call of NotifyBalanceInsufficiency.
func (mr *MockIBalanceInsufficiencyServiceMockRecorder) NotifyBalanceInsufficiency(ctx, now interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyBalanceInsufficiency", reflect.TypeOf((*MockIBalanceInsufficiencyService)(nil).NotifyBalanceInsufficiency), ctx, now)
}
