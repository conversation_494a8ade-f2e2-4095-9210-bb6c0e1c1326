package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measured/model"
)

func TestGetUsersParamsV1RoundTrip(t *testing.T) {
	x := &model.GetUsersParams{
		ProductCode: "fusion",
		Begin:       time.Date(2019, 7, 1, 0, 0, 0, 0, time.Local),
		End:         time.Date(2019, 8, 1, 0, 0, 0, 0, time.Local),
		ZoneCode:    3001,
		Debug:       true,
	}

	y, err := BuildPbGetUsersParamsV1(x)
	assert.NoError(t, err)

	z, err := BuildModelGetUsersParamsV1(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ProductCode, z.ProductCode)
	assert.Equal(t, x.Begin.UnixNano(), z.Begin.UnixNano())
	assert.Equal(t, x.End.Unix<PERSON>ano(), z.End.UnixNano())
	assert.Equal(t, x.ZoneCode, z.ZoneCode)
	assert.Equal(t, x.<PERSON><PERSON>, z.<PERSON>bug)
}
