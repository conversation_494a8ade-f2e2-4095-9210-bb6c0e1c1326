package adminModel

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestParsedMeasureUnitReprErrorCases(t *testing.T) {
	testcases := []struct {
		desc  string
		input ParsedMeasureUnit
		err   error
	}{
		{
			desc: "0 字节为单位的数据量不应该能被表示",
			input: ParsedMeasureUnit{
				Kind:                MeasureUnitKindData,
				classifier:          "",
				premultipliedFactor: 0,
			},
			err: errScalingFactorNotRepresentable,
		},
		{
			desc: "233 字节为单位的数据量不应该能被表示",
			input: ParsedMeasureUnit{
				Kind:                MeasureUnitKindData,
				classifier:          "",
				premultipliedFactor: 233,
			},
			err: errScalingFactorNotRepresentable,
		},
		{
			desc: "1000 bps 不应该能被表示",
			input: ParsedMeasureUnit{
				Kind:                MeasureUnitKindBandwidth,
				classifier:          "",
				premultipliedFactor: 1000,
			},
			err: errScalingFactorNotRepresentable,
		},
		{
			desc: "1024 bps 不应该能被表示",
			input: ParsedMeasureUnit{
				Kind:                MeasureUnitKindBandwidth,
				classifier:          "",
				premultipliedFactor: 1024,
			},
			err: errScalingFactorNotRepresentable,
		},
		{
			desc: "1 火星日不应该能被表示",
			input: ParsedMeasureUnit{
				Kind:       MeasureUnitKindDuration,
				classifier: "",
				// https://en.wikipedia.org/wiki/Mars_sol
				premultipliedFactor: ScalingFactor(88775244 * time.Millisecond),
			},
			err: errScalingFactorNotRepresentable,
		},
		{
			desc: "未知的量纲不应该能被表示",
			input: ParsedMeasureUnit{
				Kind:                MeasureUnitKindUnknown,
				classifier:          "",
				premultipliedFactor: 1,
			},
			err: errUnknownMeasureUnitKind,
		},
		{
			desc: "未知的量纲带上量词也不应该能被表示",
			input: ParsedMeasureUnit{
				Kind:                MeasureUnitKindUnknown,
				classifier:          "test",
				premultipliedFactor: 1,
			},
			err: errUnknownMeasureUnitKind,
		},
		{
			desc: "未知的量纲带上非 1 系数也不应该能被表示",
			input: ParsedMeasureUnit{
				Kind:                MeasureUnitKindUnknown,
				classifier:          "",
				premultipliedFactor: 100,
			},
			err: errUnknownMeasureUnitKind,
		},
	}

	for _, tc := range testcases {
		s, err := tc.input.Repr()
		assert.Equal(t, tc.err, err, tc.desc)
		assert.Empty(t, s, tc.desc)
	}
}
