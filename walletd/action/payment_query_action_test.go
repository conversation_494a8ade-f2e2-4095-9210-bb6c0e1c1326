package action

import (
	"context"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/test"
	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/qpay/common"
	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/wallet/dao"
)

func TestPaymentAction_ListWalletTransactionByBatchPaymentID(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	entryId := common.GetUUID()
	excode := common.GetUUID()
	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:     sandbox.UID,
			Money:   10000,
			Desc:    "测试充值一笔,今年发大财",
			Excode:  excode,
			EntryId: entryId,
		},
	}

	ctx := context.Background()
	_, err := sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	txn, err := sandbox.Factory.QueryService.QueryPaymentTransactionByEntryID(ctx, sandbox.UID, entryId)
	assert.NoError(t, err)

	wTxn, err := sandbox.Factory.QueryService.QueryWalletTransactionsByPaymentID(ctx, txn.PaymentID)
	assert.NoError(t, err)

	assert.EqualValues(t, 10000, wTxn[0].Change)
	assert.EqualValues(t, 0, wTxn[0].Left)

	walletTxns, err := sandbox.PaymentAction.ListWalletTransactionByBatchPaymentID(ctx, &pb.CodeParamList{
		Code: []*pb.CodeParam{
			{
				Code: txn.PaymentID,
			},
		},
	})
	assert.NoError(t, err)

	assert.EqualValues(t, 10000, walletTxns.Txns[0].Change)
	assert.EqualValues(t, 0, walletTxns.Txns[0].Left)
}

func TestPaymentAction_ListPaymentTransByCondition(t *testing.T) {
	test.RunWithUTCAndCST(t, testPaymentAction_ListPaymentTransByCondition)
}

func testPaymentAction_ListPaymentTransByCondition(t *testing.T, loc *time.Location) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	entryId := common.GetUUID()
	excode := common.GetUUID()
	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:     sandbox.UID,
			Money:   10000,
			Desc:    "测试充值一笔,今年发大财",
			Excode:  excode,
			EntryId: entryId,
		},
	}

	ctx := tz.WithRefLocation(context.Background(), loc)
	_, err := sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	txn, err := sandbox.Factory.QueryService.QueryPaymentTransactionByEntryID(ctx, sandbox.UID, entryId)
	assert.NoError(t, err)

	wTxn, err := sandbox.Factory.QueryService.QueryWalletTransactionsByPaymentID(ctx, txn.PaymentID)
	assert.NoError(t, err)

	assert.EqualValues(t, 10000, wTxn[0].Change)
	assert.EqualValues(t, 0, wTxn[0].Left)

	txns, err := sandbox.PaymentAction.ListPaymentTransByCondition(ctx, &pb.ListPaymentTransConditionRequest{
		Uid:        sandbox.UID,
		Excode:     []string{excode},
		GotDetails: true,
	})
	assert.NoError(t, err)

	assert.EqualValues(t, 10000, txns.Txns[0].WalletTransactionList.Txns[0].Change)
	assert.EqualValues(t, 0, txns.Txns[0].WalletTransactionList.Txns[0].Left)
}

func TestPaymentAction_QueryPaymentTransactionByPaymentID(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	entryId := common.GetUUID()
	excode := common.GetUUID()
	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:     sandbox.UID,
			Money:   10000,
			Desc:    "测试充值一笔现金",
			Excode:  excode,
			EntryId: entryId,
		},
	}

	ctx := context.Background()
	_, err := sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	txn, err := sandbox.Factory.QueryService.QueryPaymentTransactionByEntryID(ctx, sandbox.UID, entryId)
	assert.NoError(t, err)
	assert.EqualValues(t, request.PaymentRequest.Money, txn.PaymentAmount)

	paymentTxn, err := sandbox.PaymentAction.QueryPaymentTransactionByPaymentID(ctx, &pb.CodeParam{
		Code: txn.PaymentID,
	})
	assert.NoError(t, err)
	assert.EqualValues(t, request.PaymentRequest.Money, paymentTxn.PaymentAmount)
}

func TestPaymentAction_QueryPaymentTransactionByEntryID(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	entryId := common.GetUUID()
	excode := common.GetUUID()
	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:     sandbox.UID,
			Money:   10000,
			Desc:    "测试充值一笔现金",
			Excode:  excode,
			EntryId: entryId,
		},
	}

	ctx := context.Background()
	_, err := sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	paymentTxn, err := sandbox.PaymentAction.QueryPaymentTransactionByEntryID(ctx, &pb.EntryIDParam{
		Uid:     sandbox.UID,
		EntryId: entryId,
	})
	assert.NoError(t, err)
	assert.EqualValues(t, request.PaymentRequest.Money, paymentTxn.PaymentAmount)
}

func TestPaymentAction_ListWalletTransactionByPaymentID(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	entryId := common.GetUUID()
	excode := common.GetUUID()
	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:     sandbox.UID,
			Money:   10000,
			Desc:    "测试充值一笔现金",
			Excode:  excode,
			EntryId: entryId,
		},
	}

	ctx := context.Background()
	_, err := sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	txn, err := sandbox.Factory.QueryService.QueryPaymentTransactionByEntryID(ctx, sandbox.UID, entryId)
	assert.NoError(t, err)
	assert.EqualValues(t, request.PaymentRequest.Money, txn.PaymentAmount)

	walletTxns, err := sandbox.PaymentAction.ListWalletTransactionByPaymentID(ctx, &pb.CodeParam{
		Code: txn.PaymentID,
	})
	assert.NoError(t, err)
	if len(walletTxns.Txns) == 1 {
		assert.EqualValues(t, request.PaymentRequest.Money, walletTxns.Txns[0].Change)
		assert.EqualValues(t, 0, walletTxns.Txns[0].Left)
	} else {
		t.Fatal("ListWalletTransactionByPaymentID 不符合预期")
	}
}

func TestPaymentAction_GetUserBalance(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	entryId := common.GetUUID()
	excode := common.GetUUID()
	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:     sandbox.UID,
			Money:   10000,
			Desc:    "测试充值一笔现金",
			Excode:  excode,
			EntryId: entryId,
		},
	}

	ctx := context.Background()
	_, err := sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	balance, err := sandbox.PaymentAction.GetUserBalance(ctx, &pb.UIDParam{
		Uid: sandbox.UID,
	})
	assert.NoError(t, err)
	assert.EqualValues(t, request.PaymentRequest.Money, balance.Cash)
}

func TestPaymentAction_GetUserDetail(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	entryId := common.GetUUID()
	excode := common.GetUUID()
	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:     sandbox.UID,
			Money:   10000,
			Desc:    "测试充值一笔现金",
			Excode:  excode,
			EntryId: entryId,
		},
	}

	ctx := context.Background()
	_, err := sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	userDetails, err := sandbox.PaymentAction.GetUserDetail(ctx, &pb.UIDParam{
		Uid: sandbox.UID,
	})
	assert.NoError(t, err)
	assert.EqualValues(t, request.PaymentRequest.Money, userDetails.UserBalance.Cash)
	assert.EqualValues(t, request.PaymentRequest.Money, userDetails.UserBalance.Balance, "Balance 应该为充值的 10000现金")
}

func TestPaymentAction_GetUserWalletItemList(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)

	entryId := common.GetUUID()
	excode := common.GetUUID()
	request := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:     sandbox.UID,
			Money:   10000,
			Desc:    "测试充值一笔现金",
			Excode:  excode,
			EntryId: entryId,
		},
	}

	ctx := context.Background()
	_, err := sandbox.PaymentAction.DepositCash(ctx, &request)
	assert.NoError(t, err)

	// 再充值一笔牛币
	entryId = common.GetUUID()
	excode = common.GetUUID()
	requestNiuCoin := pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			Uid:     sandbox.UID,
			Money:   20000,
			Desc:    "测试充值一笔牛币",
			Excode:  excode,
			EntryId: entryId,
		},
	}

	_, err = sandbox.PaymentAction.DepositNiuCoin(ctx, &requestNiuCoin)
	assert.NoError(t, err)

	walletItemList, err := sandbox.PaymentAction.GetUserWalletItemList(ctx, &pb.UIDParam{
		Uid: sandbox.UID,
	})
	assert.NoError(t, err)
	if len(walletItemList.WalletItem) == 2 {
		assert.EqualValues(t, request.PaymentRequest.Money, walletItemList.WalletItem[0].Amount)
		assert.EqualValues(t, request.PaymentRequest.Money, walletItemList.WalletItem[0].Balance)
		assert.EqualValues(t, requestNiuCoin.PaymentRequest.Money, walletItemList.WalletItem[1].Amount)
		assert.EqualValues(t, requestNiuCoin.PaymentRequest.Money, walletItemList.WalletItem[1].Balance)
	} else {
		t.Fatal("GetUserWalletItemList 不符合预期")
	}
}

func TestPaymentAction_GetUserVoucher(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)
	voucher := prepareVoucher(t, sandbox)

	voucher.Uid = dao.UID

	ctx := context.Background()
	v, err := sandbox.PaymentAction.ActiveVoucher(ctx, voucher)
	assert.NoError(t, err)
	assert.EqualValues(t, enums.VoucherStatusActive, v.Status)

	userVoucher, err := sandbox.PaymentAction.GetUserVoucher(ctx, &pb.UIDParam{
		Uid: sandbox.UID,
	})
	assert.NoError(t, err)
	assert.EqualValues(t, voucher.Balance, userVoucher.VoucherUnused)
	assert.EqualValues(t, 0, userVoucher.VoucherUsed)
}

func TestPaymentAction_GetUserVoucherItemList(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)
	voucher := prepareVoucher(t, sandbox)

	voucher.Uid = dao.UID

	ctx := context.Background()
	v, err := sandbox.PaymentAction.ActiveVoucher(ctx, voucher)
	assert.NoError(t, err)
	assert.EqualValues(t, enums.VoucherStatusActive, v.Status)

	voucherItemList, err := sandbox.PaymentAction.GetUserVoucherItemList(ctx, &pb.UIDParam{
		Uid: sandbox.UID,
	})
	assert.NoError(t, err)
	if len(voucherItemList.VoucherItem) == 1 {
		assert.EqualValues(t, voucher.Uid, voucherItemList.VoucherItem[0].Uid)
		assert.EqualValues(t, voucher.Amount, voucherItemList.VoucherItem[0].Amount)
		assert.EqualValues(t, voucher.Balance, voucherItemList.VoucherItem[0].Balance)
	} else {
		t.Fatal("GetUserVoucherItemList 不符合预期")
	}
}
