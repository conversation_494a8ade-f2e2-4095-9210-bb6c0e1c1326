package adapter

import (
	"github.com/samber/lo"

	pb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/model/adminModel"
)

func buildSPDDetail(in *pb.SeriesPropertyDefinitionDetail) *adminModel.SeriesPropertyDefinitionDetail {
	return &adminModel.SeriesPropertyDefinitionDetail{
		Key:        in.Key,
		Name:       in.Name,
		ValueType:  adminModel.SPValueType(in.ValueType),
		StringEnum: buildStringEnumDecl(in.StringEnum),
	}
}

func buildSPDDetails(in []*pb.SeriesPropertyDefinitionDetail) []*adminModel.SeriesPropertyDefinitionDetail {
	return lo.Map(in, func(x *pb.SeriesPropertyDefinitionDetail, _ int) *adminModel.SeriesPropertyDefinitionDetail {
		return buildSPDDetail(x)
	})
}

func buildPbSPDDetail(in *adminModel.SeriesPropertyDefinitionDetail) *pb.SeriesPropertyDefinitionDetail {
	return &pb.SeriesPropertyDefinitionDetail{
		Key:        in.Key,
		Name:       in.Name,
		ValueType:  pb.SPValueType(in.ValueType),
		StringEnum: buildPbStringEnumDecl(in.StringEnum),
	}
}

func buildPbSPDDetails(in []*adminModel.SeriesPropertyDefinitionDetail) []*pb.SeriesPropertyDefinitionDetail {
	return lo.Map(in, func(x *adminModel.SeriesPropertyDefinitionDetail, _ int) *pb.SeriesPropertyDefinitionDetail {
		return buildPbSPDDetail(x)
	})
}

func buildStringEnumDecl(in *pb.StringEnumDecl) *adminModel.StringEnumDecl {
	if in == nil {
		return nil
	}
	return &adminModel.StringEnumDecl{
		Variants: lo.Map(in.Variants, func(x *pb.StringEnumVariant, _ int) adminModel.StringEnumVariant { return buildStringEnumVariant(x) }),
	}
}

func buildPbStringEnumDecl(in *adminModel.StringEnumDecl) *pb.StringEnumDecl {
	if in == nil {
		return nil
	}
	return &pb.StringEnumDecl{
		Variants: lo.Map(in.Variants, func(x adminModel.StringEnumVariant, _ int) *pb.StringEnumVariant { return buildPbStringEnumVariant(x) }),
	}
}

func buildStringEnumVariant(in *pb.StringEnumVariant) adminModel.StringEnumVariant {
	return adminModel.StringEnumVariant{
		Value: in.Value,
		Desc:  in.Desc,
	}
}

func buildPbStringEnumVariant(in adminModel.StringEnumVariant) *pb.StringEnumVariant {
	return &pb.StringEnumVariant{
		Value: in.Value,
		Desc:  in.Desc,
	}
}
