package dataplaneModel

import (
	"context"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"

	"qiniu.io/pay/kirby/model/adminModel"
)

type aggSeriesMetadataRow struct {
	ID uint64 `gorm:"column:id;not null;primary_key"`

	// UID 该汇总序列计量归属的 UID
	UID uint64 `gorm:"column:uid;not null"`
	// Start 该汇总序列计量的起始时刻（含）
	Start time.Time `gorm:"column:start;type:DATETIME(6);not null"`
	// End 该汇总序列计量的结束时刻（不含）
	End time.Time `gorm:"column:end;type:DATETIME(6);not null"`
	// ItemCode 计费系统口径的计费项 code
	ItemCode string `gorm:"column:item_code;type:varchar(256);not null"`
	// ZoneCode 计费系统口径的区域 code
	ZoneCode int64 `gorm:"column:zone_code;not null"`

	// AggR 用来产生该汇总序列的聚合规则 code
	AggR string `gorm:"column:aggr;type:varchar(64);not null"`
	// MeasureUnit 该汇总序列数据的计量单位
	MeasureUnit adminModel.MeasureUnit `gorm:"column:measure_unit;type:varchar(16);not null"`
	// G 该汇总序列数据的粒度
	G adminModel.Granularity `gorm:"column:g;type:tinyint(1);not null"`

	// AggSeriesKey 该汇总序列的数据的检索 key
	AggSeriesKey string `gorm:"column:agg_series_key;type:varchar(64);not null"`

	// CreatedAt 记录的创建时刻
	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);not null;default:now(6)"`
}

func (x *aggSeriesMetadataRow) TableName() string {
	return getAggSeriesMetadataTableName(x.Start, x.Start.Location())
}

// AggSeriesMetadataDao is data access object of aggSeriesMetadataRow model
type AggSeriesMetadataDao struct {
	base *dao.BaseDao
}

// NewAggSeriesMetadataDao is constructor of AggSeriesMetadataDao
func NewAggSeriesMetadataDao(base *dao.BaseDao) *AggSeriesMetadataDao {
	return &AggSeriesMetadataDao{
		base: base,
	}
}

func makeAggSeriesMetadataTableSelector(t time.Time, loc *time.Location) *aggSeriesMetadataRow {
	return &aggSeriesMetadataRow{Start: t.In(loc)}
}

// EnsureShard ensures existence of the month's shard table.
func (d *AggSeriesMetadataDao) EnsureShard(month time.Time, loc *time.Location) error {
	tableName := makeAggSeriesMetadataTableSelector(month, loc).TableName()
	stmt, err := makeCreateAggSeriesMetadataTableStmt(tableName)
	if err != nil {
		return err
	}

	err = d.base.LogMode(false).Exec(stmt).Error
	if err != nil && !isTableAlreadyExisting(err) {
		return err
	}
	return nil
}

type AggSeriesMetadataShard AggSeriesMetadataDao

func (d *AggSeriesMetadataDao) GetShard(month time.Time, loc *time.Location) *AggSeriesMetadataShard {
	tableName := makeAggSeriesMetadataTableSelector(month, loc).TableName()
	tbl := d.base.DB.Table(tableName)
	return (*AggSeriesMetadataShard)(NewAggSeriesMetadataDao(dao.NewBaseDao(tbl, nil)))
}

// Save inserts or updates an aggSeriesMetadataRow by id
func (d *AggSeriesMetadataShard) Save(model *aggSeriesMetadataRow) error {
	err := d.base.Execute(func(value interface{}) error {
		return d.base.Save(value).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

func (d *AggSeriesMetadataShard) BulkInsert(ctx context.Context, data []aggSeriesMetadataRow) error {
	return d.base.BulkInsert(ctx, data)
}

// GetByMonthUIDItemZone lists aggregated series metadata by (month, uid, item_code, zone_code)
func (d *AggSeriesMetadataShard) GetByMonthUIDItemZone(
	epoch time.Time,
	month time.Time,
	loc *time.Location,
	uid uint64,
	itemCode string,
	zoneCode int64,
) (*aggSeriesMetadataRow, error) {
	month = base.ThisMonth(month.In(loc))
	if epoch.Before(month) {
		// 被查询的月份在当前时刻还没到来
		return nil, nil
	}

	var result aggSeriesMetadataRow
	err := d.base.Execute(func(value interface{}) error {
		return d.base.
			Where(
				"`uid` = ? AND `item_code` = ? AND `zone_code` = ? AND `created_at` <= ?",
				uid,
				itemCode,
				zoneCode,
				epoch,
			).
			Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		if gorm.IsRecordNotFoundError(err) || errors.Cause(err) == dao.ErrRecordNotFound {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"epoch":    epoch,
			"month":    month,
			"uid":      uid,
			"itemCode": itemCode,
			"zoneCode": zoneCode,
		})
	}

	return &result, nil
}

func (x *ReqListAggregatedSeries) toSQL(loc *time.Location) (string, []any, error) {
	conds := squirrel.And{
		squirrel.GtOrEq{"created_at": x.Epoch.In(loc)},
	}

	if !timeIsZero(x.DataTimeFrom) {
		conds = append(conds, squirrel.GtOrEq{"end": x.DataTimeFrom.In(loc)})
	}
	if !timeIsZero(x.DataTimeTo) {
		conds = append(conds, squirrel.Lt{"start": x.DataTimeTo.In(loc)})
	}
	if !timeIsZero(x.CtimeFrom) {
		conds = append(conds, squirrel.GtOrEq{"created_at": x.CtimeFrom.In(loc)})
	}
	if !timeIsZero(x.CtimeTo) {
		conds = append(conds, squirrel.Lt{"created_at": x.CtimeTo.In(loc)})
	}
	if len(x.AggSeriesKeys) > 0 {
		conds = append(conds, squirrel.Eq{"agg_series_key": x.AggSeriesKeys})
	}
	if len(x.UIDs) > 0 {
		conds = append(conds, squirrel.Eq{"uid": x.UIDs})
	}
	if len(x.ItemCodes) > 0 {
		conds = append(conds, squirrel.Eq{"item_code": x.ItemCodes})
	}

	return conds.ToSql()
}

const maxPageSize = 10000

type bigEnoughInteger interface {
	~int | ~int32 | ~int64 | ~uint | ~uint32 | ~uint64 | ~uintptr
}

func paginate[T bigEnoughInteger](
	reqPage T,
	reqPageSize T,
) (offset T, limit T) {
	if reqPage > 0 {
		reqPage--
	}
	if reqPageSize < 0 || reqPageSize > maxPageSize {
		reqPageSize = maxPageSize
	}

	return reqPage * reqPageSize, reqPageSize
}

// ListByConds lists aggregated series metadata by the specified conditions
func (d *AggSeriesMetadataShard) ListByConds(
	req *ReqListAggregatedSeries,
	loc *time.Location,
) ([]*aggSeriesMetadataRow, error) {
	where, args, err := req.toSQL(loc)
	if err != nil {
		return nil, err
	}

	offset, limit := paginate(req.Page, req.PageSize)

	var result []*aggSeriesMetadataRow
	err = d.base.Execute(func(value any) error {
		return d.base.Where(where, args...).
			Offset(offset).
			Limit(limit).
			Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		if gorm.IsRecordNotFoundError(err) || errors.Cause(err) == dao.ErrRecordNotFound {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"req": req,
		})
	}

	return result, nil
}

// CountByConds counts number of metadata rows matching the specified conditions,
// ignoring pagination
func (d *AggSeriesMetadataShard) CountByConds(
	req *ReqListAggregatedSeries,
	loc *time.Location,
) (uint64, error) {
	where, args, err := req.toSQL(loc)
	if err != nil {
		return 0, err
	}

	var result uint64
	err = d.base.Execute(func(value any) error {
		return d.base.Where(where, args...).
			Count(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return 0, nil
		}

		if gorm.IsRecordNotFoundError(err) || errors.Cause(err) == dao.ErrRecordNotFound {
			return 0, nil
		}

		return 0, errors.Trace(err).WithFields(errors.Fields{
			"req": req,
		})
	}

	return result, nil
}

// ListDistinctUIDsByItemCodesAndZoneCode lists distinct uids by item codes and zone code
func (d *AggSeriesMetadataShard) ListDistinctUIDsByItemCodesAndZoneCode(
	itemCodes []string,
	haveZone bool,
	zoneCode int64,
	epoch time.Time,
	loc *time.Location,
) ([]uint64, error) {
	conds := squirrel.And{
		squirrel.GtOrEq{"created_at": epoch.In(loc)},
	}

	if len(itemCodes) > 0 {
		conds = append(conds, squirrel.Eq{"item_code": itemCodes})
	}
	if haveZone {
		conds = append(conds, squirrel.Eq{"zone_code": zoneCode})
	}

	where, args, err := conds.ToSql()
	if err != nil {
		return nil, err
	}

	var result []uint64
	err = d.base.Execute(func(value any) error {
		return d.base.Where(where, args...).
			Select("DISTINCT `uid`").
			Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		if gorm.IsRecordNotFoundError(err) || errors.Cause(err) == dao.ErrRecordNotFound {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"itemCodes": itemCodes,
			"haveZone":  haveZone,
			"zoneCode":  zoneCode,
			"epoch":     epoch,
		})
	}

	return result, nil
}

// ListDistinctItemCodesByUIDsAndZoneCode lists distinct item codes by uids and zone code
func (d *AggSeriesMetadataShard) ListDistinctItemCodesByUIDsAndZoneCode(
	uids []uint64,
	haveZone bool,
	zoneCode int64,
	epoch time.Time,
	loc *time.Location,
) ([]string, error) {
	// 不可以不传 uids
	if len(uids) == 0 {
		return nil, nil
	}

	conds := squirrel.And{
		squirrel.Eq{"uid": uids},
		squirrel.GtOrEq{"created_at": epoch.In(loc)},
	}

	if haveZone {
		conds = append(conds, squirrel.Eq{"zone_code": zoneCode})
	}

	where, args, err := conds.ToSql()
	if err != nil {
		return nil, err
	}

	var result []string
	err = d.base.Execute(func(value any) error {
		return d.base.Where(where, args...).
			Select("DISTINCT `item_code`").
			Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		if gorm.IsRecordNotFoundError(err) || errors.Cause(err) == dao.ErrRecordNotFound {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uids":     uids,
			"haveZone": haveZone,
			"zoneCode": zoneCode,
			"epoch":    epoch,
		})
	}

	return result, nil
}

// ListDistinctUIDsByConds pulls distinct UIDs that have aggregated series data
// matching the specified conditions
func (d *AggSeriesMetadataShard) ListDistinctUIDsByConds(
	req *ReqListAggregatedSeries,
	loc *time.Location,
) ([]uint64, error) {
	where, args, err := req.toSQL(loc)
	if err != nil {
		return nil, err
	}

	offset, limit := paginate(req.Page, req.PageSize)

	var result []uint64
	err = d.base.Execute(func(value any) error {
		return d.base.Where(where, args...).
			Offset(offset).
			Limit(limit).
			Select("DISTINCT `uid`").
			Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		if errors.Cause(err) == dao.ErrRecordNotFound {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"req": req,
		})
	}

	return result, nil
}
