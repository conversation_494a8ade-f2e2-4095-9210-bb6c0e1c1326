package adapter

import (
	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/service"
)

// BuildPbPOLockDetail converts from *service.POLockDetail to the pb layer type.
func BuildPbPOLockDetail(in *service.POLockDetail) *pb.POLockDetail {
	return &pb.POLockDetail{
		CouponCode: in.CouponCode,
		OrderHash:  in.OrderHash,
		PoId:       in.PoID,
		Money:      in.Money.ToInt64(),
	}
}

// BuildPbPOLockDetails converts from []*service.POLockDetail to the pb layer type.
func BuildPbPOLockDetails(in []*service.POLockDetail) []*pb.POLockDetail {
	result := make([]*pb.POLockDetail, len(in))
	for i, x := range in {
		result[i] = BuildPbPOLockDetail(x)
	}
	return result
}

// BuildPOLockDetail converts from pb.POLockDetail to the service layer type.
func BuildPOLockDetail(in *pb.POLockDetail) *service.POLockDetail {
	return &service.POLockDetail{
		CouponCode: in.GetCouponCode(),
		OrderHash:  in.GetOrderHash(),
		PoID:       in.GetPoId(),
		Money:      base.Money(in.GetMoney()),
	}
}

// BuildPOLockDetails converts from []pb.POLockDetail to the service layer type.
func BuildPOLockDetails(in []*pb.POLockDetail) []*service.POLockDetail {
	result := make([]*service.POLockDetail, len(in))
	for i, x := range in {
		result[i] = BuildPOLockDetail(x)
	}
	return result
}
