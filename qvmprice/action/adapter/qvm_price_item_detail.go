package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/qvmprice"

	"qiniu.io/pay/qvmprice/service"
)

func BuildPbQVMPriceItemDetail(
	x *service.QVMPriceItemDetail,
) (*pb.QVMPriceItemDetail, error) {
	stairs, err := buildPbQVMItemPriceStairDetails(x.Stairs)
	if err != nil {
		return nil, err
	}

	return &pb.QVMPriceItemDetail{
		Uid:            x.UID,
		ItemCode:       x.ItemCode,
		ZoneCode:       x.ZoneCode,
		StairPriceType: x.StairPriceType,
		CurrencyType:   x.CurrencyType,
		Stairs:         stairs,
		EffectTime:     timestamppb.New(x.EffectTime),
		DeadTime:       timestamppb.New(x.DeadTime),
		CreatedAt:      timestamppb.New(x.CreatedAt),
		IsVip:          x.IsVIP,
		VipOp:          x.VIPOp,
	}, nil
}

func buildPbQVMPriceItemDetails(
	x []*service.QVMPriceItemDetail,
) ([]*pb.QVMPriceItemDetail, error) {
	return mapSliceFallible(BuildPbQVMPriceItemDetail, x)
}

func BuildPbQVMPriceItemDetailList(
	x []*service.QVMPriceItemDetail,
) (*pb.QVMPriceItemDetailList, error) {
	y, err := buildPbQVMPriceItemDetails(x)
	if err != nil {
		return nil, err
	}

	return &pb.QVMPriceItemDetailList{
		Data: y,
	}, nil
}

func buildServiceQVMPriceItemDetail(
	x *pb.QVMPriceItemDetail,
) (*service.QVMPriceItemDetail, error) {
	if err := x.EffectTime.CheckValid(); err != nil {
		return nil, err
	}

	if err := x.DeadTime.CheckValid(); err != nil {
		return nil, err
	}

	// 这个类型作为入参时，应当无视 CreatedAt、IsVIP、VIPOp

	stairs := buildServiceQVMItemPriceStairDetails(x.Stairs)

	return &service.QVMPriceItemDetail{
		UID:            x.Uid,
		ItemCode:       x.ItemCode,
		ZoneCode:       x.ZoneCode,
		StairPriceType: x.StairPriceType,
		CurrencyType:   x.CurrencyType,
		Stairs:         stairs,
		EffectTime:     x.EffectTime.AsTime(),
		DeadTime:       x.DeadTime.AsTime(),
		CreatedAt:      time.Time{},
		IsVIP:          false,
		VIPOp:          "",
	}, nil
}

func BuildServiceQVMPriceItemDetails(
	x []*pb.QVMPriceItemDetail,
) ([]*service.QVMPriceItemDetail, error) {
	return mapSliceFallible(buildServiceQVMPriceItemDetail, x)
}
