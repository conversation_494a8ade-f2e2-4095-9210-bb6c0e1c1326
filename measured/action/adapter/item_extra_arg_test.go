package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measured/model"
)

func TestItemExtraArgRoundTrip(t *testing.T) {
	x := &model.ItemExtraArg{
		ID:        123,
		ItemID:    233,
		Key:       "foo",
		Value:     "bar=2&baz=3&quux=%20",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	y, err := BuildPbItemExtraArg(x)
	assert.NoError(t, err)

	z, err := BuildModelItemExtraArg(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.ItemID, z.ItemID)
	assert.Equal(t, x.Key, z.Key)
	assert.Equal(t, x.Value, z.Value)
	assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.Unix<PERSON>ano(), z.UpdatedAt.UnixNano())
}
