package service

import (
	"github.com/qbox/bo-base/v4/base"
	"qiniu.io/pay/measured/model"
)

// PackageWithItems params for create package
type PackageWithItems struct {
	Code         string
	Type         string
	Name         string
	Description  string
	Remark       string
	PackageItems []model.PackageItem
}

// PackageWithItemsResp package data with package_items
type PackageWithItemsResp struct {
	Package      model.Package
	PackageItems []model.PackageItem
}

// UIDTimePointQuery params for query package_items at specified time
type UIDTimePointQuery struct {
	UID uint64
	At  base.HNS
}

// UIDTimeRangeQuery params for query package_items effected at time range
type UIDTimeRangeQuery struct {
	UID       uint64
	StartTime base.HNS
	EndTime   base.HNS
}

// BindPackageParam params for bind package onto user
type BindPackageParam struct {
	UID        uint64
	Code       string
	Excode     string
	EffectTime base.HNS
	DeadTime   base.HNS
	Idempotent bool
}

// BindPackageResp is response for package bind/upgrades.
type BindPackageResp struct {
	Package        model.Package
	PackageItems   []model.PackageItem
	PackageUserMap model.PackageUserMap
}

// UpgradePackageParam params for upgrade package for user
type UpgradePackageParam struct {
	UID        uint64
	SrcCode    string
	DstCode    string
	Excode     string
	EffectTime base.HNS
	DeadTime   base.HNS
}

// UnbindPackageParam params for unbind package for user
type UnbindPackageParam struct {
	UID    uint64
	Excode string
}
