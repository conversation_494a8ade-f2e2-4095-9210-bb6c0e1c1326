package adapter

import (
	"time"

	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/service"
)

// BuildBindCouponFromBatchParams converts pb.BindCouponFromBatchParams to the service layer type.
func BuildBindCouponFromBatchParams(
	in *pb.BindCouponFromBatchParams,
) (*service.BindCouponFromBatchParams, error) {
	return &service.BindCouponFromBatchParams{
		// 目前只支持真实当前时间
		// 不支持例如在 20191005 或者 20190930 的时间，当作今天是 20191001 来发券
		Epoch:                time.Time{},
		UID:                  in.Uid,
		BatchID:              in.BatchId,
		IsOverrideEffectTime: in.IsOverrideEffectTime,
		OverrideEffectTime:   in.OverrideEffectTime.AsTime(),
	}, nil
}
