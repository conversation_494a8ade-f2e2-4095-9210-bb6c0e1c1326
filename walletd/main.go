package main

import (
	"context"
	"flag"
	"net/http"
	"time"

	"github.com/go-openapi/strfmt"
	"github.com/go-redis/redis/v8"
	"github.com/grpc-ecosystem/grpc-gateway/runtime"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/qiniu/version/v2"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"github.com/qbox/bo-base/v4/cli"
	"github.com/qbox/bo-base/v4/dao"
	hook "github.com/qbox/bo-base/v4/errors/logrus"
	"github.com/qbox/bo-base/v4/eventbus"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/lock"
	baselog "github.com/qbox/bo-base/v4/log"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/pay-sdk/base/account"
	gaeaClient "github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/jm"
	notificationV2Client "github.com/qbox/pay-sdk/notification-v2/client"
	qbpmClient "github.com/qbox/pay-sdk/qbpm/client"
	sofaClient "github.com/qbox/pay-sdk/sofa/client"
	"github.com/qbox/pay-sdk/themis"
	tradeClient "github.com/qbox/pay-sdk/trade/client"
	walletClient "github.com/qbox/pay-sdk/v3/walletEx/client"
	pb "github.com/qbox/pay-sdk/wallet"

	couponAction "qiniu.io/pay/coupon/action"
	couponModel "qiniu.io/pay/coupon/model"
	couponService "qiniu.io/pay/coupon/service"
	jmAction "qiniu.io/pay/jm/action"
	jmModel "qiniu.io/pay/jm/model"
	jmService "qiniu.io/pay/jm/service"
	"qiniu.io/pay/qpay/payment"
	qpayDAO "qiniu.io/pay/qpay/wallet/dao"
	qpayService "qiniu.io/pay/qpay/wallet/service"
	"qiniu.io/pay/walletd/action"
	walletdCfg "qiniu.io/pay/walletd/config"
	"qiniu.io/pay/walletd/i18n"
	"qiniu.io/pay/walletd/model"
	"qiniu.io/pay/walletd/service"
)

func main() {
	var confPath string
	var enableHTTPPprof bool
	var runMigration bool
	flag.StringVar(&confPath, "conf", "walletd.yml", "config file path")
	flag.BoolVar(&enableHTTPPprof, "pprof", false, "enable net/http/pprof under /debug/pprof paths")
	flag.BoolVar(&runMigration, "automigrate", false, "enable auto migration of db schema")
	_ = flag.Bool("version", false, "print version info and exit")
	flag.Parse()
	cli.InitFlagMap()

	if cli.IsFlagProvided("version") {
		version.Print()
		return
	}

	// read config
	conf, err := loadConfig(confPath)
	if err != nil {
		log.WithField("err", err).Fatal("failed to load config")
		return
	}

	l10nProvider, err := intl.Init(&conf.Intl, i18n.L10nFS, i18n.RelativePath)
	if err != nil {
		log.WithError(err).Fatal("failed to init l10n mechanism")
		return
	}

	// command-line --pprof switch has higher priority over config settings
	enablePProf(conf, enableHTTPPprof)

	// init logger
	loggerEntry := initLogger()

	// init mysql dao
	baseDao, err := initMysqlDao(conf, runMigration)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init dao layer")
		return
	}

	// init redis
	redisClient := initRedisClient(conf)

	// init http client with acc
	accTransport, err := account.NewTransport(&conf.Acc)
	if err != nil {
		log.WithError(err).Fatal("failed to init http client transport")
		return
	}
	client := &http.Client{
		Transport: accTransport,
	}

	tradeSrv, err := initTradeSrv(conf, client)
	if err != nil {
		log.WithError(err).Fatal("failed to init trade client transport")
		return
	}

	themisClient, err := initThemisClient(conf)
	if err != nil {
		log.WithError(err).Fatal("failed to init themis client transport")
		return
	}

	gaeaAdminSrv, err := initGaeaAdminSrv(conf, client)
	if err != nil {
		log.WithError(err).Fatal("failed to init gaeaAdmin client transport")
		return
	}

	qbpmSrv, err := initQbpmSrv(conf, client)
	if err != nil {
		log.WithError(err).Fatal("failed to init qbpm client transport")
		return
	}

	notificationV2Srv, err := initNotificationV2Srv(conf, client)
	if err != nil {
		log.WithError(err).Fatal("failed to init notificationV2 client transport")
		return
	}

	walletV3Client, err := initWalletV3Client(conf, client)
	if err != nil {
		log.WithError(err).Fatal("failed to init wallet-v3 client transport")
		return
	}

	sofaSrv, err := initSofaSrv(conf, client)
	if err != nil {
		log.WithError(err).Fatal("failed to init sofa client transport")
		return
	}

	accClient := account.New(conf.Acc.Host, client)

	// init wallet action
	walletAction, err := initWalletAction(
		baseDao,
		conf,
		l10nProvider,
		notificationV2Srv,
		client,
		walletV3Client,
		gaeaAdminSrv,
		accTransport,
		redisClient,
	)
	if err != nil {
		log.WithError(err).Fatal("init wallet service failed")
		return
	}

	eventBus, err := initPublisher(conf)
	if err != nil {
		log.Error("initialize eventbus failed:", err)
		return
	}

	paymentFactory := payment.NewPaymentFactory(
		payment.WithBaseDao(baseDao),
		payment.WithRedisClient(redisClient),
		payment.WithGaeaSrv(gaeaAdminSrv),
		payment.WithTradeSrv(tradeSrv),
		payment.WithWalletV3(walletV3Client),
		payment.WithAccTransport(accTransport),
		payment.WithVoucherScopeWrapper(walletAction.GetWalletBizSrv()),
		payment.WithEventBus(eventBus),
		payment.WithDeductTopicPrefix(conf.Wallet.DeductTopicPrefix),
		payment.WithSofaSrv(sofaSrv),
		payment.WithAccClient(accClient),

		payment.WithL10nProvider(l10nProvider),
	)
	// init payment action
	paymentAction, err := initPaymentAction(
		paymentFactory,
		conf,
	)
	if err != nil {
		log.Error("initialize initPaymentAction failed:", err)
		return
	}

	// init coupon action
	couponLocker := lock.NewRedisLocker(
		redisClient,
		lock.WithSpinTimes(200),
		lock.WithSpinInterval(200*time.Millisecond),
	)
	cAction := initCouponAction(
		baseDao,
		couponLocker,
		tradeSrv,
		themisClient,
		qbpmSrv,
		notificationV2Srv,
		gaeaAdminSrv,
		conf,
		eventBus,
	)
	cAction.EventbusDaemon(tz.MustWithGlobalRefLocation(context.Background()))

	// init job manager action
	jobManagerAction := initJobManagerAction(baseDao, conf)

	err = rpc.Serve(
		&conf.RPC,
		loggerEntry,
		func(s *grpc.Server) {
			pb.RegisterPayWalletServiceServer(s, walletAction)
			pb.RegisterPaymentServiceServer(s, paymentAction)
			pb.RegisterPayCouponServiceServer(s, cAction)
			jm.RegisterJobManagerServiceServer(s, jobManagerAction)
		},
		func(ctx context.Context, serveMux *runtime.ServeMux, endpoint string, option []grpc.DialOption) error {
			err1 := pb.RegisterPayWalletServiceHandlerFromEndpoint(ctx, serveMux, endpoint, option)
			if err1 != nil {
				return err1
			}
			err1 = pb.RegisterPaymentServiceHandlerFromEndpoint(ctx, serveMux, endpoint, option)
			if err1 != nil {
				return err1
			}
			err1 = pb.RegisterPayCouponServiceHandlerFromEndpoint(ctx, serveMux, endpoint, option)
			if err1 != nil {
				return err1
			}
			err1 = jm.RegisterJobManagerServiceHandlerFromEndpoint(ctx, serveMux, endpoint, option)
			if err != nil {
				return err1
			}
			return nil
		},
	)

	if err != nil {
		log.WithField("err", err).Fatal("failed to serve")
		return
	}
}

func initThemisClient(conf *walletdCfg.WalletdConfig) (themis.ThemisServiceClient, error) {
	themisConn, err := rpc.GrpcConnectWithName(conf.Services.Themis, rpc.ServiceThemis, conf.RPC.Keepalive.Client)
	if err != nil {
		return nil, err
	}
	return themis.NewThemisServiceClient(themisConn), nil
}

func initGaeaAdminSrv(conf *walletdCfg.WalletdConfig, client *http.Client) (*gaeaClient.Gaea, error) {
	clientTransport, err := rpc.NewSwaggerTransport(conf.Services.GaeaAdmin, client)
	if err != nil {
		return nil, err
	}
	return gaeaClient.New(clientTransport, strfmt.Default), nil
}

func initTradeSrv(conf *walletdCfg.WalletdConfig, client *http.Client) (*tradeClient.Trade, error) {
	clientTransport, err := rpc.NewSwaggerTransport(conf.Services.Trade, client)
	if err != nil {
		return nil, err
	}
	return tradeClient.New(clientTransport, strfmt.Default), nil
}

func initQbpmSrv(conf *walletdCfg.WalletdConfig, client *http.Client) (*qbpmClient.Qbpm, error) {
	clientTransport, err := rpc.NewSwaggerTransport(conf.Services.Qbpm, client)
	if err != nil {
		return nil, err
	}
	return qbpmClient.New(clientTransport, strfmt.Default), nil
}

func initWalletV3Client(
	conf *walletdCfg.WalletdConfig,
	client *http.Client,
) (*walletClient.WalletEx, error) {
	clientTransport, err := rpc.NewSwaggerTransport(conf.Services.Wallet, client)
	if err != nil {
		return nil, err
	}

	return walletClient.New(clientTransport, strfmt.Default), nil
}

func initPaymentAction(paymentFactory *payment.Factory, conf *walletdCfg.WalletdConfig) (*action.PaymentAction, error) {
	ctx := tz.MustWithGlobalRefLocation(context.Background())
	go func() {
		paymentFactory.EventBus.Subscribe(
			conf.Wallet.DeductTopicPrefix+eventbus.WalletPaymentPostDeductTopic,
			eventbus.WalletPaymentDeductQueue,
			paymentFactory.MakeDeductMessageHandler(ctx),
		)
	}()

	return action.NewPaymentAction(conf.Wallet.DefaultPageSize, paymentFactory), nil
}

func initNotificationV2Srv(conf *walletdCfg.WalletdConfig, client *http.Client) (*notificationV2Client.NotificationV2, error) {
	clientTransport, err := rpc.NewSwaggerTransport(conf.Services.NotificationV2, client)
	if err != nil {
		return nil, err
	}
	return notificationV2Client.New(clientTransport, strfmt.Default), nil
}

func initSofaSrv(conf *walletdCfg.WalletdConfig, client *http.Client) (*sofaClient.Sofa, error) {
	clientTransport, err := rpc.NewSwaggerTransport(conf.Services.Sofa, client)
	if err != nil {
		return nil, err
	}
	return sofaClient.New(clientTransport, strfmt.Default), nil
}

func initWalletAction(
	baseDao *dao.BaseDao,
	conf *walletdCfg.WalletdConfig,
	l10nProvider intl.L10nProvider,
	noticeClient *notificationV2Client.NotificationV2,
	httpClient *http.Client,
	walletV3Client *walletClient.WalletEx,
	gaeaAdminClient *gaeaClient.Gaea,
	accTransport *account.Transport,
	redisClient redis.UniversalClient,
) (*action.WalletAction, error) {
	// BO-22888 需要能够给用户上 walletd（非钱包部分）范畴的分布式锁
	const userLockExpiryInterval = 5 * time.Minute // 一个不长不短的随便取的时间，感觉上比默认 1 小时好一点
	locker := lock.NewRedisLocker(redisClient, lock.WithExpiry(userLockExpiryInterval))

	walletService, err := service.NewWalletBizService(
		conf,
		model.NewWalletDao(baseDao),
		httpClient,
		locker,
		conf.Cache.DefaultExpires,
	)
	if err != nil {
		return nil, err
	}

	balanceInsufficiencyService, err := service.NewBalanceInsufficiencyService(
		conf,
		model.NewBalanceInsufficiencyPredictionDao(baseDao),
		noticeClient,
		walletV3Client,
		accTransport,
	)
	if err != nil {
		return nil, err
	}

	bankVirtAccountSrv, err := service.NewBankVirtualAccountService(
		model.NewUserBankVirtualAccountDao(baseDao),
		&conf.BankVirtAcc,
		redisClient,
	)
	if err != nil {
		return nil, err
	}

	userCardService := qpayService.NewUserCardService(
		qpayDAO.NewUserCardDAO(baseDao),
	)
	bankTransferSrv := service.NewBankTransferService(
		model.NewBankTransferDao(baseDao),
		qpayDAO.NewCurrencyDAO(baseDao),
		model.NewUserBankVirtualAccountDao(baseDao),
		walletV3Client,
		gaeaAdminClient,
	)

	return action.NewWalletAction(
		walletService,
		l10nProvider,
		balanceInsufficiencyService,
		bankVirtAccountSrv,
		userCardService,
		bankTransferSrv,
		conf.Wallet.DefaultPageSize,
	), nil
}

func initCouponAction(
	baseDao *dao.BaseDao,
	couponLocker lock.Locker,
	tradeClient *tradeClient.Trade,
	themisClient themis.ThemisServiceClient,
	qbpmClient *qbpmClient.Qbpm,
	notificationV2Client *notificationV2Client.NotificationV2,
	gaeaClient *gaeaClient.Gaea,
	conf *walletdCfg.WalletdConfig,
	eventbus eventbus.EventBus,
) *couponAction.CouponAction {
	// init couponBatchDao / couponBatchSrv etc.
	couponBatchSrv, err := couponService.NewCouponBatchService(
		couponModel.NewCouponBatchDao(baseDao),
		tradeClient,
		themisClient,
		conf.Cache.DefaultExpires,
	)
	if err != nil {
		log.WithError(err).Fatal("init couponBatch service failed")
		return nil
	}

	couponSrv, err := couponService.NewCouponService(
		couponModel.NewCouponDao(baseDao),
		couponLocker,
		couponBatchSrv,
		qbpmClient,
		notificationV2Client,
		gaeaClient,
		conf.Cache.DefaultExpires,
		eventbus,
	)
	if err != nil {
		log.WithError(err).Fatal("init coupon service failed")
		return nil
	}

	return couponAction.NewCouponAction(couponSrv, couponBatchSrv, conf.Wallet.DefaultPageSize)
}

func initJobManagerAction(
	baseDao *dao.BaseDao,
	conf *walletdCfg.WalletdConfig,
) *jmAction.JobManagerAction {
	jmSrv := jmService.NewJobManagerService(jmModel.NewJMDao(baseDao), conf.Cache.DefaultExpires)
	return jmAction.NewJobManagerAction(jmSrv, conf.Wallet.DefaultPageSize)
}

func initMysqlDao(conf *walletdCfg.WalletdConfig, runMigration bool) (*dao.BaseDao, error) {
	baseDao, err := dao.InitMysqlDao(&conf.MySQL, &conf.Cache)
	if err != nil {
		return nil, err
	}
	if runMigration {
		model.RegisterMigrate(baseDao.DB)
		couponModel.RegisterMigrate(baseDao.DB)
		jmModel.RegisterMigrate(baseDao.DB)
	}
	return baseDao, nil
}

func initRedisClient(conf *walletdCfg.WalletdConfig) redis.UniversalClient {
	return redis.NewUniversalClient(&conf.Cache.RedisConfig)
}

func initPublisher(conf *walletdCfg.WalletdConfig) (eventbus.EventBus, error) {
	eventBus, err := eventbus.NewRabbitMQEventBus(&conf.EventBus, log.New())
	if err != nil {
		log.Error("initialize eventbus publisher failed:", err)
		return nil, err
	}
	return eventBus, nil
}

func loadConfig(confPath string) (*walletdCfg.WalletdConfig, error) {
	conf, err := walletdCfg.LoadWalletdConfig(confPath)
	if err != nil {
		return nil, err
	}
	conf.RPC.InterceptorConf.Acc = conf.Acc
	return conf, nil
}

func enablePProf(conf *walletdCfg.WalletdConfig, enableHTTPPprof bool) {
	if !cli.IsFlagProvided("pprof") {
		return
	}
	log.WithFields(log.Fields{
		"configValue": conf.RPC.EnablePprof,
		"cliValue":    enableHTTPPprof,
	}).Info("overriding pprof option with command-line flag")
	conf.RPC.EnablePprof = enableHTTPPprof
}

func initLogger() *log.Entry {
	log.AddHook(hook.NewHook(hook.WithKeys("reqid")))
	log.SetReportCaller(true)

	log.SetFormatter(baselog.NewFlattenJSONFormatter())

	loggerEntry := rpc.NewLoggerEntry(log.StandardLogger())

	rpc.InitLogging(loggerEntry)

	return loggerEntry
}
