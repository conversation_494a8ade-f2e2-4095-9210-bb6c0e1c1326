package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"text/template"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/base/account"
	"github.com/qbox/pay-sdk/base/morse"
	"github.com/qbox/pay-sdk/middleware/logging"
	clientV2 "github.com/qbox/pay-sdk/notification-v2/client"
	operationsV2 "github.com/qbox/pay-sdk/notification-v2/client/operations"
	httpModelsV2 "github.com/qbox/pay-sdk/notification-v2/models"
	walletpb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/creditd/config"
	"qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

// NotificationService is service handler of credit
type NotificationService struct {
	conf                 *config.CreditdConfig
	generalHttpClient    *http.Client
	morseHttpClient      *http.Client
	notificationV2Client *clientV2.NotificationV2
	creditDao            *dao.CreditDao
	noticeRecordDao      *dao.NoticeRecordDao
	paymentCl            walletpb.PaymentServiceClient
}

// NewNotificationService is constructor of NotificationService
func NewNotificationService(
	conf *config.CreditdConfig,
	creditDao *dao.CreditDao,
	noticeRecordDao *dao.NoticeRecordDao,
	paymentCl walletpb.PaymentServiceClient,
) (*NotificationService, error) {
	generalTransport, err := account.NewTransport(&conf.AccConfig)
	if err != nil {
		return nil, errors.Trace(err)
	}

	morseTransport, err := morse.NewTransport(&conf.AccConfig)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &NotificationService{
		conf:              conf,
		generalHttpClient: &http.Client{Transport: generalTransport},
		morseHttpClient:   &http.Client{Transport: morseTransport},
		creditDao:         creditDao,
		noticeRecordDao:   noticeRecordDao,
		notificationV2Client: clientV2.NewHTTPClientWithConfig(nil, &clientV2.TransportConfig{
			Host: conf.Services.NotificationV2,
		}),
		paymentCl: paymentCl,
	}, nil
}

type templateFillIns struct {
	UID          uint64            `json:"uid"`
	CustomerName string            `json:"customer_name"`
	Email        string            `json:"email"`
	CurrencyType base.CurrencyType `json:"currency_type"`
	CreditLine   string            `json:"credit_line"`
	Content      string            `json:"content"`
}

type buildNotificationFunc func(templateFillIns) (*model.Notification, error)

func currencySymbol(x base.CurrencyType) string {
	return x.Symbol()
}

var renderFuncs = template.FuncMap{
	"currencySymbol": currencySymbol,
}

func (s *NotificationService) render(tmplName, tmplStr string, tfi templateFillIns) (renderedBuf bytes.Buffer, err error) {
	// Create a new template and parse the letter into it.
	t := template.Must(template.New(tmplName).Funcs(renderFuncs).Parse(tmplStr))

	renderedBuf = bytes.Buffer{}
	// Execute the template for each recipient.
	err = t.Execute(&renderedBuf, tfi)
	if err != nil {
		// TODO log
		return renderedBuf, err
	}
	return
}

// BuildNotification returns buildNotificationFunc
func (s *NotificationService) BuildNotification(tmplUnit config.TemplatesUnit) buildNotificationFunc {
	return func(tfi templateFillIns) (*model.Notification, error) {
		salesEmailContentTmpl := tmplUnit.SalesEmailTmpl
		emailHtmlTmpl := config.EmailHtmlTmpl

		salesEmailContentBuf, err := s.render("salesEmail", salesEmailContentTmpl, tfi)
		if err != nil {
			// TODO log
			return nil, err
		}

		salesEmailHtmlBuf, err := s.render("salesEmailHtml", emailHtmlTmpl, templateFillIns{Content: salesEmailContentBuf.String()})
		if err != nil {
			// TODO log
			return nil, err
		}

		return &model.Notification{
			SalesEmailSubject: tmplUnit.SalesEmailSubject,
			SalesEmailContent: salesEmailHtmlBuf.String(),
			TemplateName:      tmplUnit.TemplateName,
		}, nil
	}
}

// SendInstantNotice notifies user & salesman that certain credit has been created or activated
func (s *NotificationService) SendInstantNotice(ctx context.Context,
	uid uint64,
	event enums.Event,
	buildFunc buildNotificationFunc) error {
	l := logging.GetLogger(ctx)
	now := time.Now()
	noticeRecord := &model.NoticeRecord{
		UID:        uid,
		Event:      event,
		Status:     enums.NoticeStatusSuccess,
		NoticeType: enums.TypeInstant,
		TriggerAt:  now, // 记录发送时间
		ExpiredAt:  now, // 过期同发送时间
	}

	err := s.notify(uid, buildFunc)
	if err != nil {
		err = errors.Trace(err)
		l.Error(err)
		noticeRecord.Status = enums.NoticeStatusFailed
	}

	err = s.noticeRecordDao.DoTransaction(func(recordDao *dao.NoticeRecordDao) error {
		// 若为 EventActivate||EventCreate||EventCancel，则找到对应的延迟通知更新状态
		if event == enums.EventActivate || event == enums.EventCreate || event == enums.EventCancel {
			err := s.noticeRecordDao.UpdateStatusToCanceled(uid, enums.EventCreate, enums.NoticeStatusDelayed)
			if err != nil {
				err = errors.Trace(err)
				l.Error(err)
				return err
			}
		}
		err = s.noticeRecordDao.Save(noticeRecord)
		if err != nil {
			err = errors.Trace(err)
			l.Error(err)
			return err
		}

		return nil
	})

	if err != nil {
		err = errors.Trace(err)
		l.Error(err)
	}

	return err
}

// RegisterDelayedNotice 注册延迟通知
func (s *NotificationService) RegisterDelayedNotice(ctx context.Context, uid uint64, event enums.Event) error {
	l := logging.GetLogger(ctx)

	now := time.Now()
	noticeRecord := &model.NoticeRecord{
		UID:        uid,
		Event:      event,
		Status:     enums.NoticeStatusDelayed,
		NoticeType: enums.TypeScheduled,
		TriggerAt:  now, // 记录发送时间
		ExpiredAt:  now.Add(s.conf.NoticeConfig.DelayedDuration),
	}

	err := s.noticeRecordDao.Save(noticeRecord)
	if err != nil {
		l.Error(err)
		return errors.Trace(err)
	}
	return nil
}

func (s *NotificationService) notify(uid uint64, buildFunc buildNotificationFunc) error {
	// get user's email & phone
	userNotificationToInfo, err := s.getUserEmailAndPhone(s.conf.Services.GaeaAdmin, uid)
	if err != nil {
		return errors.Trace(err)
	}

	creditModels, err := s.creditDao.ManualCredit.GetByUID(
		&dao.GetByUIDParams{
			UID: uid,
			Status: []enums.CreditStatus{enums.StatusValid,
				enums.StatusUnActivated},
			Offset: 0,
			Limit:  1})
	if err != nil {
		return errors.Trace(err)
	}
	var creditLine float64
	if len(creditModels) == 1 {
		creditLine = base.Money(creditModels[0].CreditLine).AsYuan()
	}
	systemCredit, err := s.creditDao.SystemCredit.GetSystemCredit(uid)
	if err != nil {
		return err
	}
	if systemCredit.CreditLine != 0 && creditLine == 0 {
		creditLine = base.Money(systemCredit.CreditLine).AsYuan()
	}

	ct, err := s.paymentCl.GetSingleCurrency(context.TODO(), &walletpb.UIDParam{Uid: uid})
	if err != nil {
		return errors.Trace(err)
	}

	ti := templateFillIns{
		UID:          uid,
		CustomerName: userNotificationToInfo.GetCompanyName(),
		Email:        userNotificationToInfo.GetEmail(),
		CreditLine:   fmt.Sprintf("%.2f", creditLine),
		CurrencyType: base.CurrencyType(ct.CurrencyType),
	}
	notification, err := buildFunc(ti)
	if err != nil {
		return errors.Trace(err)
	}

	if !s.conf.Intl.IsSufy() {
		// get salesman's email & phone
		salesNotificationToInfo, err := s.getSalesEmailAndPhone(s.conf.Services.Sofa, uid)
		if err != nil {
			return errors.Trace(err)
		}

		// build mail
		mail := model.Email{
			UID:     uid,
			To:      []string{salesNotificationToInfo.GetEmail()},
			Subject: notification.SalesEmailSubject,
			Content: notification.SalesEmailContent,
		}

		// send email
		err = s.send(mail, "/api/notification/send/mail")
		if err != nil {
			logrus.WithError(err).Error("NotificationService.notify send sales email failed")
		}
	}

	params := operationsV2.NewSendMessageParamsWithHTTPClient(&http.Client{Transport: s.generalHttpClient.Transport}).
		WithContext(context.Background())
	msg := httpModelsV2.SendMessageReq{
		ChannelID:  strconv.Itoa(config.ChannelID),
		UID:        int64(uid),
		TemplateID: notification.TemplateName,
	}
	msg.TemplateData = ti
	params.WithBody(&msg)

	_, err = s.notificationV2Client.Operations.SendMessage(params)
	if err != nil {
		return errors.Errorf("invoke notification.SendMessage error: %+v,uid: %+v,template_name:%+v", err, uid, notification.TemplateName)
	}

	return nil
}

func (s *NotificationService) getUserEmailAndPhone(host string,
	uid uint64) (notificationInfo model.NotificationToInfo, err error) {
	params := url.Values{}
	params.Set("uid", fmt.Sprintf("%d", uid))

	path := "/api/developer"
	urlStr := fmt.Sprintf("%s%s?%s", host, path, params.Encode())

	resp, err := s.generalHttpClient.Get(urlStr)
	if err != nil {
		err = errors.Trace(err)
		return
	}
	defer resp.Body.Close()

	type respWrap struct {
		Code    int64
		Data    model.UserInfo
		Message string
	}
	rw := &respWrap{}
	err = json.NewDecoder(resp.Body).Decode(rw)
	if err != nil {
		err = errors.Trace(err)
		return
	}

	return model.NotificationToInfo(rw.Data), nil
}

func (s *NotificationService) getSalesEmailAndPhone(host string,
	uid uint64) (notificationInfo model.NotificationToInfo, err error) {

	path := fmt.Sprintf("/api/v1/admin/developers/%d/saler", uid)
	urlStr := fmt.Sprintf("%s%s", host, path)
	resp, err := s.generalHttpClient.Get(urlStr)
	if err != nil {
		err = errors.Trace(err)
		return
	}
	defer resp.Body.Close()

	type respWrap struct {
		Status  int64
		Data    model.SalesmanInfo
		Message string
	}
	rw := &respWrap{}
	err = json.NewDecoder(resp.Body).Decode(rw)
	if err != nil {
		err = errors.Trace(err)
		return
	}

	return model.NotificationToInfo(rw.Data), nil
}

func (s *NotificationService) send(model any, path string) (err error) {
	buf := new(bytes.Buffer)
	err = json.NewEncoder(buf).Encode(model)
	if err != nil {
		return
	}

	urlStr := fmt.Sprintf("%s%s", s.conf.Services.Morse, path)
	resp, err := s.morseHttpClient.Post(urlStr, "application/json", buf)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return errors.Trace(err)
		}
		return errors.Errorf("failed to send:%+v, body:%+v failed", model, string(bodyBytes))
	}
	return
}

// SendExpiredNotices sends expired notice （发送延迟通知）
func (s *NotificationService) SendExpiredNotices(ctx context.Context,
	buildFunc buildNotificationFunc) (total, failureCount int, err error) {
	l := logging.GetLogger(ctx)
	noticeIDs, err := s.noticeRecordDao.ListExpiredIDs()
	if err != nil {
		err = errors.Trace(err)
		return
	}
	total = len(noticeIDs)
	for _, notice := range noticeIDs {
		failed := false
		noticeRecord := &model.NoticeRecord{
			ID:     notice.ID,
			Status: enums.NoticeStatusSuccess,
		}

		notifyErr := s.notify(notice.UID, buildFunc)
		if notifyErr != nil {
			notifyErr = errors.Trace(notifyErr)
			noticeRecord.Status = enums.NoticeStatusFailed
			failed = true
			l.Error(notifyErr)
		}

		updateErr := s.noticeRecordDao.Update(noticeRecord)
		if updateErr != nil {
			updateErr = errors.Trace(updateErr)
			failed = true
			l.Error(updateErr)
		}

		if failed {
			failureCount++
		}
	}
	l.WithFields(logrus.Fields{
		"total":         total,
		"failure_count": failureCount,
		"notices":       noticeIDs,
	}).Infoln("SendExpiredNotices count info")
	return
}
