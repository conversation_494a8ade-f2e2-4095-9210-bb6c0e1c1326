package service

import (
	"context"

	log "github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/dict"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/model"
)

// ScopeDetail 计费范围详情类型定义
type ScopeDetail struct {
	ScopeID    uint64
	IsAll      bool
	Products   []model.ScopeProductMap
	ItemGroups []model.ScopeItemGroupMap
	Items      []model.ScopeItemMap
}

// IsEmpty check if scope is empty
func (s *ScopeDetail) IsEmpty() bool {
	return len(s.Products) == 0 &&
		len(s.ItemGroups) == 0 &&
		len(s.Items) == 0
}

// ItemsContain check if scope.Items contains specified item
func (s *ScopeDetail) ItemsContain(itemID uint64) (hit bool, result bool) {
	for _, scopeItem := range s.Items {
		if scopeItem.ItemID == itemID {
			return true, !scopeItem.IsExcluded
		}
	}
	return false, false
}

// GroupsContain check if scope.ItemGroups contains specified item_group
func (s *ScopeDetail) GroupsContain(groupID uint64) (hit bool, result bool) {
	for _, scopeGroup := range s.ItemGroups {
		if scopeGroup.GroupID == groupID {
			return true, !scopeGroup.IsExcluded
		}
	}
	return false, false
}

// ProductsContain check if scope.Products contains specified product
func (s *ScopeDetail) ProductsContain(productID uint64) (hit bool, result bool) {
	for _, scopeProduct := range s.Products {
		if scopeProduct.ProductID == productID {
			return true, !scopeProduct.IsExcluded
		}
	}
	return false, false
}

// GetScopeByID 通过ID获取计费范围
func (s *WalletBizService) GetScopeByID(
	ctx context.Context,
	id uint64,
) (*model.Scope, error) {
	return s.walletDao.Scope.GetByID(id, s.cacheExpires)
}

// CreateScope 创建计费范围
func (s *WalletBizService) CreateScope(
	ctx context.Context,
	scope *model.Scope,
) (*model.Scope, error) {
	scope.ID = 0
	err := s.walletDao.Scope.Save(scope, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "save scope failed").WithField("scope", scope)
	}
	return scope, nil
}

// UpdateScopeByID 通过ID删除计费范围
func (s *WalletBizService) UpdateScopeByID(
	ctx context.Context,
	id uint64,
	scope *model.Scope,
) (*model.Scope, error) {
	scope.ID = id
	err := s.walletDao.Scope.Save(scope, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "update scope failed").WithField("scope", scope)
	}
	return scope, nil
}

// DeleteScopeByID 通过ID删除计费范围
func (s *WalletBizService) DeleteScopeByID(
	ctx context.Context,
	id uint64,
) (*model.Scope, error) {
	scope, err := s.GetScopeByID(ctx, id)
	if err != nil {
		return nil, errors.Annotate(err, "query scope failed").WithField("id", id)
	}
	err = s.walletDao.Scope.Delete(scope)
	return scope, err
}

// ListAllScopes 列出所有计费范围
func (s *WalletBizService) ListAllScopes(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.Scope, error) {
	return s.walletDao.Scope.ListAll(offset, limit, s.cacheExpires)
}

// CountAllScopes 获取所有计费范围的数量
func (s *WalletBizService) CountAllScopes(
	ctx context.Context,
) (uint64, error) {
	return s.walletDao.Scope.CountAll(s.cacheExpires)
}

func (s *WalletBizService) ensureProductsInScope(
	ctx context.Context,
	scopeID uint64,
	products []model.ScopeProductMap,
) (results []model.ScopeProductMap, err error) {
	exists, err := s.walletDao.ScopeProductMap.ListByScopeID(scopeID, 0, 1000, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "list scope_product_map failed").
			WithField("scope_id", scopeID)
	}
	existsMap := make(map[uint64]model.ScopeProductMap, len(exists))
	for n, p := range exists {
		existsMap[p.ProductID] = exists[n]
	}
	for _, p := range products {
		m, ok := existsMap[p.ProductID]
		if !ok {
			m = model.ScopeProductMap{
				ScopeID:    scopeID,
				ProductID:  p.ProductID,
				IsExcluded: p.IsExcluded,
			}
			err := s.walletDao.ScopeProductMap.Save(&m, s.cacheExpires)
			if err != nil {
				return nil, errors.Annotate(err, "save scope_product_map failed").
					WithField("scope_product_map", m)
			}
		}
		results = append(results, m)
		delete(existsMap, p.ID)
	}
	for _, p := range existsMap {
		err := s.walletDao.ScopeProductMap.Delete(&p)
		if err != nil {
			return nil, errors.Annotate(err, "delete scope_product_map failed").
				WithField("scope_product_map", p)
		}
	}
	return
}

func (s *WalletBizService) ensureItemGroupsInScope(
	ctx context.Context,
	scopeID uint64,
	itemGroups []model.ScopeItemGroupMap,
) (results []model.ScopeItemGroupMap, err error) {
	exists, err := s.walletDao.ScopeItemGroupMap.ListByScopeID(scopeID, 0, 1000, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "list scope_item_group_map failed").
			WithField("scope_id", scopeID)
	}
	existsMap := make(map[uint64]model.ScopeItemGroupMap, len(exists))
	for n, g := range exists {
		existsMap[g.GroupID] = exists[n]
	}
	for _, g := range itemGroups {
		m, ok := existsMap[g.ID]
		if !ok || m.IsExcluded {
			if !ok {
				m = model.ScopeItemGroupMap{
					ScopeID:    scopeID,
					GroupID:    g.GroupID,
					IsExcluded: g.IsExcluded,
				}
			} else {
				m.IsExcluded = false
			}
			err := s.walletDao.ScopeItemGroupMap.Save(&m, s.cacheExpires)
			if err != nil {
				return nil, errors.Annotate(err, "save scope_item_group_map failed").
					WithField("scope_item_group_map", m)
			}
		}
		results = append(results, m)
		delete(existsMap, g.ID)
	}
	for _, g := range existsMap {
		err := s.walletDao.ScopeItemGroupMap.Delete(&g)
		if err != nil {
			return nil, errors.Annotate(err, "delete scope_item_group_map failed").
				WithField("id", g.ID)
		}
	}
	return
}

func (s *WalletBizService) ensureItemsInScope(
	ctx context.Context,
	scopeID uint64,
	items []model.ScopeItemMap,
) (results []model.ScopeItemMap, err error) {
	exists, err := s.walletDao.ScopeItemMap.ListByScopeID(scopeID, 0, 1000, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "list scope_item_map failed").
			WithField("scope_id", scopeID)
	}
	existsMap := make(map[uint64]model.ScopeItemMap, len(exists))
	for n, i := range exists {
		existsMap[i.ID] = exists[n]
	}
	for _, i := range items {
		m, ok := existsMap[i.ID]
		if !ok || m.IsExcluded {
			if !ok {
				m = model.ScopeItemMap{
					ScopeID:    scopeID,
					ItemID:     i.ItemID,
					IsExcluded: i.IsExcluded,
				}
			} else {
				m.IsExcluded = false
			}
			err := s.walletDao.ScopeItemMap.Save(&m, s.cacheExpires)
			if err != nil {
				return nil, errors.Annotate(err, "save scope_item_map failed").
					WithField("scope_item_map", m)
			}
		}
		results = append(results, m)
		delete(existsMap, i.ID)
	}
	for _, i := range existsMap {
		err := s.walletDao.ScopeItemMap.Delete(&i)
		if err != nil {
			return nil, errors.Annotate(err, "delete scope_item_map failed").
				WithField("id", i.ID)
		}
	}
	return
}

// CreateScopeDetail 创建计费范围详情
func (s *WalletBizService) CreateScopeDetail(
	ctx context.Context,
	detail *ScopeDetail,
) (*ScopeDetail, error) {
	scope, err := s.GetScopeByID(ctx, detail.ScopeID)
	if err != nil {
		return nil, errors.Annotate(err, "query scope failed").
			WithField("id", detail.ScopeID)
	}
	err = s.DoTransaction(func(s *WalletBizService) error {
		scope.IsAll = detail.IsAll
		_, err := s.UpdateScopeByID(ctx, scope.ID, scope)
		if err != nil {
			return errors.Annotate(err, "update scope failed").WithField("id", scope.ID)
		}

		_, err = s.ensureProductsInScope(ctx, scope.ID, detail.Products)
		if err != nil {
			return errors.Annotate(err, "ensure products in scope failed").
				WithFields(errors.Fields{
					"scope_id": scope.ID,
					"products": detail.Products,
				})
		}
		_, err = s.ensureItemGroupsInScope(ctx, scope.ID, detail.ItemGroups)
		if err != nil {
			return errors.Annotate(err, "ensure groups in scope failed").
				WithFields(errors.Fields{
					"scope_id": scope.ID,
					"groups":   detail.ItemGroups,
				})
		}
		_, err = s.ensureItemsInScope(ctx, scope.ID, detail.Items)
		if err != nil {
			return errors.Annotate(err, "ensure items in scope failed").
				WithFields(errors.Fields{
					"scope_id": scope.ID,
					"items":    detail.Items,
				})
		}
		return nil
	})
	detail.ScopeID = scope.ID
	return detail, err
}

// GetScopeDetailByID 根据计费范围ID获取计费范围详情
func (s *WalletBizService) GetScopeDetailByID(
	ctx context.Context,
	scopeID uint64,
) (*ScopeDetail, error) {
	scope, err := s.GetScopeByID(ctx, scopeID)
	if err != nil {
		return nil, errors.Annotate(err, "query scope failed").WithField("id", scopeID)
	}

	products, err := s.ListScopeProductMapsByScopeID(ctx, scopeID, 0, 1000)
	if err != nil && errors.Cause(err) != dao.ErrRecordNotFound {
		return nil, errors.Annotate(err, "list scope_product_map failed").
			WithField("id", scopeID)
	}
	itemGroups, err := s.ListScopeItemGroupMapsByScopeID(ctx, scopeID, 0, 1000)
	if err != nil && errors.Cause(err) != dao.ErrRecordNotFound {
		return nil, errors.Annotate(err, "list scope_item_group_map failed").
			WithField("id", scopeID)
	}
	// FIXME(wangxuerui): 1000 might be too small once QVM is ready
	items, err := s.ListScopeItemMapsByScopeID(ctx, scopeID, 0, 1000)
	if err != nil && errors.Cause(err) != dao.ErrRecordNotFound {
		return nil, errors.Annotate(err, "list scope_item_map failed").
			WithField("id", scopeID)
	}
	return &ScopeDetail{
		ScopeID:    scopeID,
		IsAll:      scope.IsAll,
		Products:   products,
		ItemGroups: itemGroups,
		Items:      items,
	}, nil
}

// ListAllScopeDetails 列出所有计费范围详情
func (s *WalletBizService) ListAllScopeDetails(
	ctx context.Context,
	offset int,
	limit int,
) ([]ScopeDetail, error) {
	scopes, err := s.walletDao.Scope.ListAll(offset, limit, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "list scope failed")
	}
	details := make([]ScopeDetail, len(scopes))
	for i, scope := range scopes {
		detail, err := s.GetScopeDetailByID(ctx, scope.ID)
		if err != nil {
			return nil, errors.Annotate(err, "query scope_detail failed").
				WithField("id", scope.ID)
		}
		details[i] = *detail
	}
	return details, nil
}

// ScopeCheckLegacyByID check scope by item_id、group_id、product_id as follow:
// 1. scope is empty, return true
// 2. item is empty, check group, same for group、product
// 3. item is not empty, return true if scope.Items contain item
// 4. use item.Group() override group, return true if scope.Groups constain group
// 5. use item.Product() override product, return true if scope.Products constain product
// 6. use scope.All as ok, return
func (s *WalletBizService) ScopeCheckLegacyByID(
	ctx context.Context,
	scopeID, itemID, groupID, productID uint64,
) (bool, error) {
	// get scope_detail by scope_id
	scopeDetail, err := s.GetScopeDetailByID(ctx, scopeID)
	if err != nil {
		return false, errors.Annotate(err, "query scope_detail failed").
			WithField("id", scopeID)
	}

	if scopeDetail.IsEmpty() {
		return true, nil
	}

	if itemID != 0 {
		if hit, result := scopeDetail.ItemsContain(itemID); hit {
			return result, nil
		}
		// use item.group_id override param.group_id
		item, err := s.dictClient.GetItemByID(ctx, &dict.IDParam{Id: itemID})
		if err != nil {
			return false, errors.Annotate(err, "query item failed").WithField("item_id", itemID)
		}

		groupID = item.GroupId
	}

	if groupID != 0 {
		if hit, result := scopeDetail.GroupsContain(groupID); hit {
			return result, nil
		}

		// use group.product_id override param.product_id
		group, err := s.dictClient.GetItemGroupByID(ctx, &dict.IDParam{Id: groupID})
		if err != nil {
			return false, errors.Annotate(err, "query item_group failed").
				WithField("group_id", groupID)
		}
		productID = group.ProductId
	}

	if productID != 0 {
		if hit, result := scopeDetail.ProductsContain(productID); hit {
			return result, nil
		}
	}

	return scopeDetail.IsAll, nil
}

// ScopeCheckByItemCode check scope by item code
// NOTE: itemCode should not be empty
func (s *WalletBizService) ScopeCheckByItemCode(
	ctx context.Context,
	scopeID uint64, itemCode string,
) (bool, error) {
	item, err := s.dictClient.GetItemByCode(ctx, &dict.CodeParam{Code: itemCode})
	if err != nil {
		return false, errors.Annotate(err, "query item failed").WithField("code", itemCode)
	}

	return s.ScopeCheckLegacyByID(ctx, scopeID, item.Id, 0, 0)
}

// ScopeCheckLegacyInlineByCode checks if the specified item/group/product is
// covered by the scope specified inline in v3 rules.
func (s *WalletBizService) ScopeCheckLegacyInlineByCode(
	ctx context.Context,
	scope *wallet.LegacyScope,
	itemCode, groupCode, productCode string,
) (bool, error) {
	isEmpty := len(scope.Items) == 0 &&
		len(scope.Groups) == 0 &&
		len(scope.Products) == 0
	if isEmpty {
		return true, nil
	}

	l := logging.GetLogger(ctx)

	var item *dict.Item
	var product *dict.Product
	var group *dict.ItemGroup
	var groupID uint64
	var productID uint64
	var err error
	if itemCode != "" {
		// ScopeDetail.ItemsContain
		for _, x := range scope.Items {
			if x.Code == itemCode {
				return !x.IsExcluded, nil
			}
		}
		// use item.group_id override param.group_id
		item, err = s.dictClient.GetItemByCode(ctx, &dict.CodeParam{Code: itemCode})
		if err != nil {
			return false, errors.Annotate(err, "query item failed").WithField("code", itemCode)
		}

		groupID = item.GroupId
	}

	if item == nil && groupCode != "" {
		if productCode == "" {
			// Without product ID one cannot query v4 ItemGroup with code alone.
			// Actually v3 used item's product for group's product too...
			// However wallet used this for deducting coupons so this call can't
			// error, or the whole deduct will error out leaving a user's bills
			// incompletely deducted.
			//
			// NOTE: checks with only products are unaffected.
			l.WithFields(log.Fields{
				"scope":       scope,
				"groupCode":   groupCode,
				"productCode": productCode,
			}).Warn("no item passed and no product for disambiguation")

			return false, nil
		}

		// 单看 group 有歧义，但走到这就还有 product code
		// 可以抢救一下

		// BO-6980 需要先处理一下万一 product=storage 的情况
		if productCode == "storage" {
			// XXX: 先不处理那些 product=storage 的 Fusion 计费项了。。。
			productCode = "kodo"
		}

		product, err = s.dictClient.GetProductByCode(ctx, &dict.CodeParam{Code: productCode})
		if err != nil {
			return false, errors.Annotate(err, "query product failed").
				WithField("product", productCode)
		}
		// 顺便填入 productID
		productID = product.Id

		// 找出归属该 product 的 group
		// 因为没有 ListItemGroupsByCode 所以按 product 查一查
		groups, err := s.dictClient.ListItemGroupsByProductID(
			ctx,
			&dict.IDPagingParam{
				Id:       productID,
				Page:     1,
				PageSize: 1000000,
			},
		)
		if err != nil {
			return false, errors.Annotate(err, "list item_group failed").
				WithField("product_id", productID)
		}

		// 找
		for _, obj := range groups.ItemGroups {
			if obj.Code == groupCode {
				group = obj
				break
			}
		}

		if group == nil {
			// 没命中
			l.WithFields(log.Fields{
				"groupCode":     groupCode,
				"product":       product,
				"productGroups": groups.ItemGroups,
			}).Warn("no item passed and item_group not found in product groups")
			return false, nil
		}
	}

	if group == nil && groupID != 0 {
		group, err = s.dictClient.GetItemGroupByID(ctx, &dict.IDParam{Id: groupID})
		if err != nil {
			return false, errors.Annotate(err, "query item_group failed").
				WithField("group_id", groupID)
		}
		groupCode = group.Code
	}

	if groupCode != "" {
		// ScopeDetail.GroupsContain
		for _, x := range scope.Groups {
			if x.Code == groupCode {
				return !x.IsExcluded, nil
			}
		}

		// use group.product_id override param.product_id
		productID = group.ProductId
	}

	if product == nil && productID != 0 {
		product, err = s.dictClient.GetProductByID(ctx, &dict.IDParam{Id: productID})
		if err != nil {
			return false, errors.Annotate(err, "query product failed").
				WithField("product_id", productID)
		}
		productCode = product.Code
	}

	if productCode != "" {
		// scopeDetail.ProductsContain
		for _, x := range scope.Products {
			// BO-6980 scope 定义中的 storage 可以无视
			// 因为当初迁移时洗过库，所有 storage 都有伴随的 kodo 记录
			if x.Code == "storage" {
				continue
			}

			if x.Code == productCode {
				return !x.IsExcluded, nil
			}
		}
	}

	return scope.IsAll, nil
}
