package doraItemsQuerier

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsProductSupported(t *testing.T) {
	assert.True(t, IsProductSupported("atlab"))
	assert.True(t, IsProductSupported("mps"))
	assert.True(t, IsProductSupported("market"))
	assert.False(t, IsProductSupported(""), "空值不合法")
	assert.False(t, IsProductSupported("kodo"), "KODO 不是 Dora")
	assert.False(t, IsProductSupported("dora"), "Dora 产品线不叫 dora")
	assert.False(t, IsProductSupported("ufop2"), "UFOP2 不是 Dora 计量源")
	assert.False(t, IsProductSupported("Mps"), "大小写敏感")
}
