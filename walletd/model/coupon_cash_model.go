package model

import (
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// CouponCash model definition
type CouponCash struct {
	ID          uint64 `gorm:"primary_key"`
	Code        string `gorm:"type:varchar(128);unique_index:idx_coupon_cash_code"`
	Type        string `gorm:"type:varchar(128)"`
	ScopeID     uint64 `gorm:"index:idx_coupon_cash_scope_id"`
	Name        string `gorm:"type:varchar(128)"`
	Description string `gorm:"type:varchar(512)"`
	Remark      string `gorm:"type:varchar(512)"`
	Money       int64
	EffectTime  base.HNS
	DeadTime    base.HNS
	CreatedAt   time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt   time.Time `sql:"type:DATETIME(6)"`
}

// CouponCashDao is data access object of CouponCash model
type CouponCashDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *CouponCashDao) GetCachePrefix() string {
	return "wallet:coupon_cash:"
}

// GetCacheRefs get cache refs
func (d *CouponCashDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewCouponCashDao is constructor of CouponCashDao
func NewCouponCashDao(base *dao.BaseDao) *CouponCashDao {
	cachePrefix := (*CouponCashDao)(nil).GetCachePrefix()
	return &CouponCashDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of CouponCash by id
func (d *CouponCashDao) GetByID(id uint64, expires ...time.Duration) (*CouponCash, error) {
	model := &CouponCash{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// GetByCode select a record of CouponCash by code
func (d *CouponCashDao) GetByCode(code string, expires ...time.Duration) (*CouponCash, error) {
	model := &CouponCash{Code: code}
	keys := dao.NewCacheKeysFmt(
		"%s:code:%s",
		d.cachePrefix, code,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, "`coupon_cashes`.`code` = ?", code).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("code", code)
	}
	return model, nil
}

// ListAll select all records
func (d *CouponCashDao) ListAll(offset, limit int, expires ...time.Duration) (list []CouponCash, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:offset=%d&limit=%d",
		d.cachePrefix, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Offset(offset).Limit(limit).Find(value).Error
	}, keys, &list, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": offset,
			"limit":  limit,
		})
	}
	return
}

// CountAll select count of all records
func (d *CouponCashDao) CountAll(expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:count",
		d.cachePrefix,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&CouponCash{}).Count(value).Error
	}, keys, &count, expires...)
	return
}

// ListByScopeID select records by scope_id
func (d *CouponCashDao) ListByScopeID(scopeID uint64, offset, limit int, expires ...time.Duration) (list []CouponCash, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:scope_id=%d&offset=%d&limit=%d",
		d.cachePrefix, scopeID, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&CouponCash{ScopeID: scopeID}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &list, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return
}

// CountByScopeID select count of records by scope_id
func (d *CouponCashDao) CountByScopeID(scopeID uint64, expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:count:scope_id=%d",
		d.cachePrefix, scopeID,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&CouponCash{}).Where(&CouponCash{ScopeID: scopeID}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
		})
	}
	return
}

// ListByIDs select records by ids
func (d *CouponCashDao) ListByIDs(ids []uint64, expires ...time.Duration) (list []CouponCash, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:ids=%s",
		d.cachePrefix, base.UintSliceJoin(ids, ","),
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where("`id` in (?)", ids).Find(value).Error
	}, keys, &list, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"ids": ids,
		})
	}
	return
}

// Save insert or update a record of CouponCash by id
func (d *CouponCashDao) Save(model *CouponCash, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// Delete delete a record of CouponCash
func (d *CouponCashDao) Delete(model *CouponCash) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
}
