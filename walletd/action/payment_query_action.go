package action

import (
	"context"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/action/adapter"
)

// GetUserWalletItemList get user wallet item list
func (a *PaymentAction) GetUserWalletItemList(ctx context.Context, uid *pb.UIDParam) (*pb.WalletItemList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"uid": uid.GetUid(),
	}).Info("New-Wallet : GetUserWalletItemList")

	WalletItem := make([]*pb.WalletItem, 0)
	cashList, err := a.factory.QueryService.GetCashDetail(ctx, uid.Uid)
	if err != nil {
		return nil, err
	} else {
		for _, cash := range cashList {
			item := pb.WalletItem{
				WalletId: cash.AssetID,
				Amount:   int64(cash.Amount),
				Balance:  int64(cash.Balance),
				CreatedAt: &timestamp.Timestamp{
					Seconds: cash.CreatedAt.Unix(),
				},
			}
			WalletItem = append(WalletItem, &item)
		}
	}
	nbList, err := a.factory.QueryService.GetNiuCoinDetail(ctx, uid.Uid)
	if err != nil {
		return nil, err
	} else {
		for _, nb := range nbList {
			item := pb.WalletItem{
				WalletId: nb.AssetID,
				Amount:   int64(nb.Amount),
				Balance:  int64(nb.Balance),
				CreatedAt: &timestamp.Timestamp{
					Seconds: nb.CreatedAt.Unix(),
				},
			}
			WalletItem = append(WalletItem, &item)
		}
	}
	return &pb.WalletItemList{WalletItem: WalletItem}, nil
}

// GetUserVoucherItemList get user voucher item list
func (a *PaymentAction) GetUserVoucherItemList(ctx context.Context, uid *pb.UIDParam) (*pb.VoucherItemList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"uid": uid.GetUid(),
	}).Info("New-Wallet : GetUserVoucherItemList")

	voucherList, err := a.factory.QueryService.GetVoucherDetail(ctx, uid.Uid)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid": uid.GetUid(),
		}).WithError(err).Error("GetVoucherDetail failed")
		return nil, err
	}

	pbVoucherList, err := adapter.BuildPbVoucherItemList(voucherList)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid": uid.GetUid(),
		}).WithError(err).Error("BuildPbVoucherItemList failed")
		return nil, err
	}

	return pbVoucherList, nil
}

// GetUserBalance get user balance
func (a *PaymentAction) GetUserBalance(ctx context.Context, uid *pb.UIDParam) (*pb.UserBalance, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"uid": uid.GetUid(),
	}).Info("New-Wallet : GetUserBalance")

	balance, err := a.factory.QueryService.GetUserBalance(ctx, uid.Uid)
	if balance == nil {
		return nil, err
	}
	return adapter.BuildUserBalance(balance), err
}

// GetUserVoucher get user voucher
func (a *PaymentAction) GetUserVoucher(ctx context.Context, uid *pb.UIDParam) (*pb.UserVoucher, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"uid": uid.GetUid(),
	}).Info("New-Wallet : GetUserVoucher")

	userVoucher, err := a.factory.QueryService.GetUserVoucher(ctx, uid.Uid)
	if userVoucher == nil {
		return nil, err
	}
	return &pb.UserVoucher{
		VoucherUnused:  int64(userVoucher.Unused),
		VoucherUsed:    int64(userVoucher.Used),
		VoucherOverdue: int64(userVoucher.Overdue),
	}, err
}

// QueryUserOverallDiscounts 综合优惠率业务，统计某个 UID 近 X 个月以来的现金充值与牛币、账单券赠送情况
//
// NOTE: 原先代码中使用的单词是 Overview，但表示“优惠”的单词基本都已经使用过了，
// 因此此处的 OverallDiscounts 请作为一个整体理解，与优惠模型 Discount 没有任何关系。
func (a *PaymentAction) QueryUserOverallDiscounts(
	ctx context.Context,
	req *pb.QueryUserOverallDiscountsRequest,
) (*pb.UserOverallDiscounts, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": req,
	}).Info("New-Wallet : QueryUserOverallDiscounts")

	srvReq, err := adapter.BuildQueryUserOverallDiscountsRequest(req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": req,
		}).WithError(err).Error("build query condition failed")
		return nil, err
	}

	srvResp, err := a.overallDiscountsService.QueryUserOverallDiscounts(
		ctx,
		srvReq,
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": req,
		}).WithError(err).Error("query user overall discounts failed")
		return nil, err
	}

	resp, err := adapter.BuildPbUserOverallDiscounts(&srvResp)
	if err != nil {
		logger.WithError(err).Error("BuildPbUserOverallDiscounts failed")
		return nil, err
	}

	return resp, nil
}

// QueryUserOverallDiscountsV2021 综合优惠率业务 v2021
//
// 详见 PRD: https://cf.qiniu.io/pages/viewpage.action?pageId=57484641
func (a *PaymentAction) QueryUserOverallDiscountsV2021(
	ctx context.Context,
	req *pb.QueryUserOverallDiscountsV2021Request,
) (*pb.UserOverallDiscountsV2021, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": req,
	}).Info("New-Wallet : QueryUserOverallDiscountsV2021")

	srvReq, err := adapter.BuildQueryUserOverallDiscountsV2021Request(req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": req,
		}).WithError(err).Error("build query condition failed")
		return nil, err
	}

	srvResp, err := a.overallDiscountsV2021Service.QueryUserOverallDiscountsV2021(
		ctx,
		srvReq,
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": req,
		}).WithError(err).Error("query user overall discounts failed")
		return nil, err
	}

	resp, err := adapter.BuildPbUserOverallDiscountsV2021(&srvResp)
	if err != nil {
		logger.WithError(err).Error("BuildPbUserOverallDiscountsV2021 failed")
		return nil, err
	}

	return resp, nil
}

// ListPaymentTransByCondition list payment trans by condition
func (a *PaymentAction) ListPaymentTransByCondition(
	ctx context.Context,
	request *pb.ListPaymentTransConditionRequest,
) (*pb.PaymentTransactionList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : ListPaymentTransByCondition")
	queryCondition, err := adapter.BuildQueryCondition(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build query condition failed")
		return nil, err
	}
	start := time.Now().UnixNano()
	txnList, err := a.factory.QueryService.QueryPaymentTransactionByCondition(
		ctx,
		queryCondition,
	)
	end := time.Now().UnixNano()
	logger.WithFields(logrus.Fields{
		"from API": request.FromApi,
		"cost":     end - start,
	}).Info("Call QueryPaymentTransactionByCondition Cost")
	if err != nil {
		logger.WithError(err).
			WithField("condition", queryCondition).
			Error("QueryPaymentTransactionByCondition failed")
		return nil, err
	}

	txns, err := adapter.BuildPbPaymentTransactions(txnList)
	if err != nil {
		logger.WithError(err).Error("BuildPbPaymentTransactions failed")
		return nil, err
	}

	return &pb.PaymentTransactionList{Txns: txns}, nil
}

func (a *PaymentAction) QueryPaymentTransactionByPaymentID(ctx context.Context, request *pb.CodeParam) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : QueryPaymentTransactionByPaymentID")

	txn, err := a.factory.QueryService.QueryPaymentTransactionByID(ctx, request.Code)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("QueryPaymentTransactionByID failed")
		return nil, err
	}

	pbTxn, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithError(err).Error("BuildPbPaymentTransactions failed")
		return nil, err
	}
	return pbTxn, nil
}

func (a *PaymentAction) QueryPaymentTransactionByEntryID(ctx context.Context, request *pb.EntryIDParam) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : QueryPaymentTransactionByEntryID")

	txn, err := a.factory.QueryService.QueryPaymentTransactionByEntryID(ctx, request.Uid, request.EntryId)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("QueryPaymentTransactionByID failed")
		return nil, err
	}

	pbTxn, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithError(err).Error("BuildPbPaymentTransactions failed")
		return nil, err
	}
	return pbTxn, nil
}

func (a *PaymentAction) ListWalletTransactionByPaymentID(ctx context.Context, request *pb.CodeParam) (*pb.WalletTransactionList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : ListWalletTransactionByPaymentID")

	txns, err := a.factory.QueryService.QueryWalletTransactionsByPaymentID(ctx, request.Code)

	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("ListWalletTransactionByPaymentID failed")
		return nil, err
	}

	pbTxns, err := adapter.BuildPbWalletTransactions(txns)
	if err != nil {
		logger.WithError(err).Error("BuildPbPaymentTransactions failed")
		return nil, err
	}

	return &pb.WalletTransactionList{Txns: pbTxns}, nil
}

func (a *PaymentAction) ListUids(ctx context.Context, request *pb.ListPaymentTransConditionRequest) (*pb.IDListParam, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : ListUids")

	queryCondition, err := adapter.BuildQueryCondition(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build query condition failed")
		return nil, err
	}

	uids, err := a.factory.QueryService.QueryUidsByCondition(ctx, queryCondition)
	if err != nil {
		return nil, err
	}

	return &pb.IDListParam{
		Ids: uids,
	}, nil
}

func (a *PaymentAction) ListWalletTransactionByBatchPaymentID(ctx context.Context, request *pb.CodeParamList) (*pb.WalletTransactionList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : ListWalletTransactionByBatchPaymentID")

	txns, err := a.factory.QueryService.QueryWalletTransactionsByBatchPaymentID(ctx, adapter.BuildIDList(request))
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("QueryWalletTransactionsByBatchPaymentID failed")
		return nil, err
	}

	pbTxn, err := adapter.BuildPbWalletTransactions(txns)
	if err != nil {
		logger.WithError(err).Error("BuildPbWalletTransactions failed")
		return nil, err
	}
	return &pb.WalletTransactionList{Txns: pbTxn}, nil
}

func (a *PaymentAction) GetPaymentTransactionIdsByRecharge(ctx context.Context, request *pb.CodeParam) (*pb.PaymentTransactionIdList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"paymentID": request.Code,
	}).Info("New-Wallet : ListPaymentTransactionIdsByRecharge")

	paymentIDs, err := a.factory.QueryService.ListPaymentTransactionIdsByRecharge(ctx, request.Code)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("ListPaymentTransactionIdsByRecharge failed")
		return nil, err
	}

	return &pb.PaymentTransactionIdList{PaymentIds: paymentIDs}, nil
}

func (a *PaymentAction) ListWalletTransByCondition(ctx context.Context, request *pb.ListWalletTransConditionRequest) (*pb.WalletTransactionList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : ListWalletTransConditionRequest")
	queryCondition, err := adapter.BuildWalletTransactionQuery(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("build wallet transaction query condition failed")
		return nil, err
	}
	txnList, err := a.factory.QueryService.QueryWalletTransactionByCondition(ctx, queryCondition)
	if err != nil {
		return nil, err
	}

	txns, err := adapter.BuildPbWalletTransactions(txnList)
	if err != nil {
		logger.WithError(err).Error("BuildPbWalletTransactions failed")
		return nil, err
	}

	return &pb.WalletTransactionList{Txns: txns}, nil
}

// 获取用户所有信息的明细
func (a *PaymentAction) GetUserDetail(ctx context.Context, uid *pb.UIDParam) (*pb.UserDetail, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"uid": uid.Uid,
	}).Info("New-Wallet : GetUserDetail")

	userDetail, err := a.factory.QueryService.GetUserDetail(ctx, uid.Uid)
	if err != nil {
		return nil, err
	}

	pbUserDetail, err := adapter.BuildUserDetail(userDetail)
	if err != nil {
		logger.WithError(err).Error("BuildUserDetail failed")
		return nil, err
	}

	return pbUserDetail, nil
}

// ListUserBalanceSnapshotByPaymentIDs 获取指定 paymentID 对应的余额快照
func (a *PaymentAction) ListUserBalanceSnapshotByPaymentIDs(
	ctx context.Context,
	pbParam *pb.ListUserBalanceSnapshotParam,
) (*pb.UserBalanceSnapshotList, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"uid":       pbParam.GetUid(),
		"paymentId": pbParam.GetPaymentIds(),
	}).Info("New-Wallet : ListUserBalanceSnapshotByPaymentIDs")

	param := adapter.BuildListUserBalanceSnapshotParam(pbParam)

	userBalanceSnapshots, err := a.factory.UserBalanceSnapService.ListUserBalanceSnapByPaymentIDs(ctx, param)
	if err != nil {
		logger.WithError(err).Error("ListUserBalanceSnapByPaymentIDs failed")
		return nil, err
	}

	pbUserDetail := adapter.BuildPbUserBalanceSnapshotList(userBalanceSnapshots)

	return pbUserDetail, nil
}

func (a *PaymentAction) QueryOriginalAssetsByDeductPaymentIDs(ctx context.Context, req *pb.QueryOriginalAssetsReq) (*pb.QueryOriginalAssetsResp, error) {
	logger := logging.GetLogger(ctx)
	loc := tz.MustLocationFromCtx(ctx)
	param := adapter.BuildQueryOriginalAssetsParam(*req, loc)
	rs, err := a.factory.QueryService.QueryOriginalAssetsByDeductPaymentIDs(ctx, param.MonthBillPaymentIDsMap, param.MonthOrderPaymentIDsMap, param.RefundedOrderPaymentIDs)
	if err != nil {
		logger.WithField("param", param).WithError(err).Error("QueryOriginalAssetsByDeductPaymentIDs failed")
		return nil, err
	}
	return adapter.BuildQueryOriginalAssetsResp(rs), nil
}
