package config

import (
	"os"

	"gopkg.in/yaml.v2"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/db"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/pay-sdk/base/account"
)

// PlandConfig is config for Pland service
type PlandConfig struct {
	RPC rpc.Config `yaml:"rpc"`

	Intl        intl.Config       `yaml:"intl"`
	Acc         account.AccConfig `yaml:"acc"`
	MySQL       db.MySQLConfig    `yaml:"mysql"`
	Cache       dao.CacheConfig   `yaml:"cache"`
	Plan        PlanConfig        `yaml:"plan"`
	HttpService HttpServiceConfig `yaml:"http_service"`
	RpcService  RpcServiceConfig  `yaml:"rpc_service"`
}

// PlanConfig plan custom cfg
type PlanConfig struct {
	DefaultPageSize uint64 `yaml:"default_pagesize"`
}

// HttpServiceConfig http host cfg
type HttpServiceConfig struct {
	Gaea   string `yaml:"gaea"`
	Price  string `yaml:"price"`
	Wallet string `yaml:"wallet"`
}

// RpcServiceConfig grpc host cfg
type RpcServiceConfig struct {
	Dict    string `yaml:"dict"`
	Respack string `yaml:"respack"`
	Price   string `yaml:"price"`
}

// LoadPlandConfig load config from yaml file
func LoadPlandConfig(yamlPath string) (*PlandConfig, error) {
	byts, err := os.ReadFile(yamlPath)
	if err != nil {
		return nil, err
	}
	conf := &PlandConfig{}
	err = yaml.UnmarshalStrict(byts, conf)
	if err != nil {
		return nil, err
	}
	return conf, nil
}
