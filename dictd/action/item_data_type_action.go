package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/dict"
	"qiniu.io/pay/dictd/action/adapter"
)

// GetItemDataTypeByID 根据ID获取计费项类型
func (a *DictAction) GetItemDataTypeByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ItemDataType, error) {
	dataType, err := a.dictBizSrv.GetItemDataTypeByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataType(dataType)
}

// ListItemDataTypesByItemID 根据计费项的ID获取计费项数据类型列表
func (a *DictAction) ListItemDataTypesByItemID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ItemDataTypeList, error) {
	count, err := a.dictBizSrv.CountItemDataTypesByItemID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	dataTypes, err := a.dictBizSrv.ListItemDataTypesByItemID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemDataTypeList{
		Count:         count,
		ItemDataTypes: make([]*pb.ItemDataType, len(dataTypes)),
	}
	for i, dataType := range dataTypes {
		dt, err := adapter.BuildPbItemDataType(&dataType)
		if err != nil {
			return nil, err
		}
		list.ItemDataTypes[i] = dt
	}
	return list, nil
}

// ListItemDataTypesByItemCode 根据计费项的 code 获取计费项数据类型列表
func (a *DictAction) ListItemDataTypesByItemCode(
	ctx context.Context,
	param *pb.CodePagingParam,
) (*pb.ItemDataTypeList, error) {
	count, err := a.dictBizSrv.CountItemDataTypesByItemCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	dataTypes, err := a.dictBizSrv.ListItemDataTypesByItemCode(ctx, param.GetCode(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemDataTypeList{
		Count:         count,
		ItemDataTypes: make([]*pb.ItemDataType, len(dataTypes)),
	}
	for i, dataType := range dataTypes {
		dt, err := adapter.BuildPbItemDataType(&dataType)
		if err != nil {
			return nil, err
		}
		list.ItemDataTypes[i] = dt
	}
	return list, nil
}

// CountItemDataTypesByItemID 根据计费项ID获取计费项数据类型数量
func (a *DictAction) CountItemDataTypesByItemID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemDataTypesByItemID(ctx, param.GetId())
	return &pb.CountParam{Count: count}, err
}

// CountItemDataTypesByItemCode 根据计费项 code 获取计费项数据类型数量
func (a *DictAction) CountItemDataTypesByItemCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemDataTypesByItemCode(ctx, param.GetCode())
	return &pb.CountParam{Count: count}, err
}

// CreateItemDataType 创建计费项数据类型
func (a *DictAction) CreateItemDataType(
	ctx context.Context,
	dataType *pb.ItemDataType,
) (*pb.ItemDataType, error) {
	m, err := adapter.BuildItemDataType(dataType)
	if err != nil {
		return nil, err
	}

	t, err := a.dictBizSrv.CreateItemDataType(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataType(t)
}

// UpdateItemDataTypeByID 根据ID更新计费项数据类型
func (a *DictAction) UpdateItemDataTypeByID(
	ctx context.Context,
	param *pb.IDItemDataTypeParam,
) (*pb.ItemDataType, error) {
	dataType := param.GetItemDataType()
	m, err := adapter.BuildItemDataType(dataType)
	if err != nil {
		return nil, err
	}

	t, err := a.dictBizSrv.UpdateItemDataTypeByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataType(t)
}

// DeleteItemDataTypeByID 根据ID删除计费项数据类型
func (a *DictAction) DeleteItemDataTypeByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ItemDataType, error) {
	dataType, err := a.dictBizSrv.DeleteItemDataTypeByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataType(dataType)
}
