package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/measured/model"
)

// GetStat 拉量
func (s *MeasureBizService) GetStat(
	ctx context.Context,
	params *model.StatReq,
) (*model.StatResp, error) {
	resp, err := s.statClient.GetStat(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// GetUsers 拉某产品线某区域有量用户列表
func (s *MeasureBizService) GetUsers(
	ctx context.Context,
	params *model.GetUsersParams,
) (model.UsersResp, error) {
	resp, err := s.statClient.GetUsers(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// GetItemsInUse 拉单个用户有量计费项列表
func (s *MeasureBizService) GetItemsInUse(
	ctx context.Context,
	params *model.GetItemsInUseParams,
) (model.ItemsInUseResp, error) {
	resp, err := s.statClient.GetItemsInUse(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// GetUserResourceTags 获取用户分账标签
func (s *MeasureBizService) GetUserResourceTags(
	ctx context.Context,
	uid uint64,
) (*model.UserResourceTags, error) {
	l := logging.GetLogger(ctx)
	resourceTags, err := s.statClient.GetUserResourceTags(ctx, uid)
	if err != nil {
		l.WithFields(logrus.Fields{
			"uid": uid,
		}).WithError(err).Error("statClient GetUserResourceTags failed")
		return nil, err
	}
	return resourceTags, err
}

// SyncCustomBills 同步外部第三方自定义账单
func (s *MeasureBizService) SyncCustomBills(
	ctx context.Context,
	params *model.SyncCustomBillParams,
) (any, error) {
	return s.statClient.SyncCustomBills(ctx, params)
}
