package adminModel

import (
	"errors"
	"fmt"
	"strconv"
)

var errUnknownMeasureUnitKind = errors.New("unknown measure unit kind")
var errScalingFactorNotRepresentable = errors.New("scaling factor is not representable")

var dataUnitReprs = map[ScalingFactor]string{
	1:               "B",
	scalingFactorK:  "KB",
	scalingFactorM:  "MB",
	scalingFactorG:  "GB",
	scalingFactorT:  "TB",
	scalingFactorP:  "PB",
	scalingFactorKi: "KiB",
	scalingFactorMi: "MiB",
	scalingFactorGi: "GiB",
	scalingFactorTi: "TiB",
	scalingFactorPi: "PiB",
}

var durationUnitReprs = map[ScalingFactor]string{
	scalingFactorNanosecond:  "ns",
	scalingFactorMicrosecond: "us",
	scalingFactorMillisecond: "ms",
	scalingFactorSecond:      "s",
	scalingFactorMinute:      "min",
	scalingFactorHour:        "hr",
	scalingFactorDay:         "d",
}

func (x *ParsedMeasureUnit) Repr() (string, error) {
	switch x.Kind {
	case MeasureUnitKindDimensionless:
		if x.classifier != "" {
			return fmt.Sprintf("%d-%s", x.premultipliedFactor, x.classifier), nil
		}
		return strconv.FormatInt(int64(x.premultipliedFactor), 10), nil

	case MeasureUnitKindData:
		if repr, ok := dataUnitReprs[x.premultipliedFactor]; ok {
			return repr, nil
		}
		return "", errScalingFactorNotRepresentable

	case MeasureUnitKindBandwidth:
		// only bps for now
		if x.premultipliedFactor != 1 {
			return "", errScalingFactorNotRepresentable
		}
		return "bps", nil

	case MeasureUnitKindDuration:
		if repr, ok := durationUnitReprs[x.premultipliedFactor]; ok {
			return repr, nil
		}
		return "", errScalingFactorNotRepresentable

	default:
		return "", errUnknownMeasureUnitKind
	}
}
