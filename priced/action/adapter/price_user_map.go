package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"

	"qiniu.io/pay/priced/model"
)

// BuildPbPriceUserMap 转换数据层 model PriceUserMap 为 proto model
func BuildPbPriceUserMap(priceUserMap *model.PriceUserMap) (*pb.PriceUserMap, error) {
	return &pb.PriceUserMap{
		Id:      priceUserMap.ID,
		Uid:     priceUserMap.UID,
		PriceId: priceUserMap.PriceID,

		CreatedAt: timestamppb.New(priceUserMap.CreatedAt),
		UpdatedAt: timestamppb.New(priceUserMap.UpdatedAt),
	}, nil
}

// BuildPriceUserMap 转换 proto model 为数据层 model PriceUserMap
func BuildPriceUserMap(priceUserMap *pb.PriceUserMap) (*model.PriceUserMap, error) {
	var createdAt, updatedAt time.Time
	if priceUserMap.GetCreatedAt() != nil {
		err := priceUserMap.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		createdAt = priceUserMap.GetCreatedAt().AsTime()
	}

	if priceUserMap.GetUpdatedAt() != nil {
		err := priceUserMap.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		updatedAt = priceUserMap.GetUpdatedAt().AsTime()
	}

	return &model.PriceUserMap{
		ID:        priceUserMap.GetId(),
		UID:       priceUserMap.GetUid(),
		PriceID:   priceUserMap.GetPriceId(),
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	}, nil
}
