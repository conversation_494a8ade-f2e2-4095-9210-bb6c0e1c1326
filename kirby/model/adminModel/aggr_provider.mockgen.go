// Code generated by MockGen. DO NOT EDIT.
// Source: kirby/model/adminModel/aggr_provider.go

package adminModel

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
)

// MockIAggRProvider is a mock of IAggRProvider interface.
type MockIAggRProvider struct {
	ctrl     *gomock.Controller
	recorder *MockIAggRProviderMockRecorder
}

// MockIAggRProviderMockRecorder is the mock recorder for MockIAggRProvider.
type MockIAggRProviderMockRecorder struct {
	mock *MockIAggRProvider
}

// NewMockIAggRProvider creates a new mock instance.
func NewMockIAggRProvider(ctrl *gomock.Controller) *MockIAggRProvider {
	mock := &MockIAggRProvider{ctrl: ctrl}
	mock.recorder = &MockIAggRProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAggRProvider) EXPECT() *MockIAggRProviderMockRecorder {
	return m.recorder
}

// GetAggregationRule mocks base method.
func (m *MockIAggRProvider) GetAggregationRule(ctx context.Context, epoch time.Time, aggrCode string) (*AggregationRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAggregationRule", ctx, epoch, aggrCode)
	ret0, _ := ret[0].(*AggregationRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAggregationRule indicates an expected call of GetAggregationRule.
func (mr *MockIAggRProviderMockRecorder) GetAggregationRule(ctx, epoch, aggrCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregationRule", reflect.TypeOf((*MockIAggRProvider)(nil).GetAggregationRule), ctx, epoch, aggrCode)
}
