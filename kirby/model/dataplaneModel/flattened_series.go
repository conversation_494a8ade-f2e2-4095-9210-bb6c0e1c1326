package dataplaneModel

import (
	"time"

	pb "github.com/qbox/bo-sdk/kirbypb"
	"qiniu.io/pay/kirby/model/adminModel"
)

type FlattenedSeries struct {
	// 资源 QRN
	QRN string
	// 计量指标 code
	MM string
	// 合并后的序列属性
	MergedProps map[string]string
	// 推入时的计量包 UUID
	MBUUID string
	// 起始时刻（含）
	Start time.Time
	// 结束时刻（不含）
	End time.Time
	// 计量点
	Data []*pb.StatPoint

	// 需要补完的数据

	RTDCode     string
	AggRCode    string
	MeasureUnit adminModel.MeasureUnit
	G           adminModel.Granularity

	UID      uint64
	ItemCode string
	ZoneCode int64
}

func (x *FlattenedSeries) ToLogObject() map[string]any {
	return map[string]any{
		"qrn":         x.QRN,
		"mm":          x.MM,
		"mergedProps": x.MergedProps,
		"start":       x.Start,
		"end":         x.End,
		"lenData":     len(x.Data),
		"derived": map[string]any{
			"rtdCode":     x.RTDCode,
			"aggrCode":    x.AggRCode,
			"measureUnit": x.MeasureUnit,
			"g":           x.G,
			"uid":         x.UID,
			"itemCode":    x.ItemCode,
			"zoneCode":    x.ZoneCode,
		},
	}
}
