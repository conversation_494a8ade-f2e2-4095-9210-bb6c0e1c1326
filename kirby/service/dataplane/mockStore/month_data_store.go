package mockStore

import (
	"sort"
	"sync"
	"time"

	"github.com/samber/lo"
	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/uuid"
	"github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/dataplaneModel"
)

type itemAndZone struct {
	i string
	z int64
}

type storedFlattenedSeries struct {
	seriesKey string
	ctime     time.Time
	fs        *dataplaneModel.FlattenedSeries
}

type storedAggrSeries struct {
	aggrSeriesKey string
	aggrCode      string
	ctime         time.Time
	start         time.Time
	end           time.Time
	itemCode      string
	zoneCode      int64
	unit          adminModel.MeasureUnit
	g             adminModel.Granularity
	// map[statPoint.Time.Unix()]statPoint.Data
	points map[int64]uint64
}

// one UID's one month's worth of data
type mockMonthDataStore struct {
	uid uint64

	seriesMu          sync.RWMutex
	rawSeriesData     []storedFlattenedSeries
	pendingSeriesData []storedFlattenedSeries

	aggrSeriesMu   sync.RWMutex
	aggregatedData map[itemAndZone][]*storedAggrSeries
}

func newMockMonthDataStore(uid uint64) *mockMonthDataStore {
	return &mockMonthDataStore{
		uid:               uid,
		seriesMu:          sync.RWMutex{},
		rawSeriesData:     nil,
		pendingSeriesData: nil,
		aggregatedData:    make(map[itemAndZone][]*storedAggrSeries),
	}
}

type aggregationBuffer struct {
	buf map[itemAndZone]*storedAggrSeries
}

func newAggregationBuffer() *aggregationBuffer {
	return &aggregationBuffer{
		buf: make(map[itemAndZone]*storedAggrSeries),
	}
}

func (x *aggregationBuffer) ensureAggregatedState(
	iz itemAndZone,
	unit adminModel.MeasureUnit,
	g adminModel.Granularity,
) *storedAggrSeries {
	if state, ok := x.buf[iz]; ok {
		return state
	}

	newState := &storedAggrSeries{
		aggrSeriesKey: uuid.New(),
		aggrCode:      "",
		ctime:         time.Now(),
		itemCode:      iz.i,
		zoneCode:      iz.z,
		unit:          unit,
		g:             g,
		points:        make(map[int64]uint64),
	}
	x.buf[iz] = newState
	return newState
}

func (x *mockMonthDataStore) feedSeries(s *dataplaneModel.FlattenedSeries) {
	x.seriesMu.Lock()
	defer x.seriesMu.Unlock()

	now := time.Now()
	x.pendingSeriesData = append(x.pendingSeriesData, storedFlattenedSeries{
		seriesKey: uuid.NewWithEpoch(now),
		ctime:     now,
		fs:        s,
	})
}

func (x *mockMonthDataStore) persistAllPendingSeries() int {
	x.seriesMu.Lock()
	defer x.seriesMu.Unlock()

	x.rawSeriesData = append(x.rawSeriesData, x.pendingSeriesData...)
	numSeriesProcessed := len(x.pendingSeriesData)
	x.pendingSeriesData = nil
	return numSeriesProcessed
}

func (x *mockMonthDataStore) runAggregation(
	since time.Time,
	epoch time.Time,
) error {
	x.seriesMu.RLock()
	seriesData := x.rawSeriesData
	x.seriesMu.RUnlock()

	sinceIdx, _ := slices.BinarySearchFunc(seriesData, since, func(a storedFlattenedSeries, t time.Time) int {
		return a.ctime.Compare(t)
	})
	epochIdx, _ := slices.BinarySearchFunc(seriesData, epoch, func(a storedFlattenedSeries, t time.Time) int {
		return a.ctime.Compare(t)
	})
	newSeriesSince := seriesData[sinceIdx:epochIdx]
	if len(newSeriesSince) == 0 {
		return nil
	}

	izsToRecompute := make(map[itemAndZone]struct{})
	for _, sfs := range newSeriesSince {
		iz := itemAndZone{i: sfs.fs.ItemCode, z: sfs.fs.ZoneCode}
		izsToRecompute[iz] = struct{}{}
	}

	// recompute (item_code, zone_code) states affected by replace of existing data
	// TODO: possibly make this more performant (if current approach is not enough)
	aggBuf := newAggregationBuffer()
	for _, sfs := range seriesData {
		iz := itemAndZone{i: sfs.fs.ItemCode, z: sfs.fs.ZoneCode}
		if _, ok := izsToRecompute[iz]; ok {
			aggBuf.aggregateInto(iz, sfs.fs)
		}
	}

	x.aggrSeriesMu.Lock()
	defer x.aggrSeriesMu.Unlock()

	for iz, newState := range aggBuf.buf {
		x.aggregatedData[iz] = append(x.aggregatedData[iz], newState)
	}

	return nil
}

// sum into an existing (uid, item_code, zone_code)
// only call this while holding x.mu
func (x *aggregationBuffer) aggregateInto(
	iz itemAndZone,
	series *dataplaneModel.FlattenedSeries,
) {
	if len(series.Data) == 0 {
		return
	}

	state := x.ensureAggregatedState(iz, series.MeasureUnit, series.G)
	state.aggrCode = series.AggRCode
	minTime := series.Data[0].Time.Seconds
	maxTime := series.Data[0].Time.Seconds
	for _, p := range series.Data {
		state.points[p.Time.Seconds] += p.Value
		if minTime > p.Time.Seconds {
			minTime = p.Time.Seconds
		}
		if maxTime < p.Time.Seconds {
			maxTime = p.Time.Seconds
		}
	}
	if state.start.IsZero() || state.start.Unix() > minTime {
		state.start = time.Unix(minTime, 0).UTC()
	}
	if state.end.IsZero() || state.end.Unix() < maxTime {
		state.end = time.Unix(maxTime, 0).UTC()
	}
}

func (x *mockMonthDataStore) hasData(
	itemCodes []string,
	zoneCode int64,
) bool {
	x.seriesMu.RLock()
	defer x.seriesMu.RUnlock()

	for _, i := range itemCodes {
		key := itemAndZone{i: i, z: zoneCode}
		if _, ok := x.aggregatedData[key]; ok {
			return true
		}
	}

	return false
}

func (x *mockMonthDataStore) listItemCodesOfZone(
	zoneCode int64,
) []string {
	x.seriesMu.RLock()
	defer x.seriesMu.RUnlock()

	// why does samber/lo provide no FilterMap...
	resultSet := make(map[string]struct{})
	for iz := range x.aggregatedData {
		if iz.z == zoneCode {
			resultSet[iz.i] = struct{}{}
		}
	}

	result := lo.MapToSlice(resultSet, func(k string, v struct{}) string { return k })
	sort.Strings(result)
	return result
}

func toSet[T comparable](l []T) map[T]struct{} {
	var result map[T]struct{}
	if len(l) == 0 {
		return nil
	}
	result = make(map[T]struct{}, len(l))
	for _, a := range l {
		result[a] = struct{}{}
	}
	return result
}

func isSeriesTimeMatching(
	s *storedFlattenedSeries,
	epoch time.Time,
	dataTimeFrom time.Time,
	dataTimeTo time.Time,
	ctimeFrom time.Time,
	ctimeTo time.Time,
) bool {
	// ctime >= epoch -> false
	// epoch is guaranteed non-zero
	if !s.ctime.Before(epoch) {
		return false
	}
	// ctime < ctimeFrom || ctime >= cimeTo -> false
	if !ctimeFrom.IsZero() && s.ctime.Before(ctimeFrom) {
		return false
	}
	if !ctimeTo.IsZero() && !s.ctime.Before(ctimeTo) {
		return false
	}
	// s.end <= dataTimeFrom || s.start >= dataTimeTo -> false
	// dataTimeFrom & dataTimeTo is guaranteed non-zero
	if !s.fs.End.After(dataTimeFrom) || !s.fs.Start.Before(dataTimeTo) {
		return false
	}
	return true
}

func (x *mockMonthDataStore) listSeries(
	epoch time.Time,
	dataTimeFrom time.Time,
	dataTimeTo time.Time,
	ctimeFrom time.Time,
	ctimeTo time.Time,
	seriesKeys []string,
	mbUUIDs []string,
	qrns []string,
	rtdCodes []string,
	mms []string,
	spConditions []*dataplaneModel.SPCondition,
) *dataplaneModel.SeriesDetailList {
	x.seriesMu.RLock()
	defer x.seriesMu.RUnlock()

	seriesKeysSet := toSet(seriesKeys)
	mbUUIDsSet := toSet(mbUUIDs)
	qrnsSet := toSet(qrns)
	rtdCodesSet := toSet(rtdCodes)
	mmsSet := toSet(mms)

	var matchedData []storedFlattenedSeries
	for _, sfs := range x.rawSeriesData {
		if seriesKeysSet != nil {
			if _, ok := seriesKeysSet[sfs.seriesKey]; !ok {
				continue
			}
		}

		if qrnsSet != nil {
			if _, ok := qrnsSet[sfs.fs.QRN]; !ok {
				continue
			}
		}

		if mbUUIDsSet != nil {
			if _, ok := mbUUIDsSet[sfs.fs.MBUUID]; !ok {
				continue
			}
		}

		if rtdCodesSet != nil {
			if _, ok := rtdCodesSet[sfs.fs.RTDCode]; !ok {
				continue
			}
		}

		if mmsSet != nil {
			if _, ok := mmsSet[sfs.fs.MM]; !ok {
				continue
			}
		}

		if !isSeriesTimeMatching(&sfs, epoch, dataTimeFrom, dataTimeTo, ctimeFrom, ctimeTo) {
			continue
		}
		matchedData = append(matchedData, sfs)
	}

	resultSet := lo.Map(matchedData, func(a storedFlattenedSeries, _ int) *dataplaneModel.SeriesDetail {
		return &dataplaneModel.SeriesDetail{
			SeriesKey: a.seriesKey,
			QRN:       a.fs.QRN,
			MM:        a.fs.MM,
			Props:     a.fs.MergedProps,
			Start:     a.fs.Start,
			End:       a.fs.End,
			MBUUID:    a.fs.MBUUID,
			UID:       x.uid,
			RTDCode:   a.fs.RTDCode,
			AggRCode:  a.fs.AggRCode,
			Unit:      string(a.fs.MeasureUnit),
			G:         a.fs.G,
			Data:      a.fs.Data,
			Ctime:     a.ctime,
		}
	})

	return &dataplaneModel.SeriesDetailList{
		Data:  resultSet,
		Count: uint64(len(resultSet)),
	}
}

func isAggrSeriesTimeMatching(
	s *storedAggrSeries,
	epoch time.Time,
	dataTimeFrom time.Time,
	dataTimeTo time.Time,
	ctimeFrom time.Time,
	ctimeTo time.Time,
) bool {
	// ctime >= epoch -> false
	// epoch is guaranteed non-zero
	if !s.ctime.Before(epoch) {
		return false
	}
	// ctime < ctimeFrom || ctime >= cimeTo -> false
	if !ctimeFrom.IsZero() && s.ctime.Before(ctimeFrom) {
		return false
	}
	if !ctimeTo.IsZero() && !s.ctime.Before(ctimeTo) {
		return false
	}
	// s.end <= dataTimeFrom || s.start >= dataTimeTo -> false
	// dataTimeFrom & dataTimeTo is guaranteed non-zero
	if !s.end.After(dataTimeFrom) || !s.start.Before(dataTimeTo) {
		return false
	}
	return true
}

func (x *mockMonthDataStore) listAggregatedSeries(
	epoch time.Time,
	dataTimeFrom time.Time,
	dataTimeTo time.Time,
	ctimeFrom time.Time,
	ctimeTo time.Time,
	aggSeriesKeys []string,
	itemCodes []string,
) *dataplaneModel.AggregatedSeriesDetailList {
	x.aggrSeriesMu.RLock()
	defer x.aggrSeriesMu.RUnlock()

	itemCodesSet := toSet(itemCodes)
	aggSeriesKeysSet := toSet(aggSeriesKeys)

	var matchedData []*storedAggrSeries
	for k, data := range x.aggregatedData {
		if itemCodesSet != nil {
			if _, ok := itemCodesSet[k.i]; !ok {
				continue
			}
		}

		for _, s := range data {
			if aggSeriesKeysSet != nil {
				if _, ok := aggSeriesKeysSet[s.aggrSeriesKey]; !ok {
					continue
				}
			}
			if !isAggrSeriesTimeMatching(s, epoch, dataTimeFrom, dataTimeTo, ctimeFrom, ctimeTo) {
				continue
			}
			matchedData = append(matchedData, s)
		}
	}

	resultSet := lo.Map(matchedData, func(a *storedAggrSeries, _ int) *dataplaneModel.AggregatedSeriesDetail {
		return &dataplaneModel.AggregatedSeriesDetail{
			AggSeriesKey: a.aggrSeriesKey,
			UID:          x.uid,
			Start:        a.start,
			End:          a.end,
			ItemCode:     a.itemCode,
			ZoneCode:     a.zoneCode,
			AggRCode:     a.aggrCode,
			Unit:         string(a.unit),
			G:            a.g,
			Data:         transformStoredPoints(a.points),
			Ctime:        a.ctime,
			Sources:      nil,
		}
	})

	return &dataplaneModel.AggregatedSeriesDetailList{
		Data:  resultSet,
		Count: uint64(len(resultSet)),
	}
}

func (x *mockMonthDataStore) queryAggregatedSeriesData(
	epoch time.Time,
	iz itemAndZone,
) *storedAggrSeries {
	x.aggrSeriesMu.RLock()
	defer x.aggrSeriesMu.RUnlock()

	// find the most recent entry w.r.t. epoch
	// lo.Max isn't usable because custom comparator cannot be used
	var m *storedAggrSeries
	for _, a := range x.aggregatedData[iz] {
		// if ctime >= epoch: continue
		if !a.ctime.Before(epoch) {
			continue
		}
		if m == nil || a.ctime.After(m.ctime) {
			m = a
			continue
		}
	}

	return m
}

func transformStoredPoints(points map[int64]uint64) []*kirbypb.StatPoint {
	tmp := lo.MapToSlice(points, func(tsUnix int64, v uint64) *kirbypb.StatPoint {
		return &kirbypb.StatPoint{
			Time:  timestamppb.New(time.Unix(tsUnix, 0)),
			Value: v,
		}
	})
	sort.Slice(tmp, func(i int, j int) bool {
		return tmp[i].Time.AsTime().Before(tmp[j].Time.AsTime())
	})
	return tmp
}
