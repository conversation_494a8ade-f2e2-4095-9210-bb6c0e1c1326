package model

import (
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// ItemStatConfig model definition
type ItemStatConfig struct {
	ID          uint64 `gorm:"primary_key"`
	ItemID      uint64 `gorm:"index:idx_item_stat_config_item_id"`
	StatSrcType uint64 `gorm:"index:idx_item_stat_config_stat_src_type"`
}

// ItemStatConfigDao is data access object of ItemStatConfig model
type ItemStatConfigDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *ItemStatConfigDao) GetCachePrefix() string {
	return "measure:item_stat_config:"
}

// GetCacheRefs get cache refs
func (d *ItemStatConfigDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewItemStatConfigDao is constructor of ItemStatConfigDao
func NewItemStatConfigDao(base *dao.BaseDao) *ItemStatConfigDao {
	cachePrefix := (*ItemStatConfigDao)(nil).GetCachePrefix()
	return &ItemStatConfigDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of ItemStatConfig by ID
func (d *ItemStatConfigDao) GetByID(id uint64, expires ...time.Duration) (*ItemStatConfig, error) {
	model := &ItemStatConfig{}
	keys := dao.NewCacheKeysFmt(
		"%s:config_id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// GetByItemID select a record of ItemStatConfig by item ID
func (d *ItemStatConfigDao) GetByItemID(id uint64, expires ...time.Duration) (*ItemStatConfig, error) {
	model := &ItemStatConfig{}
	keys := dao.NewCacheKeysFmt(
		"%s:config_item_id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, "`item_stat_configs`.`item_id` = ?", id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("item_id", id)
	}
	return model, nil
}

// ListItemIDsByStatSrcType lists item IDs with the specified StatSrcType
//
// NOTE: The query is uncached.
func (d *ItemStatConfigDao) ListItemIDsByStatSrcType(
	statSrcType uint64,
) ([]uint64, error) {
	var result []uint64
	err := d.base.
		Model(&ItemStatConfig{}).
		Where("`stat_src_type` = ?", statSrcType).
		Pluck("item_id", &result).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithField("stat_src_type", statSrcType)
	}
	return result, nil
}

// Save insert or update a record of ItemStatConfig by id
func (d *ItemStatConfigDao) Save(model *ItemStatConfig, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// Delete delete a record of ItemStatConfig
func (d *ItemStatConfigDao) Delete(model *ItemStatConfig) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
}

// DeleteByID delete a record of ItemStatConfig by id
func (d *ItemStatConfigDao) DeleteByID(id uint64) error {
	model := &ItemStatConfig{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}
