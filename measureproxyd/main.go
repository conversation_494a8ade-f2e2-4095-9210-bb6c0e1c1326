package main

import (
	"flag"

	"github.com/qiniu/version/v2"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"github.com/qbox/bo-base/v4/cli"
	hook "github.com/qbox/bo-base/v4/errors/logrus"
	"github.com/qbox/bo-base/v4/intl"
	baselog "github.com/qbox/bo-base/v4/log"
	"github.com/qbox/bo-base/v4/rpc"
	pb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measureproxyd/action"
	"qiniu.io/pay/measureproxyd/config"
	"qiniu.io/pay/measureproxyd/i18n"
	"qiniu.io/pay/measureproxyd/service"
)

func main() {
	var confPath string
	var enableHTTPPprof bool
	flag.StringVar(&confPath, "conf", "measureproxyd.yml", "config file path")
	flag.BoolVar(&enableHTTPPprof, "pprof", false, "enable net/http/pprof under /debug/pprof paths")
	_ = flag.Bool("version", false, "print version info and exit")
	flag.Parse()
	cli.InitFlagMap()

	if cli.IsFlagProvided("version") {
		version.Print()
		return
	}

	conf, err := config.LoadMeasureProxydConfig(confPath)
	if err != nil {
		log.WithField("err", err).Fatal("failed to load config")
	}

	_, err = intl.Init(&conf.Intl, i18n.L10nFS, i18n.RelativePath)
	if err != nil {
		log.WithError(err).Fatal("failed to init l10n mechanism")
		return
	}

	// command-line --pprof switch has higher priority over config settings
	if cli.IsFlagProvided("pprof") {
		log.WithFields(log.Fields{
			"configValue": conf.RPC.EnablePprof,
			"cliValue":    enableHTTPPprof,
		}).Info("overriding pprof option with command-line flag")
		conf.RPC.EnablePprof = enableHTTPPprof
	}

	log.AddHook(hook.NewHook(hook.WithKeys("reqid")))
	log.SetFormatter(baselog.NewFlattenJSONFormatter())
	loggerEntry := rpc.NewLoggerEntry(log.StandardLogger())
	rpc.InitLogging(loggerEntry)

	measureService, err := service.NewMeasureProxyBizService(&conf.Acc, &conf.StatSources)
	if err != nil {
		log.WithField("err", err).Fatal("failed to initialize measureService layer")
	}

	resourceTagsService, err := service.NewResourceTagsService(&conf.Acc, &conf.ResourceTags)
	if err != nil {
		log.WithField("err", err).Fatal("failed to initialize resourceTagsService layer")
	}

	a := action.NewMeasureProxyAction(measureService, resourceTagsService)

	if err := rpc.Serve(
		&conf.RPC,
		loggerEntry,
		func(s *grpc.Server) {
			pb.RegisterPayMeasureProxyServiceServer(s, a)
		},
		pb.RegisterPayMeasureProxyServiceHandlerFromEndpoint,
	); err != nil {
		log.WithField("err", err).Fatal("failed to serve")
	}
}
