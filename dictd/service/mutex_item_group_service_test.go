package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestMutexItemGroupService_CreateOrUpdateMutexItemGroup(t *testing.T) {
	sandbox := buildSandbox(t)
	ctx := context.TODO()

	// 空的计费项互斥组
	mutexItemGroupName := "BlowingInTheWind"
	err := sandbox.dictService.CreateOrUpdateMutexItemGroup(ctx, &model.MutexItemGroup{
		Name:            mutexItemGroupName,
		MutexGroups:     nil,
		MustEnabledItem: true,
	})
	assert.NotNil(t, err)

	// 有不存在的计费项，报错
	mutexGroups := [][]string{
		{"cdn:http", "cdn:https"},
		{"cdn:all"},
	}
	err = sandbox.dictService.CreateOrUpdateMutexItemGroup(ctx, &model.MutexItemGroup{
		Name:            mutexItemGroupName,
		MutexGroups:     mutexGroups,
		MustEnabledItem: true,
	})
	assert.NotNil(t, err)

	// 合法的
	item1, err := sandbox.dictService.CreateItem(ctx, &model.Item{
		Code: "cdn:http",
	})
	assert.Nil(t, err)

	item2, err := sandbox.dictService.CreateItem(ctx, &model.Item{
		Code: "cdn:https",
	})
	assert.Nil(t, err)

	item3, err := sandbox.dictService.CreateItem(ctx, &model.Item{
		Code: "cdn:all",
	})
	assert.Nil(t, err)
	mutexGroups = [][]string{
		{item1.Code, item2.Code},
		{item3.Code},
	}
	err = sandbox.dictService.CreateOrUpdateMutexItemGroup(ctx, &model.MutexItemGroup{
		Name:            mutexItemGroupName,
		MutexGroups:     mutexGroups,
		MustEnabledItem: true,
	})
	assert.Nil(t, err)

	// 更新一个不存在的计费项
	mutexGroups = append(mutexGroups, []string{"cdn:ndc"})
	err = sandbox.dictService.CreateOrUpdateMutexItemGroup(ctx, &model.MutexItemGroup{
		ID:              1,
		Name:            mutexItemGroupName,
		MutexGroups:     mutexGroups,
		MustEnabledItem: true,
	})
	assert.NotNil(t, err)

	_, err = sandbox.dictService.CreateItem(ctx, &model.Item{
		Code: "cdn:ndc",
	})
	assert.Nil(t, err)
	err = sandbox.dictService.CreateOrUpdateMutexItemGroup(ctx, &model.MutexItemGroup{
		ID:              1,
		Name:            mutexItemGroupName,
		MutexGroups:     mutexGroups,
		MustEnabledItem: true,
	})
	assert.Nil(t, err)

	mutexItemGroups, err := sandbox.dictService.ListAllMutexItemGroups(ctx)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(mutexItemGroups))
	assert.Equal(t, mutexGroups, mutexItemGroups[0].MutexGroups)
	assert.Equal(t, mutexItemGroupName, mutexItemGroups[0].Name)
	assert.True(t, mutexItemGroups[0].MustEnabledItem)
}

func TestMutexItemGroupService_ListAllMutexItemGroups(t *testing.T) {
	sandbox := buildSandbox(t)
	ctx := context.TODO()

	item1, err := sandbox.dictService.CreateItem(ctx, &model.Item{
		Code: "cdn:http",
	})
	assert.Nil(t, err)

	emptyMutexGroups, err := sandbox.dictService.ListAllMutexItemGroups(ctx)
	assert.Nil(t, err)
	assert.Equal(t, 0, len(emptyMutexGroups))

	item2, err := sandbox.dictService.CreateItem(ctx, &model.Item{
		Code: "cdn:https",
	})
	assert.Nil(t, err)

	item3, err := sandbox.dictService.CreateItem(ctx, &model.Item{
		Code: "cdn:all",
	})
	assert.Nil(t, err)

	mutexItemGroupName := "TooYoungTooSimple"
	mutexGroups := [][]string{
		{item1.Code, item2.Code},
		{item3.Code},
	}
	err = sandbox.dictService.CreateOrUpdateMutexItemGroup(ctx, &model.MutexItemGroup{
		Name:            mutexItemGroupName,
		MutexGroups:     mutexGroups,
		MustEnabledItem: true,
	})
	assert.Nil(t, err)

	allMutexItemGroups, err := sandbox.dictService.ListAllMutexItemGroups(ctx)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(allMutexItemGroups))
	assert.Equal(t, mutexItemGroupName, allMutexItemGroups[0].Name)
	assert.True(t, allMutexItemGroups[0].MustEnabledItem)
	assert.Equal(t, mutexGroups, allMutexItemGroups[0].MutexGroups)

	item11, err := sandbox.dictService.CreateItem(ctx, &model.Item{
		Code: "cdn:ndc",
	})
	assert.Nil(t, err)
	anotherMutexItemGroupName := "SometimesNaive"
	anotherMutexGroups := [][]string{
		{item1.Code},
		{item11.Code},
	}
	err = sandbox.dictService.CreateOrUpdateMutexItemGroup(ctx, &model.MutexItemGroup{
		Name:            anotherMutexItemGroupName,
		MutexGroups:     anotherMutexGroups,
		MustEnabledItem: true,
	})
	assert.Nil(t, err)

	allMutexItemGroups, err = sandbox.dictService.ListAllMutexItemGroups(ctx)
	assert.Nil(t, err)
	assert.Equal(t, 2, len(allMutexItemGroups))
}
