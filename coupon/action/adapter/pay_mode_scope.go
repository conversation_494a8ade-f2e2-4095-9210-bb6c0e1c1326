package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/model"
)

// BuildPbCouponPayModeScopes converts from []model.CouponPayMode to the pb layer type.
func BuildPbCouponPayModeScopes(in []model.CouponPayMode) []pb.CouponPayModeScope {
	result := make([]pb.CouponPayModeScope, len(in))
	for i, x := range in {
		result[i] = pb.CouponPayModeScope(x)
	}
	return result
}

// BuildCouponPayModes converts from []pb.CouponPayModeScope to the model layer type.
func BuildCouponPayModes(in []pb.CouponPayModeScope) []model.CouponPayMode {
	result := make([]model.CouponPayMode, len(in))
	for i, x := range in {
		result[i] = model.CouponPayMode(x)
	}
	return result
}
