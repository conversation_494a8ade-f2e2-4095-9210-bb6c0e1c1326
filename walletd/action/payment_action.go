package action

import (
	"github.com/qbox/bo-base/v4/action"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qpay/payment"
	qpay "qiniu.io/pay/qpay/payment/service"
)

// PaymentAction defines PaymentAction
type PaymentAction struct {
	*action.BaseAction
	pb.UnimplementedPaymentServiceServer

	defaultPageSize              uint64
	billPaymentService           qpay.PaymentService
	orderPaymentService          qpay.OrderPaymentService
	depositService               qpay.DepositService
	genericService               qpay.GenericService
	billRefundService            qpay.RefundService
	orderRefundService           qpay.RefundService
	reverseService               qpay.ReverseService
	voucherService               qpay.VoucherService
	postDeductService            qpay.PaymentService
	preDeductService             qpay.PaymentService
	overallDiscountsService      qpay.OverallDiscountsService
	overallDiscountsV2021Service qpay.OverallDiscountsV2021Service
	userPaymentSeqService        qpay.UserPaymentSeqService
	factory                      *payment.Factory
}

var _ pb.PaymentServiceServer = (*PaymentAction)(nil)

// NewPaymentAction new payment action
func NewPaymentAction(defaultPageSize uint64, factory *payment.Factory) *PaymentAction {
	return &PaymentAction{
		BaseAction:      action.NewBaseAction(defaultPageSize),
		defaultPageSize: defaultPageSize,

		factory:                      factory,
		billPaymentService:           factory.BillPaymentService,
		orderPaymentService:          factory.OrderPaymentService,
		depositService:               factory.DepositService,
		billRefundService:            factory.BillRefundService,
		orderRefundService:           factory.OrderRefundService,
		voucherService:               factory.VoucherService,
		reverseService:               factory.ReverseService,
		genericService:               factory.GenericService,
		postDeductService:            factory.PostDeductService,
		preDeductService:             factory.PreDeductService,
		overallDiscountsService:      factory.OverallDiscountsService,
		overallDiscountsV2021Service: factory.OverallDiscountsV2021Service,
		userPaymentSeqService:        factory.UserPaymentSeqService,
	}
}
