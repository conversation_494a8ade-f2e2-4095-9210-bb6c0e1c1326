package service

import (
	"context"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
	po "github.com/qbox/pay-sdk/v3/priceshim/client/operations"
	pm "github.com/qbox/pay-sdk/v3/priceshim/models"

	"qiniu.io/pay/walletd/model"
)

// GetCouponDiscountByID 通过ID获取折扣优惠券
func (s *WalletBizService) GetCouponDiscountByID(
	ctx context.Context,
	id uint64,
) (*model.CouponDiscount, error) {
	return s.walletDao.CouponDiscount.GetByID(id, s.cacheExpires)
}

// GetCouponDiscountByCode 通过 code 获取折扣优惠券
func (s *WalletBizService) GetCouponDiscountByCode(
	ctx context.Context,
	code string,
) (*model.CouponDiscount, error) {
	return s.walletDao.CouponDiscount.GetByCode(code, s.cacheExpires)
}

// CreateCouponDiscount 创建折扣优惠券
func (s *WalletBizService) CreateCouponDiscount(
	ctx context.Context,
	discount *model.CouponDiscount,
) (*model.CouponDiscount, error) {
	discount.ID = 0
	err := s.walletDao.CouponDiscount.Save(discount, s.cacheExpires)
	return discount, err
}

type ZoneInfo struct {
	ZoneID   uint64
	ZoneCode int64
}

type CouponDiscountWithScope struct {
	CouponDiscount *model.CouponDiscount
	Scope          *ScopeDetail
	Zones          []*ZoneInfo
}

// GetScope implements CouponWithScope interface(GetScope)
func (c *CouponDiscountWithScope) GetScope() *ScopeDetail {
	return c.Scope
}

// GetZones implements CouponWithScope interface(GetZones)
func (c *CouponDiscountWithScope) GetZones() []*ZoneInfo {
	return c.Zones
}

// GetCouponEffectTime implements CouponWithScope interface(GetCouponEffectTime)
func (c *CouponDiscountWithScope) GetCouponEffectTime() base.HNS {
	return c.CouponDiscount.EffectTime
}

// GetCouponDeadTime implements CouponWithScope interface(GetCouponDeadTime)
func (c *CouponDiscountWithScope) GetCouponDeadTime() base.HNS {
	return c.CouponDiscount.DeadTime
}

// BatchCreateBindCouponDiscount 批量创建、绑定折扣优惠券
func (s *WalletBizService) BatchCreateBindCouponDiscount(
	ctx context.Context,
	uid uint64,
	discountWithScopes []*CouponDiscountWithScope,
) (bindingChanged bool, err error) {
	for _, discountWithScope := range discountWithScopes {
		thisBindingChanged, err := s.createBindCouponDiscount(ctx, uid, discountWithScope)
		if err != nil {
			// 日志在 createBindCouponDiscount 打过了
			return false, err
		}
		bindingChanged = bindingChanged || thisBindingChanged
	}

	return bindingChanged, nil
}

// createBindCouponDiscount 创建、绑定一张折扣优惠券
func (s *WalletBizService) createBindCouponDiscount(
	ctx context.Context,
	uid uint64,
	discountWithScope *CouponDiscountWithScope,
) (bindingChanged bool, err error) {
	log := logging.GetLogger(ctx)

	// 0. 如果传入的 discount 有 code 就检查幂等性
	var needToCreate bool
	var needToBind bool
	inputCouponCode := discountWithScope.CouponDiscount.Code
	if inputCouponCode != "" {
		// 0a. 有没有这个 code 的券?
		couponDiscount, err := s.GetCouponDiscountByCode(ctx, inputCouponCode)
		if err != nil {
			if errors.Cause(err) != dao.ErrRecordNotFound {
				log.Errorf("<WalletBizService.createBindCouponDiscount> check existing discount error: %+v", err)
				return false, err
			}

			// 这个 code 的 discount 没有创建，需要创建，当然也需要绑定
			needToCreate = true
			needToBind = true
		} else {
			// err == nil
			// 能查出来这个 discount
			needToCreate = false

			// 检查是否已经绑定到给定用户了
			// XXX 这里没有处理涉及多个 zone 的情况下，部分绑定成功，部分失败的情况
			isBound, err := s.isCouponBoundToUserByCouponID(ctx, couponDiscount.ID, model.CouponTypeDiscount, uid)
			if err != nil {
				log.Errorf("<WalletBizService.createBindCouponDiscount> check discount bound status error: %+v", err)
				return false, err
			}

			needToBind = !isBound
		}
	} else {
		// 无条件创建一个新的
		needToCreate = true
		needToBind = true

		// 替调用方生成一个 code
		discountWithScope.CouponDiscount.Code = s.genCouponCode(uid, discountWithScope.CouponDiscount.Type)
	}

	log.WithFields(logrus.Fields{
		"inputCouponCode": inputCouponCode,
		"couponCodeToUse": discountWithScope.CouponDiscount.Code,
		"uid":             uid,
		"needToCreate":    needToCreate,
		"needToBind":      needToBind,
	}).Info("<WalletBizService.createBindCouponDiscount> idempotence check info")

	if needToCreate {
		// 1. 创建 scope
		scope := &model.Scope{
			IsAll:  discountWithScope.Scope.IsAll,
			Remark: discountWithScope.CouponDiscount.Remark,
		}
		scopeRet, err1 := s.CreateScope(ctx, scope)
		if err1 != nil {
			log.Errorf("<WalletBizService.createBindCouponDiscount> CreateScope error: %+v", err1)
			return false, err1
		}

		// 2. 创建 scopeDetail
		scopeDetail := discountWithScope.Scope
		scopeDetail.ScopeID = scopeRet.ID
		scopeDetailRet, err1 := s.CreateScopeDetail(ctx, scopeDetail)
		if err1 != nil {
			log.Errorf("<WalletBizService.createBindCouponDiscount> CreateScopeDetail error: %+v", err1)
			return false, err1
		}

		// 3. 创建 couponDiscount
		discountWithScope.CouponDiscount.ScopeID = scopeDetailRet.ScopeID
		discountWithScope.CouponDiscount.ID = 0
		err1 = s.walletDao.CouponDiscount.Save(discountWithScope.CouponDiscount, s.cacheExpires)
		if err1 != nil {
			log.Errorf("<WalletBizService.createBindCouponDiscount> CouponDiscount.Save error: %+v", err1)
			return false, err1
		}
	}

	if needToBind {
		// 4. 绑定优惠到用户，遍历区域，每个区域的绑定一遍
		for _, zoneInfo := range discountWithScope.Zones {
			couponUserMapRet, err2 := s.BindCoupon(ctx, &BindCouponParam{
				CouponID:   discountWithScope.CouponDiscount.ID,
				CouponType: model.CouponTypeDiscount,
				UID:        uid,
				ZoneID:     zoneInfo.ZoneID,
				EffectTime: discountWithScope.CouponDiscount.EffectTime,
				DeadTime:   discountWithScope.CouponDiscount.DeadTime,
			})
			if err2 != nil {
				log.Errorf("<WalletBizService.createBindCouponDiscount> BindCoupon error: %+v", err2)
				return false, err2
			}

			// 需要调用 price-shim 写 oid
			err2 = s.saveOidMapRecord(ctx, couponUserMapRet.ID, "discount_op")
			if err2 != nil {
				log.Errorf("<WalletBizService.createBindCouponDiscount> saveOidMapRecord error: %+v", err2)
				return false, err2
			}

			bindingChanged = true
		}
	}

	return bindingChanged, nil
}

func (s *WalletBizService) saveOidMapRecord(
	ctx context.Context,
	couponUserMapID uint64,
	objType string,
) (err error) {
	op := primitive.NewObjectID().Hex()

	req := &pm.ReqTypeIDOIDKey{
		ID:      int64(couponUserMapID),
		Key:     op,
		Oid:     op,
		ObjType: objType,
	}

	params := po.NewSaveOidmapWithOIDKeyParamsWithContext(ctx)
	params.SetReqTypeIDOIDKey(req)
	_, err = s.priceshim.Operations.SaveOidmapWithOIDKey(params)
	if err != nil {
		return errors.Trace(err)
	}

	return
}

// UpdateCouponDiscountByID 按照 ID 更新折扣优惠券
func (s *WalletBizService) UpdateCouponDiscountByID(
	ctx context.Context,
	id uint64,
	discount *model.CouponDiscount,
) (*model.CouponDiscount, error) {
	discount.ID = id
	err := s.walletDao.CouponDiscount.Save(discount, s.cacheExpires)
	return discount, err
}

// UpdateCouponDiscountByCode 按照 code 更新折扣优惠券
func (s *WalletBizService) UpdateCouponDiscountByCode(
	ctx context.Context,
	code string,
	discount *model.CouponDiscount,
) (*model.CouponDiscount, error) {
	discount.Code = code
	err := s.walletDao.CouponDiscount.Save(discount, s.cacheExpires)
	return discount, err
}

// DeleteCouponDiscountByID 按照 ID 删除折扣优惠券
func (s *WalletBizService) DeleteCouponDiscountByID(
	ctx context.Context,
	id uint64,
) (*model.CouponDiscount, error) {
	discount, err := s.GetCouponDiscountByID(ctx, id)
	if err != nil {
		return nil, errors.Annotate(err, "query coupon discount failed").WithField("id", id)
	}
	err = s.walletDao.CouponDiscount.Delete(discount)
	return discount, err
}

// DeleteCouponDiscountByCode 按照 code 删除折扣优惠券
func (s *WalletBizService) DeleteCouponDiscountByCode(
	ctx context.Context,
	code string,
) (*model.CouponDiscount, error) {
	discount, err := s.GetCouponDiscountByCode(ctx, code)
	if err != nil {
		return nil, errors.Annotate(err, "query coupon discount failed").
			WithField("code", code)
	}
	err = s.walletDao.CouponDiscount.Delete(discount)
	return discount, err
}

// ListCouponDiscountsByConds 按条件列举折扣优惠券
func (s *WalletBizService) ListCouponDiscountsByConds(
	ctx context.Context,
	codePattern string,
	offset int,
	limit int,
) ([]model.CouponDiscount, error) {
	return s.walletDao.CouponDiscount.ListByConds(&model.ListCouponConds{
		CodePattern: codePattern,
		Offset:      offset,
		Limit:       limit,
	}, s.cacheExpires)
}

// CountAllCouponDiscounts 获取所有折扣优惠券数量
func (s *WalletBizService) CountAllCouponDiscounts(
	ctx context.Context,
) (uint64, error) {
	return s.walletDao.CouponDiscount.CountAll(s.cacheExpires)
}

// ListCouponDiscountsByScopeID 通过计费范围列举折扣优惠券
func (s *WalletBizService) ListCouponDiscountsByScopeID(
	ctx context.Context,
	scopeID uint64,
	offset int,
	limit int,
) ([]model.CouponDiscount, error) {
	return s.walletDao.CouponDiscount.ListByScopeID(scopeID, offset, limit, s.cacheExpires)
}

// CountCouponDiscountsByScopeID 通过计费范围获取折扣优惠券数量
func (s *WalletBizService) CountCouponDiscountsByScopeID(
	ctx context.Context,
	scopeID uint64,
) (uint64, error) {
	return s.walletDao.CouponDiscount.CountByScopeID(scopeID, s.cacheExpires)
}

// ListCouponDiscountsByUID 通过用户ID列举折扣优惠券
func (s *WalletBizService) ListCouponDiscountsByUID(
	ctx context.Context,
	uid uint64,
	offset int,
	limit int,
) ([]model.CouponDiscount, error) {
	cs, err := s.walletDao.CouponUserMap.ListByUID(uid,
		model.CouponTypeDiscount, offset, limit, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "list coupon discount failed").WithField("uid", uid)
	}

	ids := make([]uint64, len(cs))
	for i, c := range cs {
		ids[i] = c.CouponID
	}

	return s.walletDao.CouponDiscount.ListByIDs(ids, s.cacheExpires)
}

// CountCouponDiscountsByUID 通过用户ID获取折扣优惠券数量
func (s *WalletBizService) CountCouponDiscountsByUID(
	ctx context.Context,
	uid uint64,
) (uint64, error) {
	return s.walletDao.CouponUserMap.CountByUID(uid, model.CouponTypeDiscount, s.cacheExpires)
}
