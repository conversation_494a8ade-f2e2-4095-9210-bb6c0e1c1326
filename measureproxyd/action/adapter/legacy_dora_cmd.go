package adapter

import (
	"github.com/qbox/bo-base/v4/legacystat/common"
	pb "github.com/qbox/pay-sdk/measureproxy"
)

// BuildPbLegacyDoraCmd converts from common.LegacyDoraCmd to the protobuf type.
func BuildPbLegacyDoraCmd(x *common.LegacyDoraCmd) *pb.LegacyDoraCmd {
	return &pb.LegacyDoraCmd{
		Key: x.Key,
		Cmd: x.Cmd,
	}
}

// BuildModelLegacyDoraCmd converts from pb.LegacyDoraCmd to the model layer type.
func BuildModelLegacyDoraCmd(x *pb.LegacyDoraCmd) *common.LegacyDoraCmd {
	return &common.LegacyDoraCmd{
		Key: x.Key,
		Cmd: x.Cmd,
	}
}
