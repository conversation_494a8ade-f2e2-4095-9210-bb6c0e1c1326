// Code generated by MockGen. DO NOT EDIT.
// Source: measured/service/stat/client.go

package stat

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	model "qiniu.io/pay/measured/model"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// GetItemsInUse mocks base method.
func (m *MockClient) GetItemsInUse(ctx context.Context, params *model.GetItemsInUseParams) (model.ItemsInUseResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetItemsInUse", ctx, params)
	ret0, _ := ret[0].(model.ItemsInUseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItemsInUse indicates an expected call of GetItemsInUse.
func (mr *MockClientMockRecorder) GetItemsInUse(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemsInUse", reflect.TypeOf((*MockClient)(nil).GetItemsInUse), ctx, params)
}

// GetStat mocks base method.
func (m *MockClient) GetStat(ctx context.Context, params *model.StatReq) (*model.StatResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStat", ctx, params)
	ret0, _ := ret[0].(*model.StatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStat indicates an expected call of GetStat.
func (mr *MockClientMockRecorder) GetStat(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStat", reflect.TypeOf((*MockClient)(nil).GetStat), ctx, params)
}

// GetUserResourceTags mocks base method.
func (m *MockClient) GetUserResourceTags(ctx context.Context, uid uint64) (*model.UserResourceTags, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserResourceTags", ctx, uid)
	ret0, _ := ret[0].(*model.UserResourceTags)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserResourceTags indicates an expected call of GetUserResourceTags.
func (mr *MockClientMockRecorder) GetUserResourceTags(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserResourceTags", reflect.TypeOf((*MockClient)(nil).GetUserResourceTags), ctx, uid)
}

// GetUsers mocks base method.
func (m *MockClient) GetUsers(ctx context.Context, params *model.GetUsersParams) (model.UsersResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsers", ctx, params)
	ret0, _ := ret[0].(model.UsersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsers indicates an expected call of GetUsers.
func (mr *MockClientMockRecorder) GetUsers(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsers", reflect.TypeOf((*MockClient)(nil).GetUsers), ctx, params)
}

// SyncCustomBills mocks base method.
func (m *MockClient) SyncCustomBills(ctx context.Context, params *model.SyncCustomBillParams) (any, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncCustomBills", ctx, params)
	ret0, _ := ret[0].(any)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncCustomBills indicates an expected call of SyncCustomBills.
func (mr *MockClientMockRecorder) SyncCustomBills(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncCustomBills", reflect.TypeOf((*MockClient)(nil).SyncCustomBills), ctx, params)
}
