package action_test

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/i18n/intlmockhelper"
	"qiniu.io/pay/walletd/action"
	"qiniu.io/pay/walletd/service"
)

func TestWalletAction_NotifyBalanceInsufficiency(t *testing.T) {
	test.RunWithUTCAndCST(t, testWalletAction_NotifyBalanceInsufficiency)
}

func testWalletAction_NotifyBalanceInsufficiency(t *testing.T, loc *time.Location) {
	mockCtrl := gomock.NewController(t)

	ctx := tz.WithRefLocation(context.Background(), loc)
	balanceInsufficiencySrv := service.NewMockIBalanceInsufficiencyService(mockCtrl)
	expectedTotal := 333
	expectedFailureCount := 0
	now := time.Now().In(loc)
	balanceInsufficiencySrv.EXPECT().
		NotifyBalanceInsufficiency(ctx, base.Today(now)).
		Return(expectedTotal, expectedFailureCount, nil)

	l10nProvider := intlmockhelper.NewCommonMockL10nProvider(t)
	walletAction := action.NewWalletAction(nil, l10nProvider, balanceInsufficiencySrv, nil, nil, nil, 10)

	pbNotifyBalanceInsufficiency, err := walletAction.NotifyBalanceInsufficiency(ctx, &empty.Empty{})
	assert.NoError(t, err)
	assert.Equal(t, pbNotifyBalanceInsufficiency.Total, uint64(expectedTotal))
	assert.Equal(t, pbNotifyBalanceInsufficiency.FailureCount, uint64(expectedFailureCount))
}
