package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"
	"qiniu.io/pay/priced/action/adapter"
)

// SwitchUserPriceAlgorithmByID 切换用户公开价计费方式
func (a *PriceAction) SwitchUserPriceAlgorithmByID(
	ctx context.Context,
	param *pb.SwitchUserPriceAlgorithmParamByID,
) (*empty.Empty, error) {
	switchParam := adapter.BuildSwitchPriceUserAlgorithmByID(param)

	err := a.priceBizSrv.SwitchUserPriceAlgorithmByID(ctx, switchParam)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &empty.Empty{}, nil
}

// SwitchUserPriceAlgorithmByCode 切换用户公开价计费方式
func (a *PriceAction) SwitchUserPriceAlgorithmByCode(
	ctx context.Context,
	param *pb.SwitchUserPriceAlgorithmParamByCode,
) (*empty.Empty, error) {
	switchParam := adapter.BuildSwitchPriceUserAlgorithmByCode(param)

	err := a.priceBizSrv.SwitchUserPriceAlgorithmByCode(ctx, switchParam)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &empty.Empty{}, nil
}

// GetUserPriceAlgorithm 获取用户当前公开价计费方式
func (a *PriceAction) GetUserPriceAlgorithm(
	ctx context.Context,
	param *pb.GetUserPriceAlgorithmParam,
) (*pb.GetUserPriceAlgorithmResp, error) {
	userAlgorithms, err := a.priceBizSrv.GetUserPriceAlgorithm(ctx,
		param.GetUid(),
		param.GetProduct(),
		param.GetZoneCode())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbGetUserPriceAlgorithmResp(userAlgorithms)
}
