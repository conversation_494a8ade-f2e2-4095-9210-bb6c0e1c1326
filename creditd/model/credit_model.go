package model

import (
	"fmt"
	"time"

	"github.com/nicksnyder/go-i18n/v2/i18n"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl"

	"qiniu.io/pay/creditd/model/enums"
)

// ManualCredit model definition
type ManualCredit struct {
	ID           uint64             `gorm:"primary_key"`
	UID          uint64             // 用户账号
	CustomerName string             `gorm:"type:varchar(128)"` // 用户名称选填
	CreditLine   int64              // 信用额度
	CreditPeriod uint64             // 账期
	PeriodUnit   enums.PeriodUnit   // 账期基本单位(天、日)
	Status       enums.CreditStatus // 信用额度状态
	Remark       string             `gorm:"type:varchar(512)"`
	CurrencyType string             `gorm:"type:varchar(10);not null;default:'CNY'"`
	InitAt       time.Time          `sql:"type:DATETIME(6);not null;default:'1000-01-01 00:00:00'"` // 新增授信时间
	CancelAt     time.Time          `sql:"type:DATETIME(6);not null;default:'1000-01-01 00:00:00'"` // 撤销授信时间
	ActivateAt   time.Time          `sql:"type:DATETIME(6);not null;default:'1000-01-01 00:00:00'"` // 激活授信时间
	RevokeAt     time.Time          `sql:"type:DATETIME(6);not null;default:'1000-01-01 00:00:00'"` // 停用授信时间

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// TableName 兼容之前的 credits 表数据
func (ManualCredit) TableName() string {
	return "credits"
}

// SystemCredit 系统授信
type SystemCredit struct {
	ID           uint64             `gorm:"primary_key"`
	UID          uint64             `gorm:"type:int;not null"`
	CustomerName string             `gorm:"type:varchar(128)"` // 用户名称选填
	CreditLine   int64              `gorm:"type:int;not null;default:0"`
	CreditPeriod uint64             `gorm:"type:int;not null"`
	PeriodUnit   enums.PeriodUnit   `gorm:"type:int;not null"`
	CurrencyType string             `sql:"type:varchar(10);not null;default:'USD'"`
	AssignReason enums.AssignReason `gorm:"type:int;not null"`
	Month        time.Time          `sql:"type:DATETIME(6)"`
	CreatedAt    time.Time          `sql:"type:DATETIME(6)"`
	UpdatedAt    time.Time          `sql:"type:DATETIME(6)"`
	IsDeleted    bool
}

func (SystemCredit) TableName() string {
	return "system_credits"
}

// SystemCreditHistory 系统授信更新记录
type SystemCreditHistory struct {
	ID           uint64             `gorm:"primary_key"`
	UID          uint64             `gorm:"type:int;not null"`
	CustomerName string             `gorm:"type:varchar(128)"` // 用户名称选填
	CreditLine   int64              `gorm:"type:int;not null;default:0"`
	CreditPeriod uint64             `gorm:"type:int;not null"`
	PeriodUnit   enums.PeriodUnit   `gorm:"type:int;not null"`
	CurrencyType string             `gorm:"type:varchar(10);not null;default:'USD'"`
	AssignReason enums.AssignReason `gorm:"type:int;not null"`
	Remark       string             `gorm:"type:varchar(128)"`
	Month        time.Time          `sql:"type:DATETIME(6)"`
	CreatedAt    time.Time          `sql:"type:DATETIME(6)"`
	IsDeleted    bool
}

func (SystemCreditHistory) TableName() string {
	return "system_credit_histories"
}

func (c SystemCredit) GenerateRemark(localizer intl.Localizer) string {
	switch c.AssignReason {
	case enums.AssignReasonBindFirstCard:
		return localizer.MustLocalizeMessage(&i18n.Message{
			ID:    "首次绑卡",
			Other: "首次绑卡",
		})
	case enums.AssignReasonChangeDefaultCard:
		return localizer.MustLocalizeMessage(&i18n.Message{
			ID:    "更换默认支付方式",
			Other: "更换默认支付方式",
		})
	case enums.AssignReasonChargeCard:
		return localizer.MustLocalizeMessage(&i18n.Message{
			ID:    "消费超授信自动付款",
			Other: "消费超授信自动付款",
		})
	case enums.AssignReasonRevokeManualCredit:
		return localizer.MustLocalizeMessage(&i18n.Message{
			ID:    "人工授信停用",
			Other: "人工授信停用",
		})
	case enums.AssignReasonGenMonthStatement:
		template := localizer.MustLocalizeMessage(&i18n.Message{
			ID:    "%s 出账付款",
			Other: "%s 出账付款",
		})
		return fmt.Sprintf(template, base.NewMonth(c.Month))
	default:
		return localizer.MustLocalizeMessage(&i18n.Message{
			ID:    "原因未知",
			Other: "原因未知",
		})
	}
}
