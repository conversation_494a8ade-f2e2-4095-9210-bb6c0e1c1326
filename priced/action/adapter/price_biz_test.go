package adapter

import (
	"testing"

	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
	"qiniu.io/pay/priced/service"
)

func TestAdapterStairPrice(t *testing.T) {
	expect := &service.StairPrice{
		UnitID:   1,
		Quantity: 10,
		Price:    base.NewNMoneyWithHighAccuracyI64(300),
	}

	m, err := BuildPbStairPrice(expect)
	assert.NoError(t, err)

	actual, err := BuildStairPrice(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterItemPriceParam(t *testing.T) {
	expect := &service.ItemPriceParam{
		ItemID:         1,
		AlgorithmID:    2,
		StairPriceType: "test",
		IsDisabled:     false,
		UnitRate:       1024,
		StairPrices: []service.StairPrice{
			{
				UnitID:   1,
				Quantity: 10,
				Price:    base.NewNMoneyWithHighAccuracyI64(300),
			},
			{
				UnitID:   2,
				Quantity: 20,
				Price:    base.NewNMoneyWithHighAccuracyI64(500),
			},
		},
		EffectTime: base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
		DeadTime:   base.NewHNS(time.Date(2018, 2, 6, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbItemPriceParam(expect)
	assert.NoError(t, err)

	actual, err := BuildItemPriceParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterPricesParam(t *testing.T) {
	expect := &service.PricesParam{
		Type:   model.PriceTableTypeFinal,
		Remark: "test remark",
		ItemPrices: []service.ItemPriceParam{
			{
				ItemID:         1,
				AlgorithmID:    2,
				StairPriceType: "test",
				IsDisabled:     false,
				UnitRate:       1024,
				Type:           model.PriceTableTypeFinal,
				StairPrices: []service.StairPrice{
					{
						UnitID:   1,
						Quantity: 10,
						Price:    base.NewNMoneyWithHighAccuracyI64(300),
					},
					{
						UnitID:   2,
						Quantity: 20,
						Price:    base.NewNMoneyWithHighAccuracyI64(500),
					},
				},
				EffectTime: base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
				DeadTime:   base.NewHNS(time.Date(2018, 2, 6, 0, 0, 0, 0, time.UTC)),
			},
			{
				ItemID:         2,
				AlgorithmID:    3,
				StairPriceType: "test",
				IsDisabled:     false,
				UnitRate:       1000,
				Type:           model.PriceTableTypeFinal,
				StairPrices: []service.StairPrice{
					{
						UnitID:   1,
						Quantity: 10,
						Price:    base.NewNMoneyWithHighAccuracyI64(300),
					},
					{
						UnitID:   2,
						Quantity: 20,
						Price:    base.NewNMoneyWithHighAccuracyI64(500),
					},
				},
				EffectTime: base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
				DeadTime:   base.NewHNS(time.Date(2018, 2, 6, 0, 0, 0, 0, time.UTC)),
			},
		},
	}

	m, err := BuildPbPricesParam(expect)
	assert.NoError(t, err)

	actual, err := BuildPricesParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterUserPriceParam(t *testing.T) {
	expect := &service.UserPriceTableParam{
		UID: 1,
		Prices: service.PricesParam{
			Type:   model.PriceTableTypeFinal,
			Remark: "test remark",
			ItemPrices: []service.ItemPriceParam{
				{
					ItemID:         1,
					AlgorithmID:    2,
					StairPriceType: "test",
					IsDisabled:     false,
					UnitRate:       1024,
					Type:           model.PriceTableTypeFinal,
					StairPrices: []service.StairPrice{
						{
							UnitID:   1,
							Quantity: 10,
							Price:    base.NewNMoneyWithHighAccuracyI64(300),
						},
						{
							UnitID:   2,
							Quantity: 20,
							Price:    base.NewNMoneyWithHighAccuracyI64(500),
						},
					},
					EffectTime: base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2018, 2, 6, 0, 0, 0, 0, time.UTC)),
				},
				{
					ItemID:         2,
					AlgorithmID:    3,
					StairPriceType: "test",
					IsDisabled:     false,
					UnitRate:       1000,
					Type:           model.PriceTableTypeFinal,
					StairPrices: []service.StairPrice{
						{
							UnitID:   1,
							Quantity: 10,
							Price:    base.NewNMoneyWithHighAccuracyI64(300),
						},
						{
							UnitID:   2,
							Quantity: 20,
							Price:    base.NewNMoneyWithHighAccuracyI64(500),
						},
					},
					EffectTime: base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
					DeadTime:   base.NewHNS(time.Date(2018, 2, 6, 0, 0, 0, 0, time.UTC)),
				},
			},
		},
	}

	m, err := BuildPbUserPriceParam(expect)
	assert.NoError(t, err)

	actual, err := BuildUserPricesParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterItemPriceWithTimePointParam(t *testing.T) {
	expect := &service.ItemPriceWithTimePointParam{
		UID:    1,
		ItemID: 2,
		ZoneID: 3,
		At:     base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbItemPriceWithTimePointParam(expect)
	assert.NoError(t, err)

	actual, err := BuildItemPriceWithTimePointParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterItemPriceWithTimeRangeParam(t *testing.T) {
	expect := &service.ItemPriceWithTimeRangeParam{
		UID:       1,
		ItemID:    2,
		ZoneID:    3,
		StartTime: base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
		EndTime:   base.NewHNS(time.Date(2018, 2, 5, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbItemPriceWithTimeRangeParam(expect)
	assert.NoError(t, err)

	actual, err := BuildItemPriceWithTimeRangeParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterBatchItemPricesWithTimeRangeParam(t *testing.T) {
	expect := &service.BatchItemPricesWithTimeRangeParam{
		UID:       1,
		ItemIDs:   []uint64{2, 3, 4},
		ZoneID:    3,
		StartTime: base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
		EndTime:   base.NewHNS(time.Date(2018, 2, 5, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbBatchItemPricesWithTimeRangeParam(expect)
	assert.NoError(t, err)

	actual, err := BuildBatchItemPricesWithTimeRangeParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterUserPriceWithTimePointParam(t *testing.T) {
	expect := &service.UserPriceWithTimePointParam{
		UID:    1,
		ZoneID: 3,
		At:     base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbUserPriceWithTimePointParam(expect)
	assert.NoError(t, err)

	actual, err := BuildUserPriceWithTimePointParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterUserPriceWithTimePointResp(t *testing.T) {
	expect := &service.UserPriceWithTimePointResp{
		ItemPrices: map[uint64]model.PriceItem{
			1: {
				ID:             1,
				PriceID:        2,
				ItemID:         3,
				AlgorithmID:    4,
				StairPriceType: "test",
				IsDisabled:     true,
				UnitRate:       100,
				Type:           model.PriceTableTypeVIP,
				EffectTime:     base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
				DeadTime:       base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
			},
			2: {
				ID:             2,
				PriceID:        3,
				ItemID:         4,
				AlgorithmID:    5,
				StairPriceType: "test 2",
				IsDisabled:     false,
				UnitRate:       1024,
				Type:           model.PriceTableTypeDefault,
				EffectTime:     base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
				DeadTime:       base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
			},
		},
	}

	m, err := BuildPbUserPriceWithTimePointResp(expect)
	assert.NoError(t, err)

	actual, err := BuildUserPriceWithTimePointResp(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterUserPriceWithTimeRangeParam(t *testing.T) {
	expect := &service.UserPriceWithTimeRangeParam{
		UID:       1,
		ZoneID:    3,
		StartTime: base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
		EndTime:   base.NewHNS(time.Date(2018, 2, 5, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbUserPriceWithTimeRangeParam(expect)
	assert.NoError(t, err)

	actual, err := BuildUserPriceWithTimeRangeParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterUserPriceWithTimeRangeResp(t *testing.T) {
	expect := &service.UserPriceWithTimeRangeResp{
		ItemPrices: map[uint64][]model.PriceItem{
			1: {
				{
					ID:             1,
					PriceID:        2,
					ItemID:         3,
					AlgorithmID:    4,
					StairPriceType: "test",
					IsDisabled:     true,
					UnitRate:       100,
					Type:           model.PriceTableTypeVIP,
					EffectTime:     base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
					DeadTime:       base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:             4,
					PriceID:        6,
					ItemID:         3,
					AlgorithmID:    4,
					StairPriceType: "test",
					IsDisabled:     true,
					UnitRate:       100,
					Type:           model.PriceTableTypeFinal,
					EffectTime:     base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
					DeadTime:       base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
				},
			},
			2: {
				{
					ID:             2,
					PriceID:        3,
					ItemID:         4,
					AlgorithmID:    5,
					StairPriceType: "test 2",
					IsDisabled:     false,
					UnitRate:       1024,
					Type:           model.PriceTableTypeDefault,
					EffectTime:     base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
					DeadTime:       base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
				},
			},
		},
	}

	m, err := BuildPbUserPriceWithTimeRangeResp(expect)
	assert.NoError(t, err)

	actual, err := BuildUserPriceWithTimeRangeResp(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterPriceTableLifecycleChangeParam(t *testing.T) {
	expect := &service.PriceTableLifecycleChangeParam{
		UID:          1,
		PriceTableID: 3,
		EffectTime:   base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
		DeadTime:     base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbPriceTableLifecycleChangeParam(expect)
	assert.NoError(t, err)

	actual, err := BuildPriceTableLifecycleChangeParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestUpdateStairPriceParam(t *testing.T) {
	expect := &service.UpdateStairPriceParam{
		Quantity: 10,
		Price:    base.NewNMoneyWithHighAccuracyI64(300),
	}

	m, err := BuildPbUpdateStairPriceParam(expect)
	assert.NoError(t, err)

	actual := BuildUpdateStairPriceParam(m)

	assert.Equal(t, expect, actual)
}

func TestUpdateItemPriceParam(t *testing.T) {
	expect := &service.UpdateItemPriceParam{
		ItemCode: "xxx",
		ZoneCode: 1,
		DataType: "test_data_type",
		StairPrices: []service.UpdateStairPriceParam{
			{
				Quantity: 5,
				Price:    base.NewNMoneyWithHighAccuracyI64(320),
			},
			{
				Quantity: 10,
				Price:    base.NewNMoneyWithHighAccuracyI64(300),
			},
			{
				Quantity: 10,
				Price:    base.NewNMoneyWithHighAccuracyI64(280),
			},
		},
	}

	m, err := BuildPbUpdateItemPriceParam(expect)
	assert.NoError(t, err)

	actual, err := BuildUpdateItemPriceParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}

func TestAdapterQueryPriceWithTimeRangeParam(t *testing.T) {
	expect := &service.QueryPriceWithTimeRangeParam{
		ItemIDs:   []uint64{2, 3, 4},
		ZoneID:    3,
		StartTime: base.NewHNS(time.Date(2018, 2, 3, 0, 0, 0, 0, time.UTC)),
		EndTime:   base.NewHNS(time.Date(2018, 2, 5, 0, 0, 0, 0, time.UTC)),
	}

	m, err := BuildPbQueryPriceWithTimeRangeParam(expect)
	assert.NoError(t, err)

	actual, err := BuildQueryPriceWithTimeRangeParam(m)
	assert.NoError(t, err)

	assert.Equal(t, expect, actual)
}
