package mockStore

import (
	"context"
	"sort"
	"sync"
	"time"

	"github.com/samber/lo"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/dataplaneModel"
	"qiniu.io/pay/kirby/service/dataplane/common"
)

type MockDataStore struct {
	mu sync.RWMutex

	flushTrigger *time.Timer
	lastFlush    time.Time

	// map[uid]*userDataStore
	users map[uint64]*mockUserDataStore
}

const durationBeforeFlush = 30 * time.Second

func NewMockDataStore() *MockDataStore {
	return &MockDataStore{
		mu:           sync.RWMutex{},
		flushTrigger: nil,
		lastFlush:    time.Now(),
		users:        make(map[uint64]*mockUserDataStore),
	}
}

var _ common.IDataStore = (*MockDataStore)(nil)

func (x *MockDataStore) ensureUserStore(uid uint64) *mockUserDataStore {
	x.mu.Lock()
	defer x.mu.Unlock()

	if s, ok := x.users[uid]; ok {
		return s
	}

	newStore := newMockUserDataStore(uid)
	x.users[uid] = newStore
	return newStore
}

func (x *MockDataStore) kickTrigger() {
	x.flushTrigger.Stop()
	x.flushTrigger.Reset(durationBeforeFlush)
}

func (x *MockDataStore) FeedSeries(ctx context.Context, s *dataplaneModel.FlattenedSeries) {
	us := x.ensureUserStore(s.UID)
	us.feedSeries(ctx, s)
	x.kickTrigger()
}

func (x *MockDataStore) PersistAllPendingSeries(ctx context.Context) (int, error) {
	loc := tz.MustLocationFromCtx(ctx)

	x.mu.RLock()
	defer x.mu.RUnlock()

	numSeriesProcessed := 0
	for _, userStore := range x.users {
		numSeriesProcessed += userStore.persistAllPendingSeries()
	}

	// automatically run aggregations after the flush
	if numSeriesProcessed == 0 {
		return 0, nil
	}

	go func() {
		x.mu.Lock()
		defer x.mu.Unlock()

		now := time.Now().In(loc)
		x.runAggregationsLocked(ctx, base.ThisMonth(x.lastFlush), x.lastFlush, now)
		x.lastFlush = now
	}()

	return numSeriesProcessed, nil
}

func (x *MockDataStore) RunAggregations(
	ctx context.Context,
	month time.Time,
	since time.Time,
	epoch time.Time,
) error {
	x.mu.Lock()
	defer x.mu.Unlock()
	return x.runAggregationsLocked(ctx, month, since, epoch)
}

func (x *MockDataStore) runAggregationsLocked(
	ctx context.Context,
	month time.Time,
	since time.Time,
	epoch time.Time,
) error {
	loc := tz.MustLocationFromCtx(ctx)

	for _, userStore := range x.users {
		err := userStore.runAggregation(month.In(loc), since.In(loc), epoch.In(loc))
		if err != nil {
			return err
		}
	}

	return nil
}

func (x *MockDataStore) ProvisionShards(ctx context.Context, month time.Time) error {
	// shard creation is lightweight enough to not need this
	return nil
}

func (x *MockDataStore) NotifyEngineStart() {
	x.flushTrigger = time.NewTimer(durationBeforeFlush)
	// don't schedule aggregation immediately (kickTrigger is responsible for that)
	x.flushTrigger.Stop()
}

func (x *MockDataStore) NotifyEngineStop() {
	x.flushTrigger.Stop()
}

func (x *MockDataStore) EventChan() <-chan time.Time {
	return x.flushTrigger.C
}

func timeInLocOrZero(t time.Time, loc *time.Location) time.Time {
	return timeInLocOr(t, loc, time.Time{})
}

func timeInLocOrNow(t time.Time, loc *time.Location) time.Time {
	return timeInLocOr(t, loc, time.Now().In(loc))
}

func timeInLocOr(t time.Time, loc *time.Location, def time.Time) time.Time {
	if t.Unix() == 0 || t.IsZero() {
		return def
	}
	return t.In(loc)
}

func (x *MockDataStore) ListSeries(
	ctx context.Context,
	req *dataplaneModel.ReqListSeries,
) (*dataplaneModel.SeriesDetailList, error) {
	loc := tz.MustLocationFromCtx(ctx)

	x.mu.RLock()
	defer x.mu.RUnlock()

	epoch := timeInLocOrNow(req.Epoch, loc)
	dataTimeFrom := timeInLocOrZero(req.DataTimeFrom, loc)
	dataTimeTo := timeInLocOrZero(req.DataTimeTo, loc)
	ctimeFrom := timeInLocOrZero(req.CtimeFrom, loc)
	ctimeTo := timeInLocOrZero(req.CtimeTo, loc)

	var allResults []*dataplaneModel.SeriesDetail
	totalCount := uint64(0)
	if len(req.UIDs) > 0 {
		for _, uid := range req.UIDs {
			userStore, ok := x.users[uid]
			if !ok {
				continue
			}
			userResults := userStore.listSeries(
				epoch,
				dataTimeFrom,
				dataTimeTo,
				ctimeFrom,
				ctimeTo,
				req.SeriesKeys,
				req.MBUUIDs,
				req.QRNs,
				req.RTDCodes,
				req.MMs,
				req.SPConditions,
			)
			allResults = append(allResults, userResults.Data...)
			totalCount += userResults.Count
		}
	} else {
		for _, userStore := range x.users {
			userResults := userStore.listSeries(
				epoch,
				dataTimeFrom,
				dataTimeTo,
				ctimeFrom,
				ctimeTo,
				req.SeriesKeys,
				req.MBUUIDs,
				req.QRNs,
				req.RTDCodes,
				req.MMs,
				req.SPConditions,
			)
			allResults = append(allResults, userResults.Data...)
			totalCount += userResults.Count
		}
	}

	sort.Slice(allResults, func(i int, j int) bool {
		return seriesDetailLess(allResults[i], allResults[j])
	})

	if req.Page == 0 {
		req.Page = 1
	}
	startOffset := int((req.Page - 1) * req.PageSize)
	endOffset := startOffset + int(req.PageSize)

	var pagedResults []*dataplaneModel.SeriesDetail
	if startOffset < len(allResults) {
		if endOffset > len(allResults) {
			endOffset = len(allResults)
		}
		pagedResults = allResults[startOffset:endOffset]
	}

	return &dataplaneModel.SeriesDetailList{
		Data:  pagedResults,
		Count: totalCount,
	}, nil
}

func seriesDetailLess(
	a *dataplaneModel.SeriesDetail,
	b *dataplaneModel.SeriesDetail,
) bool {
	// 按照目前实现，每条记录的 ctime 都不会再改变，且互不相同，因此只按这个字段排序
	return a.Ctime.Before(b.Ctime)
}

func (x *MockDataStore) ListSeriesDistinctUIDs(
	ctx context.Context,
	req *dataplaneModel.ReqListSeries,
) ([]uint64, error) {
	// NOTE: 后续有性能问题再说
	o, err := x.ListSeries(ctx, req)
	if err != nil {
		return nil, err
	}
	return base.UniqueIntSlice(lo.Map(o.Data, func(x *dataplaneModel.SeriesDetail, _ int) uint64 { return x.UID })), nil
}

func (x *MockDataStore) ListAggregatedSeries(
	ctx context.Context,
	req *dataplaneModel.ReqListAggregatedSeries,
) (*dataplaneModel.AggregatedSeriesDetailList, error) {
	loc := tz.MustLocationFromCtx(ctx)

	x.mu.RLock()
	defer x.mu.RUnlock()

	epoch := timeInLocOrNow(req.Epoch, loc)
	dataTimeFrom := timeInLocOrZero(req.DataTimeFrom, loc)
	dataTimeTo := timeInLocOrZero(req.DataTimeTo, loc)
	ctimeFrom := timeInLocOrZero(req.CtimeFrom, loc)
	ctimeTo := timeInLocOrZero(req.CtimeTo, loc)

	var allResults []*dataplaneModel.AggregatedSeriesDetail
	totalCount := uint64(0)
	if len(req.UIDs) > 0 {
		for _, uid := range req.UIDs {
			userStore, ok := x.users[uid]
			if !ok {
				continue
			}
			userResults := userStore.listAggregatedSeries(
				epoch,
				dataTimeFrom,
				dataTimeTo,
				ctimeFrom,
				ctimeTo,
				req.AggSeriesKeys,
				req.ItemCodes,
			)
			allResults = append(allResults, userResults.Data...)
			totalCount += userResults.Count
		}
	} else {
		for _, userStore := range x.users {
			userResults := userStore.listAggregatedSeries(
				epoch,
				dataTimeFrom,
				dataTimeTo,
				ctimeFrom,
				ctimeTo,
				req.AggSeriesKeys,
				req.ItemCodes,
			)
			allResults = append(allResults, userResults.Data...)
			totalCount += userResults.Count
		}
	}

	sort.Slice(allResults, func(i int, j int) bool {
		return aggrSeriesDetailLess(allResults[i], allResults[j])
	})

	if req.Page == 0 {
		req.Page = 1
	}
	startOffset := int((req.Page - 1) * req.PageSize)
	endOffset := startOffset + int(req.PageSize)

	var pagedResults []*dataplaneModel.AggregatedSeriesDetail
	if startOffset < len(allResults) {
		if endOffset > len(allResults) {
			endOffset = len(allResults)
		}
		pagedResults = allResults[startOffset:endOffset]
	}

	return &dataplaneModel.AggregatedSeriesDetailList{
		Data:  pagedResults,
		Count: totalCount,
	}, nil
}

func aggrSeriesDetailLess(
	a *dataplaneModel.AggregatedSeriesDetail,
	b *dataplaneModel.AggregatedSeriesDetail,
) bool {
	// 按照目前实现，每条记录的 ctime 都不会再改变，且互不相同，因此只按这个字段排序
	return a.Ctime.Before(b.Ctime)
}

func (x *MockDataStore) ListAggregatedSeriesDistinctUIDs(
	ctx context.Context,
	req *dataplaneModel.ReqListAggregatedSeries,
) ([]uint64, error) {
	// NOTE: 后续有性能问题再说
	o, err := x.ListAggregatedSeries(ctx, req)
	if err != nil {
		return nil, err
	}
	return base.UniqueIntSlice(lo.Map(o.Data, func(x *dataplaneModel.AggregatedSeriesDetail, _ int) uint64 { return x.UID })), nil
}

func (x *MockDataStore) QueryAggregatedSeriesData(
	ctx context.Context,
	uid uint64,
	month time.Time,
	itemCode string,
	zoneCode int64,
	// dataType string, // TODO
	desiredG adminModel.Granularity,
	epoch time.Time,
) ([]*pb.StatPoint, error) {
	loc := tz.MustLocationFromCtx(ctx)

	x.mu.RLock()
	defer x.mu.RUnlock()

	userStore := x.users[uid]
	if userStore == nil {
		return nil, nil
	}

	monthUnix := base.ThisMonth(month.In(loc)).Unix()
	as := userStore.queryAggregatedSeriesData(epoch, monthUnix, itemAndZone{i: itemCode, z: zoneCode})
	if as == nil {
		return nil, nil
	}

	return common.TransformGranularity(as.g, desiredG, transformStoredPoints(as.points), loc)
}

func (x *MockDataStore) ListItemUIDsByMonthAndZone(
	ctx context.Context,
	itemCodes []string,
	month time.Time,
	zoneCode int64,
	epoch time.Time,
) ([]uint64, error) {
	loc := tz.MustLocationFromCtx(ctx)

	x.mu.RLock()
	defer x.mu.RUnlock()

	monthUnix := base.ThisMonth(month.In(loc)).Unix()

	var result []uint64
	for uid, userStore := range x.users {
		if userStore.hasItemDataForMonth(monthUnix, itemCodes, zoneCode) {
			result = append(result, uid)
		}
	}

	return result, nil
}

func (x *MockDataStore) ListUserItemsByMonthAndZone(
	ctx context.Context,
	uid uint64,
	month time.Time,
	zoneCode int64,
	epoch time.Time,
) ([]string, error) {
	loc := tz.MustLocationFromCtx(ctx)

	x.mu.RLock()
	defer x.mu.RUnlock()

	userStore, ok := x.users[uid]
	if !ok {
		return nil, nil
	}

	monthUnix := base.ThisMonth(month.In(loc)).Unix()
	return userStore.listItemsByMonthAndZone(monthUnix, zoneCode), nil
}
