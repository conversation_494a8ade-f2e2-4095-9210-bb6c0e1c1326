package stat

import (
	"context"
	"fmt"
	"time"

	"github.com/qbox/pay-sdk/dict"
	"github.com/samber/lo"

	"github.com/qbox/bo-base/v4/errors"
)

// productItemCodesReq 某产品线所有计费项 code 列表的查询请求
//
// NOTE: 单独拉出来做一个结构体，主要为了方便实现缓存
type productItemCodesReq struct {
	// Epoch    time.Time // dict 后端似乎没支持，不过好在计费项不会被删除
	ProductCode string
}

// productItemCodesResp 某产品线所有计费项 code 列表的查询结果
//
// NOTE: 单独拉出来做一个结构体，主要为了方便实现缓存
type productItemCodesResp struct {
	ItemCodes []string
}

// ToCacheKey 返回这组参数对应的 cache key
func (r *productItemCodesReq) ToCacheKey() string {
	return fmt.Sprintf(
		"measure:stat:product_item_codes:%s",
		r.ProductCode,
	)
}

// queryProductItemCodes 查询某产品线所有计费项 code 列表
//
// 走缓存，没有就算出来并存入缓存
func (s *measuredStatClient) queryProductItemCodes(
	ctx context.Context,
	params *productItemCodesReq,
) (*productItemCodesResp, error) {
	return makeCacheableQuery(
		s.queryProductItemCodesUncached,
		30*time.Minute,
		s,
		ctx,
		params,
	)
}

// queryProductItemCodesUncached 查询某产品线所有计费项 code 列表，不走缓存
func (s *measuredStatClient) queryProductItemCodesUncached(
	ctx context.Context,
	params *productItemCodesReq,
) (*productItemCodesResp, error) {
	const pageSize = 1000

	var result []string
	page := uint64(1)
	for {
		items, err := s.dictClient.ListItemsByProductCode(ctx, &dict.CodePagingActiveParam{
			Code:       params.ProductCode,
			Page:       page,
			PageSize:   pageSize,
			OnlyActive: true,
		})
		if err != nil {
			return nil, errors.Trace(err)
		}

		result = append(result, lo.Map(items.Items, func(x *dict.Item, _ int) string { return x.Code })...)

		if len(items.Items) < pageSize {
			break
		}
		page++

	}

	return &productItemCodesResp{
		ItemCodes: result,
	}, nil
}
