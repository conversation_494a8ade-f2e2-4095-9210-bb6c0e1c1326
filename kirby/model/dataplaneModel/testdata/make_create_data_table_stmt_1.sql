CREATE TABLE IF NOT EXISTS `foo` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `series_key` varchar(64) NOT NULL,
  `t0` datetime(6) NOT NULL,
  `p0` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p1` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p2` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p3` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p4` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p5` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p6` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p7` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p8` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p9` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p10` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p11` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p12` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p13` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p14` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p15` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p16` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p17` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p18` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p19` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p20` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p21` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p22` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p23` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p24` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p25` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p26` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p27` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p28` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p29` bigint(20) unsigned NOT NULL DEFAULT 0,
  `p30` bigint(20) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_foo_series_t` (`series_key`, `t0`)
)
  ENGINE=InnoDB
  ROW_FORMAT=COMPRESSED
  KEY_BLOCK_SIZE=8
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci;
