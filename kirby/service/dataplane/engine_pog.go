package dataplane

import (
	"context"
	"time"

	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	"qiniu.io/pay/kirby/model/dataplaneModel"
	"qiniu.io/pay/kirby/service/dataplane/common"
)

const queueSize = 1024

// 100 series x 256 shards
const defaultMaxNumSeriesBeforeFlush = 25600

// pogDataplaneEngine is the dataplane engine entirely written in "plain old Go".
type pogDataplaneEngine struct {
	debug  bool
	ctx    context.Context
	cancel context.CancelFunc
	pipe   chan *dataplaneModel.FlattenedSeries
	finish <-chan struct{}

	// number of series currently remembered in the store
	//
	// used to limit amount of buffering when data come in continuously
	// (thus rendering the "flush when quiet" logic useless)
	//
	// maybe move this into store in the future
	nSeries                 int
	maxNumSeriesBeforeFlush int

	store common.IDataStore
}

func newPogDataplaneEngine(
	parent context.Context,
	store common.IDataStore,
	maxNumSeriesBeforeFlush int,
	debug bool,
) *pogDataplaneEngine {
	if maxNumSeriesBeforeFlush == 0 {
		maxNumSeriesBeforeFlush = defaultMaxNumSeriesBeforeFlush
	}

	ctx, cancel := context.WithCancel(parent)
	return &pogDataplaneEngine{
		debug:                   debug,
		ctx:                     ctx,
		cancel:                  cancel,
		pipe:                    make(chan *dataplaneModel.FlattenedSeries, queueSize),
		finish:                  nil,
		nSeries:                 0,
		maxNumSeriesBeforeFlush: maxNumSeriesBeforeFlush,
		store:                   store,
	}
}

func (e *pogDataplaneEngine) stop() {
	if e == nil || e.finish == nil {
		return
	}

	e.store.NotifyEngineStop()
	close(e.pipe)
	e.cancel()
	<-e.finish
}

func (e *pogDataplaneEngine) bundleProcessCtxCallback(fs *dataplaneModel.FlattenedSeries) error {
	e.pipe <- fs
	return nil
}

func (e *pogDataplaneEngine) persistPendingSeries(l *logrus.Entry, reason string) {
	if e.debug {
		l.WithField("reason", reason).Info("persisting pending series")
	}

	s := time.Now()
	numSeriesProcessed, err := e.store.PersistAllPendingSeries(e.ctx)
	dur := time.Since(s)
	if err != nil {
		l.WithError(err).WithFields(logrus.Fields{
			"reason":   reason,
			"duration": dur,
		}).Error("failed to persist pending series, DATA IS LOST")
	}

	if e.debug || numSeriesProcessed > 0 {
		l.WithFields(logrus.Fields{
			"reason":             reason,
			"numSeriesProcessed": numSeriesProcessed,
			"duration":           dur,
		}).Info("all pending series persisted")
	}
}

func (e *pogDataplaneEngine) run() {
	l := logging.GetLogger(e.ctx).WithField("system", "pogDataplaneEngine")
	l.Info("starting")

	finishCh := make(chan struct{})
	e.finish = finishCh

	e.store.NotifyEngineStart()
	// possibly nil but that's okay: nil channels won't get selected
	eventsCh := e.store.EventChan()

	stop := false
	for !stop {
		select {
		case fs, ok := <-e.pipe:
			if !ok {
				stop = true
				break
			}

			if e.debug {
				l.WithField("fs", fs.ToLogObject()).Info("got one series")
			}
			e.store.FeedSeries(e.ctx, fs)
			e.nSeries++
			if e.nSeries > e.maxNumSeriesBeforeFlush {
				e.persistPendingSeries(l, "bufferFull")
				e.nSeries = 0
			}

		case <-eventsCh:
			e.persistPendingSeries(l, "tick")

		case <-e.ctx.Done():
			stop = true
		}
	}

	l.Info("stopping")
	close(finishCh)
}
