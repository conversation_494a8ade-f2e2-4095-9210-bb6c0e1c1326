package enums

// NoticeType defines notice type
type NoticeType uint32

const (
	// UnknownNoticeType is a unknown type
	UnknownNoticeType = NoticeType(iota) // 0
	// TypeInstant 立即发送类型
	TypeInstant // 1
	// TypeScheduled 延迟发送类型
	TypeScheduled // 2
)

func (s NoticeType) String() string {
	switch s {
	case UnknownNoticeType:
		return "UNKNOWN"
	case TypeInstant:
		return "INSTANT"
	case TypeScheduled:
		return "SCHEDULED"
	}
	return "UNKNOWN"
}
