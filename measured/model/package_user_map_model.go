package model

import (
	stdErrors "errors"
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// PackageUserMap model definition
type PackageUserMap struct {
	ID         uint64 `gorm:"primary_key"`
	UID        uint64 `gorm:"index:idx_package_user_map_uid"`
	PackageID  uint64 `gorm:"index:idx_package_user_map_package_id"`
	Excode     string `gorm:"excode"`
	EffectTime base.HNS
	DeadTime   base.HNS
	CreatedAt  time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt  time.Time `sql:"type:DATETIME(6)"`
}

// PackageUserMapDao is data access object of PackageUserMap model
type PackageUserMapDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *PackageUserMapDao) GetCachePrefix() string {
	return "measure:package_user_map:"
}

// GetCacheRefs get cache refs
func (d *PackageUserMapDao) GetCacheRefs() []dao.CacheLayer {
	return []dao.CacheLayer{
		(*PackageItemDao)(nil),
		(*PackageDao)(nil),
		(*PackageUserMapDao)(nil),
	}
}

// NewPackageUserMapDao is constructor of PackageUserMapDao
func NewPackageUserMapDao(base *dao.BaseDao) *PackageUserMapDao {
	cachePrefix := (*PackageUserMapDao)(nil).GetCachePrefix()
	return &PackageUserMapDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of PackageUserMap by id
func (d *PackageUserMapDao) GetByID(id uint64, expires ...time.Duration) (*PackageUserMap, error) {
	model := &PackageUserMap{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

func (d *PackageUserMapDao) GetExistingBindingInTimeRange(
	uid uint64,
	packageID uint64,
	from base.HNS,
	to base.HNS,
) (*PackageUserMap, error) {
	var obj PackageUserMap
	err := d.base.Execute(func(v any) error {
		return d.base.Model(&PackageUserMap{}).
			Where(
				"`uid` = ? AND `package_id` = ? AND `effect_time` <= ? AND `dead_time` >= ?",
				uid,
				packageID,
				from,
				to,
			).First(v).Error
	}, &obj)
	if err != nil {
		if stdErrors.Is(errors.Cause(err), dao.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, err
	}

	return &obj, nil
}

// ListAll lists all records of PackageUserMap
func (d *PackageUserMapDao) ListAll(
	offset int,
	limit int,
	expires ...time.Duration,
) (data []PackageUserMap, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:offset=%d&limit=%d",
		d.cachePrefix,
		offset,
		limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Offset(offset).Limit(limit).Find(value).Error
	}, keys, &data, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": offset,
			"limit":  limit,
		})
	}
	return
}

// CountAll counts all PackageUserMap records.
func (d *PackageUserMapDao) CountAll(
	expires ...time.Duration,
) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:all:count",
		d.cachePrefix,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PackageUserMap{}).Count(value).Error
	}, keys, &count, expires...)
	return
}

// ListByExcode list records of PackageUserMap by excode
func (d *PackageUserMapDao) ListByExcode(
	uid uint64,
	excode string,
	expires ...time.Duration,
) (data []PackageUserMap, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:uid:%d:excode:%s",
		d.cachePrefix,
		uid,
		excode,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&PackageUserMap{
			UID:    uid,
			Excode: excode,
		}).Find(value).Error
	}, keys, &data, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid":    uid,
			"excode": excode,
		})
	}
	return
}

// ListByUID lists records of PackageUserMap by UID
func (d *PackageUserMapDao) ListByUID(
	uid uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (data []PackageUserMap, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:uid=%d&offset=%d&limit=%d",
		d.cachePrefix,
		uid,
		offset,
		limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&PackageUserMap{UID: uid}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &data, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid":    uid,
			"offset": offset,
			"limit":  limit,
		})
	}
	return
}

// CountByUID counts PackageUserMap records by UID.
func (d *PackageUserMapDao) CountByUID(
	uid uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:count:uid=%d",
		d.cachePrefix,
		uid,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PackageUserMap{}).Where(&PackageUserMap{UID: uid}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"uid": uid,
		})
	}
	return
}

// Save insert or update a record of PackageUserMap by id
func (d *PackageUserMapDao) Save(model *PackageUserMap, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// Delete delete a record of PackageUserMap
func (d *PackageUserMapDao) Delete(model *PackageUserMap) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
}

// DeleteByID delete a record of PackageUserMap by id
func (d *PackageUserMapDao) DeleteByID(id uint64) error {
	model := &PackageUserMap{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}

// DeleteByExcode delete maps by uid and excode
func (d *PackageUserMapDao) DeleteByExcode(
	uid uint64,
	excode string,
) error {
	if uid == 0 || excode == "" {
		return nil
	}
	return d.base.ExecuteWithSyncDelCache(func(a any) error {
		return d.base.
			Delete(&PackageUserMap{}, `uid = ? AND excode = ?`, uid, excode).
			Error
	}, d.keys, &PackageUserMap{}, d)
}
