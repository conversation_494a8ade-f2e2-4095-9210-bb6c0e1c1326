package model

import (
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// maxPredictionDays 最大需要预测是否余额不足的天数
const maxPredictionDays = 7

// minPredictionDays 最小需要预测是否余额不足的天数
const minPredictionDays = 1
const undefinedPredictionDays = 9999999

// BalanceInsufficiencyPrediction model definition
type BalanceInsufficiencyPrediction struct {
	ID                          uint64 `gorm:"primary_key"`
	UID                         uint32
	Email                       string    `gorm:"type:varchar(128)"`
	LeastUnaffordableDays       uint32    `gorm:"least_unaffordable_days"` // 预测该账号余额不能够支付未来几天的消费，最小的天数
	IsMergeAccount              bool      `gorm:"is_merge_account"`        // 是否合账子账号
	ParentID                    uint32    `gorm:"parent_id"`               // 父账号 UID
	ParentEmail                 string    `gorm:"type:varchar(128)"`
	ParentLeastUnaffordableDays uint32    `gorm:"parent_least_unaffordable_days"` // 预测父账号余额不能够支付未来几天父账号自己以及其所有子账号的消费，最小的天数
	DataDate                    time.Time `gorm:"type:datetime(6);data_date"`     // 时间点字段
}

// GetBalanceInsufficiencyUIDsResult model definition
type GetBalanceInsufficiencyUIDsResult struct {
	UID                       uint32 `gorm:"uid"`
	Email                     string `gorm:"email"`
	LeastUnaffordableDays     uint32 `gorm:"least_unaffordable_days"`      // 预测该账号余额不能够支付未来几天的消费，最小的天数
	LastLeastUnaffordableDays uint32 `gorm:"last_least_unaffordable_days"` // 预测昨天该账号余额不能够支付未来几天的消费，最小的天数
}

// BalanceInsufficiencyPredictionDao is data access object of BalanceInsufficiencyPrediction model
type BalanceInsufficiencyPredictionDao struct {
	base *dao.BaseDao
}

// NewBalanceInsufficiencyPredictionDao is constructor of CouponCashDao
func NewBalanceInsufficiencyPredictionDao(base *dao.BaseDao) *BalanceInsufficiencyPredictionDao {
	return &BalanceInsufficiencyPredictionDao{
		base: base,
	}
}

// Save saves BalanceInsufficiencyPrediction(only used by unit test for now)
func (d *BalanceInsufficiencyPredictionDao) Save(prediction *BalanceInsufficiencyPrediction) error {
	if prediction == nil {
		return fmt.Errorf("dao invalid parameter")
	}

	err := d.base.Save(prediction).Error

	if err != nil {
		return errors.Trace(err).WithField("model", prediction)
	}

	return err
}

// GetBalanceInsufficiencyUIDs gets users with insufficient balance in 7/3/1 day(s).
// 获取 7、3、1 天内可能余额不足（需要通知的）用户
func (d *BalanceInsufficiencyPredictionDao) GetBalanceInsufficiencyUIDs(now time.Time) ([]GetBalanceInsufficiencyUIDsResult, error) {
	predictions := make([]GetBalanceInsufficiencyUIDsResult, 0)

	thisDay := base.Today(now)
	prevDay := thisDay.AddDate(0, 0, -1)
	sqlStr := "SELECT t1.uid,t1.email,t1.least_unaffordable_days,t2.least_unaffordable_days as last_least_unaffordable_days FROM " +
		"(SELECT * FROM balance_insufficiency_predictions WHERE data_date = ? AND least_unaffordable_days >= ? AND least_unaffordable_days <= ?) AS t1 " +
		"LEFT JOIN " +
		"( SELECT * FROM balance_insufficiency_predictions WHERE data_date = ? ) AS t2 " +
		"ON " +
		"t1.uid = t2.uid"
	err := d.base.Raw(sqlStr, thisDay, minPredictionDays, maxPredictionDays, prevDay).
		Scan(&predictions).Error
	if err != nil {
		return nil, errors.Trace(err)
	}
	return d.processDays(predictions), nil
}

func (d *BalanceInsufficiencyPredictionDao) processDays(predictions []GetBalanceInsufficiencyUIDsResult) []GetBalanceInsufficiencyUIDsResult {
	alignedPredictions := make([]GetBalanceInsufficiencyUIDsResult, 0)
	for _, prediction := range predictions {
		// assert: 数据校验，确保数据在正确范围内
		if prediction.LeastUnaffordableDays > maxPredictionDays ||
			prediction.LeastUnaffordableDays < minPredictionDays {
			continue
		}

		alignedPrediction := GetBalanceInsufficiencyUIDsResult{
			UID:                       prediction.UID,
			Email:                     prediction.Email,
			LeastUnaffordableDays:     d.alignToGivenDay(prediction.LeastUnaffordableDays),
			LastLeastUnaffordableDays: d.alignToGivenDay(prediction.LastLeastUnaffordableDays),
		}
		alignedPredictions = append(alignedPredictions, alignedPrediction)
	}
	return alignedPredictions
}

// alignToGivenDay aligns days in db to given days(7,3,1)(将从数据库查询得到的最小不能支付天数，对齐到 7、3、1 天)
func (d *BalanceInsufficiencyPredictionDao) alignToGivenDay(days uint32) uint32 {
	// 若最小不能支付天数 > 3 且 <= 7，那么对齐到 7 天
	if days > 3 && days <= 7 {
		return 7
	} else if days > 1 && days <= 3 {
		// 若最小不能支付天数 > 1 且 <= 3，那么对齐到 3 天
		return 3
	} else if days == 1 {
		return 1
	}

	return undefinedPredictionDays
}
