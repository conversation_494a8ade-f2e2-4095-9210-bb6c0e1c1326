package adapter

import (
	"github.com/samber/lo"

	pb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/model/adminModel"
)

func buildMMDDetail(in *pb.MeasureMetricDefinitionDetail) *adminModel.MeasureMetricDefinitionDetail {
	return &adminModel.MeasureMetricDefinitionDetail{
		Code: in.Code,
		Name: in.Name,
		G:    adminModel.Granularity(in.G),
		Unit: adminModel.MeasureUnit(in.Unit),
	}
}

func buildMMDDetails(in []*pb.MeasureMetricDefinitionDetail) []*adminModel.MeasureMetricDefinitionDetail {
	return lo.Map(in, func(x *pb.MeasureMetricDefinitionDetail, _ int) *adminModel.MeasureMetricDefinitionDetail {
		return buildMMDDetail(x)
	})
}

func buildPbMMDDetail(in *adminModel.MeasureMetricDefinitionDetail) *pb.MeasureMetricDefinitionDetail {
	return &pb.MeasureMetricDefinitionDetail{
		Code: in.Code,
		Name: in.Name,
		G:    pb.Granularity(in.G),
		Unit: string(in.Unit),
	}
}

func buildPbMMDDetails(in []*adminModel.MeasureMetricDefinitionDetail) []*pb.MeasureMetricDefinitionDetail {
	return lo.Map(in, func(x *adminModel.MeasureMetricDefinitionDetail, _ int) *pb.MeasureMetricDefinitionDetail {
		return buildPbMMDDetail(x)
	})
}
