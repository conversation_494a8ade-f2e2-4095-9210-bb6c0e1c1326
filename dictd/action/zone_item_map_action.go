package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/dict"
	"qiniu.io/pay/dictd/action/adapter"
)

// GetZoneItemMapByID 通过ID获取机房-计费项关联记录
func (a *DictAction) GetZoneItemMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ZoneItemMap, error) {
	zim, err := a.dictBizSrv.GetZoneItemMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZoneItemMap(zim)
}

// ListZoneItemMapsByZoneID 通过机房ID列举机房-计费项关联记录
func (a *DictAction) ListZoneItemMapsByZoneID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ZoneItemMapList, error) {
	count, err := a.dictBizSrv.CountZoneItemMapsByZoneID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param, a.defaultPageSize)
	zims, err := a.dictBizSrv.ListZoneItemMapsByZoneID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	_zims := make([]*pb.ZoneItemMap, len(zims))
	for i, zim := range zims {
		_zim, err := adapter.BuildPbZoneItemMap(&zim)
		if err != nil {
			return nil, err
		}
		_zims[i] = _zim
	}
	return &pb.ZoneItemMapList{ZoneItemMaps: _zims, Count: count}, nil
}

// ListZoneItemMapsByZoneCode 通过机房 code 列举机房-计费项关联记录
func (a *DictAction) ListZoneItemMapsByZoneCode(
	ctx context.Context,
	param *pb.ZoneCodePagingParam,
) (*pb.ZoneItemMapList, error) {
	count, err := a.dictBizSrv.CountZoneItemMapsByZoneCode(ctx, param.GetZone())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param, a.defaultPageSize)
	zims, err := a.dictBizSrv.ListZoneItemMapsByZoneCode(ctx, param.GetZone(), offset, limit)
	if err != nil {
		return nil, err
	}
	_zims := make([]*pb.ZoneItemMap, len(zims))
	for i, zim := range zims {
		_zim, err := adapter.BuildPbZoneItemMap(&zim)
		if err != nil {
			return nil, err
		}
		_zims[i] = _zim
	}
	return &pb.ZoneItemMapList{ZoneItemMaps: _zims, Count: count}, nil
}

// CountZoneItemMapsByZoneID 通过机房ID获取机房-计费项关联记录条数
func (a *DictAction) CountZoneItemMapsByZoneID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountZoneItemMapsByZoneID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// CountZoneItemMapsByZoneCode 通过机房 code 获取机房-计费项关联记录条数
func (a *DictAction) CountZoneItemMapsByZoneCode(
	ctx context.Context,
	param *pb.ZoneCodeParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountZoneItemMapsByZoneCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListZoneItemMapsByItemID 通过计费项ID列举机房-计费项关联记录
func (a *DictAction) ListZoneItemMapsByItemID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ZoneItemMapList, error) {
	count, err := a.dictBizSrv.CountZoneItemMapsByItemID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param, a.defaultPageSize)
	zims, err := a.dictBizSrv.ListZoneItemMapsByItemID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	_zims := make([]*pb.ZoneItemMap, len(zims))
	for i, zim := range zims {
		_zim, err := adapter.BuildPbZoneItemMap(&zim)
		if err != nil {
			return nil, err
		}
		_zims[i] = _zim
	}
	return &pb.ZoneItemMapList{ZoneItemMaps: _zims, Count: count}, nil
}

// ListZoneItemMapsByItemCode 通过计费项 code 列举机房-计费项关联记录
func (a *DictAction) ListZoneItemMapsByItemCode(
	ctx context.Context,
	param *pb.CodePagingParam,
) (*pb.ZoneItemMapList, error) {
	count, err := a.dictBizSrv.CountZoneItemMapsByItemCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param, a.defaultPageSize)
	zims, err := a.dictBizSrv.ListZoneItemMapsByItemCode(ctx, param.GetCode(), offset, limit)
	if err != nil {
		return nil, err
	}
	_zims := make([]*pb.ZoneItemMap, len(zims))
	for i, zim := range zims {
		_zim, err := adapter.BuildPbZoneItemMap(&zim)
		if err != nil {
			return nil, err
		}
		_zims[i] = _zim
	}
	return &pb.ZoneItemMapList{ZoneItemMaps: _zims, Count: count}, nil
}

// CountZoneItemMapsByItemID 通过计费项ID获取机房-计费项关联记录条数
func (a *DictAction) CountZoneItemMapsByItemID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountZoneItemMapsByItemID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// CountZoneItemMapsByItemCode 通过计费项 code 获取机房-计费项关联记录条数
func (a *DictAction) CountZoneItemMapsByItemCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountZoneItemMapsByItemCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// CreateZoneItemMap 创建机房-计费项关联记录
func (a *DictAction) CreateZoneItemMap(
	ctx context.Context,
	zim *pb.ZoneItemMap,
) (*pb.ZoneItemMap, error) {
	_zim, err := adapter.BuildZoneItemMap(zim)
	if err != nil {
		return nil, err
	}
	_zim, err = a.dictBizSrv.CreateZoneItemMap(ctx, _zim)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZoneItemMap(_zim)
}

// UpdateZoneItemMapByID 通过ID更新机房-计费项关联记录
func (a *DictAction) UpdateZoneItemMapByID(
	ctx context.Context,
	param *pb.IDZoneItemMapParam,
) (*pb.ZoneItemMap, error) {
	zim, err := adapter.BuildZoneItemMap(param.GetZoneItemMap())
	if err != nil {
		return nil, err
	}
	zim, err = a.dictBizSrv.UpdateZoneItemMapByID(ctx, param.GetId(), zim)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZoneItemMap(zim)
}

// DeleteZoneItemMapByID 通过ID删除机房-计费项关联记录
func (a *DictAction) DeleteZoneItemMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ZoneItemMap, error) {
	zim, err := a.dictBizSrv.DeleteZoneItemMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbZoneItemMap(zim)
}
