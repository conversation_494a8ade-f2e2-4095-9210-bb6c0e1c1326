package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
)

// BuildPbCouponType convert model.CouponType to proto.CouponType
func BuildPbCouponType(typ model.CouponType) pb.CouponType {
	return pb.CouponType(pb.CouponType_value[string(typ)])
}

// BuildCouponType convert proto.CouponType to model.CouponType
func BuildCouponType(typ pb.CouponType) model.CouponType {
	return model.CouponType(typ.String())
}

// BuildPbCouponUserMap convert model.CouponUserMap to the protobuf type.
func BuildPbCouponUserMap(x *model.CouponUserMap) (*pb.CouponUserMap, error) {
	return &pb.CouponUserMap{
		Id:            x.ID,
		CouponId:      x.CouponID,
		Uid:           x.UID,
		ZoneId:        x.ZoneID,
		TransactionId: x.TransactionID,
		Type:          BuildPbCouponType(x.Type),
		EffectTime:    timestamppb.New(x.EffectTime.TimeIn(time.UTC)),
		DeadTime:      timestamppb.New(x.DeadTime.TimeIn(time.UTC)),
		CreatedAt:     timestamppb.New(x.CreatedAt),
		UpdatedAt:     timestamppb.New(x.UpdatedAt),
	}, nil
}

// BuildCouponUserMap convert from pb.CouponUserMap to the model layer type.
func BuildCouponUserMap(x *pb.CouponUserMap) (*model.CouponUserMap, error) {
	err := x.GetEffectTime().CheckValid()
	if err != nil {
		return nil, err
	}
	effectTime := x.GetEffectTime().AsTime()

	err = x.GetDeadTime().CheckValid()
	if err != nil {
		return nil, err
	}
	deadTime := x.GetDeadTime().AsTime()

	var createdAt, updatedAt time.Time
	if x.GetCreatedAt() != nil {
		err = x.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = x.GetCreatedAt().AsTime()
	}

	if x.GetUpdatedAt() != nil {
		err = x.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = x.GetUpdatedAt().AsTime()
	}

	return &model.CouponUserMap{
		ID:            x.GetId(),
		CouponID:      x.GetCouponId(),
		UID:           x.GetUid(),
		ZoneID:        x.GetZoneId(),
		TransactionID: x.GetTransactionId(),
		Type:          BuildCouponType(x.GetType()),
		EffectTime:    base.NewHNS(effectTime),
		DeadTime:      base.NewHNS(deadTime),
		CreatedAt:     createdAt,
		UpdatedAt:     updatedAt,
	}, nil
}
