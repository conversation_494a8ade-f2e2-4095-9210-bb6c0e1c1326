package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/crypto"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/lock"

	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	walletdCfg "qiniu.io/pay/walletd/config"
	"qiniu.io/pay/walletd/model"
)

// bankAPIReqPath 银行接口 url path
const bankAPIReqPath = "/web/MBSServlet"

// BankVirtualAccountService is business of BankVirtualAccount service
type BankVirtualAccountService struct {
	userBankVirtualAccountDao *model.UserBankVirtualAccountDao
	config                    *walletdCfg.BankVirtAccConfig
	sha1WithRSA               *crypto.SHA1WithRSA
	redisClient               redis.UniversalClient
	httpClient                *bankHTTPClient
}

// NewBankVirtualAccountService is constructor of BankVirtualAccountService
func NewBankVirtualAccountService(
	dao *model.UserBankVirtualAccountDao,
	config *walletdCfg.BankVirtAccConfig,
	redisClient redis.UniversalClient,
) (*BankVirtualAccountService, error) {
	// 为空则不初始化bank virtual acc srv
	if config.PrivateKey == "" || config.PublicKey == "" {
		return nil, nil
	}

	sha1WithRSA, err := crypto.NewSHA1WithRSA(
		[]byte(config.PrivateKey), []byte(config.PublicKey),
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &BankVirtualAccountService{
		userBankVirtualAccountDao: dao,
		config:                    config,
		sha1WithRSA:               sha1WithRSA,
		redisClient:               redisClient,
		httpClient:                newBankHTTPClient(),
	}, nil
}

// GetUIDByBankVirtualAccount 通过 bankVirtualAccount 获取对应的 uid
func (s *BankVirtualAccountService) GetUIDByBankVirtualAccount(
	ctx context.Context,
	bankVirtAccount string,
) (*model.UserBankVirtualAccount, error) {
	userBankVirtualAccount, err := s.userBankVirtualAccountDao.GetByBankVirtAccount(bankVirtAccount)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return userBankVirtualAccount, nil
}

// BatchGetUIDByBankVirtualAccount 批量通过 bankVirtualAccount 获取对应的 uid
func (s *BankVirtualAccountService) BatchGetUIDByBankVirtualAccount(
	ctx context.Context,
	bankVirtAccounts []string,
) ([]*model.UserBankVirtualAccount, error) {
	userBankVirtualAccounts, err := s.userBankVirtualAccountDao.GetByBankVirtAccounts(bankVirtAccounts)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return userBankVirtualAccounts, nil
}

// GetBankVirtualAccountByUID 通过 uid 获取对应的 bankVirtualAccount
func (s *BankVirtualAccountService) GetBankVirtualAccountByUID(
	ctx context.Context,
	uid uint64,
) (*model.UserBankVirtualAccount, error) {
	userBankVirtualAccount, err := s.userBankVirtualAccountDao.GetByUID(uid)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return userBankVirtualAccount, nil
}

// GenBankVirtualAccountByUID 为 uid 生成对应的 bankVirtualAccount
func (s *BankVirtualAccountService) GenBankVirtualAccountByUID(
	ctx context.Context,
	uid uint64,
) (*model.UserBankVirtualAccount, error) {
	if uid == 0 {
		return nil, errors.New("invalid uid")
	}

	l := logging.GetLogger(ctx)

	// 生成的时候锁用户
	locker := lock.NewRedisLocker(
		s.redisClient,
		lock.WithSpinTimes(600),
		lock.WithSpinInterval(200*time.Millisecond),
	)
	get := locker.LockUser(uid)
	// 保证释放锁
	defer locker.UnlockUser(uid)
	if !get {
		err := errors.Trace(errors.New("SYSTEM ERROR : get lock user timeout"))
		return nil, err
	}

	origUserBankVirtualAccount, err := s.userBankVirtualAccountDao.GetByUID(uid)
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		return nil, errors.Trace(err)
	}
	if origUserBankVirtualAccount != nil && origUserBankVirtualAccount.ID != 0 {
		return origUserBankVirtualAccount, nil
	}

	now := time.Now()
	bankReqPacketBody := GenVirtAccReqBody{
		BusiSerial: fmt.Sprintf("root%s%s", now.Format("********"), genRandomNumber(8)),
		AccName:    s.config.AccName,
		AccCode:    s.config.AccCode,
		OpenNum:    1,
		Reserver1:  "0.00",
		BusiTime:   now.Format("2006-01-02T15:04:05"),
	}

	bankRespMessage, err := doSPDBRequest[
		GenVirtAccReqBody, VirtAccInfoRespBody,
	](s, "dpe.300.016", bankReqPacketBody, l)
	if err != nil {
		return nil, err
	}

	if len(bankRespMessage.Body.Lists) != 1 {
		return nil, errors.New("return lists length != 1")
	}
	userBankVirtualAccount := &model.UserBankVirtualAccount{
		UID:                uid,
		BankVirtualAccount: bankRespMessage.Body.Lists[0].VAcctNo,
	}
	err = s.userBankVirtualAccountDao.Save(userBankVirtualAccount)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return userBankVirtualAccount, nil
}

// doSPDBRequest 浦发 E 企直连接口入参和出参公共处理部分，对入参用 RSA 签名，出参用 RSA 校验
func doSPDBRequest[Req any, Resp any](
	s *BankVirtualAccountService,
	apiCode string,
	req Req,
	l logrus.FieldLogger,
) (*BankRespPacket[Resp], error) {
	now := time.Now()
	bankReqPacket := BankReqPacket[Req]{
		TranCode:    apiCode,
		ActorCode:   s.config.ActorCode,
		SystemCode:  s.config.SystemCode,
		UserCode:    s.config.UserCode,
		UserPass:    s.config.UserPass,
		PacketNo:    s.config.ActorCode + now.Format("********") + genRandomNumber(12),
		VerNo:       s.config.VerNo,
		CreatedDate: now.Format("2006-01-02T15:04:05"),
		Body:        req,
	}

	packetJSONBytes, err := json.Marshal(bankReqPacket)
	if err != nil {
		l.WithFields(logrus.Fields{
			"apiCode":       apiCode,
			"bankReqPacket": bankReqPacket,
		}).WithError(err).Error("json Marshal bankReqPacket failed")
		return nil, errors.Trace(err)
	}

	sig, err := s.sha1WithRSA.Sign(packetJSONBytes)
	if err != nil {
		l.WithFields(logrus.Fields{
			"apiCode":         apiCode,
			"packetJSONBytes": string(packetJSONBytes),
		}).WithError(err).Error("sha1WithRSA failed")
		return nil, errors.Trace(err)
	}

	bankReqMessage := BankReqMessage[BankReqPacket[Req]]{
		Packet:   bankReqPacket,
		CheckSum: sig,
	}
	msgJSONBytes, err := json.Marshal(bankReqMessage)
	if err != nil {
		l.WithFields(logrus.Fields{
			"apiCode":        apiCode,
			"bankReqMessage": bankReqMessage,
		}).WithError(err).Error("json Marshal bankReqMessage failed")
		return nil, errors.Trace(err)
	}

	respBodyStr, err := s.httpClient.send(s.config.ServiceURL+bankAPIReqPath, msgJSONBytes)
	if err != nil {
		l.WithFields(logrus.Fields{
			"apiCode":      apiCode,
			"msgJSONBytes": string(msgJSONBytes),
		}).WithError(err).Error("spdb api send http failed")
		return nil, errors.Trace(err)
	}

	l.WithFields(logrus.Fields{
		"apiCode":     apiCode,
		"respBodyStr": string(respBodyStr),
	}).Info("respBodyStr result")

	var bankRespMessage BankRespMessage[Resp]
	if err := json.Unmarshal(respBodyStr, &bankRespMessage); err != nil {
		l.WithFields(logrus.Fields{
			"apiCode":     apiCode,
			"respBodyStr": string(respBodyStr),
		}).WithError(err).Error("json Unmarshal bankRespMessage failed")
		return nil, errors.Trace(err)
	}

	if len(bankRespMessage.CheckSum) == 0 && bankRespMessage.Packet.ReturnCode == returnCodeFailed {
		// e 企明接口，当 returnCode == returnCodeFailed 的时候，表示失败，无 checkSum 字段
		l.WithFields(logrus.Fields{
			"apiCode":     apiCode,
			"respBodyStr": string(respBodyStr),
		}).WithError(err).Error("bankRespMessage.Packet.ReturnCode(66666) failed")
		return nil, errors.Trace(errors.New(bankRespMessage.Packet.ReturnMess))
	}

	var v VerifyObj
	err = json.Unmarshal(respBodyStr, &v)
	if err != nil {
		l.WithFields(logrus.Fields{
			"apiCode":     apiCode,
			"respBodyStr": string(respBodyStr),
		}).WithError(err).Error("json Unmarshal VerifyObj failed")
		return nil, errors.Trace(err)
	}

	err = s.sha1WithRSA.Verify(v.Packet, v.CheckSum)
	if err != nil {
		l.WithFields(logrus.Fields{
			"apiCode":  apiCode,
			"CheckSum": bankRespMessage.CheckSum,
			"packet":   string(v.Packet),
		}).WithError(err).Error("sha1WithRSA Verify failed")
		return nil, err
	}
	return &bankRespMessage.Packet, nil
}

// VerifyObj E 企直连校验
type VerifyObj struct {
	CheckSum string `json:"checkSum"`
	// packet 直接接收 E 企接口返回的 packet， 因为校验要对返回对字符串做 RSA verify
	// 需要保持返回字符串的顺序，故此处用 json.RawMessage 来接收
	Packet json.RawMessage `json:"packet"`
}

// TransferCurrency 浦发银行支付接口，该接口只接受支付参数，并做一下基本校验。
// 真正的支付结果得调用 dpe.400.100 异步查询
func (s *BankVirtualAccountService) TransferCurrency(
	ctx context.Context, req TransferCurrencyPacketBody,
) (*BankRespPacket[CommonBankRespPacketBody], error) {
	var l = logging.GetLogger(ctx)

	for i := range req.Lists {
		req.Lists[i].PayAccName = s.config.AccName
		req.Lists[i].PayBankCode = s.config.PayBankCode
		req.Lists[i].PayHouseBankName = s.config.PayHouseBankName
		req.Lists[i].PayHouseBankCode = s.config.PayHouseBankCode
	}
	bankRespMessage, err := doSPDBRequest[
		TransferCurrencyPacketBody, CommonBankRespPacketBody,
	](s, "dpe.400.090", req, l)
	if err != nil {
		return nil, err
	}
	return bankRespMessage, nil
}

// QueryBalance 查询银行卡余额
func (s *BankVirtualAccountService) QueryBalance(
	ctx context.Context,
	req QueryBalanceReqBody,
) (*BankRespPacket[QueryBalanceRespBody], error) {
	var l = logging.GetLogger(ctx)

	bankRespMessage, err := doSPDBRequest[QueryBalanceReqBody, QueryBalanceRespBody](s, "dpe.300.040", req, l)
	if err != nil {
		return nil, err
	}
	return bankRespMessage, nil
}

// QueryTransferCurrencyResult 查询支付结果
func (s *BankVirtualAccountService) QueryTransferCurrencyResult(
	ctx context.Context,
	req QueryTransferCurrencyResultReqBody,
) (*BankRespPacket[QueryTransferCurrencyResultRespBody], error) {
	var l = logging.GetLogger(ctx)

	bankRespMessage, err := doSPDBRequest[
		QueryTransferCurrencyResultReqBody, QueryTransferCurrencyResultRespBody,
	](s, "dpe.400.100", req, l)
	if err != nil {
		return nil, err
	}
	return bankRespMessage, nil
}
