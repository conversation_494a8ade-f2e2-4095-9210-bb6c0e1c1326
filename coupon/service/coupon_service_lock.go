package service

import (
	"context"
	"encoding/json"
	"time"

	"github.com/qbox/bo-base/v4/eventbus"

	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/coupon/model"
)

// LockCouponResp 锁券响应
type LockCouponResp struct {
	// OK 是否成功
	OK bool
	// LockKey 锁的唯一标识
	LockKey string
	// Err 错误码
	Err LockCouponError
}

// LockCouponError 锁定/解锁券错误码
type LockCouponError int

const (
	// LockCouponErrorUnknown 未知锁定/解锁券错误
	LockCouponErrorUnknown LockCouponError = LockCouponError(pb.LOCK_COUPON_ERROR_UNKNOWN)
	// LockCouponErrorWrongState 锁券/解锁券错误，券状态错误
	LockCouponErrorWrongState LockCouponError = LockCouponError(pb.LOCK_COUPON_ERROR_WRONG_STATE)
	// LockCouponErrorSomeCouponsNotFound 锁券/解锁券错误，有些传入的券不存在
	LockCouponErrorSomeCouponsNotFound LockCouponError = LockCouponError(pb.LOCK_COUPON_ERROR_SOME_COUPONS_NOT_FOUND)
	// LockCouponErrorLockInsufficientBalance 锁券错误，锁定的余额超过了券的余额
	LockCouponErrorLockInsufficientBalance LockCouponError = LockCouponError(pb.LOCK_COUPON_ERROR_LOCK_INSUFFICIENT_BALANCE)
	// LockCouponErrorUnlockNotLocked 解锁券错误，券没有被锁
	LockCouponErrorUnlockNotLocked LockCouponError = LockCouponError(pb.LOCK_COUPON_ERROR_UNLOCK_NOT_LOCKED)
)

func newLockCouponRespOk(lockKey string) *LockCouponResp {
	return &LockCouponResp{
		OK:      true,
		LockKey: lockKey,
		Err:     LockCouponErrorUnknown,
	}
}

func newLockCouponRespErr(err LockCouponError) *LockCouponResp {
	return &LockCouponResp{
		OK:      false,
		LockKey: "",
		Err:     err,
	}
}

// POLockDetail 券的 po 抵扣细节
type POLockDetail struct {
	// CouponCode 券码
	CouponCode string
	// orderHash 所对应的 order_hash
	OrderHash string
	// PoID 所应用的 po ID
	PoID uint64
	// Money 这个 po 占用的金额
	Money base.Money
}

// ConsumeCouponResp 用券响应
type ConsumeCouponResp struct {
	// OK 用券是否成功
	OK bool
	// ReducedMoney 如果用券成功，用了多少金额
	ReducedMoney base.Money
	// Err 如果用券失败，失败原因
	Err ConsumeCouponError
}

// ConsumeCouponError 用券失败原因
type ConsumeCouponError int

const (
	// ConsumeCouponErrorUnknown 未知用券失败原因
	ConsumeCouponErrorUnknown ConsumeCouponError = ConsumeCouponError(pb.CONSUME_COUPON_ERROR_UNKNOWN)
	// ConsumeCouponErrorLockKeyNotFound 不存在给定的锁定记录
	ConsumeCouponErrorLockKeyNotFound ConsumeCouponError = ConsumeCouponError(pb.CONSUME_COUPON_ERROR_LOCK_KEY_NOT_FOUND)
	// ConsumeCouponErrorWrongState 券状态错误
	ConsumeCouponErrorWrongState ConsumeCouponError = ConsumeCouponError(pb.CONSUME_COUPON_ERROR_WRONG_STATE)
	// ConsumeCouponErrorInsufficientBalance 券的余额不足
	ConsumeCouponErrorInsufficientBalance ConsumeCouponError = ConsumeCouponError(pb.CONSUME_COUPON_ERROR_INSUFFICIENT_BALANCE)
)

func newConsumeCouponRespOk(reducedMoney base.Money) *ConsumeCouponResp {
	return &ConsumeCouponResp{
		OK:           true,
		ReducedMoney: reducedMoney,
		Err:          ConsumeCouponErrorUnknown,
	}
}

func newConsumeCouponRespErr(err ConsumeCouponError) *ConsumeCouponResp {
	return &ConsumeCouponResp{
		OK:           false,
		ReducedMoney: 0,
		Err:          err,
	}
}

// LockCouponsToUnionOrder 锁定券到订单 union_order
func (s *CouponService) LockCouponsToUnionOrder(
	ctx context.Context,
	uid uint64,
	unionOrderHash string,
	poLockDetails []*POLockDetail,
) (*LockCouponResp, error) {

	logger := logging.GetLogger(ctx)

	var coupons []model.Coupon

	// 以 UID 维度，需要串行化
	s.locker.LockUser(uid)
	defer s.locker.UnlockUser(uid)

	// 前置条件判断
	{
		// 获取所有涉及的券码
		uniqueCouponCodes := uniqueCouponCodesFromPOLockDetails(poLockDetails)

		// []couponCode -> []Coupon
		var err error
		coupons, err = s.dao.Coupon.ListByConds(
			&model.ListCouponsByCondsParam{
				CouponCodes: uniqueCouponCodes,
			},
			0,  // offset
			-1, // limit
		)
		if err != nil {
			return nil, errors.Trace(err).
				WithField("couponCodes", uniqueCouponCodes)
		}

		if len(coupons) < len(uniqueCouponCodes) {
			// 有的券不存在
			// TODO: 想办法能返回具体哪张券不存在...
			logger.
				WithField("codes", uniqueCouponCodes).
				WithField("coupons", coupons).
				Error("LockCouponsToOrderV1 coupons not found")
			return newLockCouponRespErr(LockCouponErrorSomeCouponsNotFound), nil
		}

		// 所有涉及的券都应该可使用
		for _, coupon := range coupons {
			if coupon.State != model.CouponStateUsable {
				// TODO: 想办法能返回具体哪张券状态有问题...
				logger.WithField("coupon", coupon).Error("LockCouponsToOrderV1 coupons state abnormal")
				return newLockCouponRespErr(LockCouponErrorWrongState), nil
			}
		}

		// 检查锁定请求的金额是否有问题
		//
		// - 每张券都不应该被透支
		balancesByCouponCode := makeCouponBalancesByCodeMap(coupons)
		simulateCouponBalancesAfterDeductInplace(balancesByCouponCode, poLockDetails)
		for couponCode, balance := range balancesByCouponCode {
			if balance < 0 {
				// 有张券被透支了
				// TODO: 想办法能返回具体哪张券被透支了...
				logger.WithField("couponCode", couponCode).Error("LockCouponsToOrderV1 coupons overdraft")
				return newLockCouponRespErr(LockCouponErrorLockInsufficientBalance), nil
			}
		}
	}

	// 产生一个 lock key
	lockKey := genCouponLockKey()

	// 新建 CouponLockRecord
	err := s.dao.CouponLockRecord.Save(&model.CouponLockRecord{
		UID:            uid,
		LockKey:        lockKey,
		UnionOrderHash: unionOrderHash,
	}, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err).
			WithField("unionOrderHash", unionOrderHash)
	}

	// 拉出刚创建的 CouponLockRecord 的 ID(TODO:未必能够立即拉的出来)
	var lockRecordID uint64
	{
		r, err1 := s.dao.CouponLockRecord.GetByLockKey(lockKey, s.cacheExpires)
		if err1 != nil {
			return nil, errors.Trace(err1).WithField("lockKey", lockKey)
		}
		lockRecordID = r.ID
	}

	// []Coupon -> map[couponCode]*Coupon
	couponsByCouponCode := make(map[string]*model.Coupon, len(coupons))
	for i, c := range coupons {
		// NOTE: 小心不要取 c 的地址
		couponsByCouponCode[c.Code] = &coupons[i]
	}

	// 构造要插入的 CouponLockDetail 对象
	detailToInsert := make([]model.CouponLockDetail, len(poLockDetails))
	for i, poLockDetail := range poLockDetails {
		detailToInsert[i] = model.CouponLockDetail{
			LockRecordID: lockRecordID,
			UID:          uid,
			BatchID:      couponsByCouponCode[poLockDetail.CouponCode].BatchID,
			CouponCode:   couponsByCouponCode[poLockDetail.CouponCode].Code,
			CouponID:     couponsByCouponCode[poLockDetail.CouponCode].ID,
			OrderHash:    poLockDetail.OrderHash,
			PoID:         poLockDetail.PoID,
			Money:        poLockDetail.Money,
		}
	}

	// 批量插入 CouponLockDetail
	err = s.dao.CouponLockDetail.BulkInsert(ctx, detailToInsert)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// []Coupon -> []couponID
	couponIDs := make([]uint64, len(coupons))
	for i, c := range coupons {
		couponIDs[i] = c.ID
	}

	// 翻券的状态
	err = s.dao.Coupon.LockByIDs(couponIDs)
	if err != nil {
		return nil, errors.Trace(err).WithField("couponIDs", couponIDs)
	}

	return newLockCouponRespOk(lockKey), nil
}

func uniqueCouponCodesFromPOLockDetails(poLockDetails []*POLockDetail) []string {
	// []*POLockDetail -> set<couponCode>
	uniqueCouponCodesSet := make(map[string]struct{})
	for _, d := range poLockDetails {
		if d == nil {
			// wtf
			panic("should never happen")
		}
		uniqueCouponCodesSet[d.CouponCode] = struct{}{}
	}

	// set<couponCode> -> []couponCode
	result := make([]string, 0, len(uniqueCouponCodesSet))
	for x := range uniqueCouponCodesSet {
		result = append(result, x)
	}

	return result
}

func makeCouponBalancesByCodeMap(coupons []model.Coupon) map[string]base.Money {
	result := make(map[string]base.Money, len(coupons))
	for _, c := range coupons {
		result[c.Code] = c.Balance
	}
	return result
}

// simulateCouponBalancesAfterDeductInplace 根据 []POLockDetail 模拟抵扣券，原位修改券的余额 map
func simulateCouponBalancesAfterDeductInplace(
	balancesByCouponCode /*&mut*/ map[string]base.Money,
	poLockDetails []*POLockDetail,
) {
	for _, d := range poLockDetails {
		balancesByCouponCode[d.CouponCode] -= d.Money
	}
}

// UnlockCouponByUnionOrderHash 按 union order hash 解锁券
func (s *CouponService) UnlockCouponByUnionOrderHash(
	ctx context.Context,
	unionOrderHash string,
	epoch time.Time,
) (*LockCouponResp, error) {
	// 查出 CouponLockRecord
	lockRecord, err := s.dao.CouponLockRecord.GetByUnionOrderHash(unionOrderHash, s.cacheExpires)
	if err != nil {
		if err == dao.ErrRecordNotFound {
			// 这个 order hash 没被锁
			return newLockCouponRespErr(LockCouponErrorUnlockNotLocked), nil
		}

		return nil, errors.Trace(err).WithField("unionOrderHash", unionOrderHash)
	}

	// 以 UID 维度，需要串行化
	s.locker.LockUser(lockRecord.UID)
	defer s.locker.UnlockUser(lockRecord.UID)

	// 查出所有涉及的 CouponLockDetail
	details, err := s.dao.CouponLockDetail.ListByLockRecordID(lockRecord.ID, 0, -1, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"lockID":         lockRecord.ID,
			"unionOrderHash": unionOrderHash,
		})
	}

	// 删除对应的 CouponLockDetail 记录
	err = s.dao.CouponLockDetail.DeleteByLockRecordID(lockRecord.ID)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"lockID":         lockRecord.ID,
			"unionOrderHash": unionOrderHash,
		})
	}

	// 删除 CouponLockRecord
	err = s.dao.CouponLockRecord.DeleteByID(lockRecord.ID)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"lockID":         lockRecord.ID,
			"unionOrderHash": unionOrderHash,
		})
	}
	// 有可能提前被unlock了
	if len(details) == 0 {
		return newLockCouponRespOk(""), nil
	}

	// []CouponLockDetail -> set<couponID>
	couponIDsSet := make(map[uint64]struct{})
	for _, d := range details {
		couponIDsSet[d.CouponID] = struct{}{}
	}

	// set<couponID> -> []couponID
	couponIDs := make([]uint64, len(couponIDsSet))
	for x := range couponIDsSet {
		couponIDs = append(couponIDs, x)
	}

	// 翻回所有券的状态
	err = s.dao.Coupon.UnlockByIDs(ctx, couponIDs, epoch)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"lockID":         lockRecord.ID,
			"unionOrderHash": unionOrderHash,
			"couponIDs":      couponIDs,
		})
	}

	return newLockCouponRespOk(""), nil
}

// ConsumeCouponByUnionOrderHash 按 union order hash 使用已经锁定的券
func (s *CouponService) ConsumeCouponByUnionOrderHash(
	ctx context.Context,
	unionOrderHash string,
) (*ConsumeCouponResp, error) {
	lockRecord, err := s.dao.CouponLockRecord.GetByUnionOrderHash(unionOrderHash, s.cacheExpires)
	if err != nil {
		if err == dao.ErrRecordNotFound {
			// 没有这个锁定记录
			return newConsumeCouponRespErr(ConsumeCouponErrorLockKeyNotFound), nil
		}

		return nil, errors.Trace(err)
	}

	// 以 UID 维度，需要串行化
	s.locker.LockUser(lockRecord.UID)
	defer s.locker.UnlockUser(lockRecord.UID)

	return s.consumeLockedCoupons(ctx, lockRecord)
}

func (s *CouponService) consumeLockedCoupons(
	ctx context.Context,
	lockRecord *model.CouponLockRecord,
) (*ConsumeCouponResp, error) {
	// CouponLockRecord -> []CouponLockDetail
	details, err := s.dao.CouponLockDetail.ListByLockRecordID(lockRecord.ID, 0, -1, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 按券分组
	// []CouponLockDetail -> map[couponID][]CouponLockDetail
	detailsByCouponID := make(map[uint64][]model.CouponLockDetail)
	for _, d := range details {
		// NOTE: 使用了零值语义；此处 d 不是引用类型，所以 append 安全
		detailsByCouponID[d.CouponID] = append(detailsByCouponID[d.CouponID], d)
	}

	// 这里使用 defer，确保就算抵扣失败也触发消息，尽最大努力触发消息
	// 即此 topic 可能包含未真实消费的 coupon
	// 消费方消费的时候，具体业务需要再做检查
	defer func() {
		if len(detailsByCouponID) == 0 {
			return
		}
		go func() {
			couponIDs := make([]uint64, 0, len(detailsByCouponID))
			for couponID := range detailsByCouponID {
				couponIDs = append(couponIDs, couponID)
			}
			msg := mqTradeCouponConsumedMessage{
				CouponIDs: couponIDs,
			}
			byts, _ := json.Marshal(msg)
			err1 := s.eventbus.Publish(eventbus.TradeCouponConsumedTopic, byts)
			if err1 != nil {
				logging.GetLogger(ctx).WithError(err1).
					WithField("message", string(byts)).
					Error()
			}
		}()
	}()
	// 一张一张券的用
	// TODO: 能并发吗？
	reducedMoney := base.Money(0)
	for couponID, couponLockDetails := range detailsByCouponID {
		oneCouponResp, err1 := s.consumeOneLockedCoupon(ctx, couponID, couponLockDetails)
		if err1 != nil {
			return nil, errors.Trace(err1).
				WithFields(errors.Fields{
					"couponID":    couponID,
					"lockDetails": couponLockDetails,
				})
		}
		if !oneCouponResp.OK {
			// 这张券业务上抵扣失败了
			return oneCouponResp, nil
		}
		reducedMoney += oneCouponResp.ReducedMoney
	}

	// 此时应该所有 CouponLockDetail 都删完了
	// 删掉 CouponLockRecord
	err = s.dao.CouponLockRecord.DeleteByID(lockRecord.ID)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return newConsumeCouponRespOk(reducedMoney), nil
}

func (s *CouponService) consumeOneLockedCoupon(
	ctx context.Context,
	couponID uint64,
	lockDetails []model.CouponLockDetail,
) (*ConsumeCouponResp, error) {
	coupon, err := s.dao.Coupon.GetByID(couponID, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 这张券是否只能用一次?
	var isMultipleUse bool
	{
		couponBatch, err1 := s.dao.CouponBatch.GetByID(coupon.BatchID, s.cacheExpires)
		if err1 != nil {
			return nil, errors.Trace(err1).WithField("batchID", couponID)
		}
		isMultipleUse = couponBatch.IsMultipleUse
	}

	if coupon.State != model.CouponStateLocked {
		// 这张券不是已锁定状态
		// TODO: 想办法传回具体是哪张券
		return newConsumeCouponRespErr(ConsumeCouponErrorWrongState), nil
	}

	// 算一下一共用掉多少钱
	var totalUsedMoney base.Money
	for _, d := range lockDetails {
		totalUsedMoney += d.Money
	}

	// 校验
	if coupon.Balance < totalUsedMoney {
		return newConsumeCouponRespErr(ConsumeCouponErrorInsufficientBalance), nil
	}

	// 扣掉这么多钱
	coupon.Balance -= totalUsedMoney

	// 翻状态
	{
		if !isMultipleUse {
			// 如果这张券只能用一次，无条件翻成已用完
			coupon.State = model.CouponStateExhausted
		} else if coupon.Balance == 0 {
			// 如果用完了，也一次性翻成已用完，省一次 update
			coupon.State = model.CouponStateExhausted
		} else {
			// 暂时翻成可用，可能需要再推动一次状态机，比如券在锁定期间过期了
			//
			// XXX NOTE: 这意味着这种场景是可能出现的：
			//
			// - 锁定一张可使用多次的券到某个东西
			// - 券过期时间到
			// - 发起了支付，券抵扣了
			// - 券马上过期了（实际上是先翻回可用，过了极短的状态机处理时间，就过期）
			coupon.State = model.CouponStateUsable
		}
	}

	// update coupon
	err = s.dao.Coupon.Update(coupon)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 推动状态机
	epoch := time.Now()
	_, err = s.dao.Coupon.ProgressState(ctx, coupon, epoch)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 把 LockDetail 搬进 CouponUsageRecord
	// []POLockDetail -> []model.CouponUsageRecord
	usageRecordsToInsert := make([]model.CouponUsageRecord, len(lockDetails))
	for i, d := range lockDetails {
		usageRecordsToInsert[i] = model.CouponUsageRecord{
			UID:        d.UID,
			BatchID:    d.BatchID,
			CouponCode: d.CouponCode,
			CouponID:   d.CouponID,
			OrderHash:  d.OrderHash,
			PoID:       d.PoID,
			Money:      d.Money,
		}
	}

	// 批量插入 CouponUsageRecord
	err = s.dao.CouponUsageRecord.BulkInsert(ctx, usageRecordsToInsert)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// []LockDetail -> []id
	lockDetailIDs := make([]uint64, len(lockDetails))
	for i, d := range lockDetails {
		lockDetailIDs[i] = d.ID
	}

	// 干掉 LockDetail 记录
	err = s.dao.CouponLockDetail.DeleteByIDs(lockDetailIDs)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return newConsumeCouponRespOk(totalUsedMoney), nil
}
