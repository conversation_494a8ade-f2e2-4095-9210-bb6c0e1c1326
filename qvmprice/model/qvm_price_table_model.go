package model

import (
	"errors"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/dao"
)

const qvmPriceTableTableName = "qvm_price_tables"

// QVMPriceTable model definition
type QVMPriceTable struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// UID UID，为 0 代表公开报价，不为 0 代表改价
	// 目前 uid = 0 的价格表应该只有一张，但不排除之后会改变
	UID uint64 `gorm:"column:uid;not null;index:idx_qvmpt_uid;comment:'UID，为 0 代表公开报价，不为 0 代表改价。目前 uid = 0 的价格表应该只有一张，但不排除之后会改变'"`
	// Remark 内部描述，用户不可见
	Remark string `gorm:"column:remark;type:VARCHAR(8192);not null;default:'';comment:'内部描述，用户不可见'"`
	// IsDisabled 本报价单是否被整体禁用
	IsDisabled bool `gorm:"column:is_disabled;not null;default:0;comment:'本报价单是否被整体禁用'"`

	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);default:now(6)"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME(6);default:now(6) ON UPDATE now(6)"`
}

// 由于 QVM 不是 gorm 认识的缩略词，默认它会生成形如 q_vm_xxx 的表名，因此显式指定表名
func (*QVMPriceTable) TableName() string {
	return qvmPriceTableTableName
}

// QVMPriceTableDao is data access object of QVMPriceTable model
type QVMPriceTableDao struct {
	base *dao.BaseDao
}

// NewQVMPriceTableDao is constructor of QVMPriceTableDao
func NewQVMPriceTableDao(base *dao.BaseDao) *QVMPriceTableDao {
	return &QVMPriceTableDao{
		base: base,
	}
}

func (d *QVMPriceTableDao) GetByID(id uint64) (*QVMPriceTable, error) {
	var result QVMPriceTable
	err := d.base.Execute(func(val any) error {
		return d.base.Model(&QVMPriceTable{}).Where("`id` = ?", id).First(val).Error
	}, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (d *QVMPriceTableDao) ListDisabledStatusesByIDs(
	ids []uint64,
) (map[uint64]bool, error) {
	if len(ids) == 0 {
		return nil, errors.New("ids must not be empty for ListDisabledStatusesByIDs")
	}

	var rows []QVMPriceTable
	err := d.base.Model(&QVMPriceTable{}).
		Where("`id` IN (?)", ids).
		Find(&rows).
		Error
	if err != nil {
		return nil, err
	}

	result := make(map[uint64]bool, len(rows))
	for _, row := range rows {
		result[row.ID] = row.IsDisabled
	}

	return result, nil
}

// Create 插入一条记录，如果成功返回会更新入参的 ID、CreatedAt、UpdatedAt 等字段
func (d *QVMPriceTableDao) Create(
	x *QVMPriceTable,
) error {
	if x.ID != 0 {
		return errors.New("id must be 0 for Create")
	}

	return d.base.Execute(func(val any) error {
		return d.base.Model(&QVMPriceTable{}).Create(val).Error
	}, x)
}

// ToggleByID 根据 ID 更新该条记录的启用/禁用状态
func (d *QVMPriceTableDao) ToggleByID(ptID uint64, enabled bool) error {
	return d.base.Execute(func(_ any) error {
		result := d.base.Model(&QVMPriceTable{}).
			Where("`id` = ?", ptID).
			Update("is_disabled", !enabled)

		err := result.Error
		if err != nil {
			return err
		}

		if result.RowsAffected == 0 {
			return gorm.ErrRecordNotFound
		}

		return nil
	}, nil)
}
