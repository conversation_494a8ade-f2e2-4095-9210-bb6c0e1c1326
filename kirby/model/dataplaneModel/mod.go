package dataplaneModel

import (
	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/dao"
)

// KirbyDataplaneDao is data access object for kirby's dataplane's MySQL
// persistence layer.
type KirbyDataplaneDao struct {
	debug       bool
	base        *dao.BaseDao
	AggSeriesMD *AggSeriesMetadataDao
	SeriesMD    *SeriesMetadataDao
	DataMgr     *DataTableManager
}

// NewKirbyDataplaneDao constructs a KirbyDataplaneDao.
func NewKirbyDataplaneDao(base *dao.BaseDao, debug bool) *KirbyDataplaneDao {
	return &KirbyDataplaneDao{
		debug:       debug,
		base:        base,
		AggSeriesMD: NewAggSeriesMetadataDao(base),
		SeriesMD:    NewSeriesMetadataDao(base),
		DataMgr:     NewDataTableManager(base.DB, debug),
	}
}

// DoTransaction do a transaction
func (d *KirbyDataplaneDao) DoTransaction(fn func(*KirbyDataplaneDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		newDao := NewKirbyDataplaneDao(base, d.debug)
		return fn(newDao)
	})
}

// RegisterMigrate migrate all models
func RegisterMigrate(db *gorm.DB) {
	// all tables are manually sharded, so nothing is to be registered here
}
