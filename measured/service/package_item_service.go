package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/measured/model"
)

// GetPackageItemByID 根据 ID 查询单个资源包项目
func (s *MeasureBizService) GetPackageItemByID(
	ctx context.Context,
	id uint64,
) (*model.PackageItem, error) {
	return s.dao.PackageItem.GetByID(id, s.cacheExpires)
}

// ListPackageItemsByPackageID 根据资源包 ID 列出资源包项目列表
func (s *MeasureBizService) ListPackageItemsByPackageID(
	ctx context.Context,
	pkgID uint64,
	offset int,
	limit int,
) ([]model.PackageItem, error) {
	return s.dao.PackageItem.ListByPackageID(pkgID, offset, limit)
}

// CountPackageItemsByPackageID 根据资源包 ID 获取资源包项目数量
func (s *MeasureBizService) CountPackageItemsByPackageID(
	ctx context.Context,
	pkgID uint64,
) (uint64, error) {
	return s.dao.PackageItem.CountByPackageID(pkgID)
}

// CreatePackageItem 创建一个资源包项目
func (s *MeasureBizService) CreatePackageItem(
	ctx context.Context,
	obj *model.PackageItem,
) (*model.PackageItem, error) {
	obj.ID = 0
	err := s.dao.PackageItem.Save(obj, s.cacheExpires)
	return obj, err
}

// DeletePackageItemByID 根据 ID 删除一个资源包项目
func (s *MeasureBizService) DeletePackageItemByID(
	ctx context.Context,
	id uint64,
) (*model.PackageItem, error) {
	obj, err := s.dao.PackageItem.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "query package_item failed").WithField("id", id)
	}
	err = s.dao.PackageItem.DeleteByID(id)
	return obj, err
}
