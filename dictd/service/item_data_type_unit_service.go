package service

import (
	"context"

	"qiniu.io/pay/dictd/model"
)

// GetItemDataTypeUnitByID 根据ID获取计费项类型单位
func (s *DictBizService) GetItemDataTypeUnitByID(
	ctx context.Context,
	id uint64,
) (*model.ItemDataTypeUnit, error) {
	return s.dictDao.ItemDataTypeUnit.GetByID(id, s.cacheExpires)
}

// ListItemDataTypeUnitsByItemDataTypeID 根据计费项数据类型ID获取计费项数据类型单位
func (s *DictBizService) ListItemDataTypeUnitsByItemDataTypeID(
	ctx context.Context,
	typeID uint64,
	offset int,
	limit int,
) ([]model.ItemDataTypeUnit, error) {
	return s.dictDao.ItemDataTypeUnit.ListByItemDataTypeID(typeID, offset, limit, s.cacheExpires)
}

// ListItemDataTypeUnitsByItemAndDataType 根据计费项 code 和数据类型 type 获取计费项数据类型单位
func (s *DictBizService) ListItemDataTypeUnitsByItemAndDataType(
	ctx context.Context,
	item string,
	dataType string,
	offset int,
	limit int,
) ([]model.ItemDataTypeUnit, error) {
	return s.dictDao.ItemDataTypeUnit.ListByItemAndDataType(item, dataType, offset, limit, s.cacheExpires)
}

// CountItemDataTypeUnitsByItemDataTypeID 根据计费项数据类型ID获取计费项数据类型单位数量
func (s *DictBizService) CountItemDataTypeUnitsByItemDataTypeID(
	ctx context.Context,
	typeID uint64,
) (uint64, error) {
	return s.dictDao.ItemDataTypeUnit.CountByDataTypeID(typeID, s.cacheExpires)
}

// CountItemDataTypeUnitsByItemAndDataType 根据计费项 code 和数据类型 type 获取计费项数据类型单位数量
func (s *DictBizService) CountItemDataTypeUnitsByItemAndDataType(
	ctx context.Context,
	item string,
	dataType string,
) (uint64, error) {
	return s.dictDao.ItemDataTypeUnit.CountByItemAndDataType(item, dataType, s.cacheExpires)
}

// CreateItemDataTypeUnit 创建计费项数据类型单位
func (s *DictBizService) CreateItemDataTypeUnit(
	ctx context.Context,
	unit *model.ItemDataTypeUnit,
) (*model.ItemDataTypeUnit, error) {
	unit.ID = 0
	err := s.dictDao.ItemDataTypeUnit.Save(unit, s.cacheExpires)
	return unit, err
}

// UpdateItemDataTypeUnitByID 根据ID更新计费项数据类型单位
func (s *DictBizService) UpdateItemDataTypeUnitByID(
	ctx context.Context,
	id uint64,
	unit *model.ItemDataTypeUnit,
) (*model.ItemDataTypeUnit, error) {
	unit.ID = id
	err := s.dictDao.ItemDataTypeUnit.Save(unit, s.cacheExpires)
	return unit, err
}

// DeleteItemDataTypeUnitByID 根据ID删除计费项数据类型单位
func (s *DictBizService) DeleteItemDataTypeUnitByID(
	ctx context.Context,
	id uint64,
) (*model.ItemDataTypeUnit, error) {
	unit, err := s.GetItemDataTypeUnitByID(ctx, id)
	if err != nil {
		return nil, err
	}
	err = s.dictDao.ItemDataTypeUnit.Delete(unit)
	return unit, err
}
