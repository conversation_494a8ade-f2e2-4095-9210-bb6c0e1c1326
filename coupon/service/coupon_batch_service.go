package service

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/coupon/model"

	"github.com/fatih/structs"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/qbox/pay-sdk/themis"
	"github.com/qbox/pay-sdk/trade/client"
	"github.com/qbox/pay-sdk/trade/client/operations"
	"github.com/qbox/pay-sdk/trade/models"
	"github.com/sirupsen/logrus"
)

// CouponBatchService is service handler of couponBatch
type CouponBatchService struct {
	couponBatchDao *model.CouponBatchDao
	tradeClient    *client.Trade
	themisClient   themis.ThemisServiceClient
	cacheExpires   time.Duration
}

// NewCouponBatchService is constructor of CouponBatchService
func NewCouponBatchService(couponBatchDao *model.CouponBatchDao,
	tradeClient *client.Trade,
	themisClient themis.ThemisServiceClient,
	expires time.Duration) (*CouponBatchService, error) {

	return &CouponBatchService{
		couponBatchDao: couponBatchDao,
		tradeClient:    tradeClient,
		themisClient:   themisClient,
		cacheExpires:   expires,
	}, nil
}

type CouponBatchAndScope struct {
	CouponBatch *model.CouponBatch
	Scope       *CouponScope
}

// GetByID 根据 ID 查询一条抵用券批次
func (s *CouponBatchService) GetByID(
	ctx context.Context,
	id uint64,
) (*CouponBatchAndScope, error) {
	couponBatch, err := s.couponBatchDao.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	couponScopes, err := s.mgetCouponScope(ctx, []uint64{couponBatch.ID})
	if err != nil {
		return nil, errors.Trace(err)
	}
	return &CouponBatchAndScope{
		CouponBatch: couponBatch,
		Scope:       couponScopes[strconv.FormatUint(couponBatch.ID, 10)],
	}, nil
}

// CouponBatchList returns a list of couponBatches
type CouponBatchList struct {
	CouponBatches []CouponBatchAndScope
}

// ListByCondsUnpaginated 按条件查询所有抵用券批次记录，对调用方不支持分页
func (s *CouponBatchService) ListByCondsUnpaginated(
	ctx context.Context,
	param *model.ListCouponBatchesByCondsParam,
) (*CouponBatchList, error) {
	l := logging.GetLogger(ctx)

	couponBatches := make([]CouponBatchAndScope, 0)

	offset := 0
	limit := 1000

	for {
		list, err := s.ListByConds(ctx, param, offset, limit)
		if err != nil {
			l.Errorf("ListByCondsUnpaginated invoke ListByConds error: %s", err)
			return nil, err
		}
		if len(list.CouponBatches) == 0 {
			break
		}
		offset += limit

		couponBatches = append(couponBatches, list.CouponBatches...)
	}
	return &CouponBatchList{
		CouponBatches: couponBatches,
	}, nil
}

// ListByConds lists CouponBatches By conditions
func (s *CouponBatchService) ListByConds(
	ctx context.Context,
	param *model.ListCouponBatchesByCondsParam,
	offset, limit int,
) (*CouponBatchList, error) {
	l := logging.GetLogger(ctx)

	couponBatches, err := s.couponBatchDao.ListByConds(param, offset, limit)
	if err != nil {
		l.Errorln("ListByConds invoke couponBatchDao.ListByConds error", err)
		return nil, err
	}

	couponBatchAndScopes := make([]CouponBatchAndScope, len(couponBatches))
	batchIDs := make([]uint64, 0)
	batchIDsSet := make(map[uint64]struct{})
	for _, batch := range couponBatches {
		if _, ok := batchIDsSet[batch.ID]; ok {
			continue
		}
		batchIDsSet[batch.ID] = struct{}{}
		batchIDs = append(batchIDs, batch.ID)
	}
	couponScopeMap, err := s.mgetCouponScope(ctx, batchIDs)
	if err != nil {
		return nil, errors.Trace(err)
	}
	for i, batch := range couponBatches {
		couponBatchAndScopes[i] = CouponBatchAndScope{
			CouponBatch: &couponBatches[i],
			Scope:       couponScopeMap[strconv.FormatUint(batch.ID, 10)],
		}
	}
	return &CouponBatchList{
		CouponBatches: couponBatchAndScopes,
	}, nil
}

// CouponScope defines trade couponScope
type CouponScope struct {
	CouponID          string  `json:"coupon_id"`
	SellerIdsInclude  []int64 `json:"seller_ids_include"`
	SellerIdsExclude  []int64 `json:"seller_ids_exclude"`
	ProductIdsInclude []int64 `json:"product_ids_include"`
	ProductIdsExclude []int64 `json:"product_ids_exclude"`
	SkuIdsInclude     []int64 `json:"sku_ids_include"`
	SkuIdsExclude     []int64 `json:"sku_ids_exclude"`
	PackageIdsInclude []int64 `json:"package_ids_include"`
	PackageIdsExclude []int64 `json:"package_ids_exclude"`
}

// CreateCouponBatchParam defines couponBatch creation param
type CreateCouponBatchParam struct {
	CouponBatch model.CouponBatch
	Scope       CouponScope
}

// CreateCouponBatch creates a new CouponBatch
func (s *CouponBatchService) CreateCouponBatch(
	ctx context.Context,
	param *CreateCouponBatchParam,
) (*CouponBatchAndScope, error) {
	l := logging.GetLogger(ctx)

	now := time.Now()
	if param.CouponBatch.BindMethod == model.CouponBindMethodPrebound {
		param.CouponBatch.State = model.CouponBatchStateInProgress
	} else {
		startTime := param.CouponBatch.StartTime.Time(ctx)
		endTime := param.CouponBatch.EndTime.Time(ctx)
		if now.Before(startTime) {
			param.CouponBatch.State = model.CouponBatchStateNotStarted
		} else if now.Before(endTime) {
			param.CouponBatch.State = model.CouponBatchStateInProgress
		} else {
			param.CouponBatch.State = model.CouponBatchStateEnded
		}
	}

	// 保存 couponBatch
	err := s.couponBatchDao.Save(&param.CouponBatch)
	if err != nil {
		l.Errorln("invoke couponBatchDao.Save error", err)
		return nil, err
	}
	param.Scope.CouponID = strconv.FormatUint(param.CouponBatch.ID, 10)

	tradeParams := operations.NewPostCouponScopeNewParams().WithContext(ctx)
	// 处理 scope，写入了 scope 库表
	err = s.newCouponScopeWithParams(ctx, tradeParams, param.Scope)
	if err != nil {
		l.Errorln("invoke sendCouponScopeWithParams error", err)
		return nil, err
	}
	return &CouponBatchAndScope{
		CouponBatch: &param.CouponBatch,
		Scope:       &param.Scope,
	}, nil
}

func (s *CouponBatchService) newCouponScopeWithParams(ctx context.Context,
	params *operations.PostCouponScopeNewParams, scope CouponScope) (err error) {

	data, err := json.Marshal(scope)
	if err != nil {
		return errors.Trace(err)
	}

	_, err = s.tradeClient.Operations.PostCouponScopeNew(params.WithData(string(data)))
	if err != nil {
		return errors.Errorf("invoke PostCouponScopeNew error: %+v,%+v", err, scope)
	}

	return
}

func (s *CouponBatchService) updateCouponScopeWithParams(ctx context.Context,
	params *operations.PostCouponScopeUpdateParams, scope CouponScope) (err error) {

	data, err := json.Marshal(scope)
	if err != nil {
		return errors.Trace(err)
	}

	_, err = s.tradeClient.Operations.PostCouponScopeUpdate(params.WithData(string(data)))
	if err != nil {
		return errors.Errorf("invoke PostCouponScopeUpdate error: %+v,%+v", err, scope)
	}

	return
}

func (s *CouponBatchService) mgetCouponScope(ctx context.Context, batchIDs []uint64) (couponScopeMap map[string]*CouponScope, err error) {
	couponScopeMap = make(map[string]*CouponScope)

	if len(batchIDs) == 0 {
		return couponScopeMap, nil
	}

	batchIDsStr := make([]string, len(batchIDs))
	for i := range batchIDs {
		batchIDsStr[i] = strconv.FormatUint(batchIDs[i], 10)
	}
	resp, err := s.tradeClient.Operations.PostCouponScopeMget(
		operations.NewPostCouponScopeMgetParams().WithContext(ctx).WithReqCouponScopeMget(
			&models.ReqCouponScopeMget{
				CouponIds: batchIDsStr,
			},
		),
	)
	if err != nil {
		return couponScopeMap, errors.Trace(err)
	}

	for _, batchID := range batchIDsStr {
		if _, ok := couponScopeMap[batchID]; !ok {
			couponScopeMap[batchID] = &CouponScope{
				CouponID:         batchID,
				SellerIdsInclude: make([]int64, 0),
				SellerIdsExclude: make([]int64, 0),

				ProductIdsInclude: make([]int64, 0),
				ProductIdsExclude: make([]int64, 0),

				PackageIdsInclude: make([]int64, 0),
				PackageIdsExclude: make([]int64, 0),
			}
		}
		for _, include := range resp.GetPayload().IncludeScope {
			if include.CouponID != batchID {
				continue
			}
			scope := couponScopeMap[batchID]
			scope.SellerIdsInclude = append(scope.SellerIdsInclude, include.SellerIds...)
			scope.ProductIdsInclude = append(scope.ProductIdsInclude, include.ProductIds...)
			scope.PackageIdsInclude = append(scope.PackageIdsInclude, include.PackageIds...)
			couponScopeMap[batchID] = scope
		}

		for _, exclude := range resp.GetPayload().ExcludeScope {
			if exclude.CouponID != batchID {
				continue
			}
			scope := couponScopeMap[batchID]
			scope.SellerIdsExclude = append(scope.SellerIdsExclude, exclude.SellerIds...)
			scope.ProductIdsExclude = append(scope.ProductIdsExclude, exclude.ProductIds...)
			scope.PackageIdsExclude = append(scope.PackageIdsExclude, exclude.PackageIds...)
			couponScopeMap[batchID] = scope
		}
	}
	return couponScopeMap, nil
}

// SyncAllCouponBatchStates 批量更新所有 CouponBatch 的状态字段
func (s *CouponBatchService) SyncAllCouponBatchStates(
	ctx context.Context,
) error {
	return s.SyncAllCouponBatchStatesWithEpoch(ctx, time.Now())
}

// SyncAllCouponBatchStatesWithEpoch 以给定的时间为当前时间，批量更新所有 CouponBatch 的状态字段
func (s *CouponBatchService) SyncAllCouponBatchStatesWithEpoch(
	ctx context.Context,
	epoch time.Time,
) error {
	logger := logging.GetLogger(ctx)

	// XXX FIXME: limit 传不进 -1
	allCouponBatches, err := s.couponBatchDao.ListAllCouponBatches(0, 1000000, s.cacheExpires)
	if err != nil {
		return errors.Trace(err)
	}

	// TODO: 并行化
	for _, couponBatch := range allCouponBatches {
		_, err := s.couponBatchDao.ProgressState(ctx, &couponBatch, epoch, s.cacheExpires)
		if err != nil {
			logger.WithError(err).WithFields(logrus.Fields{
				"couponBatch": couponBatch,
			}).Error("state update for coupon batch failed")

			// 要接着处理别的，不失败返回
		}
	}

	return nil
}

// UpdateCouponBatchByID updates a CouponBatch
func (s *CouponBatchService) UpdateCouponBatchByID(
	ctx context.Context,
	param *CreateCouponBatchParam,
) (couponBatchAndScopeRet *CouponBatchAndScope, err error) {
	l := logging.GetLogger(ctx)

	couponBatchAndScopeRet = &CouponBatchAndScope{
		CouponBatch: &param.CouponBatch,
		Scope:       &param.Scope,
	}

	err = s.DoTransaction(func(couponBatchService *CouponBatchService) error {
		// 1. 查询当前 couponBatch 状态，根据状态决定哪些字段可以被更新
		oldCouponBatch, err1 := couponBatchService.couponBatchDao.GetByID(param.CouponBatch.ID)
		if err1 != nil {
			l.Errorln("invoke couponBatchDao.GetByID error", err1)
			return err1
		}

		var newCouponBatchMap map[string]any
		var omits []string
		newCouponBatchMapBase := map[string]any{
			"reason":            param.CouponBatch.Reason,
			"reason_desc":       param.CouponBatch.ReasonDesc,
			"applicant":         param.CouponBatch.Applicant,
			"coupon_scope_desc": param.CouponBatch.CouponScopeDesc,
			"url":               param.CouponBatch.Url,
		}
		switch oldCouponBatch.State {
		case model.CouponBatchStateNotStarted:
			newCouponBatchMap = structs.Map(param.CouponBatch)
			omits = []string{"bind_method", "state", "created_at"}

		case model.CouponBatchStateInProgress:
			newCouponBatchMapBase["batch_size"] = param.CouponBatch.BatchSize
			newCouponBatchMapBase["max_activation_times_per_user"] = param.CouponBatch.MaxActivationTimesPerUser
			newCouponBatchMapBase["is_unlimited_scope"] = param.CouponBatch.IsUnlimitedScope
			newCouponBatchMap = newCouponBatchMapBase

		case model.CouponBatchStateEnded:
			newCouponBatchMap = newCouponBatchMapBase

		default:
			// 其他状态直接退出，scope 信息未查
			couponBatchAndScopeRet = &CouponBatchAndScope{
				CouponBatch: oldCouponBatch,
				Scope:       nil,
			}
			return nil
		}

		// 2. 更新 couponBatch
		err1 = couponBatchService.couponBatchDao.UpdateWithOmitCol(param.CouponBatch.ID, newCouponBatchMap, omits)
		if err1 != nil {
			l.Errorln("invoke couponBatchDao.UpdateWithOmitCol error", err1)
			return err1
		}
		param.Scope.CouponID = strconv.FormatUint(param.CouponBatch.ID, 10)

		// 3. 更新 trade 中 coupon_scope
		if oldCouponBatch.State != model.CouponBatchStateEnded {
			tradeParams := operations.NewPostCouponScopeUpdateParams().WithContext(ctx)
			// 处理 scope，写入了 scope 库表
			err1 = s.updateCouponScopeWithParams(ctx, tradeParams, param.Scope)
			if err1 != nil {
				l.Errorln("invoke sendCouponScopeWithParams error", err1)
				return err1
			}
		}

		return nil
	})

	return couponBatchAndScopeRet, err
}

// DoTransaction do a transaction
func (s *CouponBatchService) DoTransaction(fn func(couponBatchService *CouponBatchService) error) error {
	return s.couponBatchDao.DoTransaction(func(dao *model.CouponBatchDao) error {
		newCouponBatchSrv, err := NewCouponBatchService(dao, s.tradeClient, s.themisClient, s.cacheExpires)
		if err != nil {
			return err
		}

		return fn(newCouponBatchSrv)
	})
}

// UpdateBatchNumUsed 更新批次对应的券使用数量
func (s *CouponBatchService) UpdateBatchNumUsed(
	ctx context.Context,
	batchID uint64,
	numUsed uint64,
) error {
	logger := logging.GetLogger(ctx)

	err := s.couponBatchDao.UpdateWithOmitCol(
		batchID,
		map[string]any{
			"num_used": numUsed,
		},
		[]string{},
	)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"batchID": batchID,
			"numUsed": numUsed,
		}).Error("update num used failed")
		return err
	}
	return nil
}
