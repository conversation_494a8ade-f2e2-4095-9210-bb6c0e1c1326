package adapter

import (
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/model"
)

// BuildPbItemStatConfig converts from model.ItemStatConfig to the protobuf type.
func BuildPbItemStatConfig(x *model.ItemStatConfig) (*pb.ItemStatConfig, error) {
	return &pb.ItemStatConfig{
		Id:          x.ID,
		ItemId:      x.ItemID,
		StatSrcType: x.StatSrcType,
	}, nil
}

// BuildModelItemStatConfig converts from pb.ItemStatConfig to the model layer type.
func BuildModelItemStatConfig(x *pb.ItemStatConfig) (*model.ItemStatConfig, error) {
	return &model.ItemStatConfig{
		ID:          x.GetId(),
		ItemID:      x.GetItemId(),
		StatSrcType: x.GetStatSrcType(),
	}, nil
}
