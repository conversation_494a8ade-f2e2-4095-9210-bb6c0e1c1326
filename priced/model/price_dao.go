package model

import (
	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/dao"
)

// PriceDao is data access object for priced service
type PriceDao struct {
	base               *dao.BaseDao
	PriceTable         *PriceTableDao
	PriceItem          *PriceItemDao
	PriceItemStair     *PriceItemStairDao
	PriceUserMap       *PriceUserMapDao
	PriceUserAlgorithm *PriceUserAlgorithmDao
}

// NewPriceDao is constructor of PriceDao
func NewPriceDao(base *dao.BaseDao) *PriceDao {
	return &PriceDao{
		base:               base,
		PriceTable:         NewPriceTableDao(base),
		PriceItem:          NewPriceItemDao(base),
		PriceItemStair:     NewPriceItemStairDao(base),
		PriceUserMap:       NewPriceUserMapDao(base),
		PriceUserAlgorithm: NewPriceUserAlgorithmDao(base),
	}
}

// DoTransaction do a transaction
func (d *PriceDao) DoTransaction(fn func(*PriceDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		priceDao := NewPriceDao(base)
		priceDao.base = d.base
		return fn(priceDao)
	})
}

// RegisterMigrate migrate all models
func RegisterMigrate(db *gorm.DB) {
	db.AutoMigrate(&PriceItem{})
	db.AutoMigrate(&PriceItemStair{})
	db.AutoMigrate(&PriceTable{})
	db.AutoMigrate(&PriceUserMap{})
	db.AutoMigrate(&PriceUserAlgorithm{})
}
