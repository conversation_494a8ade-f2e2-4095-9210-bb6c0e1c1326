package service

import (
	"context"

	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	"qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/model"
)

type SystemCreditHistoryService struct {
	systemCreditHistoryDao *dao.SystemCreditHistoryDao
}

func NewSystemCreditHistoryService(dao *dao.SystemCreditHistoryDao) *SystemCreditHistoryService {
	return &SystemCreditHistoryService{
		systemCreditHistoryDao: dao,
	}
}

func (s *SystemCreditHistoryService) ListSystemCreditHistory(
	ctx context.Context,
	uid uint64,
	offset int,
	limit int,
) ([]*model.SystemCreditHistory, error) {
	logger := logging.GetLogger(ctx)
	histories, err := s.systemCreditHistoryDao.ListSystemCreditHistory(uid, offset, limit)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"uid":    uid,
			"limit":  limit,
			"offset": offset,
		}).Error("list user system credit history failed")
		return nil, err
	}
	return histories, nil
}

func (s *SystemCreditHistoryService) CountSystemCreditHistory(ctx context.Context, uid uint64) (uint64, error) {
	logger := logging.GetLogger(ctx)
	count, err := s.systemCreditHistoryDao.CountSystemCreditHistory(uid)
	if err != nil {
		logger.WithError(err).WithField("uid", uid).Error("count system credit history failed")
		return 0, err
	}
	return count, nil
}
