rpc:
  addr: ":9805"
  gatewayaddr: ":9815"
  forwardaddr: "localhost:9805"
  enablepprof: false
  enum_as_ints: true
intl:
  ref_timezone:
    name: CST
    offset: 28800  # 8 * 3600
  default_lang_code: zh
acc:
  host: 'http://localhost:9100'
  username: 'root'
  password: 'root'
mysql:
  host: "localhost"
  port: 3306
  username: "root"
  password: ""
  db: "sufy_plan"
  # utf8 只支持 Unicode BMP 字符，要用 utf8mb4 (MySQL 5.5.3+)
  # https://dev.mysql.com/doc/refman/5.5/en/charset-unicode-utf8mb4.html
  charset: "utf8mb4"
  # 永远不要用 utf8_general_ci: https://stackoverflow.com/a/766996
  collation: "utf8mb4_unicode_ci"
  parse_time: true
  loc: "Local"
  max_open_conn: 100
  max_idle_conn: 100
  max_lifetime: 5m
  debug: true
cache:
  enabled: true
  prefix: "sufy-plan"
  redis_config:
    addrs:
      - "localhost:6379"
    mastername: ""
    poolsize: 2000
    readtimeout: 30s  #30 * time.Second
    idletimeout: 240s # 240 * time.Second
    pooltimeout: 240s # 240 * time.Second
  default_expires: 30m
plan:
  default_pagesize: 10

http_service:
  gaea: "http://127.0.0.1:9013"
  price: "http://127.0.0.1:19521"
  wallet: "http://127.0.0.1:9520"

rpc_service:
  dict: ":9701"
  respack: ":9902"
  price: ":9704"
