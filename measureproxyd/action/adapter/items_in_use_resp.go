package adapter

import (
	pb "github.com/qbox/pay-sdk/measureproxy"
	"qiniu.io/pay/measureproxyd/model"
)

// BuildPbItemsInUseResp converts from model.ItemsInUseResp to the protobuf type.
func BuildPbItemsInUseResp(x model.ItemsInUseResp) (*pb.ItemsInUseResp, error) {
	itemCodes := make([]string, len(x))
	_ = copy(itemCodes, x)

	return &pb.ItemsInUseResp{
		ItemCodes: itemCodes,
	}, nil
}

// BuildModelItemsInUseResp converts from pb.ItemsInUseResp to the model layer type.
func BuildModelItemsInUseResp(x *pb.ItemsInUseResp) (model.ItemsInUseResp, error) {
	itemCodes := make([]string, len(x.ItemCodes))
	_ = copy(itemCodes, x.ItemCodes)

	return itemCodes, nil
}
