package model

import (
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// ScopeItemGroupMap model definition
type ScopeItemGroupMap struct {
	ID         uint64 `gorm:"primary_key"`
	ScopeID    uint64 `gorm:"unique_index:idx_scope_item_group_map_scope_id_group_id"`
	GroupID    uint64 `gorm:"unique_index:idx_scope_item_group_map_scope_id_group_id"`
	GroupCode  string `gorm:"-"`
	IsExcluded bool
	CreatedAt  time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt  time.Time `sql:"type:DATETIME(6)"`
}

// TableName return table name of ScopeItemGroupMap
func (*ScopeItemGroupMap) TableName() string {
	return "scope_item_group_map"
}

// ScopeItemGroupMapDao data access object of ItemGroupMap model
type ScopeItemGroupMapDao struct {
	base *dao.BaseDao
}

// NewScopeItemGroupMapDao constructor of ScopeItemGroupMapDao
func NewScopeItemGroupMapDao(base *dao.BaseDao) *ScopeItemGroupMapDao {
	return &ScopeItemGroupMapDao{
		base: base,
	}
}

// ListAll select all records
func (d *ScopeItemGroupMapDao) ListAll(
	offset int, limit int,
	expires ...time.Duration,
) (list []ScopeItemGroupMap, err error) {
	err = d.base.Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": offset,
			"limit":  limit,
		})
	}
	return
}

// CountAll select all records
func (d *ScopeItemGroupMapDao) CountAll(
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&ScopeItemGroupMap{}).
		Count(&count).
		Error
	return
}

// ListByGroupID select records by group_id
func (d *ScopeItemGroupMapDao) ListByGroupID(
	groupID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (list []ScopeItemGroupMap, err error) {
	err = d.base.Where(&ScopeItemGroupMap{GroupID: groupID}).
		Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"group_id": groupID,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return
}

// CountByGroupID select records by group_id
func (d *ScopeItemGroupMapDao) CountByGroupID(
	groupID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&ScopeItemGroupMap{}).
		Where(&ScopeItemGroupMap{GroupID: groupID}).
		Count(&count).
		Error
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"group_id": groupID,
		})
	}
	return
}

// ListByScopeID select records by scope_id
func (d *ScopeItemGroupMapDao) ListByScopeID(
	scopeID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (list []ScopeItemGroupMap, err error) {
	err = d.base.Where(&ScopeItemGroupMap{ScopeID: scopeID}).
		Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return
}

// CountByScopeID select records by scope_id
func (d *ScopeItemGroupMapDao) CountByScopeID(
	scopeID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&ScopeItemGroupMap{}).
		Where(&ScopeItemGroupMap{ScopeID: scopeID}).
		Count(&count).
		Error
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
		})
	}
	return
}

// GetByID select a record by id
func (d *ScopeItemGroupMapDao) GetByID(
	id uint64,
	expires ...time.Duration,
) (*ScopeItemGroupMap, error) {
	model := &ScopeItemGroupMap{}
	err := d.base.Where(&ScopeItemGroupMap{ID: id}).
		First(model).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// Save insert or update a record
func (d *ScopeItemGroupMapDao) Save(
	model *ScopeItemGroupMap,
	expires ...time.Duration,
) error {
	return d.base.Save(model).Error
}

// Delete delete a record
func (d *ScopeItemGroupMapDao) Delete(
	model *ScopeItemGroupMap,
) error {
	return d.base.Delete(model).Error
}
