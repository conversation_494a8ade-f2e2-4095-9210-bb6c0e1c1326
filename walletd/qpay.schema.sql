USE `pay_wallet_v2`;

CREATE TABLE `payment_transaction` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    `is_snap` tinyint NOT NULL DEFAULT 1 COMMENT '快照与否',
    `payment_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'payment唯一标识',
    `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT 'uid',
    `entry_id` varchar(100) NOT NULL DEFAULT '' COMMENT 'entry_id',
    `entry_type` tinyint NOT NULL DEFAULT 1 COMMENT 'entry类型',
    `entry_desc` varchar(255) NOT NULL DEFAULT '' COMMENT 'entry描述',
    `payment_amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '流水总金额',
    `payment_remained` bigint(20) NOT NULL DEFAULT 0 COMMENT '流水剩余金额',
    `payment_paid` bigint(20) NOT NULL DEFAULT 0 COMMENT '流水已付金额',
    `payment_status` tinyint NOT NULL DEFAULT 1 COMMENT '流水状态',
    `payment_type` tinyint NOT NULL DEFAULT 1 COMMENT '流水类型',
    `description` varchar(255) NOT NULL DEFAULT '' COMMENT '流水描述',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '流水备注',
    `snap_id` varchar(64) NOT NULL DEFAULT '' COMMENT '快照id',
    `record_status` tinyint NOT NULL DEFAULT 0 COMMENT '记录状态',
    `excode` varchar(64) NOT NULL DEFAULT '' COMMENT '老系统的字段excode',
    `type` varchar(64) NOT NULL DEFAULT '' COMMENT '老系统的字段type',
    `details` varchar(64) NOT NULL DEFAULT '' COMMENT '老系统的字段details',
    `created_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '流水创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '流水最后更新时间',
    `order_hash` varchar(64) NOT NULL DEFAULT '' COMMENT 'order hash',
    `related_month` tinyint NOT NULL DEFAULT 0 COMMENT '关联月份',
    `product` varchar(64) NOT NULL DEFAULT '' COMMENT 'bill product',
    `group` varchar(64) NOT NULL DEFAULT '' COMMENT 'bill group',
    `item` varchar(64) NOT NULL DEFAULT '' COMMENT  'bill_item',
    `cost` bigint(20) NOT NULL DEFAULT 0 COMMENT '优惠券现金部分',
    `niu_coin` bigint(20) NOT NULL DEFAULT 0 COMMENT '优惠券牛币部分',
    `reward_id` varchar(64) NOT NULL DEFAULT '' COMMENT '优惠码',
    PRIMARY KEY (`id`),
    KEY `idx_payment_id` (`payment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `payment_transaction_snap` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    `is_snap` tinyint NOT NULL DEFAULT 1 COMMENT '快照与否',
    `payment_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'payment唯一标识',
    `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT 'uid',
    `entry_id` varchar(100) NOT NULL DEFAULT '' COMMENT 'entry_id',
    `entry_type` tinyint NOT NULL DEFAULT 1 COMMENT 'entry类型',
    `entry_desc` varchar(255) NOT NULL DEFAULT '' COMMENT 'entry描述',
    `payment_amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '流水总金额',
    `payment_remained` bigint(20) NOT NULL DEFAULT 0 COMMENT '流水剩余金额',
    `payment_paid` bigint(20) NOT NULL DEFAULT 0 COMMENT '流水已付金额',
    `payment_status` tinyint NOT NULL DEFAULT 1 COMMENT '流水状态',
    `payment_type` tinyint NOT NULL DEFAULT 1 COMMENT '流水类型',
    `description` varchar(255) NOT NULL DEFAULT '' COMMENT '流水描述',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '流水备注',
    `snap_id` varchar(64) NOT NULL DEFAULT '' COMMENT '快照id',
    `record_status` tinyint NOT NULL DEFAULT 0 COMMENT '记录状态',
    `excode` varchar(64) NOT NULL DEFAULT '' COMMENT '老系统的字段excode',
    `type` varchar(64) NOT NULL DEFAULT '' COMMENT '老系统的字段type',
    `details` varchar(64) NOT NULL DEFAULT '' COMMENT '老系统的字段details',
    `created_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '流水创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '流水最后更新时间',
    `order_hash` varchar(64) NOT NULL DEFAULT '' COMMENT 'order hash',
    `related_month` tinyint NOT NULL DEFAULT 0 COMMENT '关联月份',
    `product` varchar(64) NOT NULL DEFAULT '' COMMENT 'bill product',
    `group` varchar(64) NOT NULL DEFAULT '' COMMENT 'bill group',
    `item` varchar(64) NOT NULL DEFAULT '' COMMENT  'bill_item',
    `cost` bigint(20) NOT NULL DEFAULT 0 COMMENT '优惠券现金部分',
    `niu_coin` bigint(20) NOT NULL DEFAULT 0 COMMENT '优惠券牛币部分',
    `reward_id` varchar(64) NOT NULL DEFAULT '' COMMENT '优惠码',
    PRIMARY KEY (`id`),
    KEY `idx_payment_id` (`payment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `payment_transaction_legacy` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    -- TODO
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wallet_request` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    `payment_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'payment_id',
    `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT 'uid',
    `money` bigint(20) NOT NULL DEFAULT 0 COMMENT '金额',
    `trans_direction` tinyint NOT NULL DEFAULT 1 COMMENT '方向',
    `trans_type` tinyint NOT NULL DEFAULT 1 COMMENT '类型',
    `asset_id` varchar(64) NOT NULL DEFAULT '' COMMENT '资产id',
    `asset_type` tinyint NOT NULL DEFAULT 1 COMMENT '资产类型',
    `snap_id` varchar(64) NOT NULL DEFAULT '' COMMENT '快照id',
    `entry_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'entry_id',
    `entry_type` tinyint NOT NULL DEFAULT 1 COMMENT 'entry类型',
    `entry_desc` varchar(255) NOT NULL DEFAULT '' COMMENT 'entry描述',
    `is_reverse` tinyint NOT NULL DEFAULT 1 COMMENT '是否反转',
    `reverse_trans_id` varchar(64) NOT NULL DEFAULT '' COMMENT '反转流水id',
    `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '这个request的状态',
    `record_status` tinyint NOT NULL DEFAULT 1 COMMENT '这条记录的状态',
    `created_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '最后更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wallet_request_snap` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    `payment_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'payment_id',
    `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT 'uid',
    `money` bigint(20) NOT NULL DEFAULT 0 COMMENT '金额',
    `trans_direction` tinyint NOT NULL DEFAULT 1 COMMENT '方向',
    `trans_type` tinyint NOT NULL DEFAULT 1 COMMENT '类型',
    `asset_id` varchar(64) NOT NULL DEFAULT '' COMMENT '资产id',
    `asset_type` tinyint NOT NULL DEFAULT 1 COMMENT '资产类型',
    `snap_id` varchar(64) NOT NULL DEFAULT '' COMMENT '快照id',
    `entry_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'entry_id',
    `entry_type` tinyint NOT NULL DEFAULT 1 COMMENT 'entry类型',
    `entry_desc` varchar(255) NOT NULL DEFAULT '' COMMENT 'entry描述',
    `is_reverse` tinyint NOT NULL DEFAULT 1 COMMENT '是否反转',
    `reverse_trans_id` varchar(64) NOT NULL DEFAULT '' COMMENT '反转流水id',
    `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态',
    `record_status` tinyint NOT NULL DEFAULT 1 COMMENT '这条记录的状态',
    `created_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '最后更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wallet_transaction` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    `payment_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'payment transaction id',
    `wallet_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'wallet/voucher id',
    `asset_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'wallet/voucher asset id',
    `asset_type` tinyint NOT NULL DEFAULT 1 COMMENT '资产类型',
    `trans_type` tinyint NOT NULL DEFAULT 1 COMMENT '类型',
    `wallet_request_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'wallet request id',
    `money` bigint(20) NOT NULL DEFAULT 0 COMMENT '金额',
    `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT 'uid',
    `entry_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'entry_id',
    `entry_type` tinyint NOT NULL DEFAULT 1 COMMENT 'entry类型',
    `entry_desc` varchar(255) NOT NULL DEFAULT '' COMMENT 'entry 描述',
    `trans_direction` tinyint NOT NULL DEFAULT 1 COMMENT '方向',
    `reverse_status` tinyint NOT NULL DEFAULT 1 COMMENT '反转状态',
    `reverse_trans_id` varchar(64) NOT NULL DEFAULT '' COMMENT '反转流水id',
    `description` varchar(64) NOT NULL DEFAULT '' COMMENT '描述',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `snap_id` varchar(64) NOT NULL DEFAULT '' COMMENT '快照id',
    `record_status` tinyint NOT NULL DEFAULT 1 COMMENT '记录状态',
    `created_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '最后更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wallet_transaction_snap` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    `payment_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'payment transaction id',
    `wallet_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'wallet/voucher id',
    `asset_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'wallet/voucher asset id',
    `asset_type` tinyint NOT NULL DEFAULT 1 COMMENT '资产类型',
    `trans_type` tinyint NOT NULL DEFAULT 1 COMMENT '类型',
    `wallet_request_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'wallet request id',
    `money` bigint(20) NOT NULL DEFAULT 0 COMMENT '金额',
    `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT 'uid',
    `entry_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'entry_id',
    `entry_type` tinyint NOT NULL DEFAULT 1 COMMENT 'entry类型',
    `entry_desc` varchar(255) NOT NULL DEFAULT '' COMMENT 'entry 描述',
    `trans_direction` tinyint NOT NULL DEFAULT 1 COMMENT '方向',
    `reverse_status` tinyint NOT NULL DEFAULT 1 COMMENT '反转状态',
    `reverse_trans_id` varchar(64) NOT NULL DEFAULT '' COMMENT '反转流水id',
    `description` varchar(64) NOT NULL DEFAULT '' COMMENT '描述',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `snap_id` varchar(64) NOT NULL DEFAULT '' COMMENT '快照id',
    `record_status` tinyint NOT NULL DEFAULT 1 COMMENT '记录状态',
    `created_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '最后更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wallet_transaction_legacy` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    -- TODO
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `wallet` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT 'uid',
    `asset_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'wallet/voucher asset id',
    `asset_type` tinyint NOT NULL DEFAULT 1 COMMENT '资产类型',
    `amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '金额',
    `balance` bigint(20) NOT NULL DEFAULT 0 COMMENT '余额',
    `record_status` tinyint NOT NULL DEFAULT 1 COMMENT '记录状态',
    `description` varchar(64) NOT NULL DEFAULT '' COMMENT '描述',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `created_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '最后更新时间',
    `related_trans_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联 trans id',
    `snap_id` varchar(64) NOT NULL DEFAULT '' COMMENT '快照id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `wallet_voucher` (
    `id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主键',
    `sub_system` char(50) NOT NULL DEFAULT '' COMMENT '适用系统',
    `quota` bigint(20) NOT NULL DEFAULT 0 COMMENT '总额',
    `balance` bigint(20) NOT NULL DEFAULT 0 COMMENT '余额',
    `created_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '创建时间',
    `updated_at` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '更新时间',
    `effect_time` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '生效时间',
    `dead_time` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '激活截止时间',
    `expired_time` datetime(6) NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT '过期时间',
    `code` varchar(64) NOT NULL DEFAULT '' COMMENT '优惠券码',
    `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT 'uid',
    `day` bigint(20) NOT NULL DEFAULT 0 COMMENT 'day',
    `title` varchar(64) NOT NULL DEFAULT '' COMMENT 'title',
    `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
    `voucher_type` char(50) NOT NULL DEFAULT '' COMMENT '优惠券类型',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态',
    `version` int NOT NULL DEFAULT 0 COMMENT 'version',
    `related_trans_id` varchar(64) NOT NULL DEFAULT '' COMMENT '关联 trans',
    `asset_id` varchar(64) NOT NULL DEFAULT '' COMMENT '资产 id',
    `asset_type` tinyint NOT NULL DEFAULT 1 COMMENT '资产类型',
    `arrearage_can_use` tinyint NOT NULL DEFAULT 0 COMMENT '欠费时候可用',
    `max_activation_times` int NOT NULL DEFAULT 0 COMMENT '同批激活次数限制',
    `batch_id` varchar(64) NOT NULL DEFAULT '' COMMENT '生成批次 id',
    `record_status` tinyint NOT NULL DEFAULT 1 COMMENT '记录状态',
    `snap_id` varchar(64) NOT NULL DEFAULT '' COMMENT '快照 id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
