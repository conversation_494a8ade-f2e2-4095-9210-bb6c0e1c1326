package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measured/model"
)

func TestStatReqV1RoundTrip(t *testing.T) {
	x := &model.StatReq{
		ItemCode: "fusion:transfer:http:ov",
		DataType: "default",
		UID:      233,
		Begin:    time.Date(2019, 7, 1, 0, 0, 0, 0, time.Local),
		End:      time.Date(2019, 8, 1, 0, 0, 0, 0, time.Local),
		ZoneCode: 3001,
		Debug:    true,

		IsForIdleRespack: true, // 跟 item code 意外地很和谐 🌝
	}

	y, err := BuildPbStatReqV1(x)
	assert.NoError(t, err)

	z, err := BuildModelStatReqV1(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ItemCode, z.ItemCode)
	assert.Equal(t, x.DataType, z.DataType)
	assert.Equal(t, x.UID, z.UID)
	assert.Equal(t, x.Begin.Unix<PERSON>ano(), z.Begin.UnixNano())
	assert.Equal(t, x.End.Unix<PERSON>ano(), z.End.UnixNano())
	assert.Equal(t, x.ZoneCode, z.ZoneCode)
	assert.Equal(t, x.Debug, z.Debug)

	assert.Equal(t, x.IsForIdleRespack, y.IsForIdleRespack)
}
