package service

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/pay-sdk/dict"
	gaeaOp "github.com/qbox/pay-sdk/gaea/client/operations"
	gaeamodels "github.com/qbox/pay-sdk/gaea/models"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/qbox/pay-sdk/price"
	"github.com/qbox/pay-sdk/respack"
	"google.golang.org/protobuf/types/known/timestamppb"
	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/models/enums"
)

const TxnTypePlan = "plan" // 流水字段Type

func calcMonth(
	ctx context.Context,
	epoch time.Time,
	effectType enums.EffectType,
) time.Time {
	month := base.ThisMonth(epoch)
	if effectType == enums.EffectTypeNextMonth {
		month = base.NextMonth(month)
	}
	return month
}

func (s *PlanService) initAllOnlinePlans(
	ctx context.Context,
) (map[enums.PlanLevel]models.Plan, error) {
	logger := logging.GetLogger(ctx)
	plans, err := s.dao.PlanDao.ListOnlinePlans(ctx)
	if err != nil {
		logger.WithError(err).Error("list online plan failed")
		return nil, err
	}
	planMap := make(map[enums.PlanLevel]models.Plan)
	for i, plan := range plans {
		planMap[plan.Level] = *plans[i]
	}
	return planMap, nil
}

func (s *PlanService) initUserSubscribeTable(
	ctx context.Context,
	param *models.SubscribePlanParam,
	month time.Time,
) (*models.UserSubscribe, error) {
	logger := logging.GetLogger(ctx)
	usrSubscribe, err := s.dao.UserSubscribeDao.GetUserSubscribe(
		ctx,
		param.Uid,
		month,
	)
	// 如果订阅表都没有，则是首次订阅
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// month首次订阅
		usrSubscribe = &models.UserSubscribe{
			Month:        month,
			UID:          param.Uid,
			Status:       enums.SubscribeInit,
			Level:        param.Level,
			PacksCode:    param.PackCode,
			SubscribeSrc: param.SubscribeSrc,
		}
		err = s.dao.UserSubscribeDao.Insert(ctx, usrSubscribe)
		if err != nil {
			logger.WithField("usrSubscribe", usrSubscribe).
				WithError(err).Error("insert usrSubscribe failed")
			return nil, err
		}
		return usrSubscribe, nil
	}

	if usrSubscribe.Level == param.Level &&
		usrSubscribe.PacksCode == param.PackCode {
		// plan.level 、pack_code 一样，操作无意义
		return usrSubscribe, nil
	}

	// 对应月份(当月/次月) 已有订阅且和此次订阅的不相同,直接更新,并将 status 重置
	usrSubscribe.Status = enums.SubscribeInit
	usrSubscribe.Level = param.Level
	usrSubscribe.PacksCode = param.PackCode
	usrSubscribe.SubscribeSrc = param.SubscribeSrc
	return usrSubscribe, nil
}

func (s *PlanService) writeChanges(
	ctx context.Context,
	onlinePlanMap map[enums.PlanLevel]models.Plan,
	oldUsrPlan *models.UserPlan,
	usrSubscribe *models.UserSubscribe,
) (changes *models.UserPlanChange, err error) {
	logger := logging.GetLogger(ctx)
	// 兼容处理:实际执行订阅时刻，之前订阅的套餐已经发生了变化的情况(比如套餐删除了)
	onlinePlan, ok := onlinePlanMap[usrSubscribe.Level]
	if !ok {
		err = errors.New("plan not found")
		logger.WithField("usrSubscribe", usrSubscribe).
			WithError(err).Error("online plan not found")
		return nil, err
	}
	_, ok = onlinePlan.SearchPacks(usrSubscribe.PacksCode)
	if !ok {
		// 当前用户 plan 的最新版本中 packs 配置已发生变化
		// 则切换成该plan下的第一个套餐
		usrSubscribe.PacksCode = onlinePlan.FetchFirstPacks().Code
	}

	// 当月还没有 plan
	if oldUsrPlan == nil {
		changes = &models.UserPlanChange{
			ChangeType:      enums.ChangeTypeSubscribeRenew,
			UID:             usrSubscribe.UID,
			Version:         0,
			Level:           "",
			PacksCode:       "",
			TargetVersion:   onlinePlan.Version,
			TargetLevel:     usrSubscribe.Level,
			TargetPacksCode: usrSubscribe.PacksCode,
			ActualVersion:   onlinePlan.Version,
			ActualLevel:     usrSubscribe.Level,
			ActualPacksCode: usrSubscribe.PacksCode,
			EffectMonth:     usrSubscribe.Month,
			PaymentStatus:   enums.PaymentStatusUnpaid,
			CurrencyType:    base.CurrencyTypeUSD,
			PaymentAmount:   onlinePlan.Price,
		}
	} else {
		// 当月已有 plan, 需要判断是升级、降级、还是切换套餐
		switch oldUsrPlan.Level.Compare(usrSubscribe.Level) {
		case -1:
			// 降级
			// enterprise --> free
			// pro、business --> free
			// business --> pro
			// 写入初始 changes
			changes = &models.UserPlanChange{
				ChangeType:      enums.ChangeTypeSubscribeDegrade,
				UID:             usrSubscribe.UID,
				Version:         oldUsrPlan.Version,
				Level:           oldUsrPlan.Level,
				PacksCode:       oldUsrPlan.PacksCode,
				TargetVersion:   onlinePlan.Version,
				TargetLevel:     usrSubscribe.Level,
				TargetPacksCode: usrSubscribe.PacksCode,
				ActualVersion:   onlinePlan.Version,
				ActualLevel:     usrSubscribe.Level,
				ActualPacksCode: usrSubscribe.PacksCode,
				EffectMonth:     usrSubscribe.Month,
				PaymentStatus:   enums.PaymentStatusUnpaid,
				CurrencyType:    base.CurrencyTypeUSD,
				PaymentAmount:   onlinePlan.Price,
			}
		case 0:
			// 切换套餐
			changes = &models.UserPlanChange{
				ChangeType:      enums.ChangeTypeSubscribeSwitchPack,
				UID:             usrSubscribe.UID,
				Version:         oldUsrPlan.Version,
				Level:           oldUsrPlan.Level,
				PacksCode:       oldUsrPlan.PacksCode,
				TargetVersion:   oldUsrPlan.Version,
				TargetLevel:     usrSubscribe.Level,
				TargetPacksCode: usrSubscribe.PacksCode,
				ActualVersion:   onlinePlan.Version,
				ActualLevel:     usrSubscribe.Level,
				ActualPacksCode: usrSubscribe.PacksCode,
				EffectMonth:     usrSubscribe.Month,
				PaymentStatus:   enums.PaymentStatusNone,
				PaymentAmount:   base.NewNMoney(0),
				PaymentSN:       nil, // 切换套餐不会设计重新支付，此字段必定为空
			}
		case 1: // 升级
			// free --> pro、business、enterprise
			// pro --> business、enterprise
			// business --> enterprise
			// 写入初始 changes
			changes = &models.UserPlanChange{
				ChangeType:      enums.ChangeTypeSubscribeUpgrade,
				UID:             usrSubscribe.UID,
				Version:         oldUsrPlan.Version,
				Level:           oldUsrPlan.Level,
				PacksCode:       oldUsrPlan.PacksCode,
				TargetVersion:   onlinePlan.Version,
				TargetLevel:     usrSubscribe.Level,
				TargetPacksCode: usrSubscribe.PacksCode,
				ActualVersion:   onlinePlan.Version,
				ActualLevel:     usrSubscribe.Level,
				ActualPacksCode: usrSubscribe.PacksCode,
				EffectMonth:     usrSubscribe.Month,
				PaymentStatus:   enums.PaymentStatusUnpaid,
				CurrencyType:    base.CurrencyTypeUSD,
				PaymentAmount:   onlinePlan.Price,
			}
		}
	}

	// 写入 changes
	err = s.dao.UserPlanChangeDao.Insert(ctx, changes)
	if err != nil {
		logger.WithField("changes", changes).
			WithError(err).Error("insert changes failed")
		return nil, err
	}
	return changes, nil
}

func (s *PlanService) tryPaying(
	ctx context.Context,
	changes *models.UserPlanChange,
) (*models.UserPlanChange, error) {
	logger := logging.GetLogger(ctx)
	if changes.PaymentStatus == enums.PaymentStatusRefund ||
		changes.PaymentStatus == enums.PaymentStatusPaid {
		return nil, errors.New("wrong payment status")
	}
	if changes.PaymentStatus == enums.PaymentStatusNone ||
		changes.ActualLevel == enums.LevelEnterprise ||
		changes.ActualLevel == enums.LevelFree ||
		(changes.PaymentAmount != nil && changes.PaymentAmount.MustGetBigMoneyI64() <= 0) {
		// free 、enterprise 或者金额为 0，则不需要扣费
		changes.PaymentStatus = enums.PaymentStatusNone
	} else {
		// 请求 paypal 进行扣费
		err := s.paypalDeduct(ctx, changes)
		if err != nil {
			// 标记这次扣费失败
			changes.PaymentStatus = enums.PaymentStatusFailed
			err = s.dao.UserPlanChangeDao.Update(ctx, changes)
			if err != nil {
				logger.WithField("changes", changes).
					WithError(err).Error("update changes failed")
				return nil, err
			}
			return nil, errors.New("deduct failed")
		}

		changes.PaymentStatus = enums.PaymentStatusPaid
		changes.PaymentTime = time.Now().In(tz.MustLocationFromCtx(ctx))
	}

	// 扣费完成,更新 changes
	err := s.dao.UserPlanChangeDao.Update(ctx, changes)
	if err != nil {
		logger.WithField("changes", changes).
			WithError(err).Error("update changes failed")
		return nil, err
	}

	return changes, nil
}

func (s *PlanService) tryUnbindRights(
	ctx context.Context,
	oldUsrPlan *models.UserPlan,
) error {
	logger := logging.GetLogger(ctx)
	if oldUsrPlan == nil || oldUsrPlan.Level == enums.LevelEnterprise {
		// 当前不存在 plan, 或者是企业版 则无需解绑
		return nil
	}
	// 解绑资源包
	_, err := s.respack.DeleteRespackBindingByExcodes(
		ctx,
		&respack.DeleteByUIDExcodesParam{
			Uid:           oldUsrPlan.UID,
			Excodes:       []string{oldUsrPlan.GenRightsBindingExcode()},
			CancelRefresh: true,
		},
	)
	if err != nil {
		logger.WithField("oldUserPlan", oldUsrPlan).
			WithError(err).Error("del user respack failed")
		return err
	}

	// 禁用折扣价格表
	if oldUsrPlan.PriceTableID <= 0 {
		return nil
	}
	_, err = s.priceV4.DisablePriceTable(
		ctx,
		&price.IDParam{
			Id: oldUsrPlan.PriceTableID,
		},
	)
	if err != nil {
		logger.WithField("oldUsrPlan", oldUsrPlan).
			WithError(err).Error("disable price table failed")
		return err
	}
	return nil
}

func (s *PlanService) writeUsrPlan(
	ctx context.Context,
	oldUsrPlan *models.UserPlan,
	changes *models.UserPlanChange,
) (*models.UserPlan, error) {
	logger := logging.GetLogger(ctx)
	newUsrPlan := &models.UserPlan{
		Month:                changes.EffectMonth,
		UID:                  changes.UID,
		Version:              changes.ActualVersion,
		Level:                changes.ActualLevel,
		PacksCode:            changes.ActualPacksCode,
		ChangeCode:           changes.Code,
		PaymentSN:            changes.PaymentSN,
		RemainingSwitchCount: 1,
		EndTime:              base.NextMonth(changes.EffectMonth),
		UpdatedAt:            time.Now().In(tz.MustLocationFromCtx(ctx)),
	}
	if oldUsrPlan == nil {
		err := s.dao.UserPlanDao.Insert(ctx, newUsrPlan)
		if err != nil {
			logger.WithField("userPlan", newUsrPlan).
				WithError(err).Error("insert usrPlan failed")
			return nil, err
		}
		return newUsrPlan, nil
	}

	newUsrPlan.ID = oldUsrPlan.ID

	if changes.ChangeType == enums.ChangeTypeSubscribeSwitchPack {
		if oldUsrPlan.RemainingSwitchCount > 0 {
			newUsrPlan.RemainingSwitchCount = oldUsrPlan.RemainingSwitchCount - 1
		}
	}

	err := s.dao.UserPlanDao.Update(ctx, newUsrPlan)
	if err != nil {
		logger.WithField("userPlan", newUsrPlan).
			WithError(err).Error("update usrPlan failed")
		return nil, err
	}
	return newUsrPlan, nil
}

func (s *PlanService) bindRights(
	ctx context.Context,
	onlinePlanMap map[enums.PlanLevel]models.Plan,
	newUsrPlan *models.UserPlan,
) (*models.UserPlan, error) {
	logger := logging.GetLogger(ctx)
	// 企业版无需权益绑定
	if newUsrPlan.Level == enums.LevelEnterprise {
		return newUsrPlan, nil
	}
	plan := onlinePlanMap[newUsrPlan.Level]
	pack, found := plan.SearchPacks(newUsrPlan.PacksCode)
	if !found {
		err := errors.New("packs not found")
		logger.WithField("userPlan", newUsrPlan).
			WithError(err).Error("packs not found in online plan")
		return newUsrPlan, err
	}

	effectTime, _ := base.NewDay(newUsrPlan.Month).Time(ctx)
	deadTime, _ := base.NewDay(newUsrPlan.EndTime).Time(ctx)
	bindingExcode := newUsrPlan.GenRightsBindingExcode()

	_, err := resultgroup.ParallelMap(
		pack.Rights,
		func(rights models.PackRights) (any, error) {
			if rights.Type != enums.RightsTypeFreeRespack {
				return nil, nil
			}
			// 只有免费资源包权益需要绑定资源包
			setParam := respack.CreateRespackBindingParam{
				Uid:           newUsrPlan.UID,
				ZoneCodes:     []uint64{uint64(rights.Zone)},
				RespackCode:   rights.GenFreeRespackID(),
				EffectTime:    timestamppb.New(effectTime),
				DeadTime:      timestamppb.New(deadTime),
				Excode:        bindingExcode,
				Quantity:      1,
				IdleStart:     "",
				IdleEnd:       "",
				CancelRefresh: true,
			}
			_, err := s.respack.CreateRespackBinding(ctx, &setParam)
			if err != nil {
				logger.WithField("setParam", setParam).
					WithError(err).Error("bind respack failed")
				return nil, err
			}
			return nil, nil
		},
	)
	if err != nil {
		return newUsrPlan, err
	}
	// 开始创建用户折扣报价单
	itemCodes := make([]string, 0)
	zones := make([]uint32, 0)
	for _, right := range pack.Rights {
		if right.Type != enums.RightsTypePriceDiscount {
			continue
		}
		if right.IsCombo {
			continue
		}
		itemCodes = append(itemCodes, right.ItemCode)
		zones = append(zones, right.Zone)
	}
	if len(itemCodes) <= 0 || len(zones) <= 0 {
		return newUsrPlan, nil
	}

	itemsMap, zonesMap, dataTypesMap, algsMap, unitsMap, err := s.fetchItemConfig(
		ctx, itemCodes, zones,
	)
	if err != nil {
		return newUsrPlan, err
	}
	itemBasePriceMap, err := s.fetchItemPrice(ctx, itemCodes)
	if err != nil {
		return newUsrPlan, err
	}

	priceTableID, err := s.createUserPriceTable(
		ctx,
		plan,
		pack,
		itemBasePriceMap,
		itemsMap, dataTypesMap, unitsMap,
		zonesMap, algsMap, newUsrPlan,
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"plan":             plan,
			"pack":             pack,
			"itemBasePriceMap": itemBasePriceMap,
			"itemsMap":         itemsMap,
			"dataTypesMap":     dataTypesMap,
			"unitsMap":         unitsMap,
			"zonesMap":         zonesMap,
			"algsMap":          algsMap,
			"newUsrPlan":       newUsrPlan,
		}).WithError(err).
			Error("create user price table failed")
		return newUsrPlan, err
	}
	if priceTableID == 0 {
		return newUsrPlan, nil
	}
	newUsrPlan.PriceTableID = priceTableID
	err = s.dao.UserPlanDao.Update(ctx, newUsrPlan)
	if err != nil {
		logger.WithField("userPlan", newUsrPlan).
			WithError(err).Error("update usrPlan failed")
		return newUsrPlan, err
	}
	return newUsrPlan, nil
}

// createUserPriceTable 创建用户折扣报价单
func (s *PlanService) createUserPriceTable(
	ctx context.Context,
	plan models.Plan,
	pack *models.PlanPacks,
	itemBasePriceMap map[string]map[int64]*models.Price,
	itemsMap map[string]*dict.Item,
	dataTypesMap map[uint64]*dict.ItemDataType,
	unitsMap map[uint64]*dict.ItemDataTypeUnit,
	zonesMap map[uint64]*dict.Zone,
	algsMap map[uint64]*dict.ItemDataTypeAlgorithm,
	newUsrPlan *models.UserPlan,
) (uint64, error) {
	logger := logging.GetLogger(ctx)
	// 聚合成一张报价
	vipPrices := price.PricesParam{
		Type:       price.VIP,
		Remark:     fmt.Sprintf("discount rights. plan %s, pack: %s", plan.Level, pack.Code),
		ItemPrices: make([]*price.ItemPriceParam, 0),
	}
	for _, right := range pack.Rights {
		if right.Type != enums.RightsTypePriceDiscount {
			continue
		}
		if right.IsCombo {
			continue
		}
		zonePriceMap, ok := itemBasePriceMap[right.ItemCode]
		if !ok {
			continue
		}
		zonePrice, ok := zonePriceMap[int64(right.Zone)]
		if !ok {
			continue
		}

		itemID := itemsMap[right.ItemCode].GetId()
		dataTypeID := dataTypesMap[itemID].GetId()

		stairs := make([]*price.StairPriceParam, len(zonePrice.Ranges))
		for i, r := range zonePrice.Ranges {
			stairs[i] = &price.StairPriceParam{
				UnitId:   unitsMap[dataTypeID].GetId(),
				Quantity: r.To,
				Price: uint64(
					base.MustParseNMoney(r.Price).
						MulI64(int64(right.Discount)).
						QuoI64(10000). // rights.Discount 是 x10000 的整数
						MustGetBigMoneyI64(),
				),
			}
		}

		vipPrices.ItemPrices = append(
			vipPrices.ItemPrices,
			&price.ItemPriceParam{
				ItemId:          itemID,
				ZoneId:          zonesMap[uint64(right.Zone)].GetId(),
				AlgorithmId:     algsMap[dataTypesMap[itemID].GetId()].GetId(),
				StairPriceType:  zonePrice.Type,
				IsDisabled:      false,
				UnitRate:        0,
				CumulativeCycle: price.CUMULATIVE_TYPE_MONTH,
				BillPeriodType:  price.BILL_PERIOD_TYPE_MONTH,
				StairPrices:     stairs,
				EffectTime:      timestamppb.New(base.ThisMonth(newUsrPlan.Month)),
				DeadTime:        timestamppb.New(base.NextMonth(newUsrPlan.Month)),
				CurrencyType:    base.CurrencyTypeUSD.String(),
			},
		)
	}

	if len(vipPrices.ItemPrices) <= 0 {
		// 没有计费项折扣，则不需要创建折扣价格表
		return 0, nil
	}
	priceTable, err := s.priceV4.CreateUserPrice(ctx, &price.UserPriceParam{
		Uid:        newUsrPlan.UID,
		Prices:     &vipPrices,
		IsDisabled: false,
	})
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":    newUsrPlan.UID,
			"prices": vipPrices,
		}).WithError(err).Error("create user price table failed")
		return 0, err
	}
	logger.WithFields(logrus.Fields{
		"newUsrPlan": newUsrPlan.UID,
		"priceTable": priceTable.Id,
		"prices":     vipPrices,
	}).Info("create user price success")
	return priceTable.GetId(), nil
}

func (s *PlanService) fetchItemConfig(
	ctx context.Context,
	itemCodes []string,
	zones []uint32,
) (
	map[string]*dict.Item, // itemCode --> item
	map[uint64]*dict.Zone, // zoneCode --> zone
	map[uint64]*dict.ItemDataType, // itemID --> dataType
	map[uint64]*dict.ItemDataTypeAlgorithm, // dataTypeID --> alg
	map[uint64]*dict.ItemDataTypeUnit, // dataTypeID --> alg
	error,
) {
	logger := logging.GetLogger(ctx)

	items, err := resultgroup.ParallelMap(
		base.UniqueStringSlice(itemCodes),
		func(itemCode string) (*dict.Item, error) {
			item, e := s.dict.GetItemByCode(ctx, &dict.CodeParam{
				Code: itemCode,
			})
			if e != nil {
				return nil, e
			}
			return item, nil
		},
	)
	if err != nil {
		logger.WithField("itemCodes", itemCodes).
			WithError(err).
			Error("get item failed")
		return nil, nil, nil, nil, nil, err
	}
	itemsMap := make(map[string]*dict.Item) // itemCode --> item
	for _, item := range items {
		itemsMap[item.GetCode()] = item
	}

	zzs, err := resultgroup.ParallelMap(
		base.UniqueIntSlice(zones),
		func(z uint32) (*dict.Zone, error) {
			zone, e := s.dict.GetZoneByCode(ctx, &dict.ZoneCodeParam{
				Code: int64(z),
			})
			if e != nil {
				return nil, e
			}
			return zone, nil
		},
	)
	if err != nil {
		logger.WithField("zones", zones).
			WithError(err).
			Error("get zone failed")
		return nil, nil, nil, nil, nil, err
	}
	zonesMap := make(map[uint64]*dict.Zone) // zoneCode --> zone
	for _, zz := range zzs {
		zonesMap[uint64(zz.GetCode())] = zz
	}

	dataTypes, err := resultgroup.ParallelMap(
		base.UniqueStringSlice(itemCodes),
		func(itemCode string) (*dict.ItemDataType, error) {
			typs, e := s.dict.ListItemDataTypesByItemCode(
				ctx,
				&dict.CodePagingParam{
					Code:     itemCode,
					Page:     1,
					PageSize: 200,
				},
			)
			if e != nil {
				return nil, nil
			}
			for _, dataType := range typs.GetItemDataTypes() {
				if dataType.GetType() == "default" { // v4的默认是 default
					return dataType, nil
				}
			}
			return nil, errors.New("none default data type")
		},
	)
	if err != nil {
		logger.WithField("itemCodes", itemCodes).
			WithError(err).
			Error("get item default data type failed")
		return nil, nil, nil, nil, nil, err
	}
	dataTypesMap := make(map[uint64]*dict.ItemDataType) // itemID --> dataType
	for _, dataType := range dataTypes {
		dataTypesMap[dataType.GetItemId()] = dataType
	}

	algs, err := resultgroup.ParallelMap(
		dataTypes,
		func(param *dict.ItemDataType) (*dict.ItemDataTypeAlgorithm, error) {
			dictAlgs, e := s.dict.ListItemDataTypeAlgorithmsByItemDataTypeID(
				ctx, &dict.IDPagingParam{
					Id:       param.GetId(),
					Page:     1,
					PageSize: 200,
				},
			)
			if e != nil {
				return nil, e
			}

			for _, dictAlgorithm := range dictAlgs.GetItemDataTypeAlgorithms() {
				if dictAlgorithm.GetIsDefault() {
					return dictAlgorithm, nil
				}
			}
			return nil, errors.New("none default algorithm")
		},
	)
	if err != nil {
		logger.WithField("itemCodes", itemCodes).
			WithField("dataTypes", dataTypes).
			WithError(err).
			Error("get data type default algorithm failed")
		return nil, nil, nil, nil, nil, err
	}
	algsMap := make(map[uint64]*dict.ItemDataTypeAlgorithm) // dataTypeID --> alg
	for _, alg := range algs {
		algsMap[alg.GetDataTypeId()] = alg
	}

	units, err := resultgroup.ParallelMap(
		dataTypes,
		func(param *dict.ItemDataType) (*dict.ItemDataTypeUnit, error) {
			units, e := s.dict.ListItemDataTypeUnitsByItemDataTypeID(
				ctx,
				&dict.IDPagingParam{
					Id:       param.GetId(),
					Page:     1,
					PageSize: 1000,
				},
			)
			if e != nil {
				return nil, e
			}

			for _, unit := range units.GetItemDataTypeUnits() {
				if unit.IsPriceAnchor {
					return unit, nil
				}
			}
			return nil, errors.New("none anchor unit")
		},
	)
	if err != nil {
		logger.WithField("itemCodes", itemCodes).
			WithField("dataTypes", dataTypes).
			WithError(err).
			Error("get data type price anchor failed")
		return nil, nil, nil, nil, nil, err
	}
	unitsMap := make(map[uint64]*dict.ItemDataTypeUnit) // dataTypeID --> unit
	for _, unit := range units {
		unitsMap[unit.GetDataTypeId()] = unit
	}
	return itemsMap, zonesMap, dataTypesMap, algsMap, unitsMap, nil
}

func (s *PlanService) paypalDeduct(
	ctx context.Context,
	changes *models.UserPlanChange,
) error {
	logger := logging.GetLogger(ctx)
	changes.PaymentSN = &changes.Code

	logger.WithField("changes", changes).Info("paypal deduct")

	amount := changes.PaymentAmount.CeilToMoney().MustGetMoney().ToInt64()
	pUid := uint32(changes.UID)
	tpe := TxnTypePlan
	res, err := s.gaea.Operations.PaypalCharge(&gaeaOp.PaypalChargeParams{
		ReqPaypalCharge: &gaeamodels.ReqPaypalCharge{
			Amount: &amount,
			Excode: changes.PaymentSN,
			UID:    &pUid,
			Type:   &tpe,
		},
		Context: ctx,
	})
	if err != nil || res.GetPayload().Code != http.StatusOK {
		if err == nil {
			err = errors.New(res.GetPayload().Message)
		}
		logger.WithField("changes", changes).
			WithError(err).Error("paypal deduct failed")
		return err
	}
	return nil
}

func (s *PlanService) refundPrevious(
	ctx context.Context,
	oldUsrPlan *models.UserPlan,
	changes *models.UserPlanChange,
) error {
	if oldUsrPlan == nil ||
		oldUsrPlan.Level == enums.LevelFree ||
		oldUsrPlan.Level == enums.LevelEnterprise {
		// 之前没有 plan 或者 是 free/enterprise，则无需退款
		return nil
	}
	if changes.ChangeType == enums.ChangeTypeSubscribeSwitchPack {
		// 切换套餐操作也不需要退款
		return nil
	}

	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"changes": changes,
		"old":     oldUsrPlan},
	).Info("paypal refund")

	typ := TxnTypePlan
	pUid := int64(changes.UID)

	res, err := s.gaea.Operations.PaypalRefundAndWithdraw(&gaeaOp.PaypalRefundAndWithdrawParams{
		ReqPaypalWithdraw: &gaeamodels.ReqPaypalWithdraw{
			Description: "",
			Excode:      oldUsrPlan.PaymentSN,
			Type:        &typ,
			UID:         &pUid,
		},
		Context: ctx,
	})
	if err != nil || res.GetPayload().Code != http.StatusOK {
		if err == nil {
			err = errors.New(res.GetPayload().Message)
		}
		logger.WithField("changes", changes).
			WithError(err).Error("paypal refund failed")
		return err
	}

	err = s.dao.UserPlanChangeDao.MarkRefund(
		ctx,
		oldUsrPlan.ChangeCode,
	)
	if err != nil {
		logger.WithField("changes", changes).
			WithError(err).Error("mark refund failed")
		return err
	}

	return nil
}

func (s *PlanService) markSubscribe(
	ctx context.Context,
	usrSubscribe *models.UserSubscribe,
	status enums.SubscribeStatus,
) error {
	logger := logging.GetLogger(ctx)
	usrSubscribe.Status = status

	err := s.dao.UserSubscribeDao.Update(ctx, usrSubscribe)
	if err != nil {
		logger.WithField("userSubscribe", usrSubscribe).
			WithError(err).Error("update userSubscribe failed")
		return err
	}
	return nil
}

func (s *PlanService) changeUsrPlan(
	ctx context.Context,
	usrPlan *models.UserPlan,
	changes *models.UserPlanChange,
) error {
	switch changes.ChangeType {

	case enums.ChangeTypeSubscribeRenew:
		// 续订，change.month 还没有 plan
		// none -> plan, 绑定

	case enums.ChangeTypeSubscribeSwitchPack:
		// 切换，change.month 已有 plan
		// same plan -> same plan, 解绑 && 绑定

	case enums.ChangeTypeSubscribeUpgrade:
		// 升级，change.month 已有 plan,且可能需要退款
		// free -> pro 无需退款,解绑 && 绑定
		// free -> business 无需退款,解绑 && 绑定
		// free -> enterprise 无需退款,解绑 && 无绑定
		// pro -> business 需退款,解绑 && 绑定
		// pro -> enterprise 需退款,解绑 && 无绑定
		// business -> enterprise 需退款,解绑 && 无绑定

	case enums.ChangeTypeSubscribeDegrade:
		// 降级，change.month 已有 plan,且可能需要退款
		// enterprise -> business/pro/free 无需退款, 绑定
		// business -> pro/free 需退 business, 解绑 && 绑定
		// pro -> free 需退 pro, 解绑 && 绑定
	}

	return nil
}

func (s *PlanService) fetchUsrPlan(
	ctx context.Context,
	uid uint64,
	month time.Time,
) (*models.UserPlan, error) {
	usrPlan, _, err := s.GetUserCurrentPlan(
		ctx, uid, month,
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return usrPlan, nil
}

func (s *PlanService) cleanNextMonthSubscribe(
	ctx context.Context,
	month time.Time,
	subscribe *models.UserSubscribe,
) error {
	nextMonth := base.NextMonth(month)
	err := s.dao.UserSubscribeDao.Remove(ctx, subscribe.UID, nextMonth)
	if err != nil {
		return err
	}
	return nil
}
