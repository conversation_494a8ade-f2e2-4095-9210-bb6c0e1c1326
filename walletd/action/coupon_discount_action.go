package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// GetCouponDiscountByID 通过ID获取折扣优惠券
func (a *WalletAction) GetCouponDiscountByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CouponDiscount, error) {
	discount, err := a.walletBizSrv.GetCouponDiscountByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponDiscount(discount)
}

// GetCouponDiscountByCode 通过 code 获取折扣优惠券
func (a *WalletAction) GetCouponDiscountByCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.CouponDiscount, error) {
	discount, err := a.walletBizSrv.GetCouponDiscountByCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponDiscount(discount)
}

// CreateCouponDiscount 创建折扣优惠券
func (a *WalletAction) CreateCouponDiscount(
	ctx context.Context,
	discount *pb.CouponDiscount,
) (*pb.CouponDiscount, error) {
	_discount, err := adapter.BuildCouponDiscount(discount)
	if err != nil {
		return nil, err
	}
	_discount, err = a.walletBizSrv.CreateCouponDiscount(ctx, _discount)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponDiscount(_discount)
}

// UpdateCouponDiscountByID 按照 ID 更新折扣优惠券
func (a *WalletAction) UpdateCouponDiscountByID(
	ctx context.Context,
	param *pb.IDCouponDiscountParam,
) (*pb.CouponDiscount, error) {
	_discount, err := adapter.BuildCouponDiscount(param.GetCouponDiscount())
	if err != nil {
		return nil, err
	}
	_discount, err = a.walletBizSrv.UpdateCouponDiscountByID(ctx, param.GetId(), _discount)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponDiscount(_discount)
}

// UpdateCouponDiscountByCode 按照 code 更新折扣优惠券
func (a *WalletAction) UpdateCouponDiscountByCode(
	ctx context.Context,
	param *pb.CodeCouponDiscountParam,
) (*pb.CouponDiscount, error) {
	_discount, err := adapter.BuildCouponDiscount(param.GetCouponDiscount())
	if err != nil {
		return nil, err
	}
	_discount, err = a.walletBizSrv.UpdateCouponDiscountByCode(ctx, param.GetCode(), _discount)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponDiscount(_discount)
}

// DeleteCouponDiscountByID 按照 ID 删除折扣优惠券
func (a *WalletAction) DeleteCouponDiscountByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CouponDiscount, error) {
	_discount, err := a.walletBizSrv.DeleteCouponDiscountByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponDiscount(_discount)
}

// DeleteCouponDiscountByCode 按照 code 删除折扣优惠券
func (a *WalletAction) DeleteCouponDiscountByCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.CouponDiscount, error) {
	_discount, err := a.walletBizSrv.DeleteCouponDiscountByCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponDiscount(_discount)
}

// ListAllCouponDiscounts 列举所有折扣优惠券
func (a *WalletAction) ListAllCouponDiscounts(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.CouponDiscountList, error) {
	count, err := a.walletBizSrv.CountAllCouponDiscounts(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	discounts, err := a.walletBizSrv.ListCouponDiscountsByConds(ctx, "", offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponDiscountList{
		Count:           count,
		CouponDiscounts: make([]*pb.CouponDiscount, len(discounts)),
	}
	for i, discount := range discounts {
		_discount, err := adapter.BuildPbCouponDiscount(&discount)
		if err != nil {
			return nil, err
		}
		list.CouponDiscounts[i] = _discount
	}
	return list, nil
}

// CountAllCouponDiscounts 获取所有折扣优惠券数量
func (a *WalletAction) CountAllCouponDiscounts(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountAllCouponDiscounts(ctx)
	return &pb.CountParam{Count: count}, err
}

// ListCouponDiscountsByScopeID 通过计费范围列举折扣优惠券
func (a *WalletAction) ListCouponDiscountsByScopeID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.CouponDiscountList, error) {
	count, err := a.walletBizSrv.CountCouponDiscountsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	discounts, err := a.walletBizSrv.ListCouponDiscountsByScopeID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponDiscountList{
		Count:           count,
		CouponDiscounts: make([]*pb.CouponDiscount, len(discounts)),
	}
	for i, discount := range discounts {
		_discount, err := adapter.BuildPbCouponDiscount(&discount)
		if err != nil {
			return nil, err
		}
		list.CouponDiscounts[i] = _discount
	}
	return list, nil
}

// CountCouponDiscountsByScopeID 通过计费范围获取折扣优惠券数量
func (a *WalletAction) CountCouponDiscountsByScopeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountCouponDiscountsByScopeID(ctx, param.GetId())
	return &pb.CountParam{Count: count}, err
}

// ListCouponDiscountsByUID 通过用户ID列举折扣优惠券
func (a *WalletAction) ListCouponDiscountsByUID(
	ctx context.Context,
	param *pb.UIDPagingParam,
) (*pb.CouponDiscountList, error) {
	count, err := a.walletBizSrv.CountCouponDiscountsByUID(ctx, param.GetUid())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	discounts, err := a.walletBizSrv.ListCouponDiscountsByUID(ctx, param.GetUid(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponDiscountList{
		Count:           count,
		CouponDiscounts: make([]*pb.CouponDiscount, len(discounts)),
	}
	for i, discount := range discounts {
		_discount, err := adapter.BuildPbCouponDiscount(&discount)
		if err != nil {
			return nil, err
		}
		list.CouponDiscounts[i] = _discount
	}
	return list, nil
}

// CountCouponDiscountsByUID 通过用户ID获取折扣优惠券数量
func (a *WalletAction) CountCouponDiscountsByUID(
	ctx context.Context,
	param *pb.UIDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountCouponDiscountsByUID(ctx, param.GetUid())
	return &pb.CountParam{Count: count}, err
}

// SearchCouponDiscounts 模糊搜索 CouponDiscounts
func (a *WalletAction) SearchCouponDiscounts(
	ctx context.Context,
	param *pb.SearchCouponsParam,
) (*pb.CouponDiscountList, error) {
	count, err := a.walletBizSrv.CountAllCouponDiscounts(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	discounts, err := a.walletBizSrv.ListCouponDiscountsByConds(ctx, param.Pattern, offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponDiscountList{
		Count:           count,
		CouponDiscounts: make([]*pb.CouponDiscount, len(discounts)),
	}
	for i, discount := range discounts {
		_discount, err := adapter.BuildPbCouponDiscount(&discount)
		if err != nil {
			return nil, err
		}
		list.CouponDiscounts[i] = _discount
	}
	return list, nil
}
