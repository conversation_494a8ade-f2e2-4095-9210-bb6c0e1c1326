package enums

// ChangeType 变更类型
type ChangeType int

const (
	// ChangeTypeSubscribeRenew 续订
	ChangeTypeSubscribeRenew = 1
	// ChangeTypeSubscribeSwitchPack 切换套餐
	ChangeTypeSubscribeSwitchPack = 2
	// ChangeTypeSubscribeUpgrade 升级
	ChangeTypeSubscribeUpgrade = 3
	// ChangeTypeSubscribeDegrade 降级
	ChangeTypeSubscribeDegrade = 4
)

// Valid a change type
func (t ChangeType) Valid() bool {
	switch t {
	case ChangeTypeSubscribeRenew, ChangeTypeSubscribeSwitchPack,
		ChangeTypeSubscribeUpgrade, ChangeTypeSubscribeDegrade:
		return true
	default:
		return false
	}
}
