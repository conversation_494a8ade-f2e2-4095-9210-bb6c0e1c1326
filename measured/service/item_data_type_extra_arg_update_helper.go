package service

import (
	"context"

	"qiniu.io/pay/measured/model"
)

func itemDataTypeExtraArgsToMap(
	l []model.ItemDataTypeExtraArg,
) map[string]model.ItemDataTypeExtraArg {
	result := make(map[string]model.ItemDataTypeExtraArg, len(l))
	for _, x := range l {
		result[x.Key] = x
	}
	return result
}

type SyncItemDataTypeExtraArgsParam struct {
	ItemDataTypeID uint64
	Args           []model.ItemDataTypeExtraArg
}

// SyncItemDataTypeExtraArgs 为给定的计费项数据类型 ID 同步计费项数据类型计量查询附加参数配置
func (s *MeasureBizService) SyncItemDataTypeExtraArgs(
	ctx context.Context,
	params *SyncItemDataTypeExtraArgsParam,
) error {
	// 拉出原先这个 ItemDataType 的全量 ItemDataTypeExtraArg 列表
	orig, err := s.ListItemDataTypeExtraArgsByItemDataTypeID(ctx, params.ItemDataTypeID, 0, -1)
	if err != nil {
		return err
	}

	// 因为这边的两个列表按价格表 v3 语义，都是从 map 生成的，所以不会有 key 重复的问题
	// 傻逼的是，为了实现 diff，这边还要再把 list 转回 map。。。
	origMap := itemDataTypeExtraArgsToMap(orig)
	targetMap := itemDataTypeExtraArgsToMap(params.Args)

	// 创建原先没有的
	for k, newObj := range targetMap {
		if _, ok := origMap[k]; ok {
			// 原先已经有了，这一波不处理
			continue
		}

		_, err := s.CreateItemDataTypeExtraArg(ctx, &newObj)
		if err != nil {
			return err
		}
	}

	// 删除应该删除的
	for k, origObj := range origMap {
		if _, ok := targetMap[k]; ok {
			// 这个 key 的记录还在
			continue
		}

		_, err := s.DeleteItemDataTypeExtraArgByID(ctx, origObj.ID)
		if err != nil {
			return err
		}
	}

	// 更新变化了的
	for k, newObj := range targetMap {
		origObj, ok := origMap[k]
		if !ok {
			// 一上来已经处理过了
			continue
		}

		err := s.syncOneItemDataTypeExtraArg(ctx, &origObj, &newObj)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *MeasureBizService) syncOneItemDataTypeExtraArg(
	ctx context.Context,
	orig *model.ItemDataTypeExtraArg,
	target *model.ItemDataTypeExtraArg,
) error {
	// 因为调用这个 helper 的外层逻辑的更新姿势不会动到 key，所以只检查 value
	if orig.Value == target.Value {
		return nil
	}

	newObj := *orig
	newObj.Value = target.Value
	err := s.dao.ItemDataTypeExtraArg.Save(&newObj)
	if err != nil {
		return err
	}

	return nil
}
