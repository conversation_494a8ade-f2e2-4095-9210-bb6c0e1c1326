package stat

import (
	"net/url"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/legacystat/common"
	mppb "github.com/qbox/pay-sdk/measureproxy"
)

const magicFieldHost = "__host__"

var errInvalidStatSrc = errors.New("invalid stat source kind")
var errInvalidKodoHost = errors.New("invalid kodo host spec in query string")

// statSrcKindFromV3Extras 从 v3 风格计量源常量转换为 measureproxy 计量源枚举类型
//
// 之所以需要 statQuery 是因为需要区分 kodobill 与 mergeapi 计量源
func statSrcKindFromV3Extras(
	v3StatSrcType common.V3StatSrc,
	statQuery string,
) (mppb.StatSrc, error) {
	switch v3StatSrcType {
	case common.V3StatSrcKodo:
		result, err := parseKodoStatSrc(statQuery)
		if err != nil {
			return mppb.STAT_SRC_UNKNOWN, errors.Trace(err)
		}
		return result, nil
	case common.V3StatSrcFusion:
		return mppb.STAT_SRC_FUSION, nil
	case common.V3StatSrcDora:
		return mppb.STAT_SRC_DORA, nil
	case common.V3StatSrcUfop2:
		return mppb.STAT_SRC_UFOP2, nil
	case common.V3StatSrcPili:
		return mppb.STAT_SRC_PILI, nil
	case common.V3StatSrcDistribution:
		return mppb.STAT_SRC_DISTRIBUTION, nil
	case common.V3StatSrcPandora:
		return mppb.STAT_SRC_PANDORA, nil
	case common.V3StatSrcQvm:
		return mppb.STAT_SRC_QVM, nil
	case common.V3StatSrcLinking:
		return mppb.STAT_SRC_LINKING, nil
	case common.V3StatSrcSms:
		return mppb.STAT_SRC_SMS, nil
	case common.V3StatSrcQvs:
		return mppb.STAT_SRC_QVS, nil
	case common.V3StatSrcRtc:
		return mppb.STAT_SRC_RTC, nil
	case common.V3StatSrcPcdn:
		return mppb.STAT_SRC_PCDN, nil
	case common.V3StatSrcUms:
		return mppb.STAT_SRC_UMS, nil
	case common.V3StatSrcAvig:
		return mppb.STAT_SRC_AVIG, nil
	case common.V3StatSrcVrman:
		return mppb.STAT_SRC_VRMAN, nil
	case common.V3StatSrcMikustream:
		return mppb.STAT_SRC_MIKUSTREAM, nil
	case common.V3StatSrcGPUPool:
		return mppb.STAT_SRC_GPUPOOL, nil
	case common.V3StatSrcAIToken:
		return mppb.STAT_SRC_AITOKEN, nil
	default:
		return mppb.STAT_SRC_UNKNOWN, errors.Trace(errInvalidStatSrc)
	}
}

func parseKodoStatSrc(statQuery string) (mppb.StatSrc, error) {
	v, err := url.ParseQuery(statQuery)
	if err != nil {
		return mppb.STAT_SRC_UNKNOWN, errors.Trace(err)
	}

	switch v[magicFieldHost][0] {
	case "kodo":
		return mppb.STAT_SRC_MERGEAPI, nil
	case "all":
		return mppb.STAT_SRC_KODOBILL, nil
	default:
		return mppb.STAT_SRC_UNKNOWN, errors.Trace(errInvalidKodoHost)
	}
}

// shouldEnableMultiSrcLogic 是否应该启用 MULTI 计量源合量逻辑
//
// NOTE: 这里单写一个函数纯粹是为了让别的文件里可以不出现 statSrcXxx 常量
func shouldEnableMultiSrcLogic(v3StatSrcType common.V3StatSrc) bool {
	return v3StatSrcType == common.V3StatSrcMulti
}

// shouldEnableKirbyDatapath 是否应该以新计量中心为数据源
func shouldEnableKirbyDatapath(x mppb.StatSrc) bool {
	switch x {
	// 一期是这仨产品线
	case mppb.STAT_SRC_KODOBILL,
		mppb.STAT_SRC_MERGEAPI,
		mppb.STAT_SRC_ALLSTAT, // KODO 用户列表是这个数据源
		mppb.STAT_SRC_FUSION,
		mppb.STAT_SRC_DORA:
		return true
	default:
		return false
	}
}
