package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measureproxyd/model"
)

// BuildPbGetItemsInUseParams converts from model.GetItemsInUseParams to the protobuf type.
func BuildPbGetItemsInUseParams(x *model.GetItemsInUseParams) (*pb.GetItemsInUseParams, error) {
	return &pb.GetItemsInUseParams{
		Uid:      x.UID,
		Begin:    timestamppb.New(x.Begin),
		End:      timestamppb.New(x.End),
		HaveZone: x.HaveZone,
		ZoneCode: x.ZoneCode,
		Src:      x.Src,
		Debug:    x.Debug,
	}, nil
}

// BuildModelGetItemsInUseParams converts from pb.GetItemsInUseParams to the model layer type.
func BuildModelGetItemsInUseParams(x *pb.GetItemsInUseParams) (*model.GetItemsInUseParams, error) {
	var begin time.Time
	var end time.Time
	if x.Begin != nil {
		err := x.Begin.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		begin = x.Begin.AsTime()
	}

	if x.End != nil {
		err := x.End.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		end = x.End.AsTime()
	}

	return &model.GetItemsInUseParams{
		UID:      x.Uid,
		Begin:    begin,
		End:      end,
		HaveZone: x.HaveZone,
		ZoneCode: x.ZoneCode,
		Src:      x.Src,
		Debug:    x.Debug,
	}, nil
}
