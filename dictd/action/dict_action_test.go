package action_test

import (
	"log"
	"net"
	"testing"

	"google.golang.org/grpc/keepalive"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/bo-base/v4/test"
	pb "github.com/qbox/pay-sdk/dict"

	"qiniu.io/pay/dictd/action"
	"qiniu.io/pay/dictd/model"
	"qiniu.io/pay/dictd/service"
)

type sandbox struct {
	testWrap       *test.Wrap
	dictServer     *grpc.Server
	dictClientConn *grpc.ClientConn
	dictClient     pb.PayDictServiceClient

	listener net.Listener
}

func buildSandbox(t *testing.T) *sandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in dictd/action return error")
	}

	dictDao := model.NewDictDao(testWrap.BaseDao())
	dictService := service.NewDictBizService(dictDao, dao.CacheExpiresNoCache)

	dictAction := action.NewDictAction(dictService, 10)

	dictServer := grpc.NewServer()
	pb.RegisterPayDictServiceServer(dictServer, dictAction)
	reflection.Register(dictServer)

	sandbox := &sandbox{
		testWrap:   testWrap,
		dictServer: dictServer,
	}

	sandbox.initDictServer()
	sandbox.initDictClient()
	t.Cleanup(sandbox.cleanup)

	return sandbox
}

func (s *sandbox) initDictServer() {
	var err error
	s.listener, err = net.Listen("tcp", ":0")
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	go func() {
		if err := s.dictServer.Serve(s.listener); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()
}

func (s *sandbox) initDictClient() {
	var err error
	s.dictClientConn, err = rpc.GrpcConnectWithName(s.listener.Addr().String(), rpc.ServicePayV4Dict, keepalive.ClientParameters{})
	if err != nil {
		log.Fatalf("did not connect: %v", err)
	}
	s.dictClient = pb.NewPayDictServiceClient(s.dictClientConn)
}

func (s *sandbox) cleanup() {
	if s.dictClientConn != nil {
		s.dictClientConn.Close()
	}
	if s.dictServer != nil {
		s.dictServer.Stop()
	}
}
