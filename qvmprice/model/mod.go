package model

import (
	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/dao"
)

// QVMPriceDao is data access object for QVM price service.
type QVMPriceDao struct {
	base              *dao.BaseDao
	QVMGroupDiscount  *QVMGroupDiscountDao
	QVMPriceItem      *QVMPriceItemDao
	QVMPriceItemStair *QVMPriceItemStairDao
	QVMPriceTable     *QVMPriceTableDao
}

// NewQVMPriceDao constructs a QVMPriceDao.
func NewQVMPriceDao(base *dao.BaseDao) *QVMPriceDao {
	return &QVMPriceDao{
		base:              base,
		QVMGroupDiscount:  NewQVMGroupDiscountDao(base),
		QVMPriceItemStair: NewQVMPriceItemStairDao(base),
		QVMPriceItem:      NewQVMPriceItemDao(base),
		QVMPriceTable:     NewQVMPriceTableDao(base),
	}
}

// DoTransaction do a transaction
func (d *QVMPriceDao) DoTransaction(fn func(*QVMPriceDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		newDao := NewQVMPriceDao(base)
		return fn(newDao)
	})
}

// RegisterMigrate migrate all models
func RegisterMigrate(db *gorm.DB) {
	db.AutoMigrate(&QVMGroupDiscount{})
	db.AutoMigrate(&QVMPriceItemStair{})
	db.AutoMigrate(&QVMPriceItem{})
	db.AutoMigrate(&QVMPriceTable{})
}
