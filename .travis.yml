language: go
go:
  - 1.24.x
go_import_path: qiniu.io/pay
services:
  - mysql
cache:
  directories:
    - $GOPATH/pkg/mod  # go mod cache
    - $HOME/.cache/go-build  # go incremental compilation cache
before_install:
  - eval "$(gimme $TRAVIS_GO_VERSION)"
  - gimme list
  # this prefix is NOT open-source so mark as such
  # private repo access is now granted via Travis-injected .netrc
  - export GOPRIVATE=github.com/qbox
  - git config --global url."ssh://**************/qbox/".insteadOf "https://github.com/qbox/"
  # need to download deps before running lint, because lint mechanism is inside bo-base
  - go mod download
  # run lints even before compilation to fail ugly PRs very quickly
  - make lint
install:
  # TODO: use the Docker dist mechanism
  - go install -v ./...
before_script:
  - eval "$(gimme $TRAVIS_GO_VERSION)"
  - gimme list
  - mysql -e 'SET GLOBAL sql_mode = "STRICT_TRANS_TABLES";'
script:
  - ./ci/run-travis.sh
after_success:
  - bash <(curl -s https://codecov.io/bash) -F unit
