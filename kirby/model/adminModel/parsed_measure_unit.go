package adminModel

import (
	"errors"
	"math/big"
)

// ParsedMeasureUnit 已解析为结构化数据的计量单位
type ParsedMeasureUnit struct {
	Kind MeasureUnitKind
	// 量词 e.g. 个/张/条/次... 用于无量纲数
	classifier string
	// 预乘的换算因子
	//
	// 例如，假设原始计量单位即为 MiB，那么 Kind == MeasureUnitKindData, premultipliedFactor == scalingFactorMi
	premultipliedFactor ScalingFactor
}

var errIncompatibleUnits = errors.New("incompatible units")
var errOriginalUnitTooCoarse = errors.New("original unit is too coarse")

func (x *ParsedMeasureUnit) IsCompatibleKind(other *ParsedMeasureUnit) bool {
	if x == nil || other == nil {
		return false
	}
	return x.Kind == other.Kind
}

func (x *ParsedMeasureUnit) ConvertValueTo(
	val uint64,
	newUnit *ParsedMeasureUnit,
) (uint64, error) {
	if !x.IsCompatibleKind(newUnit) {
		return 0, errIncompatibleUnits
	}

	// 不准从更粗的单位转换到更细的单位
	// e.g. 原始数据单位为 MiB，不能从中得到 B 级别的数据
	if x.premultipliedFactor > newUnit.premultipliedFactor {
		return 0, errOriginalUnitTooCoarse
	}

	var t, u, v big.Int
	t.SetUint64(val)
	u.SetInt64(int64(x.premultipliedFactor))
	v.SetInt64(int64(newUnit.premultipliedFactor))
	t.Mul(&t, &u)
	t.Quo(&t, &v)

	// 由于不能转换到更细的单位，实际不可能出现计算结果不能以 uint64 表示的情况
	if !t.IsUint64() {
		panic("should never happen")
	}

	return t.Uint64(), nil
}
