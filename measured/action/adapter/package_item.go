package adapter

import (
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
)

// BuildPbPackageItem converts from model.PackageItem to the protobuf type.
func BuildPbPackageItem(x *model.PackageItem) (*pb.PackageItem, error) {
	return &pb.PackageItem{
		Id:          x.ID,
		PackageId:   x.PackageID,
		ItemId:      x.ItemID,
		ZoneId:      x.ZoneID,
		UnitId:      x.UnitID,
		Name:        x.Name,
		Description: x.Description,
		Remark:      x.Remark,
		CarryType:   BuildPbCarryType(x.CarryType),
		Period:      BuildPbPeriod(x.Period),
		Quantity:    x.Quantity,
		Price:       x.Price,
		EffectTime:  timestamppb.New(x.EffectTime.TimeIn(time.UTC)),
		DeadTime:    timestamppb.New(x.DeadTime.TimeIn(time.UTC)),
		CreatedAt:   timestamppb.New(x.CreatedAt),
		UpdatedAt:   timestamppb.New(x.UpdatedAt),
	}, nil
}

// BuildModelPackageItem converts from pb.PackageItem to the model layer type.
func BuildModelPackageItem(x *pb.PackageItem) (*model.PackageItem, error) {
	carryType, err := BuildModelCarryType(x.GetCarryType())
	if err != nil {
		return nil, err
	}
	period, err := BuildModelPeriod(x.GetPeriod())
	if err != nil {
		return nil, err
	}

	var effectTime, deadTime time.Time
	if x.GetEffectTime() != nil {
		err := x.GetEffectTime().CheckValid()
		if err != nil {
			return nil, err
		}
		effectTime = x.GetEffectTime().AsTime()
	}

	if x.GetDeadTime() != nil {
		err := x.GetDeadTime().CheckValid()
		if err != nil {
			return nil, err
		}
		deadTime = x.GetDeadTime().AsTime()
	}

	var createdAt, updatedAt time.Time
	if x.GetCreatedAt() != nil {
		err := x.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = x.GetCreatedAt().AsTime()
	}

	if x.GetUpdatedAt() != nil {
		err := x.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = x.GetUpdatedAt().AsTime()
	}

	return &model.PackageItem{
		ID:          x.GetId(),
		PackageID:   x.GetPackageId(),
		ItemID:      x.GetItemId(),
		ZoneID:      x.GetZoneId(),
		UnitID:      x.GetUnitId(),
		Name:        x.GetName(),
		Description: x.GetDescription(),
		Remark:      x.GetRemark(),
		CarryType:   carryType,
		Period:      period,
		Quantity:    x.GetQuantity(),
		Price:       x.GetPrice(),
		EffectTime:  base.NewHNS(effectTime),
		DeadTime:    base.NewHNS(deadTime),
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
	}, nil
}

// BuildPbCarryType converts from model.CarryType to the protobuf type.
func BuildPbCarryType(x model.CarryType) pb.CarryType {
	switch x {
	case model.CarryTypeNone:
		return pb.CARRY_TYPE_NONE
	case model.CarryTypeDaily:
		return pb.CARRY_TYPE_DAILY
	case model.CarryTypeMonthly:
		return pb.CARRY_TYPE_MONTHLY
	default:
		return pb.CARRY_TYPE_UNKNOWN
	}
}

// BuildModelCarryType converts from pb.CarryType to the model layer type.
func BuildModelCarryType(x pb.CarryType) (model.CarryType, error) {
	switch x {
	case pb.CARRY_TYPE_NONE:
		return model.CarryTypeNone, nil
	case pb.CARRY_TYPE_DAILY:
		return model.CarryTypeDaily, nil
	case pb.CARRY_TYPE_MONTHLY:
		return model.CarryTypeMonthly, nil
	default:
		return model.CarryTypeNone, fmt.Errorf("unknown CarryType(%d)", int64(x))
	}
}

// BuildPbPeriod converts from model.Period to the protobuf type.
func BuildPbPeriod(x model.Period) pb.Period {
	switch x {
	case model.PeriodDaily:
		return pb.PERIOD_DAILY
	case model.PeriodMonthly:
		return pb.PERIOD_MONTHLY
	case model.PeriodLifeTime:
		return pb.PERIOD_LIFETIME
	default:
		return pb.PERIOD_UNKNOWN
	}
}

// BuildModelPeriod converts from pb.Period to the model layer type.
func BuildModelPeriod(x pb.Period) (model.Period, error) {
	switch x {
	case pb.PERIOD_DAILY:
		return model.PeriodDaily, nil
	case pb.PERIOD_MONTHLY:
		return model.PeriodMonthly, nil
	case pb.PERIOD_LIFETIME:
		return model.PeriodLifeTime, nil
	default:
		return model.PeriodDaily, fmt.Errorf("unknown Period(%d)", int64(x))
	}
}
