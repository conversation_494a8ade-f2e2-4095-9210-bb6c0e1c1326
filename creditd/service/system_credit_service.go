package service

import (
	"context"
	"errors"
	"strconv"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/gaea/client/operations"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

type SystemCreditService struct {
	creditDao           *dao.CreditDao
	walletClient        wallet.PayWalletServiceClient
	paymentClient       wallet.PaymentServiceClient
	gaeaClient          *client.Gaea
	minSystemCreditLine int64
	maxSystemCreditLine int64
	L10nProvider        intl.L10nProvider
}

func NewSystemCreditService(
	l10nProvider intl.L10nProvider,
	dao *dao.CreditDao,
	gaeaClient *client.Gaea,
	paymentClient wallet.PaymentServiceClient,
	walletClient wallet.PayWalletServiceClient,
	minSystemCreditLine int64,
	maxSystemCreditLine int64,
) *SystemCreditService {
	return &SystemCreditService{
		creditDao:           dao,
		walletClient:        walletClient,
		paymentClient:       paymentClient,
		gaeaClient:          gaeaClient,
		maxSystemCreditLine: maxSystemCreditLine,
		minSystemCreditLine: minSystemCreditLine,
		L10nProvider:        l10nProvider,
	}
}
func (s *SystemCreditService) DoTransaction(fn func(service *SystemCreditService) error) error {
	return s.creditDao.DoTransaction(func(creditDao *dao.CreditDao) error {
		srv := NewSystemCreditService(s.L10nProvider, creditDao, s.gaeaClient, s.paymentClient, s.walletClient, s.minSystemCreditLine, s.maxSystemCreditLine)
		return fn(srv)
	})
}

func (s *SystemCreditService) GetSystemCreditByUID(ctx context.Context, uid uint64) (*model.SystemCredit, error) {
	logger := logging.GetLogger(ctx)
	credit, err := s.creditDao.SystemCredit.GetSystemCredit(uid)
	if err != nil {
		logger.WithError(err).WithField("uid", uid).Error("get system credit failed")
		return nil, err
	}
	return credit, nil
}

func (s *SystemCreditService) updateSystemCredit(
	ctx context.Context,
	credit model.SystemCredit,
	assignee SystemCreditAssignee,
) (model.SystemCredit, bool, error) {
	logger := logging.GetLogger(ctx)
	// 1. 判断是否需要更新自动授信
	needAssign, err := assignee.NeedAssign(credit)
	if err != nil {
		logger.WithError(err).WithFields(logrus.Fields{
			"credit":   credit,
			"assignee": assignee,
		}).Error("failed when seeing whether system credit needs new assignment")
		return model.SystemCredit{}, false, err
	}
	if !needAssign {
		logger.WithFields(logrus.Fields{
			"credit":   credit,
			"assignee": assignee,
		}).Warnf("user %d does not need to update system credit", credit.UID)
		return model.SystemCredit{}, false, nil
	}
	// 2. 计算新的自动授信金额
	creditLine, err := assignee.Calculate(credit)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"credit":   credit,
			"assignee": assignee,
		}).WithError(err).Error("calculate credit line failed")
		return model.SystemCredit{}, false, err
	}
	credit.CreditLine = creditLine
	// 3. 更新自动授信并添加记录
	// 记录信用额度金额是否变更
	isCreditLineUpdated := false
	err = s.DoTransaction(func(service *SystemCreditService) error {
		systemCredit, err := service.creditDao.SystemCredit.GetSystemCredit(credit.UID)
		if err != nil {
			return err
		}
		if systemCredit.ID != 0 {
			credit.ID = systemCredit.ID
		}
		if systemCredit.CreditLine != credit.CreditLine {
			isCreditLineUpdated = true
		}
		err = service.creditDao.SystemCredit.SaveSystemCredit(credit)
		if err != nil {
			return err
		}
		return service.creditDao.SystemCreditHistory.CreateSystemCreditHistory(credit, service.L10nProvider.DefaultLocalizer())
	})
	if err != nil {
		logger.WithFields(logrus.Fields{
			"credit":   credit,
			"assignee": assignee,
		}).WithError(err).Error("update user credit line failed")
		return model.SystemCredit{}, false, err
	}
	return credit, isCreditLineUpdated, nil
}

func (s *SystemCreditService) UpsertSystemCredit(
	ctx context.Context,
	credit model.SystemCredit,
) (*model.SystemCredit, bool, error) {
	logger := logging.GetLogger(ctx)
	// 自动授信的账期默认为 1 个月, 币种默认为美元
	credit.CreditPeriod = 1
	credit.PeriodUnit = enums.PeriodUnitMonth
	credit.CurrencyType = base.CurrencyTypeUSD.String()
	cards, err := s.walletClient.ListUserCards(ctx, &wallet.UIDParam{Uid: credit.UID})
	if err != nil {
		logger.WithError(err).WithField("credit", credit).Error("get user default card failed")
		return nil, false, err
	}
	// 没有绑卡的用户没有自动授信
	if len(cards.GetUserCards()) == 0 {
		return &model.SystemCredit{
			UID: credit.UID,
		}, false, nil
	}
	resp, err := s.gaeaClient.Operations.GetDeveloper(operations.NewGetDeveloperParamsWithContext(ctx).
		WithUID(strconv.FormatUint(credit.UID, 10)))
	if err != nil {
		return nil, false, err
	}
	if resp.GetPayload() == nil {
		return nil, false, errors.New("get developer data failed")
	}
	if resp.GetPayload().Data == nil {
		return nil, false, errors.New(resp.GetPayload().Message)
	}
	credit.CustomerName = resp.GetPayload().Data.Fullname
	if !credit.AssignReason.Valid() {
		return &credit, false, errors.New("unknown system credit assigning reason")
	}
	var assignee SystemCreditAssignee
	switch credit.AssignReason {
	case enums.AssignReasonBindFirstCard, enums.AssignReasonChangeDefaultCard,
		enums.AssignReasonRevokeManualCredit:
		assignee = DefaultAssignee{
			min:        s.minSystemCreditLine,
			historyDao: s.creditDao.SystemCreditHistory,
		}
	case enums.AssignReasonChargeCard:
		assignee = CardChargeAssignee{
			historyDao: s.creditDao.SystemCreditHistory,
			min:        s.minSystemCreditLine,
			max:        s.maxSystemCreditLine,
			amount:     credit.CreditLine,
		}
	case enums.AssignReasonGenMonthStatement:
		assignee = BillIssuerAssignee{
			dao:        s.creditDao.SystemCredit,
			historyDao: s.creditDao.SystemCreditHistory,
			min:        s.minSystemCreditLine,
			max:        s.maxSystemCreditLine,
			gaeaSrv:    s.gaeaClient,
		}
	default:
		logger.Errorf("unknown system credit assigning reason")
		return nil, false, errors.New("unknown system credit assigning reason")
	}
	credit, isCreditLineUpdated, err := s.updateSystemCredit(ctx, credit, assignee)
	if err != nil {
		logger.WithError(err).WithField("credit", credit).Errorf("update system credit failed")
		return nil, false, err
	}
	return &credit, isCreditLineUpdated, err
}
