package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"
	"google.golang.org/protobuf/types/known/emptypb"
	"qiniu.io/pay/priced/action/adapter"
	"qiniu.io/pay/priced/service"
)

// GetPriceTableByID 根据 ID 获取价格
func (a *PriceAction) GetPriceTableByID(
	ctx context.Context,
	priceTableID *pb.IDParam,
) (*pb.PriceTable, error) {
	priceTable, err := a.priceBizSrv.GetPriceTableByID(ctx, priceTableID.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceTable(priceTable)
}

// CreatePriceTable 创建价格
func (a *PriceAction) CreatePriceTable(
	ctx context.Context,
	priceTable *pb.PriceTable,
) (*pb.PriceTable, error) {
	p, err := adapter.BuildPriceTable(priceTable)
	if err != nil {
		return nil, errors.Trace(err)
	}

	priceTableModel, err := a.priceBizSrv.CreatePriceTable(ctx, p)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceTable(priceTableModel)
}

// UpdatePriceTableByID 根据 ID 更新价格
func (a *PriceAction) UpdatePriceTableByID(
	ctx context.Context,
	param *pb.IDPriceTableParam,
) (*pb.PriceTable, error) {
	priceTable := param.GetPriceTable()
	p, err := adapter.BuildPriceTable(priceTable)
	if err != nil {
		return nil, errors.Trace(err)
	}

	priceTableModel, err := a.priceBizSrv.UpdatePriceTableByID(ctx, param.GetId(), p)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceTable(priceTableModel)
}

// DeletePriceTableByID 根据 ID 删除价格
func (a *PriceAction) DeletePriceTableByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.PriceTable, error) {
	priceTableModel, err := a.priceBizSrv.DeletePriceTableByID(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceTable(priceTableModel)
}

// ListAllPriceTables 查询所有的价格
func (a *PriceAction) ListAllPriceTables(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.PriceTableList, error) {
	count, err := a.priceBizSrv.CountAllPriceTables(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}
	offset, limit := a.Paging(param)
	priceTableModels, err := a.priceBizSrv.ListAllPriceTables(ctx, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}
	list := &pb.PriceTableList{
		Count:       count,
		PriceTables: make([]*pb.PriceTable, len(priceTableModels)),
	}
	for i, priceTableModel := range priceTableModels {
		priceTable, err := adapter.BuildPbPriceTable(&priceTableModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceTables[i] = priceTable
	}
	return list, nil
}

// EnablePriceTable 使指定报价单生效
func (a *PriceAction) EnablePriceTable(
	ctx context.Context,
	param *pb.IDParam,
) (*empty.Empty, error) {
	err := a.priceBizSrv.EnablePriceTable(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &empty.Empty{}, nil
}

// DisablePriceTable 禁用指定报价单
func (a *PriceAction) DisablePriceTable(
	ctx context.Context,
	param *pb.IDParam,
) (*emptypb.Empty, error) {
	err := a.priceBizSrv.DisablePriceTable(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}
	return &empty.Empty{}, nil
}

// ClonePriceTable 将指定的一个 priceTable 复制到特定 uid 下
// 目前使用场景：父账号的报价单，同步复制给其名下的子账号
func (a *PriceAction) ClonePriceTable(
	ctx context.Context,
	param *pb.UIDPriceIDParam,
) (*pb.PriceTable, error) {

	priceTableWithItems, err := a.priceBizSrv.GetPriceTableAndItemsByID(
		ctx,
		param.GetPriceId(),
	)
	if err != nil {
		return nil, errors.Trace(err)
	}
	if len(priceTableWithItems.Items) <= 0 {
		return nil, errors.Trace(errors.New("missing price items"))
	}
	// 以第一个 priceItem 为基准
	minEffectTime := priceTableWithItems.Items[0].EffectTime
	maxDeadTime := priceTableWithItems.Items[0].DeadTime
	// 获取这张价格表里面，最长的时间区间作为新价格表的有效期
	for _, item := range priceTableWithItems.Items {
		if item.EffectTime < minEffectTime {
			minEffectTime = item.EffectTime
		}
		if item.DeadTime > maxDeadTime {
			maxDeadTime = item.DeadTime
		}
	}
	pt, err := a.priceBizSrv.ChangePriceTableLifecycle(
		ctx,
		&service.PriceTableLifecycleChangeParam{
			UID:          param.GetUid(),
			PriceTableID: param.GetPriceId(),
			EffectTime:   minEffectTime,
			DeadTime:     maxDeadTime,
		},
	)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceTable(pt)
}
