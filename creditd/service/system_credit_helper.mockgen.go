// Code generated by MockGen. DO NOT EDIT.
// Source: creditd/service/system_credit_helper.go

package service

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	model "qiniu.io/pay/creditd/model"
)

// MockSystemCreditAssignee is a mock of SystemCreditAssignee interface.
type MockSystemCreditAssignee struct {
	ctrl     *gomock.Controller
	recorder *MockSystemCreditAssigneeMockRecorder
}

// MockSystemCreditAssigneeMockRecorder is the mock recorder for MockSystemCreditAssignee.
type MockSystemCreditAssigneeMockRecorder struct {
	mock *MockSystemCreditAssignee
}

// NewMockSystemCreditAssignee creates a new mock instance.
func NewMockSystemCreditAssignee(ctrl *gomock.Controller) *MockSystemCreditAssignee {
	mock := &MockSystemCreditAssignee{ctrl: ctrl}
	mock.recorder = &MockSystemCreditAssigneeMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSystemCreditAssignee) EXPECT() *MockSystemCreditAssigneeMockRecorder {
	return m.recorder
}

// Calculate mocks base method.
func (m *MockSystemCreditAssignee) Calculate(credit model.SystemCredit) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Calculate", credit)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Calculate indicates an expected call of Calculate.
func (mr *MockSystemCreditAssigneeMockRecorder) Calculate(credit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Calculate", reflect.TypeOf((*MockSystemCreditAssignee)(nil).Calculate), credit)
}

// NeedAssign mocks base method.
func (m *MockSystemCreditAssignee) NeedAssign(credit model.SystemCredit) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NeedAssign", credit)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NeedAssign indicates an expected call of NeedAssign.
func (mr *MockSystemCreditAssigneeMockRecorder) NeedAssign(credit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NeedAssign", reflect.TypeOf((*MockSystemCreditAssignee)(nil).NeedAssign), credit)
}
