package adapter

import (
	pb "github.com/qbox/pay-sdk/qvmprice"

	"qiniu.io/pay/qvmprice/service"
)

func buildServiceQVMGroupDiscountDetail(
	x *pb.QVMGroupDiscountDetail,
) *service.QVMGroupDiscountDetail {
	return &service.QVMGroupDiscountDetail{
		GroupCode:  x.GroupCode,
		ZoneCode:   x.ZoneCode,
		IsDisabled: x.IsDisabled,
		Discount:   x.Discount,
		EffectTime: x.EffectTime.AsTime(),
		DeadTime:   x.DeadTime.AsTime(),
	}
}

func BuildServiceQVMGroupDiscountDetails(
	x []*pb.QVMGroupDiscountDetail,
) []*service.QVMGroupDiscountDetail {
	return mapSlice(buildServiceQVMGroupDiscountDetail, x)
}
