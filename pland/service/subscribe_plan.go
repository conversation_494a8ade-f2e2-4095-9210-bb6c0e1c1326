package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/lock"
	"github.com/qbox/bo-base/v4/sync/limiter"
	"github.com/qbox/pay-sdk/middleware/logging"

	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/models/enums"
)

// AdminSubscribePlan 订阅、更改 plan
func (s *PlanService) AdminSubscribePlan(
	ctx context.Context,
	param *models.SubscribePlanParam,
	epoch time.Time,
) error {
	logger := logging.GetLogger(ctx)

	// 1.uid 粒度加锁
	locker := lock.NewRedisLocker(s.redisClient)

	lockKey := fmt.Sprintf("sufy:plan:lock:%d", param.Uid)
	get := locker.Lock(lockKey)
	defer locker.Unlock(lockKey)
	if !get {
		return errors.New("user lock conflict")
	}
	onlinePlanMap, err := s.initAllOnlinePlans(ctx)
	if err != nil {
		return err
	}
	month := calcMonth(ctx, epoch, param.EffectType)

	// 初始化户对应月的订阅表
	usrSubscribe, err := s.initUserSubscribeTable(ctx, param, month)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"param": param,
			"month": month,
		}).WithError(err).Error("init user subscribe table failed")
		return err
	}

	// 开始按照 subscribe 表开始执行 解除+订阅 操作
	if param.EffectType == enums.EffectTypeNextMonth {
		// 下月才生效，先记录下来，下月月初定时脚本再处理
		err = s.markSubscribe(ctx, usrSubscribe, enums.SubscribeScheduled)
		if err != nil {
			logger.WithField("usrSubscribe", usrSubscribe).
				WithError(err).Error("mark usrSubscribe scheduled failed")
			return err
		}
		return nil
	}
	// 获取当前用户的 plan
	oldUsrPlan, err := s.fetchUsrPlan(ctx, usrSubscribe.UID, usrSubscribe.Month)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"usrSubscribe": usrSubscribe,
		}).WithError(err).Error("fetch user plan failed")
		return err
	}
	// 写入变更记录
	changes, err := s.writeChanges(ctx, onlinePlanMap, oldUsrPlan, usrSubscribe)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"onlinePlanMap": onlinePlanMap,
			"oldUsrPlan":    oldUsrPlan,
			"usrSubscribe":  usrSubscribe,
		}).WithError(err).Error("write changes failed")
		return err
	}
	// 支付扣费
	changes, err = s.tryPaying(ctx, changes)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"onlinePlanMap": onlinePlanMap,
			"changes":       changes,
		}).WithError(err).Error("try paying failed")
		return err
	}
	// 扣费成功，更新订阅表
	err = s.markSubscribe(ctx, usrSubscribe, enums.SubscribePaid)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"usrSubscribe": usrSubscribe,
		}).WithError(err).Error("mark subscribe paid failed")
		return err
	}

	// ①先尝试解绑(有些无需解绑的可以直接跳过)
	// ②再写入 usrPlan
	// ③最后重新绑定(有些无需绑定的可以直接跳过)
	// ④对原来的进行退款
	// ⑤标记当前 usrSubscribe 完成

	// 尝试解绑之前的绑定的 respack 权益
	err = s.tryUnbindRights(ctx, oldUsrPlan)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"oldUsrPlan": oldUsrPlan,
			"changes":    changes,
		}).WithError(err).Error("try unbind rights failed")
		return err
	}
	newUsrPlan, err := s.writeUsrPlan(ctx, oldUsrPlan, changes)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"oldUsrPlan": oldUsrPlan,
			"changes":    changes,
		}).WithError(err).Error("write userPlan failed")
		return err
	}
	// 重新绑定新的权益
	newUsrPlan, err = s.bindRights(ctx, onlinePlanMap, newUsrPlan)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"newUsrPlan":    newUsrPlan,
			"onlinePlanMap": onlinePlanMap,
		}).WithError(err).Error("bind rights failed")
		return err
	}
	// 对原来的进行退款
	err = s.refundPrevious(ctx, oldUsrPlan, changes)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"oldUsrPlan": oldUsrPlan,
			"changes":    changes,
		}).WithError(err).Error("refund previous failed")
		return err
	}
	// 变更 plan
	err = s.changeUsrPlan(ctx, oldUsrPlan, changes)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"oldUsrPlan": oldUsrPlan,
			"changes":    changes,
		}).WithError(err).Error("change userPlan failed")
		return err
	}

	err = s.markSubscribe(ctx, usrSubscribe, enums.SubscribeDone)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"usrSubscribe": usrSubscribe,
		}).WithError(err).Error("mark subscribe done failed")
		return err
	}
	err = s.cleanNextMonthSubscribe(ctx, month, usrSubscribe)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"usrSubscribe": usrSubscribe,
		}).WithError(err).Error("clean subscribe table failed")
		return err
	}

	return nil
}

// GetNextMonthSubscribeInfo 获取用户下个的订阅信息及 plan 信息
func (s *PlanService) GetNextMonthSubscribeInfo(
	ctx context.Context,
	uid uint64,
	epoch time.Time,
) (*models.UserSubscribe, *models.Plan, error) {
	logger := logging.GetLogger(ctx)
	// 续订信息：获取下个月即将订阅的版本
	nextMonth := base.NextMonth(epoch)
	usrSubscribe, err := s.dao.UserSubscribeDao.GetUserSubscribe(
		ctx,
		uid,
		nextMonth,
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.WithFields(logrus.Fields{
			"uid":       uid,
			"nextMonth": nextMonth,
		}).WithError(err).Error("get next month subscribe failed")
		return nil, nil, err
	}
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 下个月的续订表还没有，则以当月的为准
		usrSubscribe, err = s.dao.UserSubscribeDao.GetUserSubscribe(
			ctx,
			uid,
			base.ThisMonth(epoch),
		)
		if err != nil {

			logger.WithFields(logrus.Fields{
				"uid":       uid,
				"thisMonth": base.ThisMonth(epoch),
			}).WithError(err).Error("get this month subscribe failed")
			return nil, nil, err
		}
		// 用当月已订阅的 当做下个月计划要订阅的
		usrSubscribe.Month = nextMonth
	}
	plan, err := s.GetOnlineByLevel(ctx, usrSubscribe.Level)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"usrSubscribe": usrSubscribe,
		}).WithError(err).Error("get online by level failed")
		return nil, nil, err
	}

	return usrSubscribe, plan, nil
}

// AbortSubscribe 退订，终止订阅
func (s *PlanService) AbortSubscribe(
	ctx context.Context,
	uid uint64,
	epoch time.Time,
) error {
	logger := logging.GetLogger(ctx)

	// 退订：即意味着下个月开始订阅 free 版本
	err := s.AdminSubscribePlan(
		ctx,
		&models.SubscribePlanParam{
			Uid:          uid,
			Level:        enums.LevelFree,
			PackCode:     "",
			EffectType:   enums.EffectTypeNextMonth,
			SubscribeSrc: enums.SubscribeSrcCustomer,
		},
		epoch,
	)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"uid":   uid,
			"epoch": epoch,
		}).WithError(err).Error("abort subscribe failed")
		return err
	}
	return nil
}

// AutoRenew 自动续订
func (s *PlanService) AutoRenew(
	ctx context.Context,
	month string,
	epoch time.Time,
) error {
	logger := logging.GetLogger(ctx)

	monthTime, err := base.Month(month).Time(ctx)
	if err != nil {
		logger.WithError(err).
			WithField("month", month).
			Error("auto renew parse month failed")
		return err
	}
	// 不能提前允许,否则会导致 epoch 所在月的已有plan 全部重新退订后再重新订阅(一来一回，毫无意义)
	if epoch.Before(monthTime) {
		return errors.New("time before month")
	}

	resCh := make(chan *models.UserSubscribe, 10)
	go func() {
		// monthTime 是要续订的月份
		// 查询上个月的订阅表
		err = s.dao.UserSubscribeDao.ScanUserSubscribes(
			ctx,
			monthTime.AddDate(0, -1, 0),
			resCh,
		)
		if err != nil {
			logger.WithError(err).
				WithFields(logrus.Fields{
					"month": month,
					"epoch": epoch,
				}).Error("month renew scan failed")
		}
	}()
	lg := limiter.NewGoroutineLimiter(2)
	for u := range resCh {
		ub := u
		lg.Go(func() {
			override, e := s.dao.UserSubscribeDao.GetUserSubscribe(
				context.WithoutCancel(ctx),
				ub.UID,
				monthTime,
			)
			if e != nil && !errors.Is(e, gorm.ErrRecordNotFound) {
				logger.WithError(e).
					WithFields(logrus.Fields{
						"uid":   ub.UID,
						"month": month,
						"epoch": epoch,
					}).Error("get user subscribe failed")
				_ = e
				return
			}
			subscribeParam := &models.SubscribePlanParam{
				Uid:          ub.UID,
				Level:        ub.Level,
				PackCode:     ub.PacksCode,
				EffectType:   enums.EffectTypeImmediately,
				SubscribeSrc: enums.SubscribeSrcSys,
			}
			if e == nil {
				subscribeParam.Level = override.Level
				subscribeParam.PackCode = override.PacksCode
			}

			err = s.AdminSubscribePlan(
				context.WithoutCancel(ctx),
				&models.SubscribePlanParam{
					Uid:          ub.UID,
					Level:        ub.Level,
					PackCode:     ub.PacksCode,
					EffectType:   enums.EffectTypeImmediately,
					SubscribeSrc: enums.SubscribeSrcSys,
				},
				epoch,
			)
			if err != nil {
				logger.WithError(err).
					WithFields(logrus.Fields{
						"uid":   ub.UID,
						"month": month,
						"epoch": epoch,
					}).Error("auto renew failed")
				// 不影响其他 uid 的续订
				_ = err
				// 如果本身就是订阅的 Free, 这里就没必要再次发起订阅 Free 操作了
				if ub.Level != enums.LevelFree {
					err = s.AdminSubscribePlan(
						context.WithoutCancel(ctx),
						&models.SubscribePlanParam{
							Uid:          ub.UID,
							Level:        enums.LevelFree,
							PackCode:     "",
							EffectType:   enums.EffectTypeImmediately,
							SubscribeSrc: enums.SubscribeSrcSys,
						},
						epoch,
					)
					if err != nil {
						logger.WithError(err).
							WithFields(logrus.Fields{
								"uid":   ub.UID,
								"month": month,
								"epoch": epoch,
							}).Error("auto renew free failed")
						_ = err
					}
				}
			}
		})
	}
	lg.Wait()

	return nil
}
