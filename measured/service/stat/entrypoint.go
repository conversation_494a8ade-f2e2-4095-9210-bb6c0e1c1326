package stat

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/zone"
	"qiniu.io/pay/measured/model"
)

// GetStat 拉量
func (s *measuredStatClient) GetStat(
	ctx context.Context,
	params *model.StatReq,
) (*model.StatResp, error) {
	scx, err := s.prepareStatQueryContext(ctx, params, false)
	if err != nil {
		return nil, errors.Trace(err)
	}

	err = s.queryAxes(ctx, scx)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return scx.finalizeStat(), nil
}

// GetUsers 拉某产品线某区域有量用户列表
func (s *measuredStatClient) GetUsers(
	ctx context.Context,
	p *model.GetUsersParams,
) (model.UsersResp, error) {
	if p.ProductCode == zone.ProductMiku.String() {
		return s.getUsersForMiku(ctx, p)
	}
	return s.getUsers(ctx, p)
}

// GetItemsInUse 拉单个用户有量计费项列表
func (s *measuredStatClient) GetItemsInUse(
	ctx context.Context,
	p *model.GetItemsInUseParams,
) (model.ItemsInUseResp, error) {
	return s.getItemsInUse(ctx, p)
}
