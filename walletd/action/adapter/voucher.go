package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qpay/enums"
	wm "qiniu.io/pay/qpay/wallet/model"
)

func BuildVoucherCodes(request *pb.CodeParamList) []string {
	codes := make([]string, 0)

	for _, c := range request.Code {
		codes = append(codes, c.Code)
	}
	return codes
}

func BuildVoucherCondition(request *pb.ListVoucherConditionRequest) (*wm.VoucherQueryCondition, error) {
	var startTime *time.Time
	var endTime *time.Time
	if request.StartTime != nil && request.StartTime.Seconds != 0 {
		err := request.StartTime.CheckValid()
		if err != nil {
			return nil, err
		}
		from := request.StartTime.AsTime()
		startTime = &from
	}
	if request.EndTime != nil && request.EndTime.Seconds != 0 {
		err := request.EndTime.CheckValid()
		if err != nil {
			return nil, err
		}
		to := request.EndTime.AsTime()
		endTime = &to
	}
	return &wm.VoucherQueryCondition{
		SubSystem:       request.SubSystem,
		Status:          enums.NewVoucherStatus(request.Status),
		VoucherType:     enums.NewVoucherType(request.Type),
		ArrearageCanUse: request.ArrearageCanUse,
		Title:           request.Title,
		StartTime:       startTime,
		EndTime:         endTime,
		UIDs:            request.Uids,
		Codes:           request.Codes,
		ScopeIDs:        request.ScopeIds,
		AssetIDs:        request.AssetIds,
		Marker:          request.Marker,
	}, nil
}

func BuildVoucher(request *pb.VoucherItem) (*wm.Voucher, error) {
	var EffectTime *time.Time
	var DeadTime *time.Time
	var ExpiredTime *time.Time
	var CreatedAt *time.Time
	var UpdatedAt *time.Time
	if request.EffectTime != nil && request.EffectTime.Seconds != 0 {
		err := request.EffectTime.CheckValid()
		if err != nil {
			return nil, err
		}
		time := request.EffectTime.AsTime()
		EffectTime = &time
	}
	if request.DeadTime != nil && request.DeadTime.Seconds != 0 {
		err := request.DeadTime.CheckValid()
		if err != nil {
			return nil, err
		}
		time := request.DeadTime.AsTime()
		DeadTime = &time
	}
	if request.ExpiredTime != nil && request.ExpiredTime.Seconds != 0 {
		err := request.ExpiredTime.CheckValid()
		if err != nil {
			return nil, err
		}
		time := request.ExpiredTime.AsTime()
		ExpiredTime = &time
	}
	if request.CreatedAt != nil && request.CreatedAt.Seconds != 0 {
		err := request.CreatedAt.CheckValid()
		if err != nil {
			return nil, err
		}
		time := request.CreatedAt.AsTime()
		CreatedAt = &time
	}
	if request.UpdatedAt != nil && request.UpdatedAt.Seconds != 0 {
		err := request.UpdatedAt.CheckValid()
		if err != nil {
			return nil, err
		}
		time := request.UpdatedAt.AsTime()
		UpdatedAt = &time
	}

	assetType := enums.AssetTypeCNYVoucher.AdaptAssetTypeForCurrencyTypeUSD(enums.CurrencyType(request.CurrencyType))

	return &wm.Voucher{
		VoucherBase: wm.VoucherBase{
			AssetID:            request.AssetId,
			SubSystem:          enums.NewVoucherSubSystem(request.SubSystem),
			Quota:              base.Money(request.Amount),
			Balance:            base.Money(request.Balance),
			EffectTime:         EffectTime,
			DeadTime:           DeadTime,
			ExpiredTime:        ExpiredTime,
			CreatedAt:          CreatedAt,
			UpdatedAt:          UpdatedAt,
			Code:               request.Code,
			UID:                request.Uid,
			Day:                request.Day,
			Title:              request.Title,
			Description:        request.Desc,
			VoucherType:        enums.NewVoucherType(request.Type),
			Status:             enums.NewVoucherStatus(request.Status),
			ArrearageCanUse:    request.ArrearageCanUse,
			MaxActivationTimes: request.MaxActivation,
			BatchID:            request.BatchId,
			AssetType:          assetType,
		},
		Scope:  request.Scope,
		Excode: request.Excode,
	}, nil
}

func BuildPbVoucherItem(voucher *wm.Voucher) (*pb.VoucherItem, error) {
	var effectTime *timestamppb.Timestamp
	var deadTime *timestamppb.Timestamp
	var expiredTime *timestamppb.Timestamp
	var createAt *timestamppb.Timestamp
	var updateAt *timestamppb.Timestamp

	if voucher.EffectTime != nil {
		effectTime = timestamppb.New(*voucher.EffectTime)
	}
	if voucher.DeadTime != nil {
		deadTime = timestamppb.New(*voucher.DeadTime)
	}
	if voucher.ExpiredTime != nil {
		expiredTime = timestamppb.New(*voucher.ExpiredTime)
	}
	if voucher.CreatedAt != nil {
		createAt = timestamppb.New(*voucher.CreatedAt)
	}
	if voucher.UpdatedAt != nil {
		updateAt = timestamppb.New(*voucher.UpdatedAt)
	}

	currencyType := enums.CurrencyTypeCNY
	if voucher.AssetType == enums.AssetTypeUSDVoucher {
		currencyType = enums.CurrencyTypeUSD
	}

	return &pb.VoucherItem{
		Id:              voucher.ID,
		AssetId:         voucher.AssetID,
		Amount:          int64(voucher.Quota),
		Balance:         int64(voucher.Balance),
		EffectTime:      effectTime,
		DeadTime:        deadTime,
		ExpiredTime:     expiredTime,
		CreatedAt:       createAt,
		UpdatedAt:       updateAt,
		SubSystem:       string(voucher.SubSystem),
		Day:             voucher.Day,
		Type:            string(voucher.VoucherType),
		Desc:            voucher.Description,
		Title:           voucher.Title,
		ArrearageCanUse: voucher.ArrearageCanUse,
		MaxActivation:   voucher.MaxActivationTimes,
		BatchId:         voucher.BatchID,
		Code:            voucher.Code,
		Excode:          voucher.Code,
		Uid:             voucher.UID,
		Status:          uint64(voucher.Status),
		Scope:           voucher.Scope,
		CurrencyType:    string(currencyType),
	}, nil
}

func BuildPbVoucherItemList(vouchers []wm.Voucher) (*pb.VoucherItemList, error) {
	var voucherItems = make([]*pb.VoucherItem, 0)
	for _, voucher := range vouchers {
		vi, err := BuildPbVoucherItem(&voucher)
		if err != nil {
			return nil, err
		}
		voucherItems = append(voucherItems, vi)
	}
	return &pb.VoucherItemList{VoucherItem: voucherItems}, nil
}

func BuildPbWalletItem(wallet *wm.Wallet) (*pb.WalletItem, error) {
	return &pb.WalletItem{
		WalletId:  wallet.AssetID,
		Amount:    int64(wallet.Amount),
		Balance:   int64(wallet.Balance),
		CreatedAt: timestamppb.New(wallet.CreatedAt),
	}, nil
}

func BuildPbWalletItemList(wallets []wm.Wallet) (*pb.WalletItemList, error) {
	var walletItems = make([]*pb.WalletItem, 0)
	for _, item := range wallets {
		wallet, err := BuildPbWalletItem(&item)
		if err != nil {
			return nil, err
		}
		walletItems = append(walletItems, wallet)
	}
	return &pb.WalletItemList{WalletItem: walletItems}, nil
}
