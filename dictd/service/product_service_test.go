package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestProduct(t *testing.T) {
	sandbox := buildSandbox(t)

	dictService := sandbox.dictService

	ps := []model.Product{
		{
			Code:        "kodo",
			Name:        "云存储",
			Description: "七牛云存储提供高可用高可靠的分布式存储服务",
			Remark:      "云存储备注",
		},
		{
			Code:        "fusion",
			Name:        "融合CDN",
			Description: "七牛融合CDN提供多线路融合CDN加速",
			Remark:      "融合CDN备注",
		},
		{
			Code:        "dora",
			Name:        "数据处理",
			Description: "七牛数据处理提供图片音视频的处理服务",
			Remark:      "数据处理备注",
		},
	}
	assertFields := func(product *model.Product, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(ps) }) {
			assert.Equal(t, ps[n].Name, product.Name, msgAndArgs...)
			assert.Equal(t, ps[n].Code, product.Code, msgAndArgs...)
			assert.Equal(t, ps[n].Description, product.Description, msgAndArgs...)
			assert.Equal(t, ps[n].Remark, product.Remark, msgAndArgs...)
		}
	}
	ctx := context.TODO()
	ids := make(map[uint64]int, len(ps))
	codes := make(map[uint64]string, len(ps))
	for n, p := range ps {
		product, err := dictService.CreateProduct(ctx, &p)
		if assert.NoError(t, err, "CreateProduct") {
			assert.NotZero(t, product.ID, "CreateProduct return zero id")
			ids[product.ID] = n
			codes[product.ID] = p.Code
		}

		product, err = dictService.GetProductByID(ctx, product.ID)
		if assert.NoError(t, err, "GetProductByID") {
			assertFields(product, n, "GetProductByID fields not match")
		}

		product, err = dictService.GetProductByCode(ctx, p.Code)
		if assert.NoError(t, err, "GetProductByCode") {
			assertFields(product, n, "GetProductByCode fields not match")
		}

		product.Name += p.Code
		ps[n].Name += p.Code
		product, err = dictService.UpdateProductByID(ctx, product.ID, product)
		if assert.NoError(t, err, "UpdateProductByID") {
			assert.NotZero(t, product.ID, "UpdateProductByID return zero id")
		}

		product.Description += p.Description
		ps[n].Description += p.Description
		product, err = dictService.UpdateProductByCode(ctx, product.Code, product)
		if assert.NoError(t, err, "UpdateProductByCode") {
			assert.NotZero(t, product.ID, "UpdateProductByCode return zero id")
		}
	}

	products, err := dictService.ListAllProducts(ctx, false, 0, 10)
	if assert.NoError(t, err, "ListAllProducts") {
		assert.Len(t, products, len(ps), "ListAllProducts unexpected length")
		count, err := dictService.CountAllProducts(ctx, false)
		if assert.NoError(t, err, "CountAllProducts") {
			assert.Equal(t, uint64(len(ps)), count, "CountAllProducts count unexpected")
		}
		for _, product := range products {
			if assert.Contains(t, ids, product.ID, "ListAllProducts") {
				assertFields(&product, ids[product.ID], "ListAllProducts")
			}
		}
	}

	for id, n := range ids {
		if id%2 == 0 {
			product, err := dictService.DeleteProductByID(ctx, id)
			if assert.NoError(t, err, "DeleteProductByID") {
				assertFields(product, n)
			}
		} else {
			product, err := dictService.DeleteProductByCode(ctx, codes[id])
			if assert.NoError(t, err, "DeleteProductByCode") {
				assertFields(product, n, "DeleteProductByCode")
			}
		}
	}

	count, err := dictService.CountAllProducts(ctx, false)
	if assert.NoError(t, err, "CountAllProducts") {
		assert.Equal(t, uint64(0), count, "CountAllProducts count after delete all is not zero")
	}
}
