package stat

import (
	"context"
	"sort"
	"time"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/legacystat/common"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	mppb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service/stat/doraItemsQuerier"
)

// getItemsInUse 拉单个用户有量计费项列表
func (s *measuredStatClient) getItemsInUse(
	ctx context.Context,
	p *model.GetItemsInUseParams,
) (model.ItemsInUseResp, error) {
	src, ok := common.StatSrcKindFromProductCode(p.ProductCode)
	// KODO 有俩计量源，因此此处会返回 ok = false，但如果启用 kirby 那么此情况其实不是错误
	// 这种时候要跳过下边的错误处理
	shouldSpecialCaseKodo := s.enableKirbyDatapath && p.ProductCode == "kodo"
	if !ok && !shouldSpecialCaseKodo {
		return nil, ErrUnsupportedProduct{ProductCode: p.ProductCode}
	}

	// 如果启用了 kirby，此处也要为 kodo 跳过检查，因为此处能拿到的 kodo “计量源”其实是
	// unknown
	if s.enableKirbyDatapath && (shouldEnableKirbyDatapath(src) || shouldSpecialCaseKodo) {
		return s.getItemsInUseWithKirby(ctx, p)
	}

	// BO-15823 如果是请求的 Dora 产品线，则启用计量中心一侧的适配逻辑，与有量 Dora cmd 列表接口对接
	if doraItemsQuerier.IsProductSupported(p.ProductCode) {
		return s.doraItemsQuerier.DoraItemsInUse(ctx, p)
	}

	req := &mppb.GetItemsInUseParams{
		Uid:      uint32(p.UID),
		Begin:    timestamppb.New(p.Begin),
		End:      timestamppb.New(p.End),
		HaveZone: true,
		ZoneCode: uint32(p.ZoneCode),
		Src:      src,
		Debug:    p.Debug,
	}
	resp, err := s.mpClient.ProxyGetItemsInUse(ctx, req)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp.ItemCodes, nil
}

// getItemsInUseWithKirby 拉单个用户有量计费项列表（Kirby datapath）
func (s *measuredStatClient) getItemsInUseWithKirby(
	ctx context.Context,
	p *model.GetItemsInUseParams,
) (model.ItemsInUseResp, error) {
	loc := tz.MustLocationFromCtx(ctx)
	epoch := time.Now().In(loc) // TODO: stuff into model/pb definition

	// TODO: 怎么能跟 getUsersWithKirby 复用代码。。。

	// query by months then merge
	startMonth := base.ThisMonth(p.Begin.In(loc))
	endMonth := base.ThisMonth(p.End.In(loc))
	if endMonth.Equal(p.End) {
		endMonth = endMonth.AddDate(0, 1, 0)
	}

	// we can avoid having to maintain a set if only one month's worth of data
	// is requested
	singleMonth := startMonth.AddDate(0, 1, 0).Equal(endMonth)
	if singleMonth {
		result, err := s.kirbyDP.ListUserItemsByMonthAndZone(ctx, p.UID, startMonth, p.ZoneCode, epoch)
		if err != nil {
			return nil, errors.Trace(err)
		}

		return model.ItemsInUseResp(result), nil
	}

	// slow path
	itemsSet := make(map[string]struct{})

	var months []time.Time
	for month := startMonth; month.Before(endMonth); month = month.AddDate(0, 1, 0) {
		months = append(months, month)
	}

	// 3 目前是随便定的一个不太大的数，主要为了不要打爆数据面
	resultsByMonth, err := resultgroup.ThrottledParallelMap(months, 3, func(month time.Time) ([]string, error) {
		resp, err := s.kirbyDP.ListUserItemsByMonthAndZone(ctx, p.UID, month, p.ZoneCode, epoch)
		if err != nil {
			return nil, errors.Trace(err)
		}

		return resp, nil
	})
	if err != nil {
		return nil, err
	}

	for _, monthResult := range resultsByMonth {
		for _, x := range monthResult {
			itemsSet[x] = struct{}{}
		}
	}

	items := lo.MapToSlice(itemsSet, func(k string, _ struct{}) string { return k })
	sort.Strings(items)

	return model.ItemsInUseResp(items), nil
}
