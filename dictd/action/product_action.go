package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/dict"
	"qiniu.io/pay/dictd/action/adapter"
)

// GetProductByID 根据ID获取产品记录
func (a *DictAction) GetProductByID(
	ctx context.Context,
	productID *pb.IDParam,
) (*pb.Product, error) {
	product, err := a.dictBizSrv.GetProductByID(ctx, productID.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbProduct(product)
}

// GetProductByCode 根据代码获取产品记录
func (a *DictAction) GetProductByCode(
	ctx context.Context,
	productCode *pb.CodeParam,
) (*pb.Product, error) {
	product, err := a.dictBizSrv.GetProductByCode(ctx, productCode.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbProduct(product)
}

// ListAllProducts 获取所有产品记录
func (a *DictAction) ListAllProducts(
	ctx context.Context,
	param *pb.ListAllProductsPagingParam,
) (*pb.ProductList, error) {
	count, err := a.dictBizSrv.CountAllProducts(ctx, param.GetIgnoreDeprecated())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	products, err := a.dictBizSrv.ListAllProducts(ctx, param.GetIgnoreDeprecated(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ProductList{
		Count:    count,
		Products: make([]*pb.Product, len(products)),
	}
	for i, product := range products {
		p, err := adapter.BuildPbProduct(&product)
		if err != nil {
			return nil, err
		}
		list.Products[i] = p
	}
	return list, nil
}

// ListProductByItemIDs 通过itemid筛选product
func (a *DictAction) ListProductByItemIDs(
	ctx context.Context,
	param *pb.MultiIDParam,
) (*pb.ProductList, error) {
	products, err := a.dictBizSrv.ListProductByItemIDs(ctx, param.Ids)
	if err != nil {
		return nil, err
	}

	pbProducts, err := adapter.BuildPbProducts(products)
	if err != nil {
		return nil, err
	}
	return &pb.ProductList{
		Count:    uint64(len(products)),
		Products: pbProducts,
	}, nil
}

// CountAllProducts 获取所有产品记录的条数
func (a *DictAction) CountAllProducts(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountAllProducts(ctx, false)
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, err
}

// CreateProduct 创建产品记录
func (a *DictAction) CreateProduct(
	ctx context.Context,
	product *pb.Product,
) (*pb.Product, error) {
	m, err := adapter.BuildProduct(product)
	if err != nil {
		return nil, err
	}

	p, err := a.dictBizSrv.CreateProduct(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbProduct(p)
}

// UpdateProductByID 根据ID更新产品记录
func (a *DictAction) UpdateProductByID(
	ctx context.Context,
	param *pb.IDProductParam,
) (*pb.Product, error) {
	product := param.GetProduct()
	m, err := adapter.BuildProduct(product)
	if err != nil {
		return nil, err
	}

	p, err := a.dictBizSrv.UpdateProductByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbProduct(p)
}

// UpdateProductByCode 根据code更新产品记录
func (a *DictAction) UpdateProductByCode(
	ctx context.Context,
	param *pb.CodeProductParam,
) (*pb.Product, error) {
	product := param.GetProduct()
	m, err := adapter.BuildProduct(product)
	if err != nil {
		return nil, err
	}

	p, err := a.dictBizSrv.UpdateProductByCode(ctx, param.GetCode(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbProduct(p)
}

// DeleteProductByID 根据ID删除产品记录
func (a *DictAction) DeleteProductByID(
	ctx context.Context,
	productID *pb.IDParam,
) (*pb.Product, error) {
	product, err := a.dictBizSrv.DeleteProductByID(ctx, productID.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbProduct(product)
}

// DeleteProductByCode 根据ID删除产品记录
func (a *DictAction) DeleteProductByCode(
	ctx context.Context,
	productCode *pb.CodeParam,
) (*pb.Product, error) {
	product, err := a.dictBizSrv.DeleteProductByCode(ctx, productCode.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbProduct(product)
}
