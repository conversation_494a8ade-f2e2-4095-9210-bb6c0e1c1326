package action

import (
	"context"
	"encoding/json"

	"golang.org/x/sync/errgroup"

	"github.com/qbox/pay-sdk/middleware/logging"
)

// ConsumedCount 计算优惠券使用数量
func (a *CouponAction) ConsumedCount(message []byte) {
	a.consumedCount(a.mqCtx, message)
}

func (a *CouponAction) consumedCount(ctx context.Context, message []byte) {
	logger := logging.GetLogger(ctx)

	logger.Info("user coupon consumed:", string(message))

	var msg mqTradeCouponConsumedMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logger.WithError(err).WithField("message", string(message)).Error("unmarshal message failed")
		return
	}
	if len(msg.CouponIDs) <= 0 {
		return
	}
	usageRecords, err := a.couponSrv.ListCouponUsageRecordsByCouponIDs(ctx, msg.CouponIDs)
	if err != nil {
		logger.WithError(err).With<PERSON>ield("couponIDs", msg.CouponIDs).Error("query usage records failed")
		return
	}
	batchIDs := make([]uint64, 0)
	batchIDSet := make(map[uint64]struct{})
	for _, record := range usageRecords {
		if _, ok := batchIDSet[record.BatchID]; ok {
			continue
		}
		batchIDSet[record.BatchID] = struct{}{}
		batchIDs = append(batchIDs, record.BatchID)
	}
	eg := errgroup.Group{}
	for _, batchID := range batchIDs {
		id := batchID
		if id <= 0 { // 测试环境可能存这样的脏数据，线上不会存在，以防万一这里过滤下
			continue
		}
		eg.Go(func() error {
			numUsed, err1 := a.couponSrv.CountCouponUsageByBatchID(ctx, id)
			if err1 != nil {
				logger.WithError(err1).WithField("batchID", id).Error("calc num used failed")
				// NOTE: 这里查询操作出错了，不返回 err, 避免中断其他的批次更新
				return nil
			}
			err1 = a.couponBatchSrv.UpdateBatchNumUsed(ctx, id, numUsed)
			if err1 != nil {
				logger.WithError(err1).WithField("batchID", id).Error("update num used failed")
				// NOTE: 这里更新操作出错了，不返回 err, 避免中断其他的批次更新
				return nil
			}
			return nil
		})
	}
	err = eg.Wait()
	// 因为上面 go func 里面都 return nil，所以这个 err 其实永远为 nil，判断下也无妨
	if err != nil {
		logger.WithError(err).WithField("couponIDs", msg.CouponIDs).Error("calc num used failed")
		return
	}
}

type mqTradeCouponConsumedMessage struct {
	CouponIDs []uint64 `json:"coupon_ids"`
}
