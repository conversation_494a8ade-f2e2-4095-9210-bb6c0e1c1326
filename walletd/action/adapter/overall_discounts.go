package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/qpay/payment/model"
)

func BuildQueryUserOverallDiscountsRequest(
	x *pb.QueryUserOverallDiscountsRequest,
) (*model.QueryUserOverallDiscountsRequest, error) {
	return &model.QueryUserOverallDiscountsRequest{
		UIDs:       x.Uids,
		PastMonths: x.PastMonths,
	}, nil
}

func BuildPbUserOverallDiscounts(
	x *model.UserOverallDiscounts,
) (*pb.UserOverallDiscounts, error) {
	return &pb.UserOverallDiscounts{
		Cash:   x.Cash.ToInt64(),
		Freenb: x.FreeNb.ToInt64(),
		Coupon: x.Coupon.ToInt64(),
	}, nil
}
