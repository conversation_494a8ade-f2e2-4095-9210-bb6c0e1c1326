rpc:
  addr: ":9704"
  gatewayaddr: ":9714"
  forwardaddr: "localhost:9704"
intl:
  ref_timezone:
    name: CST
    offset: 28800  # 8 * 3600
  default_lang_code: zh
mysql:
  host: "localhost"
  port: 3306
  username: "root"
  password: ""
  db: "pay_price"
  # utf8 只支持 Unicode BMP 字符，要用 utf8mb4 (MySQL 5.5.3+)
  # https://dev.mysql.com/doc/refman/5.5/en/charset-unicode-utf8mb4.html
  charset: "utf8mb4"
  # 永远不要用 utf8_general_ci: https://stackoverflow.com/a/766996
  collation: "utf8mb4_unicode_ci"
  parse_time: true
  loc: "Local"
  max_open_conn: 100
  max_idle_conn: 100
  max_lifetime: 5m
  debug: true
cache:
  enabled: true
  prefix: "pay-v4-price"
  redis_config:
    addrs:
      - "localhost:6379"
    mastername: ""
    readtimeout: 30s  #30 * time.Second
    idletimeout: 240s # 240 * time.Second
    pooltimeout: 240s # 240 * time.Second
  default_expires: 30m
acc:
  host: "http://localhost:9100"
  username: "root"
  password: "root"
price:
  default_pagesize: 10
services:
  dict: "localhost:9701"
  priceshim: "http://localhost:19521"
  wallet_v4: "localhost:9703"
  wallet_biz: "http://localhost:9519"
eventbus:
  url: "amqp://bo:bo@localhost:5672/"
  heart_beat: 5 # 5s
  retry_times: 3
  retry_interval: 500 # 500ms
  tls: false
  prefetch_count: 1
