package action_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/pay-sdk/wallet"
)

func TestCouponRebateInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	walletClient := sandbox.walletClient

	const scopeID uint64 = 1
	rs := []wallet.CouponRebate{
		{
			Code:        "foo",
			Type:        "PROMOTION",
			ScopeId:     scopeID,
			Name:        "充值10元赠送1元",
			Description: "活动期间充值10元赠送1元",
			Remark:      "xx活动优惠",
			Threshold:   100000,
			Money:       10000,
			EffectTime:  timestamppb.Now(),
			DeadTime:    timestamppb.Now(),
		},
		{
			Code:        "bar",
			Type:        "PROMOTION",
			ScopeId:     scopeID,
			Name:        "充值20元赠送3元",
			Description: "活动期间充值20元赠送3元",
			Remark:      "xx活动优惠",
			Threshold:   200000,
			Money:       30000,
			EffectTime:  timestamppb.Now(),
			DeadTime:    timestamppb.Now(),
		},
		{
			Code:        "baz",
			Type:        "PROMOTION",
			ScopeId:     scopeID,
			Name:        "充值30元赠送5元",
			Description: "活动期间充值30元赠送5元",
			Remark:      "xx活动优惠",
			Threshold:   300000,
			Money:       50000,
			EffectTime:  timestamppb.Now(),
			DeadTime:    timestamppb.Now(),
		},
	}
	assertFields := func(rebate *wallet.CouponRebate, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(rs) }) {
			assert.Equal(t, rs[n].GetCode(), rebate.GetCode(), msgAndArgs...)
			assert.Equal(t, rs[n].GetType(), rebate.GetType(), msgAndArgs...)
			assert.Equal(t, rs[n].GetScopeId(), rebate.GetScopeId(), msgAndArgs...)
			assert.Equal(t, rs[n].GetName(), rebate.GetName(), msgAndArgs...)
			assert.Equal(t, rs[n].GetDescription(), rebate.GetDescription(), msgAndArgs...)
			assert.Equal(t, rs[n].GetRemark(), rebate.GetRemark(), msgAndArgs...)
			assert.Equal(t, rs[n].GetThreshold(), rebate.GetThreshold(), msgAndArgs...)
			assert.Equal(t, rs[n].GetMoney(), rebate.GetMoney(), msgAndArgs...)
		}
	}
	ids := make(map[uint64]int, len(rs)+1)
	{
		for n, c := range rs {
			rebate, err := walletClient.CreateCouponRebate(context.Background(), &c)
			if assert.NoError(t, err, "CreateCouponRebate") {
				assert.NotZero(t, rebate.GetId(), "CreateCouponRebate")
				ids[rebate.GetId()] = n
			}

			rebate, err = walletClient.GetCouponRebateByID(context.Background(), &wallet.IDParam{Id: rebate.GetId()})
			if assert.NoError(t, err, "GetCouponRebateByID") {
				assertFields(rebate, n, "GetCouponRebateByID")
			}

			rebate, err = walletClient.GetCouponRebateByCode(context.Background(), &wallet.CodeParam{Code: rebate.GetCode()})
			if assert.NoError(t, err, "GetCouponRebateByCode") {
				assertFields(rebate, n, "GetCouponRebateByCode")
			}
		}

		count, err := walletClient.CountAllCouponRebates(context.Background(), &emptypb.Empty{})
		if assert.NoError(t, err, "CountAllCouponRebates") {
			assert.Len(t, rs, int(count.GetCount()), "CountAllCouponRebates")
		}
	}

	list, err := walletClient.ListAllCouponRebates(context.Background(), &wallet.PagingParam{Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListAllCouponRebates") {
		assert.Len(t, rs, int(list.GetCount()), "ListAllCouponRebates")
		for _, rebate := range list.GetCouponRebates() {
			if assert.Contains(t, ids, rebate.GetId(), "ListAllCouponRebates") {
				assertFields(rebate, ids[rebate.GetId()], "ListAllCouponRebates")
			}
		}
	}
	{
		list, err := walletClient.ListCouponRebatesByScopeID(context.Background(), &wallet.IDPagingParam{Id: scopeID, PageSize: 10, Page: 1})
		if assert.NoError(t, err, "ListCouponRebatesByScopeID") {
			assert.Len(t, rs, int(list.GetCount()), "ListCouponRebatesByScopeID")
			for _, rebate := range list.GetCouponRebates() {
				if assert.Contains(t, ids, rebate.GetId(), "ListCouponRebatesByScopeID") {
					assertFields(rebate, ids[rebate.GetId()], "ListCouponRebatesByScopeID")
				}
			}
		}
		count, err := walletClient.CountCouponRebatesByScopeID(context.Background(), &wallet.IDParam{Id: scopeID})
		if assert.NoError(t, err, "CountCouponRebatesByScopeID") {
			assert.Len(t, rs, int(count.GetCount()), "CountCouponRebatesByScopeID")
		}
	}
	for id, n := range ids {
		rebate, err := walletClient.GetCouponRebateByID(context.Background(), &wallet.IDParam{Id: id})
		if assert.NoError(t, err, "GetCouponRebateByID") {
			assertFields(rebate, n, "GetCouponRebateByID")
		}

		rs[n].Name += rebate.GetRemark()
		rebate.Name += rebate.GetRemark()
		rebate, err = walletClient.UpdateCouponRebateByID(context.Background(), &wallet.IDCouponRebateParam{Id: rebate.GetId(), CouponRebate: rebate})
		if assert.NoError(t, err, "UpdateCouponRebateByID") {
			assert.Equal(t, rs[n].GetName(), rebate.GetName(), "UpdateCouponRebateByID")
		}

		rs[n].Name += rebate.GetRemark()
		rebate.Name += rebate.GetRemark()
		rebate, err = walletClient.UpdateCouponRebateByCode(context.Background(), &wallet.CodeCouponRebateParam{Code: rebate.GetCode(), CouponRebate: rebate})
		if assert.NoError(t, err, "UpdateCouponRebateByCode") {
			assert.Equal(t, rs[n].GetName(), rebate.GetName(), "UpdateCouponRebateByCode")
		}
	}
	for id, n := range ids {
		if n%2 == 0 {
			rebate, err := walletClient.DeleteCouponRebateByID(context.Background(), &wallet.IDParam{Id: id})
			if assert.NoError(t, err, "DeleteCouponRebateByID") {
				assertFields(rebate, n, "DeleteCouponRebateByID")
			}
		} else {
			rebate, err := walletClient.DeleteCouponRebateByCode(context.Background(), &wallet.CodeParam{Code: rs[n].GetCode()})
			if assert.NoError(t, err, "DeleteCouponRebateByCode") {
				assertFields(rebate, n, "DeleteCouponRebateByCode")
			}
		}
	}
}
