package stat

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measured/model"
)

func TestDoFinalizeStat(t *testing.T) {
	// 实际上被测的业务不感知时区
	loc := time.UTC
	tm20210101 := time.Date(2021, 1, 1, 0, 0, 0, 0, loc)
	tm20210102 := time.Date(2021, 1, 2, 0, 0, 0, 0, loc)
	tm20210103 := time.Date(2021, 1, 3, 0, 0, 0, 0, loc)
	tm20210104 := time.Date(2021, 1, 4, 0, 0, 0, 0, loc)
	tm20210106 := time.Date(2021, 1, 6, 0, 0, 0, 0, loc)

	testCases := []struct {
		desc        string
		strategy    postprocessStrategy
		axesData    []*model.StatResp
		shouldPanic bool
		expected    *model.StatResp
	}{
		{
			desc:        "edge case: empty input",
			strategy:    postprocessStrategySum,
			axesData:    nil,
			shouldPanic: false,
			expected: &model.StatResp{
				Data: nil,
			},
		},
		{
			desc:        "edge case: empty input - invalid strategy",
			strategy:    postprocessStrategy(233),
			axesData:    nil,
			shouldPanic: false,
			expected: &model.StatResp{
				Data: nil,
			},
		},
		{
			desc:     "trivial case: only one axis",
			strategy: postprocessStrategySum,
			axesData: []*model.StatResp{
				{
					Data: []model.StatPoint{
						{Time: tm20210101, Value: 0},
						{Time: tm20210102, Value: 111},
						{Time: tm20210103, Value: 233},
					},
				},
			},
			shouldPanic: false,
			expected: &model.StatResp{
				Data: []model.StatPoint{
					{Time: tm20210101, Value: 0},
					{Time: tm20210102, Value: 111},
					{Time: tm20210103, Value: 233},
				},
			},
		},
		{
			desc:     "edge case: only one axis but invalid strategy",
			strategy: postprocessStrategy(233),
			axesData: []*model.StatResp{
				{
					Data: []model.StatPoint{
						{Time: tm20210101, Value: 0},
						{Time: tm20210102, Value: 111},
						{Time: tm20210103, Value: 233},
					},
				},
			},
			shouldPanic: false,
			expected: &model.StatResp{
				Data: []model.StatPoint{
					{Time: tm20210101, Value: 0},
					{Time: tm20210102, Value: 111},
					{Time: tm20210103, Value: 233},
				},
			},
		},
		{
			desc:     "multi-src case: multiple axes, sum",
			strategy: postprocessStrategySum,
			axesData: []*model.StatResp{
				{
					Data: []model.StatPoint{
						{Time: tm20210101, Value: 0},
						{Time: tm20210102, Value: 111},
						{Time: tm20210104, Value: 0},
					},
				},
				{
					Data: []model.StatPoint{
						{Time: tm20210102, Value: 12},
						{Time: tm20210103, Value: 0},
						{Time: tm20210104, Value: 10},
						{Time: tm20210106, Value: 20},
					},
				},
			},
			shouldPanic: false,
			expected: &model.StatResp{
				Data: []model.StatPoint{
					{Time: tm20210101, Value: 0},
					{Time: tm20210102, Value: 123},
					{Time: tm20210103, Value: 0},
					{Time: tm20210104, Value: 10},
					{Time: tm20210106, Value: 20},
				},
			},
		},
		{
			desc:     "multi-src case: multiple axes, max",
			strategy: postprocessStrategyMax,
			axesData: []*model.StatResp{
				{
					Data: []model.StatPoint{
						{Time: tm20210101, Value: 0},
						{Time: tm20210102, Value: 111},
						{Time: tm20210104, Value: 0},
						{Time: tm20210106, Value: 10},
					},
				},
				{
					Data: []model.StatPoint{
						{Time: tm20210102, Value: 123},
						{Time: tm20210103, Value: 0},
						{Time: tm20210104, Value: 10},
						{Time: tm20210106, Value: 20},
					},
				},
			},
			shouldPanic: false,
			expected: &model.StatResp{
				Data: []model.StatPoint{
					{Time: tm20210101, Value: 0},
					{Time: tm20210102, Value: 123},
					{Time: tm20210103, Value: 0},
					{Time: tm20210104, Value: 10},
					{Time: tm20210106, Value: 20},
				},
			},
		},
		{
			desc:     "multi-src edge case: multiple axes, max, negative value",
			strategy: postprocessStrategyMax,
			axesData: []*model.StatResp{
				{
					Data: []model.StatPoint{
						{Time: tm20210101, Value: 21},
					},
				},
				{
					Data: []model.StatPoint{
						{Time: tm20210102, Value: -12},
					},
				},
			},
			shouldPanic: false,
			expected: &model.StatResp{
				Data: []model.StatPoint{
					{Time: tm20210101, Value: 21},
					{Time: tm20210102, Value: -12},
				},
			},
		},
		{
			desc:     "multi-src edge case: multiple axes, invalid strategy",
			strategy: postprocessStrategy(233),
			axesData: []*model.StatResp{
				{
					Data: []model.StatPoint{
						{Time: tm20210101, Value: 21},
					},
				},
				{
					Data: []model.StatPoint{
						{Time: tm20210101, Value: 12},
					},
				},
			},
			shouldPanic: true,
			expected:    nil,
		},
	}

	for _, tc := range testCases {
		if tc.shouldPanic {
			assert.Panics(t, func() {
				_ = doFinalizeStat(tc.axesData, tc.strategy)
			}, tc.desc)
			continue
		}

		var actual *model.StatResp
		assert.NotPanics(t, func() {
			actual = doFinalizeStat(tc.axesData, tc.strategy)
		}, tc.desc)

		assert.NotNil(t, actual)
		// 如果有合量逻辑，time.Time 的对象都是新生成的，不会严格相等，不能直接用方便方法断言
		// assert.EqualValues(t, tc.expected, actual, tc.desc)
		assert.Len(t, actual.Data, len(tc.expected.Data))
		for i, expectedPoint := range tc.expected.Data {
			actualPoint := actual.Data[i]
			assert.True(t, expectedPoint.Time.Equal(actualPoint.Time), tc.desc)
			assert.Equal(t, expectedPoint.Value, actualPoint.Value, tc.desc)
		}
	}
}
