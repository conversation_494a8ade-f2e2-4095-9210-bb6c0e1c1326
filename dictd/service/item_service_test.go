package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestItem(t *testing.T) {
	sandbox := buildSandbox(t)

	dictService := sandbox.dictService

	const groupID uint64 = 1
	const fncProduct = 111
	is := []model.Item{
		{
			GroupID:      groupID,
			FncProductID: fncProduct,
			Code:         "kodo:flow:receive",
			Name:         "源站上行流量",
			Description:  "云存储的源站上行流量",
			Remark:       "源站上行流量备注",
			IsBasic:      true,
			Order:        1,
		},
		{
			GroupID:      groupID,
			FncProductID: fncProduct,
			Code:         "kodo:flow:transmit",
			Name:         "源站下行流量",
			Description:  "云存储的源站下行流量",
			Remark:       "云存储的源站下行流量备注",
			IsBasic:      false,
			Order:        2,
		},
	}
	assertFields := func(item *model.Item, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(is) }) {
			assert.Equal(t, is[n].GroupID, item.GroupID, msgAndArgs...)
			assert.Equal(t, is[n].Code, item.Code, msgAndArgs...)
			assert.Equal(t, is[n].Name, item.Name, msgAndArgs...)
			assert.Equal(t, is[n].Description, item.Description, msgAndArgs...)
			assert.Equal(t, is[n].Remark, item.Remark, msgAndArgs...)
			assert.Equal(t, is[n].IsBasic, item.IsBasic, msgAndArgs...)
			assert.Equal(t, is[n].Order, item.Order, msgAndArgs...)
		}
	}
	ctx := context.TODO()
	ids := make(map[uint64]int, len(is))
	for n, i := range is {
		item, err := dictService.CreateItem(ctx, &i)
		assert.NoError(t, err, "CreateItemGroup")
		assert.NotZero(t, item, "CreateItemGroup return zero id")
		ids[item.ID] = n

		item, err = dictService.GetItemByID(ctx, item.ID)
		assert.NoError(t, err, "GetItemByID")
		assertFields(item, n, "GetItemByID fields not match")

		item, err = dictService.GetItemByCode(ctx, item.Code)
		assert.NoError(t, err, "GetItemByCode")
		assertFields(item, n, "GetItemByCode fields not match")

		item.Name += "suffix1"
		is[n].Name += "suffix1"
		item, err = dictService.UpdateItemByID(ctx, item.ID, item)
		assert.NoError(t, err, "UpdateItemByID")
		assert.NotZero(t, item.ID, "UpdateItemByID return zero id")
	}

	// TODO: test onlyActive
	items, err := dictService.ListItemsByGroupID(ctx, groupID, false, 0, 10)
	if assert.NoError(t, err, "ListItemsByGroupID") {
		assert.Len(t, items, len(is), "ListItemsByGroupID unexpected length")
		count, err := dictService.CountItemsByGroupID(ctx, groupID, false)
		if assert.NoError(t, err, "CountItemsByGroupID") {
			assert.Equal(t, uint64(len(is)), count, "CountItemsByGroupID count unexpected")
		}
		for _, group := range items {
			if assert.Contains(t, ids, group.ID) {
				assertFields(&group, ids[group.ID])
			}
		}
	}

	// ListAllItems
	items, err = dictService.ListAllItems(ctx, 0, 10)
	if assert.NoError(t, err, "ListAllItems") {
		assert.Len(t, items, len(is), "ListAllItems unexpected length")

		count, err := dictService.CountAllItems(ctx)
		if assert.NoError(t, err, "CountAllItems") {
			assert.Equal(t, uint64(len(is)), count, "CountAllItems count unexpected")
		}
	}

	for id, n := range ids {
		item, err := dictService.DeleteItemByID(ctx, id)
		if assert.NoError(t, err, "DeleteItemByID") {
			assertFields(item, n)
		}
	}

	count, err := dictService.CountItemsByGroupID(ctx, groupID, false)
	if assert.NoError(t, err, "CountItemGroupsByProductID") {
		assert.Equal(t, uint64(0), count, "CountItemGroupsByProductID count after delete all is not zero")
	}
}
