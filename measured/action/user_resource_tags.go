package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/action/adapter"
)

// GetUserResourceTagsV1 获取用户分账标签
func (a *MeasureAction) GetUserResourceTagsV1(
	ctx context.Context,
	req *pb.UserResourceTagsV1Req,
) (*pb.UserResourceTagsV1Resp, error) {
	tags, err := a.srv.GetUserResourceTags(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbUserResourceTagsV1Resp(tags), nil
}
