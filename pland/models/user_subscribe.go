package models

import (
	"context"
	"fmt"
	"time"

	"github.com/fatih/structs"

	"github.com/qbox/bo-base/v4/dao"
	"qiniu.io/pay/pland/models/enums"
)

// UserSubscribeDao user subscribe dao
type UserSubscribeDao struct {
	base *dao.BaseDao

	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix cache prefix
func (u *UserSubscribeDao) GetCachePrefix() string {
	return "sufy-plan:user-subscribe:"
}

// GetCacheRefs cache refs
func (u *UserSubscribeDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewUserSubscribeDao new user subscribe dao
func NewUserSubscribeDao(base *dao.BaseDao) *UserSubscribeDao {
	cachePrefix := (*UserPlanDao)(nil).GetCachePrefix()
	return &UserSubscribeDao{
		base:        base,
		cachePrefix: cachePrefix,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:code={Code}", cachePrefix),
		),
	}
}

// UserSubscribe 用户订阅表
type UserSubscribe struct {
	ID           uint64                `json:"id" gorm:"primaryKey"`
	Month        time.Time             `json:"month" gorm:"month" sql:"type:DATETIME(6)"`
	UID          uint64                `json:"uid" gorm:"uid"`
	Status       enums.SubscribeStatus `json:"status" gorm:"status"`
	Level        enums.PlanLevel       `json:"level" gorm:"level"`
	PacksCode    string                `json:"packs_code" gorm:"packs_code"`
	SubscribeSrc enums.SubscribeSrc    `json:"subscribe_src" gorm:"subscribe_src"`
	UpdatedAt    time.Time             `json:"updated_at" gorm:"updated_at" sql:"type:DATETIME(6)"`
	CreatedAt    time.Time             `json:"created_at" sql:"type:DATETIME(6)"`
}

// SubscribePlanParam 订阅/变更参数
type SubscribePlanParam struct {
	// uid uid
	Uid uint64 `json:"uid"`
	// plan.cod
	Level enums.PlanLevel `json:"level"`
	// pack.code
	PackCode string `json:"pack_code"`
	// effect_type 生效类型
	EffectType enums.EffectType `json:"effect_type"`
	// 订阅操作的来源
	SubscribeSrc enums.SubscribeSrc `json:"subscribe_src"`
}

// GetUserSubscribe 获取用户订阅表
func (u *UserSubscribeDao) GetUserSubscribe(
	ctx context.Context,
	uid uint64,
	month time.Time,
) (*UserSubscribe, error) {
	var us UserSubscribe
	err := u.base.Model(&UserSubscribe{}).
		Where("uid = ? AND month = ?", uid, month).
		Take(&us).
		Error
	if err != nil {
		return nil, err
	}
	return &us, nil
}

// Insert save usr subscribe
func (u *UserSubscribeDao) Insert(
	ctx context.Context,
	subscribe *UserSubscribe,
) error {
	err := u.base.Model(&UserSubscribe{}).
		Save(subscribe).
		Error
	if err != nil {
		return err
	}
	return nil
}

// Update usr subscribe table
func (u *UserSubscribeDao) Update(
	ctx context.Context,
	subscribe *UserSubscribe,
) error {
	err := u.base.Model(&UserSubscribe{}).
		Where("uid = ? AND month = ?", subscribe.UID, subscribe.Month).
		Updates(structs.Map(subscribe)).
		Error
	if err != nil {
		return err
	}
	return nil
}

// Remove uid month subscribe table
func (u *UserSubscribeDao) Remove(
	ctx context.Context,
	uid uint64,
	month time.Time,
) error {
	err := u.base.Model(&UserSubscribe{}).
		Where("uid = ? AND month = ?", uid, month).
		Delete(&UserSubscribe{}).
		Error
	if err != nil {
		return err
	}
	return nil
}

// ScanUserSubscribes 查询某月所有用户的订阅表
func (u *UserSubscribeDao) ScanUserSubscribes(
	ctx context.Context,
	month time.Time,
	ch chan<- *UserSubscribe,
) error {
	db := u.base.Model(&UserSubscribe{}).
		Where("month = ?", month)

	rows, err := db.Rows()
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var ub UserSubscribe
		err = db.ScanRows(rows, &ub)
		if err != nil {
			return err
		}
		ch <- &ub
	}
	close(ch)
	return nil
}
