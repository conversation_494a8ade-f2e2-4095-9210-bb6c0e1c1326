// Code generated by MockGen. DO NOT EDIT.
// Source: kirby/service/dataplane/common/data_store_traits.go

package common

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	kirbypb "github.com/qbox/bo-sdk/kirbypb"
	adminModel "qiniu.io/pay/kirby/model/adminModel"
	dataplaneModel "qiniu.io/pay/kirby/model/dataplaneModel"
)

// MockIDataStore is a mock of IDataStore interface.
type MockIDataStore struct {
	ctrl     *gomock.Controller
	recorder *MockIDataStoreMockRecorder
}

// MockIDataStoreMockRecorder is the mock recorder for MockIDataStore.
type MockIDataStoreMockRecorder struct {
	mock *MockIDataStore
}

// NewMockIDataStore creates a new mock instance.
func NewMockIDataStore(ctrl *gomock.Controller) *MockIDataStore {
	mock := &MockIDataStore{ctrl: ctrl}
	mock.recorder = &MockIDataStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDataStore) EXPECT() *MockIDataStoreMockRecorder {
	return m.recorder
}

// EventChan mocks base method.
func (m *MockIDataStore) EventChan() <-chan time.Time {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EventChan")
	ret0, _ := ret[0].(<-chan time.Time)
	return ret0
}

// EventChan indicates an expected call of EventChan.
func (mr *MockIDataStoreMockRecorder) EventChan() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EventChan", reflect.TypeOf((*MockIDataStore)(nil).EventChan))
}

// FeedSeries mocks base method.
func (m *MockIDataStore) FeedSeries(ctx context.Context, s *dataplaneModel.FlattenedSeries) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "FeedSeries", ctx, s)
}

// FeedSeries indicates an expected call of FeedSeries.
func (mr *MockIDataStoreMockRecorder) FeedSeries(ctx, s interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FeedSeries", reflect.TypeOf((*MockIDataStore)(nil).FeedSeries), ctx, s)
}

// ListAggregatedSeries mocks base method.
func (m *MockIDataStore) ListAggregatedSeries(ctx context.Context, req *dataplaneModel.ReqListAggregatedSeries) (*dataplaneModel.AggregatedSeriesDetailList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAggregatedSeries", ctx, req)
	ret0, _ := ret[0].(*dataplaneModel.AggregatedSeriesDetailList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAggregatedSeries indicates an expected call of ListAggregatedSeries.
func (mr *MockIDataStoreMockRecorder) ListAggregatedSeries(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAggregatedSeries", reflect.TypeOf((*MockIDataStore)(nil).ListAggregatedSeries), ctx, req)
}

// ListAggregatedSeriesDistinctUIDs mocks base method.
func (m *MockIDataStore) ListAggregatedSeriesDistinctUIDs(ctx context.Context, req *dataplaneModel.ReqListAggregatedSeries) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAggregatedSeriesDistinctUIDs", ctx, req)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAggregatedSeriesDistinctUIDs indicates an expected call of ListAggregatedSeriesDistinctUIDs.
func (mr *MockIDataStoreMockRecorder) ListAggregatedSeriesDistinctUIDs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAggregatedSeriesDistinctUIDs", reflect.TypeOf((*MockIDataStore)(nil).ListAggregatedSeriesDistinctUIDs), ctx, req)
}

// ListItemUIDsByMonthAndZone mocks base method.
func (m *MockIDataStore) ListItemUIDsByMonthAndZone(ctx context.Context, itemCodes []string, month time.Time, zoneCode int64, epoch time.Time) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListItemUIDsByMonthAndZone", ctx, itemCodes, month, zoneCode, epoch)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListItemUIDsByMonthAndZone indicates an expected call of ListItemUIDsByMonthAndZone.
func (mr *MockIDataStoreMockRecorder) ListItemUIDsByMonthAndZone(ctx, itemCodes, month, zoneCode, epoch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListItemUIDsByMonthAndZone", reflect.TypeOf((*MockIDataStore)(nil).ListItemUIDsByMonthAndZone), ctx, itemCodes, month, zoneCode, epoch)
}

// ListSeries mocks base method.
func (m *MockIDataStore) ListSeries(ctx context.Context, req *dataplaneModel.ReqListSeries) (*dataplaneModel.SeriesDetailList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSeries", ctx, req)
	ret0, _ := ret[0].(*dataplaneModel.SeriesDetailList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSeries indicates an expected call of ListSeries.
func (mr *MockIDataStoreMockRecorder) ListSeries(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSeries", reflect.TypeOf((*MockIDataStore)(nil).ListSeries), ctx, req)
}

// ListSeriesDistinctUIDs mocks base method.
func (m *MockIDataStore) ListSeriesDistinctUIDs(ctx context.Context, req *dataplaneModel.ReqListSeries) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSeriesDistinctUIDs", ctx, req)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSeriesDistinctUIDs indicates an expected call of ListSeriesDistinctUIDs.
func (mr *MockIDataStoreMockRecorder) ListSeriesDistinctUIDs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSeriesDistinctUIDs", reflect.TypeOf((*MockIDataStore)(nil).ListSeriesDistinctUIDs), ctx, req)
}

// ListUserItemsByMonthAndZone mocks base method.
func (m *MockIDataStore) ListUserItemsByMonthAndZone(ctx context.Context, uid uint64, month time.Time, zoneCode int64, epoch time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserItemsByMonthAndZone", ctx, uid, month, zoneCode, epoch)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUserItemsByMonthAndZone indicates an expected call of ListUserItemsByMonthAndZone.
func (mr *MockIDataStoreMockRecorder) ListUserItemsByMonthAndZone(ctx, uid, month, zoneCode, epoch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserItemsByMonthAndZone", reflect.TypeOf((*MockIDataStore)(nil).ListUserItemsByMonthAndZone), ctx, uid, month, zoneCode, epoch)
}

// NotifyEngineStart mocks base method.
func (m *MockIDataStore) NotifyEngineStart() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "NotifyEngineStart")
}

// NotifyEngineStart indicates an expected call of NotifyEngineStart.
func (mr *MockIDataStoreMockRecorder) NotifyEngineStart() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyEngineStart", reflect.TypeOf((*MockIDataStore)(nil).NotifyEngineStart))
}

// NotifyEngineStop mocks base method.
func (m *MockIDataStore) NotifyEngineStop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "NotifyEngineStop")
}

// NotifyEngineStop indicates an expected call of NotifyEngineStop.
func (mr *MockIDataStoreMockRecorder) NotifyEngineStop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyEngineStop", reflect.TypeOf((*MockIDataStore)(nil).NotifyEngineStop))
}

// PersistAllPendingSeries mocks base method.
func (m *MockIDataStore) PersistAllPendingSeries(ctx context.Context) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PersistAllPendingSeries", ctx)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PersistAllPendingSeries indicates an expected call of PersistAllPendingSeries.
func (mr *MockIDataStoreMockRecorder) PersistAllPendingSeries(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PersistAllPendingSeries", reflect.TypeOf((*MockIDataStore)(nil).PersistAllPendingSeries), ctx)
}

// ProvisionShards mocks base method.
func (m *MockIDataStore) ProvisionShards(ctx context.Context, month time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProvisionShards", ctx, month)
	ret0, _ := ret[0].(error)
	return ret0
}

// ProvisionShards indicates an expected call of ProvisionShards.
func (mr *MockIDataStoreMockRecorder) ProvisionShards(ctx, month interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProvisionShards", reflect.TypeOf((*MockIDataStore)(nil).ProvisionShards), ctx, month)
}

// QueryAggregatedSeriesData mocks base method.
func (m *MockIDataStore) QueryAggregatedSeriesData(ctx context.Context, uid uint64, month time.Time, itemCode string, zoneCode int64, desiredG adminModel.Granularity, epoch time.Time) ([]*kirbypb.StatPoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAggregatedSeriesData", ctx, uid, month, itemCode, zoneCode, desiredG, epoch)
	ret0, _ := ret[0].([]*kirbypb.StatPoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAggregatedSeriesData indicates an expected call of QueryAggregatedSeriesData.
func (mr *MockIDataStoreMockRecorder) QueryAggregatedSeriesData(ctx, uid, month, itemCode, zoneCode, desiredG, epoch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAggregatedSeriesData", reflect.TypeOf((*MockIDataStore)(nil).QueryAggregatedSeriesData), ctx, uid, month, itemCode, zoneCode, desiredG, epoch)
}

// RunAggregations mocks base method.
func (m *MockIDataStore) RunAggregations(ctx context.Context, month, since, epoch time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunAggregations", ctx, month, since, epoch)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunAggregations indicates an expected call of RunAggregations.
func (mr *MockIDataStoreMockRecorder) RunAggregations(ctx, month, since, epoch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunAggregations", reflect.TypeOf((*MockIDataStore)(nil).RunAggregations), ctx, month, since, epoch)
}
