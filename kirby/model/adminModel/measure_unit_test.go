package adminModel_test

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/kirby/model/adminModel"
)

func TestMeasureUnitORM(t *testing.T) {
	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	type testTable struct {
		Foo adminModel.MeasureUnit `gorm:"type:VARCHAR(20);not null"`
		Bar int
	}
	testWrap.DB().AutoMigrate(&testTable{})

	type testTable2 struct {
		// actually cannot be null
		Baz adminModel.MeasureUnit `gorm:"type:VARCHAR(20);null"`
	}
	testWrap.DB().AutoMigrate(&testTable2{})

	type testTable3 struct {
		// and cannot be int either
		Quux adminModel.MeasureUnit `gorm:"type:bigint;not null"`
	}
	testWrap.DB().AutoMigrate(&testTable3{})

	t.Run("RoundTrip", func(t *testing.T) {
		table := testWrap.DB().Model(&testTable{})

		err := table.Save(&testTable{
			Foo: "",
			Bar: 0,
		}).Error
		assert.NoError(t, err)

		err = table.Save(&testTable{
			Foo: "GiB",
			Bar: 1,
		}).Error
		assert.NoError(t, err)

		var val testTable
		err = table.First(&val, &testTable{Bar: 1}).Error
		assert.NoError(t, err)
		assert.Equal(t, adminModel.MeasureUnit("GiB"), val.Foo)
	})

	t.Run("IncorrectUsages", func(t *testing.T) {
		table2 := testWrap.DB().Model(&testTable2{})
		table3 := testWrap.DB().Model(&testTable3{})

		// this should succeed...
		err := table2.Debug().Exec("INSERT INTO `test_table2` (`baz`) VALUES (NULL)").Error
		assert.NoError(t, err)

		// ... but the row shouldn't be retrievable due to the NULL
		// XXX but it seems either MySQL or GORM allowed the case, the Scan method isn't
		// even called... let's record this fact for now
		var x testTable2
		err = table2.Debug().First(&x).Error
		_ = err // assert.Error(t, err)
		assert.Equal(t, testTable2{Baz: adminModel.MeasureUnit("")}, x)

		// due to weak-typedness of SQL, saving things like "233" might
		// work, but definitely not others
		err = table3.Save(&testTable3{Quux: adminModel.MeasureUnit("bps")}).Error
		assert.Error(t, err)
	})
}
