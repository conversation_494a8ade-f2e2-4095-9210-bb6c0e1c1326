package adapter

import (
	pb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/model/dataplaneModel"
)

func buildPbAggregatedSource(
	in *dataplaneModel.AggregatedSource,
) *pb.AggregatedSource {
	return &pb.AggregatedSource{
		Kind: pb.SeriesKind(in.Kind),
		Key:  in.Key,
	}
}

func BuildPbAggregatedSources(
	in []*dataplaneModel.AggregatedSource,
) []*pb.AggregatedSource {
	if in == nil {
		return nil
	}

	y := make([]*pb.AggregatedSource, len(in))
	for i, x := range in {
		y[i] = buildPbAggregatedSource(x)
	}
	return y
}
