package action

import (
	"context"
	"errors"

	"github.com/qbox/pay-sdk/middleware/user"

	"github.com/qbox/pay-sdk/middleware/logging"
	"qiniu.io/pay/coupon/action/adapter"

	pb "github.com/qbox/pay-sdk/wallet"
)

// LaunchCouponApplication 发起申请 coupon 审批
func (a *CouponAction) LaunchCouponApplication(
	ctx context.Context,
	req *pb.LaunchCouponApplicationParams,
) (*pb.LaunchCouponApplicationResp, error) {
	l := logging.GetLogger(ctx)

	operatorInfo := user.GetUser(ctx)
	if operatorInfo == nil {
		return nil, errors.New("no operator")
	}

	params, err := adapter.BuildLaunchCouponApplication(req, operatorInfo.Email)
	if err != nil {
		l.Errorln("invoke adapter.BuildLaunchCouponApplication error", err)
		return nil, err
	}
	l.Infof("LaunchCouponApplication applicantEmail: %+v, params:%+v", operatorInfo.Email, params)
	excode, err := a.couponSrv.LaunchCouponApplication(ctx, params)
	if err != nil {
		l.Errorln("invoke couponSrv.LaunchCouponApplication error", err)
		return nil, err
	}

	return &pb.LaunchCouponApplicationResp{
		Excode: excode,
	}, nil
}
