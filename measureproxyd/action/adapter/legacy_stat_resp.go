package adapter

import (
	pb "github.com/qbox/pay-sdk/measureproxy"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/measureproxyd/model"
)

// BuildPbLegacyStatResp converts from model.LegacyStatResp to the protobuf type.
func BuildPbLegacyStatResp(x []model.RespTableItemWithoutGroup) (*pb.LegacyStatResp, error) {
	data := make([]*pb.RespTableItemWithoutGroup, len(x))
	for i, o := range x {
		p, err := BuildPbRespTableItemWithoutGroup(&o)
		if err != nil {
			return nil, errors.Trace(err)
		}
		data[i] = p
	}
	return &pb.LegacyStatResp{
		Data: data,
	}, nil
}

// BuildModelLegacyStatResp converts from pb.LegacyStatResp to the model layer type.
func BuildModelLegacyStatResp(x *pb.LegacyStatResp) ([]model.RespTableItemWithoutGroup, error) {
	data := make([]model.RespTableItemWithoutGroup, len(x.Data))
	for i, o := range x.Data {
		p, err := BuildModelRespTableItemWithoutGroup(o)
		if err != nil {
			return nil, errors.Trace(err)
		}
		data[i] = *p
	}
	return data, nil
}
