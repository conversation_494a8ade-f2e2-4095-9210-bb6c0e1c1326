package adapter

import (
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/model"
)

// BuildPbItemsInUseRespV1 converts from model.ItemsInUseResp to the protobuf type.
func BuildPbItemsInUseRespV1(x model.ItemsInUseResp) (*pb.ItemsInUseRespV1, error) {
	itemCodes := make([]string, len(x))
	_ = copy(itemCodes, x)

	return &pb.ItemsInUseRespV1{
		ItemCodes: itemCodes,
	}, nil
}

// BuildModelItemsInUseRespV1 converts from pb.ItemsInUseRespV1 to the model layer type.
func BuildModelItemsInUseRespV1(x *pb.ItemsInUseRespV1) (model.ItemsInUseResp, error) {
	itemCodes := make([]string, len(x.ItemCodes))
	_ = copy(itemCodes, x.ItemCodes)

	return model.ItemsInUseResp(itemCodes), nil
}
