package adapter

import (
	pb "github.com/qbox/pay-sdk/measureproxy"
	"qiniu.io/pay/measureproxyd/service/model"
)

func BuildPbProxyUserResourceTagsV1Resp(list []*model.ResourceTag) *pb.ProxyUserResourceTagsV1Resp {
	r := &pb.ProxyUserResourceTagsV1Resp{
		List: make([]*pb.UserResourceTags, 0, len(list)),
	}
	for _, obj := range list {
		tmp := &pb.UserResourceTags{
			Resource: &pb.Resource{
				ResourceType: obj.ResourceType,
				ResourceName: obj.ResourceName,
			},
		}
		for _, o := range obj.Tags {
			tmp.Tags = append(tmp.Tags, &pb.Tag{
				Key:   o.Key,
				Value: o.Value,
			})
		}
		r.List = append(r.List, tmp)
	}
	return r
}
