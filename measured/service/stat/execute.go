package stat

import (
	"context"
	"runtime/debug"
	"sync"

	"github.com/samber/lo"
	log "github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-sdk/kirbypb"
	mppb "github.com/qbox/pay-sdk/measureproxy"
	"github.com/qbox/pay-sdk/middleware/logging"

	"qiniu.io/pay/measured/model"
)

// queryAxes 给所有轴拉量
//
// 会拿拉量上下文的写锁
func (s *measuredStatClient) queryAxes(
	ctx context.Context,
	scx *statQueryContext,
) error {
	// TODO: 只锁最后覆盖指针的一句，还是锁整个函数？
	scx.dataMu.Lock()
	defer scx.dataMu.Unlock()

	type resultT struct {
		ok  *model.StatResp
		err error
	}
	result := make([]resultT, len(scx.axes))

	// 在写锁的保护下并发拉量
	var wg sync.WaitGroup
	wg.Add(len(scx.axes))
	for i, ap := range scx.axes {
		// NOTE: 注意这里的循环变量坑，它们的值必须用传参的方式固定下来，函数体内不准用循环变量本体
		// 取两个不一样的参数名区分一下
		go func(axisIdx int, params *axisQueryParams) {
			// 万一有 panic 要记录下来
			defer func() {
				if v := recover(); v != nil {
					logger := logging.GetLogger(ctx)
					logger.WithFields(log.Fields{
						"error":      v,
						"stacktrace": string(debug.Stack()),
					}).Error("axis querying panicked")
					result[axisIdx].err = errors.Trace(ErrAxisQueryPanicked{V: v})
				}

				wg.Done()
			}()

			resp, err := s.queryOneAxis(ctx, params, scx.debug)
			if err != nil {
				result[axisIdx].err = errors.Trace(err)
				return
			}

			result[axisIdx].ok = resp
		}(i, ap)
	}
	wg.Wait()

	errored := false
	errs := make([]error, 0)
	newData := make([]*model.StatResp, len(result))
	for i, r := range result {
		if r.err != nil {
			errored = true
			errs = append(errs, r.err)
			continue
		}

		newData[i] = r.ok
	}

	if errored {
		return errors.Trace(ErrQueryAxesFailed{Errors: errs})
	}

	// 一次性替换掉上下文中的结果列表
	scx.axesData = newData

	return nil
}

// queryOneAxis 拉一条轴的量
//
// 因为主要的参数拼接工作都在前面做了，这里就特别清爽
func (s *measuredStatClient) queryOneAxis(
	ctx context.Context,
	ap *axisQueryParams,
	debug bool,
) (*model.StatResp, error) {
	p := ap.params

	// woah
	if p.KirbyParams != nil {
		return s.queryOneAxisWithKirby(ctx, ap)
	}

	// cannot trivially cast
	query := make([]*mppb.KV, len(p.ItemExtras.Query))
	for i, kv := range p.ItemExtras.Query {
		query[i] = (*mppb.KV)(kv)
	}

	resp, err := s.mpClient.ProxyGetLegacyStat(ctx, &mppb.StatSrcQueryParams{
		Uid:      ap.uid,
		Begin:    ap.begin,
		End:      ap.end,
		HaveZone: p.HaveZone,
		ZoneCode: p.ZoneCode,
		Key:      p.ItemExtras.Key,
		G:        p.ItemExtras.G,
		Select:   p.ItemExtras.Select,
		Query:    query,
		Src:      p.Src,
		Resource: &mppb.Resource{
			ResourceType: p.Resource.ResourceType,
			ResourceName: p.Resource.ResourceName,
		},
		Debug: debug,
	})
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 求值
	// NOTE: 在这里求值，主要还是因为与现有的拉量模型比较接近，即求值之后的结果才是原始量
	// 如果“原始量”的定义发生变化，这里当然就要跟着改
	data := make([]model.StatPoint, len(resp.Data))
	expr := ap.params.ItemExtras.Expr
	for i, point := range resp.Data {
		v, err := expr.EvalWith(point.Values)
		if err != nil {
			return nil, errors.Trace(err)
		}

		err = point.Time.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}

		data[i].Time = point.Time.AsTime()
		data[i].Value = v
	}

	result := &model.StatResp{
		Data: data,
	}

	return result, nil
}

// queryOneAxisWithKirby 向 Kirby 数据源查询一条轴的量
func (s *measuredStatClient) queryOneAxisWithKirby(
	ctx context.Context,
	ap *axisQueryParams,
) (*model.StatResp, error) {
	loc := tz.MustLocationFromCtx(ctx)

	kirbyPoints, err := s.kirbyDP.QueryAggregatedSeriesData(
		ctx,
		uint64(ap.uid),
		ap.begin.AsTime().In(loc),
		ap.params.KirbyParams.ItemCode,
		ap.params.KirbyParams.ZoneCode,
		ap.params.KirbyParams.DataType,
		ap.params.KirbyParams.G,
		ap.params.KirbyParams.Epoch,
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	data := lo.Map(kirbyPoints, func(x *kirbypb.StatPoint, _ int) model.StatPoint {
		return model.StatPoint{Time: x.Time.AsTime(), Value: int64(x.Value)}
	})

	return &model.StatResp{Data: data}, nil
}
