package adminModel

import (
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// AggregationRule 聚合规则
type AggregationRule struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// Code 聚合规则的唯一标识
	Code string `gorm:"column:code;type:VARCHAR(64);not null;unique_index:idx_aggr_code;comment:'唯一 ID'"`
	// IsDraft 草稿状态标记（计量对接过程中，暂不生效的中间编辑状态）
	IsDraft bool `gorm:"column:is_draft;type:tinyint(1);not null;default:1;comment:'草稿状态标记（计量对接过程中，暂不生效的中间编辑状态）'"`

	// RTDCode 聚合规则匹配的 RTD code，精确匹配
	RTDCode string `gorm:"column:rtd_code;type:VARCHAR(64);not null;comment:'聚合规则匹配的 RTD code，精确匹配'"`
	// MMCode 聚合规则匹配的计量指标 code，精确匹配
	MMCode string `gorm:"column:mm_code;type:VARCHAR(64);not null;comment:'聚合规则匹配的计量指标 code，精确匹配'"`
	// SPMatches 聚合规则的序列属性匹配规则，以 JSON map 形式存储
	// TODO: 后续可能迁移到 SQL-like 表达式以支持更丰富的语义
	SPMatches SPMatchExpr `gorm:"column:sp_matches;type:TEXT(65535);not null;comment:'聚合规则的序列属性匹配规则，以 JSON map 形式存储'"`

	// DestItemCode 聚合规则匹配后的目标 item code
	DestItemCode string `gorm:"column:dest_item_code;type:VARCHAR(256);not null;comment:'聚合规则匹配后的目标 item code'"`
	// DestZoneCode 聚合规则匹配后的目标 zone code
	DestZoneCode int64 `gorm:"column:dest_zone_code;not null;comment:'聚合规则匹配后的目标 zone code'"`

	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);default:now(6)"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME(6);default:now(6) ON UPDATE now(6)"`
}

// AggregationRuleDao is data access object of AggregationRule model
type AggregationRuleDao struct {
	base *dao.BaseDao
}

// NewAggregationRuleDao is constructor of AggregationRuleDao
func NewAggregationRuleDao(base *dao.BaseDao) *AggregationRuleDao {
	return &AggregationRuleDao{
		base: base,
	}
}

// Save inserts or updates a AggregationRule by id
func (d *AggregationRuleDao) Save(model *AggregationRule) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Save(value).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

func (d *AggregationRuleDao) GetByCode(
	epoch time.Time,
	code string,
	includeDrafts bool,
) (*AggregationRule, error) {
	expr := squirrel.And{
		squirrel.Eq{"code": code},
		squirrel.LtOrEq{"created_at": epoch},
	}
	if !includeDrafts {
		expr = append(expr, squirrel.Eq{"is_draft": 0})
	}
	where, args, err := expr.ToSql()
	if err != nil {
		return nil, err
	}

	var result []*AggregationRule
	err = d.base.Execute(func(value any) error {
		return d.base.Model(&AggregationRule{}).
			Where(where, args...).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{"epoch": epoch, "code": code, "includeDrafts": includeDrafts})
	}

	if len(result) == 0 {
		return nil, nil
	}

	return result[0], nil
}

func (d *AggregationRuleDao) ListAll(
	epoch time.Time,
	includeDrafts bool,
) ([]*AggregationRule, error) {
	expr := squirrel.And{
		squirrel.LtOrEq{"created_at": epoch},
	}
	if !includeDrafts {
		expr = append(expr, squirrel.Eq{"is_draft": 0})
	}
	where, args, err := expr.ToSql()
	if err != nil {
		return nil, err
	}

	var result []*AggregationRule
	err = d.base.Execute(func(value any) error {
		return d.base.Model(&AggregationRule{}).
			Where(where, args...).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{"epoch": epoch, "includeDrafts": includeDrafts})
	}
	return result, nil
}
