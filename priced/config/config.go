package config

import (
	"os"

	"gopkg.in/yaml.v2"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/db"
	"github.com/qbox/bo-base/v4/eventbus"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/pay-sdk/base/account"
)

// PriceConfig config for price business
type PriceConfig struct {
	DefaultPageSize uint64 `yaml:"default_pagesize"`
}

// ServiceConfig contain host of service dependencies
type ServiceConfig struct {
	Dict      string `yaml:"dict"`
	Priceshim string `yaml:"priceshim"`
	WalletV4  string `yaml:"wallet_v4"`
	WalletBiz string `yaml:"wallet_biz"`
}

// PricedConfig is config for priced service
type PricedConfig struct {
	RPC      rpc.Config
	Intl     intl.Config
	MySQL    db.MySQLConfig
	Cache    dao.CacheConfig
	Acc      account.AccConfig
	Price    PriceConfig
	Services ServiceConfig
	Eventbus eventbus.Config `yaml:"eventbus"`
}

// LoadPricedConfig load config from yaml file
func LoadPricedConfig(yamlPath string) (*PricedConfig, error) {
	byts, err := os.ReadFile(yamlPath)
	if err != nil {
		return nil, err
	}
	conf := &PricedConfig{}
	err = yaml.UnmarshalStrict(byts, conf)
	if err != nil {
		return nil, err
	}
	return conf, nil
}
