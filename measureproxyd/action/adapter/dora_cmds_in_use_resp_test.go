package adapter

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measureproxyd/model"
)

func TestDoraCmdsInUseRespRoundTrip(t *testing.T) {
	x := model.DoraCmdsInUseResp{
		{Key: "common", Cmd: "foo"},
		{Key: "fopg", Cmd: "bar"},
		{Key: "common", Cmd: "baz"},
	}

	y, err := BuildPbDoraCmdsInUseResp(x)
	assert.NoError(t, err)

	z, err := BuildModelDoraCmdsInUseResp(y)
	assert.NoError(t, err)

	assert.Equal(t, x, z)
}
