package adapter

import (
	pb "github.com/qbox/pay-sdk/kirby"
	"google.golang.org/protobuf/types/known/timestamppb"

	"qiniu.io/pay/kirby/model/dataplaneModel"
)

func buildPbAggregatedSeriesDetail(
	in *dataplaneModel.AggregatedSeriesDetail,
) *pb.AggregatedSeriesDetail {
	return &pb.AggregatedSeriesDetail{
		AggSeriesKey: in.AggSeriesKey,
		Uid:          in.UID,
		Start:        timestamppb.New(in.Start),
		End:          timestamppb.New(in.End),
		ItemCode:     in.ItemCode,
		ZoneCode:     in.ZoneCode,
		AggrCode:     in.AggRCode,
		Unit:         in.Unit,
		G:            pb.Granularity(in.G),
		Data:         in.Data,
		Ctime:        timestamppb.New(in.Ctime),
		Sources:      BuildPbAggregatedSources(in.Sources),
	}
}

func buildPbAggregatedSeriesDetails(
	in []*dataplaneModel.AggregatedSeriesDetail,
) []*pb.AggregatedSeriesDetail {
	if in == nil {
		return nil
	}

	y := make([]*pb.AggregatedSeriesDetail, len(in))
	for i, x := range in {
		y[i] = buildPbAggregatedSeriesDetail(x)
	}
	return y
}

func BuildPbAggregatedSeriesDetailList(
	in *dataplaneModel.AggregatedSeriesDetailList,
) *pb.AggregatedSeriesDetailList {
	return &pb.AggregatedSeriesDetailList{
		Data:  buildPbAggregatedSeriesDetails(in.Data),
		Count: in.Count,
	}
}
