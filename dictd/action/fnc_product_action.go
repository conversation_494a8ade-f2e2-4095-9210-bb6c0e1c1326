package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/dict"

	"qiniu.io/pay/dictd/action/adapter"
)

// GetFncProduct 根据 ID 获取一个财务口径产品线
func (a *DictAction) GetFncProductByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.FncProduct, error) {
	fncProduct, err := a.dictBizSrv.GetFncProductByID(ctx, param.Id)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbFncProduct(fncProduct)
}

// ListFncProductsByIDs 根据 ID 列表列出财务口径产品线
func (a *DictAction) ListFncProductsByIDs(
	ctx context.Context,
	param *pb.MultiIDParam,
) (*pb.FncProductList, error) {
	fncProducts, err := a.dictBizSrv.ListFncProductsByIDs(ctx, param.Ids)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbFncProductList(fncProducts)
}

// CreateFncProduct 创建一个财务口径产品线
func (a *DictAction) CreateFncProduct(
	ctx context.Context,
	param *pb.FncProduct,
) (*pb.FncProduct, error) {
	model, err := adapter.BuildFncProduct(param)
	if err != nil {
		return nil, err
	}
	fncProductModel, err := a.dictBizSrv.CreateFncProduct(ctx, model)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbFncProduct(fncProductModel)
}
