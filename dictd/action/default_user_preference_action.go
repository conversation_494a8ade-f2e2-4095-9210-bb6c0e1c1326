package action

import (
	"context"
	"time"

	pb "github.com/qbox/pay-sdk/dict"
	"google.golang.org/protobuf/types/known/emptypb"
	"qiniu.io/pay/dictd/action/adapter"
)

func (a *DictAction) GetDefaultUserPreference(
	ctx context.Context,
	request *pb.GetDefaultUserPreferenceRequest,
) (*pb.DefaultUserPreference, error) {
	userRegisterTime := time.Now()
	if request.GetUserRegisterTime() != nil {
		userRegisterTime = request.GetUserRegisterTime().AsTime()
	}
	itemCode := request.GetItemCode()
	preferences, err := a.dictBizSrv.GetZonePreferenceByItemCode(ctx, itemCode, userRegisterTime)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbDefaultUserPreference(preferences), nil
}

func (a *DictAction) ListDefaultUserPreferences(
	ctx context.Context,
	request *pb.ListDefaultUserPreferencesRequest,
) (*pb.DefaultUserPreferences, error) {
	userRegisterTime := time.Now()
	if request.GetUserRegisterTime() != nil {
		userRegisterTime = request.GetUserRegisterTime().AsTime()
	}
	offset, limit := a.Paging(request)
	preferences, err := a.dictBizSrv.ListDefaultUserPreferences(ctx, userRegisterTime, offset, limit)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbDefaultUserPreferences(preferences), nil
}

func (a *DictAction) UpdateDefaultUserPreferences(
	ctx context.Context,
	request *pb.UpdateDefaultUserPreferencesRequest,
) (*emptypb.Empty, error) {
	userRegisterTime := time.Now()
	if request.GetUserRegisterTime() != nil {
		userRegisterTime = request.GetUserRegisterTime().AsTime()
	}
	err := a.dictBizSrv.UpdateDefaultUserPreferences(
		ctx, []string{request.GetItemCode()}, request.GetZonePreferences(), userRegisterTime,
	)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (a *DictAction) CreateDefaultUserPreferences(
	ctx context.Context,
	preference *pb.DefaultUserPreference,
) (*emptypb.Empty, error) {
	userPreferences := adapter.BuildDefaultUserPreferences(preference)
	err := a.dictBizSrv.CreateDefaultUserPreferences(ctx, userPreferences)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
