package service

import (
	"context"
	"time"

	"github.com/qbox/pay-sdk/wallet"

	"golang.org/x/sync/errgroup"

	pb "github.com/qbox/pay-sdk/dict"
	"github.com/qbox/pay-sdk/v3/wallet/client/operations"
	"github.com/qbox/pay-sdk/v3/wallet/models"
	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
	"qiniu.io/pay/priced/model"
)

const timeFormat = "2006-01-02"

// SwitchPriceUserAlgorithmByID 切换计费方式请求参数
type SwitchPriceUserAlgorithmByID struct {
	UID             uint64
	PriceID         uint64
	ItemID          uint64
	ZoneID          uint64
	FromAlgorithmID uint64
	ToAlgorithmID   uint64
	// SwitchAt （选填）切换时间，实际以该时间的当天零点入库
	SwitchAt *time.Time
}

// SwitchPriceUserAlgorithmByCode 切换计费方式请求参数
type SwitchPriceUserAlgorithmByCode struct {
	UID               uint64
	PriceID           uint64
	ItemCode          string
	ZoneCode          int64
	FromAlgorithmCode string
	ToAlgorithmCode   string
}

const largePageSize = 10000

// SwitchUserPriceAlgorithmByCode 切换用户指定 item_code 对应的 algorithm_id
func (s *PriceBizService) SwitchUserPriceAlgorithmByCode(
	ctx context.Context,
	param *SwitchPriceUserAlgorithmByCode,
) error {
	l := logging.GetLogger(ctx).WithField("param", param)
	eg := errgroup.Group{}

	var item *pb.Item
	eg.Go(func() error {
		res, err1 := s.dictClient.GetItemByCode(
			ctx,
			&pb.CodeParam{Code: param.ItemCode},
		)
		if err1 != nil {
			return errors.Trace(err1)
		}
		item = res
		return nil
	})
	var zone *pb.Zone
	eg.Go(func() error {
		res, err1 := s.dictClient.GetZoneByCode(ctx, &pb.ZoneCodeParam{Code: param.ZoneCode})
		if err1 != nil {
			return errors.Trace(err1)
		}
		zone = res
		return nil
	})

	var currency *wallet.Currency
	eg.Go(func() error {
		res, err1 := s.paymentServiceClient.GetSingleCurrency(
			ctx,
			&wallet.UIDParam{Uid: param.UID},
		)
		if err1 != nil {
			return errors.Trace(err1)
		}
		currency = res
		return nil
	})
	err := eg.Wait()
	if err != nil {
		return errors.Trace(err)
	}
	// 从数据库拿到该计费项区域的所有 priceItems
	// 遍历 priceItems 数组，根据 priceItem.AlgorithmID 获取到对应的 itemDataTypeAlgorithm 信息
	// 将 FromAlgorithmCode 与 itemDataTypeAlgorithm.Code 匹配，并获取对应的 algorithmID
	priceItems, err := s.priceDao.PriceItem.GetItemPriceByItemIDZoneIDAndTimePoint(
		param.PriceID, item.GetId(), zone.GetId(), base.NewHNS(time.Now()),
		currency.GetCurrencyType(),
	)
	if err != nil {
		return errors.Trace(err)
	}
	if len(priceItems) < 2 {
		// 没有足够的计费项数量来切换
		return errors.New("item's price does not have specified algorithms")
	}
	algorithmCodeToIDMap := make(map[string]uint64)
	for _, priceItem := range priceItems {
		itemDataTypeAlgorithm, err1 := s.dictClient.GetItemDataTypeAlgorithmByID(ctx, &pb.IDParam{Id: priceItem.AlgorithmID})
		if err1 != nil {
			return errors.Trace(err1)
		}

		algorithmCodeToIDMap[itemDataTypeAlgorithm.GetCode()] = priceItem.AlgorithmID
	}
	l.WithField("algorithm_code_to_id_map", algorithmCodeToIDMap).Infoln("<SwitchUserPriceAlgorithmByCode> algorithmCodeToIDMap")
	fromAlgorithmID, ok := algorithmCodeToIDMap[param.FromAlgorithmCode]
	if !ok {
		// fromAlgorithmCode 对应的 algorithmID 找不到
		return errors.New("item's price does not have specified algorithm(from)")
	}
	toAlgorithmID, ok := algorithmCodeToIDMap[param.ToAlgorithmCode]
	if !ok {
		// toAlgorithmCode 对应的 algorithmID 找不到
		return errors.New("item's price does not have specified algorithm(to)")
	}

	return s.SwitchUserPriceAlgorithmByID(ctx, &SwitchPriceUserAlgorithmByID{
		UID:             param.UID,
		PriceID:         param.PriceID,
		ItemID:          item.GetId(),
		ZoneID:          zone.GetId(),
		FromAlgorithmID: fromAlgorithmID,
		ToAlgorithmID:   toAlgorithmID,
	})
}

// SwitchUserPriceAlgorithmByID 切换用户指定 item_id，zone_id 对应的 algorithm_id
func (s *PriceBizService) SwitchUserPriceAlgorithmByID(
	ctx context.Context,
	param *SwitchPriceUserAlgorithmByID,
) error {
	l := logging.GetLogger(ctx)
	// 如果 from 和 to 一样，那么直接返回
	if param.FromAlgorithmID == param.ToAlgorithmID {
		return nil
	}
	l.WithField("param", param).Info("<SwitchUserPriceAlgorithmByID> start switching")
	now := time.Now()
	if param.SwitchAt != nil {
		now = *param.SwitchAt
	}

	isSwitchWithinADay, lastUserAlgorithm, err := s.isSwitchWithinADay(ctx, param.UID, param.ItemID, param.ZoneID, now)
	if err != nil {
		return errors.Trace(err)
	}
	if isSwitchWithinADay {
		return errors.New("cannot switch item algorithm more than twice within same day")
	}

	hasVIPPrice, err := s.hasVIPPrice(ctx, param.UID, param.ItemID, param.ZoneID, now)
	if err != nil {
		return errors.Trace(err)
	}
	if hasVIPPrice {
		return errors.New("got VIP price at present")
	}

	defaultPriceTable, err := s.GetDefaultPriceTable(ctx)
	if err != nil {
		return errors.Trace(err)
	}
	getItemPriceByTimePointParam := &model.GetItemPriceByTimePointParam{
		PriceID:     defaultPriceTable.ID,
		ItemID:      param.ItemID,
		ZoneID:      param.ZoneID,
		AlgorithmID: param.FromAlgorithmID,
		At:          base.NewHNS(now),
	}
	// 查询 param.ToAlgorithmID / FromAlgorithmID 条目，不存在也是有问题的
	fromAlgoRelatedPriceItem, err := s.priceDao.PriceItem.GetItemPriceByItemIDZoneIDAlgorithmIDAndTimePoint(getItemPriceByTimePointParam)
	if err != nil {
		l.WithField("FromAlgorithmID", param.FromAlgorithmID).WithError(err).
			Error("SwitchUserPriceAlgorithmByID invoke GetItemPriceByItemIDZoneIDAlgorithmIDAndTimePoint error")
		return errors.Trace(err)
	}

	getItemPriceByTimePointParam.AlgorithmID = param.ToAlgorithmID
	_, err = s.priceDao.PriceItem.GetItemPriceByItemIDZoneIDAlgorithmIDAndTimePoint(getItemPriceByTimePointParam)
	if err != nil {
		l.WithField("ToAlgorithmID", param.ToAlgorithmID).WithError(err).
			Error("SwitchUserPriceAlgorithmByID invoke GetByID error")
		return errors.Trace(err)
	}

	isMatched, err := s.isFromAlgoIDMatchUserCurrAlgo(ctx, param.UID, param.ItemID, param.ZoneID, param.FromAlgorithmID, now, fromAlgoRelatedPriceItem)
	if err != nil {
		return errors.Trace(err)
	}
	if !isMatched {
		return errors.New("request fromAlgorithmID does not match usingAlgorithmID")
	}

	// 今天 0 点
	todayZeroTime, err := base.NewDay(now).Time(ctx)
	if err != nil {
		return errors.Trace(err)
	}
	// 生效时间设置为次日，保存新增的切换记录
	priceUserAlgorithm := &model.PriceUserAlgorithm{
		UID:         param.UID,
		ItemID:      param.ItemID,
		ZoneID:      param.ZoneID,
		AlgorithmID: param.ToAlgorithmID,
		EffectTime:  base.NewHNS(todayZeroTime),
		// 设置为一个极大的日期
		DeadTime:   base.NewHNS(base.TimeMax),
		IsDisabled: false,
	}

	// 事务
	err = s.priceDao.PriceUserAlgorithm.DoTransaction(func(dao *model.PriceUserAlgorithmDao) error {
		err = dao.Save(priceUserAlgorithm)
		if err != nil {
			return errors.Trace(err)
		}
		// 若 lastUserAlgorithms 存在，更新之前的条目的 dead_time
		if lastUserAlgorithm != nil {
			err = dao.UpdateGivenFields(lastUserAlgorithm.ID, map[string]any{"dead_time": base.NewHNS(todayZeroTime)})
			if err != nil {
				return errors.Trace(err)
			}
		}
		return nil
	})
	if err != nil {
		l.WithError(err).Error("<SwitchUserPriceAlgorithmByID> DoTransaction error")
		return errors.Trace(err)
	}

	go func() {

		params := operations.NewRefreshDummyBillsParams()
		params.WithContext(logging.SetLogger(context.Background(), l))

		// 这里是单个uid粒度的，每天也只能切换一次，异步去更新的，个别产品线挂掉也无影响，
		// 主要是用户看数据角度大多是整个账单对不对，而不是具体某个产品线对不对，
		// 这样只要有机会全部更新就全部更新，更合理一些,面向用户的数据能实时就实时一些吧，
		// 这里可以作为一天只刷一次模拟账单的补充手段
		params.SetReqRefreshDummyBills(&models.ReqRefreshDummyBills{
			Day:      base.NewDay(time.Now()).String(),
			UID:      int64(param.UID),
			Products: "", // 刷新该用户全部产品线
		})

		_, err1 := s.walletBiz.Operations.RefreshDummyBills(params)
		if err1 != nil {
			l.WithError(err1).
				WithField("param", param).
				Error("<SwitchUserPriceAlgorithmByID> refresh dummy bills error")
			return
		}
	}()
	return nil
}

// ProductPriceUserAlgorithms product 级别的用户的计费方式
type ProductPriceUserAlgorithms struct {
	Product                      string
	ItemGroupPriceUserAlgorithms []*ItemGroupPriceUserAlgorithms
}

// ItemGroupPriceUserAlgorithms itemGroup 级别用户计费方式
type ItemGroupPriceUserAlgorithms struct {
	ItemGroup               string
	ItemPriceUserAlgorithms []*ItemPriceUserAlgorithm
}

// PriceStair 价格阶梯
type PriceStair struct {
	// 用量区间
	From uint64
	To   uint64
	// 具体价格值
	Price *base.NMoney
}

type PriceInfo struct {
	PriceStairs []*PriceStair
	// StairPriceType 阶梯类型
	StairPriceType string
	// 币种（如 CNY USD）
	CurrencyType base.CurrencyType
}

type AlgorithmAndPriceInfo struct {
	// 是否当前使用的计费方式
	IsInUse bool
	// 计费方式所属大类，如 default, bandwidth 等
	ItemDataType       string
	PriceUserAlgorithm *model.PriceUserAlgorithm
	// 计费方式对应的价格阶梯信息
	PriceInfo *PriceInfo
}

// ItemPriceUserAlgorithm item 级别用户计费方式
type ItemPriceUserAlgorithm struct {
	Item                   string
	AlgorithmAndPriceInfos []*AlgorithmAndPriceInfo
}

type ItemIDAndAlgorithmID struct {
	ItemID      uint64
	AlgorithmID uint64
}

// getUserPriceAlgorithmByItem 按照 item 获取计费项及其价格
// userItemIDAlgoIDToAlgoMap 保存用户配置过的计费方式信息
func (s *PriceBizService) getUserPriceAlgorithmByItem(
	ctx context.Context,
	uid, itemID, zoneID, publicPriceTableID uint64,
	now time.Time,
	userItemIDAlgoIDToAlgoMap map[ItemIDAndAlgorithmID]*model.PriceUserAlgorithm,
	currencyType string,
) (*ItemPriceUserAlgorithm, error) {
	l := logging.GetLogger(ctx)
	// 获取计费项所有计费方式的价格
	priceItems, err1 := s.priceDao.PriceItem.GetItemPriceByItemIDZoneIDAndTimePoint(
		publicPriceTableID, itemID, zoneID, base.NewHNS(now), currencyType,
	)
	if err1 != nil {
		return nil, errors.Trace(err1)
	}

	// 定义返回值计费方式和价格信息数组
	algorithmAndPriceInfos := make([]*AlgorithmAndPriceInfo, len(priceItems))
	// 记录 priceItem.IsDefaultAlgorithm = true 条目的数组下标
	defaultAlgoIndex := 0
	// 该计费项，用户是否切换了计费方式
	hasUserCustomAlgo := false
	for i, priceItem := range priceItems {
		l.WithField("price_item", priceItem).Infoln("<getUserPriceAlgorithmByItem> process price_item's algorithm")
		if priceItem.IsDefaultAlgorithm {
			defaultAlgoIndex = i
		}

		itemDataTypeAlgorithm, err2 := s.dictClient.GetItemDataTypeAlgorithmByID(ctx, &pb.IDParam{Id: priceItem.AlgorithmID})
		if err2 != nil {
			return nil, errors.Trace(err2)
		}
		itemDataType, err2 := s.dictClient.GetItemDataTypeByID(ctx, &pb.IDParam{Id: itemDataTypeAlgorithm.GetDataTypeId()})
		if err2 != nil {
			return nil, errors.Trace(err2)
		}

		priceUserAlgorithm := &model.PriceUserAlgorithm{
			UID:           uid,
			ItemID:        itemID,
			ZoneID:        zoneID,
			AlgorithmID:   priceItem.AlgorithmID,
			AlgorithmCode: itemDataTypeAlgorithm.GetCode(),
			EffectTime:    priceItem.EffectTime,
			DeadTime:      priceItem.DeadTime,
			IsDisabled:    false,
		}
		itemIDAndAlgorithmID := ItemIDAndAlgorithmID{
			ItemID:      itemID,
			AlgorithmID: priceItem.AlgorithmID,
		}
		// 是否匹配中了用户当前使用的计费项方式
		isInUse := false

		// 如果这个 itemID 用户有对应的 priceUserAlgorithm 配置，那么直接用该配置，否则用默认的
		if matchedAlgo, ok := userItemIDAlgoIDToAlgoMap[itemIDAndAlgorithmID]; ok {
			hasUserCustomAlgo = true
			priceUserAlgorithm = matchedAlgo
			priceUserAlgorithm.Description = "user chosen algorithm"
			priceUserAlgorithm.AlgorithmCode = itemDataTypeAlgorithm.GetCode()
			isInUse = true
			l.WithField("price_user_algorithm", priceUserAlgorithm).Infoln("<getUserPriceAlgorithmByItem> got matched price_user_algorithm conf")
		}
		// 获取该 price_item 的价格
		priceInfo, err2 := s.getPriceInfo(ctx, priceItem, itemDataType)
		if err2 != nil {
			return nil, errors.Trace(err2)
		}
		algorithmAndPriceInfos[i] = &AlgorithmAndPriceInfo{
			PriceUserAlgorithm: priceUserAlgorithm,
			IsInUse:            isInUse,
			PriceInfo:          priceInfo,
			ItemDataType:       itemDataType.GetType(),
		}
	}
	// 如果用户没有切换计费项，那么将默认计费方式的条目设置为用户当前使用的
	if !hasUserCustomAlgo && len(algorithmAndPriceInfos) != 0 {
		algorithmAndPriceInfos[defaultAlgoIndex].IsInUse = true
	}
	return &ItemPriceUserAlgorithm{
		AlgorithmAndPriceInfos: algorithmAndPriceInfos,
	}, nil
}

// GetUserPriceAlgorithmByItemGroup 按照 itemGroup 获取计费方式及其对应的价格
func (s *PriceBizService) GetUserPriceAlgorithmByItemGroup(
	ctx context.Context,
	uid, itemGroupID, zoneID, publicPriceTableID uint64,
	now time.Time,
	currencyType string,
) (*ItemGroupPriceUserAlgorithms, error) {
	l := logging.GetLogger(ctx)

	// 获取 itemGroup 下属的所有 item 列表
	idPagingActiveParam := &pb.IDPagingActiveParam{
		Id:         itemGroupID,
		Page:       1,
		PageSize:   largePageSize,
		OnlyActive: true,
	}
	itemList, err := s.dictClient.ListItemsByGroupID(ctx, idPagingActiveParam)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 构造 itemID -> itemCode map
	itemIDCodeMap := make(map[uint64]string)
	// 保存所有 itemID 到单独一个数组
	itemIDs := make([]uint64, len(itemList.GetItems()))
	for i, item := range itemList.GetItems() {
		itemIDCodeMap[item.GetId()] = item.GetCode()
		itemIDs[i] = item.GetId()
	}

	// 批量查询用户某些 item_id 当前的计费方式，后面并填入 map，提升代码效率
	listParam := &model.ListByCondsParam{
		UID:       uid,
		ItemIDs:   itemIDs,
		ZoneID:    zoneID,
		StartTime: base.NewHNS(now),
		EndTime:   base.NewHNS(now),
	}
	priceUserAlgorithms, err := s.priceDao.PriceUserAlgorithm.ListByConds(listParam)
	if err != nil {
		return nil, errors.Trace(err)
	}
	// itemIDAlgoIDToAlgorithmMap 保存 ItemIDAndAlgorithmID -> model.PriceUserAlgorithm 信息
	itemIDAlgoIDToAlgorithmMap := make(map[ItemIDAndAlgorithmID]*model.PriceUserAlgorithm)
	for _, priceUserAlgorithm := range priceUserAlgorithms {
		itemIDAndAlgorithmID := ItemIDAndAlgorithmID{
			ItemID:      priceUserAlgorithm.ItemID,
			AlgorithmID: priceUserAlgorithm.AlgorithmID,
		}
		itemIDAlgoIDToAlgorithmMap[itemIDAndAlgorithmID] = priceUserAlgorithm
	}

	itemGroupPriceUserAlgorithms := make([]*ItemPriceUserAlgorithm, 0)
	for _, itemID := range itemIDs {
		itemPriceUserAlgorithm, err1 := s.getUserPriceAlgorithmByItem(
			ctx, uid, itemID, zoneID, publicPriceTableID,
			now, itemIDAlgoIDToAlgorithmMap, currencyType,
		)
		if err1 != nil {
			l.WithFields(logrus.Fields{
				"uid":                   uid,
				"item_id":               itemID,
				"zone_id":               zoneID,
				"public_price_table_id": publicPriceTableID,
				"now":                   now,
				"currency_type":         currencyType,
			}).WithError(err1).Errorln("<GetUserPriceAlgorithmByItemGroup> invoke getUserPriceAlgorithmByItem error")
			return nil, errors.Trace(err1)
		}
		itemPriceUserAlgorithm.Item = itemIDCodeMap[itemID]
		itemGroupPriceUserAlgorithms = append(itemGroupPriceUserAlgorithms, itemPriceUserAlgorithm)
	}

	return &ItemGroupPriceUserAlgorithms{
		ItemPriceUserAlgorithms: itemGroupPriceUserAlgorithms,
	}, nil
}

// GetUserPriceAlgorithm 获取用户当前计费方式及计费方式对应的价格信息
// 1.获取 product 所有计费组
// 2.获取每个计费组中所有的计费项
// 3.获取每个计费项包含的所有计费方式的价格
// 4.获取每个计费项用户当前的计费方式，如果当前用户没有，那么即为默认的计费方式
func (s *PriceBizService) GetUserPriceAlgorithm(
	ctx context.Context,
	uid uint64,
	product string,
	zoneCode int64,
) (*ProductPriceUserAlgorithms, error) {
	l := logging.GetLogger(ctx)

	eg := errgroup.Group{}

	var currency *wallet.Currency
	eg.Go(func() error {
		res, err := s.paymentServiceClient.GetSingleCurrency(ctx, &wallet.UIDParam{
			Uid: uid,
		})
		if err != nil {
			return errors.Trace(err)
		}
		currency = res
		return nil
	})
	var zone *pb.Zone
	eg.Go(func() error {
		res, err1 := s.dictClient.GetZoneByCode(ctx, &pb.ZoneCodeParam{Code: zoneCode})
		if err1 != nil {
			return errors.Trace(err1)
		}
		zone = res
		return nil
	})
	var itemGroupList *pb.ItemGroupList
	eg.Go(func() error {
		// 获取 product 下属的 itemGroup 列表
		productPagingParam := &pb.ProductPagingParam{
			Product:  product,
			Page:     1,
			PageSize: largePageSize,
		}
		res, err1 := s.dictClient.ListItemGroupsByProductCode(ctx, productPagingParam)
		if err1 != nil {
			return errors.Trace(err1)
		}
		itemGroupList = res
		return nil
	})

	var publicPriceTable *model.PriceTable
	eg.Go(func() error {
		// 获取公开价所属的 price_id
		res, err1 := s.GetDefaultPriceTable(ctx)
		if err1 != nil {
			return errors.Trace(err1)
		}
		publicPriceTable = res
		return nil
	})
	err := eg.Wait()
	if err != nil {
		return nil, errors.Trace(err)
	}

	now := time.Now()
	// 定义返回值数组
	productPriceUserAlgorithms := make([]*ItemGroupPriceUserAlgorithms, 0)
	for _, itemGroup := range itemGroupList.ItemGroups {
		// 获取 itemGroup 级别的计费方式以及价格信息
		itemGroupPriceUserAlgorithms, err1 := s.GetUserPriceAlgorithmByItemGroup(
			ctx,
			uid, itemGroup.GetId(), zone.GetId(),
			publicPriceTable.ID, now,
			currency.GetCurrencyType(),
		)
		if err1 != nil {
			l.WithFields(logrus.Fields{
				"uid":                   uid,
				"item_group_id":         itemGroup.GetId(),
				"zone_id":               zone.GetId(),
				"public_price_table_id": publicPriceTable.ID,
				"now":                   now,
				"currencyType":          currency.GetCurrencyType(),
			}).WithError(err1).Errorln("<GetUserPriceAlgorithm> invoke GetUserPriceAlgorithmByItemGroup error")
			return nil, errors.Trace(err1)
		}
		// 填充 itemGroup code 信息
		itemGroupPriceUserAlgorithms.ItemGroup = itemGroup.GetCode()
		// append 到返回值数组中
		productPriceUserAlgorithms = append(productPriceUserAlgorithms, itemGroupPriceUserAlgorithms)
	}
	return &ProductPriceUserAlgorithms{
		Product:                      product,
		ItemGroupPriceUserAlgorithms: productPriceUserAlgorithms,
	}, nil
}
