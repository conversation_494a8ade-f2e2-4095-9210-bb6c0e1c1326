package service

import (
	"github.com/go-redis/redis/v8"
	"github.com/qbox/pay-sdk/dict"
	gaeaClient "github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/price"
	"github.com/qbox/pay-sdk/respack"
	priceClient "github.com/qbox/pay-sdk/v3/price/client"
	walletClient "github.com/qbox/pay-sdk/v3/walletEx/client"
	"qiniu.io/pay/pland/models"
)

// PlanService plan srv
type PlanService struct {
	dao         *models.Dao
	gaea        *gaeaClient.Gaea
	price       *priceClient.Price
	wallet      *walletClient.WalletEx
	redisClient redis.UniversalClient
	dict        dict.PayDictServiceClient
	respack     respack.RespackServiceClient
	priceV4     price.PayPriceServiceClient
}

// NewPlanService new plan srv
func NewPlanService(opts ...PlanServiceOption) *PlanService {
	srv := &PlanService{}
	for _, apply := range opts {
		apply(srv)
	}
	return srv
}

// PlanServiceOption plan srv options
type PlanServiceOption func(service *PlanService)

// WithMySQLDao attach plan dao on plan service
func WithMySQLDao(dao *models.Dao) PlanServiceOption {
	return func(srv *PlanService) {
		srv.dao = dao
	}
}

// WithGaeaClient attach gaea service on plan service
func WithGaeaClient(gaea *gaeaClient.Gaea) PlanServiceOption {
	return func(srv *PlanService) {
		srv.gaea = gaea
	}
}

// WithPriceClient attach price client on plan service
func WithPriceClient(price *priceClient.Price) PlanServiceOption {
	return func(srv *PlanService) {
		srv.price = price
	}
}

// WithWalletClient attach wallet client on plan service
func WithWalletClient(wallet *walletClient.WalletEx) PlanServiceOption {
	return func(srv *PlanService) {
		srv.wallet = wallet
	}
}

// WithRedisClient attach redis client on plan service
func WithRedisClient(client redis.UniversalClient) PlanServiceOption {
	return func(srv *PlanService) {
		srv.redisClient = client
	}
}

// WithDictClient attach dict grpc client on plan service
func WithDictClient(client dict.PayDictServiceClient) PlanServiceOption {
	return func(srv *PlanService) {
		srv.dict = client
	}
}

// WithRespackClient attach respack grpc client on plan service
func WithRespackClient(client respack.RespackServiceClient) PlanServiceOption {
	return func(srv *PlanService) {
		srv.respack = client
	}
}

// WithPriceV4Client attach price v4 grpc client on plan service
func WithPriceV4Client(client price.PayPriceServiceClient) PlanServiceOption {
	return func(srv *PlanService) {
		srv.priceV4 = client
	}
}
