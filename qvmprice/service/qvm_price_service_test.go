package service_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/test"
	dictpb "github.com/qbox/pay-sdk/dict"
	mock_dict "github.com/qbox/pay-sdk/mocks/dict"
	mock_wallet "github.com/qbox/pay-sdk/mocks/wallet"
	walletpb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/qvmprice/model"
	"qiniu.io/pay/qvmprice/service"
)

func assertQVMPriceItemDetailEquals(
	t *testing.T,
	expected *service.QVMPriceItemDetail,
	actual *service.QVMPriceItemDetail,
) {
	assert.Equal(t, expected.UID, actual.UID)
	assert.Equal(t, expected.ItemCode, actual.ItemCode)
	assert.Equal(t, expected.ZoneCode, actual.ZoneCode)
	assert.Equal(t, expected.StairPriceType, actual.StairPriceType)
	assert.Equal(t, expected.CurrencyType, actual.CurrencyType)
	assert.Equal(t, expected.Stairs, actual.Stairs)
	assert.True(
		t,
		expected.EffectTime.Equal(actual.EffectTime),
		fmt.Sprintf(
			"%s should equal %s",
			actual.EffectTime.String(),
			expected.EffectTime.String(),
		),
	)
	assert.True(
		t,
		expected.DeadTime.Equal(actual.DeadTime),
		fmt.Sprintf(
			"%s should equal %s",
			actual.DeadTime.String(),
			expected.DeadTime.String(),
		),
	)
	assert.NotZero(t, actual.CreatedAt)
	assert.Equal(t, expected.IsVIP, actual.IsVIP)
	if expected.IsVIP {
		if expected.VIPOp == "" {
			panic("expected.VIPOp should be a regex if expected.IsVIP=true")
		}
		// 由于会存在不确定的 ID 取值，此处把预期字符串当作正则处理
		assert.Regexp(t, expected.VIPOp, actual.VIPOp)
	} else {
		assert.Equal(t, expected.VIPOp, actual.VIPOp)
	}
}

func assertItemPricesRespListMatches(
	t *testing.T,
	expected []*service.ItemPricesResp,
	actual []*service.ItemPricesResp,
) {
	assert.Len(t, actual, len(expected))
	if len(actual) != len(expected) {
		// assertion already failed, let's print less
		return
	}

	checkedIndexes := make(map[int]struct{}, len(expected))
	for _, expectedElem := range expected {
		// find element with same (item, zone) in actual array
		// assume non-nil element
		var actualElem *service.ItemPricesResp
		for i, obj := range actual {
			if obj.ItemCode == expectedElem.ItemCode && obj.ZoneCode == expectedElem.ZoneCode {
				if _, seen := checkedIndexes[i]; seen {
					assert.Fail(
						t,
						"matched duplicate ItemPricesResp element from actual list",
						"item=%s zone=%d",
						expectedElem.ItemCode,
						expectedElem.ZoneCode,
					)
					return
				}

				actualElem = obj
				checkedIndexes[i] = struct{}{}
				break
			}
		}

		if actualElem == nil {
			assert.Fail(
				t,
				"expected ItemPricesResp not found in actual list",
				"item=%s zone=%d",
				expectedElem.ItemCode,
				expectedElem.ZoneCode,
			)
			return
		}

		assert.Len(t, actualElem.Prices, len(expectedElem.Prices))
		for i, expectedPrice := range expectedElem.Prices {
			actualPrice := actualElem.Prices[i]
			assertQVMPriceItemDetailEquals(t, expectedPrice, actualPrice)
		}
	}
}

func TestQVMPriceServiceSimpleFlow(t *testing.T) {
	loc := time.UTC

	// 数据库对生效时间段的存储精度只有百纳秒
	tNow := time.Now().In(loc).Truncate(100 * time.Nanosecond)
	t202201 := time.Date(2022, 1, 1, 0, 0, 0, 0, loc)
	tThisMonth := base.ThisMonth(tNow)
	tTwoMonthsLater := tNow.AddDate(0, 2, 0)
	tTwoMonthsAndADayLater := tTwoMonthsLater.AddDate(0, 0, 1)
	tThreeMonthsLater := tNow.AddDate(0, 3, 0)
	tFourMonthsLater := tNow.AddDate(0, 4, 0)
	t220001 := time.Date(2200, 1, 1, 0, 0, 0, 0, loc)

	ctrl := gomock.NewController(t)
	ctx := tz.WithRefLocation(context.Background(), loc)

	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in qvmprice/service return error")
	}

	dao := model.NewQVMPriceDao(testWrap.BaseDao())

	mockDict := mock_dict.NewMockPayDictServiceClient(ctrl)
	{
		mockDict.EXPECT().MultiGetItemByCodes(
			gomock.Any(),
			&dictpb.MultiCodeParam{
				Codes: []string{"qvm:ip:0m"},
			},
		).AnyTimes().Return(
			&dictpb.ItemList{
				Items: []*dictpb.Item{
					{
						Id:           11111,
						GroupId:      2222,
						Code:         "qvm:ip:0m",
						Name:         "QVM 弹性公网IP(0Mbps)",
						Description:  "",
						Remark:       "",
						IsDisabled:   false,
						IsBasic:      false,
						Order:        0,
						FncProductId: 30,
					},
				},
				Count: 1,
			},
			nil,
		)

		mockDict.EXPECT().GetItemGroupByID(
			gomock.Any(),
			&dictpb.IDParam{Id: 2222},
		).AnyTimes().Return(
			&dictpb.ItemGroup{
				Id:        2222,
				ProductId: 333,
				Code:      "qvm:ip",
			},
			nil,
		)
	}

	mockPaymentSrv := mock_wallet.NewMockPaymentServiceClient(ctrl)
	{
		mockPaymentSrv.EXPECT().GetSingleCurrency(
			gomock.Any(),
			&walletpb.UIDParam{Uid: 234},
		).Times(1).Return(
			&walletpb.Currency{
				Uid:          234,
				CurrencyType: "USD",
			},
			nil,
		)
	}

	srv := service.NewQVMPriceService(dao, mockDict, mockPaymentSrv)

	// 先查询，由于没有数据，应该会失败
	{
		now := time.Now()
		today := base.Today(now)
		x, err := srv.QueryItemPriceForUID(ctx, 233, "qvm:ip:0m", 4001, "CNY", today, now)
		assert.Error(t, err)
		assert.Zero(t, x)
	}

	// 插入一些公开报价
	{
		err := srv.ImportPublicPrices(
			ctx,
			false,
			[]*service.QVMPriceItemDetail{
				{
					UID:            0,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4001,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "CNY",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(1111)},
					},
					EffectTime: t202201,
					DeadTime:   t220001,
				},
				{
					UID:            0,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4001,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "USD",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(222)},
					},
					EffectTime: t202201,
					DeadTime:   t220001,
				},
				{
					UID:            0,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4002,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "CNY",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(1234)},
					},
					EffectTime: t202201,
					DeadTime:   t220001,
				},
				{
					UID:            0,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4002,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "USD",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(234)},
					},
					EffectTime: t202201,
					DeadTime:   t220001,
				},
			},
			"",
			time.Time{},
		)
		assert.NoError(t, err)
	}

	// 再查，应该有数据了
	{
		now := time.Now()
		today := base.Today(now)
		expected := &service.QVMPriceItemDetail{
			UID:            233,
			ItemCode:       "qvm:ip:0m",
			ZoneCode:       4001,
			StairPriceType: "UNITPRICE",
			CurrencyType:   "CNY",
			Stairs: []*service.QVMItemPriceStairDetail{
				{Quantity: 0, Price: base.NewNMoney(1111)},
			},
			EffectTime: t202201,
			DeadTime:   t220001,
			// CreatedAt 不便断言
			IsVIP: false,
			VIPOp: "",
		}
		actual, err := srv.QueryItemPriceForUID(ctx, 233, "qvm:ip:0m", 4001, "CNY", today, now)
		assert.NoError(t, err)
		assertQVMPriceItemDetailEquals(t, expected, actual)
	}

	// 换一个币种
	{
		now := time.Now()
		today := base.Today(now)
		expected := &service.QVMPriceItemDetail{
			UID:            233,
			ItemCode:       "qvm:ip:0m",
			ZoneCode:       4001,
			StairPriceType: "UNITPRICE",
			CurrencyType:   "USD",
			Stairs: []*service.QVMItemPriceStairDetail{
				{Quantity: 0, Price: base.NewNMoney(222)},
			},
			EffectTime: t202201,
			DeadTime:   t220001,
			// CreatedAt 不便断言
			IsVIP: false,
			VIPOp: "",
		}
		actual, err := srv.QueryItemPriceForUID(ctx, 233, "qvm:ip:0m", 4001, "USD", today, now)
		assert.NoError(t, err)
		assertQVMPriceItemDetailEquals(t, expected, actual)
	}

	// 不传币种
	{
		now := time.Now()
		today := base.Today(now)
		expected := &service.QVMPriceItemDetail{
			UID:            234,
			ItemCode:       "qvm:ip:0m",
			ZoneCode:       4001,
			StairPriceType: "UNITPRICE",
			CurrencyType:   "USD",
			Stairs: []*service.QVMItemPriceStairDetail{
				{Quantity: 0, Price: base.NewNMoney(222)},
			},
			EffectTime: t202201,
			DeadTime:   t220001,
			// CreatedAt 不便断言
			IsVIP: false,
			VIPOp: "",
		}
		actual, err := srv.QueryItemPriceForUID(ctx, 234, "qvm:ip:0m", 4001, "", today, now)
		assert.NoError(t, err)
		assertQVMPriceItemDetailEquals(t, expected, actual)
	}

	// 换一个区域
	{
		now := time.Now()
		today := base.Today(now)
		expected := &service.QVMPriceItemDetail{
			UID:            233,
			ItemCode:       "qvm:ip:0m",
			ZoneCode:       4002,
			StairPriceType: "UNITPRICE",
			CurrencyType:   "USD",
			Stairs: []*service.QVMItemPriceStairDetail{
				{Quantity: 0, Price: base.NewNMoney(234)},
			},
			EffectTime: t202201,
			DeadTime:   t220001,
			// CreatedAt 不便断言
			IsVIP: false,
			VIPOp: "",
		}
		actual, err := srv.QueryItemPriceForUID(ctx, 233, "qvm:ip:0m", 4002, "USD", today, now)
		assert.NoError(t, err)
		assertQVMPriceItemDetailEquals(t, expected, actual)
	}

	// 更新一些不生效的公开报价
	{
		err := srv.ImportPublicPrices(
			ctx,
			true,
			[]*service.QVMPriceItemDetail{
				{
					UID:            0,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4001,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "CNY",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(99999999)},
					},
					EffectTime: tThisMonth,
					DeadTime:   t220001,
				},
			},
			"",
			time.Time{},
		)
		assert.NoError(t, err)
	}

	// 更新一些生效的公开报价
	{
		err := srv.ImportPublicPrices(
			ctx,
			false,
			[]*service.QVMPriceItemDetail{
				{
					UID:            0,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4001,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "CNY",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(1001)},
					},
					EffectTime: tTwoMonthsLater,
					DeadTime:   t220001,
				},
			},
			"",
			time.Time{},
		)
		assert.NoError(t, err)
	}

	// 报价应该更新了
	{
		expected := &service.QVMPriceItemDetail{
			UID:            233,
			ItemCode:       "qvm:ip:0m",
			ZoneCode:       4001,
			StairPriceType: "UNITPRICE",
			CurrencyType:   "CNY",
			Stairs: []*service.QVMItemPriceStairDetail{
				{Quantity: 0, Price: base.NewNMoney(1001)},
			},
			EffectTime: tTwoMonthsLater,
			DeadTime:   t220001,
		}
		actual, err := srv.QueryItemPriceForUID(ctx, 233, "qvm:ip:0m", 4001, "CNY", tTwoMonthsAndADayLater, tTwoMonthsAndADayLater)
		assert.NoError(t, err)
		assertQVMPriceItemDetailEquals(t, expected, actual)
	}

	itemSpecs := []service.ItemSpec{
		{ItemCode: "qvm:ip:0m", ZoneCode: 4001},
		{ItemCode: "qvm:ip:0m", ZoneCode: 4002},
	}

	// 测试出账接口
	{
		expected := []*service.ItemPricesResp{
			{
				ItemCode: "qvm:ip:0m",
				ZoneCode: 4001,
				Prices: []*service.QVMPriceItemDetail{
					{
						UID:            233,
						ItemCode:       "qvm:ip:0m",
						ZoneCode:       4001,
						StairPriceType: "UNITPRICE",
						CurrencyType:   "CNY",
						Stairs: []*service.QVMItemPriceStairDetail{
							{Quantity: 0, Price: base.NewNMoney(1111)},
						},
						EffectTime: t202201,
						DeadTime:   tTwoMonthsLater,
						// CreatedAt 不便断言
						IsVIP: false,
						VIPOp: "",
					},
				},
			},
			{
				ItemCode: "qvm:ip:0m",
				ZoneCode: 4002,
				Prices: []*service.QVMPriceItemDetail{
					{
						UID:            233,
						ItemCode:       "qvm:ip:0m",
						ZoneCode:       4002,
						StairPriceType: "UNITPRICE",
						CurrencyType:   "CNY",
						Stairs: []*service.QVMItemPriceStairDetail{
							{Quantity: 0, Price: base.NewNMoney(1234)},
						},
						EffectTime: t202201,
						DeadTime:   tTwoMonthsLater,
						// CreatedAt 不便断言
						IsVIP: false,
						VIPOp: "",
					},
				},
			},
		}
		actual, err := srv.QueryItemPricesForUIDWithTimeRange(
			ctx,
			233,
			itemSpecs,
			"CNY",
			tTwoMonthsAndADayLater,
			t202201,
			tTwoMonthsLater,
		)
		assert.NoError(t, err)
		assertItemPricesRespListMatches(t, expected, actual)
	}

	// 给用户改一些不生效的价格
	{
		err := srv.CreatePriceTableForUID(
			ctx,
			233,
			true,
			[]*service.QVMPriceItemDetail{
				{
					UID:            233,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4001,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "USD",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(0)},
					},
					EffectTime: tThisMonth,
					DeadTime:   tThreeMonthsLater,
				},
				{
					UID:            233,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4002,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "CNY",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(0)},
					},
					EffectTime: tThisMonth,
					DeadTime:   tThreeMonthsLater,
				},
			},
			[]*service.QVMGroupDiscountDetail{
				{
					GroupCode:  "qvm:ip",
					ZoneCode:   4001,
					IsDisabled: false,
					Discount:   200,
					EffectTime: tThisMonth,
					DeadTime:   tThreeMonthsLater,
				},
			},
			"",
			time.Time{},
		)
		assert.NoError(t, err)
	}

	// 给用户改一些生效的价格
	{
		err := srv.CreatePriceTableForUID(
			ctx,
			233,
			false,
			[]*service.QVMPriceItemDetail{
				{
					UID:            233,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4001,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "USD",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(88888)},
					},
					EffectTime: tThisMonth,
					DeadTime:   tThreeMonthsLater,
				},
				{
					UID:            233,
					ItemCode:       "qvm:ip:0m",
					ZoneCode:       4002,
					StairPriceType: "UNITPRICE",
					CurrencyType:   "CNY",
					Stairs: []*service.QVMItemPriceStairDetail{
						{Quantity: 0, Price: base.NewNMoney(6666)},
					},
					EffectTime: tThisMonth,
					DeadTime:   tThreeMonthsLater,
				},
			},
			[]*service.QVMGroupDiscountDetail{
				{
					GroupCode:  "qvm:ip",
					ZoneCode:   4001,
					IsDisabled: false,
					Discount:   85,
					EffectTime: tThisMonth,
					DeadTime:   tThreeMonthsLater,
				},
			},
			"",
			time.Time{},
		)
		assert.NoError(t, err)
	}

	// 验证改价效果（改价时间段内，只有折扣）
	{
		expected := &service.QVMPriceItemDetail{
			UID:            233,
			ItemCode:       "qvm:ip:0m",
			ZoneCode:       4001,
			StairPriceType: "UNITPRICE",
			CurrencyType:   "CNY",
			Stairs: []*service.QVMItemPriceStairDetail{
				{Quantity: 0, Price: base.NewNMoney(850)},
			},
			EffectTime: tThisMonth,
			DeadTime:   tThreeMonthsLater,
			// CreatedAt 不便断言
			IsVIP: true,
			VIPOp: `^QVMPRICE:Table:\d+:Group:\d+$`,
		}
		actual, err := srv.QueryItemPriceForUID(ctx, 233, "qvm:ip:0m", 4001, "CNY", tTwoMonthsAndADayLater, tTwoMonthsAndADayLater)
		assert.NoError(t, err)
		assertQVMPriceItemDetailEquals(t, expected, actual)
	}

	// 验证改价效果（改价时间段内，只有 VIP 报价）
	{
		expected := &service.QVMPriceItemDetail{
			UID:            233,
			ItemCode:       "qvm:ip:0m",
			ZoneCode:       4002,
			StairPriceType: "UNITPRICE",
			CurrencyType:   "CNY",
			Stairs: []*service.QVMItemPriceStairDetail{
				{Quantity: 0, Price: base.NewNMoney(6666)},
			},
			EffectTime: tThisMonth,
			DeadTime:   tThreeMonthsLater,
			// CreatedAt 不便断言
			IsVIP: true,
			VIPOp: `^QVMPRICE:Table:\d+:Item:\d+$`,
		}
		actual, err := srv.QueryItemPriceForUID(ctx, 233, "qvm:ip:0m", 4002, "CNY", tTwoMonthsAndADayLater, tTwoMonthsAndADayLater)
		assert.NoError(t, err)
		assertQVMPriceItemDetailEquals(t, expected, actual)
	}

	// 验证改价效果（改价时间段内，同时有 VIP 报价和折扣）
	{
		expected := &service.QVMPriceItemDetail{
			UID:            233,
			ItemCode:       "qvm:ip:0m",
			ZoneCode:       4001,
			StairPriceType: "UNITPRICE",
			CurrencyType:   "USD",
			Stairs: []*service.QVMItemPriceStairDetail{
				{Quantity: 0, Price: base.NewNMoney(75554)},
			},
			EffectTime: tThisMonth,
			DeadTime:   tThreeMonthsLater,
			// CreatedAt 不便断言
			IsVIP: true,
			VIPOp: `^QVMPRICE:Table:\d+:Group:\d+$`,
		}
		actual, err := srv.QueryItemPriceForUID(ctx, 233, "qvm:ip:0m", 4001, "USD", tTwoMonthsAndADayLater, tTwoMonthsAndADayLater)
		assert.NoError(t, err)
		assertQVMPriceItemDetailEquals(t, expected, actual)
	}

	// 验证改价效果（改价时间段后）
	{
		expected := &service.QVMPriceItemDetail{
			UID:            233,
			ItemCode:       "qvm:ip:0m",
			ZoneCode:       4001,
			StairPriceType: "UNITPRICE",
			CurrencyType:   "CNY",
			Stairs: []*service.QVMItemPriceStairDetail{
				{Quantity: 0, Price: base.NewNMoney(1001)},
			},
			EffectTime: tTwoMonthsLater,
			DeadTime:   t220001,
			// CreatedAt 不便断言
			IsVIP: false,
			VIPOp: "",
		}
		actual, err := srv.QueryItemPriceForUID(ctx, 233, "qvm:ip:0m", 4001, "CNY", tFourMonthsLater, tFourMonthsLater)
		assert.NoError(t, err)
		assertQVMPriceItemDetailEquals(t, expected, actual)
	}

	// 再次测试出账接口
	{
		expected := []*service.ItemPricesResp{
			{
				ItemCode: "qvm:ip:0m",
				ZoneCode: 4001,
				Prices: []*service.QVMPriceItemDetail{
					{
						UID:            233,
						ItemCode:       "qvm:ip:0m",
						ZoneCode:       4001,
						StairPriceType: "UNITPRICE",
						CurrencyType:   "CNY",
						Stairs: []*service.QVMItemPriceStairDetail{
							{Quantity: 0, Price: base.NewNMoney(1111)},
						},
						EffectTime: t202201,
						DeadTime:   tThisMonth,
						// CreatedAt 不便断言
						IsVIP: false,
						VIPOp: "",
					},
					{
						UID:            233,
						ItemCode:       "qvm:ip:0m",
						ZoneCode:       4001,
						StairPriceType: "UNITPRICE",
						CurrencyType:   "CNY",
						Stairs: []*service.QVMItemPriceStairDetail{
							{Quantity: 0, Price: base.NewNMoney(944)},
						},
						EffectTime: tThisMonth,
						DeadTime:   tTwoMonthsLater,
						// CreatedAt 不便断言
						IsVIP: true,
						VIPOp: `^QVMPRICE:Table:\d+:Group:\d+$`,
					},
					{
						UID:            233,
						ItemCode:       "qvm:ip:0m",
						ZoneCode:       4001,
						StairPriceType: "UNITPRICE",
						CurrencyType:   "CNY",
						Stairs: []*service.QVMItemPriceStairDetail{
							{Quantity: 0, Price: base.NewNMoney(850)},
						},
						EffectTime: tTwoMonthsLater,
						DeadTime:   tThreeMonthsLater,
						// CreatedAt 不便断言
						IsVIP: true,
						VIPOp: `^QVMPRICE:Table:\d+:Group:\d+$`,
					},
					{
						UID:            233,
						ItemCode:       "qvm:ip:0m",
						ZoneCode:       4001,
						StairPriceType: "UNITPRICE",
						CurrencyType:   "CNY",
						Stairs: []*service.QVMItemPriceStairDetail{
							{Quantity: 0, Price: base.NewNMoney(1001)},
						},
						EffectTime: tThreeMonthsLater,
						DeadTime:   tFourMonthsLater,
						// CreatedAt 不便断言
						IsVIP: false,
						VIPOp: "",
					},
				},
			},
			{
				ItemCode: "qvm:ip:0m",
				ZoneCode: 4002,
				Prices: []*service.QVMPriceItemDetail{
					{
						UID:            233,
						ItemCode:       "qvm:ip:0m",
						ZoneCode:       4002,
						StairPriceType: "UNITPRICE",
						CurrencyType:   "CNY",
						Stairs: []*service.QVMItemPriceStairDetail{
							{Quantity: 0, Price: base.NewNMoney(1234)},
						},
						EffectTime: t202201,
						DeadTime:   tThisMonth,
						// CreatedAt 不便断言
						IsVIP: false,
						VIPOp: "",
					},
					{
						UID:            233,
						ItemCode:       "qvm:ip:0m",
						ZoneCode:       4002,
						StairPriceType: "UNITPRICE",
						CurrencyType:   "CNY",
						Stairs: []*service.QVMItemPriceStairDetail{
							{Quantity: 0, Price: base.NewNMoney(6666)},
						},
						EffectTime: tThisMonth,
						DeadTime:   tThreeMonthsLater,
						// CreatedAt 不便断言
						IsVIP: true,
						VIPOp: `^QVMPRICE:Table:\d+:Item:\d+$`,
					},
					{
						UID:            233,
						ItemCode:       "qvm:ip:0m",
						ZoneCode:       4002,
						StairPriceType: "UNITPRICE",
						CurrencyType:   "CNY",
						Stairs: []*service.QVMItemPriceStairDetail{
							{Quantity: 0, Price: base.NewNMoney(1234)},
						},
						EffectTime: tThreeMonthsLater,
						DeadTime:   tFourMonthsLater,
						// CreatedAt 不便断言
						IsVIP: false,
						VIPOp: "",
					},
				},
			},
		}
		actual, err := srv.QueryItemPricesForUIDWithTimeRange(
			ctx,
			233,
			itemSpecs,
			"CNY",
			tFourMonthsLater,
			t202201,
			tFourMonthsLater,
		)
		assert.NoError(t, err)
		assertItemPricesRespListMatches(t, expected, actual)
	}

}
