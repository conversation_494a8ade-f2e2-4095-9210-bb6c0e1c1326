package service_test

import (
	"context"
	"net/http"
	"strconv"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/test"
	"github.com/qbox/pay-sdk/gaea/client"
	"github.com/qbox/pay-sdk/gaea/client/operations"
	"github.com/qbox/pay-sdk/gaea/models"
	gaeaMock "github.com/qbox/pay-sdk/mocks/gaea"
	mockWallet "github.com/qbox/pay-sdk/mocks/wallet"
	pbWallet "github.com/qbox/pay-sdk/wallet"

	cDao "qiniu.io/pay/creditd/dao"
	"qiniu.io/pay/creditd/service"
	"qiniu.io/pay/i18n/intlmockhelper"
)

type creditSrvSandbox struct {
	testWrap      *test.Wrap
	creditSrv     *service.CreditService
	uid           uint64
	currencyType  base.CurrencyType // 用户默认币种
	cardBound     bool              // sandbox 的用户是否绑卡
	minCreditLine int64
	maxCreditLine int64
}

func getMockGaeaService(t *testing.T) operations.ClientService {
	ctrl := gomock.NewController(t)
	mockGaeaService := gaeaMock.NewMockClientService(ctrl)
	mockGaeaService.
		EXPECT().
		GetUserBillBriefList(gomock.Any()).
		Return(&operations.GetUserBillBriefListOK{
			Payload: &models.UserBillBriefListResp{
				Code: http.StatusOK,
				Data: &models.UserBillBriefListRespData{
					Summary: false,
					Bills: []*models.BillBrief{
						{Money: 100000},
						{Money: 200000},
						{Money: 500000},
					},
				},
				Message: "",
			}}, nil,
		).AnyTimes()
	mockGaeaService.
		EXPECT().
		GetDeveloper(gomock.Any()).
		AnyTimes().
		DoAndReturn(func(param *operations.GetDeveloperParams, opts ...any) (*operations.GetDeveloperOK, error) {
			uid, _ := strconv.Atoi(param.UID)
			return &operations.GetDeveloperOK{
				Payload: &models.GetDeveloperResp{
					Code: http.StatusOK,
					Data: &models.UserInfo{
						UID:         uint32(uid),
						Fullname:    "Rufus Shinra",
						CompanyName: "Shinra Electric Power Company",
					},
				},
			}, nil
		})
	return mockGaeaService
}

type sandboxOption func(sandbox *creditSrvSandbox)

func withCurrencyType(currency base.CurrencyType) sandboxOption {
	return func(sandbox *creditSrvSandbox) {
		sandbox.currencyType = currency
	}
}

func withUserCard(hasCard bool) sandboxOption {
	return func(sandbox *creditSrvSandbox) {
		sandbox.cardBound = hasCard
	}
}

func newCreditSrvSandbox(t *testing.T, options ...sandboxOption) *creditSrvSandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(cDao.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in credit service return error")
	}

	creditDao := cDao.NewCreditDao(testWrap.BaseDao())
	ctrl := gomock.NewController(t)

	var minSystemCreditLine int64 = 100000
	var maxSystemCreditLine int64 = 2000000

	sandbox := &creditSrvSandbox{
		testWrap:      testWrap,
		uid:           114514,
		currencyType:  base.CurrencyTypeCNY,
		cardBound:     false,
		minCreditLine: minSystemCreditLine,
		maxCreditLine: maxSystemCreditLine,
	}

	for _, option := range options {
		option(sandbox)
	}

	paymentClient := mockWallet.NewMockPaymentServiceClient(ctrl)
	{
		paymentClient.EXPECT().GetSingleCurrency(
			gomock.Any(),
			gomock.Any(),
		).AnyTimes().DoAndReturn(func(ctx context.Context, req *pbWallet.UIDParam, opts ...any) (*pbWallet.Currency, error) {
			return &pbWallet.Currency{
				Uid:          req.Uid,
				CurrencyType: sandbox.currencyType.String(),
			}, nil
		})
	}
	walletClient := mockWallet.NewMockPayWalletServiceClient(ctrl)
	{
		walletClient.EXPECT().ListUserCards(gomock.Any(), gomock.Any()).
			AnyTimes().
			DoAndReturn(func(ctx context.Context, req *pbWallet.UIDParam, opts ...any) (*pbWallet.UserCards, error) {
				result := &pbWallet.UserCards{
					UserCards: make([]*pbWallet.UserCard, 0),
				}
				if sandbox.cardBound {
					result.UserCards = []*pbWallet.UserCard{
						{
							Uid:          req.GetUid(),
							IsDefault:    true,
							LastDigits:   "4698",
							Brand:        "VISA",
							CardType:     pbWallet.CardTypeCredit,
							CardStatus:   pbWallet.CardStatusValid,
							PaymentToken: "b4a6c98k",
							CreatedAt:    timestamppb.Now(),
						},
					}
				}
				return result, nil
			})
	}

	l10nProvider := intlmockhelper.NewCommonMockL10nProvider(t)

	creditSrv, err := service.NewCreditService(
		l10nProvider,
		creditDao,
		paymentClient,
		walletClient,
		&client.Gaea{Operations: getMockGaeaService(t)},
		minSystemCreditLine,
		maxSystemCreditLine,
		dao.CacheExpiresNoCache,
	)
	if err != nil {
		t.Fatal(err)
	}
	sandbox.creditSrv = creditSrv

	return sandbox
}
