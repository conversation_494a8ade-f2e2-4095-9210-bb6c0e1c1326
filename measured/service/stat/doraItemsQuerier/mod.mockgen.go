// Code generated by MockGen. DO NOT EDIT.
// Source: measured/service/stat/doraItemsQuerier/mod.go

package doraItemsQuerier

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	model "qiniu.io/pay/measured/model"
)

// MockQuerier is a mock of Querier interface.
type MockQuerier struct {
	ctrl     *gomock.Controller
	recorder *MockQuerierMockRecorder
}

// MockQuerierMockRecorder is the mock recorder for MockQuerier.
type MockQuerierMockRecorder struct {
	mock *MockQuerier
}

// NewMockQuerier creates a new mock instance.
func NewMockQuerier(ctrl *gomock.Controller) *MockQuerier {
	mock := &MockQuerier{ctrl: ctrl}
	mock.recorder = &MockQuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuerier) EXPECT() *MockQuerierMockRecorder {
	return m.recorder
}

// DoraItemsInUse mocks base method.
func (m *<PERSON>ckQuerier) DoraItemsInUse(ctx context.Context, p *model.GetItemsInUseParams) (model.ItemsInUseResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DoraItemsInUse", ctx, p)
	ret0, _ := ret[0].(model.ItemsInUseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DoraItemsInUse indicates an expected call of DoraItemsInUse.
func (mr *MockQuerierMockRecorder) DoraItemsInUse(ctx, p interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoraItemsInUse", reflect.TypeOf((*MockQuerier)(nil).DoraItemsInUse), ctx, p)
}

// RefreshCache mocks base method.
func (m *MockQuerier) RefreshCache(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshCache", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// RefreshCache indicates an expected call of RefreshCache.
func (mr *MockQuerierMockRecorder) RefreshCache(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshCache", reflect.TypeOf((*MockQuerier)(nil).RefreshCache), ctx)
}
