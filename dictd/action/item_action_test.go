package action_test

import (
	"context"
	"testing"

	"github.com/qbox/pay-sdk/dict"
	"github.com/stretchr/testify/assert"
)

func TestItemInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	dictClient := sandbox.dictClient

	const groupID uint64 = 1
	is := []dict.Item{
		{
			GroupId: groupID,
			Code:    "kodo:flow:receive",
			Name:    "源站上行流量",
			// NOTE: 下面一行的“云”字变成了 emoji 以测试 MySQL 连接的 collation 设置
			// 它应当能活过 round-trip
			Description: "☁️存储的源站上行流量",
			Remark:      "源站上行流量备注",
			IsBasic:     true,
			Order:       1,
		},
		{
			GroupId:     groupID,
			Code:        "kodo:flow:transmit",
			Name:        "源站下行流量",
			Description: "云存储的源站下行流量",
			Remark:      "云存储的源站下行流量备注",
			IsBasic:     false,
			Order:       2,
		},
	}
	assertFields := func(item *dict.Item, n int, msgAndArgs ...any) {
		if n >= len(is) {
			return
		}
		assert.Equal(t, is[n].GetGroupId(), item.GetGroupId(), msgAndArgs...)
		assert.Equal(t, is[n].GetName(), item.GetName(), msgAndArgs...)
		assert.Equal(t, is[n].GetDescription(), item.GetDescription(), msgAndArgs...)
		assert.Equal(t, is[n].GetRemark(), item.GetRemark(), msgAndArgs...)
		assert.Equal(t, is[n].GetOrder(), item.GetOrder(), msgAndArgs...)
	}
	ids := make([]uint64, len(is))
	for n, i := range is {
		item, err := dictClient.CreateItem(context.Background(), &i)
		if assert.NoError(t, err, "CreateItem") {
			assert.NotZero(t, item.GetId(), "CreateItem")
			ids[n] = item.GetId()
		}

		item, err = dictClient.GetItemByID(context.Background(), &dict.IDParam{Id: item.GetId()})
		if assert.NoError(t, err, "GetItemByID") {
			assertFields(item, n, "GetItemByID")
		}

		item2, err := dictClient.GetItemByCode(context.Background(), &dict.CodeParam{Code: item.Code})
		if assert.NoError(t, err, "GetItemByCode") {
			assertFields(item2, n, "GetItemByCode")
		}

		item.Name += item.GetDescription()
		is[n].Name += i.GetDescription()
		item, err = dictClient.UpdateItemByID(context.Background(), &dict.IDItemParam{Id: item.GetId(), Item: item})
		if assert.NoError(t, err, "UpdateItemByID") {
			assert.Equal(t, is[n].GetName(), item.GetName(), "UpdateItemByID")
		}
	}

	// TODO: test OnlyActive
	count, err := dictClient.CountItemsByGroupID(context.Background(), &dict.IDActiveParam{Id: groupID})
	if assert.NoError(t, err, "CountItemsByGroupID") {
		assert.Len(t, is, int(count.GetCount()), "CountItemsByGroupID")
	}

	// TODO: test OnlyActive
	list, err := dictClient.ListItemsByGroupID(context.Background(), &dict.IDPagingActiveParam{Id: groupID, Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListItemsByGroupID") {
		assert.Len(t, is, int(list.GetCount()), "ListItemsByGroupID")
		for i, item := range list.GetItems() {
			assertFields(item, i, "ListItemsByGroupID")
		}
	}

	for _, id := range ids {
		_, err = dictClient.DeleteItemByID(context.Background(), &dict.IDParam{Id: id})
		assert.NoError(t, err, "DeleteItemByID")
	}
}

func TestSearchItemByFilter(t *testing.T) {
	sandbox := buildSandbox(t)

	is := []dict.Item{
		{
			GroupId: 1,
			Code:    "kodo:flow:receive",
			Name:    "源站上行流量",
			// NOTE: 下面一行的“云”字变成了 emoji 以测试 MySQL 连接的 collation 设置
			// 它应当能活过 round-trip
			Description: "☁️存储的源站上行流量",
			Remark:      "源站上行流量备注",
			IsBasic:     true,
			Order:       1,
		},
		{
			GroupId:     1,
			Code:        "kodo:flow:transmit",
			Name:        "源站下行流量",
			Description: "云存储的源站下行流量",
			Remark:      "云存储的源站下行流量备注",
			IsBasic:     false,
			Order:       2,
			IsDisabled:  true,
		},
	}
	newItems := []*dict.Item{}
	for _, item := range is {
		item, err := sandbox.dictClient.CreateItem(context.Background(), &item)
		assert.NoError(t, err)
		newItems = append(newItems, item)
	}
	defer func() {
		for _, item := range is {
			sandbox.dictClient.DeleteItemByID(context.Background(), &dict.IDParam{Id: item.Id})
		}
	}()

	zones := []dict.Zone{
		{
			Code: 0,
			Name: "宁波",
		},
		{
			Code: 1,
			Name: "不知道",
		},
		{
			Code: 2,
		},
	}
	newZones := []*dict.Zone{}
	for _, zone := range zones {
		nzone, err := sandbox.dictClient.CreateZone(context.Background(), &zone)
		assert.NoError(t, err)
		newZones = append(newZones, nzone)
	}
	defer func() {
		for _, zone := range zones {
			sandbox.dictClient.DeleteZoneByID(context.Background(), &dict.IDParam{Id: zone.Id})
		}
	}()
	for _, item := range newItems {
		for _, zone := range newZones {
			_, err := sandbox.dictClient.CreateZoneItemMap(context.Background(), &dict.ZoneItemMap{
				ZoneId: zone.Id,
				ItemId: item.Id,
			})
			assert.NoError(t, err)
		}
	}
	for _, zone := range newZones {
		list, err := sandbox.dictClient.SearchItemByFilter(context.Background(), &dict.SearchItemReq{
			Offset: 0,
			Limit:  4,
			Filter: &dict.SearchItemFilter{
				ZoneIDs: []uint64{zone.Id},
			},
		})
		assert.NoError(t, err)
		assert.Len(t, list.Items, 2)
		assert.Equal(t, "kodo:flow:receive", list.Items[0].Code)
		assert.Equal(t, "kodo:flow:transmit", list.Items[1].Code)
	}

	for _, zone := range newZones {
		list, err := sandbox.dictClient.SearchItemByFilter(context.Background(), &dict.SearchItemReq{
			Offset: 0,
			Limit:  4,
			Filter: &dict.SearchItemFilter{
				ZoneIDs:    []uint64{zone.Id},
				OnlyActive: true,
			},
		})
		assert.NoError(t, err)
		assert.Len(t, list.Items, 1)
		assert.Equal(t, "kodo:flow:receive", list.Items[0].Code)
	}

	//模糊搜索
	list, err := sandbox.dictClient.SearchItemsByFuzzy(context.TODO(), &dict.SearchFuzzyReq{
		Keyword: "kodo:flow",
	})
	codeMap := map[string]bool{
		"kodo:flow:receive":  true,
		"kodo:flow:transmit": true,
	}
	assert.NoError(t, err)
	assert.Equal(t, true, codeMap[list.Items[0].Code])
	assert.Equal(t, true, codeMap[list.Items[1].Code])
}
