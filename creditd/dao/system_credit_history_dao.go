package dao

import (
	"github.com/Masterminds/squirrel"
	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/intl"

	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

type SystemCreditHistoryDao struct {
	base *dao.BaseDao
}

func NewSystemCreditHistoryDao(base *dao.BaseDao) *SystemCreditHistoryDao {
	return &SystemCreditHistoryDao{
		base: base,
	}
}

func (d *SystemCreditHistoryDao) CreateSystemCreditHistory(credit model.SystemCredit, localizer intl.Localizer) error {
	creditHistory := model.SystemCreditHistory{
		UID:          credit.UID,
		CustomerName: credit.CustomerName,
		CreditLine:   credit.CreditLine,
		CreditPeriod: credit.CreditPeriod,
		PeriodUnit:   credit.PeriodUnit,
		AssignReason: credit.AssignReason,
		CurrencyType: credit.CurrencyType,
		Remark:       credit.GenerateRemark(localizer),
		Month:        credit.Month,
	}
	return d.base.Model(&model.SystemCredit{}).Create(&creditHistory).Error
}

func (d *SystemCreditHistoryDao) GetLatestSystemCreditHistory(uid uint64) (*model.SystemCreditHistory, error) {
	var creditHistory model.SystemCreditHistory
	err := d.base.Model(&model.SystemCreditHistory{}).
		Where("uid=? and is_deleted=false", uid).
		Order("created_at desc").
		Take(&creditHistory).
		Error
	if gorm.IsRecordNotFoundError(err) {
		return &creditHistory, nil
	}
	if err != nil {
		return nil, err
	}
	return &creditHistory, nil
}

func (d *SystemCreditHistoryDao) GetUserSystemCreditHistories(uid uint64, assignReasons ...enums.AssignReason) ([]*model.SystemCreditHistory, error) {
	var histories []*model.SystemCreditHistory
	condition := squirrel.And{squirrel.Eq{"uid": uid, "is_deleted": false}}
	if len(assignReasons) != 0 {
		condition = append(condition, squirrel.And{squirrel.Eq{"assign_reason": assignReasons}})
	}
	sql, args, err := condition.ToSql()
	if err != nil {
		return nil, err
	}
	err = d.base.Model(&model.SystemCreditHistory{}).
		Where(sql, args...).
		Find(&histories).Error
	return histories, err
}

func (d *SystemCreditHistoryDao) ListSystemCreditHistory(
	uid uint64,
	offset int,
	limit int,
) ([]*model.SystemCreditHistory, error) {
	var creditHistories []*model.SystemCreditHistory
	err := d.base.Model(&model.SystemCreditHistory{}).
		Where("uid=? and is_deleted=false", uid).
		Order("created_at desc").
		Limit(limit).
		Offset(offset).
		Find(&creditHistories).
		Error
	return creditHistories, err
}

func (d *SystemCreditHistoryDao) CountSystemCreditHistory(uid uint64) (uint64, error) {
	var count uint64
	err := d.base.Model(&model.SystemCreditHistory{}).
		Where("uid=? and is_deleted=false", uid).
		Count(&count).
		Error
	return count, err
}
