package model_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/qvmprice/model"
)

func TestQVMPriceTableCreate(t *testing.T) {
	sandbox := buildSandbox(t)

	dao := sandbox.dao.QVMPriceTable

	// ID 必须为 0
	err := dao.Create(&model.QVMPriceTable{
		ID:         1,
		UID:        233,
		Remark:     "foo",
		IsDisabled: true,
	})
	assert.Error(t, err)

	// 插入成功会自动更新 ID 等字段
	x := model.QVMPriceTable{
		ID:         0,
		UID:        0,
		Remark:     "BO-23456789",
		IsDisabled: false,
	}
	err = dao.Create(&x)
	assert.NoError(t, err)
	assert.NotZero(t, x.ID)
	assert.NotZero(t, x.CreatedAt)
	assert.NotZero(t, x.UpdatedAt)

	// CreatedAt UpdatedAt 如果插入时显式设置了值，应该保留
	epoch := time.Now().Truncate(1 * time.Microsecond) // DATETIME(6) 精度限制
	y := model.QVMPriceTable{
		ID:         0,
		UID:        233,
		Remark:     "foo",
		IsDisabled: true,
		CreatedAt:  epoch,
		UpdatedAt:  epoch,
	}
	err = dao.Create(&y)
	assert.NoError(t, err)
	assert.NotZero(t, y.ID)
	assert.True(t, epoch.Equal(y.CreatedAt))
	assert.True(t, epoch.Equal(y.UpdatedAt))

	y2, err := dao.GetByID(y.ID)
	assert.NoError(t, err)
	assert.Equal(t, y.ID, y2.ID)
	assert.Equal(t, y.UID, y2.UID)
	assert.Equal(t, y.Remark, y2.Remark)
	assert.Equal(t, y.IsDisabled, y2.IsDisabled)
	assert.True(t, y.CreatedAt.Equal(y2.CreatedAt))
	assert.True(t, y.UpdatedAt.Equal(y2.UpdatedAt))

}

func TestQVMPriceTableToggle(t *testing.T) {
	sandbox := buildSandbox(t)

	dao := sandbox.dao.QVMPriceTable

	x := model.QVMPriceTable{
		ID:         0,
		UID:        0,
		Remark:     "BO-23456789",
		IsDisabled: true,
	}
	err := dao.Create(&x)
	assert.NoError(t, err)
	assert.NotZero(t, x.ID)

	ds, err := dao.ListDisabledStatusesByIDs([]uint64{x.ID})
	assert.NoError(t, err)
	assert.True(t, ds[x.ID])

	err = dao.ToggleByID(x.ID, true)
	assert.NoError(t, err)

	ds, err = dao.ListDisabledStatusesByIDs([]uint64{x.ID})
	assert.NoError(t, err)
	assert.False(t, ds[x.ID])

	// 不存在的 ID
	err = dao.ToggleByID(x.ID+1, false)
	assert.Error(t, err)
}
