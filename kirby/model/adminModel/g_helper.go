package adminModel

import "time"

func (g Granularity) Validate() error {
	switch g {
	case Granularity5Min,
		GranularityDay,
		GranularityMonth:
		return nil

	default:
		return errUnknownGranularity
	}
}

func (g Granularity) MinIntervalBetweenPoints() time.Duration {
	switch g {
	case Granularity5Min:
		return 5 * time.Minute
	case GranularityDay:
		return 24 * time.Hour
	case GranularityMonth:
		// 不适用：每 dataRow 只有一个点
		fallthrough
	default:
		return 0
	}
}

func (g Granularity) TimeOfNextPoint(t time.Time) time.Time {
	switch g {
	case Granularity5Min, GranularityDay:
		return t.Add(g.MinIntervalBetweenPoints())
	case GranularityMonth:
		return t.AddDate(0, 1, 0)
	default:
		return t
	}
}
