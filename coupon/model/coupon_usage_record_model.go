package model

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// CouponUsageRecord model definition
type CouponUsageRecord struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`

	UID uint64 `gorm:"column:uid"`
	// BatchID 券所对应的批次 ID
	BatchID uint64 `gorm:"column:batch_id;index:idx_coupon_usage_record_batch_id"`
	// CouponCode 券 code
	CouponCode string `gorm:"column:coupon_code"`
	// CouponID 关联的券 ID
	CouponID uint64 `gorm:"index:idx_coupon_usage_record_coupon_id"`
	// OrderHash 关联的 order hash
	OrderHash string `gorm:"index:idx_coupon_usage_record_order_hash"`
	// PoID 关联的 po ID
	PoID uint64 `gorm:"index:idx_coupon_usage_record_po_id"`
	// Money 这张券在这个 po 上抵扣的金额
	Money base.Money

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// CouponUsageRecordDao is data access object of CouponUsageRecord model
type CouponUsageRecordDao struct {
	base *dao.BaseDao
}

// NewCouponUsageRecordDao is constructor of CouponUsageRecordDao
func NewCouponUsageRecordDao(base *dao.BaseDao) *CouponUsageRecordDao {
	return &CouponUsageRecordDao{
		base: base,
	}
}

// XXX NOTE: 代码几乎完全跟 CouponLockDetail 一样的，fuck Golang

// GetByID selects a CouponUsageRecord by id
func (d *CouponUsageRecordDao) GetByID(id uint64, expires ...time.Duration) (*CouponUsageRecord, error) {
	model := &CouponUsageRecord{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponUsageRecord{}).
			Where("`id` = ?", id).
			First(value).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// Save inserts or updates a CouponUsageRecord by id
func (d *CouponUsageRecordDao) Save(model *CouponUsageRecord, expires ...time.Duration) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Save(value).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// BulkInsert 批量插入一组 CouponUsageRecords
func (d *CouponUsageRecordDao) BulkInsert(ctx context.Context, l []CouponUsageRecord) error {
	return d.base.BulkInsert(ctx, l)
}

// DeleteByCouponID delete records of CouponUsageRecord by coupon ID
func (d *CouponUsageRecordDao) DeleteByCouponID(id uint64) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Delete(&CouponUsageRecord{}, "`coupon_id` = ?", id).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("coupon_id", id)
	}
	return nil
}

// DeleteByOrderHash delete records of CouponUsageRecord by order hash
func (d *CouponUsageRecordDao) DeleteByOrderHash(orderHash string) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Delete(&CouponUsageRecord{}, "`order_hash` = ?", orderHash).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("order_hash", orderHash)
	}
	return nil
}

// ListAll returns all records of CouponUsageRecord
func (d *CouponUsageRecordDao) ListAll(
	offset int,
	limit int,
	expires ...time.Duration,
) ([]CouponUsageRecord, error) {
	var result []CouponUsageRecord
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponUsageRecord{}).
			Offset(offset).
			Limit(limit).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return result, nil
}

// ListByCouponID select records of CouponUsageRecord by coupon id
func (d *CouponUsageRecordDao) ListByCouponID(
	id uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) ([]CouponUsageRecord, error) {
	var result []CouponUsageRecord
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponUsageRecord{}).
			Where("`coupon_id` = ?", id).
			Offset(offset).
			Limit(limit).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return result, nil
}

// ListByOrderHash select records of CouponUsageRecord by order hash
func (d *CouponUsageRecordDao) ListByOrderHash(
	orderHash string,
	expires ...time.Duration,
) ([]CouponUsageRecord, error) {
	var result []CouponUsageRecord
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponUsageRecord{}).
			Where("`order_hash` = ?", orderHash).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err).WithField("order_hash", orderHash)
	}
	return result, nil
}

// ListByOrderHashes select records of CouponUsageRecord by order hashes
func (d *CouponUsageRecordDao) ListByOrderHashes(
	orderHashes []string,
) ([]CouponUsageRecord, error) {
	var result []CouponUsageRecord
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponUsageRecord{}).
			Where("`order_hash` IN (?)", orderHashes).
			Find(&result).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err).WithField("order_hashes", orderHashes)
	}
	return result, nil
}

// ListByCouponIDs select records of CouponUsageRecord by couponIDs
func (d *CouponUsageRecordDao) ListByCouponIDs(
	couponIDs []uint64,
) ([]CouponUsageRecord, error) {
	var result []CouponUsageRecord
	if len(couponIDs) <= 0 {
		return result, nil
	}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&CouponUsageRecord{}).
			Where("`coupon_id` IN (?)", couponIDs).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err).WithField("couponIDs", couponIDs)
	}
	return result, nil
}

// CountUsageByBatchID 获取 batchID 对应的券的使用数量
func (d *CouponUsageRecordDao) CountUsageByBatchID(batchID uint64) (count uint64, err error) {
	// SELECT COUNT(DISTINCT(`coupon_id`)) FROM coupon_usage_records WHERE batch_id = ?
	err = d.base.Table("coupon_usage_records").
		Select("COUNT(DISTINCT(`coupon_id`))").
		Where("`batch_id` = ?", batchID).
		Count(&count).
		Error
	if err != nil {
		return 0, errors.Trace(err).WithField("batchID", batchID)
	}
	return count, nil
}
