CREATE TABLE IF NOT EXISTS `{{ .TableName }}` (
  `id` bigint unsigned AUTO_INCREMENT NOT NULL,
  `uid` bigint unsigned NOT NULL COMMENT '该汇总序列计量归属的 UID',
  `start` DATETIME(6) NOT NULL COMMENT '该汇总序列计量的起始时刻（含）',
  `end` DATETIME(6) NOT NULL COMMENT '该汇总序列计量的结束时刻（不含）',
  `item_code` varchar(256) NOT NULL COMMENT '计费系统口径的计费项 code',
  `zone_code` bigint NOT NULL COMMENT '计费系统口径的区域 code',
  `aggr` varchar(64) NOT NULL COMMENT '用来产生该汇总序列的聚合规则 code',
  `measure_unit` varchar(16) NOT NULL COMMENT '该汇总序列数据的计量单位',
  `g` tinyint(1) NOT NULL COMMENT '该汇总序列数据的粒度',
  `agg_series_key` varchar(64) NOT NULL COMMENT '该汇总序列的数据的检索 key',
  `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '记录的创建时刻',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_{{ .TableName }}_ask` (`agg_series_key`)
)
  ENGINE=InnoDB
  ROW_FORMAT=COMPRESSED
  KEY_BLOCK_SIZE=8
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci;
