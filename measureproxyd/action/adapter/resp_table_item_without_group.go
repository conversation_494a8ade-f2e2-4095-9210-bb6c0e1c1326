package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measureproxyd/model"
)

// BuildPbRespTableItemWithoutGroup converts from model.RespTableItemWithoutGroup to the protobuf type.
func BuildPbRespTableItemWithoutGroup(x *model.RespTableItemWithoutGroup) (*pb.RespTableItemWithoutGroup, error) {
	return &pb.RespTableItemWithoutGroup{
		Time:   timestamppb.New(x.Time),
		Values: x.Values,
	}, nil
}

// BuildModelRespTableItemWithoutGroup converts from pb.RespTableItemWithoutGroup to the model layer type.
func BuildModelRespTableItemWithoutGroup(x *pb.RespTableItemWithoutGroup) (*model.RespTableItemWithoutGroup, error) {
	var t time.Time
	if x.Time != nil {
		err := x.Time.CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		t = x.Time.AsTime()
	}

	return &model.RespTableItemWithoutGroup{
		Time:   t,
		Values: x.Values,
	}, nil
}
