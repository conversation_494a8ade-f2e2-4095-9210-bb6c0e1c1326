package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measured/model"
)

func TestGetItemsInUseParamsV1RoundTrip(t *testing.T) {
	x := &model.GetItemsInUseParams{
		UID:         233,
		ProductCode: "fusion",
		Begin:       time.Date(2019, 7, 1, 0, 0, 0, 0, time.Local),
		End:         time.Date(2019, 8, 1, 0, 0, 0, 0, time.Local),
		ZoneCode:    3001,
		Debug:       true,
	}

	y, err := BuildPbGetItemsInUseParamsV1(x)
	assert.NoError(t, err)

	z, err := BuildModelGetItemsInUseParamsV1(y)
	assert.NoError(t, err)

	assert.Equal(t, x.UID, z.UID)
	assert.Equal(t, x.ProductCode, z.ProductCode)
	assert.Equal(t, x.Begin.UnixNano(), z.<PERSON>gin.<PERSON><PERSON><PERSON>())
	assert.Equal(t, x.End.<PERSON><PERSON><PERSON>(), z.<PERSON>.<PERSON><PERSON>())
	assert.Equal(t, x.ZoneCode, z.ZoneCode)
	assert.Equal(t, x.Debug, z.Debug)
}
