package adminModel

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParsedMeasureUnitIsCompatibleKind(t *testing.T) {
	testcases := []struct {
		a        string
		b        string
		expected bool
	}{
		{a: "", b: "", expected: false},
		{a: "", b: "1", expected: false},
		{a: "1", b: "1", expected: true},
		{a: "1", b: "2", expected: true},
		{a: "0", b: "1", expected: true},
		{a: "1", b: "1-张", expected: true},
		{a: "10-张", b: "100-张", expected: true},
		{a: "2-张", b: "5-张", expected: true},
		{a: "1-条", b: "1-坨", expected: true}, // XXX 目前系统不感知量词
		{a: "1024-个", b: "2048-只", expected: true},
		{a: "1", b: "B", expected: false},
		{a: "1", b: "bps", expected: false},
		{a: "1", b: "s", expected: false},
		{a: "B", b: "KB", expected: true},
		{a: "B", b: "KiB", expected: true},
		{a: "KB", b: "KiB", expected: true},
		{a: "B", b: "bps", expected: false},
		{a: "B", b: "hr", expected: false},
		{a: "min", b: "s", expected: true},
	}

	for _, tc := range testcases {
		desc := fmt.Sprintf("'%s' vs '%s' - should be %v", tc.a, tc.b, tc.expected)
		var a, b *ParsedMeasureUnit
		var err error
		if tc.a != "" {
			a, err = ParseMeasureUnitRepr(tc.a)
			assert.NoError(t, err, desc+" - parsing of a should succeed")
		}
		if tc.b != "" {
			b, err = ParseMeasureUnitRepr(tc.b)
			assert.NoError(t, err, desc+" - parsing of b should succeed")
		}

		assert.Equal(
			t,
			tc.expected,
			a.IsCompatibleKind(b),
			desc+" - a.IsCompatibleKind(b) should match expectation",
		)
		assert.Equal(
			t,
			tc.expected,
			b.IsCompatibleKind(a),
			desc+" - b.IsCompatibleKind(a) should match expectation",
		)
	}
}

func TestParsedMeasureUnitConvertValueTo(t *testing.T) {
	testcases := []struct {
		from        string
		to          string
		fromVal     uint64
		err         error
		expectedVal uint64
	}{
		{from: "1", to: "1", fromVal: 233, err: nil, expectedVal: 233},
		{from: "1", to: "2", fromVal: 233, err: nil, expectedVal: 116},
		{from: "10-张", to: "100-张", fromVal: 233, err: nil, expectedVal: 23},
		{from: "100-张", to: "10-张", fromVal: 233, err: errOriginalUnitTooCoarse, expectedVal: 0},
		{from: "B", to: "GiB", fromVal: 4651876843, err: nil, expectedVal: 4},
		{from: "B", to: "bps", fromVal: 4651876843, err: errIncompatibleUnits, expectedVal: 0}, // not yet
		{from: "ns", to: "s", fromVal: 10368000000000000, err: nil, expectedVal: 10368000},
		{from: "ns", to: "min", fromVal: 10368000000000000, err: nil, expectedVal: 172800},
		{from: "ns", to: "hr", fromVal: 10368000000000000, err: nil, expectedVal: 2880},
		{from: "ns", to: "d", fromVal: 10368000000000000, err: nil, expectedVal: 120},
	}

	for _, tc := range testcases {
		var desc string
		if tc.err != nil {
			desc = fmt.Sprintf("%d in %s -> %s: err = %v", tc.fromVal, tc.from, tc.to, tc.err)
		} else {
			desc = fmt.Sprintf(
				"%d in %s -> %d in %s",
				tc.fromVal,
				tc.from,
				tc.expectedVal,
				tc.to,
			)
		}

		a, err := ParseMeasureUnitRepr(tc.from)
		assert.NoError(t, err, desc+" - parsing of a should succeed")
		b, err := ParseMeasureUnitRepr(tc.to)
		assert.NoError(t, err, desc+" - parsing of b should succeed")

		actual, err := a.ConvertValueTo(tc.fromVal, b)
		assert.Equal(t, tc.err, err, desc)
		assert.Equal(t, tc.expectedVal, actual, desc)
	}
}
