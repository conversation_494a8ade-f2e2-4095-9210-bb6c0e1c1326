package service

import (
	"time"

	"qiniu.io/pay/dictd/model"
)

// DictBizService is business of dictd service
type DictBizService struct {
	dictDao      *model.DictDao
	cacheExpires time.Duration
}

// NewDictBizService is constructor of DictBizService
func NewDictBizService(dictDao *model.DictDao, expires time.Duration) *DictBizService {
	return &DictBizService{
		dictDao:      dictDao,
		cacheExpires: expires,
	}
}
