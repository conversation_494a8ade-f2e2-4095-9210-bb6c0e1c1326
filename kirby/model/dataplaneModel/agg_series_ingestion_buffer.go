package dataplaneModel

import (
	"context"
	"sync"
	"time"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
	"qiniu.io/pay/kirby/model/adminModel"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/bo-base/v4/uuid"
)

func generateAggSeriesKey() string {
	return "QKASK" + uuid.New()
}

type AggSeriesIngestionBuffer struct {
	dao *KirbyDataplaneDao
	loc *time.Location

	mu                    sync.Mutex
	pendingMetadataShards map[time.Time][]aggSeriesMetadataRow
	pendingDataShards     map[computedShardInfo][]dataRowAndKey
}

func (d *KirbyDataplaneDao) NewAggSeriesIngestionBuffer(
	loc *time.Location,
) *AggSeriesIngestionBuffer {
	return &AggSeriesIngestionBuffer{
		dao:                   d,
		loc:                   loc,
		mu:                    sync.Mutex{},
		pendingMetadataShards: make(map[time.Time][]aggSeriesMetadataRow),
		pendingDataShards:     make(map[computedShardInfo][]dataRowAndKey),
	}
}

type bufferedAggSeries struct {
	d            *AggregatedSeriesDetail
	aggSeriesKey string
	rows         []dataRow
}

func (b *AggSeriesIngestionBuffer) Add(aggSeries []*AggregatedSeriesDetail) error {
	var bas []bufferedAggSeries
	for _, s := range aggSeries {
		rows, err := rowsFromSparseReprAndTimespan(
			s.G,
			s.Start,
			s.End,
			b.loc,
			s.Data,
		)
		if err != nil {
			return err
		}
		aggSeriesKey := generateAggSeriesKey()
		bas = append(bas, bufferedAggSeries{
			d:            s,
			aggSeriesKey: aggSeriesKey,
			rows:         rows,
		})
	}

	b.mu.Lock()
	defer b.mu.Unlock()

	var eg errgroup.Group
	eg.Go(func() error {
		b.addMetadataLocked(bas)
		return nil
	})
	eg.Go(func() error {
		b.addDataLocked(bas)
		return nil
	})

	err := eg.Wait()
	if err != nil {
		return err
	}

	return nil
}

func (b *AggSeriesIngestionBuffer) addMetadataLocked(bas []bufferedAggSeries) {
	mdRows := lo.Map(bas, func(x bufferedAggSeries, _ int) aggSeriesMetadataRow {
		return aggSeriesMetadataRow{
			UID:          x.d.UID,
			Start:        x.d.Start.In(b.loc),
			End:          x.d.End.In(b.loc),
			ItemCode:     x.d.ItemCode,
			ZoneCode:     x.d.ZoneCode,
			AggR:         x.d.AggRCode,
			MeasureUnit:  adminModel.MeasureUnit(x.d.Unit),
			G:            x.d.G,
			AggSeriesKey: x.aggSeriesKey,
		}
	})

	sharded := lo.GroupBy(mdRows, func(x aggSeriesMetadataRow) time.Time {
		return base.ThisMonth(x.Start)
	})

	for shardMonth, data := range sharded {
		b.pendingMetadataShards[shardMonth] = append(b.pendingMetadataShards[shardMonth], data...)
	}
}

func (b *AggSeriesIngestionBuffer) addDataLocked(bas []bufferedAggSeries) {
	dataByMonth := lo.GroupBy(bas, func(x bufferedAggSeries) time.Time {
		return base.ThisMonth(x.d.Start.In(b.loc))
	})

	for month, monthBASes := range dataByMonth {
		sharded := lo.GroupBy(monthBASes, func(x bufferedAggSeries) computedShardInfo {
			return computeDataTableShard(
				dataTableKindAggregatedSeries,
				x.d.G,
				month,
				b.loc,
				"",
			)
		})

		for shardName, data := range sharded {
			for _, bas := range data {
				seriesRows := lo.Map(bas.rows, func(y dataRow, _ int) dataRowAndKey {
					return dataRowAndKey{
						SeriesKey: bas.aggSeriesKey,
						T0:        y.T0,
						P:         y.P,
					}
				})
				b.pendingDataShards[shardName] = append(b.pendingDataShards[shardName], seriesRows...)
			}
		}
	}
}

func (b *AggSeriesIngestionBuffer) Commit(ctx context.Context) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	// ensure shards
	// this has to be done outside of DB transaction because of DDL

	for month := range b.pendingMetadataShards {
		err := b.dao.AggSeriesMD.EnsureShard(month, month.Location())
		if err != nil {
			return err
		}
	}

	for shardInfo := range b.pendingDataShards {
		err := b.dao.DataMgr.getShard(&shardInfo).Ensure()
		if err != nil {
			return err
		}
	}

	// do the inserts
	return b.dao.DoTransaction(func(txnDao *KirbyDataplaneDao) error {
		var eg errgroup.Group
		eg.Go(func() error { return b.commitMetadataLocked(ctx, txnDao) })
		eg.Go(func() error { return b.commitDataLocked(ctx, txnDao) })
		return eg.Wait()
	})
}

func (b *AggSeriesIngestionBuffer) commitMetadataLocked(
	ctx context.Context,
	txnDao *KirbyDataplaneDao,
) error {
	type shardParam struct {
		month time.Time
		data  []aggSeriesMetadataRow
	}
	jobs := lo.MapToSlice(b.pendingMetadataShards, func(k time.Time, v []aggSeriesMetadataRow) shardParam {
		return shardParam{month: k, data: v}
	})

	const concurrency = 2 // 随便取的大概不太会打挂数据库的并发，要和 data 部分共同考虑
	_, err := resultgroup.ThrottledParallelMap(jobs, concurrency, func(p shardParam) (struct{}, error) {
		return struct{}{}, txnDao.AggSeriesMD.GetShard(p.month, b.loc).BulkInsert(ctx, p.data)
	})

	return err
}

func (b *AggSeriesIngestionBuffer) commitDataLocked(
	ctx context.Context,
	txnDao *KirbyDataplaneDao,
) error {
	type shardParam struct {
		si   computedShardInfo
		data []dataRowAndKey
	}
	jobs := lo.MapToSlice(b.pendingDataShards, func(k computedShardInfo, v []dataRowAndKey) shardParam {
		return shardParam{si: k, data: v}
	})

	const concurrency = 2 // 随便取的大概不太会打挂数据库的并发，要和 metadata 部分共同考虑
	_, err := resultgroup.ThrottledParallelMap(jobs, concurrency, func(p shardParam) (struct{}, error) {
		shard := txnDao.DataMgr.getShard(&p.si)
		return struct{}{}, shard.bulkInsert(p.data)
	})

	return err
}
