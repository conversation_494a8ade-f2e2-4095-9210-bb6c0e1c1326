package model

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// CarryType 结转类型
type CarryType string

const (
	// CarryTypeNone 不结转
	CarryTypeNone CarryType = "none"
	// CarryTypeDaily 按日结转
	CarryTypeDaily CarryType = "daily"
	// CarryTypeMonthly 按月结转
	CarryTypeMonthly CarryType = "monthly"
)

// Value implements the driver.Valuer interface
func (x CarryType) Value() (driver.Value, error) {
	return string(x), nil
}

// <PERSON>an implements the sql.Scanner interface
func (x *CarryType) Scan(src any) error {
	if src == nil {
		*x = ""
	} else {
		*x = CarryType(string(src.([]uint8)))
	}
	return nil
}

// Period 资源包计费周期
type Period string

const (
	// PeriodDaily 按日计
	PeriodDaily Period = "daily"
	// PeriodMonthly 按月计
	PeriodMonthly Period = "monthly"
	// PeriodLifeTime 生命期内计
	PeriodLifeTime Period = "lifetime"
)

// Value implements the driver.Valuer interface
func (x Period) Value() (driver.Value, error) {
	return string(x), nil
}

// Scan implements the sql.Scanner interface
func (x *Period) Scan(src any) error {
	if src == nil {
		*x = ""
	} else {
		*x = Period(string(src.([]uint8)))
	}
	return nil
}

// PackageItem model definition
type PackageItem struct {
	ID          uint64 `gorm:"primary_key"`
	PackageID   uint64 `gorm:"index:idx_package_item_package_id"`
	ItemID      uint64 `gorm:"index:idx_package_item_item_id"`
	ZoneID      uint64 `gorm:"index:idx_package_item_zone_id"`
	UnitID      uint64 `gorm:"index:idx_package_item_unit_id"`
	Name        string `gorm:"type:varchar(128)"`
	Description string `gorm:"type:varchar(512)"`
	Remark      string `gorm:"type:varchar(512)"`
	CarryType   CarryType
	Period      Period
	Quantity    uint64
	Price       uint64
	EffectTime  base.HNS
	DeadTime    base.HNS
	CreatedAt   time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt   time.Time `sql:"type:DATETIME(6)"`
}

// PackageItemDao is data access object of PackageItem model
type PackageItemDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *PackageItemDao) GetCachePrefix() string {
	return "measure:package_item:"
}

// GetCacheRefs get cache refs
func (d *PackageItemDao) GetCacheRefs() []dao.CacheLayer {
	return []dao.CacheLayer{
		(*PackageItemDao)(nil),
		(*PackageDao)(nil),
		(*PackageUserMapDao)(nil),
	}
}

// NewPackageItemDao is constructor of PackageItemDao
func NewPackageItemDao(base *dao.BaseDao) *PackageItemDao {
	cachePrefix := (*PackageItemDao)(nil).GetCachePrefix()
	return &PackageItemDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of PackageItem by id
func (d *PackageItemDao) GetByID(id uint64, expires ...time.Duration) (*PackageItem, error) {
	model := &PackageItem{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// ListByPackageID select records of PackageItem by package_id
func (d *PackageItemDao) ListByPackageID(
	pkgID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (data []PackageItem, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:list:package_id=%d&offset=%d&limit=%d",
		d.cachePrefix,
		pkgID,
		offset,
		limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&PackageItem{PackageID: pkgID}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &data, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"package_id": pkgID,
			"offset":     offset,
			"limit":      limit,
		})
	}
	return
}

// CountByPackageID select count of PackageItem by package_id
func (d *PackageItemDao) CountByPackageID(
	pkgID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:package_id=%d:count",
		d.cachePrefix,
		pkgID,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PackageItem{}).Where(&PackageItem{PackageID: pkgID}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"package_id": pkgID,
		})
	}
	return
}

// Save insert or update a record of PackageItem by id
func (d *PackageItemDao) Save(model *PackageItem, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// Delete delete a record of PackageItem
func (d *PackageItemDao) Delete(model *PackageItem) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
}

// DeleteByID delete a record of PackageItem by id
func (d *PackageItemDao) DeleteByID(id uint64) error {
	model := &PackageItem{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}

// ListByUIDTimePoint list all package_items by uid and effected at specified time point
func (d *PackageItemDao) ListByUIDTimePoint(
	uid uint64, at base.HNS,
) (packageItems []PackageItem, err error) {

	err = d.base.Execute(func(value any) error {
		return d.base.Model(&PackageItem{}).Joins(
			"join packages on `packages`.`id` = `package_items`.`package_id`").Joins(
			"join package_user_maps on `package_user_maps`.`package_id` = `packages`.`id`").Where(
			"`package_user_maps`.`uid` = ? and `package_items`.`effect_time` <= ? and `package_items`.`dead_time` > ?",
			uid, at, at).Find(value).Error
	}, &packageItems)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid": uid,
			"at":  at,
		})
	}
	return
}

// ListByUIDTimeRange list all package_items by uid and effected at specified time range
func (d *PackageItemDao) ListByUIDTimeRange(
	uid uint64, startTime, endTime base.HNS,
) (packageItems []PackageItem, err error) {

	err = d.base.Execute(func(value any) error {
		return d.base.Model(&PackageItem{}).Joins(
			"join packages on `packages`.`id` = `package_items`.`package_id`").Joins(
			"join package_user_maps on `package_user_maps`.`package_id` = `packages`.`id`").Where(
			"`package_user_maps`.`uid` = ? and `package_items`.`effect_time` < ? and `package_items`.`dead_time` > ?",
			uid, endTime, startTime).Find(value).Error
	}, &packageItems)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid":        uid,
			"start_time": startTime,
			"end_time":   endTime,
		})
	}
	return
}
