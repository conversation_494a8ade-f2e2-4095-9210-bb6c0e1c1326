package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/walletd/model"
)

func TestScopeItemMapRoundTrip(t *testing.T) {
	x := &model.ScopeItemMap{
		ID:         233,
		ScopeID:    2333,
		ItemID:     1,
		IsExcluded: false,
		CreatedAt:  time.Now().Round(0),
		UpdatedAt:  time.Now().Round(0),
	}

	y, err := BuildPbScopeItemMap(x)
	assert.NoError(t, err)

	z, err := BuildScopeItemMap(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.Scope<PERSON>, z.Scope<PERSON>)
	assert.Equal(t, x.ItemID, z.ItemID)
	assert.Equal(t, x.IsExcluded, z.IsExcluded)
	assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.<PERSON>ano(), z.UpdatedAt.UnixNano())
}

func TestScopeItemMapsRoundTrip(t *testing.T) {
	xs := []model.ScopeItemMap{
		{
			ID:         233,
			ScopeID:    2333,
			ItemID:     1,
			IsExcluded: false,
			CreatedAt:  time.Now().Round(0),
			UpdatedAt:  time.Now().Round(0),
		},
		{
			ID:         233,
			ScopeID:    2334,
			ItemID:     2,
			IsExcluded: true,
			CreatedAt:  time.Now().Round(0),
			UpdatedAt:  time.Now().Round(0),
		},
	}

	y, err := BuildPbScopeItemMaps(xs)
	assert.NoError(t, err)

	zs, err := BuildScopeItemMaps(y)
	assert.NoError(t, err)

	for i, x := range xs {
		z := zs[i]
		assert.Equal(t, x.ID, z.ID)
		assert.Equal(t, x.ScopeID, z.ScopeID)
		assert.Equal(t, x.ItemID, z.ItemID)
		assert.Equal(t, x.IsExcluded, z.IsExcluded)
		assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
		assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
	}
}
