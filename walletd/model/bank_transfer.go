package model

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/uuid"

	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// BankTransferStatus 银行转账状态
type BankTransferStatus int32

const (
	// BankTransferStatusUnknown 未知状态
	BankTransferStatusUnknown BankTransferStatus = 0
	// BankTransferStatusPendingReview 待审核
	BankTransferStatusPendingReview BankTransferStatus = 1
	// BankTransferStatusSalesUnconfirmed 有销售归属未确认
	BankTransferStatusSalesUnconfirmed BankTransferStatus = 2
	// BankTransferStatusSalesConfirmed 有销售归属已确认
	BankTransferStatusSalesConfirmed BankTransferStatus = 3
	// BankTransferStatusRecharging 充值中
	BankTransferStatusRecharging BankTransferStatus = 4
	// BankTransferStatusRecharged 已充值到账
	BankTransferStatusRecharged BankTransferStatus = 5
	// BankTransferStatusRevoking 撤销中
	BankTransferStatusRevoking BankTransferStatus = 6
	// BankTransferStatusRevoked 已撤销
	BankTransferStatusRevoked BankTransferStatus = 7
	// BankTransferStatusVoid 已作废
	BankTransferStatusVoid BankTransferStatus = 8
)

// RecordType 记录类型
type RecordType int32

const (
	// RecordTypeUnknown 未知类型
	RecordTypeUnknown RecordType = 0
	// RecordTypeFirstAllocate 初次分配
	RecordTypeFirstAllocate RecordType = 1
	// RecordTypeReAllocate 重新分配
	RecordTypeReAllocate RecordType = 2
)

// MatchingType 匹配方式
type MatchingType int32

const (
	// MatchingTypeUnknown 未知匹配方式
	MatchingTypeUnknown MatchingType = 0
	// MatchingTypeVirtualAccount 虚账号匹配
	MatchingTypeVirtualAccount MatchingType = 1
	// MatchingTypeRemark 备注匹配
	MatchingTypeRemark MatchingType = 2
	// MatchingTypeManual 人工匹配
	MatchingTypeManual MatchingType = 3
)

// BankTransfer 银行转账记录模型
type BankTransfer struct {
	ID                uint64             `gorm:"primary_key"`
	ParentSN          string             `gorm:"type:varchar(64)"`                                   // 关联记录
	SN                string             `gorm:"type:varchar(64);unique_index:idx_bank_transfer_sn"` // 唯一编号
	PaymentAccount    string             `gorm:"type:varchar(128)"`                                  // 打款账户
	PaymentAccountNo  string             `gorm:"type:varchar(64)"`                                   // 打款账号
	PaymentBank       string             `gorm:"type:varchar(128)"`                                  // 打款银行
	ReceivedAccount   string             `gorm:"type:varchar(128)"`                                  // 收款账户
	ReceivedAccountNo string             `gorm:"type:varchar(64)"`                                   // 收款账号
	ReceivedBank      string             `gorm:"type:varchar(128)"`                                  // 收款银行
	CurrencyType      base.CurrencyType  `gorm:"type:varchar(10);not null;default:'CNY'"`            // 币种
	Amount            *base.NMoney       `gorm:"type:decimal(20,4)"`                                 // 金额
	ReceivingDate     time.Time          `sql:"type:DATETIME(6)"`                                    // 收款日期
	Remark            string             `gorm:"type:text"`                                          // 备注
	IsVirtualAccount  bool               `sql:"type:tinyint"`                                        // 是否虚账号打款
	BankTxnNo         string             `gorm:"type:varchar(128)"`                                  // 银行交易唯一标识
	BankDescription   string             `gorm:"type:varchar(256)"`                                  // 银行摘要
	UID               uint64             `gorm:"index:idx_bank_transfer_uid"`                        // 七牛 uid
	RecordType        RecordType         `sql:"type:tinyint"`                                        // 记录类型(初次分配、重新分配)
	MatchingType      MatchingType       `sql:"type:tinyint"`                                        // 匹配方式(虚账号、备注、人工匹配)
	Creator           string             `gorm:"type:varchar(128)"`                                  // 创建者
	Status            BankTransferStatus `sql:"type:tinyint"`                                        // 状态

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// BankTransferQuery 银行转账记录查询条件
type BankTransferQuery struct {
	UID              uint64             // 用户ID
	Status           BankTransferStatus // 状态
	PaymentAccountNo string             // 打款账号
	PaymentAccount   string             // 打款账户
	ReceivedAccount  string             // 收款银行
	CreatedAtStart   *time.Time         // 创建时间开始
	CreatedAtEnd     *time.Time         // 创建时间结束
	Offset           int64              // 分页偏移
	Limit            int64              // 分页大小
	ParentSN         *string            // sn.parent_sn
}

// BankTransferRevokeParam param of revoke
type BankTransferRevokeParam struct {
	SN          string             `json`
	Creator     string             `json:"creator"`
	Reallocates []*ReallocateParam `json:"reallocates"`
}

// ReallocateParam param of reallocate
type ReallocateParam struct {
	Uid uint64 `json:"uid"`
	// 金额
	Amount *base.NMoney `json:"amount"`
	// 备注
	Remark string `json:"remark"`
}

// BankTransferDao 银行转账数据访问对象
type BankTransferDao struct {
	base *dao.BaseDao
}

// NewBankTransferDao 创建银行转账数据访问对象
func NewBankTransferDao(
	base *dao.BaseDao,
) *BankTransferDao {
	return &BankTransferDao{
		base: base,
	}
}

// Save 保存银行转账记录
func (d *BankTransferDao) Save(
	model *BankTransfer,
) error {
	if model.SN == "" {
		model.SN = uuid.New()
	}
	return d.base.Save(model).Error
}

// GetBySN 通过序列号查询银行转账记录
func (d *BankTransferDao) GetBySN(
	sn string,
) (*BankTransfer, error) {
	model := &BankTransfer{}
	err := d.base.Where("sn=?", sn).First(model).Error
	if err != nil {
		return nil, errors.Trace(err).WithField("sn", sn)
	}
	return model, nil
}

// ListByUID 通过用户ID查询银行转账记录列表
func (d *BankTransferDao) ListByUID(
	uid uint64,
	offset, limit int,
) ([]*BankTransfer, error) {
	var transfers []*BankTransfer
	err := d.base.Where("uid=?", uid).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&transfers).Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"uid":    uid,
			"offset": offset,
			"limit":  limit,
		})
	}
	return transfers, nil
}

// CountByUID 统计用户的银行转账记录数量
func (d *BankTransferDao) CountByUID(
	uid uint64,
) (int64, error) {
	var count int64
	err := d.base.Model(&BankTransfer{}).
		Where("uid=?", uid).
		Count(&count).Error
	if err != nil {
		return 0, errors.Trace(err).WithField("uid", uid)
	}
	return count, nil
}

// UpdateStatus 更新银行转账记录状态
func (d *BankTransferDao) UpdateStatus(
	sn string,
	status BankTransferStatus,
) error {
	err := d.base.Model(&BankTransfer{}).
		Where("sn=?", sn).
		Update("status", status).Error
	if err != nil {
		return errors.Trace(err).WithFields(errors.Fields{
			"sn":     sn,
			"status": status,
		})
	}
	return nil
}

// ListByStatus 通过状态查询银行转账记录列表
func (d *BankTransferDao) ListByStatus(
	status BankTransferStatus,
	offset, limit int,
) ([]*BankTransfer, error) {
	var transfers []*BankTransfer
	err := d.base.Where("status=?", status).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&transfers).Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"status": status,
			"offset": offset,
			"limit":  limit,
		})
	}
	return transfers, nil
}

// CountByStatus 统计特定状态的银行转账记录数量
func (d *BankTransferDao) CountByStatus(
	status BankTransferStatus,
) (int64, error) {
	var count int64
	err := d.base.Model(&BankTransfer{}).
		Where("status=?", status).
		Count(&count).Error
	if err != nil {
		return 0, errors.Trace(err).WithField("status", status)
	}
	return count, nil
}

// ListByQuery 根据查询条件获取银行转账记录列表
func (d *BankTransferDao) ListByQuery(
	ctx context.Context,
	query *BankTransferQuery,
) ([]*BankTransfer, error) {
	db := d.base.Model(&BankTransfer{})

	// 构建查询条件
	db = d.buildQueryCondition(db, query)

	// 排序、分页
	db = db.Order("created_at DESC").
		Offset(int(query.Offset)).
		Limit(int(query.Limit))

	// 执行查询
	var transfers []*BankTransfer
	err := db.Find(&transfers).Error
	if err != nil {
		return nil, errors.Trace(err).WithField("query", query)
	}

	return transfers, nil
}

// CountByQuery 根据查询条件统计银行转账记录数量
func (d *BankTransferDao) CountByQuery(
	ctx context.Context,
	query *BankTransferQuery,
) (uint64, error) {
	db := d.base.Model(&BankTransfer{})

	// 构建查询条件
	db = d.buildQueryCondition(db, query)

	// 执行统计
	var count uint64
	err := db.Count(&count).Error
	if err != nil {
		return 0, errors.Trace(err).WithField("query", query)
	}

	return count, nil
}

// buildQueryCondition 构建查询条件
func (d *BankTransferDao) buildQueryCondition(
	db *gorm.DB,
	query *BankTransferQuery,
) *gorm.DB {
	// 根据 UID 过滤
	if query.UID > 0 {
		db = db.Where("uid = ?", query.UID)
	}

	// 根据状态过滤
	if query.Status > 0 {
		db = db.Where("status = ?", query.Status)
	}

	// 根据打款账号
	if query.PaymentAccountNo != "" {
		db = db.Where("payment_account_no LIKE ?", "%"+query.PaymentAccountNo+"%")
	}

	// 根据打款账户过滤
	if query.PaymentAccount != "" {
		db = db.Where("payment_account LIKE ?", "%"+query.PaymentAccount+"%")
	}

	// 根据收款银行过滤
	if query.ReceivedAccount != "" {
		db = db.Where("received_account LIKE ?", "%"+query.ReceivedAccount+"%")
	}

	// 根据时间范围过滤
	if query.CreatedAtStart != nil {
		db = db.Where("created_at >= ?", query.CreatedAtStart)
	}

	if query.CreatedAtEnd != nil {
		db = db.Where("created_at <= ?", query.CreatedAtEnd)
	}

	if query.ParentSN != nil {
		db = db.Where("parent_sn = ?", query.ParentSN)
	}

	return db
}

// SearchPaymentAccounts 模糊查询打款账户信息
func (d *BankTransferDao) SearchPaymentAccounts(
	ctx context.Context,
	paymentAccount string,
) ([]string, error) {
	if paymentAccount == "" {
		return []string{}, nil
	}

	// 构建 SQL 查询，使用 DISTINCT 去重
	var accounts []string
	err := d.base.Model(&BankTransfer{}).
		Select("DISTINCT payment_account").
		Where("payment_account LIKE ?", "%"+paymentAccount+"%").
		Limit(30). // 限制返回数量，避免返回过多结果
		Pluck("payment_account", &accounts).Error

	if err != nil {
		return nil, errors.Trace(err).WithField("payment_account", paymentAccount)
	}

	return accounts, nil
}
