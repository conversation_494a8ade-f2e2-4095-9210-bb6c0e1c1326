package enums

// Event defines action type
type Event uint32

const (
	// UnknownEvent is default value
	UnknownEvent = Event(iota) // 0
	// EventCreate
	EventCreate // 1
	// EventCancel means event = cancel
	EventCancel // 2
	// EventAdjustLineOrPeriod  means event = adjustLineOrPeriod
	EventAdjustLineOrPeriod // 3
	// EventActivate means event = activate
	EventActivate // 4
	// EventRevoke means event = revoke
	EventRevoke // 5
)

func (s Event) String() string {
	switch s {
	case UnknownEvent:
		return "UNKNOWN"
	case EventCreate:
		return "CREATE"
	case EventCancel:
		return "CANCEL"
	case EventAdjustLineOrPeriod:
		return "ADJUST_LINE_OR_PERIOD"
	case EventActivate:
		return "ACTIVATE"
	case EventRevoke:
		return "REVOKE"
	}
	return "UNKNOWN"
}
