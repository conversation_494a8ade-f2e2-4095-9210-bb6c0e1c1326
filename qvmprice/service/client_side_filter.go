package service

import "qiniu.io/pay/qvmprice/model"

// 在客户端（i.e. 非数据库一端）对（往往是并发）拉到的不一定完全满足 JOIN 关系的
// QVMPriceItemStair 记录进行过滤，去掉那些不该被查到的数据
//
// 具体而言，并发查询时无法严格按照关联字段（QVMPriceItemID）查询，只能按照冗余字段查询，
// 而 QVMPriceItemStair 没有冗余 zone_code 字段，这就意味着可能存在许多按照真正被查到的
// QVMPriceItems 实际不该被查出来的 QVMPriceItemStairs。这些需要在实际使用前被过滤掉。
func filterQVMPriceItemStairsClientside(
	priceItems []*model.QVMPriceItem,
	priceItemStairs []*model.QVMPriceItemStair,
) []*model.QVMPriceItemStair {
	distinctPriceItemsSet := distinctSet(
		func(x *model.QVMPriceItem) uint64 {
			return x.ID
		},
		priceItems,
	)

	return filter(
		func(x *model.QVMPriceItemStair) bool {
			_, joinSatisfied := distinctPriceItemsSet[x.QVMPriceItemID]
			return joinSatisfied
		},
		priceItemStairs,
	)
}

func filterDisabledRecords(
	priceTableDisabledStatuses map[uint64]bool,
	priceItems []*model.QVMPriceItem,
	groupDiscounts []*model.QVMGroupDiscount,
) ([]*model.QVMPriceItem, []*model.QVMGroupDiscount) {
	filteredPriceItems := filter(
		func(x *model.QVMPriceItem) bool {
			// 如果自己就被禁用了，不要返回
			if x.IsDisabled {
				return false
			}

			// 无论是不满足 JOIN 条件，还是所属的价格表被整体禁用了，这个 QVMPriceItem 都不应该被返回
			isPriceTableDisabled, joinSatisfied := priceTableDisabledStatuses[x.QVMPriceTableID]
			if !joinSatisfied {
				return false
			}
			if isPriceTableDisabled {
				return false
			}

			return true
		},
		priceItems,
	)

	// 此处仍然存在重复，是因为目前不方便针对普遍存在的 **字段** 写泛型
	filteredGroupDiscounts := filter(
		func(x *model.QVMGroupDiscount) bool {
			// 如果自己就被禁用了，不要返回
			if x.IsDisabled {
				return false
			}

			// 无论是不满足 JOIN 条件，还是所属的价格表被整体禁用了，这个 QVMPriceItem 都不应该被返回
			isPriceTableDisabled, joinSatisfied := priceTableDisabledStatuses[x.QVMPriceTableID]
			if !joinSatisfied {
				return false
			}
			if isPriceTableDisabled {
				return false
			}

			return true

		},
		groupDiscounts,
	)

	return filteredPriceItems, filteredGroupDiscounts
}
