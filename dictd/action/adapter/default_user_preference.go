package adapter

import (
	"time"

	pb "github.com/qbox/pay-sdk/dict"
	"google.golang.org/protobuf/types/known/timestamppb"
	"qiniu.io/pay/dictd/model"
)

func BuildPbDefaultUserPreference(preference *model.DefaultUserPreference) *pb.DefaultUserPreference {
	if preference == nil {
		return nil
	}

	var startTime *timestamppb.Timestamp
	var endTime *timestamppb.Timestamp
	if preference.RegisterStartTime != nil {
		startTime = timestamppb.New(*preference.RegisterStartTime)
	}
	if preference.RegisterEndTime != nil {
		startTime = timestamppb.New(*preference.RegisterEndTime)
	}
	return &pb.DefaultUserPreference{
		Id:                    preference.ID,
		ItemCode:              preference.ItemCode,
		ZonePreferences:       preference.ZonePreferences,
		UserRegisterStartTime: startTime,
		UserRegisterEndTime:   endTime,
	}
}

func BuildPbDefaultUserPreferences(preferences []*model.DefaultUserPreference) *pb.DefaultUserPreferences {
	var p []*pb.DefaultUserPreference
	for _, preference := range preferences {
		p = append(p, BuildPbDefaultUserPreference(preference))
	}
	return &pb.DefaultUserPreferences{
		Preferences: p,
		Count:       uint64(len(preferences)),
	}
}

func BuildDefaultUserPreferences(preferences *pb.DefaultUserPreference) *model.DefaultUserPreference {
	var startTime *time.Time
	if preferences.GetUserRegisterStartTime() != nil {
		t := preferences.GetUserRegisterStartTime().AsTime()
		startTime = &t
	}
	var endTime *time.Time
	if preferences.GetUserRegisterEndTime() != nil {
		t := preferences.GetUserRegisterEndTime().AsTime()
		endTime = &t
	}
	return &model.DefaultUserPreference{
		ID:                preferences.GetId(),
		ItemCode:          preferences.GetItemCode(),
		ZonePreferences:   preferences.GetZonePreferences(),
		RegisterStartTime: startTime,
		RegisterEndTime:   endTime,
	}
}
