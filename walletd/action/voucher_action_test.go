package action

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"
	"qiniu.io/pay/qpay/common"
	"qiniu.io/pay/qpay/enums"
	"qiniu.io/pay/qpay/wallet/dao"
)

func prepareVoucher(t *testing.T, sandbox *TestSandbox) *pb.VoucherItem {

	scope := pb.LegacyScope{
		IsAll:    true,
		Products: make([]*pb.LegacyScopeMap, 0),
		Groups:   make([]*pb.LegacyScopeMap, 0),
		Items:    make([]*pb.LegacyScopeMap, 0),
	}

	scope.Products = append(scope.Products, &pb.LegacyScopeMap{
		Code:       "KODO",
		IsExcluded: false,
	})

	scope.Products = append(scope.Products, &pb.LegacyScopeMap{
		Code:       "QVM",
		IsExcluded: true,
	})
	now := time.Now()
	d, _ := time.ParseDuration("-24h")
	effectTime := now.Add(d)
	et := timestamppb.New(effectTime)

	d, _ = time.ParseDuration("24h")
	deadTime := now.Add(d)
	dt := timestamppb.New(deadTime)

	request := pb.VoucherItem{
		Amount:     100 * 10000,
		Balance:    100 * 10000,
		EffectTime: et,
		DeadTime:   dt,
		//ExpiredTime:
		SubSystem:       string(enums.VoucherSubsystemBill),
		Day:             1,
		Type:            string(enums.VoucherTypeRecharge),
		Desc:            "this is a test voucher",
		Title:           "AABB",
		ArrearageCanUse: true,
		MaxActivation:   3,
		BatchId:         common.GetUUID(),
		Scope:           &scope,
		CurrencyType:    base.CurrencyTypeCNY.String(),
	}

	response, err := sandbox.PaymentAction.CreateVoucher(context.Background(), &request)
	assert.NoError(t, err)

	v, err := sandbox.PaymentAction.GetVoucherByCode(context.Background(), &pb.CodeParam{Code: response.Code})
	assert.NoError(t, err)

	assert.EqualValues(t, request.Title, v.Title)
	assert.EqualValues(t, request.Amount, v.Amount)
	assert.EqualValues(t, request.Desc, v.Desc)
	assert.NotNil(t, v.CreatedAt)
	assert.NotNil(t, v.UpdatedAt)
	fmt.Println(v.Excode)

	return v
}

func TestPaymentAction_CreateVoucher(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)
	prepareVoucher(t, sandbox)
}

func TestPaymentAction_ActiveVoucher(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)
	voucher := prepareVoucher(t, sandbox)

	voucher.Uid = dao.UID

	v, err := sandbox.PaymentAction.ActiveVoucher(context.Background(), voucher)
	assert.NoError(t, err)
	assert.EqualValues(t, enums.VoucherStatusActive, v.Status)

	txn, err := sandbox.Factory.QueryService.QueryPaymentTransactionByExcode(context.Background(), v.Uid, v.Excode, enums.PaymentPrefixCoupon, "active")
	assert.NoError(t, err)
	assert.EqualValues(t, "COUPON", txn.Prefix)
	assert.EqualValues(t, "active", txn.Type)

	wt, err := sandbox.Factory.QueryService.QueryWalletTransactionsByPaymentID(context.Background(), txn.PaymentID)
	assert.NoError(t, err)
	assert.EqualValues(t, 1, len(wt))
	assert.EqualValues(t, v.AssetId, wt[0].AssetID)
	assert.EqualValues(t, "coupon", wt[0].PayTool)
}

func TestPaymentAction_CancelVoucher(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, 0)
	voucher := prepareVoucher(t, sandbox)

	v, err := sandbox.PaymentAction.CancelVoucher(context.Background(), voucher)
	assert.NoError(t, err)
	assert.EqualValues(t, enums.VoucherStatusCancel, v.Status)

	// 取消之后再去激活，应该报错
	voucher.Uid = dao.UID
	_, err = sandbox.PaymentAction.ActiveVoucher(context.Background(), voucher)
	assert.Error(t, err, "取消抵用券之后再去激活，应该报错 voucher is canceled")
}

func TestPaymentAction_ListVoucherByCondition(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)
	voucher := prepareVoucher(t, sandbox)

	condition := pb.ListVoucherConditionRequest{
		SubSystem:       string(enums.VoucherSubsystemBill),
		Type:            string(enums.VoucherTypeRecharge),
		ArrearageCanUse: 1,
		Status:          uint64(enums.VoucherStatusNew),
		Title:           "AABB",
	}

	vs, err := sandbox.PaymentAction.ListVoucherByCondition(context.Background(), &condition)
	assert.NoError(t, err)

	assert.EqualValues(t, 1, len(vs.VoucherItem))
	assert.EqualValues(t, string(enums.VoucherSubsystemBill), vs.VoucherItem[0].SubSystem)
	assert.EqualValues(t, string(enums.VoucherTypeRecharge), vs.VoucherItem[0].Type)
	assert.EqualValues(t, voucher.Code, vs.VoucherItem[0].Code)

	_, err = sandbox.PaymentAction.ActiveVoucher(context.Background(), &pb.VoucherItem{
		Code:   vs.VoucherItem[0].Code,
		Excode: common.GetUUID(),
		Uid:    dao.UID,
		Desc:   "active voucher",
	})
	assert.NoError(t, err)

	condition = pb.ListVoucherConditionRequest{
		Uids:            []uint64{dao.UID},
		SubSystem:       string(enums.VoucherSubsystemBill),
		Type:            string(enums.VoucherTypeRecharge),
		ArrearageCanUse: 1,
		Status:          uint64(enums.VoucherStatusActive),
		Title:           "AABB",
	}

	vs, err = sandbox.PaymentAction.ListVoucherByCondition(context.Background(), &condition)
	assert.NoError(t, err)

	assert.EqualValues(t, 1, len(vs.VoucherItem))
	assert.EqualValues(t, string(enums.VoucherSubsystemBill), vs.VoucherItem[0].SubSystem)
	assert.EqualValues(t, string(enums.VoucherTypeRecharge), vs.VoucherItem[0].Type)
	assert.EqualValues(t, voucher.Code, vs.VoucherItem[0].Code)
}

func TestPaymentAction_GetVoucherByID(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)
	voucher := prepareVoucher(t, sandbox)

	v, err := sandbox.PaymentAction.GetVoucherByCode(context.Background(), &pb.CodeParam{Code: voucher.Code})
	assert.NoError(t, err)
	assert.EqualValues(t, string(enums.VoucherSubsystemBill), v.SubSystem)
	assert.EqualValues(t, string(enums.VoucherTypeRecharge), v.Type)
	assert.EqualValues(t, voucher.Code, v.Code)

	// 查询不存在的券，应该报错
	_, err = sandbox.PaymentAction.GetVoucherByCode(context.Background(), &pb.CodeParam{Code: "NotExistVoucherCode"})
	assert.Error(t, err, "查询不存在的券，应该报错 record not found")
}

func TestPaymentAction_GetVoucherByCodes(t *testing.T) {
	sandbox := BuildSandbox(t)
	prepareCurrency(t, sandbox, sandbox.UID)
	voucher := prepareVoucher(t, sandbox)

	codeParams := []*pb.CodeParam{
		{
			Code: voucher.Code,
		},
	}
	paramList := pb.CodeParamList{
		Code: codeParams,
	}

	voucherItemList, err := sandbox.PaymentAction.ListVoucherByCodes(context.Background(), &paramList)
	assert.NoError(t, err)
	if len(voucherItemList.VoucherItem) == 1 {
		assert.EqualValues(t, voucher.Code, voucherItemList.VoucherItem[0].Code)
		assert.EqualValues(t, string(enums.VoucherSubsystemBill), voucherItemList.VoucherItem[0].SubSystem)
		assert.EqualValues(t, string(enums.VoucherTypeRecharge), voucherItemList.VoucherItem[0].Type)
	} else {
		t.Fatal("ListVoucherByCodes 结果不预期")
	}
}
