-- MySQL dump 10.13  Distrib 5.7.20, for osx10.12 (x86_64)
--
-- Host: ************    Database: pay_wallet
-- ------------------------------------------------------
-- Server version	5.7.20

/*!40101 SET @OLD_CHARACTER_SET_CLIENT = @@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS = @@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION = @@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE = @@TIME_ZONE */;
/*!40103 SET TIME_ZONE = '+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS = @@UNIQUE_CHECKS, UNIQUE_CHECKS = 0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS = @@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS = 0 */;
/*!40101 SET @OLD_SQL_MODE = @@SQL_MODE, SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES = @@SQL_NOTES, SQL_NOTES = 0 */;

--
-- Table structure for table `balance_insufficiency_predictions`
--

DROP TABLE IF EXISTS `balance_insufficiency_predictions`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `balance_insufficiency_predictions`
(
    `id`                             bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uid`                            int(10) unsigned                        DEFAULT NULL,
    `email`                          varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `least_unaffordable_days`        int(10) unsigned                        DEFAULT NULL,
    `is_merge_account`               tinyint(1)                              DEFAULT NULL,
    `parent_id`                      int(10) unsigned                        DEFAULT NULL,
    `parent_least_unaffordable_days` int(10) unsigned                        DEFAULT NULL,
    `parent_email`                   varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `data_date`                      date                NOT NULL            DEFAULT '1000-01-01',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 13
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupon_batches`
--

DROP TABLE IF EXISTS `coupon_batches`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupon_batches`
(
    `id`                            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `name`                          varchar(255)        DEFAULT NULL,
    `description`                   varchar(255)        DEFAULT NULL,
    `remark`                        varchar(255)        DEFAULT NULL,
    `reason`                        bigint(20)          DEFAULT NULL,
    `reason_desc`                   varchar(255)        DEFAULT NULL,
    `state`                         bigint(20)          DEFAULT NULL,
    `bind_method`                   bigint(20)          DEFAULT NULL,
    `start_time`                    bigint(20)          DEFAULT NULL,
    `end_time`                      bigint(20)          DEFAULT NULL,
    `batch_size`                    bigint(20) unsigned DEFAULT NULL,
    `num_activated`                 bigint(20) unsigned DEFAULT NULL,
    `num_used`                      bigint(20) unsigned DEFAULT 0,
    `max_activation_times_per_user` bigint(20) unsigned DEFAULT NULL,
    `time_period_type`              bigint(20)          DEFAULT NULL,
    `effect_days`                   bigint(20) unsigned DEFAULT NULL,
    `coupon_effect_time`            bigint(20)          DEFAULT NULL,
    `coupon_dead_time`              bigint(20)          DEFAULT NULL,
    `threshold_money`               bigint(20)          DEFAULT NULL,
    `coupon_money`                  bigint(20)          DEFAULT NULL,
    `is_multiple_use`               tinyint(1)          DEFAULT NULL,
    `applicable_user_type`          bigint(20)          DEFAULT NULL,
    `applicable_pay_mode`           bigint(20)          DEFAULT NULL,
    `arrear_can_use`                tinyint(1)          DEFAULT NULL,
    `is_unlimited_scope`            tinyint(1)          DEFAULT NULL,
    `coupon_scope_id`               bigint(20) unsigned DEFAULT NULL,
    `coupon_scope_desc`             varchar(255)        DEFAULT NULL,
    `applicant`                     bigint(20) unsigned DEFAULT NULL,
    `url`                           varchar(255)        DEFAULT NULL,
    `created_at`                    datetime(6)         DEFAULT NULL,
    `updated_at`                    datetime(6)         DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3836
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupon_cashes`
--

DROP TABLE IF EXISTS `coupon_cashes`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupon_cashes`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `code`        varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `type`        varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `scope_id`    bigint(20) unsigned                     DEFAULT NULL,
    `name`        varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `description` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `remark`      varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `money`       bigint(20)                              DEFAULT NULL,
    `effect_time` bigint(20)                              DEFAULT NULL,
    `dead_time`   bigint(20)                              DEFAULT NULL,
    `created_at`  datetime(6)                             DEFAULT NULL,
    `updated_at`  datetime(6)                             DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_coupon_cash_code` (`code`),
    KEY `idx_coupon_cash_scope_id` (`scope_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupon_discounts`
--

DROP TABLE IF EXISTS `coupon_discounts`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupon_discounts`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `code`        varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `type`        varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `scope_id`    bigint(20) unsigned                     DEFAULT NULL,
    `name`        varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `description` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `remark`      varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `discount`    bigint(20) unsigned                     DEFAULT NULL,
    `effect_time` bigint(20)                              DEFAULT NULL,
    `dead_time`   bigint(20)                              DEFAULT NULL,
    `created_at`  datetime(6)                             DEFAULT NULL,
    `updated_at`  datetime(6)                             DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_coupon_discount_code` (`code`),
    KEY `idx_coupon_discount_scope_id` (`scope_id`),
    KEY `idx_coupon_discount_created_at` (`created_at`),
    KEY `idx_coupon_discount_updated_at` (`updated_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 269
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupon_lock_details`
--

DROP TABLE IF EXISTS `coupon_lock_details`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupon_lock_details`
(
    `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `lock_record_id` bigint(20) unsigned DEFAULT NULL,
    `uid`            bigint(20) unsigned DEFAULT 0,
    `batch_id`       bigint(20) unsigned DEFAULT 0,
    `coupon_code`    varchar(64)         DEFAULT '',
    `coupon_id`      bigint(20) unsigned DEFAULT NULL,
    `order_hash`     varchar(255)        DEFAULT NULL,
    `po_id`          bigint(20) unsigned DEFAULT NULL,
    `money`          bigint(20)          DEFAULT NULL,
    `created_at`     datetime(6)         DEFAULT NULL,
    `updated_at`     datetime(6)         DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_coupon_lock_detail_lock_record_id` (`lock_record_id`),
    KEY `idx_coupon_lock_detail_coupon_id` (`coupon_id`),
    KEY `idx_coupon_lock_detail_order_hash` (`order_hash`),
    KEY `idx_coupon_lock_detail_po_id` (`po_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 297
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupon_lock_records`
--

DROP TABLE IF EXISTS `coupon_lock_records`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupon_lock_records`
(
    `id`               bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uid`              bigint(20) unsigned          DEFAULT NULL,
    `lock_key`         varchar(255)                 DEFAULT NULL,
    `order_hash`       varchar(255)                 DEFAULT NULL,
    `union_order_hash` varchar(64)         NOT NULL DEFAULT '',
    `created_at`       datetime(6)                  DEFAULT NULL,
    `updated_at`       datetime(6)                  DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `coupon_lock_record_lock_key` (`lock_key`),
    KEY `coupon_lock_record_order_hash` (`order_hash`),
    KEY `coupon_lock_record_uid` (`uid`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 285
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupon_rebates`
--

DROP TABLE IF EXISTS `coupon_rebates`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupon_rebates`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `code`          varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `type`          varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `scope_id`      bigint(20) unsigned                     DEFAULT NULL,
    `name`          varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `description`   varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `remark`        varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `threshold`     bigint(20) unsigned                     DEFAULT NULL,
    `money`         bigint(20) unsigned                     DEFAULT NULL,
    `effect_time`   bigint(20)                              DEFAULT NULL,
    `dead_time`     bigint(20)                              DEFAULT NULL,
    `created_at`    datetime(6)                             DEFAULT NULL,
    `updated_at`    datetime(6)                             DEFAULT NULL,
    `currency_type` VARCHAR(10)         NOT NULL COMMENT '币种（如CNY USD）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_coupon_rebate_code` (`code`),
    KEY `idx_coupon_rebate_scope_id` (`scope_id`),
    KEY `idx_coupon_rebate_created_at` (`created_at`),
    KEY `idx_coupon_rebate_updated_at` (`updated_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 216
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupon_usage_records`
--

DROP TABLE IF EXISTS `coupon_usage_records`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupon_usage_records`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uid`         bigint(20) unsigned DEFAULT 0,
    `batch_id`    bigint(20) unsigned DEFAULT 0,
    `coupon_code` varchar(64)         DEFAULT '',
    `coupon_id`   bigint(20) unsigned DEFAULT NULL,
    `order_hash`  varchar(255)        DEFAULT NULL,
    `po_id`       bigint(20) unsigned DEFAULT NULL,
    `money`       bigint(20)          DEFAULT NULL,
    `created_at`  datetime(6)         DEFAULT NULL,
    `updated_at`  datetime(6)         DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_coupon_usage_record_order_hash` (`order_hash`),
    KEY `idx_coupon_usage_record_po_id` (`po_id`),
    KEY `idx_coupon_usage_record_coupon_id` (`coupon_id`),
    KEY `idx_coupon_usage_record_batch_id` (`batch_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 90
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupon_user_maps`
--

DROP TABLE IF EXISTS `coupon_user_maps`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupon_user_maps`
(
    `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `coupon_id`      bigint(20) unsigned                    DEFAULT NULL,
    `uid`            bigint(20) unsigned                    DEFAULT NULL,
    `zone_id`        bigint(20) unsigned                    DEFAULT NULL,
    `transaction_id` bigint(20) unsigned                    DEFAULT NULL,
    `type`           varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `effect_time`    bigint(20)                             DEFAULT NULL,
    `dead_time`      bigint(20)                             DEFAULT NULL,
    `created_at`     datetime(6)                            DEFAULT NULL,
    `updated_at`     datetime(6)                            DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_coupon_user_map_zone_id` (`zone_id`),
    KEY `idx_coupon_user_map_transaction_id` (`transaction_id`),
    KEY `idx_coupon_user_map_coupon_id` (`coupon_id`),
    KEY `idx_coupon_user_map_uid` (`uid`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 382821
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupons`
--

DROP TABLE IF EXISTS `coupons`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupons`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `batch_id`    bigint(20) unsigned DEFAULT NULL,
    `uid`         bigint(20) unsigned DEFAULT NULL,
    `code`        varchar(64)         DEFAULT NULL,
    `bind_method` bigint(20)          DEFAULT NULL,
    `state`       bigint(20)          DEFAULT NULL,
    `money`       bigint(20)          DEFAULT NULL,
    `balance`     bigint(20)          DEFAULT NULL,
    `bound_at`    bigint(20)          DEFAULT NULL,
    `effect_time` bigint(20)          DEFAULT NULL,
    `dead_time`   bigint(20)          DEFAULT NULL,
    `created_at`  datetime(6)         DEFAULT NULL,
    `updated_at`  datetime(6)         DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_coupon_uid_state` (`uid`, `state`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2324
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `jobs`
--

DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `type`         varchar(255)        NOT NULL,
    `code`         varchar(64)         NOT NULL,
    `name`         varchar(255)  DEFAULT NULL,
    `desc`         varchar(255)  DEFAULT NULL,
    `remark`       varchar(255)  DEFAULT NULL,
    `params`       text,
    `priority`     bigint(20)    DEFAULT NULL,
    `callback_url` varchar(1024) DEFAULT NULL,
    `status`       bigint(20)          NOT NULL,
    `start_time`   datetime(6)   DEFAULT NULL,
    `end_time`     datetime(6)   DEFAULT NULL,
    `created_at`   datetime(6)   DEFAULT NULL,
    `updated_at`   datetime(6)   DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code` (`code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 299
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment_transaction`
--

DROP TABLE IF EXISTS `payment_transaction`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_transaction`
(
    `sn`                   bigint(20)   NOT NULL AUTO_INCREMENT,
    `id`                   varchar(64)  NOT NULL,
    `is_snap`              tinyint(1)   NOT NULL,
    `payment_type`         tinyint(2)   NOT NULL,
    `payment_type_desc`    varchar(64)  NOT NULL,
    `payment_id`           varchar(64)  NOT NULL COMMENT 'payment唯一标识',
    `uid`                  bigint(20)   NOT NULL,
    `entry_id`             varchar(100) NOT NULL,
    `entry_type`           tinyint(1)            DEFAULT NULL,
    `entry_type_desc`      varchar(64)           DEFAULT NULL,
    `entry_desc`           varchar(255)          DEFAULT NULL,
    `payment_amount`       bigint(20)   NOT NULL,
    `payment_remained`     bigint(20)   NOT NULL,
    `payment_paid`         bigint(20)   NOT NULL,
    `payment_status`       tinyint(1)   NOT NULL,
    `currency_type`        varchar(10)  NOT NULL,
    `created_at`           datetime(6)           DEFAULT NULL,
    `updated_at`           datetime(6)           DEFAULT NULL,
    `record_status`        tinyint(1)   NOT NULL DEFAULT '1',
    `description`          varchar(2000)         DEFAULT NULL,
    `snap_id`              varchar(64)           DEFAULT NULL,
    `excode`               varchar(64)           DEFAULT NULL,
    `prefix`               varchar(64)           DEFAULT NULL,
    `type`                 varchar(64)           DEFAULT NULL,
    `details`              varchar(1000)         DEFAULT NULL,
    `order_hash`           varchar(64)           DEFAULT NULL COMMENT 'order_hash',
    `related_month`        datetime(6)           DEFAULT NULL,
    `product`              varchar(64)           DEFAULT NULL,
    `group`                varchar(64)           DEFAULT NULL,
    `item`                 varchar(64)           DEFAULT NULL,
    `reward_cost`          bigint(20)            DEFAULT NULL,
    `reward_type`          tinyint(1)            DEFAULT NULL,
    `reward_id`            varchar(255)          DEFAULT NULL,
    `trans_direction`      tinyint(1)   NOT NULL,
    `trans_direction_desc` varchar(64)  NOT NULL,
    `money`                bigint(20)            DEFAULT NULL,
    `left`                 bigint(20)            DEFAULT NULL,
    `cash`                 bigint(20)            DEFAULT NULL,
    `coupon`               bigint(20)            DEFAULT NULL,
    `free_nb`              bigint(20)            DEFAULT NULL,
    `is_hide`              tinyint(1)            DEFAULT '0',
    `only_cash`            tinyint(1)            DEFAULT '0',
    `no_free_nb`           tinyint(1)            DEFAULT '0',
    `business_at`          datetime(6)           DEFAULT NULL,
    `ver`                  int(8)                DEFAULT NULL,
    `legacy`               tinyint(1)   NOT NULL DEFAULT '0',
    `pay_uid`              bigint(20)   NOT NULL,
    `remark`               varchar(64)           DEFAULT NULL,
    PRIMARY KEY (`sn`),
    UNIQUE KEY `uidx_entry_id` (`entry_id`),
    KEY `idx_updated_at` (`updated_at`),
    KEY `idx_uid` (`uid`),
    KEY `idx_payment_id` (`payment_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment_transaction_history`
--

DROP TABLE IF EXISTS `payment_transaction_history`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_transaction_history`
(
    `sn`                   bigint(20)   NOT NULL DEFAULT '0',
    `id`                   varchar(64)  NOT NULL,
    `is_snap`              tinyint(1)   NOT NULL,
    `payment_type`         tinyint(2)   NOT NULL,
    `payment_type_desc`    varchar(64)  NOT NULL,
    `payment_id`           varchar(64)  NOT NULL COMMENT 'payment唯一标识',
    `uid`                  bigint(20)   NOT NULL,
    `entry_id`             varchar(100) NOT NULL,
    `entry_type`           tinyint(1)            DEFAULT NULL,
    `entry_type_desc`      varchar(64)           DEFAULT NULL,
    `entry_desc`           varchar(255)          DEFAULT NULL,
    `payment_amount`       bigint(20)   NOT NULL,
    `payment_remained`     bigint(20)   NOT NULL,
    `payment_paid`         bigint(20)   NOT NULL,
    `payment_status`       tinyint(1)   NOT NULL,
    `currency_type`        varchar(10)  NOT NULL,
    `created_at`           datetime(6)           DEFAULT NULL,
    `updated_at`           datetime(6)           DEFAULT NULL,
    `record_status`        tinyint(1)   NOT NULL DEFAULT '1',
    `description`          varchar(2000)         DEFAULT NULL,
    `snap_id`              varchar(64)           DEFAULT NULL,
    `excode`               varchar(64)           DEFAULT NULL,
    `prefix`               varchar(64)           DEFAULT NULL,
    `type`                 varchar(64)           DEFAULT NULL,
    `details`              varchar(1000)         DEFAULT NULL,
    `order_hash`           varchar(64)           DEFAULT NULL COMMENT 'order_hash',
    `related_month`        datetime(6)           DEFAULT NULL,
    `product`              varchar(64)           DEFAULT NULL,
    `group`                varchar(64)           DEFAULT NULL,
    `item`                 varchar(64)           DEFAULT NULL,
    `reward_cost`          bigint(20)            DEFAULT NULL,
    `reward_type`          tinyint(1)            DEFAULT NULL,
    `reward_id`            varchar(255)          DEFAULT NULL,
    `trans_direction`      tinyint(1)   NOT NULL,
    `trans_direction_desc` varchar(64)  NOT NULL,
    `money`                bigint(20)            DEFAULT NULL,
    `left`                 bigint(20)            DEFAULT NULL,
    `cash`                 bigint(20)            DEFAULT NULL,
    `coupon`               bigint(20)            DEFAULT NULL,
    `free_nb`              bigint(20)            DEFAULT NULL,
    `is_hide`              tinyint(1)            DEFAULT '0',
    `only_cash`            tinyint(1)            DEFAULT '0',
    `no_free_nb`           tinyint(1)            DEFAULT '0',
    `business_at`          datetime(6)           DEFAULT NULL,
    `ver`                  int(8)                DEFAULT NULL,
    `legacy`               tinyint(1)   NOT NULL DEFAULT '0',
    `pay_uid`              bigint(20)   NOT NULL,
    `remark`               varchar(64)           DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment_transaction_history_x`
--

DROP TABLE IF EXISTS `payment_transaction_history_x`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_transaction_history_x`
(
    `sn`                   bigint(20)   NOT NULL AUTO_INCREMENT,
    `id`                   varchar(64)  NOT NULL,
    `is_snap`              tinyint(1)   NOT NULL,
    `payment_type`         tinyint(2)   NOT NULL,
    `payment_type_desc`    varchar(64)  NOT NULL,
    `payment_id`           varchar(64)  NOT NULL COMMENT 'payment唯一标识',
    `uid`                  bigint(20)   NOT NULL,
    `entry_id`             varchar(100) NOT NULL,
    `entry_type`           tinyint(1)            DEFAULT NULL,
    `entry_type_desc`      varchar(64)           DEFAULT NULL,
    `entry_desc`           varchar(255)          DEFAULT NULL,
    `payment_amount`       bigint(20)   NOT NULL,
    `payment_remained`     bigint(20)   NOT NULL,
    `payment_paid`         bigint(20)   NOT NULL,
    `payment_status`       tinyint(1)   NOT NULL,
    `currency_type`        varchar(10)  NOT NULL,
    `created_at`           datetime(6)           DEFAULT NULL,
    `updated_at`           datetime(6)           DEFAULT NULL,
    `record_status`        tinyint(1)   NOT NULL DEFAULT '1',
    `description`          varchar(2000)         DEFAULT NULL,
    `snap_id`              varchar(64)           DEFAULT NULL,
    `excode`               varchar(64)           DEFAULT NULL,
    `prefix`               varchar(64)           DEFAULT NULL,
    `type`                 varchar(64)           DEFAULT NULL,
    `details`              varchar(1000)         DEFAULT NULL,
    `order_hash`           varchar(64)           DEFAULT NULL COMMENT 'order_hash',
    `related_month`        datetime(6)           DEFAULT NULL,
    `product`              varchar(64)           DEFAULT NULL,
    `group`                varchar(64)           DEFAULT NULL,
    `item`                 varchar(64)           DEFAULT NULL,
    `reward_cost`          bigint(20)            DEFAULT NULL,
    `reward_type`          tinyint(1)            DEFAULT NULL,
    `reward_id`            varchar(255)          DEFAULT NULL,
    `trans_direction`      tinyint(1)   NOT NULL,
    `trans_direction_desc` varchar(64)  NOT NULL,
    `money`                bigint(20)            DEFAULT NULL,
    `left`                 bigint(20)            DEFAULT NULL,
    `cash`                 bigint(20)            DEFAULT NULL,
    `coupon`               bigint(20)            DEFAULT NULL,
    `free_nb`              bigint(20)            DEFAULT NULL,
    `is_hide`              tinyint(1)            DEFAULT '0',
    `only_cash`            tinyint(1)            DEFAULT '0',
    `no_free_nb`           tinyint(1)            DEFAULT '0',
    `business_at`          datetime(6)           DEFAULT NULL,
    `ver`                  int(8)                DEFAULT NULL,
    `legacy`               tinyint(1)   NOT NULL DEFAULT '0',
    `pay_uid`              bigint(20)   NOT NULL,
    `remark`               varchar(64)           DEFAULT NULL,
    PRIMARY KEY (`sn`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4096
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `scope_item_group_map`
--

DROP TABLE IF EXISTS `scope_item_group_map`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `scope_item_group_map`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `scope_id`    bigint(20) unsigned DEFAULT NULL,
    `group_id`    bigint(20) unsigned DEFAULT NULL,
    `is_excluded` tinyint(1)          DEFAULT NULL,
    `created_at`  datetime(6)         DEFAULT NULL,
    `updated_at`  datetime(6)         DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_scope_item_group_map_scope_id_group_id` (`scope_id`, `group_id`),
    KEY `idx_scope_item_group_map_created_at` (`created_at`),
    KEY `idx_scope_item_group_map_updated_at` (`updated_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 50
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `scope_item_map`
--

DROP TABLE IF EXISTS `scope_item_map`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `scope_item_map`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `scope_id`    bigint(20) unsigned DEFAULT NULL,
    `item_id`     bigint(20) unsigned DEFAULT NULL,
    `is_excluded` tinyint(1)          DEFAULT NULL,
    `created_at`  datetime(6)         DEFAULT NULL,
    `updated_at`  datetime(6)         DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_scope_item_map_scope_id_item_id` (`scope_id`, `item_id`),
    KEY `idx_scope_item_map_created_at` (`created_at`),
    KEY `idx_scope_item_map_updated_at` (`updated_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 343
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `scope_product_map`
--

DROP TABLE IF EXISTS `scope_product_map`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `scope_product_map`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `scope_id`    bigint(20) unsigned DEFAULT NULL,
    `product_id`  bigint(20) unsigned DEFAULT NULL,
    `is_excluded` tinyint(1)          DEFAULT NULL,
    `created_at`  datetime(6)         DEFAULT NULL,
    `updated_at`  datetime(6)         DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_scope_product_map_scope_id_product_id` (`scope_id`, `product_id`),
    KEY `idx_scope_product_map_created_at` (`created_at`),
    KEY `idx_scope_product_map_updated_at` (`updated_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 107
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `scopes`
--

DROP TABLE IF EXISTS `scopes`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `scopes`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `is_all`     tinyint(1)                              DEFAULT NULL,
    `remark`     varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `created_at` datetime(6)                             DEFAULT NULL,
    `updated_at` datetime(6)                             DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_scope_created_at` (`created_at`),
    KEY `idx_scope_updated_at` (`updated_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 662
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_contexts`
--

DROP TABLE IF EXISTS `task_contexts`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `task_contexts`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `task_id`    bigint(20) unsigned NOT NULL,
    `ctx`        blob,
    `created_at` datetime(6) DEFAULT NULL,
    `updated_at` datetime(6) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_taskid` (`task_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2403816
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tasks`
--

DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tasks`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `code`       varchar(64)         NOT NULL,
    `job_code`   varchar(64)         NOT NULL,
    `status`     bigint(20)          NOT NULL,
    `error`      text,
    `created_at` datetime(6) DEFAULT NULL,
    `updated_at` datetime(6) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code` (`code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2403892
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users`
(
    `id`         int(10) unsigned NOT NULL AUTO_INCREMENT,
    `created_at` timestamp        NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp        NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` timestamp        NULL DEFAULT NULL,
    `name`       varchar(255)          DEFAULT NULL,
    `create`     timestamp        NULL DEFAULT CURRENT_TIMESTAMP,
    `update`     timestamp        NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_users_deleted_at` (`deleted_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet`
--

DROP TABLE IF EXISTS `wallet`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet`
(
    `sn`               bigint(20)   NOT NULL AUTO_INCREMENT,
    `id`               varchar(64)  NOT NULL,
    `uid`              bigint(20)   NOT NULL,
    `asset_id`         varchar(64)  NOT NULL,
    `asset_type`       tinyint(1)   NOT NULL,
    `asset_type_desc`  varchar(64)  NOT NULL,
    `amount`           bigint(20)   NOT NULL,
    `balance`          bigint(20)   NOT NULL,
    `record_status`    tinyint(1)   NOT NULL DEFAULT '1',
    `description`      varchar(255) NOT NULL,
    `remark`           varchar(255) NOT NULL,
    `created_at`       datetime(6)  NOT NULL,
    `updated_at`       datetime(6)  NOT NULL,
    `related_trans_id` varchar(64)           DEFAULT NULL,
    `snap_id`          varchar(100)          DEFAULT NULL,
    `ver`              bigint(20)            DEFAULT NULL,
    `legacy`           tinyint(1)   NOT NULL DEFAULT '0',
    `status`           tinyint(1)   NOT NULL DEFAULT '1',
    `assigned_id`      varchar(64)           DEFAULT NULL,
    PRIMARY KEY (`sn`),
    UNIQUE KEY `uidx_asset_id` (`asset_id`),
    KEY `idx_updated_at` (`updated_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 46422
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_history`
--

DROP TABLE IF EXISTS `wallet_history`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_history`
(
    `sn`               bigint(20)   NOT NULL DEFAULT '0',
    `id`               varchar(64)  NOT NULL,
    `uid`              bigint(20)   NOT NULL,
    `asset_id`         varchar(64)  NOT NULL,
    `asset_type`       tinyint(1)   NOT NULL,
    `asset_type_desc`  varchar(64)  NOT NULL,
    `amount`           bigint(20)   NOT NULL,
    `balance`          bigint(20)   NOT NULL,
    `record_status`    tinyint(1)   NOT NULL DEFAULT '1',
    `description`      varchar(255) NOT NULL,
    `remark`           varchar(255) NOT NULL,
    `created_at`       datetime(6)  NOT NULL,
    `updated_at`       datetime(6)  NOT NULL,
    `related_trans_id` varchar(64)           DEFAULT NULL,
    `snap_id`          varchar(100)          DEFAULT NULL,
    `ver`              bigint(20)            DEFAULT NULL,
    `legacy`           tinyint(1)   NOT NULL DEFAULT '0',
    `status`           tinyint(1)   NOT NULL DEFAULT '1',
    `assigned_id`      varchar(64)           DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_request`
--

DROP TABLE IF EXISTS `wallet_request`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_request`
(
    `sn`               bigint(20)  NOT NULL AUTO_INCREMENT,
    `id`               varchar(64) NOT NULL,
    `payment_id`       varchar(64) NOT NULL,
    `uid`              bigint(20)  NOT NULL,
    `money`            bigint(20)  NOT NULL,
    `trans_direction`  tinyint(1)  NOT NULL,
    `trans_type`       tinyint(1)  NOT NULL,
    `asset_id`         varchar(64)          DEFAULT NULL,
    `asset_type`       varchar(64) NOT NULL,
    `snap_id`          varchar(100)         DEFAULT NULL,
    `entry_id`         varchar(100)         DEFAULT NULL,
    `entry_desc`       varchar(255)         DEFAULT NULL,
    `entry_type`       tinyint(1)           DEFAULT NULL,
    `is_reverse`       tinyint(1)           DEFAULT NULL,
    `reverse_trans_id` varchar(64)          DEFAULT NULL,
    `description`      varchar(255)         DEFAULT NULL,
    `remark`           varchar(255)         DEFAULT NULL,
    `created_at`       datetime(6)          DEFAULT NULL,
    `updated_at`       datetime(6)          DEFAULT NULL,
    `status`           tinyint(1)  NOT NULL DEFAULT '1',
    `record_status`    tinyint(1)  NOT NULL DEFAULT '1',
    PRIMARY KEY (`sn`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 89762
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_request_snap`
--

DROP TABLE IF EXISTS `wallet_request_snap`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_request_snap`
(
    `sn`               bigint(20)  NOT NULL AUTO_INCREMENT,
    `id`               varchar(64) NOT NULL,
    `payment_id`       varchar(64) NOT NULL,
    `uid`              bigint(20)  NOT NULL,
    `money`            bigint(20)  NOT NULL,
    `trans_direction`  tinyint(1)  NOT NULL,
    `trans_type`       tinyint(1)  NOT NULL,
    `asset_id`         varchar(64)          DEFAULT NULL,
    `asset_type`       varchar(64) NOT NULL,
    `snap_id`          varchar(100)         DEFAULT NULL,
    `entry_id`         varchar(100)         DEFAULT NULL,
    `entry_desc`       varchar(255)         DEFAULT NULL,
    `entry_type`       tinyint(1)           DEFAULT NULL,
    `is_reverse`       tinyint(1)           DEFAULT NULL,
    `reverse_trans_id` varchar(64)          DEFAULT NULL,
    `description`      varchar(255)         DEFAULT NULL,
    `remark`           varchar(255)         DEFAULT NULL,
    `created_at`       datetime(6)          DEFAULT NULL,
    `updated_at`       datetime(6)          DEFAULT NULL,
    `status`           tinyint(1)  NOT NULL DEFAULT '1',
    `record_status`    tinyint(1)  NOT NULL DEFAULT '1',
    PRIMARY KEY (`sn`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_snap`
--

DROP TABLE IF EXISTS `wallet_snap`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_snap`
(
    `snap_at`          datetime(6)  NOT NULL,
    `sn`               bigint(20)   NOT NULL AUTO_INCREMENT,
    `id`               varchar(64)  NOT NULL,
    `uid`              bigint(20)   NOT NULL,
    `asset_id`         varchar(64)  NOT NULL,
    `asset_type`       tinyint(1)   NOT NULL,
    `asset_type_desc`  varchar(64)  NOT NULL,
    `amount`           bigint(20)   NOT NULL,
    `balance`          bigint(20)   NOT NULL,
    `record_status`    tinyint(1)   NOT NULL DEFAULT '1',
    `description`      varchar(255) NOT NULL,
    `remark`           varchar(255) NOT NULL,
    `created_at`       datetime(6)  NOT NULL,
    `updated_at`       datetime(6)  NOT NULL,
    `related_trans_id` varchar(64)           DEFAULT NULL,
    `snap_id`          varchar(100)          DEFAULT NULL,
    `ver`              bigint(20)            DEFAULT NULL,
    `legacy`           tinyint(1)   NOT NULL DEFAULT '0',
    PRIMARY KEY (`sn`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_transaction`
--

DROP TABLE IF EXISTS `wallet_transaction`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_transaction`
(
    `sn`                   bigint(20)   NOT NULL AUTO_INCREMENT,
    `id`                   varchar(64)  NOT NULL,
    `payment_id`           varchar(64)           DEFAULT NULL,
    `wallet_id`            varchar(64)           DEFAULT NULL,
    `asset_id`             varchar(64)           DEFAULT NULL,
    `asset_type`           tinyint(1)   NOT NULL,
    `asset_type_desc`      varchar(64)  NOT NULL,
    `trans_type`           tinyint(1)   NOT NULL,
    `trans_type_desc`      varchar(64)  NOT NULL,
    `wallet_request_id`    varchar(64)           DEFAULT NULL,
    `money`                bigint(20)            DEFAULT NULL,
    `uid`                  bigint(20)   NOT NULL,
    `entry_id`             varchar(100) NOT NULL,
    `entry_type`           tinyint(2)   NOT NULL,
    `entry_desc`           varchar(255)          DEFAULT NULL,
    `trans_direction`      tinyint(1)   NOT NULL,
    `trans_direction_desc` varchar(64)  NOT NULL,
    `created_at`           datetime(6)  NOT NULL,
    `updated_at`           datetime(6)  NOT NULL,
    `record_status`        tinyint(1)   NOT NULL DEFAULT '1',
    `reverse_status`       tinyint(1)   NOT NULL DEFAULT '1',
    `reverse_trans_id`     varchar(64)           DEFAULT NULL,
    `description`          varchar(255)          DEFAULT NULL,
    `remark`               varchar(255)          DEFAULT NULL,
    `snap_id`              varchar(100) NOT NULL,
    `pay_uid`              bigint(20)   NOT NULL,
    `pay_tool`             varchar(255)          DEFAULT NULL,
    `pay_tool_id`          varchar(255)          DEFAULT NULL,
    `before`               bigint(20)            DEFAULT NULL,
    `change`               bigint(20)            DEFAULT NULL,
    `after`                bigint(20)            DEFAULT NULL,
    `left`                 bigint(20)            DEFAULT NULL,
    `tr_time`              datetime(6)           DEFAULT NULL,
    `business_at`          datetime(6)           DEFAULT NULL,
    `related_month`        datetime(6)           DEFAULT NULL,
    `details`              varchar(255)          DEFAULT NULL,
    `ver`                  int(8)                DEFAULT NULL,
    `excode`               varchar(64)           DEFAULT NULL,
    `prefix`               varchar(64)           DEFAULT NULL,
    `type`                 varchar(64)           DEFAULT NULL,
    `legacy`               tinyint(1)   NOT NULL DEFAULT '0',
    PRIMARY KEY (`sn`),
    KEY `idx_updated_at` (`updated_at`),
    KEY `idx_entry_id` (`entry_id`),
    KEY `idx_wallet_request_id` (`wallet_request_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 91528
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_transaction_snap`
--

DROP TABLE IF EXISTS `wallet_transaction_snap`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_transaction_snap`
(
    `sn`                   bigint(20)   NOT NULL AUTO_INCREMENT,
    `id`                   varchar(64)  NOT NULL,
    `payment_id`           varchar(64)           DEFAULT NULL,
    `wallet_id`            varchar(64)           DEFAULT NULL,
    `asset_id`             varchar(64)           DEFAULT NULL,
    `asset_type`           tinyint(1)   NOT NULL,
    `asset_type_desc`      varchar(64)  NOT NULL,
    `trans_type`           tinyint(1)   NOT NULL,
    `trans_type_desc`      varchar(64)  NOT NULL,
    `wallet_request_id`    varchar(64)           DEFAULT NULL,
    `money`                bigint(20)            DEFAULT NULL,
    `uid`                  bigint(20)   NOT NULL,
    `entry_id`             varchar(100) NOT NULL,
    `entry_type`           tinyint(2)   NOT NULL,
    `entry_desc`           varchar(255)          DEFAULT NULL,
    `trans_direction`      tinyint(1)   NOT NULL,
    `trans_direction_desc` varchar(64)  NOT NULL,
    `created_at`           datetime(6)  NOT NULL,
    `updated_at`           datetime(6)  NOT NULL,
    `record_status`        tinyint(1)   NOT NULL DEFAULT '1',
    `reverse_status`       tinyint(1)   NOT NULL DEFAULT '1',
    `reverse_trans_id`     varchar(64)           DEFAULT NULL,
    `description`          varchar(255)          DEFAULT NULL,
    `remark`               varchar(255)          DEFAULT NULL,
    `snap_id`              varchar(100) NOT NULL,
    `pay_uid`              bigint(20)   NOT NULL,
    `pay_tool`             varchar(255)          DEFAULT NULL,
    `pay_tool_id`          varchar(255)          DEFAULT NULL,
    `before`               bigint(20)            DEFAULT NULL,
    `change`               bigint(20)            DEFAULT NULL,
    `after`                bigint(20)            DEFAULT NULL,
    `left`                 bigint(20)            DEFAULT NULL,
    `tr_time`              datetime(6)           DEFAULT NULL,
    `business_at`          datetime(6)           DEFAULT NULL,
    `related_month`        datetime(6)           DEFAULT NULL,
    `details`              varchar(255)          DEFAULT NULL,
    `ver`                  int(8)                DEFAULT NULL,
    `excode`               varchar(64)           DEFAULT NULL,
    `prefix`               varchar(64)           DEFAULT NULL,
    `type`                 varchar(64)           DEFAULT NULL,
    `legacy`               tinyint(1)   NOT NULL DEFAULT '0',
    PRIMARY KEY (`sn`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_uid`
--

DROP TABLE IF EXISTS `wallet_uid`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_uid`
(
    `id`         int(10) unsigned NOT NULL AUTO_INCREMENT,
    `created_at` timestamp        NULL DEFAULT NULL,
    `updated_at` timestamp        NULL DEFAULT NULL,
    `deleted_at` timestamp        NULL DEFAULT NULL,
    `uid`        bigint(20)            DEFAULT NULL,
    `status`     tinyint(1)            DEFAULT '0',
    `ex`         tinyint(1)            DEFAULT '0',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uidx_uid` (`uid`),
    KEY `idx_wallet_uid_deleted_at` (`deleted_at`),
    KEY `idx_uid` (`uid`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 13307
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_voucher`
--

DROP TABLE IF EXISTS `wallet_voucher`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_voucher`
(
    `sn`                   bigint(20)    NOT NULL AUTO_INCREMENT,
    `id`                   varchar(64)   NOT NULL,
    `sub_system`           varchar(64)            DEFAULT NULL,
    `quota`                bigint(20)             DEFAULT NULL,
    `balance`              bigint(20)             DEFAULT NULL,
    `effect_time`          datetime(6)            DEFAULT NULL,
    `dead_time`            datetime(6)            DEFAULT NULL,
    `expired_time`         datetime(6)            DEFAULT NULL,
    `code`                 varchar(64)            DEFAULT NULL,
    `uid`                  bigint(20)    NOT NULL,
    `day`                  bigint(20)             DEFAULT NULL,
    `title`                varchar(64)            DEFAULT NULL,
    `description`          varchar(2000)          DEFAULT NULL,
    `remark`               varchar(2048) NOT NULL DEFAULT '',
    `voucher_type`         varchar(64)            DEFAULT NULL,
    `status`               tinyint(1)             DEFAULT NULL,
    `status_desc`          varchar(64)            DEFAULT NULL,
    `version`              int(10)                DEFAULT NULL,
    `related_trans_id`     varchar(64)            DEFAULT NULL,
    `asset_id`             varchar(64)   NOT NULL,
    `asset_type`           tinyint(1)    NOT NULL,
    `asset_type_desc`      varchar(64)   NOT NULL,
    `created_at`           datetime(6)            DEFAULT NULL,
    `updated_at`           datetime(6)            DEFAULT NULL,
    `arrearage_can_use`    tinyint(1)    NOT NULL DEFAULT '0',
    `max_activation_times` int(10)                DEFAULT NULL,
    `batch_id`             varchar(50)            DEFAULT NULL,
    `record_status`        tinyint(1)    NOT NULL DEFAULT '1',
    `snap_id`              varchar(100)           DEFAULT NULL,
    `is_all`               tinyint(1)             DEFAULT NULL,
    `legacy`               tinyint(1)    NOT NULL DEFAULT '0',
    `scope_id`             bigint(20)             DEFAULT NULL,
    PRIMARY KEY (`sn`),
    UNIQUE KEY `uidx_asset_id` (`asset_id`),
    KEY `idx_updated_at` (`updated_at`),
    KEY `idx_uid` (`uid`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 23766
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_voucher_history`
--

DROP TABLE IF EXISTS `wallet_voucher_history`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_voucher_history`
(
    `sn`                   bigint(20)    NOT NULL DEFAULT '0',
    `id`                   varchar(64)   NOT NULL,
    `sub_system`           varchar(64)            DEFAULT NULL,
    `quota`                bigint(20)             DEFAULT NULL,
    `balance`              bigint(20)             DEFAULT NULL,
    `effect_time`          datetime(6)            DEFAULT NULL,
    `dead_time`            datetime(6)            DEFAULT NULL,
    `expired_time`         datetime(6)            DEFAULT NULL,
    `code`                 varchar(64)            DEFAULT NULL,
    `uid`                  bigint(20)    NOT NULL,
    `day`                  bigint(20)             DEFAULT NULL,
    `title`                varchar(64)            DEFAULT NULL,
    `description`          varchar(2000)          DEFAULT NULL,
    `remark`               varchar(2048) NOT NULL DEFAULT '',
    `voucher_type`         varchar(64)            DEFAULT NULL,
    `status`               tinyint(1)             DEFAULT NULL,
    `status_desc`          varchar(64)            DEFAULT NULL,
    `version`              int(10)                DEFAULT NULL,
    `related_trans_id`     varchar(64)            DEFAULT NULL,
    `asset_id`             varchar(64)   NOT NULL,
    `asset_type`           tinyint(1)    NOT NULL,
    `asset_type_desc`      varchar(64)   NOT NULL,
    `created_at`           datetime(6)            DEFAULT NULL,
    `updated_at`           datetime(6)            DEFAULT NULL,
    `arrearage_can_use`    tinyint(1)    NOT NULL DEFAULT '0',
    `max_activation_times` int(10)                DEFAULT NULL,
    `batch_id`             varchar(50)            DEFAULT NULL,
    `record_status`        tinyint(1)    NOT NULL DEFAULT '1',
    `snap_id`              varchar(100)           DEFAULT NULL,
    `is_all`               tinyint(1)             DEFAULT NULL,
    `legacy`               tinyint(1)    NOT NULL DEFAULT '0',
    `scope_id`             bigint(20)             DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_voucher_scope`
--

DROP TABLE IF EXISTS `wallet_voucher_scope`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_voucher_scope`
(
    `sn`               bigint(20)  NOT NULL AUTO_INCREMENT,
    `id`               varchar(64) NOT NULL,
    `uid`              bigint(20)  NOT NULL,
    `asset_id`         varchar(64) NOT NULL,
    `product`          varchar(64)          DEFAULT NULL,
    `product_selected` tinyint(1)           DEFAULT NULL,
    `group`            varchar(64)          DEFAULT NULL,
    `group_selected`   tinyint(1)           DEFAULT NULL,
    `item`             varchar(64)          DEFAULT NULL,
    `item_selected`    tinyint(1)           DEFAULT NULL,
    `created_at`       datetime(6)          DEFAULT NULL,
    `updated_at`       datetime(6)          DEFAULT NULL,
    `record_status`    tinyint(1)  NOT NULL DEFAULT '0',
    `legacy`           tinyint(1)  NOT NULL DEFAULT '0',
    PRIMARY KEY (`sn`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5897
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wallet_voucher_snap`
--

DROP TABLE IF EXISTS `wallet_voucher_snap`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_voucher_snap`
(
    `snap_at`              timestamp   NULL     DEFAULT NULL,
    `sn`                   bigint(20)  NOT NULL AUTO_INCREMENT,
    `id`                   varchar(64) NOT NULL,
    `sub_system`           varchar(64)          DEFAULT NULL,
    `quota`                bigint(20)           DEFAULT NULL,
    `balance`              bigint(20)           DEFAULT NULL,
    `effect_time`          datetime(6)          DEFAULT NULL,
    `dead_time`            datetime(6)          DEFAULT NULL,
    `expired_time`         datetime(6)          DEFAULT NULL,
    `code`                 varchar(64)          DEFAULT NULL,
    `uid`                  bigint(20)  NOT NULL,
    `day`                  bigint(20)           DEFAULT NULL,
    `title`                varchar(64)          DEFAULT NULL,
    `description`          varchar(2000)        DEFAULT NULL,
    `voucher_type`         varchar(64)          DEFAULT NULL,
    `status`               tinyint(1)           DEFAULT NULL,
    `status_desc`          varchar(64)          DEFAULT NULL,
    `version`              int(10)              DEFAULT NULL,
    `related_trans_id`     varchar(64)          DEFAULT NULL,
    `asset_id`             varchar(64) NOT NULL,
    `asset_type`           tinyint(1)  NOT NULL,
    `asset_type_desc`      varchar(64) NOT NULL,
    `created_at`           datetime(6)          DEFAULT NULL,
    `updated_at`           datetime(6)          DEFAULT NULL,
    `arrearage_can_use`    tinyint(1)  NOT NULL DEFAULT '0',
    `max_activation_times` int(10)              DEFAULT NULL,
    `batch_id`             varchar(50)          DEFAULT NULL,
    `record_status`        tinyint(1)  NOT NULL DEFAULT '1',
    `snap_id`              varchar(100)         DEFAULT NULL,
    `is_all`               tinyint(1)           DEFAULT NULL,
    `legacy`               tinyint(1)  NOT NULL DEFAULT '0',
    `scope_id`             bigint(20)           DEFAULT NULL,
    PRIMARY KEY (`sn`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE = @OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE = @OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS = @OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS = @OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT = @OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS = @OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION = @OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES = @OLD_SQL_NOTES */;

-- Dump completed on 2020-07-06  9:44:57


CREATE TABLE `user_balance_snaps`
(
    `sn`                bigint(20)                               NOT NULL AUTO_INCREMENT,
    `id`                varchar(64) COLLATE utf8mb4_unicode_ci   NOT NULL DEFAULT '',
    `uid`               bigint(20)                               NOT NULL DEFAULT '0',
    `cash_balance`      bigint(20)                               NOT NULL DEFAULT '0',
    `niu_coin_balance`  bigint(20)                               NOT NULL DEFAULT '0',
    `arrears`           bigint(20)                               NOT NULL DEFAULT '0',
    `available_balance` bigint(20)                               NOT NULL DEFAULT '0' COMMENT '可用余额，为 cash_balance + niu_coin_balance + arrears',
    `description`       varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
    `payment_id`        varchar(64) COLLATE utf8mb4_unicode_ci   NOT NULL DEFAULT '',
    `created_at`        datetime(6)                              NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    `updated_at`        datetime(6)                              NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    PRIMARY KEY (`sn`),
    KEY `user_balance_snaps_payment_id_index` (`payment_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `user_payment_seqs`
(
    `sn`                     bigint(20)  NOT NULL AUTO_INCREMENT,
    `uid`                    bigint(20)  NOT NULL DEFAULT 0,
    `seq_type`               tinyint(2)  NOT NULL DEFAULT 0 COMMENT '支付顺序类型 1.按流水时间 2.按月',
    `config_at`              datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000' COMMENT '设置时间',
    `operator`               varchar(64) NOT NULL DEFAULT '' COMMENT '操作人',
    `should_immediately_pay` tinyint(1)  NOT NULL DEFAULT 1 COMMENT '是否应该立即支付；对于扣费顺序类型为【按月支付】的账号产生的订单扣费流水，触发支付时是否立即支付，而非等到下月；仅当扣费类型为【按月支付】时该字段才有意义',
    `created_at`             datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    `updated_at`             datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    PRIMARY KEY (`sn`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `user_payment_seq_histories`
(
    `sn`                     bigint(20)  NOT NULL AUTO_INCREMENT,
    `uid`                    bigint(20)  NOT NULL DEFAULT 0,
    `seq_type`               tinyint(2)  NOT NULL DEFAULT 0 COMMENT '支付顺序类型 1.按流水时间 2.按月',
    `config_at`              datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000' COMMENT '设置时间',
    `operator`               varchar(64) NOT NULL DEFAULT '' COMMENT '操作人',
    `should_immediately_pay` tinyint(1)  NOT NULL DEFAULT 1 COMMENT '是否应该立即支付；对于扣费顺序类型为【按月支付】的账号产生的订单扣费流水，触发支付时是否立即支付，而非等到下月；仅当扣费类型为【按月支付】时该字段才有意义',
    `created_at`             datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    `updated_at`             datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    PRIMARY KEY (`sn`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `related_wallet_assets`
(
    `sn`                 bigint(20)  NOT NULL AUTO_INCREMENT,
    `uid`                bigint(20)  NOT NULL DEFAULT 0,
    `asset_id`           varchar(64) NOT NULL DEFAULT '' COMMENT '资产 id',
    `payment_id`         varchar(64) NOT NULL DEFAULT '' COMMENT '生成资产的流水 id',
    `related_asset_id`   varchar(64) NOT NULL DEFAULT '' COMMENT '对应关联资产 id',
    `related_payment_id` varchar(64) NOT NULL DEFAULT '' COMMENT '对应关联资产的生成流水 id',
    `relation_type`      tinyint(1)  NOT NULL DEFAULT 0 COMMENT '1.牛币关联现金 2.转入关联转出',
    `created_at`         datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    `updated_at`         datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    PRIMARY KEY (`sn`),
    KEY `idx_asset_id` (`asset_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `user_bank_virtual_accounts`
(
    `id`                   bigint(20) unsigned                     NOT NULL AUTO_INCREMENT,
    `uid`                  bigint(20) unsigned                     NOT NULL DEFAULT '0',
    `bank_virtual_account` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '该用户在银行虚拟专属账号',
    `is_deleted`           tinyint(1)                              NOT NULL DEFAULT 0,
    `created_at`           datetime(6)                             NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    `updated_at`           datetime(6)                             NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
    PRIMARY KEY (`id`),
    KEY `user_bank_virt_acc_bank_virtual_account_idx` (`bank_virtual_account`),
    KEY `user_bank_virt_acc_uid_idx` (`uid`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `currency`
(
    `sn`            bigint(20)                             NOT NULL AUTO_INCREMENT,
    `uid`           bigint(20)                             NOT NULL,
    `currency_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
    `created_at`    datetime(6)                            NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `is_default`    tinyint(1)                             NOT NULL,
    `updated_at`    datetime(6)                            NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    PRIMARY KEY (`sn`),
    UNIQUE KEY `idx_uid_currency_type` (`uid`, `currency_type`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `user_card`
(
    `id`            int(11)     NOT NULL AUTO_INCREMENT,
    `uid`           bigint(20)  NOT NULL DEFAULT 0,
    `last_digits`   char(4)     NOT NULL COMMENT '卡号最后四位',
    `brand`         varchar(32) NOT NULL DEFAULT '' COMMENT '发卡机构',
    `card_type`     int(11)     NOT NULL DEFAULT 0 COMMENT '卡类型: 0:Unknown 1:Credit 2.Debit',
    `status`        int(11)     NOT NULL DEFAULT 0 COMMENT '卡状态: 0:Unknown 1:Valid 2.Invalid',
    `payment_token` varchar(32) NOT NULL DEFAULT '',
    `is_default`    tinyint(4)  NOT NULL DEFAULT 0 COMMENT '默认支付方式',
    `is_deleted`    tinyint(4)  NOT NULL DEFAULT 0,
    `created_at`    datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`    datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `wallet_card_uid_is_default_index` (`uid`, `is_default`),
    KEY `wallet_card_uid_is_deleted_index` (`uid`, `is_deleted`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户信用卡';

CREATE TABLE `bank_transfers`
(
    `id`                  BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `parent_sn`           VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '关联记录SN',
    `sn`                  VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '唯一编号',
    `payment_account`     VARCHAR(128)    NOT NULL DEFAULT '' COMMENT '打款账户',
    `payment_account_no`  VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '打款账号',
    `payment_bank`        VARCHAR(128)    NOT NULL DEFAULT '' COMMENT '打款银行',
    `received_account`    VARCHAR(128)    NOT NULL DEFAULT '' COMMENT '收款账户',
    `received_account_no` VARCHAR(64)     NOT NULL DEFAULT '' COMMENT '收款账号',
    `received_bank`       VARCHAR(128)    NOT NULL DEFAULT '' COMMENT '收款银行',
    `currency_type`       VARCHAR(10)     NOT NULL DEFAULT 'CNY' COMMENT '币种（如CNY、USD）',
    `amount`              DECIMAL(20, 8)  NOT NULL DEFAULT 0 COMMENT '转账金额',
    `received_date`       DATETIME(6)     NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '收款日期',
    `remark`              TEXT            NOT NULL COMMENT '备注',
    `is_virtual_account`  TINYINT(1)      NOT NULL DEFAULT 0 COMMENT '是否虚账号打款: 0-否, 1-是',
    `bank_txn_no`         VARCHAR(128)    NOT NULL DEFAULT '' COMMENT '银行交易唯一标识',
    `bank_description`    VARCHAR(256)    NOT NULL DEFAULT '' COMMENT '银行摘要',
    `uid`                 BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '七牛用户ID',
    `identity_name`       VARCHAR(128)    NOT NULL DEFAULT '' COMMENT '认证名称',
    `sales`               VARCHAR(128)    NOT NULL DEFAULT '' COMMENT '归属销售',
    `record_type`         TINYINT         NOT NULL DEFAULT 0 COMMENT '记录类型: 0-Unknown, 1-FirstAllocate, 2-ReAllocate',
    `matching_type`       TINYINT         NOT NULL DEFAULT 0 COMMENT '匹配方式: 0-Unknown, 1-VirtualAccount, 2-Remark, 3-Manual',
    `creator`             VARCHAR(128)    NOT NULL DEFAULT '' COMMENT '创建者',
    `status`              TINYINT         NOT NULL DEFAULT 0 COMMENT '状态: 0-Unknown, 1-PendingReview, 2-NoSales, 3-SalesUnconfirmed, 4-SalesConfirmed, 5-Recharging, 6-Recharged, 7-Revoking, 8-Revoked',
    `payment_id`          VARCHAR(128)    NOT NULL DEFAULT '' COMMENT '钱包流水 id',
    `created_at`          DATETIME(6)     NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `updated_at`          DATETIME(6)     NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_bank_transfer_sn` (`sn`),
    KEY `idx_bank_transfer_uid` (uid)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='银行转账记录';
