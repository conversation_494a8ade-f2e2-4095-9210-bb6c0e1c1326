/**
 * <AUTHOR>
 * @since 2025-05-21 18:15
 */
package action

import (
	"fmt"
	"testing"
)

func TestWalletAction_CreateBankTransfer(t *testing.T) {
	body := `
	{
  "Packet": {
    "PacketNo": ****************,
    "TranCode": "dpe.100.010",
    "SystemCode": ********,
    "ActorCode": ************,
    "UserCode": "zhangshan",
    "UserPass": "ABCDEFFADEFADFDEFEFEPOUYTRDFCDWQ",
    "OrionNo": "",
    "VerNo": "v2.02",
    "CreatedDate": "2025-05-21T15:01:02",
    "Body": {
      "TotalCount": 3,
      "AcctNo": 9508821,
      "AcctName": "上海技术有限公司",
      "CurrencyCode": "CNY",
      "Lists": [
        {
          "VoucherNo": ************,
          "SeqNo": "************202505211",
          "TxAmount": 1458008.17,
          "Balance": ********.67,
          "TranFlag": 1,
          "TransDate": "2025-05-21",
          "TransTime": "145932",
          "Note": "对公付款-IDC\\u0026CDN",
          "Remark": "汇入外LZ25052100495884",
          "PayeeBankNo": "************",
          "PayeeBankName": "招商银行股份有限公司北京首体科技金融支行",
          "PayeeAcctNo": "***************",
          "PayeeName": "小米科技有限责任公司",
          "TransCode": "EK95"
        },
        {
          "VoucherNo": ************,
          "SeqNo": "************202505211",
          "TxAmount": 2210158.52,
          "Balance": ********.19,
          "TranFlag": 1,
          "TransDate": "2025-05-21",
          "TransTime": "145932",
          "Note": "对公付款-IDC\\u0026CDN",
          "Remark": "汇入外LZ25052100495886",
          "PayeeBankNo": "************",
          "PayeeBankName": "招商银行股份有限公司北京首体科技金融支行",
          "PayeeAcctNo": "***************",
          "PayeeName": "小米科技有限责任公司",
          "TransCode": "EK95"
        },
        {
          "VoucherNo": ************,
          "SeqNo": "************202505211",
          "TxAmount": 1543018.19,
          "Balance": ********.38,
          "TranFlag": 1,
          "TransDate": "2025-05-21",
          "TransTime": "145933",
          "Note": "对公付款-IDC\\u0026CDN",
          "Remark": "汇入外LZ25052100495885",
          "PayeeBankNo": "************",
          "PayeeBankName": "招商银行股份有限公司北京首体科技金融支行",
          "PayeeAcctNo": "***************",
          "PayeeName": "小米科技有限责任公司",
          "TransCode": "EK95"
        }
      ]
    }
  }
}
`
	fmt.Print(body)
}
