package service

import (
	"context"
	"errors"
	"sort"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"

	"qiniu.io/pay/qvmprice/model"
)

// QueryItemPricesForUIDWithTimeRange 以给定的时刻作为当前时刻，批量查询给定的 UID 某些计费项在给定时间范围的生效报价（时间范围批量询价，“出账接口”）
func (a *QVMPriceService) QueryItemPricesForUIDWithTimeRange(
	ctx context.Context,
	uid uint64,
	itemSpecs []ItemSpec,
	currencyType string,
	epoch time.Time,
	startTime time.Time,
	endTime time.Time,
) ([]*ItemPricesResp, error) {
	loc := tz.MustLocationFromCtx(ctx)
	epoch = ensureEpoch(epoch, loc)
	startTime = startTime.In(loc)
	endTime = endTime.In(loc)

	currencyType, err := a.ensureCurrencyTypeForUID(ctx, uid, currencyType)
	if err != nil {
		return nil, err
	}

	tr := base.TimeRange{
		Start: startTime,
		End:   endTime,
	}

	var eg errgroup.Group

	var vipPriceRecords *fetchedItemPriceRecords
	eg.Go(func() error {
		resp, err := a.fetchItemPriceRecordsForUID(
			ctx,
			uid,
			itemSpecs,
			currencyType,
			epoch,
			&tr,
		)
		if errors.Is(err, errNotFound) {
			err = nil
		}
		if err != nil {
			// 真错误
			return err
		}

		vipPriceRecords = resp
		return nil
	})

	var publicPriceRecords *fetchedItemPriceRecords
	eg.Go(func() error {
		resp, err := a.fetchItemPriceRecordsForUID(
			ctx,
			0,
			itemSpecs,
			currencyType,
			epoch,
			&tr,
		)
		if errors.Is(err, errNotFound) {
			err = nil
		}
		if err != nil {
			// 真错误
			return err
		}

		publicPriceRecords = resp
		return nil
	})

	err = eg.Wait()
	if err != nil {
		return nil, err
	}

	var result []*ItemPricesResp
	for _, is := range itemSpecs {
		var groupCode string
		if vipPriceRecords != nil && vipPriceRecords.groupCodesMap != nil {
			groupCode = vipPriceRecords.groupCodesMap[is.ItemCode]
		}

		calculatedPrices, err := calculateItemPricesWithTimeRange(
			uid,
			is.ItemCode,
			groupCode,
			is.ZoneCode,
			currencyType,
			epoch,
			tr,
			vipPriceRecords,
			publicPriceRecords,
		)
		if err != nil {
			return nil, err
		}

		result = append(result, calculatedPrices)
	}

	return result, nil
}

type extendedPriceInfo struct {
	price *QVMPriceItemDetail
	gd    *model.QVMGroupDiscount
}

func calculateItemPricesWithTimeRange(
	uid uint64,
	itemCode string,
	groupCode string,
	zoneCode int64,
	currencyType string,
	epoch time.Time,
	tr base.TimeRange,
	vipRecords *fetchedItemPriceRecords,
	publicRecords *fetchedItemPriceRecords,
) (*ItemPricesResp, error) {
	// 使用 epoch 的时区
	loc := epoch.Location()

	startHNS := base.NewHNS(tr.Start)
	endHNS := base.NewHNS(tr.End)

	// 过滤出与当前 item, zone, group 匹配的，且与传入时间范围有重合的 price items 和 group discounts
	priceItemFilterFn := func(x *model.QVMPriceItem) bool {
		if x.ItemCode != itemCode || x.ZoneCode != zoneCode {
			return false
		}
		if x.DeadTime <= startHNS || x.EffectTime >= endHNS {
			return false
		}
		return true
	}
	var filteredVIPPriceItems []*model.QVMPriceItem
	if vipRecords != nil {
		filteredVIPPriceItems = filter(priceItemFilterFn, vipRecords.priceItems)
	}
	var filteredPublicPriceItems []*model.QVMPriceItem
	if publicRecords != nil {
		filteredPublicPriceItems = filter(priceItemFilterFn, publicRecords.priceItems)
	}

	var filteredVIPGroupDiscounts []*model.QVMGroupDiscount
	if vipRecords != nil && groupCode != "" {
		groupFilterFn := func(x *model.QVMGroupDiscount) bool {
			if x.GroupCode != groupCode || x.ZoneCode != zoneCode {
				return false
			}
			if x.DeadTime <= startHNS || x.EffectTime >= endHNS {
				return false
			}
			return true
		}
		filteredVIPGroupDiscounts = filter(groupFilterFn, vipRecords.groupDiscounts)
	}

	// 分裂时间区间（可参考 priced 的 SuperposePriceItems 算法）
	var hnsTimePoints []base.HNS
	hnsTimePoints = append(
		hnsTimePoints,
		base.NewHNS(tr.Start),
		base.NewHNS(tr.End),
	)

	priceItemFlattener := func(x *model.QVMPriceItem) []base.HNS {
		return []base.HNS{x.EffectTime, x.DeadTime}
	}
	hnsTimePoints = append(hnsTimePoints, mapAndFlattenSlice(priceItemFlattener, filteredVIPPriceItems)...)
	hnsTimePoints = append(hnsTimePoints, mapAndFlattenSlice(priceItemFlattener, filteredPublicPriceItems)...)

	if groupCode != "" {
		groupDiscountFlattener := func(x *model.QVMGroupDiscount) []base.HNS {
			return []base.HNS{x.EffectTime, x.DeadTime}
		}
		hnsTimePoints = append(hnsTimePoints, mapAndFlattenSlice(groupDiscountFlattener, filteredVIPGroupDiscounts)...)
	}

	sort.Slice(hnsTimePoints, func(i int, j int) bool {
		return hnsTimePoints[i] < hnsTimePoints[j]
	})
	timePoints := mapSlice(func(x base.HNS) time.Time { return x.TimeIn(loc) }, hnsTimePoints)
	timePoints = base.UniqTimeSlice(timePoints)

	var pricesWithDiscountInfo []extendedPriceInfo
	for i := range timePoints {
		if i == len(timePoints)-1 {
			break
		}

		start := timePoints[i]
		end := timePoints[i+1]

		// 只处理 [tr.Start, tr.End) 之间的时间段
		// end <= tr.Start || tr.End <= start
		// !(end > tr.Start) || !(tr.End > start)
		if !end.After(tr.Start) || !tr.End.After(start) {
			continue
		}

		// 经过上面的处理后，每个时间段只会有一组对应的 vipPriceItem、publicPriceItem、groupDiscount 生效
		// 拉出对应的记录，构造简化的入参，调用 calculateItemPrice 即可得到这个时间段的报价
		// 以下的操作和 calculateItemPrice 第一部分十分相似

		// 如果有按组改价操作，那么应该以改价时刻为当前时刻去拉报价，所以先处理 QVMGroupDiscounts
		var effectiveGroupDiscount *model.QVMGroupDiscount
		var effectiveVIPPriceItem *model.QVMPriceItem
		var effectivePublicPriceItem *model.QVMPriceItem
		{
			discountAdjustedEpoch := epoch
			if groupCode != "" {
				for _, gd := range vipRecords.groupDiscounts {
					if shouldUpdateEffectiveGroupDiscountForTimeRange(
						effectiveGroupDiscount,
						gd,
						groupCode,
						zoneCode,
						epoch,
						start,
						end,
					) {
						effectiveGroupDiscount = gd
					}
				}

				if effectiveGroupDiscount != nil {
					discountAdjustedEpoch = effectiveGroupDiscount.CreatedAt
				}
			}

			if vipRecords != nil {
				for _, pi := range vipRecords.priceItems {
					if shouldUpdateEffectivePriceItemForTimeRange(
						effectiveVIPPriceItem,
						pi,
						itemCode,
						zoneCode,
						currencyType,
						epoch,
						start,
						end,
					) {
						effectiveVIPPriceItem = pi
					}
				}
			}

			for _, pi := range publicRecords.priceItems {
				if shouldUpdateEffectivePriceItemForTimeRange(
					effectivePublicPriceItem,
					pi,
					itemCode,
					zoneCode,
					currencyType,
					discountAdjustedEpoch,
					start,
					end,
				) {
					effectivePublicPriceItem = pi
				}
			}
		}

		// 不能既没有公开报价也没有 VIP 报价
		if effectiveVIPPriceItem == nil && effectivePublicPriceItem == nil {
			continue
		}

		var vipRecordsForThisPeriod *fetchedItemPriceRecords
		if vipRecords != nil {
			vipRecordsForThisPeriod = &fetchedItemPriceRecords{
				priceItems:      nil,
				priceItemStairs: vipRecords.priceItemStairs,
				groupCodesMap:   nil,
				groupDiscounts:  nil,
			}
			if effectiveVIPPriceItem != nil {
				vipRecordsForThisPeriod.priceItems = []*model.QVMPriceItem{effectiveVIPPriceItem}
			}
			if effectiveGroupDiscount != nil {
				vipRecordsForThisPeriod.groupDiscounts = []*model.QVMGroupDiscount{effectiveGroupDiscount}
			}
		}

		publicRecordsForThisPeriod := fetchedItemPriceRecords{
			priceItems:      nil,
			priceItemStairs: publicRecords.priceItemStairs,
			groupCodesMap:   nil,
			groupDiscounts:  nil,
		}
		if effectivePublicPriceItem != nil {
			publicRecordsForThisPeriod.priceItems = []*model.QVMPriceItem{effectivePublicPriceItem}
		}

		price, err := calculateItemPrice(
			uid,
			itemCode,
			groupCode,
			zoneCode,
			currencyType,
			time.Time{},
			epoch,
			&base.TimeRange{Start: start, End: end},
			vipRecordsForThisPeriod,
			&publicRecordsForThisPeriod,
		)
		if err != nil {
			return nil, err
		}

		pricesWithDiscountInfo = append(pricesWithDiscountInfo, extendedPriceInfo{
			price: price,
			gd:    effectiveGroupDiscount,
		})
	}

	// 将归属相同 groupDiscount 且报价也相同的记录合并
	prices := coalescePriceSpans(pricesWithDiscountInfo)

	return &ItemPricesResp{
		ItemCode: itemCode,
		ZoneCode: zoneCode,
		Prices:   prices,
	}, nil
}

// 合并时间轴上相邻的、归属相同 QVMGroupDiscount 且价格相同的报价记录
func coalescePriceSpans(x []extendedPriceInfo) []*QVMPriceItemDetail {
	if len(x) == 0 {
		return nil
	}

	if len(x) == 1 {
		return []*QVMPriceItemDetail{x[0].price}
	}

	// len(x) >= 2
	result := make([]*QVMPriceItemDetail, 0, len(x))
	clonedPrice := *x[0].price
	result = append(result, &clonedPrice)

	prevEPI := &x[0]
	for i := 1; i < len(x); i++ {
		epi := &x[i]
		if shouldMergePriceSpans(prevEPI, epi) {
			// 把最新一条的 DeadTime 更新为当前条的 DeadTime，也就是将当前一条与上一条合并
			result[len(result)-1].DeadTime = epi.price.DeadTime
		} else {
			// 避免更改入参
			clonedPrice := *epi.price
			result = append(result, &clonedPrice)
		}

		prevEPI = &x[i]
	}

	return result
}

func shouldMergePriceSpans(
	prevEPI *extendedPriceInfo,
	epi *extendedPriceInfo,
) bool {
	if prevEPI.gd == nil || epi.gd == nil || prevEPI.gd != epi.gd {
		return false
	}
	if !prevEPI.price.equalWithoutEffectDeadTimes(epi.price) {
		return false
	}
	if !prevEPI.price.DeadTime.Equal(epi.price.EffectTime) {
		return false
	}
	return true
}

func shouldUpdateEffectivePriceItemForTimeRange(
	prevEffectivePriceItem *model.QVMPriceItem,
	pi *model.QVMPriceItem,
	itemCode string,
	zoneCode int64,
	currencyType string,
	epoch time.Time,
	start time.Time,
	end time.Time,
) bool {
	if prevEffectivePriceItem != nil && prevEffectivePriceItem.CreatedAt.After(pi.CreatedAt) {
		return false
	}

	if pi.ItemCode != itemCode || pi.ZoneCode != zoneCode || pi.CurrencyType != currencyType {
		return false
	}

	// 此处使用的 epoch 可能与查询时的 epoch 不同（例如涉及按组改价操作的情况），因此仍然要按 epoch 过滤
	if pi.CreatedAt.After(epoch) {
		return false
	}

	// 由于时间范围分裂算法的缘故，感兴趣的 pi 的生效时间段需要完全包含 [start, end) 时间段
	// pi.EffectTime <= start && pi.DeadTime >= end
	// 反转条件: pi.EffectTime > start || pi.DeadTime < end
	if pi.EffectTime.TimeIn(time.UTC).After(start) || pi.DeadTime.TimeIn(time.UTC).Before(end) {
		return false
	}

	return true
}

func shouldUpdateEffectiveGroupDiscountForTimeRange(
	prevEffectiveGroupDiscount *model.QVMGroupDiscount,
	gd *model.QVMGroupDiscount,
	groupCode string,
	zoneCode int64,
	epoch time.Time,
	start time.Time,
	end time.Time,
) bool {
	if prevEffectiveGroupDiscount != nil && prevEffectiveGroupDiscount.CreatedAt.After(gd.CreatedAt) {
		return false
	}

	if gd.GroupCode != groupCode || gd.ZoneCode != zoneCode {
		return false
	}

	// 此处使用的 epoch 可能与查询时的 epoch 不同（例如涉及按组改价操作的情况），因此仍然要按 epoch 过滤
	if gd.CreatedAt.After(epoch) {
		return false
	}

	// 由于时间范围分裂算法的缘故，感兴趣的 gd 的生效时间段需要完全包含 [start, end) 时间段
	// gd.EffectTime <= start && gd.DeadTime >= end
	// 反转条件: gd.EffectTime > start || gd.DeadTime < end
	if gd.EffectTime.TimeIn(time.UTC).After(start) || gd.DeadTime.TimeIn(time.UTC).Before(end) {
		return false
	}

	return true
}
