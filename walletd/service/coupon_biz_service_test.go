package service_test

import (
	"context"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/walletd/model"
	"qiniu.io/pay/walletd/service"
)

func TestBindCouponRebate(t *testing.T) {
	sandbox := buildSandbox(t)

	walletService := sandbox.walletService

	param := &service.BindCouponRebateParam{
		UID:           1,
		Code:          "Free:MPS:Rebate",
		TransactionID: 233,
		ZoneID:        1,
		EffectTime:    base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:      base.NewHNS(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
	}

	ctx := context.Background()

	// create coupon rebate
	_, err := walletService.CreateCouponRebate(ctx, &model.CouponRebate{
		Code:         param.Code,
		Type:         "FREE",
		ScopeID:      1,
		Name:         "free-mps-rebate",
		Description:  "test free mps rebate",
		Threshold:    0,
		CurrencyType: "CNY",
		Money:        20 * 10000,
		EffectTime:   base.NewHNS(time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:     base.NewHNS(time.Date(2199, 1, 1, 0, 0, 0, 0, time.UTC)),
	})
	assert.NoError(t, err)

	resp, err := walletService.BindCouponRebate(ctx, param)
	assert.NoError(t, err)
	assert.Equal(t, param.Code, resp.Coupon.Code)
	assert.Equal(t, param.EffectTime, resp.Coupon.EffectTime)
	assert.Equal(t, param.DeadTime, resp.Coupon.DeadTime)
}

func TestBindCouponRebateInconsistentCurrencyType(t *testing.T) {
	sandbox := buildSandbox(t)

	walletService := sandbox.walletService

	param := &service.BindCouponRebateParam{
		UID:           1,
		Code:          "Free:MPS:Rebate!USD",
		TransactionID: 233,
		ZoneID:        1,
		EffectTime:    base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:      base.NewHNS(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
	}

	ctx := context.Background()

	// create coupon rebate
	_, err := walletService.CreateCouponRebate(ctx, &model.CouponRebate{
		Code:         param.Code,
		Type:         "FREE",
		ScopeID:      1,
		Name:         "free-mps-rebate",
		Description:  "test free mps rebate",
		Threshold:    0,
		CurrencyType: "USD", // mock 的用户币种肯定是 CNY，此处写 USD，绑定的时候应该会死
		Money:        20 * 10000,
		EffectTime:   base.NewHNS(time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:     base.NewHNS(time.Date(2199, 1, 1, 0, 0, 0, 0, time.UTC)),
	})
	assert.NoError(t, err)

	resp, err := walletService.BindCouponRebate(ctx, param)
	assert.Error(t, err)
	assert.Zero(t, resp)
}

func TestUpgradeCouponRebate(t *testing.T) {
	sandbox := buildSandbox(t)

	walletService := sandbox.walletService

	param := &service.UpgradeCouponRebateParam{
		UID:           1,
		ZoneID:        1,
		TransactionID: 233,
		SrcCode:       "Free:MPS:Rebate-20",
		DstCode:       "Free:MPS:Rebate-30",
		EffectTime:    base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:      base.NewHNS(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
	}

	ctx := context.Background()

	// create coupon rebate
	srcRebate, err := walletService.CreateCouponRebate(ctx, &model.CouponRebate{
		Code:        param.SrcCode,
		Type:        "FREE",
		ScopeID:     1,
		Name:        "free-mps-rebate-20",
		Description: "test free mps rebate 20",
		Threshold:   0,
		Money:       20 * 10000,
		EffectTime:  base.NewHNS(time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:    base.NewHNS(time.Date(2199, 1, 1, 0, 0, 0, 0, time.UTC)),
	})
	assert.NoError(t, err)

	dstRebate, err := walletService.CreateCouponRebate(ctx, &model.CouponRebate{
		Code:        param.DstCode,
		Type:        "FREE",
		ScopeID:     1,
		Name:        "free-mps-rebate-30",
		Description: "test free mps rebate 30",
		Threshold:   0,
		Money:       30 * 10000,
		EffectTime:  base.NewHNS(time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:    base.NewHNS(time.Date(2199, 1, 1, 0, 0, 0, 0, time.UTC)),
	})
	assert.NoError(t, err)

	_, err = walletService.CreateCouponUserMap(ctx, &model.CouponUserMap{
		CouponID:   srcRebate.ID,
		Type:       model.CouponTypeRebate,
		UID:        param.UID,
		ZoneID:     param.ZoneID,
		EffectTime: base.NewHNS(time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:   base.NewHNS(time.Date(2199, 1, 1, 0, 0, 0, 0, time.UTC)),
	})
	assert.NoError(t, err)

	resp, err := walletService.UpgradeCouponRebate(ctx, param)
	assert.NoError(t, err)
	assert.Equal(t, dstRebate.Code, resp.Coupon.Code)
	assert.Equal(t, param.EffectTime, resp.Coupon.EffectTime)
	assert.Equal(t, param.DeadTime, resp.Coupon.DeadTime)
}

func TestUnbindCouponRebate(t *testing.T) {
	sandbox := buildSandbox(t)

	walletService := sandbox.walletService

	ctx := context.Background()

	// create coupon rebate
	rebateCode := "Free:MPS:Rebate"
	_, err := walletService.CreateCouponRebate(ctx, &model.CouponRebate{
		Code:         rebateCode,
		Type:         "FREE",
		ScopeID:      1,
		Name:         "free-mps-rebate",
		Description:  "test free mps rebate",
		Threshold:    0,
		Money:        20 * 10000,
		EffectTime:   base.NewHNS(time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:     base.NewHNS(time.Date(2199, 1, 1, 0, 0, 0, 0, time.UTC)),
		CurrencyType: base.CurrencyTypeCNY,
	})
	assert.NoError(t, err)

	err = walletService.UnbindCouponRebate(ctx, "NotExistCode")
	assert.Error(t, err, "rebate 不存在的情况下,解绑应该报错:query coupon rebate failed:code=NotExistCode")

	err = walletService.UnbindCouponRebate(ctx, rebateCode)
	assert.Error(t, err, "rebate 没有绑定用户的情况下,解绑应该报错:couponRebate is not bound or bound to multiple uid")

	// rebate Free:MPS:Rebate 绑定给 uid=1 的用户
	param1 := &service.BindCouponRebateParam{
		UID:           1,
		Code:          rebateCode,
		TransactionID: 233,
		ZoneID:        1,
		EffectTime:    base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:      base.NewHNS(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
	}
	resp1, err := walletService.BindCouponRebate(ctx, param1)
	assert.NoError(t, err)
	assert.Equal(t, param1.Code, resp1.Coupon.Code)
	assert.Equal(t, param1.EffectTime, resp1.Coupon.EffectTime)
	assert.Equal(t, param1.DeadTime, resp1.Coupon.DeadTime)

	// rebate Free:MPS:Rebate 解除 uid=1 用户的绑定
	err = walletService.UnbindCouponRebate(ctx, rebateCode)
	assert.NoError(t, err, "rebate 绑定了一个用户的情况下,解绑应该成功")

	// rebate Free:MPS:Rebate 绑定给  uid=2 的用户
	param2 := &service.BindCouponRebateParam{
		UID:           2,
		Code:          rebateCode,
		TransactionID: 233,
		ZoneID:        1,
		EffectTime:    base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:      base.NewHNS(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
	}
	resp2, err := walletService.BindCouponRebate(ctx, param2)
	assert.NoError(t, err)
	assert.Equal(t, param2.Code, resp2.Coupon.Code)
	assert.Equal(t, param2.EffectTime, resp2.Coupon.EffectTime)
	assert.Equal(t, param2.DeadTime, resp2.Coupon.DeadTime)

	// rebate Free:MPS:Rebate 绑定给 uid=3 的用户
	param3 := &service.BindCouponRebateParam{
		UID:           3,
		Code:          rebateCode,
		TransactionID: 233,
		ZoneID:        1,
		EffectTime:    base.NewHNS(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC)),
		DeadTime:      base.NewHNS(time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)),
	}
	resp3, err := walletService.BindCouponRebate(ctx, param3)
	assert.NoError(t, err)
	assert.Equal(t, param3.Code, resp3.Coupon.Code)
	assert.Equal(t, param3.EffectTime, resp3.Coupon.EffectTime)
	assert.Equal(t, param3.DeadTime, resp3.Coupon.DeadTime)

	err = walletService.UnbindCouponRebate(ctx, rebateCode)
	assert.Error(t, err, "rebate 绑定多个用户的情况下,解绑应该报错:couponRebate is not bound or bound to multiple uid")
}
