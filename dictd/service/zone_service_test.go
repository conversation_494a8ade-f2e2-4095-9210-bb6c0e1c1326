package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestZone(t *testing.T) {
	sandbox := buildSandbox(t)

	dictService := sandbox.dictService

	zs := []model.Zone{
		{
			Code:        1,
			Name:        "qiniu1",
			Title:       "七牛机房1",
			Description: "七牛的机房1",
			Remark:      "测试Zone-1",
		},
		{
			Code:        2,
			Name:        "qiniu2",
			Title:       "七牛机房2",
			Description: "七牛的机房2",
			Remark:      "测试Zone-2",
		},
		{
			Code:        3,
			Name:        "qiniu3",
			Title:       "七牛机房3",
			Description: "七牛的机房3",
			Remark:      "测试Zone-3",
		},
		{
			// GetZoneByCode(0) 应当不能导致 WHERE code = 0 的查询条件被丢掉
			Code:        0,
			Name:        "nb",
			Title:       "零值机房",
			Description: "零值是万恶之源",
			Remark:      "忘记历史就意味着 bug",
		},
	}
	assertFields := func(zone *model.Zone, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(zs) }) {
			assert.Equal(t, zs[n].Name, zone.Name, msgAndArgs...)
			assert.Equal(t, zs[n].Code, zone.Code, msgAndArgs...)
			assert.Equal(t, zs[n].Title, zone.Title, msgAndArgs...)
			assert.Equal(t, zs[n].Description, zone.Description, msgAndArgs...)
			assert.Equal(t, zs[n].Remark, zone.Remark, msgAndArgs...)
		}
	}
	ctx := context.TODO()
	ids := make(map[uint64]int, len(zs))
	codes := make(map[uint64]int64, len(zs))
	for n, z := range zs {
		zone, err := dictService.CreateZone(ctx, &z)
		if assert.NoError(t, err, "CreateZone") {
			assert.NotZero(t, zone.ID, "CreateZone return zero id")
			ids[zone.ID] = n
			codes[zone.ID] = z.Code
		}

		zone, err = dictService.GetZoneByID(ctx, zone.ID)
		if assert.NoError(t, err, "GetZoneByID") {
			assertFields(zone, n, "GetZoneByID fields not match")
		}

		zone.Name += z.Name
		zs[n].Name += z.Name
		zone, err = dictService.UpdateZoneByID(ctx, zone.ID, zone)
		if assert.NoError(t, err, "UpdateZoneByID") {
			assert.NotZero(t, zone.ID, "UpdateZoneByID return zero id")
		}

		zone, err = dictService.GetZoneByCode(ctx, z.Code)
		if assert.NoError(t, err, "GetZoneByCode") {
			assertFields(zone, n, "GetZoneByCode fields not match")
		}

		zone.Description += z.Description
		zs[n].Description += z.Description
		zone, err = dictService.UpdateZoneByCode(ctx, zone.Code, zone)
		if assert.NoError(t, err, "UpdateZoneByCode") {
			assert.NotZero(t, zone.ID, "UpdateZoneByCode return zero id")
		}
	}

	zones, err := dictService.ListAllZones(ctx, 0, 10)
	if assert.NoError(t, err, "ListAllZones") {
		assert.Len(t, zones, len(zs), "ListAllZones length unexpected")
		count, err := dictService.CountAllZones(ctx)
		if assert.NoError(t, err, "CountAllZones") {
			assert.Equal(t, uint64(len(zs)), count, "CountAllZones count unexpected")
		}
		for _, zone := range zones {
			if assert.Contains(t, ids, zone.ID, "ListAllZones") {
				assertFields(&zone, ids[zone.ID], "ListAllZones")
			}
		}
	}

	for id, n := range ids {
		if id%2 == 0 {
			zone, err := dictService.DeleteZoneByID(ctx, id)
			if assert.NoError(t, err, "DeleteZoneByID") {
				assertFields(zone, n, "DeleteZoneByID")
			}
		} else {
			zone, err := dictService.DeleteZoneByCode(ctx, codes[id])
			if assert.NoError(t, err, "DeleteZoneByCode") {
				assertFields(zone, n, "DeleteZoneByCode")
			}

		}
	}

	count, err := dictService.CountAllZones(ctx)
	if assert.NoError(t, err, "CountAllZones") {
		assert.Equal(t, uint64(0), count, "CountAllZones count after delete all is not zero")
	}
}
