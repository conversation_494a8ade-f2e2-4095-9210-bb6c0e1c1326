package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/intl"
	pb "github.com/qbox/pay-sdk/credit"

	"qiniu.io/pay/creditd/model"
	"qiniu.io/pay/creditd/model/enums"
)

// BuildPbCreditFromManualCredit converts from model.ManualCredit to the protobuf type.
func BuildPbCreditFromManualCredit(credit *model.ManualCredit) (*pb.Credit, error) {
	creditType := pb.CreditTypeUnknown
	if credit.CreditLine > 0 {
		creditType = pb.CreditTypeManual
	}
	return &pb.Credit{
		Id:           credit.ID,
		Uid:          credit.UID,
		CustomerName: credit.CustomerName,
		CreditType:   creditType,
		CreditLine:   credit.CreditLine,
		CreditPeriod: credit.CreditPeriod,
		PeriodUnit:   pb.PeriodUnit(credit.PeriodUnit),
		Status:       pb.Status(credit.Status),
		Remark:       credit.Remark,
		AuthTime:     int64(base.NewHNS(credit.InitAt)),
		CreatedAt:    timestamppb.New(credit.CreatedAt),
		UpdatedAt:    timestamppb.New(credit.UpdatedAt),
		CurrencyType: credit.CurrencyType,
	}, nil
}

func BuildPbCreditFromSystemCredit(credit *model.SystemCredit, localizer intl.Localizer) (*pb.Credit, error) {
	creditType := pb.CreditTypeUnknown
	creditStatus := pb.StatusUnknown
	if credit.CreditLine > 0 {
		creditType = pb.CreditTypeSystem
		creditStatus = pb.StatusValid
	}
	return &pb.Credit{
		Id:           credit.ID,
		Uid:          credit.UID,
		CustomerName: credit.CustomerName,
		CreditType:   creditType,
		CreditLine:   credit.CreditLine,
		CreditPeriod: credit.CreditPeriod,
		PeriodUnit:   pb.PeriodUnit(credit.PeriodUnit),
		AssignReason: pb.AssignReason(credit.AssignReason),
		Month:        timestamppb.New(credit.Month),
		Remark:       credit.GenerateRemark(localizer),
		Status:       creditStatus,
		AuthTime:     int64(base.NewHNS(credit.UpdatedAt)),
		CreatedAt:    timestamppb.New(credit.CreatedAt),
		UpdatedAt:    timestamppb.New(credit.UpdatedAt),
		CurrencyType: credit.CurrencyType,
	}, nil
}

// BuildPbManualCredit converts from model.ManualCredit to the protobuf type.
func BuildPbManualCredit(credit *model.ManualCredit) (*pb.ManualCredit, error) {
	return &pb.ManualCredit{
		Id:           credit.ID,
		Uid:          credit.UID,
		CustomerName: credit.CustomerName,
		CreditLine:   credit.CreditLine,
		CreditPeriod: credit.CreditPeriod,
		PeriodUnit:   pb.PeriodUnit(credit.PeriodUnit),
		Status:       pb.Status(credit.Status),
		Remark:       credit.Remark,
		AuthTime:     int64(base.NewHNS(credit.InitAt)),
		CreatedAt:    timestamppb.New(credit.CreatedAt),
		UpdatedAt:    timestamppb.New(credit.UpdatedAt),
		CurrencyType: credit.CurrencyType,
	}, nil
}

// BuildPbSystemCredit converts from model.ManualCredit to the protobuf type.
func BuildPbSystemCredit(credit *model.SystemCredit) (*pb.SystemCredit, error) {
	return &pb.SystemCredit{
		Id:           credit.ID,
		Uid:          credit.UID,
		CustomerName: credit.CustomerName,
		CreditLine:   credit.CreditLine,
		CreditPeriod: credit.CreditPeriod,
		PeriodUnit:   pb.PeriodUnit(credit.PeriodUnit),
		CreatedAt:    timestamppb.New(credit.CreatedAt),
		UpdatedAt:    timestamppb.New(credit.UpdatedAt),
		CurrencyType: credit.CurrencyType,
	}, nil
}

// BuildModelCredit converts from pb.ManualCredit to the model layer type.
func BuildModelCredit(credit *pb.Credit) (*model.ManualCredit, error) {
	return &model.ManualCredit{
		UID:          credit.GetUid(),
		CustomerName: credit.GetCustomerName(),
		CreditLine:   credit.GetCreditLine(),
		CreditPeriod: credit.GetCreditPeriod(),
		PeriodUnit:   enums.PeriodUnit(credit.GetPeriodUnit()),
		Status:       enums.CreditStatus(credit.GetStatus()),
		Remark:       credit.GetRemark(),
		CurrencyType: credit.GetCurrencyType(),
	}, nil
}

func BuildModelManualCredit(credit *pb.ManualCredit) (*model.ManualCredit, error) {
	return &model.ManualCredit{
		UID:          credit.GetUid(),
		CustomerName: credit.GetCustomerName(),
		CreditLine:   credit.GetCreditLine(),
		CreditPeriod: credit.GetCreditPeriod(),
		PeriodUnit:   enums.PeriodUnit(credit.GetPeriodUnit()),
		Status:       enums.CreditStatus(credit.GetStatus()),
		Remark:       credit.GetRemark(),
		CurrencyType: credit.GetCurrencyType(),
	}, nil
}

func BuildModelSystemCredit(credit *pb.SystemCredit) (*model.SystemCredit, error) {
	return &model.SystemCredit{
		UID:          credit.GetUid(),
		CreditLine:   credit.GetCreditLine(),
		CreditPeriod: credit.GetCreditPeriod(),
		PeriodUnit:   enums.PeriodUnit(credit.GetPeriodUnit()),
		CurrencyType: credit.GetCurrencyType(),
		AssignReason: enums.AssignReason(credit.GetAssignReason()),
		Month:        credit.GetMonth().AsTime(),
	}, nil
}

func BuildPbSystemCreditHistory(history *model.SystemCreditHistory) (*pb.SystemCreditHistory, error) {
	return &pb.SystemCreditHistory{
		Id:           history.ID,
		Uid:          history.UID,
		CustomerName: history.CustomerName,
		CreditLine:   history.CreditLine,
		CreditPeriod: history.CreditPeriod,
		PeriodUnit:   pb.PeriodUnit(history.PeriodUnit),
		AssignReason: pb.AssignReason(history.AssignReason),
		Remark:       history.Remark,
		Month:        timestamppb.New(history.Month),
		CurrencyType: history.CurrencyType,
		CreatedAt:    timestamppb.New(history.CreatedAt),
	}, nil
}

// BuildStatus converts from pb.CreditStatus to the model layer type.
func BuildStatus(pbStatusArr []pb.Status) []enums.CreditStatus {
	statusArr := make([]enums.CreditStatus, len(pbStatusArr))
	for i, pbStatus := range pbStatusArr {
		statusArr[i] = enums.CreditStatus(pbStatus)
	}
	return statusArr
}

func BuildStartEnd(pbStart, pbEnd *timestamppb.Timestamp) (start, end *time.Time, err error) {
	if pbStart != nil {
		err1 := pbStart.CheckValid()
		if err1 != nil {
			return nil, nil, errors.Trace(err1)
		}
		startVal := pbStart.AsTime()
		start = &startVal
	}

	if pbEnd != nil {
		err1 := pbEnd.CheckValid()
		if err1 != nil {
			return nil, nil, errors.Trace(err1)
		}
		endVal := pbEnd.AsTime()
		end = &endVal
	}

	return start, end, nil
}
