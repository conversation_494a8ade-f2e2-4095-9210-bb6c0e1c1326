package action_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/pay-sdk/dict"
	"github.com/stretchr/testify/assert"
)

func TestProductInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	dictClient := sandbox.dictClient

	ps := []dict.Product{
		{
			Code:        "kodo",
			Name:        "云存储",
			Description: "七牛云存储提供高可用高可靠的分布式存储服务",
			Remark:      "云存储备注",
			Order:       1,
		},
		{
			Code:        "fusion",
			Name:        "融合CDN",
			Description: "七牛融合CDN提供多线路融合CDN加速",
			Remark:      "融合CDN备注",
			Order:       2,
		},
		{
			Code:        "dora",
			Name:        "数据处理",
			Description: "七牛数据处理提供图片音视频的处理服务",
			Remark:      "数据处理备注",
			Order:       3,
		},
	}
	assertFields := func(product *dict.Product, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(ps) }) {
			assert.Equal(t, ps[n].GetName(), product.GetName(), msgAndArgs...)
			assert.Equal(t, ps[n].GetCode(), product.GetCode(), msgAndArgs...)
			assert.Equal(t, ps[n].GetDescription(), product.GetDescription(), msgAndArgs...)
			assert.Equal(t, ps[n].GetRemark(), product.GetRemark(), msgAndArgs...)
		}
	}
	ids := make(map[uint64]int, len(ps))
	codes := make(map[uint64]string, len(ps))
	for n, p := range ps {
		product, err := dictClient.CreateProduct(context.Background(), &p)
		if assert.NoError(t, err, "CreateProduct") {
			assert.NotZero(t, product.GetId(), "CreateProduct")
			ids[product.GetId()] = n
			codes[product.GetId()] = p.GetCode()
		}

		product, err = dictClient.GetProductByID(context.Background(), &dict.IDParam{Id: product.GetId()})
		if assert.NoError(t, err, "GetProductByID") {
			assertFields(product, n, "GetProductByID")
		}

		product.Name += product.GetCode()
		ps[n].Name += p.GetCode()
		product, err = dictClient.UpdateProductByID(context.Background(), &dict.IDProductParam{Id: product.GetId(), Product: product})
		if assert.NoError(t, err, "UpdateProductByID") {
			assert.Equal(t, product.GetId(), product.GetId(), "UpdateProductByID")
		}

		product, err = dictClient.GetProductByCode(context.Background(), &dict.CodeParam{Code: product.Code})
		if assert.NoError(t, err, "GetProductByCode") {
			assertFields(product, n, "GetProductByCode")
		}

		product.Name += product.GetCode()
		ps[n].Name += product.GetCode()
		product, err = dictClient.UpdateProductByCode(context.Background(), &dict.CodeProductParam{Code: p.Code, Product: product})
		if assert.NoError(t, err, "UpdateProductByCode") {
			assert.Equal(t, ps[n].GetName(), product.GetName(), "UpdateProductByCode")
		}
	}

	count, err := dictClient.CountAllProducts(context.Background(), &empty.Empty{})
	if assert.NoError(t, err, "CountAllProducts") {
		assert.Len(t, ps, int(count.GetCount()), "CountAllProducts")
	}

	list, err := dictClient.ListAllProducts(context.Background(), &dict.ListAllProductsPagingParam{Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListAllProducts") {
		assert.Len(t, ps, int(list.GetCount()), "ListAllProducts")
		for i, p := range list.GetProducts() {
			assertFields(p, i, "ListAllProducts")
		}
	}

	for id, n := range ids {
		if id%2 == 0 {
			product, err := dictClient.DeleteProductByID(context.Background(), &dict.IDParam{Id: id})
			if assert.NoError(t, err, "DeleteProductByID") {
				assertFields(product, n, "DeleteProductByID")
			}
		} else {
			product, err := dictClient.DeleteProductByCode(context.Background(), &dict.CodeParam{Code: codes[id]})
			if assert.NoError(t, err, "DeleteProductByCode") {
				assertFields(product, n, "DeleteProductByCode")
			}
		}
	}
}
