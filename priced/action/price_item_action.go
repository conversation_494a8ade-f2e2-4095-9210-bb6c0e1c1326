package action

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"
	"qiniu.io/pay/priced/action/adapter"
)

// GetPriceItemByID 根据 ID 获取计费项价格
func (a *PriceAction) GetPriceItemByID(
	ctx context.Context,
	priceItemID *pb.IDParam,
) (*pb.PriceItem, error) {
	priceItem, err := a.priceBizSrv.GetPriceItemByID(ctx, priceItemID.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceItem(priceItem)
}

// CreatePriceItem 创建计费项价格
func (a *PriceAction) CreatePriceItem(
	ctx context.Context,
	priceItem *pb.PriceItem,
) (*pb.PriceItem, error) {
	p, err := adapter.BuildPriceItem(priceItem)
	if err != nil {
		return nil, errors.Trace(err)
	}

	priceItemModel, err := a.priceBizSrv.CreatePriceItem(ctx, p)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceItem(priceItemModel)
}

// UpdatePriceItemByID 根据 ID 更新计费项价格
func (a *PriceAction) UpdatePriceItemByID(
	ctx context.Context,
	param *pb.IDPriceItemParam,
) (*pb.PriceItem, error) {
	priceItem := param.GetPriceItem()
	p, err := adapter.BuildPriceItem(priceItem)
	if err != nil {
		return nil, errors.Trace(err)
	}

	priceItemModel, err := a.priceBizSrv.UpdatePriceItemByID(ctx, param.GetId(), p)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceItem(priceItemModel)
}

// DeletePriceItemByID 根据 ID 删除计费项价格
func (a *PriceAction) DeletePriceItemByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.PriceItem, error) {
	priceItemModel, err := a.priceBizSrv.DeletePriceItemByID(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceItem(priceItemModel)
}

// ListAllPriceItems 查询所有的计费项价格
func (a *PriceAction) ListAllPriceItems(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.PriceItemList, error) {
	count, err := a.priceBizSrv.CountAllPriceItems(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}
	offset, limit := a.Paging(param)
	priceItemModels, err := a.priceBizSrv.ListAllPriceItems(ctx, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	list := &pb.PriceItemList{
		Count:      count,
		PriceItems: make([]*pb.PriceItem, len(priceItemModels)),
	}
	for i, priceItemModel := range priceItemModels {
		priceItem, err := adapter.BuildPbPriceItem(&priceItemModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceItems[i] = priceItem
	}
	return list, nil
}

// ListAllPriceItemsByPriceID 查询所有的计费项价格
func (a *PriceAction) ListAllPriceItemsByPriceID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.PriceItemList, error) {
	count, err := a.priceBizSrv.CountAllPriceItemsByPriceID(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}
	offset, limit := a.Paging(param)
	priceItemModels, err := a.priceBizSrv.ListAllPriceItemsByPriceID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}
	list := &pb.PriceItemList{
		Count:      count,
		PriceItems: make([]*pb.PriceItem, len(priceItemModels)),
	}
	for i, priceItemModel := range priceItemModels {
		priceItem, err := adapter.BuildPbPriceItem(&priceItemModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceItems[i] = priceItem
	}
	return list, nil
}

// ListPriceItemsByPriceIDItemID 根据价格表 ID 和计费项 ID 查询计费项价格
func (a *PriceAction) ListPriceItemsByPriceIDItemID(
	ctx context.Context,
	param *pb.PriceItemPagingParam,
) (*pb.PriceItemList, error) {
	at := base.NewHNS(time.Now())

	count, err := a.priceBizSrv.CountPriceItemsByPriceIDItems(ctx,
		param.GetPriceId(), param.GetZoneId(), []uint64{param.GetItemId()}, at)
	if err != nil {
		return nil, errors.Trace(err)
	}

	offset, limit := a.Paging(param)
	priceItemModels, err := a.priceBizSrv.ListPriceItemsByPriceIDItemID(ctx,
		param.GetPriceId(), param.GetZoneId(), []uint64{param.GetItemId()}, at, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}

	list := &pb.PriceItemList{
		Count:      count,
		PriceItems: make([]*pb.PriceItem, len(priceItemModels)),
	}

	for i, priceItemModel := range priceItemModels {
		priceItem, err := adapter.BuildPbPriceItem(&priceItemModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceItems[i] = priceItem
	}

	return list, nil
}
