package model

import (
	"context"
	"errors"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
)

const qvmPriceItemTableName = "qvm_price_items"

// QVMPriceItem model definition
type QVMPriceItem struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// QVMPriceTableID 关联的 QVMPriceTable ID
	QVMPriceTableID uint64 `gorm:"column:qvm_price_table_id;not null;unique_index:idx_qvmpi_qvmptid_item_zone_effect_dead_curr_ctime;comment:'关联的 QVMPriceTable ID'"`
	// UID 冗余的 QVMPriceTable UID 字段
	UID uint64 `gorm:"column:uid;not null;index:idx_qvmpi_uid_item_zone_effect_dead_curr_ctime;comment:'冗余的 QVMPriceTable UID 字段'"`
	// ItemCode 计费项 code
	ItemCode string `gorm:"column:item_code;type:VARCHAR(256);not null;unique_index:idx_qvmpi_qvmptid_item_zone_effect_dead_curr_ctime;index:idx_qvmpi_uid_item_zone_effect_dead_curr_ctime;comment:'计费项 code'"`
	// ZoneCode 区域 code
	ZoneCode int64 `gorm:"column:zone_code;not null;unique_index:idx_qvmpi_qvmptid_item_zone_effect_dead_curr_ctime;index:idx_qvmpi_uid_item_zone_effect_dead_curr_ctime;comment:'区域 code'"`
	// IsDisabled 该条报价是否被禁用
	IsDisabled bool `gorm:"column:is_disabled;not null;default:0;comment:'该条报价是否被禁用'"`
	// StairPriceType 阶梯类型，语义同价格表 v3
	StairPriceType string `gorm:"column:stair_price_type;type:VARCHAR(64);not null;comment:'阶梯类型，语义同价格表 v3'"`
	// CurrencyType 币种信息（如 CNY USD）
	CurrencyType string `gorm:"column:currency_type;type:VARCHAR(10);not null;unique_index:idx_qvmpi_qvmptid_item_zone_effect_dead_curr_ctime;index:idx_qvmpi_uid_item_zone_effect_dead_curr_ctime;comment:'币种信息（如 CNY USD）'"`
	// EffectTime 生效时刻（含）
	EffectTime base.HNS `gorm:"column:effect_time;not null;unique_index:idx_qvmpi_qvmptid_item_zone_effect_dead_curr_ctime;index:idx_qvmpi_uid_item_zone_effect_dead_curr_ctime;comment:'生效时刻（含），百纳秒'"`
	// DeadTime 失效时刻（不含）
	DeadTime base.HNS `gorm:"column:dead_time;not null;unique_index:idx_qvmpi_qvmptid_item_zone_effect_dead_curr_ctime;index:idx_qvmpi_uid_item_zone_effect_dead_curr_ctime;comment:'失效时刻（不含），百纳秒'"`

	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);unique_index:idx_qvmpi_qvmptid_item_zone_effect_dead_curr_ctime;index:idx_qvmpi_uid_item_zone_effect_dead_curr_ctime;default:now(6)"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME(6);default:now(6) ON UPDATE now(6)"`
}

// 由于 QVM 不是 gorm 认识的缩略词，默认它会生成形如 q_vm_xxx 的表名，因此显式指定表名
func (*QVMPriceItem) TableName() string {
	return qvmPriceItemTableName
}

// QVMPriceItemDao is data access object of QVMPriceItem model
type QVMPriceItemDao struct {
	base *dao.BaseDao
}

// NewQVMPriceItemDao is constructor of QVMPriceItemDao
func NewQVMPriceItemDao(base *dao.BaseDao) *QVMPriceItemDao {
	return &QVMPriceItemDao{
		base: base,
	}
}

func (d *QVMPriceItemDao) BulkInsert(ctx context.Context, rows []QVMPriceItem) error {
	return d.base.BulkInsert(ctx, rows)
}

func (d *QVMPriceItemDao) ListByPriceTableID(
	ptid uint64,
) ([]*QVMPriceItem, error) {
	if ptid == 0 {
		return nil, errors.New("price table ID must not be 0")
	}

	var result []*QVMPriceItem
	err := d.base.Execute(func(i any) error {
		return d.base.Model(&QVMPriceItem{}).
			Where("`qvm_price_table_id` = ?", ptid).
			Find(&result).
			Error
	}, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

type ItemSpec struct {
	ItemCode string
	ZoneCode int64
}

func (d *QVMPriceItemDao) ListByUIDItemSpecsTimePoint(
	uid uint64,
	specs []ItemSpec,
	t base.HNS,
) ([]*QVMPriceItem, error) {
	if len(specs) == 0 {
		return nil, errors.New("QVMPriceItem cannot be listed without explicit item specs")
	}

	specsExpr := squirrel.Or{}
	for _, s := range specs {
		specsExpr = append(specsExpr, squirrel.Eq{
			"item_code": s.ItemCode,
			"zone_code": s.ZoneCode,
		})
	}

	cond := squirrel.And{
		squirrel.Eq{"uid": uid},
		specsExpr,
		squirrel.LtOrEq{"effect_time": t},
		squirrel.Gt{"dead_time": t},
	}

	sql, args, err := cond.ToSql()
	if err != nil {
		return nil, err
	}

	var result []*QVMPriceItem
	err = d.base.Model(&QVMPriceItem{}).
		Where(sql, args...).
		Find(&result).
		Error
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (d *QVMPriceItemDao) ListByUIDItemSpecsTimeRange(
	uid uint64,
	specs []ItemSpec,
	effectTime base.HNS,
	deadTime base.HNS,
) ([]*QVMPriceItem, error) {
	if len(specs) == 0 {
		return nil, errors.New("QVMPriceItem cannot be listed without explicit item specs")
	}

	specsExpr := squirrel.Or{}
	for _, s := range specs {
		specsExpr = append(specsExpr, squirrel.Eq{
			"item_code": s.ItemCode,
			"zone_code": s.ZoneCode,
		})
	}

	cond := squirrel.And{
		squirrel.Eq{"uid": uid},
		specsExpr,
		squirrel.Gt{"dead_time": effectTime},
		squirrel.Lt{"effect_time": deadTime},
	}

	sql, args, err := cond.ToSql()
	if err != nil {
		return nil, err
	}

	var result []*QVMPriceItem
	err = d.base.Model(&QVMPriceItem{}).
		Where(sql, args...).
		Find(&result).
		Error
	if err != nil {
		return nil, err
	}

	return result, nil
}
