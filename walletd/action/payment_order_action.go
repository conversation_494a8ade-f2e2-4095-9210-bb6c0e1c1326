package action

import (
	"context"

	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/sirupsen/logrus"
	"qiniu.io/pay/qpay/payment/model"
	"qiniu.io/pay/walletd/action/adapter"
)

// DeductOrder deduct order api
func (a *PaymentAction) DeductOrder(ctx context.Context, request *pb.OrderPaymentRequest) (*pb.OrderPaymentResponse, error) {

	logger := logging.GetLogger(ctx)

	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : deduct order")

	var (
		trans       []model.PaymentTransaction
		rechargeTxn *model.PaymentTransaction
		err         error
	)
	switch request.PayMode {
	case pb.PAY_MODE_PRE_PAY:
		trans, err = a.orderPaymentService.DeductOrderPrePay(ctx, adapter.BuildDeductOrderRequest(request))
	case pb.PAY_MODE_POST_PAY:
		trans, err = a.orderPaymentService.DeductOrderPostPay(ctx, adapter.BuildDeductOrderRequest(request))
	case pb.PAY_MODE_DEDUCT_PAY:
		rechargeTxn, trans, err = a.orderPaymentService.DeductOrderDeductPay(ctx, adapter.BuildDeductOrderRequest(request))
	}

	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("deduct order failed")
		return nil, err
	}

	return adapter.BuildPbOrderPaymentResponse(trans, rechargeTxn)
}

// ListOrderPaymentTrans list payment trans api
func (a *PaymentAction) ListOrderPaymentTrans(ctx context.Context, request *pb.ListOrderPaymentTransRequest) (*pb.PaymentTransactionList, error) {

	logger := logging.GetLogger(ctx)

	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : list payment trans")

	trans, err := a.orderPaymentService.ListPaymentTrans(ctx, adapter.BuildListOrderPaymentTransRequest(request))

	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("list payment txns failed")
		return nil, err
	}

	txns, err := adapter.BuildPbPaymentTransactions(trans)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
			"trans":   trans,
		}).WithError(err).Error("build pb payment txns failed")
		return nil, err
	}

	return &pb.PaymentTransactionList{Txns: txns}, nil
}

// RefundOrder refund order/po
func (a *PaymentAction) RefundOrder(ctx context.Context, request *pb.OrderRefundRequest) (*pb.PaymentTransactionList, error) {
	logger := logging.GetLogger(ctx)

	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : refund order")

	trans, err := a.orderPaymentService.RefundOrder(ctx, adapter.BuildRefundOrderRequest(request))
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("refund order failed")
		return nil, err
	}

	txns, err := adapter.BuildPbPaymentTransactions(trans)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
			"trans":   trans,
		}).WithError(err).Error("build pb payment txns failed")
		return nil, err
	}

	return &pb.PaymentTransactionList{Txns: txns}, nil
}

// DeductPost 这个方法是为了支持订单的预付费
// 订单那边还是调用的V2的接口
func (a *PaymentAction) DeductPost(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)

	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : deduct order post")

	req, err := adapter.BuildPaymentRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildPaymentRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.DeductPost GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	txn, err := a.postDeductService.Deduct(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("deduct order post failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}
	return pbTransaction, nil
}

// DeductOrderPre 这个方法是为了支持订单的后付费
// 订单那边还是调用的V2的接口
func (a *PaymentAction) DeductOrderPre(ctx context.Context, request *pb.PaymentRequest) (*pb.PaymentTransaction, error) {
	logger := logging.GetLogger(ctx)

	logger.WithFields(logrus.Fields{
		"request": request,
	}).Info("New-Wallet : deduct order pre")

	req, err := adapter.BuildPaymentRequest(request)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("BuildPaymentRequest failed")
		return nil, err
	}

	currencyType, err := a.factory.CurrencyService.GetOnlyOneCurrencyType(ctx, req.UID)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("PaymentAction.DeductOrderPre GetOnlyOneCurrencyType failed")
		return nil, err
	}
	req.CurrencyType = currencyType

	txn, err := a.preDeductService.Deduct(ctx, req)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request": request,
		}).WithError(err).Error("deduct order pre failed")
		return nil, err
	}

	pbTransaction, err := adapter.BuildPbPaymentTransaction(*txn)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"request is": request,
		}).WithError(err).Error("build pb payment transaction failed")
		return nil, err
	}
	return pbTransaction, nil
}
