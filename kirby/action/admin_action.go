package action

import (
	"context"

	"google.golang.org/protobuf/types/known/emptypb"

	pb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/action/adapter"
)

// CreateClient 新建一个推量客户端
func (a *KirbyAction) CreateClient(
	ctx context.Context,
	req *pb.ReqCreateClient,
) (*pb.Client, error) {
	obj, err := a.srv.CreateClient(ctx, req.Name, req.QrnMatches)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbClient(obj)
}

// ListAllClients 列出所有推量客户端的信息
func (a *KirbyAction) ListAllClients(
	ctx context.Context,
	_ *emptypb.Empty,
) (*pb.Clients, error) {
	obj, err := a.srv.ListAllClients(ctx)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbClients(obj)
}

// GetClientDetail 查询一个推量客户端的明细
func (a *KirbyAction) GetClientDetail(
	ctx context.Context,
	req *pb.ReqCodeEpoch,
) (*pb.ClientDetail, error) {
	obj, err := a.srv.GetClientDetail(ctx, req.Code, req.Epoch.AsTime())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbClientDetail(obj)
}

// UpdateClientACL 更新一个推量客户端的 ACL 配置
func (a *KirbyAction) UpdateClientACL(
	ctx context.Context,
	req *pb.ClientResourceACL,
) (*emptypb.Empty, error) {
	panic("not implemented") // TODO: Implement
}

// UpdateClientEnabledStatus 打开或关闭一个推量客户端的推量权限
func (a *KirbyAction) UpdateClientEnabledStatus(
	ctx context.Context,
	req *pb.ReqUpdateClientEnabledStatus,
) (*emptypb.Empty, error) {
	panic("not implemented") // TODO: Implement
}

// CreateRTD 创建一个 RTD
func (a *KirbyAction) CreateRTD(
	ctx context.Context,
	req *pb.ResourceTypeDefinitionDetail,
) (*emptypb.Empty, error) {
	obj := adapter.BuildRTDDetail(req)
	_, err := a.srv.CreateRTD(ctx, obj)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

// UpdateRTD 更新一个 RTD
func (a *KirbyAction) UpdateRTD(
	ctx context.Context,
	req *pb.ResourceTypeDefinitionDetail,
) (*emptypb.Empty, error) {
	panic("not implemented") // TODO: Implement
}

// GetRTDDetail 查询一个 RTD 的明细
func (a *KirbyAction) GetRTDDetail(
	ctx context.Context,
	req *pb.ReqCodeEpoch,
) (*pb.ResourceTypeDefinitionDetail, error) {
	obj, err := a.srv.GetRTDDetailByCode(req.Code, req.Epoch.AsTime())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbRTDDetail(obj), nil
}

// ListSeries 列出满足查询条件的序列
func (a *KirbyAction) ListSeries(
	ctx context.Context,
	req *pb.ReqListSeries,
) (*pb.SeriesDetailList, error) {
	obj, err := a.srv.DP.ListSeries(
		ctx,
		adapter.BuildReqListSeries(req),
	)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbSeriesDetailList(obj), nil
}

// ListAggregatedSeries 列出满足查询条件的汇总序列
func (a *KirbyAction) ListAggregatedSeries(
	ctx context.Context,
	req *pb.ReqListAggregatedSeries,
) (*pb.AggregatedSeriesDetailList, error) {
	obj, err := a.srv.DP.ListAggregatedSeries(
		ctx,
		adapter.BuildReqListAggregatedSeries(req),
	)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbAggregatedSeriesDetailList(obj), nil
}

// TriggerAggregations 触发聚合任务（手动跑数）
func (a *KirbyAction) TriggerAggregations(
	ctx context.Context,
	req *pb.ReqTriggerAggregations,
) (*emptypb.Empty, error) {
	err := a.srv.DP.RunAggregations(
		ctx,
		req.Month.AsTime(),
		req.Since.AsTime(),
		req.Epoch.AsTime(),
	)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

// ProvisionShards 为一个月份建立分片（建表）
//
// 一般在给定月份到来之前，提前调用
func (a *KirbyAction) ProvisionShards(
	ctx context.Context,
	req *pb.ReqProvisionShards,
) (*emptypb.Empty, error) {
	err := a.srv.DP.ProvisionShards(ctx, req.Month.AsTime())
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

// 列出有满足查询条件的序列的 UIDs
func (a *KirbyAction) ListSeriesDistinctUIDs(
	ctx context.Context,
	req *pb.ReqListSeries,
) (*pb.RespUIDs, error) {
	result, err := a.srv.DP.ListSeriesDistinctUIDs(ctx, adapter.BuildReqListSeries(req))
	if err != nil {
		return nil, err
	}
	return &pb.RespUIDs{
		Uids: result,
	}, nil
}

// 列出有满足查询条件的汇总序列的 UIDs
func (a *KirbyAction) ListAggregatedSeriesDistinctUIDs(
	ctx context.Context,
	req *pb.ReqListAggregatedSeries,
) (*pb.RespUIDs, error) {
	result, err := a.srv.DP.ListAggregatedSeriesDistinctUIDs(ctx, adapter.BuildReqListAggregatedSeries(req))
	if err != nil {
		return nil, err
	}
	return &pb.RespUIDs{
		Uids: result,
	}, nil
}
