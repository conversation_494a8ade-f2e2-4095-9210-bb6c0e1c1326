package stat

import (
	"sort"
	"time"

	"qiniu.io/pay/measured/model"
)

// finalizeStat 汇聚并返回该上下文拉到的最终计量。
//
// 需要拿拉量上下文的读锁
func (c *statQueryContext) finalizeStat() *model.StatResp {
	c.dataMu.RLock()
	defer c.dataMu.RUnlock()

	return doFinalizeStat(c.axesData, c.postprocessStrat)
}

func doFinalizeStat(
	axesData []*model.StatResp,
	strategy postprocessStrategy,
) *model.StatResp {
	// fast path
	if len(axesData) == 1 {
		// NOTE: fast path 不会对返回的计量点排序，如果这是个问题，可以加个 sort.SliceIsSorted 判断，如果乱序就排一下

		// 理论上这里应该 memcpy 一次，但 Golang 不能清晰表达这里是否让渡所有权
		// (其实就是 Rust 的 self or &self)，所以直接返回了
		return axesData[0]
	}
	if len(axesData) == 0 {
		// 虽然不太会出现这个情形但还是返回一下
		return &model.StatResp{
			Data: nil,
		}
	}

	// 无视响应计量点顺序的合量
	m := make(map[int64]int64)
	for _, axis := range axesData {
		for _, p := range axis.Data {
			ts := p.Time.UnixNano()

			switch strategy {
			case postprocessStrategySum:
				// 第一次遇到 m[ts] 是零值，不用单独处理
				m[ts] += p.Value

			case postprocessStrategyMax:
				// NOTE: 虽然正常不会有负值的计量点，但这里仍然做一下兼容处理
				// 否则第一次遇到 m[ts] 是 0，等于后续最大值逻辑会确保每个值都 >= 0，意味着会吃掉所有负值的点
				// 这会掩盖不符合预期的计量响应
				if oldValue, ok := m[ts]; ok {
					if oldValue < p.Value {
						m[ts] = p.Value
					}
				} else {
					m[ts] = p.Value
				}

			default:
				panic("should never happen: unknown postprocess strategy")
			}
		}
	}

	// map -> list
	l := make([]model.StatPoint, len(m))
	i := 0
	for ts, val := range m {
		l[i].Time = time.Unix(0, ts)
		l[i].Value = val
		i++
	}

	// 按时间升序排序最终的计量点
	sort.Slice(l, func(i int, j int) bool {
		return l[i].Time.Before(l[j].Time)
	})

	return &model.StatResp{
		Data: l,
	}
}
