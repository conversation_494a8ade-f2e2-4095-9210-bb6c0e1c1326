package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/measureproxyd/model"
)

func TestLegacyStatRespRoundTrip(t *testing.T) {
	x := []model.RespTableItemWithoutGroup{
		{
			Time: time.Date(2019, 8, 1, 0, 0, 0, 0, time.Local),
			Values: map[string]int64{
				"flow":       233,
				"bandwidth":  456,
				"concurrent": 789,
			},
		},
		{
			Time: time.Date(2019, 8, 1, 0, 5, 0, 0, time.Local),
			Values: map[string]int64{
				"flow":       1233,
				"bandwidth":  1456,
				"concurrent": 1789,
			},
		},
	}

	y, err := BuildPbLegacyStatResp(x)
	assert.NoError(t, err)

	z, err := BuildModelLegacyStatResp(y)
	assert.NoError(t, err)

	assert.Equal(t, len(x), len(z))
	assert.Equal(t, 2, len(z))
	for i := range x {
		a := x[i]
		b := z[i]
		assert.Equal(t, a.Time.UnixNano(), b.Time.UnixNano())
		assert.Equal(t, a.Values, b.Values)
	}
}
