package dataplaneModel

import (
	"time"

	"github.com/jinzhu/gorm"

	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
)

type DataTableManager struct {
	db    *gorm.DB
	debug bool
}

func NewDataTableManager(d *gorm.DB, debug bool) *DataTableManager {
	return &DataTableManager{
		db:    d,
		debug: debug,
	}
}

func (m *DataTableManager) GetShardForQRN(
	g adminModel.Granularity,
	t time.Time,
	loc *time.Location,
	qrn string,
) *DataTableInstance {
	shardInfo := computeDataTableShard(dataTableKindSeries, g, t, loc, qrn)
	return m.getShard(&shardInfo)
}

func (m *DataTableManager) getShardForOneAggregatedSeries(
	g adminModel.Granularity,
	t time.Time,
	loc *time.Location,
	key string,
) *DataTableInstance {
	shardInfo := computeDataTableShard(dataTableKindAggregatedSeries, g, t, loc, key)
	return m.getShard(&shardInfo)
}

func (m *DataTableManager) getShard(
	shardInfo *computedShardInfo,
) *DataTableInstance {
	return &DataTableInstance{
		shardName:   shardInfo.name,
		tbl:         m.db.Table(shardInfo.name),
		numPoints:   shardInfo.numPoints,
		extraChecks: m.debug,
	}
}

func (m *DataTableManager) QueryAggregatedSeriesData(
	month time.Time,
	loc *time.Location,
	md *aggSeriesMetadataRow,
	fillZeroes bool,
) ([]*pb.StatPoint, error) {
	shard := m.getShardForOneAggregatedSeries(md.G, month, loc, md.AggSeriesKey)
	rows, err := shard.getSeries(md.AggSeriesKey)
	if err != nil {
		return nil, err
	}

	sparseRepr, err := sparseReprFromRows(md.G, loc, rows)
	if err != nil {
		return nil, err
	}

	if !fillZeroes {
		return sparseRepr, nil
	}

	return fillZeroesForSparseRepr(md.G, md.Start, md.End, loc, sparseRepr), nil
}
