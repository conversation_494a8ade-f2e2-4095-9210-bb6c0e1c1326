package model

import (
	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/dao"
)

// CouponDao is data access object for coupon service.
type CouponDao struct {
	base              *dao.BaseDao
	CouponBatch       *CouponBatchDao
	Coupon            *CouponModelDao
	CouponLockRecord  *CouponLockRecordDao
	CouponLockDetail  *CouponLockDetailDao
	CouponUsageRecord *CouponUsageRecordDao
}

// NewCouponDao constructs a CouponDao.
func NewCouponDao(base *dao.BaseDao) *CouponDao {
	return &CouponDao{
		base:              base,
		CouponBatch:       NewCouponBatchDao(base),
		Coupon:            NewCouponModelDao(base),
		CouponLockRecord:  NewCouponLockRecordDao(base),
		CouponLockDetail:  NewCouponLockDetailDao(base),
		CouponUsageRecord: NewCouponUsageRecordDao(base),
	}
}

// DoTransaction do a transaction
func (d *CouponDao) DoTransaction(fn func(*CouponDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		couponDao := NewCouponDao(base)
		return fn(couponDao)
	})
}

// RegisterMigrate migrate all models
func RegisterMigrate(db *gorm.DB) {
	db.AutoMigrate(&CouponBatch{})
	db.AutoMigrate(&Coupon{})
	db.AutoMigrate(&CouponLockRecord{})
	db.AutoMigrate(&CouponLockDetail{})
	db.AutoMigrate(&CouponUsageRecord{})
}
