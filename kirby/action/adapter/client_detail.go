package adapter

import (
	"time"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/kirby"
	"google.golang.org/protobuf/types/known/timestamppb"

	"qiniu.io/pay/kirby/model/adminModel"
)

// BuildClientDetail converts pb.ClientDetail to the model layer type.
func BuildClientDetail(
	in *pb.ClientDetail,
) (*adminModel.ClientDetail, error) {
	err := in.CreatedAt.CheckValid()
	if err != nil {
		return nil, err
	}

	return &adminModel.ClientDetail{
		ClientID: in.ClientId,
		Name:     in.Name,
		// Enabled:   in.Enabled,
		QRNMatches: in.QrnMatches,
		CreatedAt:  in.CreatedAt.AsTime(),
		UpdatedAt:  time.Time{},
	}, nil
}

// BuildPbClientDetail converts adminModel.ClientDetail to the pb layer type.
func BuildPbClientDetail(
	in *adminModel.ClientDetail,
) (*pb.ClientDetail, error) {
	return &pb.ClientDetail{
		ClientId: in.ClientID,
		Name:     in.Name,
		// Enabled:  in.Enabled,
		QrnMatches: in.QRNMatches,
		CreatedAt:  timestamppb.New(in.CreatedAt),
	}, nil
}

// BuildPbClientDetails converts from []adminModel.ClientDetail to the pb layer type.
func BuildPbClientDetails(in []adminModel.ClientDetail) ([]*pb.ClientDetail, error) {
	result := make([]*pb.ClientDetail, len(in))
	for i, x := range in {
		obj, err := BuildPbClientDetail(&x)
		if err != nil {
			return nil, errors.Trace(err)
		}
		result[i] = obj
	}
	return result, nil
}

// BuildClientDetails converts from []*pb.ClientDetail to the model layer type.
func BuildClientDetails(in []*pb.ClientDetail) ([]adminModel.ClientDetail, error) {
	result := make([]adminModel.ClientDetail, len(in))
	for i, x := range in {
		obj, err := BuildClientDetail(x)
		if err != nil {
			return nil, errors.Trace(err)
		}
		result[i] = *obj
	}
	return result, nil
}
