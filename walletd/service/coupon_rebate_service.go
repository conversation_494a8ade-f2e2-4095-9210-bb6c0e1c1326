package service

import (
	"context"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	"qiniu.io/pay/walletd/model"
)

// GetCouponRebateByID 通过ID获取返现优惠券
func (s *WalletBizService) GetCouponRebateByID(
	ctx context.Context,
	id uint64,
) (*model.CouponRebate, error) {
	return s.walletDao.CouponRebate.GetByID(id, s.cacheExpires)
}

// GetCouponRebateByCode 通过 code 获取返现优惠券
func (s *WalletBizService) GetCouponRebateByCode(
	ctx context.Context,
	code string,
) (*model.CouponRebate, error) {
	return s.walletDao.CouponRebate.GetByCode(code, s.cacheExpires)
}

// GetCouponRebateWithIsBoundByCode 通过 code 获取返现优惠券
func (s *WalletBizService) GetCouponRebateWithIsBoundByCode(
	ctx context.Context,
	code string,
) (*CouponRebateWithIsBound, error) {
	couponRebate, err := s.walletDao.CouponRebate.GetByCode(code, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	isBound, err := s.isCouponBoundToUserByCouponID(ctx, couponRebate.ID, model.CouponTypeRebate, 0)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return &CouponRebateWithIsBound{
		CouponRebate: *couponRebate,
		IsBound:      isBound,
	}, nil
}

// CreateCouponRebate 创建返现优惠券
func (s *WalletBizService) CreateCouponRebate(
	ctx context.Context,
	rebate *model.CouponRebate,
) (*model.CouponRebate, error) {
	rebate.ID = 0
	err := s.walletDao.CouponRebate.Save(rebate, s.cacheExpires)
	return rebate, err
}

// UpdateCouponRebateByID 按照 ID 更新返现优惠券
func (s *WalletBizService) UpdateCouponRebateByID(
	ctx context.Context,
	id uint64,
	rebate *model.CouponRebate,
) (*model.CouponRebate, error) {
	rebate.ID = id
	err := s.walletDao.CouponRebate.Save(rebate, s.cacheExpires)
	return rebate, err
}

// UpdateCouponRebateByCode 按照 code 更新返现优惠券
func (s *WalletBizService) UpdateCouponRebateByCode(
	ctx context.Context,
	code string,
	rebate *model.CouponRebate,
) (*model.CouponRebate, error) {
	rebate.Code = code
	err := s.walletDao.CouponRebate.Save(rebate, s.cacheExpires)
	return rebate, err
}

// DeleteCouponRebateByID 按照 ID 删除返现优惠券
func (s *WalletBizService) DeleteCouponRebateByID(
	ctx context.Context,
	id uint64,
) (*model.CouponRebate, error) {
	rebate, err := s.GetCouponRebateByID(ctx, id)
	if err != nil {
		return nil, errors.Annotate(err, "query coupon rebate failed").WithField("id", id)
	}
	err = s.walletDao.CouponRebate.Delete(rebate)
	return rebate, err
}

// DeleteCouponRebateByCode 按照 code 删除返现优惠券
func (s *WalletBizService) DeleteCouponRebateByCode(
	ctx context.Context,
	code string,
) (*model.CouponRebate, error) {
	rebate, err := s.GetCouponRebateByCode(ctx, code)
	if err != nil {
		return nil, errors.Annotate(err, "query coupon rebate failed").
			WithField("code", code)
	}
	err = s.walletDao.CouponRebate.Delete(rebate)
	return rebate, err
}

// CouponRebateWithIsBound 包含 couponRebate 和 isBound 信息
type CouponRebateWithIsBound struct {
	CouponRebate model.CouponRebate
	IsBound      bool
}

// ListCouponRebatesByConds 按条件列举返现优惠券
func (s *WalletBizService) ListCouponRebatesByConds(
	ctx context.Context,
	codePattern string,
	offset int,
	limit int,
) ([]*CouponRebateWithIsBound, error) {
	couponRebates, err := s.walletDao.CouponRebate.ListByConds(&model.ListCouponConds{
		CodePattern: codePattern,
		Offset:      offset,
		Limit:       limit,
	}, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 构造 GetBoundCouponByCouponIDs 入参，couponRebateID 数组
	couponRebateIDs := make([]uint64, len(couponRebates))
	for i, rebate := range couponRebates {
		couponRebateIDs[i] = rebate.ID
	}

	boundCouponRebates, err := s.walletDao.CouponUserMap.
		GetBoundCouponByCouponIDs(couponRebateIDs, model.CouponTypeRebate, 0)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// 构造 map
	boundCouponRebateMap := make(map[uint64]bool)
	for _, rebate := range boundCouponRebates {
		boundCouponRebateMap[rebate] = true
	}

	couponRebateWithIsBoundList := make([]*CouponRebateWithIsBound, len(couponRebates))
	for i, couponRebate := range couponRebates {
		couponRebateWithIsBoundList[i] = &CouponRebateWithIsBound{
			CouponRebate: couponRebate,
			IsBound:      boundCouponRebateMap[couponRebate.ID],
		}
	}
	return couponRebateWithIsBoundList, nil
}

// CountAllCouponRebates 获取所有返现优惠券数量
func (s *WalletBizService) CountAllCouponRebates(
	ctx context.Context,
) (uint64, error) {
	return s.walletDao.CouponRebate.CountAll(s.cacheExpires)
}

// ListCouponRebatesByScopeID 通过计费范围列举返现优惠券
func (s *WalletBizService) ListCouponRebatesByScopeID(
	ctx context.Context,
	scopeID uint64,
	offset int,
	limit int,
) ([]model.CouponRebate, error) {
	return s.walletDao.CouponRebate.ListByScopeID(scopeID, offset, limit, s.cacheExpires)
}

// CountCouponRebatesByScopeID 通过计费范围获取返现优惠券数量
func (s *WalletBizService) CountCouponRebatesByScopeID(
	ctx context.Context,
	scopeID uint64,
) (uint64, error) {
	return s.walletDao.CouponRebate.CountByScopeID(scopeID, s.cacheExpires)
}

// ListCouponRebatesByUID 通过用户ID列举返现优惠券
func (s *WalletBizService) ListCouponRebatesByUID(
	ctx context.Context,
	uid uint64,
	offset int,
	limit int,
) ([]model.CouponRebate, error) {
	cs, err := s.walletDao.CouponUserMap.ListByUID(uid,
		model.CouponTypeRebate, offset, limit, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "list coupon rebate failed").WithField("uid", uid)
	}

	ids := make([]uint64, len(cs))
	for i, c := range cs {
		ids[i] = c.CouponID
	}
	if len(ids) <= 0 {
		return nil, nil
	}
	return s.walletDao.CouponRebate.ListByIDs(ids, s.cacheExpires)
}

// CountCouponRebatesByUID 通过用户ID获取返现优惠券数量
func (s *WalletBizService) CountCouponRebatesByUID(
	ctx context.Context,
	uid uint64,
) (uint64, error) {
	return s.walletDao.CouponUserMap.CountByUID(uid, model.CouponTypeRebate, s.cacheExpires)
}

type CouponRebateWithScope struct {
	CouponRebate *model.CouponRebate
	Scope        *ScopeDetail
	Zones        []*ZoneInfo
}

// GetScope implements CouponWithScope interface(GetScope)
func (c *CouponRebateWithScope) GetScope() *ScopeDetail {
	return c.Scope
}

// GetZones implements CouponWithScope interface(GetZones)
func (c *CouponRebateWithScope) GetZones() []*ZoneInfo {
	return c.Zones
}

// GetCouponEffectTime implements CouponWithScope interface(GetCouponEffectTime)
func (c *CouponRebateWithScope) GetCouponEffectTime() base.HNS {
	return c.CouponRebate.EffectTime
}

// GetCouponDeadTime implements CouponWithScope interface(GetCouponDeadTime)
func (c *CouponRebateWithScope) GetCouponDeadTime() base.HNS {
	return c.CouponRebate.DeadTime
}

// BatchCreateBindCouponRebate 批量创建、绑定返利优惠券
func (s *WalletBizService) BatchCreateBindCouponRebate(
	ctx context.Context,
	uid uint64,
	rebateWithScopes []*CouponRebateWithScope,
) (bindingChanged bool, err error) {
	for _, rebateWithScope := range rebateWithScopes {
		thisBindingChanged, err := s.createBindCouponRebate(ctx, uid, rebateWithScope)
		if err != nil {
			// 日志在 createBindCouponRebate 打过了
			return false, err
		}
		bindingChanged = bindingChanged || thisBindingChanged
	}
	return bindingChanged, nil
}

// createBindCouponRebate 创建、绑定一张返利优惠券
func (s *WalletBizService) createBindCouponRebate(
	ctx context.Context,
	uid uint64,
	rebateWithScope *CouponRebateWithScope,
) (bindingChanged bool, err error) {
	log := logging.GetLogger(ctx)

	// 0. 如果传入的 rebate 有 code 就检查幂等性
	var needToCreate bool
	var needToBind bool
	inputCouponCode := rebateWithScope.CouponRebate.Code
	if inputCouponCode != "" {
		// 0a. 有没有这个 code 的券?
		couponRebate, err := s.GetCouponRebateByCode(ctx, inputCouponCode)
		if err != nil {
			if errors.Cause(err) != dao.ErrRecordNotFound {
				log.Errorf("<WalletBizService.createBindCouponRebate> check existing rebate error: %+v", err)
				return false, err
			}

			// 这个 code 的 rebate 没有创建，需要创建，当然也需要绑定
			needToCreate = true
			needToBind = true
		} else {
			// err == nil
			// 能查出来这个 rebate
			needToCreate = false

			// 检查是否已经绑定到给定用户了
			// XXX 这里没有处理涉及多个 zone 的情况下，部分绑定成功，部分失败的情况
			isBound, err := s.isCouponBoundToUserByCouponID(ctx, couponRebate.ID, model.CouponTypeRebate, uid)
			if err != nil {
				log.Errorf("<WalletBizService.createBindCouponRebate> check rebate bound status error: %+v", err)
				return false, err
			}

			needToBind = !isBound
		}
	} else {
		// 无条件创建一个新的
		needToCreate = true
		needToBind = true

		// 替调用方生成一个 code
		rebateWithScope.CouponRebate.Code = s.genCouponCode(uid, rebateWithScope.CouponRebate.Type)
	}

	log.WithFields(logrus.Fields{
		"inputCouponCode": inputCouponCode,
		"couponCodeToUse": rebateWithScope.CouponRebate.Code,
		"uid":             uid,
		"needToCreate":    needToCreate,
		"needToBind":      needToBind,
	}).Info("<WalletBizService.createBindCouponRebate> idempotence check info")

	if needToCreate {
		// 1. 创建 scope
		scope := &model.Scope{
			IsAll:  rebateWithScope.Scope.IsAll,
			Remark: rebateWithScope.CouponRebate.Remark,
		}
		scopeRet, err1 := s.CreateScope(ctx, scope)
		if err1 != nil {
			log.Errorf("<WalletBizService.createBindCouponRebate> CreateScope error: %+v", err1)
			return false, err1
		}

		// 2. 创建 scopeDetail
		scopeDetail := rebateWithScope.Scope
		scopeDetail.ScopeID = scopeRet.ID
		scopeDetailRet, err1 := s.CreateScopeDetail(ctx, scopeDetail)
		if err1 != nil {
			log.Errorf("<WalletBizService.createBindCouponRebate> CreateScopeDetail error: %+v", err1)
			return false, err1
		}

		// 3. 创建 couponRebate
		rebateWithScope.CouponRebate.ScopeID = scopeDetailRet.ScopeID
		rebateWithScope.CouponRebate.ID = 0
		err1 = s.walletDao.CouponRebate.Save(rebateWithScope.CouponRebate, s.cacheExpires)
		if err1 != nil {
			log.Errorf("<WalletBizService.createBindCouponRebate> CouponRebate.Save error: %+v", err1)
			return false, err1
		}
	}

	if needToBind {
		// 4. 绑定优惠到用户，遍历区域，每个区域的绑定一遍
		for _, zoneInfo := range rebateWithScope.Zones {
			couponUserMapRet, err2 := s.BindCoupon(ctx, &BindCouponParam{
				CouponID:   rebateWithScope.CouponRebate.ID,
				CouponType: model.CouponTypeRebate,
				UID:        uid,
				ZoneID:     zoneInfo.ZoneID,
				EffectTime: rebateWithScope.CouponRebate.EffectTime,
				DeadTime:   rebateWithScope.CouponRebate.DeadTime,
			})
			if err2 != nil {
				log.Errorf("<WalletBizService.createBindCouponRebate> BindCoupon error: %+v", err2)
				return false, err2
			}

			// 需要调用 price-shim 写 oid
			err2 = s.saveOidMapRecord(ctx, couponUserMapRet.ID, "rebate_op")
			if err2 != nil {
				log.Errorf("<WalletBizService.createBindCouponRebate> saveOidMapRecord error: %+v", err2)
				return false, err2
			}

			bindingChanged = true
		}
	}

	return bindingChanged, nil
}
