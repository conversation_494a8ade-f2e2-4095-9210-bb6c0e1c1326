package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/walletd/model"
	"qiniu.io/pay/walletd/service"
)

func TestScopeRoundTrip(t *testing.T) {
	x := &model.Scope{
		ID:        233,
		IsAll:     true,
		Remark:    "test remark",
		CreatedAt: time.Now().Round(0),
		UpdatedAt: time.Now().Round(0),
	}

	y, err := BuildPbScope(x)
	assert.NoError(t, err)

	z, err := BuildScope(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.Is<PERSON>ll, z.<PERSON>ll)
	assert.Equal(t, x.Remark, z.Remark)
	assert.Equal(t, x.CreatedAt.Unix<PERSON>ano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
}

func TestScopeDetailRoundTrip(t *testing.T) {
	x := &service.ScopeDetail{
		ScopeID: 233,
		IsAll:   true,
		Products: []model.ScopeProductMap{
			{
				ID:         2333,
				ScopeID:    233,
				ProductID:  456,
				IsExcluded: true,
				CreatedAt:  time.Now().Round(0),
				UpdatedAt:  time.Now().Round(0),
			},
		},
		ItemGroups: []model.ScopeItemGroupMap{
			{
				ID:         2334,
				ScopeID:    233,
				GroupID:    567,
				IsExcluded: true,
				CreatedAt:  time.Now().Round(0),
				UpdatedAt:  time.Now().Round(0),
			},
		},
		Items: []model.ScopeItemMap{
			{
				ID:         2335,
				ScopeID:    233,
				ItemID:     678,
				IsExcluded: true,
				CreatedAt:  time.Now().Round(0),
				UpdatedAt:  time.Now().Round(0),
			},
		},
	}

	y, err := BuildPbScopeDetail(x)
	assert.NoError(t, err)

	z, err := BuildScopeDetail(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ScopeID, z.ScopeID)
	assert.Equal(t, x.IsAll, z.IsAll)

	assert.Equal(t, len(x.Products), len(z.Products))
	for i, x1 := range x.Products {
		z1 := z.Products[i]
		assert.Equal(t, x1.ID, z1.ID)
		assert.Equal(t, x1.ScopeID, z1.ScopeID)
		assert.Equal(t, x1.ProductID, z1.ProductID)
		assert.Equal(t, x1.IsExcluded, z1.IsExcluded)
		assert.Equal(t, x1.CreatedAt.UnixNano(), z1.CreatedAt.UnixNano())
		assert.Equal(t, x1.UpdatedAt.UnixNano(), z1.UpdatedAt.UnixNano())
	}

	assert.Equal(t, len(x.ItemGroups), len(z.ItemGroups))
	for i, x1 := range x.ItemGroups {
		z1 := z.ItemGroups[i]
		assert.Equal(t, x1.ID, z1.ID)
		assert.Equal(t, x1.ScopeID, z1.ScopeID)
		assert.Equal(t, x1.GroupID, z1.GroupID)
		assert.Equal(t, x1.IsExcluded, z1.IsExcluded)
		assert.Equal(t, x1.CreatedAt.UnixNano(), z1.CreatedAt.UnixNano())
		assert.Equal(t, x1.UpdatedAt.UnixNano(), z1.UpdatedAt.UnixNano())
	}

	assert.Equal(t, len(x.Items), len(z.Items))
	for i, x1 := range x.Items {
		z1 := z.Items[i]
		assert.Equal(t, x1.ID, z1.ID)
		assert.Equal(t, x1.ScopeID, z1.ScopeID)
		assert.Equal(t, x1.ItemID, z1.ItemID)
		assert.Equal(t, x1.IsExcluded, z1.IsExcluded)
		assert.Equal(t, x1.CreatedAt.UnixNano(), z1.CreatedAt.UnixNano())
		assert.Equal(t, x1.UpdatedAt.UnixNano(), z1.UpdatedAt.UnixNano())
	}
}
