package adapter

import (
	"time"

	"github.com/qbox/bo-base/v4/base"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/wallet"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/coupon/model"
)

// BuildCoupon converts pb.Coupon to the model layer type.
func BuildCoupon(
	in *pb.Coupon,
) (*model.Coupon, error) {
	return &model.Coupon{
		ID:         in.Id,
		BatchID:    in.BatchId,
		UID:        in.Uid,
		Code:       in.Code,
		BindMethod: model.CouponBindMethod(in.BindMethod),
		State:      model.CouponState(in.State),
		Money:      base.Money(in.Money),
		Balance:    base.Money(in.Balance),
		BoundAt:    base.NewHNS(in.BoundAt.AsTime()),
		EffectTime: base.NewHNS(in.EffectTime.AsTime()),
		DeadTime:   base.NewHNS(in.DeadTime.AsTime()),
		CreatedAt:  in.CreatedAt.AsTime(),
		UpdatedAt:  in.UpdatedAt.AsTime(),
	}, nil
}

// BuildPbCoupon converts model.Coupon to the pb layer type.
func BuildPbCoupon(
	in *model.Coupon,
) (*pb.Coupon, error) {
	return &pb.Coupon{
		Id:         in.ID,
		BatchId:    in.BatchID,
		Uid:        in.UID,
		Code:       in.Code,
		BindMethod: pb.CouponBindMethod(in.BindMethod),
		State:      pb.CouponState(in.State),
		Money:      in.Money.ToInt64(),
		Balance:    in.Balance.ToInt64(),
		BoundAt:    timestamppb.New(in.BoundAt.TimeIn(time.UTC)),
		EffectTime: timestamppb.New(in.EffectTime.TimeIn(time.UTC)),
		DeadTime:   timestamppb.New(in.DeadTime.TimeIn(time.UTC)),
		CreatedAt:  timestamppb.New(in.CreatedAt),
		UpdatedAt:  timestamppb.New(in.UpdatedAt),
	}, nil
}

// BuildPbCoupons converts from []model.Coupon to the pb layer type.
func BuildPbCoupons(in []model.Coupon) ([]*pb.Coupon, error) {
	result := make([]*pb.Coupon, len(in))
	for i, x := range in {
		obj, err := BuildPbCoupon(&x)
		if err != nil {
			return nil, errors.Trace(err)
		}
		result[i] = obj
	}
	return result, nil
}

// BuildCoupons converts from []*pb.Coupon to the model layer type.
func BuildCoupons(in []*pb.Coupon) ([]model.Coupon, error) {
	result := make([]model.Coupon, len(in))
	for i, x := range in {
		obj, err := BuildCoupon(x)
		if err != nil {
			return nil, errors.Trace(err)
		}
		result[i] = *obj
	}
	return result, nil
}
