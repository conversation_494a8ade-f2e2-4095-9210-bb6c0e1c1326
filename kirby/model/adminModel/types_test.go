package adminModel_test

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/kirby/model/adminModel"
)

func TestSPValueTypeORM(t *testing.T) {
	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	type testTable struct {
		Foo adminModel.SPValueType `gorm:"type:tinyint;not null"`
	}
	testWrap.DB().AutoMigrate(&testTable{})

	table := testWrap.DB().Model(&testTable{})

	err = table.Save(&testTable{
		Foo: adminModel.SPValueTypeStringEnum,
	}).Error
	assert.NoError(t, err)

	var val testTable
	err = table.First(&val).Error
	assert.NoError(t, err)
	assert.Equal(t, adminModel.SPValueTypeStringEnum, val.Foo)

}

func TestStringEnumDeclORM(t *testing.T) {
	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	type testTable struct {
		Foo *adminModel.StringEnumDecl `gorm:"type:VARCHAR(4096);not null"`
	}
	testWrap.DB().AutoMigrate(&testTable{})

	table := testWrap.DB().Model(&testTable{})

	x := testTable{
		Foo: &adminModel.StringEnumDecl{
			Variants: []adminModel.StringEnumVariant{
				{Value: "foo", Desc: "这是"},
				{Value: "bar", Desc: "一些"},
				{Value: "baz", Desc: "文案"},
			},
		},
	}

	err = table.Save(&x).Error
	assert.NoError(t, err)

	var val testTable
	err = table.First(&val).Error
	assert.NoError(t, err)
	assert.Equal(t, x.Foo, val.Foo)

}

func TestGranularityORM(t *testing.T) {
	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	type testTable struct {
		Foo adminModel.Granularity `gorm:"type:tinyint;not null"`
	}
	testWrap.DB().AutoMigrate(&testTable{})

	table := testWrap.DB().Model(&testTable{})

	err = table.Save(&testTable{
		Foo: adminModel.Granularity5Min,
	}).Error
	assert.NoError(t, err)

	var val testTable
	err = table.First(&val).Error
	assert.NoError(t, err)
	assert.Equal(t, adminModel.Granularity5Min, val.Foo)
}

func TestSPMatchExprORM(t *testing.T) {
	testWrap, err := test.NewTestWrap(t)
	if err != nil {
		t.Fatalf("NewTestWrap failed: %v", err)
	}

	type testTable struct {
		Foo *adminModel.SPMatchExpr `gorm:"type:VARCHAR(4096);not null"`
	}
	testWrap.DB().AutoMigrate(&testTable{})

	table := testWrap.DB().Model(&testTable{})

	x := testTable{
		Foo: &adminModel.SPMatchExpr{
			"foo": adminModel.SPMatcher{StringEnumValueIn: []string{"1"}},
			"bar": adminModel.SPMatcher{StringEnumValueIn: []string{"22", "333"}},
		},
	}

	err = table.Save(&x).Error
	assert.NoError(t, err)

	var val testTable
	err = table.First(&val).Error
	assert.NoError(t, err)
	assert.Equal(t, x.Foo, val.Foo)
}
