package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/model"
)

// BuildPbCouponStates converts from []model.CouponState to the pb layer type.
func BuildPbCouponStates(in []model.CouponState) []pb.CouponState {
	result := make([]pb.CouponState, len(in))
	for i, x := range in {
		result[i] = pb.CouponState(x)
	}
	return result
}

// BuildCouponStates converts from []pb.CouponState to the model layer type.
func BuildCouponStates(in []pb.CouponState) []model.CouponState {
	result := make([]model.CouponState, len(in))
	for i, x := range in {
		result[i] = model.CouponState(x)
	}
	return result
}
