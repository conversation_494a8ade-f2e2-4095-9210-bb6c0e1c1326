package action

import (
	"context"
	"errors"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"

	"github.com/jinzhu/gorm"
	"google.golang.org/protobuf/types/known/emptypb"

	pb "github.com/qbox/pay-sdk/plan"
	"qiniu.io/pay/pland/action/adapter"
	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/models/enums"
)

// CreateOrUpdatePlan 创建or更新 plan
func (p *PlanAction) CreateOrUpdatePlan(
	ctx context.Context,
	in *pb.PlanParam,
) (*pb.Plan, error) {
	plan, err := adapter.BuildPlan(in)
	if err != nil {
		return nil, err
	}
	plan, err = p.srv.SavePlan(ctx, plan)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPlan(plan)
}

// EffectDuplicate 获取重复生效时间的 plan
func (p *PlanAction) EffectDuplicate(
	ctx context.Context,
	in *pb.PlanParam,
) (*pb.Plan, error) {
	plan, err := adapter.BuildPlan(in)
	if err != nil {
		return nil, err
	}
	plan, err = p.srv.CheckEffectDuplicate(ctx, plan)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return adapter.BuildPbPlan(&models.Plan{})
	}
	return adapter.BuildPbPlan(plan)
}

// GetOnlinePlanByLevel 获取某 level 在线的 plan 配置
func (p *PlanAction) GetOnlinePlanByLevel(
	ctx context.Context,
	in *pb.OnlinePlanParam,
) (*pb.Plan, error) {
	if in.GetLevel() == "" {
		return nil, errors.New("invalid param")
	}
	plan, err := p.srv.GetOnlineByLevel(ctx, enums.PlanLevel(in.GetLevel()))
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPlan(plan)
}

// PlanHistory 获取某 plan 的历史版本
func (p *PlanAction) PlanHistory(
	ctx context.Context,
	in *pb.PlanHistoryParam,
) (*pb.PlanList, error) {
	if in.GetLevel() == "" || !enums.PlanLevel(in.GetLevel()).Valid() {
		return nil, errors.New("invalid param")
	}
	if in.GetVersion() == 0 {
		offset, limit := p.Paging(in)
		// 查询所有版本
		plans, err := p.srv.List(
			ctx,
			in.GetLevel(),
			offset,
			limit,
		)
		if err != nil {
			return nil, err
		}
		return adapter.BuildPbPlanList(plans)
	}
	// 查询特定版本
	plan, err := p.srv.GetByLevelAndVersion(
		ctx,
		enums.PlanLevel(in.GetLevel()),
		in.GetVersion(),
	)
	if err != nil {
		// 没有查询到特定版本的记录，对外不报错，保证此接口对外返回格式，统一只返回 []
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return adapter.BuildPbPlanList([]*models.Plan{})
		}
		return nil, err
	}
	return adapter.BuildPbPlanList([]*models.Plan{plan})
}

// ListUserPlans 用户 plan 列表
func (p *PlanAction) ListUserPlans(
	ctx context.Context,
	in *pb.UserPlanListParam,
) (*pb.UserPlanList, error) {
	offset, limit := p.Paging(in)
	// 这里默认只查询出当月的 user plan
	month := base.ThisMonth(time.Now().In(tz.MustLocationFromCtx(ctx)))
	userPlans, planMap, err := p.srv.ListUserPlanList(
		ctx,
		in.GetUid(),
		enums.PlanLevel(in.GetLevel()),
		month,
		offset,
		limit,
	)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbUserPlanList(userPlans, planMap)
}

// CustomizeUserPlan 标记为企业定制 plan
func (p *PlanAction) CustomizeUserPlan(
	ctx context.Context,
	in *pb.CustomizeParam,
) (*pb.CustomizeResp, error) {
	loc := tz.MustLocationFromCtx(ctx)

	err := p.srv.CustomizeUserPlan(
		ctx,
		in.GetUid(),
		time.Now().In(loc),
	)
	if err != nil {
		return nil, err
	}
	return &pb.CustomizeResp{}, nil
}

// UnCustomizeUserPlan 取消标记为企业定制 plan
func (p *PlanAction) UnCustomizeUserPlan(
	ctx context.Context,
	in *pb.UnCustomizeParam,
) (*pb.UnCustomizeResp, error) {
	loc := tz.MustLocationFromCtx(ctx)

	err := p.srv.UnCustomizeUserPlan(
		ctx,
		in.GetUid(),
		time.Now().In(loc),
	)
	if err != nil {
		return nil, err
	}
	return &pb.UnCustomizeResp{}, nil
}

// AdminSubscribePlan 后台变更 plan
func (p *PlanAction) AdminSubscribePlan(
	ctx context.Context,
	in *pb.SubscribePlanParam,
) (*pb.SubscribePlanResp, error) {
	loc := tz.MustLocationFromCtx(ctx)

	param := adapter.BuildSubscribePlanParam(in)
	if !param.EffectType.Valid() {
		return nil, errors.New("effect type invalid")
	}
	param.SubscribeSrc = enums.SubscribeSrcAdmin
	err := p.srv.AdminSubscribePlan(
		ctx,
		param,
		time.Now().In(loc),
	)
	if err != nil {
		return nil, err
	}
	return &pb.SubscribePlanResp{}, nil
}

// UserSubscribeTicker 每月续订任务定时器
func (p *PlanAction) UserSubscribeTicker(
	ctx context.Context,
	param *pb.SubscribeTickerParam,
) (*emptypb.Empty, error) {
	loc := tz.MustLocationFromCtx(ctx)

	// month 是要续订的月份
	err := p.srv.AutoRenew(
		ctx,
		param.GetMonth(),
		time.Now().In(loc),
	)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// PlanStatusTicker plan 状态更新定时器
func (p *PlanAction) PlanStatusTicker(
	ctx context.Context,
	param *pb.StatusTickerParam,
) (*emptypb.Empty, error) {
	loc := tz.MustLocationFromCtx(ctx)

	err := p.srv.OnlineWaitingPlans(
		ctx,
		time.Now().In(loc),
	)
	if err != nil {
		return &emptypb.Empty{}, err
	}
	return &emptypb.Empty{}, nil
}
