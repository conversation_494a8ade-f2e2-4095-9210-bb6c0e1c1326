package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/walletd/model"
)

// GetScopeItemMapByID 根据ID获取计费范围-计费项关系
func (s *WalletBizService) GetScopeItemMapByID(
	ctx context.Context,
	id uint64,
) (*model.ScopeItemMap, error) {
	return s.walletDao.ScopeItemMap.GetByID(id, s.cacheExpires)
}

// ListAllScopeItemMaps 获取所有计费范围-计费项关系
func (s *WalletBizService) ListAllScopeItemMaps(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.ScopeItemMap, error) {
	return s.walletDao.ScopeItemMap.ListAll(offset, limit, s.cacheExpires)
}

// CountAllScopeItemMaps 获取所有计费范围-计费项关系数量
func (s *WalletBizService) CountAllScopeItemMaps(
	ctx context.Context,
) (uint64, error) {
	return s.walletDao.ScopeItemMap.CountAll(s.cacheExpires)
}

// ListScopeItemMapsByItemID 通过计费项ID获取所有计费范围-计费项关系
func (s *WalletBizService) ListScopeItemMapsByItemID(
	ctx context.Context,
	itemID uint64,
	offset int,
	limit int,
) ([]model.ScopeItemMap, error) {
	return s.walletDao.ScopeItemMap.ListByItemID(itemID, offset, limit, s.cacheExpires)
}

// CountScopeItemMapsByItemID 通过计费项ID获取所有计费范围-计费关系数量
func (s *WalletBizService) CountScopeItemMapsByItemID(
	ctx context.Context,
	itemID uint64,
) (uint64, error) {
	return s.walletDao.ScopeItemMap.CountByItemID(itemID, s.cacheExpires)
}

// ListScopeItemMapsByScopeID 通过计费范围ID获取所有计费范围-计费项关系
func (s *WalletBizService) ListScopeItemMapsByScopeID(
	ctx context.Context,
	scopeID uint64,
	offset int,
	limit int,
) ([]model.ScopeItemMap, error) {
	return s.walletDao.ScopeItemMap.ListByScopeID(scopeID, offset, limit, s.cacheExpires)
}

// CountScopeItemMapsByScopeID 通过计费范围ID获取所有计费范围-计费关系数量
func (s *WalletBizService) CountScopeItemMapsByScopeID(
	ctx context.Context,
	scopeID uint64,
) (uint64, error) {
	return s.walletDao.ScopeItemMap.CountByScopeID(scopeID, s.cacheExpires)
}

// CreateScopeItemMap 创建计费范围-计费项关系
func (s *WalletBizService) CreateScopeItemMap(
	ctx context.Context,
	_map *model.ScopeItemMap,
) (*model.ScopeItemMap, error) {
	_map.ID = 0
	err := s.walletDao.ScopeItemMap.Save(_map, s.cacheExpires)
	return _map, err
}

// UpdateScopeItemMapByID 更新计费范围-计费项关系
func (s *WalletBizService) UpdateScopeItemMapByID(
	ctx context.Context,
	id uint64,
	_map *model.ScopeItemMap,
) (*model.ScopeItemMap, error) {
	_map.ID = id
	err := s.walletDao.ScopeItemMap.Save(_map, s.cacheExpires)
	return _map, err
}

// DeleteScopeItemMapByID 删除计费范围-计费项关系
func (s *WalletBizService) DeleteScopeItemMapByID(
	ctx context.Context,
	id uint64,
) (*model.ScopeItemMap, error) {
	_map, err := s.GetScopeItemMapByID(ctx, id)
	if err != nil {
		return nil, errors.Annotate(err, "query scope_item_map failed").WithField("id", id)
	}
	err = s.walletDao.ScopeItemMap.Delete(_map)
	return _map, err
}

// DeleteScopeItemMapsByScopeID 通过计费范围ID删除计费范围-计费项关联关系
func (s *WalletBizService) DeleteScopeItemMapsByScopeID(
	ctx context.Context,
	scopeID uint64,
) (result []model.ScopeItemMap, err error) {
	offset, limit := 0, 100
	for {
		maps, err := s.ListScopeItemMapsByScopeID(ctx, scopeID, offset, limit)
		if err != nil {
			return nil, errors.Annotate(err, "list scope_item_map failed").
				WithField("scope_id", scopeID)
		}
		for _, _map := range maps {
			err := s.walletDao.ScopeItemMap.Delete(&_map)
			if err != nil {
				return nil, err
			}
		}
		result = append(result, maps...)
		if len(maps) < limit {
			break
		}
		offset += limit
	}
	return
}
