package doraItemsQuerier

import (
	"context"
	"sync"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/legacystat/common"
	dictpb "github.com/qbox/pay-sdk/dict"
	mppb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measured/model"
)

// Querier 有量 Dora 计费项查询接口
type Querier interface {
	// RefreshCache 刷新 Dora cmd 映射关系缓存
	RefreshCache(ctx context.Context) error
	// DoraItemsInUse 查询有量 Dora 计费项列表
	DoraItemsInUse(
		ctx context.Context,
		p *model.GetItemsInUseParams,
	) (model.ItemsInUseResp, error)
}

type querierImpl struct {
	dao        *model.MeasureDao
	dictClient dictpb.PayDictServiceClient
	mpClient   mppb.PayMeasureProxyServiceClient

	cacheMu      sync.Mutex
	dict         doraCmdsItemsDict
	cacheExpires time.Time
}

var _ Querier = (*querierImpl)(nil)

func New(
	mDao *model.MeasureDao,
	dictClient dictpb.PayDictServiceClient,
	mpClient mppb.PayMeasureProxyServiceClient,
) Querier {
	return &querierImpl{
		dao:        mDao,
		dictClient: dictClient,
		mpClient:   mpClient,

		cacheMu:      sync.Mutex{},
		dict:         nil, // 第一次 DoraItemsInUse 时候去构建索引
		cacheExpires: time.Time{},
	}
}

// DoraItemsInUse 查询有量 Dora 计费项列表
func (c *querierImpl) DoraItemsInUse(
	ctx context.Context,
	p *model.GetItemsInUseParams,
) (model.ItemsInUseResp, error) {
	src, ok := common.StatSrcKindFromProductCode(p.ProductCode)
	if !ok {
		// XXX 不可能发生：只有 IsProductSupported 返回 true 才有可能走到这个方法调用，此时必然是受支持的产品线了
		panic("should never happen")
	}

	// 确保走到下面之前有索引
	dict, err := c.ensureCache(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}

	resp, err := c.mpClient.ProxyGetDoraCmdsInUse(
		ctx,
		&mppb.GetItemsInUseParams{
			Uid:      uint32(p.UID),
			Begin:    timestamppb.New(p.Begin),
			End:      timestamppb.New(p.End),
			Src:      src,
			HaveZone: true,
			ZoneCode: uint32(p.ZoneCode),
			Debug:    p.Debug,
		},
	)
	if err != nil {
		return nil, errors.Trace(err)
	}

	cmds := buildModelDoraCmdsInUseResp(resp)
	keys := keysFromDoraCmdsInUseResp(cmds)
	items := mapDoraCmdKeysToItems(dict, p.ProductCode, keys)

	return model.ItemsInUseResp(items), nil
}
