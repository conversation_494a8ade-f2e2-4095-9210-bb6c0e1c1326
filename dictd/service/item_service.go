package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/dictd/model"
)

// GetItemByID 根据ID获取计费项
func (s *DictBizService) GetItemByID(
	ctx context.Context,
	id uint64,
) (*model.Item, error) {
	return s.dictDao.Item.GetByID(id, s.cacheExpires)
}

// ListItemsByIDs 根据 ID 列表批量获取计费项
func (s *DictBizService) ListItemsByIDs(ctx context.Context, ids []uint64) ([]model.Item, error) {
	return s.dictDao.Item.ListByIDs(ids, s.cacheExpires)
}

// GetItemByCode 根据Code获取计费项
func (s *DictBizService) GetItemByCode(
	ctx context.Context,
	code string,
) (*model.Item, error) {
	return s.dictDao.Item.GetByCode(code, s.cacheExpires)
}

// ListItemsByProductID 根据产品 ID 获取计费项列表
func (s *DictBizService) ListItemsByProductID(
	ctx context.Context,
	productID uint64,
	onlyActive bool,
	offset int,
	limit int,
) ([]model.Item, error) {
	return s.dictDao.Item.ListByProductID(productID, onlyActive, offset, limit, s.cacheExpires)
}

// ListItemsByProductCode 根据产品 Code 获取计费项列表
func (s *DictBizService) ListItemsByProductCode(
	ctx context.Context,
	product string,
	onlyActive bool,
	offset int,
	limit int,
) ([]model.Item, error) {
	return s.dictDao.Item.ListByProductCode(product, onlyActive, offset, limit, s.cacheExpires)
}

// CountItemsByProductID 根据产品 ID 获取计费项数量
func (s *DictBizService) CountItemsByProductID(
	ctx context.Context,
	productID uint64,
	onlyActive bool,
) (uint64, error) {
	return s.dictDao.Item.CountByProductID(productID, onlyActive, s.cacheExpires)
}

// CountItemsByProductCode 根据产品 Code 获取计费项数量
func (s *DictBizService) CountItemsByProductCode(
	ctx context.Context,
	product string,
	onlyActive bool,
) (uint64, error) {
	return s.dictDao.Item.CountByProductCode(product, onlyActive, s.cacheExpires)
}

// ListItemsByGroupID 根据计费项组ID获取所有计费项
func (s *DictBizService) ListItemsByGroupID(
	ctx context.Context,
	groupID uint64,
	onlyActive bool,
	offset int,
	limit int,
) ([]model.Item, error) {
	return s.dictDao.Item.ListByGroupID(groupID, onlyActive, offset, limit, s.cacheExpires)
}

// ListItemsByGroupCode 根据计费项组 Code 获取所有计费项
func (s *DictBizService) ListItemsByGroupCode(
	ctx context.Context,
	group string,
	onlyActive bool,
	offset int,
	limit int,
) ([]model.Item, error) {
	return s.dictDao.Item.ListByGroupCode(group, onlyActive, offset, limit, s.cacheExpires)
}

// CountItemsByGroupID 根据计费项组ID获取计费项数量
func (s *DictBizService) CountItemsByGroupID(
	ctx context.Context,
	groupID uint64,
	onlyActive bool,
) (uint64, error) {
	return s.dictDao.Item.CountByGroupID(groupID, onlyActive, s.cacheExpires)
}

// CountItemsByGroupCode 根据计费项组 Code 获取计费项数量
func (s *DictBizService) CountItemsByGroupCode(
	ctx context.Context,
	group string,
	onlyActive bool,
) (uint64, error) {
	return s.dictDao.Item.CountByGroupCode(group, onlyActive, s.cacheExpires)
}

// ListItemsByZoneID 根据区域/机房ID获取所有计费项
func (s *DictBizService) ListItemsByZoneID(
	ctx context.Context,
	zoneID uint64,
	onlyActive bool,
	offset int,
	limit int,
) ([]model.Item, error) {
	return s.dictDao.Item.ListByZoneID(zoneID, onlyActive, offset, limit, s.cacheExpires)
}

// ListItemsByZoneCode 根据区域/机房 Code 获取所有计费项
func (s *DictBizService) ListItemsByZoneCode(
	ctx context.Context,
	zoneCode int64,
	onlyActive bool,
	offset int,
	limit int,
) ([]model.Item, error) {
	return s.dictDao.Item.ListByZoneCode(zoneCode, onlyActive, offset, limit, s.cacheExpires)
}

// CountItemsByZoneID 根据区域/机房ID获取计费项数量
func (s *DictBizService) CountItemsByZoneID(
	ctx context.Context,
	zoneID uint64,
	onlyActive bool,
) (uint64, error) {
	return s.dictDao.Item.CountByZoneID(zoneID, onlyActive, s.cacheExpires)
}

// CountItemsByZoneCode 根据区域/机房 Code 获取计费项数量
func (s *DictBizService) CountItemsByZoneCode(
	ctx context.Context,
	zoneCode int64,
	onlyActive bool,
) (uint64, error) {
	return s.dictDao.Item.CountByZoneCode(zoneCode, onlyActive, s.cacheExpires)
}

// CreateItem 创建计费项
func (s *DictBizService) CreateItem(
	ctx context.Context,
	item *model.Item,
) (*model.Item, error) {
	item.ID = 0
	err := s.dictDao.Item.Save(item, s.cacheExpires)
	return item, err
}

// UpdateItemByID 根据计费项ID更新计费项
func (s *DictBizService) UpdateItemByID(
	ctx context.Context,
	id uint64,
	item *model.Item,
) (*model.Item, error) {
	item.ID = id
	err := s.dictDao.Item.Save(item, s.cacheExpires)
	return item, err
}

// UpdateItemByCode 根据计费项 Code 更新计费项
func (s *DictBizService) UpdateItemByCode(
	ctx context.Context,
	code string,
	item *model.Item,
) (*model.Item, error) {
	item.Code = code
	err := s.dictDao.Item.UpdateByCode(code, item, s.cacheExpires)
	return item, err
}

// DeleteItemByID 根据计费项ID删除计费项
func (s *DictBizService) DeleteItemByID(
	ctx context.Context,
	id uint64,
) (*model.Item, error) {
	item, err := s.GetItemByID(ctx, id)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	err = s.dictDao.Item.Delete(item)
	return item, err
}

// SearchItemByFilter 按条件搜索
func (s *DictBizService) SearchItemByFilter(
	ctx context.Context,
	ids []uint64,
	productIDs []uint64,
	zoneIDs []uint64,
	onlyActive bool,
	offset, limit int,
) ([]model.Item, error) {
	return s.dictDao.Item.SearchItemByFilter(ids, productIDs, zoneIDs, onlyActive, offset, limit)
}

// SearchItemsByFuzzy 模糊搜索
func (s *DictBizService) SearchItemsByFuzzy(
	ctx context.Context,
	keyword string,
	offset, limit int,
) ([]model.Item, error) {
	return s.dictDao.Item.SearchItemsByFuzzy(keyword, offset, limit)
}

// ListAllItems 列出所有计费项
func (s *DictBizService) ListAllItems(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.Item, error) {
	return s.dictDao.Item.ListAll(offset, limit, s.cacheExpires)
}

// ListItemsByCodes list item by codes
func (s *DictBizService) ListItemsByCodes(
	ctx context.Context,
	codes []string,
) (items []model.Item, err error) {
	if len(codes) == 0 {
		return items, nil
	}
	return s.dictDao.Item.ListByCodes(codes)
}

// CountAllItems 获取所有 Item 记录的条数
func (s *DictBizService) CountAllItems(
	ctx context.Context,
) (uint64, error) {
	return s.dictDao.Item.CountAll(s.cacheExpires)
}
