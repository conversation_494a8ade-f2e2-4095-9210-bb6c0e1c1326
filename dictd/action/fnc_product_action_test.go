package action_test

import (
	"context"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/dict"

	"github.com/stretchr/testify/assert"
)

func TestFncProduct(t *testing.T) {
	sandbox := buildSandbox(t)

	dictClient := sandbox.dictClient

	ti := time.Date(2021, 3, 11, 0, 0, 0, 0, time.UTC)
	pbTime := timestamppb.New(ti)

	fncProductEqual := func(x *pb.FncProduct, y *pb.FncProduct) bool {
		return x.Id == y.Id && x.Name == y.Name && x.CreatedAt.AsTime().Equal(y.CreatedAt.AsTime())
	}

	fooX := &pb.FncProduct{
		Id:        1,
		Name:      "foo",
		CreatedAt: pbTime,
		UpdatedAt: pbTime,
	}
	barX := &pb.FncProduct{
		Id:        2,
		Name:      "bar",
		CreatedAt: pbTime,
		UpdatedAt: pbTime,
	}
	ctx := context.Background()

	fooY, err := dictClient.CreateFncProduct(ctx, fooX)
	assert.NoError(t, err)
	assert.True(t, fncProductEqual(fooX, fooY))

	barY, err := dictClient.CreateFncProduct(ctx, barX)
	assert.NoError(t, err)
	assert.True(t, fncProductEqual(barX, barY))

	fooZ, err := dictClient.GetFncProductByID(ctx, &pb.IDParam{Id: fooX.Id})
	assert.NoError(t, err)
	assert.True(t, fncProductEqual(fooX, fooZ))

	fncProducts, err := dictClient.ListFncProductsByIDs(context.Background(), &pb.MultiIDParam{
		Ids: []uint64{fooX.Id, barX.Id},
	})
	assert.NoError(t, err)

	for _, fncProduct := range fncProducts.FncProducts {
		if fncProduct.Id == fooX.Id {
			assert.True(t, fncProductEqual(fncProduct, fooX))
		}
		if fncProduct.Id == barX.Id {
			assert.True(t, fncProductEqual(fncProduct, barX))
		}
		assert.True(t, fncProduct.Id == fooX.Id || fncProduct.Id == barX.Id)
	}
}
