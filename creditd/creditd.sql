-- MySQL dump 10.13  Distrib 5.7.20, for osx10.12 (x86_64)
--
-- Host: ************    Database: pay_credit
-- ------------------------------------------------------
-- Server version	5.7.20

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `available_balance_thresholds`
--

DROP TABLE IF EXISTS `available_balance_thresholds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `available_balance_thresholds` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) unsigned NOT NULL DEFAULT '0',
  `threshold` bigint(20) NOT NULL DEFAULT '0' COMMENT '可用额度阈值',
  `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '状态：1：未启用，2：启用',
  `remark` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `created_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
  `updated_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3604 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `manager_available_balance_thresholds`
--

DROP TABLE IF EXISTS `manager_available_balance_thresholds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `manager_available_balance_thresholds` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) unsigned NOT NULL DEFAULT '0',
  `threshold` bigint(20) NOT NULL DEFAULT '0' COMMENT '可用额度阈值',
  `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '状态：1：未启用，2：启用',
  `remark` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `created_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
  `updated_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `credits`
--

DROP TABLE IF EXISTS `credits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `credits` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) unsigned NOT NULL DEFAULT '0',
  `customer_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `credit_line` bigint(20) NOT NULL DEFAULT '0' COMMENT '信用额度值',
  `credit_period` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '账期',
  `period_unit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '账期单位：1：天，2：月',
  `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '状态：1：未授信，2：未激活，3：生效中，4：停用',
  `remark` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `init_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000' COMMENT '创建时间',
  `cancel_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000' COMMENT '取消时间',
  `activate_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000' COMMENT '激活时间',
  `revoke_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000' COMMENT '停用时间',
  `created_at` datetime(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000',
  `updated_at` datetime(6) NOT NULL DEFAULT '0000-00-00 00:00:00.000000',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2453 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `locks`
--

DROP TABLE IF EXISTS `locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `locks` (
  `lock_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`lock_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `notice_records`
--

DROP TABLE IF EXISTS `notice_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notice_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) unsigned NOT NULL DEFAULT '0',
  `event` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '事件：1：创建信用额度 2：取消信用额度 3：调整额度/账期 4：激活信用额度 5：停用信用额度',
  `notice_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '通知类型：1：立即发送，2：延迟发送',
  `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '状态：1：成功发送，2：发送失败，3：延后发送，4：取消/无需发送',
  `trigger_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000' COMMENT '触发时间',
  `expired_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000' COMMENT '过期时间',
  `remark` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `created_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
  `updated_at` datetime(6) NOT NULL DEFAULT '1000-01-01 00:00:00.000000',
  PRIMARY KEY (`id`),
  KEY `idx_notice_record_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2271 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2020-07-06  9:43:22
