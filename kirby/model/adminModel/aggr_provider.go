package adminModel

import (
	"context"
	"sync"
	"time"

	"github.com/qbox/bo-base/v4/intl/tz"
	"golang.org/x/sync/singleflight"
)

type IAggRProvider interface {
	// GetAggregationRule 按 code 查询一条聚合规则，如果不存在则返回 nil
	GetAggregationRule(
		ctx context.Context,
		epoch time.Time,
		aggrCode string,
	) (*AggregationRule, error)
}

type cachedAggRStore struct {
	dao *AggregationRuleDao

	// map[aggrCode]aggr
	// it is assumed an AggR doesn't change after getting out of draft status
	cache       sync.Map
	refreshWork singleflight.Group
}

func NewCachedAggRStore(dao *AggregationRuleDao) IAggRProvider {
	return &cachedAggRStore{
		dao:   dao,
		cache: sync.Map{},
	}
}

func (x *cachedAggRStore) GetAggregationRule(
	ctx context.Context,
	epoch time.Time,
	aggrCode string,
) (*AggregationRule, error) {
	if cached, ok := x.cache.Load(aggrCode); ok {
		return cached.(*AggregationRule), nil
	}

	loc := tz.MustLocationFromCtx(ctx)
	epoch = epoch.In(loc)

	result, err, _ := x.refreshWork.Do(aggrCode, func() (any, error) {
		return x.dao.GetByCode(epoch, aggrCode, false)
	})
	if err != nil {
		return nil, err
	}

	x.cache.Store(aggrCode, result)

	return result.(*AggregationRule), nil
}
