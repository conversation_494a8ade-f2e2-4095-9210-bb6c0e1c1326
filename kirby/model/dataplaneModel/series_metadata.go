package dataplaneModel

import (
	"context"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"

	"qiniu.io/pay/kirby/model/adminModel"
)

type seriesMetadataRow struct {
	ID uint64 `gorm:"column:id;not null;primary_key"`

	// 直接传入的属性

	// QRN 该序列对应资源的 QRN
	QRN string `gorm:"column:qrn;type:varchar(512);not null"`
	// MM 该序列的计量指标 code
	MM string `gorm:"column:mm;type:varchar(64);not null"`
	// SP 该序列的属性
	SP *SP `gorm:"column:sp;type:text;not null"`
	// Start 该序列计量的起始时刻（含）
	Start time.Time `gorm:"column:start;type:DATETIME(6);not null"`
	// End 该序列计量的结束时刻（不含）
	End time.Time `gorm:"column:end;type:DATETIME(6);not null"`
	// MBUUID 推来计量包的 UUID，用于服务端去重
	MBUUID string `gorm:"column:mb_uuid;type:varchar(64);not null"`

	// 派生的属性

	// UID 是从 QRN 中解出的 UID
	UID uint64 `gorm:"column:uid;not null"`
	// SPHash 是由 SP 得到的一致性 hash 值，用于高效的复合查询
	SPHash uint32 `gorm:"column:sp_hash;not null"`
	// RT 资源类型 code
	RT string `gorm:"column:rt;type:varchar(64);not null"`
	// AggR 该序列匹配到的聚合规则 code
	AggR string `gorm:"column:aggr;type:varchar(64);not null"`
	// MeasureUnit 该序列数据的计量单位
	MeasureUnit adminModel.MeasureUnit `gorm:"column:measure_unit;type:varchar(16);not null"`
	// G 该序列数据的粒度
	G adminModel.Granularity `gorm:"column:g;type:tinyint(1);not null"`

	// SeriesKey 该序列的数据的检索 key
	SeriesKey string `gorm:"column:series_key;type:varchar(64);not null"`

	// CreatedAt 记录的创建时刻
	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);not null;default:now(6)"`
}

func (x *seriesMetadataRow) TableName() string {
	return getSeriesMetadataTableName(x.Start, x.Start.Location())
}

func (x *seriesMetadataRow) syncSPHash() {
	x.SPHash = SPHasher(*x.SP).Hash()
}

// SeriesMetadataDao is data access object of SeriesMetadata model
type SeriesMetadataDao struct {
	base *dao.BaseDao
}

// NewSeriesMetadataDao is constructor of SeriesMetadataDao
func NewSeriesMetadataDao(base *dao.BaseDao) *SeriesMetadataDao {
	return &SeriesMetadataDao{
		base: base,
	}
}

func makeSeriesMetadataTableSelector(t time.Time, loc *time.Location) *seriesMetadataRow {
	return &seriesMetadataRow{Start: t.In(loc)}
}

// EnsureShard ensures existence of the month's shard table.
func (d *SeriesMetadataDao) EnsureShard(month time.Time, loc *time.Location) error {
	tableName := makeSeriesMetadataTableSelector(month, loc).TableName()
	stmt, err := makeCreateSeriesMetadataTableStmt(tableName)
	if err != nil {
		return err
	}

	err = d.base.LogMode(false).Exec(stmt).Error
	if err != nil && !isTableAlreadyExisting(err) {
		return err
	}
	return nil
}

type SeriesMetadataShard SeriesMetadataDao

func (d *SeriesMetadataDao) GetShard(month time.Time, loc *time.Location) *SeriesMetadataShard {
	tableName := makeSeriesMetadataTableSelector(month, loc).TableName()
	tbl := d.base.DB.Table(tableName)
	return (*SeriesMetadataShard)(NewSeriesMetadataDao(dao.NewBaseDao(tbl, nil)))
}

// Save inserts or updates a seriesMetadataRow by id
//
// model.SPHash will be synced with model.SP in-place
func (d *SeriesMetadataShard) Save(model *seriesMetadataRow) error {
	model.syncSPHash()

	err := d.base.Execute(func(value interface{}) error {
		return d.base.Save(value).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

func (d *SeriesMetadataShard) BulkInsert(ctx context.Context, data []seriesMetadataRow) error {
	return d.base.BulkInsert(ctx, data)
}

func (d *SeriesMetadataShard) listAffectedUIDAndAggRsSince(
	epoch time.Time,
	month time.Time,
	since time.Time,
	loc *time.Location,
) ([]uidAndAggR, error) {
	epoch = epoch.In(loc)
	since = since.In(loc)
	month = base.ThisMonth(month.In(loc))
	if epoch.Before(month) {
		// 被查询的月份在当前时刻还没到来
		return nil, nil
	}

	var result []uidAndAggR
	err := d.base.Execute(func(value interface{}) error {
		return d.base.Where(
			"`created_at` >= ? AND `created_at` <= ?",
			since,
			epoch,
		).Group("uid, aggr").Select("uid, aggr").Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"epoch": epoch,
			"month": month,
			"since": since,
		})
	}

	return result, nil
}

func (d *SeriesMetadataShard) listByAffectedScope(
	epoch time.Time,
	month time.Time,
	loc *time.Location,
	as *affectedScope,
) ([]*seriesMetadataRow, error) {
	epoch = epoch.In(loc)
	month = base.ThisMonth(month.In(loc))
	if epoch.Before(month) {
		// 被查询的月份在当前时刻还没到来
		return nil, nil
	}

	if as.isEmpty() {
		return nil, nil
	}

	where, args, err := as.toSqlizer(epoch).ToSql()
	if err != nil {
		return nil, err
	}

	var result []*seriesMetadataRow
	err = d.base.Execute(func(value interface{}) error {
		return d.base.Where(where, args...).Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"epoch": epoch,
			"month": month,
			// `as` is not printed because it may get very very large
		})
	}

	return result, nil
}

// ListByMonthUIDRTMMAndSPHashes lists series metadata by (month, uid, rt, mm, sp_hashes)
func (d *SeriesMetadataShard) ListByMonthUIDRTMMAndSPHashes(
	epoch time.Time,
	month time.Time,
	loc *time.Location,
	uid uint64,
	rt string,
	mm string,
	spHashes []uint32,
) ([]*seriesMetadataRow, error) {
	month = base.ThisMonth(month.In(loc))
	if epoch.Before(month) {
		// 被查询的月份在当前时刻还没到来
		return nil, nil
	}

	var result []*seriesMetadataRow
	err := d.base.Execute(func(value interface{}) error {
		return d.base.Where(
			"`uid` = ? AND `rt` = ? AND `mm` = ? AND `sp_hash` IN (?) AND `created_at` <= ?",
			uid,
			rt,
			mm,
			spHashes,
			epoch,
		).Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"epoch":    epoch,
			"month":    month,
			"uid":      uid,
			"rt":       rt,
			"mm":       mm,
			"spHashes": spHashes,
		})
	}

	return result, nil
}

func (x *ReqListSeries) toSQL(loc *time.Location) (string, []any, error) {
	conds := squirrel.And{
		squirrel.GtOrEq{"created_at": x.Epoch.In(loc)},
	}

	if !timeIsZero(x.DataTimeFrom) {
		conds = append(conds, squirrel.GtOrEq{"end": x.DataTimeFrom.In(loc)})
	}
	if !timeIsZero(x.DataTimeTo) {
		conds = append(conds, squirrel.Lt{"start": x.DataTimeTo.In(loc)})
	}
	if !timeIsZero(x.CtimeFrom) {
		conds = append(conds, squirrel.GtOrEq{"created_at": x.CtimeFrom.In(loc)})
	}
	if !timeIsZero(x.CtimeTo) {
		conds = append(conds, squirrel.Lt{"created_at": x.CtimeTo.In(loc)})
	}
	if len(x.SeriesKeys) > 0 {
		conds = append(conds, squirrel.Eq{"series_key": x.SeriesKeys})
	}
	if len(x.MBUUIDs) > 0 {
		conds = append(conds, squirrel.Eq{"mb_uuid": x.MBUUIDs})
	}
	if len(x.QRNs) > 0 {
		conds = append(conds, squirrel.Eq{"qrn": x.QRNs})
	}
	if len(x.RTDCodes) > 0 {
		conds = append(conds, squirrel.Eq{"rt": x.RTDCodes})
	}
	if len(x.UIDs) > 0 {
		conds = append(conds, squirrel.Eq{"uid": x.UIDs})
	}
	if len(x.MMs) > 0 {
		conds = append(conds, squirrel.Eq{"mm": x.MMs})
	}
	// TODO: SPConditions: need to perform cartesian product to map this to []spHash

	return conds.ToSql()
}

// ListByConds lists series metadata by the specified conditions
func (d *SeriesMetadataShard) ListByConds(
	req *ReqListSeries,
	loc *time.Location,
) ([]*seriesMetadataRow, error) {
	where, args, err := req.toSQL(loc)
	if err != nil {
		return nil, err
	}

	offset, limit := paginate(req.Page, req.PageSize)

	var result []*seriesMetadataRow
	err = d.base.Execute(func(value any) error {
		return d.base.Where(where, args...).
			Offset(offset).
			Limit(limit).
			Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		if gorm.IsRecordNotFoundError(err) || errors.Cause(err) == dao.ErrRecordNotFound {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"req": req,
		})
	}

	return result, nil
}

// CountByConds counts number of metadata rows matching the specified conditions,
// ignoring pagination
func (d *SeriesMetadataShard) CountByConds(
	req *ReqListSeries,
	loc *time.Location,
) (uint64, error) {
	where, args, err := req.toSQL(loc)
	if err != nil {
		return 0, err
	}

	var result uint64
	err = d.base.Execute(func(value any) error {
		return d.base.Where(where, args...).
			Count(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return 0, nil
		}

		if gorm.IsRecordNotFoundError(err) || errors.Cause(err) == dao.ErrRecordNotFound {
			return 0, nil
		}

		return 0, errors.Trace(err).WithFields(errors.Fields{
			"req": req,
		})
	}

	return result, nil
}

// ListDistinctUIDsByConds pulls distinct UIDs that have series data matching
// the specified conditions
func (d *SeriesMetadataShard) ListDistinctUIDsByConds(
	req *ReqListSeries,
	loc *time.Location,
) ([]uint64, error) {
	where, args, err := req.toSQL(loc)
	if err != nil {
		return nil, err
	}

	offset, limit := paginate(req.Page, req.PageSize)

	var result []uint64
	err = d.base.Execute(func(value any) error {
		return d.base.Where(where, args...).
			Offset(offset).
			Limit(limit).
			Select("DISTINCT `uid`").
			Find(value).Error
	}, &result)
	if err != nil {
		if isTableNotFound(err) {
			return nil, nil
		}

		if errors.Cause(err) == dao.ErrRecordNotFound {
			return nil, nil
		}

		return nil, errors.Trace(err).WithFields(errors.Fields{
			"req": req,
		})
	}

	return result, nil
}
