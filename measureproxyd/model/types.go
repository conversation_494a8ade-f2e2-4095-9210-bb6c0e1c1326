package model

import (
	"time"

	"github.com/qbox/bo-base/v4/legacystat/common"
	pb "github.com/qbox/pay-sdk/measureproxy"
)

// StatSrcQueryParams 计量源查询参数
//
// 基于 qbox.us/payment/base/bizcommon/stat.go 中同名的结构体
// 有些许改动以便解耦
type StatSrcQueryParams struct {
	// UID UID
	UID uint32
	// Begin 查询起始时间
	Begin time.Time
	// End 查询截止时间
	End time.Time
	// HaveZone 查询是否指定区域
	//
	// NOTE: proto3 零值语义无法区分 `zone=0` 是不需要传还是需要传一个 `0`，
	// 因此用 Golang "best practice" 加一个 flag 解决
	HaveZone bool
	// ZoneCode 区域 code
	ZoneCode uint32
	// Key 计量 KEY
	Key string
	// G 计量粒度 G
	G pb.Granularity
	// Select 计量 SELECT
	Select []string
	// Query 计量 QUERY
	Query []KV
	// Src 计量源种类
	Src pb.StatSrc
	// Debug 是否在此请求的链路上开启调试
	Debug bool

	Resource Resource
}

type Resource struct {
	ResourceName string
	ResourceType pb.ResourceType
}

// KV 键/值对
type KV struct {
	// K key
	K string
	// V value
	V string
}

// RespTableItemWithoutGroup 原始计量点响应
type RespTableItemWithoutGroup struct {
	// Time 时间戳
	Time time.Time
	// Values 计量求值上下文
	Values map[string]int64
}

// GetUsersParams 产品线某区域有量用户列表请求参数
type GetUsersParams struct {
	// Begin 查询起始时间
	Begin time.Time
	// End 查询截止时间
	End time.Time
	// HaveZone 查询是否指定区域
	HaveZone bool
	// ZoneCode 区域 code
	ZoneCode uint32
	// Src 计量源种类
	Src pb.StatSrc
	// Debug 是否在此请求的链路上开启调试
	Debug bool
}

// GetItemsInUseParams 单个用户有量计费项列表请求参数
type GetItemsInUseParams struct {
	// UID 用户 ID
	UID uint32
	// Begin 查询起始时间
	Begin time.Time
	// End 查询截止时间
	End time.Time
	// HaveZone 查询是否指定区域
	HaveZone bool
	// ZoneCode 区域 code
	ZoneCode uint32
	// Src 计量源种类
	Src pb.StatSrc
	// Debug 是否在此请求的链路上开启调试
	Debug bool
}

// UsersResp 有量用户列表响应
type UsersResp []uint32

// ItemsInUseResp 有量计费项列表响应
type ItemsInUseResp []string

// DoraCmdsInUseResp 有量 Dora cmd 列表响应
type DoraCmdsInUseResp []common.LegacyDoraCmd

// GetBillsParams 直接获取账单参数
type GetBillsParams struct {
	// UID 用户 ID
	UID uint32
	// Begin 查询起始时间
	Begin time.Time
	// End 查询截止时间
	End time.Time
	// HaveZone 查询是否指定区域
	HaveZone bool
	// ZoneCode 区域 code
	ZoneCode uint32
	// Src 计量源种类
	Src pb.StatSrc
	// Debug 是否在此请求的链路上开启调试
	Debug bool
}
