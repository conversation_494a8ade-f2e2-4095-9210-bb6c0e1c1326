package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/measured/model"
)

// GetItemExtraArgByID 根据 ID 查询单个计费项计量查询附加参数
func (s *MeasureBizService) GetItemExtraArgByID(
	ctx context.Context,
	id uint64,
) (*model.ItemExtraArg, error) {
	return s.dao.ItemExtraArg.GetByID(id, s.cacheExpires)
}

// ListItemExtraArgsByItemID 根据计费项 ID 列出计量查询附加参数列表
func (s *MeasureBizService) ListItemExtraArgsByItemID(
	ctx context.Context,
	idtID uint64,
	offset int,
	limit int,
) ([]model.ItemExtraArg, error) {
	return s.dao.ItemExtraArg.ListByItemID(
		idtID,
		offset,
		limit,
		s.cacheExpires,
	)
}

// CountItemExtraArgsByItemID 根据计费项 ID 获取计量查询附加参数数量
func (s *MeasureBizService) CountItemExtraArgsByItemID(
	ctx context.Context,
	idtID uint64,
) (uint64, error) {
	return s.dao.ItemExtraArg.CountByItemID(idtID, s.cacheExpires)
}

// CreateItemExtraArg 创建一个计费项计量查询附加参数
func (s *MeasureBizService) CreateItemExtraArg(
	ctx context.Context,
	obj *model.ItemExtraArg,
) (*model.ItemExtraArg, error) {
	obj.ID = 0
	err := s.dao.ItemExtraArg.Save(obj, s.cacheExpires)
	return obj, err
}

// DeleteItemExtraArgByID 根据 ID 删除一个计费项计量查询附加参数
func (s *MeasureBizService) DeleteItemExtraArgByID(
	ctx context.Context,
	id uint64,
) (*model.ItemExtraArg, error) {
	obj, err := s.dao.ItemExtraArg.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	err = s.dao.ItemExtraArg.DeleteByID(id)
	return obj, err
}
