package model

import (
	"context"
	"fmt"
	"time"

	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	"github.com/Masterminds/squirrel"
	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// Coupon model definition
type Coupon struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`

	// BatchID 该抵用券归属的批次 ID
	BatchID uint64
	// UID 绑定的 UID
	UID uint64
	// Code 券码
	Code string `gorm:"type:varchar(64)"`
	// BindMethod 该抵用券以何种方式被绑定
	BindMethod CouponBindMethod
	// State 状态
	//
	// TODO: 看看之后能不能做成完整审计功能，不靠 update
	State CouponState
	// Money 总值，万分之一元
	Money base.Money
	// Balance 余额，万分之一元
	Balance base.Money
	// BoundAt 抵用券的绑定时间
	BoundAt base.HNS
	// EffectTime 抵用券的生效时间（含）
	EffectTime base.HNS
	// DeadTime 抵用券的失效时间（不含）
	DeadTime base.HNS

	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// CouponModelDao is data access object of Coupon model
type CouponModelDao struct {
	base *dao.BaseDao
}

// NewCouponModelDao is constructor of CouponModelDao
func NewCouponModelDao(base *dao.BaseDao) *CouponModelDao {
	return &CouponModelDao{
		base: base,
	}
}

// GetByID selects a Coupon by id
func (d *CouponModelDao) GetByID(id uint64, expires ...time.Duration) (*Coupon, error) {
	model := &Coupon{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&Coupon{}).
			Where("id = ?", id).
			First(value).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// GetByCode selects a Coupon by code
func (d *CouponModelDao) GetByCode(code string, expires ...time.Duration) (*Coupon, error) {
	model := &Coupon{}
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&Coupon{}).
			Where("`code` = ?", code).
			First(value).
			Error
	}, model)
	if err != nil {
		return nil, errors.Trace(err).WithField("code", code)
	}
	return model, nil
}

// Save inserts or updates a Coupon by id
func (d *CouponModelDao) Save(model *Coupon, expires ...time.Duration) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Save(value).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// BulkInsert 批量插入一组 Coupons
func (d *CouponModelDao) BulkInsert(ctx context.Context, l []Coupon) error {
	return d.base.BulkInsert(ctx, l)
}

// Update updates a record of Coupon by id
func (d *CouponModelDao) Update(m *Coupon) error {
	if m == nil {
		return fmt.Errorf("dao invalid parameter")
	}
	err := d.base.Execute(func(i any) error {

		expr := d.base.Model(&Coupon{}).Updates(m)

		// XXX 余额可能变成 0，gorm Updates 不会碰零值字段，因此这里要特意考虑
		// 当新值的 Balance 为 0 时强制更新它，否则不用管
		// 所有其他字段的零值不是无意义（Money 以外）就是本来也不会修改（Money），因此只感知 Balance 即可
		if m.Balance == 0 {
			expr = expr.Update("balance", m.Balance)
		}

		return expr.Find(m).Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("id", m.ID)
	}
	return nil
}

// CountByBatchIDAndUID 查询一个 UID 一个批次内绑了几张券
func (d *CouponModelDao) CountByBatchIDAndUID(
	batchID uint64,
	uid uint64,
) (uint64, error) {
	var result uint64
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&Coupon{}).
			Where("`batch_id` = ? AND `uid` = ?", batchID, uid).
			Count(value).
			Error
	}, &result)
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"batch_id": batchID,
			"uid":      uid,
		})
	}
	return result, nil
}

// LockByIDs 按 ID 列表锁定一批券
func (d *CouponModelDao) LockByIDs(ids []uint64) error {
	err := d.base.Execute(func(i any) error {
		return d.base.Model(&Coupon{}).
			Where("`id` IN (?) AND `state` = ?", ids, CouponStateUsable).
			Update("state", CouponStateLocked).
			Error
	}, nil)
	if err != nil {
		return errors.Trace(err).WithField("ids", ids)
	}
	return nil
}

// UnlockByIDs 按 ID 列表解锁一批券
func (d *CouponModelDao) UnlockByIDs(
	ctx context.Context,
	ids []uint64,
	epoch time.Time,
) error {
	err := d.base.Model(&Coupon{}).
		Where("`id` IN (?) AND `state` = ?", ids, CouponStateLocked).
		Update("state", CouponStateUsable).
		Error
	if err != nil {
		return errors.Trace(err).WithField("ids", ids)
	}

	// 对每张券推一次状态机
	// NOTE: 这是为了防止发生一些神奇的事情，例如某张券在解锁之前过期，解锁之后错误变为可用的情形
	coupons, err := d.ListByConds(
		&ListCouponsByCondsParam{
			CouponIDs: ids,
		},
		0,  // offset
		-1, // limit
	)
	if err != nil {
		return errors.Trace(err).WithField("ids", ids)
	}

	for _, c := range coupons {
		_, err := d.ProgressState(ctx, &c, epoch)
		if err != nil {
			return errors.Trace(err).WithField("coupon", c)
		}
	}

	return nil
}

// ProgressState 推动状态更新，直到收敛为止
func (d *CouponModelDao) ProgressState(
	ctx context.Context,
	c *Coupon,
	epoch time.Time,
) (newState CouponState, err error) {
	for {
		prevState := c.State

		nextState, err := d.progressState(ctx, c, epoch)
		if err != nil {
			return CouponStateUnknown, errors.Trace(err)
		}

		if prevState == nextState {
			return nextState, nil
		}
	}
}

// progressState 推动一次状态更新
func (d *CouponModelDao) progressState(
	ctx context.Context,
	c *Coupon,
	epoch time.Time,
) (newState CouponState, err error) {
	prevState := c.State
	nextState := ComputeNextStateForCoupon(c, epoch)
	if prevState == nextState {
		return nextState, nil
	}

	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"couponCode": c.Code,
		"prevState":  prevState,
		"nextState":  nextState,
		"epoch":      epoch,
	}).Info("coupon state transition triggered")

	c.State = nextState
	err = d.Update(c)
	if err != nil {
		c.State = prevState
		return prevState, errors.Trace(err).WithFields(errors.Fields{
			"nextState": nextState,
			"coupon":    c,
		})
	}

	return nextState, nil
}

// ListAvailable list available coupon, order by id asc
// 以下状态为 unavailable
// 已冻结 3 已用完 4 已过期 5 已锁定 6 已撤销 7
// 以下状态为 available
// 未知抵用券状态 0 未生效 1 可使用 2
func (d *CouponModelDao) ListAvailable(idMarker, limit uint64) (result []Coupon, err error) {
	err = d.base.Execute(func(value any) error {
		return d.base.
			Table("`coupons` FORCE INDEX(idx_coupon_uid_state)"). // force use db index idx_coupon_uid_state
			Where("`uid` > ? AND `state` <= ? AND `id` > ?", 0, CouponStateUsable, idMarker).
			Order("id ASC").
			Limit(limit).
			Find(value).
			Error
	}, &result)

	if err != nil {
		return nil, errors.Trace(err)
	}
	return result, nil
}

// ListCouponsByCondsParam 按条件列出抵用券参数
type ListCouponsByCondsParam struct {
	// CouponIDs 抵用券 ID 列表
	CouponIDs []uint64
	// CouponCodes 券码列表
	CouponCodes []string

	// UID UID
	UID uint64
	// BatchID 批次 ID
	BatchID uint64
	// States 抵用券状态
	States []CouponState
	// PayModeScopes 抵用券付费类型
	PayModeScopes []CouponPayMode
}

// ListCouponDetailsByCondsParam 按条件列出抵用券详情参数
type ListCouponDetailsByCondsParam struct {
	ListCouponsByCondsParam

	// PayModeScopes 抵用券付费类型
	PayModeScopes []CouponPayMode
}

var errEmptyCondition = errors.New("condition empty")

func (param *ListCouponsByCondsParam) validate() error {
	// CouponID CouponCode UID BatchID 不能全为空，否则炸
	if len(param.CouponIDs) == 0 &&
		len(param.CouponCodes) == 0 &&
		param.UID == 0 &&
		param.BatchID == 0 {
		return errEmptyCondition
	}

	return nil
}

func (param *ListCouponsByCondsParam) toWhereClause() (conditionStr string, args []any, err error) {
	return param.toSQLizer().ToSql()
}

func (param *ListCouponsByCondsParam) toSQLizer() squirrel.Sqlizer {
	andCond := squirrel.And{}
	if len(param.CouponIDs) > 0 {
		andCond = append(andCond, squirrel.Eq{
			"`coupons`.`id`": param.CouponIDs,
		})
	}
	if len(param.CouponCodes) > 0 {
		andCond = append(andCond, squirrel.Eq{
			"`coupons`.`code`": param.CouponCodes,
		})
	}
	if param.UID != 0 {
		andCond = append(andCond, squirrel.Eq{
			"`coupons`.`uid`": param.UID,
		})
	}
	if param.BatchID != 0 {
		andCond = append(andCond, squirrel.Eq{
			"`coupons`.`batch_id`": param.BatchID,
		})
	}
	if len(param.States) > 0 {
		andCond = append(andCond, squirrel.Eq{
			"`coupons`.`state`": param.States,
		})
	}

	return andCond
}

func (param *ListCouponDetailsByCondsParam) toWhereClause() (conditionStr string, args []any, err error) {
	return param.toSQLizer().ToSql()
}

func (param *ListCouponDetailsByCondsParam) toSQLizer() squirrel.Sqlizer {
	andCond := param.ListCouponsByCondsParam.toSQLizer().(squirrel.And)

	if len(param.PayModeScopes) > 0 {
		// 在 ListByDetailConds 里 JOIN 的
		andCond = append(andCond, squirrel.Eq{
			"`coupon_batches`.`applicable_pay_mode`": param.PayModeScopes,
		})
	}

	return andCond
}

// ListByConds list by conds without count
func (d *CouponModelDao) ListByConds(
	param *ListCouponsByCondsParam,
	offset int,
	limit int,
) (result []Coupon, err error) {
	err = param.validate()
	if err != nil {
		return nil, errors.Trace(err).WithField("param", param)
	}

	where, args, err := param.toWhereClause()
	if err != nil {
		return nil, ErrGenSQLFailure
	}

	err = d.base.Execute(func(value any) error {
		return d.base.Model(&Coupon{}).
			Where(where, args...).
			Offset(offset).
			Limit(limit).
			Order("created_at DESC").
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err).WithField("param", param)
	}
	return result, nil
}

// ListByCondsWithCount select records of Coupon according to the given conditions.
func (d *CouponModelDao) ListByCondsWithCount(
	param *ListCouponsByCondsParam,
	offset int,
	limit int,
) (result []Coupon, count uint64, err error) {

	err = param.validate()
	if err != nil {
		return nil, 0, errors.Trace(err).WithField("param", param)
	}

	where, args, err := param.toWhereClause()
	if err != nil {
		return nil, 0, ErrGenSQLFailure
	}

	err = d.base.Execute(func(value any) error {
		return d.base.Model(&Coupon{}).
			Where(where, args...).
			Offset(offset).
			Limit(limit).
			Order("created_at DESC").
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, 0, errors.Trace(err).WithFields(errors.Fields{
			"param": param,
		})
	}

	err = d.base.Execute(func(value any) error {
		return d.base.Model(&Coupon{}).
			Where(where, args...).
			Count(value).
			Error
	}, &count)
	if err != nil {
		return nil, 0, errors.Trace(err).WithFields(errors.Fields{
			"param": param,
		})
	}

	return result, count, nil
}

// ListByDetailConds select records of Coupon according to the given detail filtering conditions.
//
// NOTE: same as ListByConds except for type of param
func (d *CouponModelDao) ListByDetailConds(
	param *ListCouponDetailsByCondsParam,
	offset int,
	limit int,
	expires ...time.Duration,
) (result []Coupon, count uint64, err error) {

	err = param.validate()
	if err != nil {
		return nil, 0, errors.Trace(err).WithField("param", param)
	}

	where, args, err := param.toWhereClause()
	if err != nil {
		return nil, 0, ErrGenSQLFailure
	}

	expr := d.base.Model(&Coupon{})
	if len(param.PayModeScopes) > 0 {
		expr = expr.Joins("LEFT JOIN `coupon_batches` ON `coupons`.`batch_id` = `coupon_batches`.`id`")
	}

	err = d.base.Execute(func(value any) error {
		return expr.
			Where(where, args...).
			Offset(offset).
			Limit(limit).
			Order("`coupons`.`created_at` DESC").
			Find(&result).
			Error
	}, &result)
	if err != nil {
		return nil, 0, errors.Trace(err).WithFields(errors.Fields{
			"param": param,
		})
	}

	err = d.base.Execute(func(value any) error {
		return expr.
			Where(where, args...).
			Count(value).
			Error
	}, &count)
	if err != nil {
		return nil, 0, errors.Trace(err).WithFields(errors.Fields{
			"param": param,
		})
	}

	return result, count, nil
}

// ListByDetailCondsWithOrder select records of Coupon according to the given detail filtering conditions and order by balance & dead_time.
//
// NOTE: same as ListByConds except for type of param
func (d *CouponModelDao) ListByDetailCondsWithOrder(
	param *ListCouponDetailsByCondsParam,
	offset int,
	limit int,
	expires ...time.Duration,
) (result []Coupon, count uint64, err error) {
	where, args, err := param.toWhereClause()
	if err != nil {
		return nil, 0, ErrGenSQLFailure
	}

	expr := d.base.Model(&Coupon{})
	if len(param.PayModeScopes) > 0 {
		expr = expr.Joins("LEFT JOIN `coupon_batches` ON `coupons`.`batch_id` = `coupon_batches`.`id`")
	}

	err = d.base.Execute(func(value any) error {
		return expr.
			Where(where, args...).
			Offset(offset).
			Limit(limit).
			Order("`coupons`.balance DESC,`coupons`.dead_time DESC,`coupons`.`created_at` DESC").
			Find(&result).
			Error
	}, &result)
	if err != nil {
		return nil, 0, errors.Trace(err).WithFields(errors.Fields{
			"param": param,
		})
	}

	err = d.base.Execute(func(value any) error {
		return expr.
			Where(where, args...).
			Count(value).
			Error
	}, &count)
	if err != nil {
		return nil, 0, errors.Trace(err).WithFields(errors.Fields{
			"param": param,
		})
	}

	return result, count, nil
}

// ListBoundUIDsByBatchID returns all uid bound to given couponBatch
func (d *CouponModelDao) ListBoundUIDsByBatchID(
	batchID uint64,
) (uids []uint64, err error) {
	err = d.base.Execute(func(value any) error {
		return d.base.Table("coupons").
			Where("`batch_id` = ?", batchID).
			Pluck("uid", value).
			Error
	}, &uids)

	if err != nil {
		return nil, errors.Trace(err)
	}

	return uids, nil
}

// ListCouponAboutToExpire 查询指定日期会过期的抵用券
func (d *CouponModelDao) ListCouponAboutToExpire(
	expectedExpirationDate time.Time,
) (coupons []Coupon, err error) {

	// 过期时间在这一天内的所有条目
	expectedExpirationDateStart := base.NewHNS(expectedExpirationDate)
	expectedExpirationDateEnd := base.NewHNS(expectedExpirationDate.AddDate(0, 0, 1))
	// 过期时间为 expectedExpirationDate 的 coupon
	err = d.base.Execute(func(value any) error {
		return d.base.Table("coupons").
			Where("`dead_time` >= ? and `dead_time` < ? and `state` = ?",
				expectedExpirationDateStart, expectedExpirationDateEnd, CouponStateUsable).
			Find(value).
			Error
	}, &coupons)

	if err != nil {
		return nil, errors.Trace(err)
	}

	return coupons, nil
}

// UpdateCouponStateByBatchID 按照 batchID 更新 coupon 的状态值
func (d *CouponModelDao) UpdateCouponStateByBatchID(
	batchID uint64,
	toState CouponState,
) (err error) {

	err = d.base.Execute(func(i any) error {
		return d.base.Table("coupons").
			Where("batch_id = ?", batchID).
			Update("state", toState).
			Error
	}, nil)

	if err != nil {
		return errors.Trace(err)
	}

	return nil
}

// CountAvailableCoupon 获取订单可用优惠券数量
func (d *CouponModelDao) CountAvailableCoupon(uid uint64) (count uint64, err error) {
	// coupon_batches 小表 right join coupons 大表
	// select count(*) from coupon_batches
	// right join coupons on coupon_batches.id = coupons.batch_id
	// where coupons.uid=uid
	// and coupons.state = 2 // 本来应该是IN(2,6),但是目前前端计算逻辑不一样，这里是为了和前端保持一致而做的调整
	// and coupon_batches.pay_mode in(1,2)
	err = d.base.Table("coupon_batches").
		Joins("RIGHT JOIN `coupons` ON `coupon_batches`.id = `coupons`.batch_id").
		Where("`coupons`.`uid` = ?", uid).
		Where("`coupons`.`state` = ?", CouponStateUsable).
		Where("`coupon_batches`.`applicable_pay_mode` IN (?)", []CouponPayMode{
			CouponPayModeScopeUnlimited, CouponPayModeScopePrepaid,
		}).
		Count(&count).Error

	if err != nil {
		err = errors.Trace(err)
		return 0, err
	}
	return count, nil
}
