package enums

// CreditStatus defines status type
type CreditStatus uint32

const (
	// UnknownCreditStatus is a unknown status
	UnknownCreditStatus = CreditStatus(iota) // 0
	// StatusUnAuthorized 未授信
	StatusUnAuthorized // 1
	// StatusUnActivated 未激活
	StatusUnActivated // 2
	// StatusValid 生效中
	StatusValid // 3
	// StatusSuspended 停用
	StatusRevoked // 4
)

func (s CreditStatus) String() string {
	switch s {
	case UnknownCreditStatus:
		return "UNKNOWN"
	case StatusUnAuthorized:
		return "UNAUTHORIZED"
	case StatusUnActivated:
		return "UNACTIVATED"
	case StatusValid:
		return "VALID"
	case StatusRevoked:
		return "REVOKED"
	}
	return "UNKNOWN"
}
