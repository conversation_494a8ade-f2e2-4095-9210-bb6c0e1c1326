package adapter

import (
	"time"

	pb "github.com/qbox/pay-sdk/wallet"
	"google.golang.org/protobuf/types/known/timestamppb"
	"qiniu.io/pay/qpay/payment/model"
)

func BuildPbListWithdrawableAssetsRequest(req *pb.ListWithdrawableAssetsRequest) *model.ListWithdrawableAssetsRequest {
	return &model.ListWithdrawableAssetsRequest{
		UID:                  req.Uid,
		NeedSimulationDeduct: req.NeedSimulationDeduct,
		NeedNiuCoin:          req.NeedNiuCoin,
		PaymentIDs:           req.PaymentIds,
	}
}

func BuildPbListWithdrawableAssetsResponse(list []model.WithdrawableAssetDetails) *pb.ListWithdrawableAssetsResponse {
	resp := &pb.ListWithdrawableAssetsResponse{
		List: make([]*pb.WithdrawableAsset, len(list)),
	}
	for i, obj := range list {
		var businessAt *timestamppb.Timestamp
		if obj.BusinessAt != nil && obj.BusinessAt.IsZero() {
			businessAt = timestamppb.New(*obj.BusinessAt)
		} else {
			businessAt = timestamppb.New(time.Unix(0, 0))
		}

		resp.List[i] = &pb.WithdrawableAsset{
			PaymentType:     int32(obj.PaymentType),
			CurrencyType:    string(obj.CurrencyType),
			PaymentId:       obj.PaymentID,
			AssetType:       int32(obj.AssetType),
			AssetId:         obj.AssetID,
			Uid:             obj.UID,
			PayUid:          obj.PayUID,
			EntryId:         obj.EntryID,
			PaymentAmount:   int64(obj.PaymentAmount),
			PaymentRemained: int64(obj.PaymentRemained),
			PaymentPaid:     int64(obj.PaymentPaid),
			RecordStatus:    pb.RecordStatus(obj.RecordStatus),
			Excode:          obj.Excode,
			Prefix:          string(obj.Prefix),
			Type:            obj.Type,
			RewardCost:      int64(obj.RewardCost),
			RewardType:      int32(obj.RewardType),
			RewardId:        obj.RewardID,
			IsHide:          obj.IsHide,
			BusinessAt:      businessAt,
			Amount:          int64(obj.Amount),
			Balance:         int64(obj.Balance),
			CreatedAt:       timestamppb.New(obj.CreatedAt),
		}
	}
	return resp
}
