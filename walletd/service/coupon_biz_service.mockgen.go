// Code generated by MockGen. DO NOT EDIT.
// Source: walletd/service/coupon_biz_service.go

package service

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	base "github.com/qbox/bo-base/v4/base"
)

// MockCouponWithScope is a mock of CouponWithScope interface.
type MockCouponWithScope struct {
	ctrl     *gomock.Controller
	recorder *MockCouponWithScopeMockRecorder
}

// MockCouponWithScopeMockRecorder is the mock recorder for MockCouponWithScope.
type MockCouponWithScopeMockRecorder struct {
	mock *MockCouponWithScope
}

// NewMockCouponWithScope creates a new mock instance.
func NewMockCouponWithScope(ctrl *gomock.Controller) *MockCouponWithScope {
	mock := &MockCouponWithScope{ctrl: ctrl}
	mock.recorder = &MockCouponWithScopeMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCouponWithScope) EXPECT() *MockCouponWithScopeMockRecorder {
	return m.recorder
}

// GetCouponDeadTime mocks base method.
func (m *MockCouponWithScope) GetCouponDeadTime() base.HNS {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCouponDeadTime")
	ret0, _ := ret[0].(base.HNS)
	return ret0
}

// GetCouponDeadTime indicates an expected call of GetCouponDeadTime.
func (mr *MockCouponWithScopeMockRecorder) GetCouponDeadTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCouponDeadTime", reflect.TypeOf((*MockCouponWithScope)(nil).GetCouponDeadTime))
}

// GetCouponEffectTime mocks base method.
func (m *MockCouponWithScope) GetCouponEffectTime() base.HNS {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCouponEffectTime")
	ret0, _ := ret[0].(base.HNS)
	return ret0
}

// GetCouponEffectTime indicates an expected call of GetCouponEffectTime.
func (mr *MockCouponWithScopeMockRecorder) GetCouponEffectTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCouponEffectTime", reflect.TypeOf((*MockCouponWithScope)(nil).GetCouponEffectTime))
}

// GetScope mocks base method.
func (m *MockCouponWithScope) GetScope() *ScopeDetail {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScope")
	ret0, _ := ret[0].(*ScopeDetail)
	return ret0
}

// GetScope indicates an expected call of GetScope.
func (mr *MockCouponWithScopeMockRecorder) GetScope() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScope", reflect.TypeOf((*MockCouponWithScope)(nil).GetScope))
}
