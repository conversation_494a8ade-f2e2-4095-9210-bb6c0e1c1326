package service_test

import (
	"testing"

	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/dictd/model"
	"qiniu.io/pay/dictd/service"
)

type sandbox struct {
	testWrap    *test.Wrap
	dictService *service.DictBizService
}

func buildSandbox(t *testing.T) *sandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in dict/service return error")
	}

	dictDao := model.NewDictDao(testWrap.BaseDao())
	dictService := service.NewDictBizService(dictDao, dao.CacheExpiresNoCache)

	return &sandbox{
		testWrap:    testWrap,
		dictService: dictService,
	}
}
