package qrn

import "regexp"

type matchExprInput struct {
	// productMatchExact 指定精确匹配 QRN 的 Product 部分为何。
	productMatchExact string
	// resourceMatchRegex 用来匹配 QRN Resource 部分字符串的正则表达式。
	// 只会在字符串开头匹配（即在该表达式的开头隐含了 ^ 的操作）。
	resourceMatchRegex string
}

type ParsedMatchExpr struct {
	p string
	r *regexp.Regexp
}

func (e *matchExprInput) makeParsedMatchExpr() (*ParsedMatchExpr, error) {
	r, err := regexp.Compile("^" + e.resourceMatchRegex)
	if err != nil {
		return nil, err
	}

	return &ParsedMatchExpr{
		p: e.productMatchExact,
		r: r,
	}, nil
}

// GetProductMatchExact 返回该表达式精确匹配 QRN 的 Product 部分为何。
func (e *ParsedMatchExpr) GetProductMatchExact() string {
	return e.p
}

// Matches 返回给定的 QRN 是否被当前表达式匹配。
func (e *ParsedMatchExpr) Matches(x *QRN) bool {
	if x == nil {
		return false
	}

	if x.Product != e.p {
		return false
	}

	return e.r.MatchString(x.Resource)
}
