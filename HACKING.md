# 开发指南及注意事项

## 注意事项

### 循环变量

**Go 语言的循环变量只有一个地址，并不是每次循环都被赋予一次“新的生命”。切记！**

很多别的语言中，循环变量在每次循环都相当于一个新的量，很方便。例如：

```js
let a = [{x: 1}, {x: 2}, {x: 3}, {x: 4}, {x: 5}];
let b = [];
for (let x of a) {
  if (x.x % 2 !== 0) {
    b.push(x);
  }
}

console.log(b);  // [ { x: 1 }, { x: 3 }, { x: 5 } ]
```

然而在 Go 语言中，你经常会以为 Go 作为一门“高级语言”，for 循环使用起来肯定也一样方便；
实际上，Go 的循环变量跟 C 语言的一样，只有一个身份，不会刷新。

```go
package main

import "fmt"

type T struct {
	X int
}

func main() {
	a := []T{{1}, {2}, {3}, {4}, {5}}

	var b []*T
	for _, x := range a {
		if x.X%2 != 0 {
			b = append(b, &x)
		}
	}

	fmt.Printf("%v\n", b)
}

// example output:
//
// [0xc0000180c8 0xc0000180c8 0xc0000180c8]
```

其他一些更加负责任的语言，即便循环变量实际上也只有一个，但不会让你乱用：

```rust
#[derive(Debug)]
struct T {
    a: i32,
}

fn main() {
    let x: Vec<T> = vec![1, 2, 3, 4, 5].into_iter().map(|x| T{a: x}).collect();

    let mut y = Vec::new();
    for ref i in x {
        if i.a % 2 != 0 {
            y.push(i);
        }
    }

    println!("{:?}", y);
}
```

会编译出错：

```
error[E0597]: `__next` does not live long enough
  --> x.rs:10:9
   |
10 |     for ref i in x {
   |         ^^^^^ borrowed value does not live long enough
...
14 |     }
   |     - `__next` dropped here while still borrowed
...
17 | }
   | - borrowed value needs to live until here
```


### map 类型变量、字段等一定要合理命名

使用 `map[K]V` 类型时，为避免望文生义将变量理解为 `[]T` 类型，在这种情况下必须在变量名中带上
`map`、`set` 等提示语（字段同理）：

```go
// param.ItemPrices 是 map[uint64]ItemPriceParam
// 这里的本意是从 item ID 到 ItemPriceParam 的映射，用 map 语义避免传入多个针对相同 item 的报价
// 但因为是 struct 的字段，因此在使用的地方光看一个文件（如 GitHub PR review 界面）不知道它的类型

if len(param.ItemPrices) != 1 {
	return nil, errors.New("should have one public price")
}

// ...

// 此处是想拿出里面唯一的值
itemPriceParam := param.ItemPrices[0]
return s.CreateItemPrice(ctx, priceTableID, &itemPriceParam)
```

此处由于单纯看调用方的代码看不到字段类型，在 code review
时极难发现脑海中认为的类型是错误的。此处做了迷你小调查，该段代码的研发、reviewer、QA
以及别组的一位前端小伙伴均未能发现问题。Go 语言中 map 的使用姿势与 slice
相当一致，加上 map 访问不存在的 key 永远不会崩溃，进一步加重了 code review
的心智负担。因此为了将这部分不必要的心智负担去除，在不能弃用 Go
语言的前提下，只好通过合理命名（本质是一种文档）的方式解决。


### 禁用 `db.Where(value)` 方式构造可能传入零值的 gorm WHERE 子句

使用传入参数构造 WHERE 子句时，如果传入参数可能为零值（非主键的绝大多数情况），禁止直接以其构造
`struct` 传入 `Where()`：

```go
// var code int64
// string 等类型同理
model := &Zone{Code: code}

// ...

_ = d.base.Where(value).First(value)
```

原因：Go 反射机制无法区分传入参数为零值时，相应的字段有没有实际被设置。因此
gorm 此种情况下会以为你没有按相应字段过滤，导致生成了不含该字段的 SQL 查询。因此禁止除按照
ID 查询以外的任何其他情况使用。（ID 不为零的判断在请求参数校验阶段即完成，因此没有风险。）
