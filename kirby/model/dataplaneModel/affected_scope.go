package dataplaneModel

import (
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/samber/lo"
)

type uidAndAggR struct {
	UID  uint64 `gorm:"column:uid"`
	AggR string `gorm:"column:aggr"`
}

type affectedScope struct {
	// 查询哪些 uids 的全部聚合规则的数据
	uids []uint64
	// 查询哪些聚合规则的全部 uids 的数据
	aggrs []string
	// 查询哪些单独进行匹配的 (uid, aggr) 的数据
	outliers []uidAndAggR
}

// 当影响范围中某个 uid 下属的序列至少涉及了多少条不同的聚合规则，那么查询该 uid 的全量聚合规则的序列数据
const defaultUIDAggRCountThreshold = 100

// 当影响范围中某条聚合规则匹配的序列至少涉及了多少个不同的 uids，那么查询该聚合规则的全量 uids 的序列数据
const defaultAggRUIDCountThreshold = 10000

func compactAffectedScope(input []uidAndAggR) affectedScope {
	return doCompactAffectedScope(input, defaultAggRUIDCountThreshold, defaultUIDAggRCountThreshold)
}

// NOTE: 将此两个阈值参数化的目的是方便测试：这样测试用例不用写特别特别多记录
func doCompactAffectedScope(
	input []uidAndAggR,
	aggrUIDCountThreshold int,
	uidAggRCountThreshold int,
) affectedScope {
	var result affectedScope

	// aggr 比 uids 会少很多，这样 map keys 不会太多
	// 将涉及很多 uids 的聚合规则都单独拉出来
	inputGroupedByAggR := lo.GroupBy(input, func(x uidAndAggR) string { return x.AggR })
	var remaining []uidAndAggR
	for aggrCode, records := range inputGroupedByAggR {
		if len(records) >= aggrUIDCountThreshold {
			result.aggrs = append(result.aggrs, aggrCode)
			continue
		}
		remaining = append(remaining, records...)
	}

	// 将剩余的记录中，涉及很多聚合规则的 uids 再单独拉出来
	// 剩下的部分是仍然要单独构造查询条件的 outliers
	aggrsGroupedByUID := lo.GroupBy(remaining, func(x uidAndAggR) uint64 { return x.UID })
	for uid, records := range aggrsGroupedByUID {
		if len(records) >= uidAggRCountThreshold {
			result.uids = append(result.uids, uid)
			continue
		}
		result.outliers = append(result.outliers, records...)
	}

	return result
}

func (x *affectedScope) isEmpty() bool {
	return x == nil || len(x.aggrs) == 0 && len(x.uids) == 0 && len(x.outliers) == 0
}

func (x *affectedScope) toSqlizer(epoch time.Time) squirrel.Sqlizer {
	var y squirrel.Or

	if len(x.aggrs) > 0 {
		y = append(y, squirrel.Eq{"aggr": x.aggrs})
	}

	if len(x.uids) > 0 {
		y = append(y, squirrel.Eq{"uid": x.uids})
	}

	if len(x.outliers) > 0 {
		y = append(y, lo.Map(x.outliers, func(a uidAndAggR, _ int) squirrel.Sqlizer {
			return squirrel.Eq{"uid": a.UID, "aggr": a.AggR}
		})...)
	}

	epochCondition := squirrel.LtOrEq{"created_at": epoch}

	if len(y) == 0 {
		return epochCondition
	}

	return squirrel.And{epochCondition, y}
}
