package model

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/samber/lo"
)

// PriceItemStair model definition
type PriceItemStair struct {
	ID          uint64       `gorm:"primary_key"`
	PriceItemID uint64       `gorm:"unique_index:idx_price_item_stair_price_item_id_quantity_order"`
	UnitID      uint64       `gorm:"index:idx_price_item_stair_unit_id"`
	Quantity    uint64       `gorm:"unique_index:idx_price_item_stair_price_item_id_quantity_order"`
	Price       *base.NMoney `gorm:"column:price;type:DECIMAL(20,8);not null;comment:'阶梯价格，8 位小数的定点数'"`
	Order       uint64       `gorm:"unique_index:idx_price_item_stair_price_item_id_quantity_order"`
	CreatedAt   time.Time    `sql:"type:DATETIME(6)"`
	UpdatedAt   time.Time    `sql:"type:DATETIME(6)"`
}

// Clone new a instance of PriceItemStair same as m
func (m *PriceItemStair) Clone() PriceItemStair {
	return *m
}

// PriceItemStairDao is data access object of PriceItemStair model
type PriceItemStairDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *PriceItemStairDao) GetCachePrefix() string {
	return "price:price_item_stair:"
}

// GetCacheRefs get cache refs
func (d *PriceItemStairDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewPriceItemStairDao is constructor of PriceItemStairDao
func NewPriceItemStairDao(base *dao.BaseDao) *PriceItemStairDao {
	cachePrefix := (*PriceItemStairDao)(nil).GetCachePrefix()
	return &PriceItemStairDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id:{ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of PriceItemStair by id
func (d *PriceItemStairDao) GetByID(id uint64, expires ...time.Duration) (*PriceItemStair, error) {
	model := &PriceItemStair{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, err
}

// Save insert or update a record of PriceItemStair by id
func (d *PriceItemStairDao) Save(model *PriceItemStair, expires ...time.Duration) error {
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// BulkInsert 批量插入一组 PriceItemStairs
func (d *PriceItemStairDao) BulkInsert(ctx context.Context, stairs []PriceItemStair) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.BulkInsert(ctx, stairs)
	}, d.keys, nil, d)
}

// Delete delete a record of PriceItemStair
func (d *PriceItemStairDao) Delete(model *PriceItemStair) error {
	err := d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

// DeleteByID delete a record of PriceItemStair by id
func (d *PriceItemStairDao) DeleteByID(id uint64) error {
	model := &PriceItemStair{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}

// List select records of PriceItemStair
func (d *PriceItemStairDao) List(offset, limit int, expires ...time.Duration) (stairs []PriceItemStair, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:offset=%d&limit=%d",
		d.cachePrefix, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Offset(offset).Limit(limit).Find(value).Error
	}, keys, &stairs, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("offset", offset).WithField("limit", limit)
	}
	return
}

// Count select count of PriceItemStair
func (d *PriceItemStairDao) Count(expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeys(
		"%s:count",
		d.cachePrefix,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceItemStair{}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err)
	}
	return
}

// ListByPriceItemID select records of PriceItemStair by price_item_id
func (d *PriceItemStairDao) ListByPriceItemID(priceItemID uint64, offset, limit int, expires ...time.Duration) (stairs []PriceItemStair, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:price_item_id=%d&offset=%d&limit=%d",
		d.cachePrefix, priceItemID, offset, limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&PriceItemStair{PriceItemID: priceItemID}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &stairs, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"price_item_id": priceItemID,
			"offset":        offset,
			"limit":         limit,
		})
	}
	return
}

// CountByPriceItemID select count of PriceItemStair by price_item_id
func (d *PriceItemStairDao) CountByPriceItemID(priceItemID uint64, expires ...time.Duration) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:price_item_id=%d:count",
		d.cachePrefix, priceItemID,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&PriceItemStair{}).Where(&PriceItemStair{PriceItemID: priceItemID}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithField("price_item_id", priceItemID)
	}
	return
}

// BatchListByPriceItemIDs select records of PriceItemStair by price_item_id
func (d *PriceItemStairDao) BatchListByPriceItemIDs(priceItemIDs []uint64, expires ...time.Duration) (stairs []PriceItemStair, err error) {
	if len(priceItemIDs) == 0 {
		return []PriceItemStair{}, nil
	}
	sort.Stable(base.Uint64Slice(priceItemIDs))

	// 实践中遇到 len(priceItemIDs) == 80000，超大的数据集对于 redis 和 mysql 网络 IO 以及查询都是极大的负担，
	// slice 扩容算法面对大数据集也会造成很大的时空开销。
	// 优化前耗时 2 分多钟。改为并发查询，每批最多查询 2000 条，优化后耗时 738.587442ms，性能差别近 300 倍。
	chunks := lo.Chunk(priceItemIDs, 2000)

	oks, err := resultgroup.ThrottledParallelMap(chunks, 50, func(ids []uint64) ([]PriceItemStair, error) {
		priceItemIDsStr := base.UintSliceJoin(ids, ",")

		keys := dao.NewCacheKeysFmt(
			"%s:price_item_ids=%s",
			d.cachePrefix, priceItemIDsStr,
		)
		var itemStairs []PriceItemStair
		err1 := d.base.QueryWithSetCache(func(value any) error {
			return d.base.Model(&PriceItemStair{}).
				Where(fmt.Sprintf("`price_item_id` in (%s)", priceItemIDsStr)).Find(value).Error
		}, keys, &itemStairs, expires...)
		if err1 != nil {
			return nil, errors.Trace(err1).WithFields(errors.Fields{
				"price_item_ids":   priceItemIDsStr,
				"price_item_count": len(priceItemIDs),
			})
		}
		return itemStairs, nil
	})
	if err != nil {
		return nil, err
	}

	for _, ok := range oks {
		stairs = append(stairs, ok...)
	}
	return stairs, nil
}
