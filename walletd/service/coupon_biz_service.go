package service

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/sync/errgroup"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/uuid"
	"github.com/qbox/pay-sdk/middleware/logging"
	walletop "github.com/qbox/pay-sdk/v3/wallet/client/operations"
	wallet "github.com/qbox/pay-sdk/v3/wallet/models"
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
)

var errInconsistentCurrencyTypeForRebate = errors.New("inconsistent currency type for rebate")

// BindCouponRebate 为用户绑定指定 CouponRebate
func (s *WalletBizService) BindCouponRebate(
	ctx context.Context,
	param *BindCouponRebateParam,
) (*BindCouponRebateResp, error) {
	var eg errgroup.Group

	var rebate *model.CouponRebate
	eg.Go(func() error {
		resp, err := s.GetCouponRebateByCode(ctx, param.Code)
		if err != nil {
			return errors.Annotate(err, "query coupon rebate failed").
				WithField("code", param.Code)
		}
		rebate = resp
		return nil
	})

	var userCurrecyType base.CurrencyType
	eg.Go(func() error {
		paymentSrv, err := s.getPaymentServiceClient()
		if err != nil {
			return errors.Annotate(err, "get payment service failed")
		}
		resp, err := paymentSrv.GetSingleCurrency(ctx, &pb.UIDParam{Uid: param.UID})
		if err != nil {
			return errors.Annotate(err, "query user currency type failed").WithField("uid", param.UID)
		}
		userCurrecyType = base.CurrencyType(resp.CurrencyType)
		return nil
	})

	err := eg.Wait()
	if err != nil {
		return nil, err
	}

	// 检查币种，用户币种配置必须和 rebate 一致
	if rebate.CurrencyType != userCurrecyType {
		return nil, errInconsistentCurrencyTypeForRebate
	}

	s.lockUserForCouponBiz(param.UID)
	defer s.unlockUserForCouponBiz(param.UID)

	var m *model.CouponUserMap
	if param.Idempotent {
		existingBinding, err := s.walletDao.CouponUserMap.GetExistingBindingInTimeRange(
			param.UID,
			rebate.ID,
			model.CouponTypeRebate,
			param.ZoneID,
			param.EffectTime,
			param.DeadTime,
		)
		if err != nil {
			return nil, err
		}

		if existingBinding != nil {
			m = existingBinding
		}
	}

	if m == nil {
		m = &model.CouponUserMap{
			CouponID:      rebate.ID,
			UID:           param.UID,
			ZoneID:        param.ZoneID,
			TransactionID: param.TransactionID,
			Type:          model.CouponTypeRebate,
			EffectTime:    param.EffectTime,
			DeadTime:      param.DeadTime,
		}

		err = s.walletDao.CouponUserMap.Save(m, s.cacheExpires)
		if err != nil {
			return nil, errors.Annotate(err, "save coupon_user_map failed").
				WithField("coupon_user_map", m)
		}
	}

	rebate.EffectTime = param.EffectTime
	rebate.DeadTime = param.DeadTime

	return &BindCouponRebateResp{
		Coupon:        *rebate,
		CouponUserMap: *m,
	}, nil
}

// UnbindCouponRebate 解绑指定 CouponRebate
// NOTE: 目前仅解绑 rebate 绑定了一个用户的情况，若 rebate 绑定了多个用户那么报错
func (s *WalletBizService) UnbindCouponRebate(
	ctx context.Context,
	code string,
) error {
	// TODO 加锁
	rebate, err := s.GetCouponRebateByCode(ctx, code)
	if err != nil {
		return errors.Annotate(err, "query coupon rebate failed").
			WithField("code", code)
	}

	couponUserMaps, err := s.walletDao.CouponUserMap.GetByCouponID(rebate.ID)
	if err != nil {
		return errors.Trace(err)
	}

	if len(couponUserMaps) != 1 {
		return errors.New("couponRebate is not bound or bound to multiple uid").
			WithField("coupon_id", rebate.ID)
	}

	err = s.walletDao.CouponUserMap.Delete(&couponUserMaps[0])
	if err != nil {
		return errors.Annotate(err, "delete coupon_user_map failed").
			WithField("coupon_user_map", couponUserMaps[0])
	}

	return nil
}

// UpgradeCouponRebate 为用户升级 CouponRebate
func (s *WalletBizService) UpgradeCouponRebate(
	ctx context.Context,
	param *UpgradeCouponRebateParam,
) (*BindCouponRebateResp, error) {
	srcCouponRebate, err := s.GetCouponRebateByCode(ctx, param.SrcCode)
	if err != nil {
		return nil, errors.Annotate(err, "query src coupon rebate failed").
			WithField("code", param.SrcCode)
	}

	dstCouponRebate, err := s.GetCouponRebateByCode(ctx, param.DstCode)
	if err != nil {
		return nil, errors.Annotate(err, "query dst coupon rebate failed").
			WithField("code", param.SrcCode)
	}

	s.lockUserForCouponBiz(param.UID)
	defer s.unlockUserForCouponBiz(param.UID)

	// 同一个用户可能绑定多个相同 code 的 rebate
	couponUserMaps, err := s.ListCouponUserMapsByUID(ctx, param.UID, 0, -1)
	if err != nil {
		return nil, errors.Annotate(err, "list coupon user map failed").
			WithField("uid", param.UID)
	}

	effectedCUMs := make([]*model.CouponUserMap, 0)
	for _, cum := range couponUserMaps {
		if cum.Type == model.CouponTypeRebate &&
			cum.ZoneID == param.ZoneID &&
			cum.CouponID == srcCouponRebate.ID {
			effectedCUMs = append(effectedCUMs, &cum)
		}
	}

	// NOTE: 现在的响应结构只能回传 1 个 CouponUserMap，然后考虑到一次 upgrade 多个 packages
	// 的需求在业务上暂时想不到，先禁掉这个情况，于是一次最多影响一个 CouponUserMap 了
	if len(effectedCUMs) > 1 {
		return nil, fmt.Errorf("upgrading >1 coupons is currently unsupported: couponUserMaps=%+v", effectedCUMs)
	}
	var m *model.CouponUserMap

	// 调整原有 rebate 生效时间，并绑定新 rebate
	for _, cum := range effectedCUMs {
		if cum.EffectTime <= param.EffectTime && cum.DeadTime > param.EffectTime {
			cum.DeadTime = param.EffectTime
		}

		err = s.walletDao.CouponUserMap.Save(cum, s.cacheExpires)
		if err != nil {
			return nil, errors.Annotate(err, "save coupon_user_map failed").
				WithField("coupon_user_map", cum)
		}

		// bind new rebate
		// NOTE: 如果解除上面 NOTE 部分的限制，这个 = 要变成 :=
		m = &model.CouponUserMap{
			CouponID:      dstCouponRebate.ID,
			UID:           param.UID,
			ZoneID:        param.ZoneID,
			TransactionID: param.TransactionID,
			Type:          model.CouponTypeRebate,
			EffectTime:    param.EffectTime,
			DeadTime:      param.DeadTime,
		}

		err = s.walletDao.CouponUserMap.Save(m, s.cacheExpires)
		if err != nil {
			return nil, errors.Annotate(err, "save coupon_user_map failed").
				WithField("coupon_user_map", m)
		}
	}

	dstCouponRebate.EffectTime = param.EffectTime
	dstCouponRebate.DeadTime = param.DeadTime

	return &BindCouponRebateResp{
		Coupon:        *dstCouponRebate,
		CouponUserMap: *m,
	}, nil
}

// BindCoupon 为用户绑定指定 Coupon
func (s *WalletBizService) BindCoupon(
	ctx context.Context,
	param *BindCouponParam,
) (*model.CouponUserMap, error) {

	m := &model.CouponUserMap{
		CouponID:      param.CouponID,
		UID:           param.UID,
		ZoneID:        param.ZoneID,
		TransactionID: param.TransactionID,
		Type:          param.CouponType,
		EffectTime:    param.EffectTime,
		DeadTime:      param.DeadTime,
	}

	err := s.walletDao.CouponUserMap.Save(m, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "save coupon_user_map failed").
			WithField("coupon_user_map", m)
	}

	return m, nil
}

func (s *WalletBizService) genCouponCode(uid uint64, couponType string) string {
	return fmt.Sprintf("%d:%s:%s", uid, couponType, uuid.New())
}

// CouponWithScope defines common interface of CouponDiscountWithScope & CouponRebateWithScope
type CouponWithScope interface {
	GetScope() *ScopeDetail
	GetCouponEffectTime() base.HNS
	GetCouponDeadTime() base.HNS
}

type reqRegenBaseBillsKey struct {
	UID   int64
	Start base.HNS
	End   base.HNS
}

type regenBillsMapType map[reqRegenBaseBillsKey]*wallet.Scope

func (s *WalletBizService) alignCouponWithScope(uid int64,
	couponWithScopes []CouponWithScope,
	reqRegenBillsMap regenBillsMapType) regenBillsMapType {
	for _, couponWithScope := range couponWithScopes {
		key := reqRegenBaseBillsKey{
			UID:   uid,
			Start: couponWithScope.GetCouponEffectTime(),
			End:   couponWithScope.GetCouponDeadTime(),
		}

		scope := couponWithScope.GetScope()
		// 历史上没有，那么新添加，并初始化为空 map
		if _, ok := reqRegenBillsMap[key]; !ok {
			reqRegenBillsMap[key] = &wallet.Scope{
				All:      scope.IsAll,
				Groups:   make(map[string]int64),
				Items:    make(map[string]int64),
				Products: make(map[string]int64),
			}
		}

		// 取数据
		regenBillsScope := reqRegenBillsMap[key]
		for _, scopeProduct := range scope.Products {
			// 之前没有，那么新加
			if _, hasProduct := regenBillsScope.Products[scopeProduct.ProductCode]; !hasProduct {
				regenBillsScope.Products[scopeProduct.ProductCode] = 1
			}
		}
		for _, scopeGroup := range scope.ItemGroups {
			if _, hasGroup := regenBillsScope.Groups[scopeGroup.GroupCode]; !hasGroup {
				regenBillsScope.Groups[scopeGroup.GroupCode] = 1
			}
		}
		for _, scopeItem := range scope.Items {
			if _, hasItem := regenBillsScope.Items[scopeItem.ItemCode]; !hasItem {
				regenBillsScope.Items[scopeItem.ItemCode] = 1
			}
		}
	}

	return reqRegenBillsMap
}

func (s *WalletBizService) alignRegenBillsScope(
	uid int64,
	couponDiscounts []*CouponDiscountWithScope,
	couponRebates []*CouponRebateWithScope,
) map[reqRegenBaseBillsKey]*wallet.Scope {
	reqRegenBillsMap := make(regenBillsMapType)

	// convert to interface array
	couponsImplByDiscount := make([]CouponWithScope, len(couponDiscounts))
	for i, couponDiscount := range couponDiscounts {
		couponsImplByDiscount[i] = couponDiscount
	}
	// NOTE: 先聚合 discount 所有的重出账参数
	reqRegenBillsMap = s.alignCouponWithScope(uid, couponsImplByDiscount, reqRegenBillsMap)

	couponsImplByRebate := make([]CouponWithScope, len(couponRebates))
	for i, couponRebate := range couponRebates {
		couponsImplByRebate[i] = couponRebate
	}
	// NOTE: 聚合 discount 以及 rebate 所有的重出账参数
	reqRegenBillsMap = s.alignCouponWithScope(uid, couponsImplByRebate, reqRegenBillsMap)

	return reqRegenBillsMap
}

// RegenBills 对相关时间段、product、item、group 进行重出账
func (s *WalletBizService) RegenBills(ctx context.Context,
	uid int64,
	couponDiscounts []*CouponDiscountWithScope,
	couponRebates []*CouponRebateWithScope) (err error) {

	log := logging.GetLogger(ctx)
	log.Info("start regen bills")

	// NOTE: 把 key 相同的重出账参数（以uid、时间、zone 为 key）聚合到一起，减少调用 RegenBills 次数
	alignedRegenBaseBills := s.alignRegenBillsScope(uid, couponDiscounts, couponRebates)

	for key, val := range alignedRegenBaseBills {
		regenBillsParams := walletop.NewRegenBillsParamsWithContext(ctx)
		reqRegenBaseBill := &wallet.ReqRegenBasebill{
			// TODO 时间转换是否正确，需要判断 end 和 now ？
			UID:   key.UID,
			Start: key.Start.TimeIn(time.UTC).Unix(),
			End:   key.End.TimeIn(time.UTC).Unix(),
			Scope: val,
		}
		regenBillsParams.WithReqRegenBasebill(reqRegenBaseBill)

		log.WithFields(logrus.Fields{
			"regen_bill_params": regenBillsParams,
		}).Info("start regen bills for discount/rebate")

		_, err = s.walletV3.Operations.RegenBills(regenBillsParams)
		if err != nil {
			log.Errorf("<WalletBizService.RegenBills> walletV3.RegenBills error: %+v", err)
			return errors.Annotate(err, "regen bills for price_item failed").
				WithFields(errors.Fields{
					"regen_bill_params": regenBillsParams,
				})
		}
	}

	return nil
}

func (s *WalletBizService) CreateDiscountRebateAndRegenBills(ctx context.Context,
	uid uint64,
	couponDiscounts []*CouponDiscountWithScope,
	couponRebates []*CouponRebateWithScope,
	needRegenBills bool,
) error {

	log := logging.GetLogger(ctx)
	// NOTE: 注意，如果 couponDiscounts, couponRebates 里的相关ID缺失，这里会补充.
	err := s.supplementIDByCode(ctx, couponDiscounts, couponRebates)
	if err != nil {
		return err
	}

	// 如果有 rebate，以用户的币种为 rebate 的币种
	if len(couponRebates) > 0 {
		paymentCl, err := s.getPaymentServiceClient()
		if err != nil {
			return err
		}

		ct, err := paymentCl.GetSingleCurrency(ctx, &pb.UIDParam{Uid: uid})
		if err != nil {
			return err
		}

		for i := range couponRebates {
			couponRebates[i].CouponRebate.CurrencyType = base.CurrencyType(ct.CurrencyType)
		}
	}

	// 如果某个传入的 coupon 定义的 code 不为空，则判断是否已经创建过 discount 和 rebate 并绑定
	// 若已创建那么不重复创建，若已绑定那么不重复绑定
	// TODO 事务
	discountBindingChanged, err := s.BatchCreateBindCouponDiscount(ctx, uid, couponDiscounts)
	if err != nil {
		log.Errorf("<WalletAction.CreateDiscountRebateAndRegenBills> BatchCreateBindCouponDiscount error: %+v", err)
		return err
	}

	// TODO 有重复代码，可重构
	rebateBindingChanged, err := s.BatchCreateBindCouponRebate(ctx, uid, couponRebates)
	if err != nil {
		log.Errorf("<WalletAction.CreateDiscountRebateAndRegenBills> BatchCreateBindCouponRebate error: %+v", err)
		return err
	}

	bindingsChanged := discountBindingChanged || rebateBindingChanged

	// 判断是否需要重出账
	if needRegenBills {
		// 如果没产生新的绑定就不用重出了
		if !bindingsChanged {
			log.WithFields(logrus.Fields{
				"uid":             uid,
				"couponDiscounts": couponDiscounts,
				"couponRebates":   couponRebates,
			}).Info("<WalletAction.CreateDiscountRebateAndRegenBills> regen bill requested but bindings are fresh, skipping")
			return nil
		}

		err = s.RegenBills(ctx, int64(uid), couponDiscounts, couponRebates)
		if err != nil {
			log.Errorf("<WalletAction.CreateDiscountRebateAndRegenBills> RegenBills error: %+v", err)
			return err
		}
	}
	return nil
}
