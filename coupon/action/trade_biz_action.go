package action

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/coupon/action/adapter"

	pb "github.com/qbox/pay-sdk/wallet"
)

// ConsumeCouponByUnionOrderHash 使用一张已被 union_order 锁定的券
func (a *CouponAction) ConsumeCouponByUnionOrderHash(ctx context.Context, req *pb.CodeParam) (*pb.ConsumeCouponResp, error) {
	srvResp, err := a.couponSrv.ConsumeCouponByUnionOrderHash(ctx, req.Code)
	if err != nil {
		return nil, errors.Trace(err)
	}

	resp, err := adapter.BuildPbConsumeCouponResp(srvResp)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// LockCouponToUnionOrder 锁定一张券到 union_order
func (a *CouponAction) LockCouponToUnionOrder(ctx context.Context, req *pb.LockCouponToUnionOrderParam) (*pb.LockCouponResp, error) {
	srvPoLockDetails := adapter.BuildPOLockDetails(req.GetPoLockDetails())

	srvResp, err := a.couponSrv.LockCouponsToUnionOrder(
		ctx,
		req.GetUid(),
		req.GetUnionOrderHash(),
		srvPoLockDetails,
	)
	if err != nil {
		return nil, errors.Trace(err).
			WithField("req", req).
			WithField("poDetails", srvPoLockDetails)
	}

	resp, err := adapter.BuildPbLockCouponResp(srvResp)
	if err != nil {
		return nil, errors.Trace(err).
			WithField("req", req).
			WithField("resp", srvResp)
	}
	return resp, nil
}

// UnlockCouponByUnionOrderHash 解锁一张被 union_order 锁定的券
func (a *CouponAction) UnlockCouponByUnionOrderHash(ctx context.Context, req *pb.CodeParam) (*pb.LockCouponResp, error) {
	srvResp, err := a.couponSrv.UnlockCouponByUnionOrderHash(ctx, req.GetCode(), time.Now())
	if err != nil {
		return nil, errors.Trace(err)
	}

	resp, err := adapter.BuildPbLockCouponResp(srvResp)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// CountTradeCoupon 获取订单优惠券数量
func (a *CouponAction) CountTradeCoupon(ctx context.Context, req *pb.UIDParam) (*pb.CountTradeCouponResponse, error) {
	count, err := a.couponSrv.CountTradeCoupon(ctx, req.GetUid())
	if err != nil {
		return nil, err
	}
	return &pb.CountTradeCouponResponse{CouponCount: count}, nil
}
