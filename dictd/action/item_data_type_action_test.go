package action_test

import (
	"context"
	"testing"

	"github.com/qbox/pay-sdk/dict"
	"github.com/stretchr/testify/assert"
)

func TestItemDataTypeInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	dictClient := sandbox.dictClient

	const itemID uint64 = 1
	ts := []dict.ItemDataType{
		{
			ItemId:    itemID,
			Name:      "流量",
			Type:      "flow",
			UnitRate:  1024,
			IsDefault: true,
		},
		{
			ItemId:    itemID,
			Name:      "带宽",
			Type:      "bandwidth",
			UnitRate:  1000,
			IsDefault: false,
		},
	}
	assertFields := func(group *dict.ItemDataType, n int, msgAndArgs ...any) {
		if n >= len(ts) {
			return
		}
		assert.Equal(t, ts[n].GetItemId(), group.GetItemId(), msgAndArgs...)
		assert.Equal(t, ts[n].GetName(), group.GetName(), msgAndArgs...)
		assert.Equal(t, ts[n].GetUnitRate(), group.GetUnitRate(), msgAndArgs...)
		assert.Equal(t, ts[n].GetIsDefault(), group.GetIsDefault(), msgAndArgs...)
	}
	ids := make([]uint64, len(ts))
	for n, _t := range ts {
		dataType, err := dictClient.CreateItemDataType(context.Background(), &_t)
		if assert.NoError(t, err, "CreateItemDataType") {
			assert.NotZero(t, dataType.GetId(), "CreateItemDataType")
			ids[n] = dataType.GetId()
		}

		dataType, err = dictClient.GetItemDataTypeByID(context.Background(), &dict.IDParam{Id: dataType.GetId()})
		if assert.NoError(t, err, "GetItemDataTypeByID") {
			assertFields(dataType, n, "GetItemDataTypeByID")
		}

		dataType.Name += "suffix"
		ts[n].Name += "suffix"
		dataType, err = dictClient.UpdateItemDataTypeByID(context.Background(), &dict.IDItemDataTypeParam{Id: dataType.GetId(), ItemDataType: dataType})
		if assert.NoError(t, err, "UpdateItemDataTypeByID") {
			assert.Equal(t, ts[n].GetName(), dataType.GetName(), "UpdateItemDataTypeByID")
		}
	}

	count, err := dictClient.CountItemDataTypesByItemID(context.Background(), &dict.IDParam{Id: itemID})
	if assert.NoError(t, err, "CountItemDataTypesByItemID") {
		assert.Len(t, ts, int(count.GetCount()), "CountItemDataTypesByItemID")
	}

	list, err := dictClient.ListItemDataTypesByItemID(context.Background(), &dict.IDPagingParam{Id: itemID, Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListItemDataTypesByItemID") {
		assert.Len(t, ts, int(list.GetCount()), "ListItemDataTypesByItemID")
		for _, dt := range list.GetItemDataTypes() {
			for i, id := range ids {
				if id == dt.Id {
					assertFields(dt, i, "ListItemDataTypesByItemID")
				}
			}
		}
	}

	for _, id := range ids {
		_, err = dictClient.DeleteItemDataTypeByID(context.Background(), &dict.IDParam{Id: id})
		assert.NoError(t, err, "DeleteItemDataTypeByID")
	}
}
