package action

import (
	"context"
	"strconv"
	"testing"

	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/stretchr/testify/assert"
)

func TestPaymentAction_DeductOrderPre(t *testing.T) {
	sandbox := BuildSandbox(t)
	var uid uint64 = 1382088642
	prepareCurrency(t, sandbox, uid)
	_, err := sandbox.PaymentAction.DepositCash(context.Background(), &pb.DepositRequest{
		PaymentRequest: &pb.PaymentRequest{
			SnapId:     "",
			EntryId:    "",
			EntryType:  0,
			EntryDesc:  "",
			Uid:        uid,
			Desc:       "外部支付账号:kep***@hotmail.com,外部单号:2019123122001467211405756416",
			Money:      10000,
			PaymentId:  "",
			Excode:     "5e0b0cbd9522776890008156",
			Type:       "alipay",
			Details:    "",
			BusinessAt: nil,
			Prefix:     "RECHARGE",
			PayUid:     0,
			Remark:     "",
			FromApi:    "",
		},
		Cost:            0,
		RewardId:        "",
		DepositType:     1,
		IsAssignDeposit: false,
		PaymentId:       "",
	})
	assert.NoError(t, err)

	_, err = sandbox.PaymentAction.DeductOrderPre(context.Background(), &pb.PaymentRequest{
		SnapId:    "",
		EntryId:   "DEDUCT_product_order_609156_" + strconv.FormatUint(uid, 10),
		EntryType: 1,
		EntryDesc: "v2 deduct cash",
		Uid:       uid,
		Desc:      "action: 订单扣费, uid: " + strconv.FormatUint(uid, 10) + ", order: a037f79efa4e97e13d7d73f7ab7741c5, po: 609156",
		Money:     100,
		PaymentId: "",
		Excode:    "609156",
		Type:      "product_order",
		Prefix:    "DEDUCT",
	})
	assert.NoError(t, err)
}
