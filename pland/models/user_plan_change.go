package models

import (
	"context"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/fatih/structs"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/uuid"

	"qiniu.io/pay/pland/models/enums"
)

// UserPlanChangeDao  用户 plan 变更记录
type UserPlanChangeDao struct {
	base *dao.BaseDao

	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *UserPlanChangeDao) GetCachePrefix() string {
	return "sufy-plan:user-plan-change:"
}

// GetCacheRefs get cache refs
func (d *UserPlanChangeDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewUserPlanChangeDao new user plan change dao
func NewUserPlanChangeDao(base *dao.BaseDao) *UserPlanChangeDao {
	cachePrefix := (*UserPlanChangeDao)(nil).GetCachePrefix()
	return &UserPlanChangeDao{
		base:        base,
		cachePrefix: cachePrefix,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:code={Code}", cachePrefix),
		),
	}
}

// UserPlanChange 用户 plan 变更记录表
type UserPlanChange struct {
	ID uint64 `json:"id" gorm:"primaryKey"`
	// 记录编号
	Code       string           `json:"code" gorm:"code"`
	ChangeType enums.ChangeType `json:"change_type" gorm:"change_type"`
	UID        uint64           `json:"uid" gorm:"uid"`
	// 变动之前当前的 plan
	Version   uint32          `json:"version" gorm:"version"`
	Level     enums.PlanLevel `json:"level" gorm:"level"`
	PacksCode string          `json:"packs_code" gorm:"packs_code"`
	// 期望变更之后的 plan
	TargetVersion   uint32          `json:"target_version" gorm:"target_version"`
	TargetLevel     enums.PlanLevel `json:"target_level" gorm:"target_level"`
	TargetPacksCode string          `json:"target_packs_code" gorm:"target_packs_code"`
	// 实际变更 的结果
	ActualVersion   uint32          `json:"actual_version" gorm:"actual_version"`
	ActualLevel     enums.PlanLevel `json:"actual_level" gorm:"actual_level"`
	ActualPacksCode string          `json:"actual_packs_code" gorm:"actual_packs_code"`
	// 生效月份
	EffectMonth time.Time `json:"effect_month" gorm:"effect_month" sql:"type:DATETIME(6)"`
	// 支付相关
	PaymentSN     *string             `json:"payment_sn" gorm:"payment_sn"`
	PaymentStatus enums.PaymentStatus `json:"payment_status" gorm:"payment_status"`
	CurrencyType  base.CurrencyType   `json:"currency_type" gorm:"currency_type"`
	PaymentAmount *base.NMoney        `json:"payment_amount" gorm:"payment_amount"`
	PaymentTime   time.Time           `json:"payment_time" gorm:"payment_time" sql:"type:DATETIME(6)"`
	RefundTime    time.Time           `json:"refund_time" gorm:"refund_time" sql:"type:DATETIME(6)"`

	CreatedAt time.Time `json:"created_at" gorm:"created_at" sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `json:"updated_at" gorm:"updated_at" sql:"type:DATETIME(6)"`
}

// Insert insert a changes
func (d *UserPlanChangeDao) Insert(
	ctx context.Context,
	c *UserPlanChange,
) error {
	c.Code = uuid.New()
	err := d.base.Model(&UserPlanChange{}).Save(c).Error
	if err != nil {
		return err
	}
	return nil
}

// MarkRefund 标记为退款状态
func (d *UserPlanChangeDao) MarkRefund(
	ctx context.Context,
	code string,
) error {
	if code == "" {
		return nil
	}
	err := d.base.Model(&UserPlanChange{}).
		Where("code = ?", code).
		Updates(UserPlanChange{
			PaymentStatus: enums.PaymentStatusRefund,
			RefundTime:    time.Now().In(tz.MustLocationFromCtx(ctx)),
		}).Error
	if err != nil {
		return err
	}
	return nil
}

// Update 更新 changes
func (d *UserPlanChangeDao) Update(
	ctx context.Context,
	c *UserPlanChange,
) error {
	err := d.base.Model(&UserPlanChange{}).
		Where("code = ?", c.Code).
		Updates(structs.Map(c)).
		Error
	if err != nil {
		return err
	}
	return nil
}

// DescList 获取列表, order by desc
func (d *UserPlanChangeDao) DescList(
	ctx context.Context,
	uid uint64,
	code string,
	changeType enums.ChangeType,
	status enums.PaymentStatus,
	offset int,
	limit int,
) ([]*UserPlanChange, uint64, error) {

	cond := squirrel.And{}
	if uid > 0 {
		cond = append(cond, squirrel.Eq{"uid": uid})
	}
	if code != "" {
		cond = append(cond, squirrel.Eq{"code": code})
	}
	if changeType.Valid() {
		cond = append(cond, squirrel.Eq{"change_type": changeType})
	}
	if status.Valid() {
		cond = append(cond, squirrel.Eq{"payment_status": status})
	}
	where, args, err := cond.ToSql()
	if err != nil {
		return nil, 0, err
	}
	var changes []*UserPlanChange
	err = d.base.Model(&UserPlanChange{}).
		Where(where, args...).
		Offset(offset).
		Limit(limit).
		Order("id DESC").
		Find(&changes).
		Error

	if err != nil {
		return nil, 0, err
	}

	var total uint64
	d.base.Model(&UserPlanChange{}).
		Where(where, args...).
		Count(&total)

	return changes, total, nil
}
