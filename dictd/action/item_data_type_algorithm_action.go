package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/dict"
	"qiniu.io/pay/dictd/action/adapter"
)

// GetItemDataTypeAlgorithmByID 根据ID获取计费项类型算法
func (a *DictAction) GetItemDataTypeAlgorithmByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ItemDataTypeAlgorithm, error) {
	algorithm, err := a.dictBizSrv.GetItemDataTypeAlgorithmByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeAlgorithm(algorithm)
}

// ListItemDataTypeAlgorithmsByItemDataTypeID 根据计费项数据类型ID获取计费项数据类型算法列表
func (a *DictAction) ListItemDataTypeAlgorithmsByItemDataTypeID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ItemDataTypeAlgorithmList, error) {
	count, err := a.dictBizSrv.CountItemDataTypeAlgorithmsByItemDataTypeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	algorithms, err := a.dictBizSrv.ListItemDataTypeAlgorithmsByItemDataTypeID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemDataTypeAlgorithmList{
		Count:                  count,
		ItemDataTypeAlgorithms: make([]*pb.ItemDataTypeAlgorithm, len(algorithms)),
	}
	for i, algorithm := range algorithms {
		al, err := adapter.BuildPbItemDataTypeAlgorithm(&algorithm)
		if err != nil {
			return nil, err
		}
		list.ItemDataTypeAlgorithms[i] = al
	}
	return list, nil
}

// ListItemDataTypeAlgorithmsByItemAndDataType 根据计费项 code 和数据类型 code 获取计费项数据类型算法列表
func (a *DictAction) ListItemDataTypeAlgorithmsByItemAndDataType(
	ctx context.Context,
	param *pb.ItemAndDataTypePagingParam,
) (*pb.ItemDataTypeAlgorithmList, error) {
	count, err := a.dictBizSrv.CountItemDataTypeAlgorithmsByItemAndDataType(ctx,
		param.GetItem(), param.GetDataTypeCode())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	algorithms, err := a.dictBizSrv.ListItemDataTypeAlgorithmsByItemAndDataType(ctx,
		param.GetItem(), param.GetDataTypeCode(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemDataTypeAlgorithmList{
		Count:                  count,
		ItemDataTypeAlgorithms: make([]*pb.ItemDataTypeAlgorithm, len(algorithms)),
	}
	for i, algorithm := range algorithms {
		al, err := adapter.BuildPbItemDataTypeAlgorithm(&algorithm)
		if err != nil {
			return nil, err
		}
		list.ItemDataTypeAlgorithms[i] = al
	}
	return list, nil
}

// CountItemDataTypeAlgorithmsByItemDataTypeID 根据计费项数据类型获取计费项数据类型算法数量
func (a *DictAction) CountItemDataTypeAlgorithmsByItemDataTypeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemDataTypeAlgorithmsByItemDataTypeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, err
}

// CountItemDataTypeAlgorithmsByItemAndDataType 根据计费项 code 和数据类型 code 获取计费项数据类型算法数量
func (a *DictAction) CountItemDataTypeAlgorithmsByItemAndDataType(
	ctx context.Context,
	param *pb.ItemAndDataTypeParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemDataTypeAlgorithmsByItemAndDataType(ctx,
		param.GetItem(), param.GetDataTypeCode())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, err
}

// CreateItemDataTypeAlgorithm 创建计费项数据类型算法
func (a *DictAction) CreateItemDataTypeAlgorithm(
	ctx context.Context,
	algorithm *pb.ItemDataTypeAlgorithm,
) (*pb.ItemDataTypeAlgorithm, error) {
	m, err := adapter.BuildItemDataTypeAlgorithm(algorithm)
	if err != nil {
		return nil, err
	}

	al, err := a.dictBizSrv.CreateItemDataTypeAlgorithm(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeAlgorithm(al)
}

// UpdateItemDataTypeAlgorithmByID 更新计费项数据类型算法
func (a *DictAction) UpdateItemDataTypeAlgorithmByID(
	ctx context.Context,
	param *pb.IDItemDataTypeAlgorithmParam,
) (*pb.ItemDataTypeAlgorithm, error) {
	algorithm := param.GetItemDataTypeAlgorithm()
	m, err := adapter.BuildItemDataTypeAlgorithm(algorithm)
	if err != nil {
		return nil, err
	}

	al, err := a.dictBizSrv.UpdateItemDataTypeAlgorithmByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeAlgorithm(al)
}

// DeleteItemDataTypeAlgorithmByID 删除计费项数据类型算法
func (a *DictAction) DeleteItemDataTypeAlgorithmByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ItemDataTypeAlgorithm, error) {
	algorithm, err := a.dictBizSrv.DeleteItemDataTypeAlgorithmByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeAlgorithm(algorithm)
}
