package service

import (
	"context"
	"fmt"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/measured/model"
)

// CreatePackageWithItems 创建 Package 及其包含的 PackageItems
func (s *MeasureBizService) CreatePackageWithItems(
	ctx context.Context,
	param *PackageWithItems,
) (*PackageWithItemsResp, error) {
	resp := &PackageWithItemsResp{
		PackageItems: make([]model.PackageItem, 0),
	}

	pkg, err := s.CreatePackage(ctx, &model.Package{
		ID:          0,
		Code:        param.Code,
		Type:        param.Type,
		Name:        param.Name,
		Description: param.Description,
		Remark:      param.Remark,
	})
	if err != nil {
		return nil, errors.Annotate(err, "create package failed").WithField("package", pkg)
	}
	resp.Package = *pkg

	for _, packageItem := range param.PackageItems {
		// NOTE: 此处原位修改了传入参数，需要注意
		packageItem.PackageID = pkg.ID
		m, err := s.CreatePackageItem(ctx, &packageItem)
		if err != nil {
			return nil, errors.Annotate(err, "create package_item failed").
				WithField("package_item", m)
		}
		resp.PackageItems = append(resp.PackageItems, *m)
	}
	return resp, nil
}

// GetPackageWithItemsByID 根据 ID 查询 Package 详情
func (s *MeasureBizService) GetPackageWithItemsByID(
	ctx context.Context,
	id uint64,
) (*PackageWithItemsResp, error) {
	resp := &PackageWithItemsResp{
		PackageItems: make([]model.PackageItem, 0),
	}

	pkg, err := s.GetPackageByID(ctx, id)
	if err != nil {
		return nil, errors.Annotate(err, "query package failed").WithField("id", id)
	}
	resp.Package = *pkg

	resp.PackageItems, err = s.ListPackageItemsByPackageID(ctx, pkg.ID, 0, -1)
	if err != nil {
		return nil, errors.Annotate(err, "list package_items failed").
			WithField("package_id", pkg.ID)
	}
	return resp, nil
}

// ListPackageItemsByUIDAndTimePoint 查询用户在指定时间点的生效的所有 PackageItem
func (s *MeasureBizService) ListPackageItemsByUIDAndTimePoint(
	ctx context.Context,
	param *UIDTimePointQuery,
) ([]model.PackageItem, error) {
	return s.dao.PackageItem.ListByUIDTimePoint(param.UID, param.At)
}

// ListPackageItemsByUIDAndTimeRange 查询用户在指定时间段生效的所有 PackageItem
func (s *MeasureBizService) ListPackageItemsByUIDAndTimeRange(
	ctx context.Context,
	param *UIDTimeRangeQuery,
) ([]model.PackageItem, error) {
	return s.dao.PackageItem.ListByUIDTimeRange(
		param.UID, param.StartTime, param.EndTime,
	)
}

// BindPackage 为用户绑定指定 Package
func (s *MeasureBizService) BindPackage(
	ctx context.Context,
	param *BindPackageParam,
) (*BindPackageResp, error) {
	p, err := s.GetPackageByCode(ctx, param.Code)
	if err != nil {
		return nil, errors.Annotate(err, "query package failed").
			WithField("package", param.Code)
	}

	s.lockUser(param.UID)
	defer s.unlockUser(param.UID)

	var m *model.PackageUserMap
	if param.Idempotent {
		// double-check 要的记录是否在要求的时间段已经有了
		existingBinding, err := s.dao.PackageUserMap.GetExistingBindingInTimeRange(
			param.UID,
			p.ID,
			param.EffectTime,
			param.DeadTime,
		)
		if err != nil {
			return nil, errors.Trace(err)
		}
		if existingBinding != nil {
			m = existingBinding
		}
	}

	if m == nil {
		m = &model.PackageUserMap{
			UID:        param.UID,
			PackageID:  p.ID,
			Excode:     param.Excode,
			EffectTime: param.EffectTime,
			DeadTime:   param.DeadTime,
		}

		err = s.dao.PackageUserMap.Save(m, s.cacheExpires)
		if err != nil {
			return nil, errors.Annotate(err, "save package_user_map failed").
				WithField("package_user_map", m)
		}
	}

	packageWithItems, err := s.GetPackageWithItemsByID(ctx, p.ID)
	if err != nil {
		return nil, errors.Annotate(err, "query package failed").WithField("id", p.ID)
	}

	return &BindPackageResp{
		Package:        packageWithItems.Package,
		PackageItems:   packageWithItems.PackageItems,
		PackageUserMap: *m,
	}, nil
}

// UnbindPackage 为用户解绑满足 excode 的免费额度
func (s *MeasureBizService) UnbindPackage(
	ctx context.Context,
	param *UnbindPackageParam,
) error {
	return s.dao.PackageUserMap.DeleteByExcode(param.UID, param.Excode)
}

// UpgradePackage 为用户升级 Package
func (s *MeasureBizService) UpgradePackage(
	ctx context.Context,
	param *UpgradePackageParam,
) (*BindPackageResp, error) {
	srcPackage, err := s.GetPackageByCode(ctx, param.SrcCode)
	if err != nil {
		return nil, errors.Annotate(err, "query src package failed").
			WithField("package", param.SrcCode)
	}

	dstPackage, err := s.GetPackageByCode(ctx, param.DstCode)
	if err != nil {
		return nil, errors.Annotate(err, "query dst package failed").WithField("package", param.DstCode)
	}

	s.lockUser(param.UID)
	defer s.unlockUser(param.UID)

	// 同一个用户可能绑定多个相同 code 的 package
	packageUserMaps, err := s.ListPackageUserMapsByUID(ctx, param.UID, 0, -1)
	if err != nil {
		return nil, errors.Annotate(err, "list package_user_map failed").
			WithField("uid", param.UID)
	}

	effectedPUMs := make([]*model.PackageUserMap, 0)
	for i := range packageUserMaps {
		if packageUserMaps[i].PackageID == srcPackage.ID {
			// XXX: Golang 循环变量只有一个地址
			effectedPUMs = append(effectedPUMs, &packageUserMaps[i])
		}
	}

	// NOTE: 现在的响应结构只能回传 1 个 PackageUserMap，然后考虑到一次 upgrade 多个 packages
	// 的需求在业务上暂时想不到（邀请赠送不会 upgrade 的吧。。。），先禁掉这个情况，于是一次最多影响一个
	// PackageUserMap 了
	if len(effectedPUMs) > 1 {
		return nil, fmt.Errorf("upgrading >1 packages is currently unsupported: packageUserMaps=%+v", effectedPUMs)
	}
	var m *model.PackageUserMap

	// 调整原有 package 生效时间，并绑定新 package
	for _, pum := range effectedPUMs {
		if pum.EffectTime <= param.EffectTime && pum.DeadTime > param.EffectTime {
			pum.DeadTime = param.EffectTime
		}

		err = s.dao.PackageUserMap.Save(pum, s.cacheExpires)
		if err != nil {
			return nil, errors.Annotate(err, "save package_user_map failed").
				WithField("package_user_map", pum)
		}

		// bind new package
		// NOTE: 如果解除上面 NOTE 部分的限制，这个 = 要变成 :=
		m = &model.PackageUserMap{
			UID:        param.UID,
			PackageID:  dstPackage.ID,
			Excode:     param.Excode,
			EffectTime: param.EffectTime,
			DeadTime:   param.DeadTime,
		}

		err = s.dao.PackageUserMap.Save(m, s.cacheExpires)
		if err != nil {
			return nil, errors.Annotate(err, "save package_user_map failed").
				WithField("package_user_map", m)
		}
	}

	packageWithItems, err := s.GetPackageWithItemsByID(ctx, dstPackage.ID)
	if err != nil {
		return nil, errors.Annotate(err, "query package failed").
			WithField("package_id", dstPackage.ID)
	}

	return &BindPackageResp{
		Package:        packageWithItems.Package,
		PackageItems:   packageWithItems.PackageItems,
		PackageUserMap: *m,
	}, nil
}
