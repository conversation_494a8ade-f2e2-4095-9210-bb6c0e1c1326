package service

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/pay-sdk/middleware/logging"
	"github.com/sirupsen/logrus"

	"qiniu.io/pay/dictd/model"
)

func (s *DictBizService) CreateDefaultUserPreferences(
	ctx context.Context,
	preference *model.DefaultUserPreference,
) error {
	logger := logging.GetLogger(ctx)

	item, err := s.dictDao.Item.GetByCode(preference.ItemCode)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"preference": preference,
		}).WithError(err).Error("get item by item code failed")
		return err
	}

	if preference.RegisterStartTime == nil {
		defaultRegisterStartTime := time.Date(2010, 1, 1, 16, 0, 0, 0, time.UTC)
		preference.RegisterStartTime = &defaultRegisterStartTime
	}

	if preference.RegisterEndTime == nil {
		defaultRegisterEndTime := time.Date(2099, 12, 31, 23, 59, 59, 59, time.UTC)
		preference.RegisterEndTime = &defaultRegisterEndTime
	}

	err = preference.Validate()
	if err != nil {
		logger.WithFields(logrus.Fields{
			"preference": preference,
		}).WithError(err).Error("invalid preference fields")
		return err
	}

	preferenceJSON, _ := json.Marshal(preference.ZonePreferences)
	err = s.dictDao.DefaultUserPreference.CreateDefaultUserPreferences(model.DefaultUserPreferenceTable{
		ItemID:            item.ID,
		ZonePreferences:   string(preferenceJSON),
		RegisterStartTime: preference.RegisterStartTime,
		RegisterEndTime:   preference.RegisterEndTime,
	})
	if err != nil {
		logger.WithFields(logrus.Fields{
			"preference": preference,
		}).WithError(err).Error("create default user preference failed")
		return err
	}
	return nil
}

func (s *DictBizService) UpdateDefaultUserPreferences(
	ctx context.Context,
	itemCodes []string,
	preference map[int64]bool,
	registerTime time.Time,
) error {
	logger := logging.GetLogger(ctx)
	if len(itemCodes) == 0 {
		return errors.New("empty item code is invalid")
	}
	if preference == nil {
		return errors.New("empty zone preferences is invalid")
	}
	preferenceJSON, err := json.Marshal(preference)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"itemCodes":        itemCodes,
			"zonePreference":   preference,
			"userRegisterTime": registerTime,
		}).WithError(err).Error("marshal zone preferences failed")
		return err
	}
	err = s.dictDao.DefaultUserPreference.UpdateDefaultUserPreferences(itemCodes, string(preferenceJSON), registerTime)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"itemCodes":        itemCodes,
			"zonePreference":   preference,
			"userRegisterTime": registerTime,
		}).WithError(err).Error("update zone preferences failed")
		return err
	}
	return nil
}

func (s *DictBizService) GetZonePreferenceByItemCode(ctx context.Context, itemCode string, registerTime time.Time) (*model.DefaultUserPreference, error) {
	logger := logging.GetLogger(ctx)
	if itemCode == "" {
		return nil, errors.New("invalid item codes")
	}
	preference, err := s.dictDao.DefaultUserPreference.GetDefaultUserPreferenceByItemCode(registerTime, itemCode)
	if gorm.IsRecordNotFoundError(err) {
		// 目前看 QVM 的计费项区域最多，有 25 个
		zoneItemMaps, err := s.dictDao.ZoneItemMap.ListByItemCode(itemCode, 1, 50)
		if err != nil {
			return nil, err
		}
		if len(zoneItemMaps) == 0 {
			return &model.DefaultUserPreference{}, nil
		}
		// 没有配置区域偏好的计费项，默认所有区域都开启
		zoneIDs := make([]uint64, 0)
		for _, itemMap := range zoneItemMaps {
			zoneIDs = append(zoneIDs, itemMap.ZoneID)
		}
		zones, err := resultgroup.ParallelMap(zoneIDs, func(zoneID uint64) (*model.Zone, error) {
			return s.dictDao.Zone.GetByID(zoneID)
		})
		if err != nil {
			return nil, err
		}
		defaultAllEnabledZonePreferences := make(map[int64]bool)
		for _, zone := range zones {
			defaultAllEnabledZonePreferences[zone.Code] = true
		}
		return &model.DefaultUserPreference{
			ItemCode:        itemCode,
			ZonePreferences: defaultAllEnabledZonePreferences,
		}, nil
	}
	if err != nil {
		logger.WithFields(logrus.Fields{
			"itemCode":         itemCode,
			"userRegisterTime": registerTime,
		}).WithError(err).Error("get default user preferences failed")
		return nil, err
	}
	zonePreferences := make(map[int64]bool)
	err = json.Unmarshal([]byte(preference.ZonePreferences), &zonePreferences)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"itemCodes":        itemCode,
			"userRegisterTime": registerTime,
			"itemCode":         preference.ItemCode,
		}).WithError(err).Error("unmarshal zone preferences failed")
		return nil, err
	}
	return &model.DefaultUserPreference{
		ID:                preference.ID,
		ItemCode:          preference.ItemCode,
		ZonePreferences:   zonePreferences,
		RegisterStartTime: preference.RegisterStartTime,
		RegisterEndTime:   preference.RegisterEndTime,
	}, nil
}

func (s *DictBizService) ListDefaultUserPreferences(
	ctx context.Context,
	registerTime time.Time,
	offset int,
	limit int,
) ([]*model.DefaultUserPreference, error) {
	logger := logging.GetLogger(ctx)
	defaultUserPreferences, err := s.dictDao.DefaultUserPreference.ListDefaultUserPreferences(registerTime, offset, limit)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"offset":           offset,
			"limit":            limit,
			"userRegisterTime": registerTime,
		}).WithError(err).Error("list default user preferences failed")
		return nil, err
	}
	var itemIDs []uint64
	for _, preference := range defaultUserPreferences {
		itemIDs = append(itemIDs, preference.ItemID)
	}

	items, err := s.dictDao.Item.ListByIDs(itemIDs)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"offset":           offset,
			"limit":            limit,
			"userRegisterTime": registerTime,
		}).WithError(err).Error("list items by item ids failed")
		return nil, err
	}

	itemIDCodeMap := make(map[uint64]string)
	for _, item := range items {
		itemIDCodeMap[item.ID] = item.Code
	}

	var result []*model.DefaultUserPreference
	for _, preference := range defaultUserPreferences {
		itemCode, ok := itemIDCodeMap[preference.ItemID]
		if !ok {
			continue
		}
		zp := make(map[int64]bool)
		err := json.Unmarshal([]byte(preference.ZonePreferences), &zp)
		if err != nil {
			logger.WithFields(logrus.Fields{
				"offset":           offset,
				"limit":            limit,
				"userRegisterTime": registerTime,
				"itemCode":         preference.ItemCode,
			}).WithError(err).Error("unmarshal zone preferences of item code failed")
			return nil, err
		}
		result = append(result, &model.DefaultUserPreference{
			ID:                preference.ID,
			ItemCode:          itemCode,
			ZonePreferences:   zp,
			RegisterStartTime: preference.RegisterStartTime,
			RegisterEndTime:   preference.RegisterEndTime,
		})
	}
	return result, nil
}
