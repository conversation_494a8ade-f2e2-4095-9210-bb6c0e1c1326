package dataplane

import (
	"context"
	"errors"
	"io"

	"github.com/xen0n/bufferqueue"

	pb "github.com/qbox/bo-sdk/kirbypb"
	"github.com/qbox/pay-sdk/middleware/logging"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/dataplaneModel"
	"qiniu.io/pay/kirby/model/qrn"
)

var errUnsupportedCompressionMethod = errors.New("unsupported compression method")
var errNoSuchMM = errors.New("no such MM")
var errInvalidStatPointData = errors.New("invalid stat point data")

type BundleProcessCtx struct {
	// streaming stage states

	bq     *bufferqueue.BBQ
	decomp io.ReadCloser
	pbsd   *pbStreamDecoder[*pb.MetricsBundle]

	// control plane stage states

	rtdm  *rtdMatcher
	aggrm *aggregationRuleMatcher

	// data plane interface

	fscb func(*dataplaneModel.FlattenedSeries) error

	// channel to notify consumption completion
	endCh chan struct{}
}

func newBundleProcessCtx(
	cm pb.CompressionMethod,
	rtdm *rtdMatcher,
	aggrm *aggregationRuleMatcher,
	fscb func(*dataplaneModel.FlattenedSeries) error,
) (*BundleProcessCtx, error) {
	bq := bufferqueue.New()

	// NOTE: 这里必须和 SeriesBundle 定义的 repeated MetricsBundle 字段的编号一致
	attentionFieldNum := uint64(1)

	switch cm {
	case pb.CompressionMethod_COMPRESSION_METHOD_NONE:
		pbsd := newPbStreamDecoder[*pb.MetricsBundle](bq, attentionFieldNum)
		return &BundleProcessCtx{
			bq:     bq,
			decomp: nil,
			pbsd:   pbsd,
			rtdm:   rtdm,
			aggrm:  aggrm,
			fscb:   fscb,
			endCh:  make(chan struct{}),
		}, nil

	case pb.CompressionMethod_COMPRESSION_METHOD_ZSTD:
		decomp, err := newZstdStreamFromFirstChunk(bq)
		if err != nil {
			return nil, err
		}

		pbsd := newPbStreamDecoder[*pb.MetricsBundle](decomp, attentionFieldNum)
		return &BundleProcessCtx{
			bq:     bq,
			decomp: decomp,
			pbsd:   pbsd,
			rtdm:   rtdm,
			aggrm:  aggrm,
			fscb:   fscb,
			endCh:  make(chan struct{}),
		}, nil

	default:
		return nil, errUnsupportedCompressionMethod
	}
}

func (c *BundleProcessCtx) Feed(chunk []byte) {
	c.bq.QueueBuffer(chunk)
}

func (c *BundleProcessCtx) MarkEOF() {
	// we may be called before the instance is even ready in extraordinary
	// error cases
	if c == nil {
		return
	}
	c.bq.MarkEOF()
}

func (c *BundleProcessCtx) Finalize() {
	// we may be called before the instance is even ready in extraordinary
	// error cases
	if c == nil {
		return
	}

	<-c.endCh
	if c.decomp != nil {
		_ = c.decomp.Close()
	}
}

func (c *BundleProcessCtx) StartConsuming(ctx context.Context) {
	go func() {
		defer close(c.endCh)

		l := logging.GetLogger(ctx)
		for {
			// TODO: properly propagate ctx
			if ctx.Err() != nil {
				l.WithError(ctx.Err()).Info("BundleProcessCtx request ctx ended")
				// request context ended
				return
			}

			err := c.consume(ctx)
			if err != nil {
				if err == io.EOF {
					return
				}

				l.WithError(err).Error("BundleProcessCtx consume chunk failed")
				// stream state may not be consistent enough for
				// resync to be worthwhile, hence bailing out
				return
			}
		}
	}()
}

func (c *BundleProcessCtx) consume(ctx context.Context) error {
	l := logging.GetLogger(ctx)

	var mb pb.MetricsBundle
	err := c.pbsd.readOneInto(&mb)
	if err != nil {
		if err != io.EOF {
			l.WithError(err).Error("failed to stream-decode MetricsBundle")
		}
		return err
	}

	for _, s := range mb.Series {
		fs := dataplaneModel.FlattenedSeries{
			QRN:         s.Qrn,
			MM:          mb.Mm,
			MergedProps: mergeProps(mb.CommonProps, s.Props),
			MBUUID:      mb.MbUuid,
			Start:       mb.Start.AsTime(),
			End:         mb.End.AsTime(),
			Data:        s.Data,
		}

		err := c.processOneFlattenedSeries(&fs)
		if err != nil {
			// for not flooding the log in case of catastrophic failure
			// l.WithError(err).WithField("fs", fs.toLogObject()).Error("failed to process this dataplaneModel.FlattenedSeries")
			// 一条序列处理错误不见得要整个都失败，但需要能够感知这个信息
			continue
		}
	}

	return nil
}

func (c *BundleProcessCtx) processOneFlattenedSeries(fs *dataplaneModel.FlattenedSeries) error {
	// fill in UID
	q, err := qrn.Parse(fs.QRN)
	if err != nil {
		return err
	}
	fs.UID = q.UID

	// merge in QRN-derived SP
	fs.MergedProps[adminModel.BuiltinSPKeyQRNRegion] = q.Region
	fs.MergedProps[adminModel.BuiltinSPKeyQRNResource] = q.Resource

	// fill in RTD code
	rtdd, err := c.rtdm.queryRTDForQRN(&q)
	if err != nil {
		return err
	}
	fs.RTDCode = rtdd.Code

	// fill in MM properties
	mmd := rtdd.QueryMMD(fs.MM)
	if mmd == nil {
		return errNoSuchMM
	}
	fs.MeasureUnit = mmd.Unit
	fs.G = mmd.G

	// TODO: RTD & MM-directed type checking

	// fill in destination (item_code, zone_code)
	err = c.aggrm.match(fs)
	if err != nil {
		return err
	}

	// check points for invalid data
	for _, p := range fs.Data {
		if p.Value == dataplaneModel.NonexistentValue {
			return errInvalidStatPointData
		}
	}

	// shove into dataplane backend
	err = c.fscb(fs)
	if err != nil {
		return err
	}

	return nil
}
