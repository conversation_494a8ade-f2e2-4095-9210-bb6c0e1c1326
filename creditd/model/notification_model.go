package model

import (
	"time"

	"qiniu.io/pay/creditd/model/enums"
)

type NoticeRecord struct {
	ID         uint64             `gorm:"primary_key"`
	UID        uint64             // 用户账号
	Event      enums.Event        // 要通知什么事件
	NoticeType enums.NoticeType   // 通知类型
	Status     enums.NoticeStatus `gorm:"index:idx_notice_record_status"` // 状态
	TriggerAt  time.Time          `sql:"type:DATETIME(6)"`                // 什么时候触发（/注册）通知（/延迟通知）
	ExpiredAt  time.Time          `sql:"type:DATETIME(6)"`                // 过期时间
	Remark     string             `gorm:"type:varchar(512)"`
	CreatedAt  time.Time          `sql:"type:DATETIME(6)"`
	UpdatedAt  time.Time          `sql:"type:DATETIME(6)"`
}

type NotificationToInfo interface {
	GetEmail() string
	GetPhoneNumber() string
	GetCompanyName() string
}

// Email defines morse mail model
type Email struct {
	UID     uint64   `json:"uid"`
	To      []string `json:"to"`
	Subject string   `json:"subject"`
	Content string   `json:"content"`
}

type Sms struct {
	UID         uint64 `json:"uid"`
	PhoneNumber string `json:"phone_number"`
	Message     string `json:"message"`
}

type Notification struct {
	UserEmailSubject  string
	UserEmailContent  string
	SalesEmailSubject string
	SalesEmailContent string
	UserSms           string
	TemplateName      string
	Event             enums.Event
}

type UserInfo struct {
	Uid          uint32 `json:"uid,omitempty"`
	Email        string `json:"email,omitempty"`
	PhoneNumber  string `json:"phoneNumber,omitempty"`
	CompanyName  string `json:"companyName,omitempty"`
	MobileBinded bool   `json:"mobileBinded"`
}

func (u UserInfo) GetEmail() string {
	return u.Email
}

func (u UserInfo) GetPhoneNumber() string {
	return u.PhoneNumber
}

func (u UserInfo) GetCompanyName() string {
	return u.CompanyName
}

type SalesmanInfo struct {
	UserName string `json:"user_name"`
	NickName string `json:"nick_name"`
	Ldap     string `json:"ldap"`

	Email       string `json:"email"`
	Mobile      string `json:"mobile"`
	PhoneNumber string `json:"phone"`

	IsActive bool `json:"is_active"`
}

func (s SalesmanInfo) GetEmail() string {
	return s.Email
}

func (s SalesmanInfo) GetPhoneNumber() string {
	return s.Mobile
}

func (s SalesmanInfo) GetCompanyName() string {
	return "QiNiu"
}

// ExpiredNotice contains ids & uids info of expired notices
type ExpiredNotice struct {
	ID  uint64
	UID uint64
}
