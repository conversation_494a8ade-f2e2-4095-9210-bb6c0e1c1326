package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/walletd/model"
)

func TestScopeProductMapRoundTrip(t *testing.T) {
	x := &model.ScopeProductMap{
		ID:         233,
		ScopeID:    2333,
		ProductID:  1,
		IsExcluded: false,
		CreatedAt:  time.Now().Round(0),
		UpdatedAt:  time.Now().Round(0),
	}

	y, err := BuildPbScopeProductMap(x)
	assert.NoError(t, err)

	z, err := BuildScopeProductMap(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.ScopeID, z.ScopeID)
	assert.Equal(t, x.ProductID, z.ProductID)
	assert.Equal(t, x.IsExcluded, z.IsExcluded)
	assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
}

func TestScopeProductMapsRoundTrip(t *testing.T) {
	xs := []model.ScopeProductMap{
		{
			ID:         233,
			ScopeID:    2333,
			ProductID:  1,
			IsExcluded: false,
			CreatedAt:  time.Now().Round(0),
			UpdatedAt:  time.Now().Round(0),
		},
		{
			ID:         233,
			ScopeID:    2334,
			ProductID:  2,
			IsExcluded: true,
			CreatedAt:  time.Now().Round(0),
			UpdatedAt:  time.Now().Round(0),
		},
	}

	y, err := BuildPbScopeProductMaps(xs)
	assert.NoError(t, err)

	zs, err := BuildScopeProductMaps(y)
	assert.NoError(t, err)

	for i, x := range xs {
		z := zs[i]
		assert.Equal(t, x.ID, z.ID)
		assert.Equal(t, x.ScopeID, z.ScopeID)
		assert.Equal(t, x.ProductID, z.ProductID)
		assert.Equal(t, x.IsExcluded, z.IsExcluded)
		assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
		assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
	}
}
