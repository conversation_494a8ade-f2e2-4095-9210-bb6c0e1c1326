package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
)

// BuildPbItemDataTypeExtraArg converts from model.ItemDataTypeExtraArg to the protobuf type.
func BuildPbItemDataTypeExtraArg(x *model.ItemDataTypeExtraArg) (*pb.ItemDataTypeExtraArg, error) {
	return &pb.ItemDataTypeExtraArg{
		Id:             x.ID,
		ItemDataTypeId: x.ItemDataTypeID,
		Key:            x.Key,
		Value:          x.Value,
		CreatedAt:      timestamppb.New(x.CreatedAt),
		UpdatedAt:      timestamppb.New(x.UpdatedAt),
	}, nil
}

// BuildModelItemDataTypeExtraArg converts from pb.ItemDataTypeExtraArg to the model layer type.
func BuildModelItemDataTypeExtraArg(x *pb.ItemDataTypeExtraArg) (*model.ItemDataTypeExtraArg, error) {
	var createdAt, updatedAt time.Time
	if x.GetCreatedAt() != nil {
		err := x.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = x.GetCreatedAt().AsTime()
	}

	if x.GetUpdatedAt() != nil {
		err := x.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = x.GetUpdatedAt().AsTime()
	}

	return &model.ItemDataTypeExtraArg{
		ID:             x.GetId(),
		ItemDataTypeID: x.GetItemDataTypeId(),
		Key:            x.GetKey(),
		Value:          x.GetValue(),
		CreatedAt:      createdAt,
		UpdatedAt:      updatedAt,
	}, nil
}
