package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
)

func TestPriceUserMapCRUD(t *testing.T) {
	sandbox := buildSandbox(t)

	priceService := sandbox.priceService

	pums := []model.PriceUserMap{
		{
			UID:     1,
			PriceID: 1,
		},
		{
			UID:     2,
			PriceID: 2,
		},
		{
			UID:     3,
			PriceID: 3,
		},
		{
			UID:     4,
			PriceID: 4,
		},
	}

	assertFields := func(expect, actual *model.PriceUserMap, msgAndArgs ...any) {
		assert.Equal(t, expect.UID, actual.UID, msgAndArgs...)
		assert.Equal(t, expect.PriceID, actual.PriceID, msgAndArgs...)
	}

	ctx := context.TODO()
	for i, pum := range pums {
		// create
		m, err := priceService.CreatePriceUserMap(ctx, &pum)
		if assert.NoError(t, err) {
			assert.NotZero(t, m.ID, "CreatePriceUserMap return zero id")
			assertFields(&pum, m, "Unmatched pum")
		}
		pums[i].ID = m.ID

		// query
		pumID := m.ID
		m, err = priceService.GetPriceUserMapByID(ctx, pumID)
		if assert.NoError(t, err) {
			assertFields(&pum, m, "Unmatched pum")
		}
		// update
		m.PriceID = 10
		updatedPum, err := priceService.UpdatePriceUserMapByID(ctx, pumID, m)
		if assert.NoError(t, err) {
			assertFields(m, updatedPum)
		}
		pums[i].PriceID = m.PriceID
	}

	// list all price item pums
	pumList, err := priceService.ListAllPriceUserMaps(ctx, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, pumList, len(pums))
	}

	// list all price item pum by price_item_id
	pumWithSameUID, err := priceService.ListAllPriceUserMapsByUID(ctx, 1, 0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, pumWithSameUID, 1)
		for _, pum := range pumWithSameUID {
			for _, expectedPum := range pums {
				if pum.ID == expectedPum.ID {
					assertFields(&expectedPum, &pum)
					break
				}
			}
		}
	}

	// delete all price items
	for _, pum := range pums {
		deletedPum, err := priceService.DeletePriceUserMapByID(ctx, pum.ID)
		if assert.NoError(t, err) {
			assertFields(deletedPum, &pum)
		}
	}

	// check price item list, should be empty
	l, err := priceService.ListAllPriceUserMaps(ctx, 0, 10)
	if assert.NoError(t, err) {
		assert.Empty(t, l)
	}

	// de-duplication
	for _, pum := range pums {
		_, err := priceService.DeletePriceUserMapByID(ctx, pum.ID)
		assert.Error(t, err)
	}

}
