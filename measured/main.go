package main

import (
	"context"
	"flag"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/grpc-ecosystem/grpc-gateway/runtime"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/qiniu/version/v2"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"

	"github.com/qbox/bo-base/v4/cli"
	"github.com/qbox/bo-base/v4/dao"
	hook "github.com/qbox/bo-base/v4/errors/logrus"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/lock"
	baselog "github.com/qbox/bo-base/v4/log"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/bo-sdk/kirbypb"
	dictpb "github.com/qbox/pay-sdk/dict"
	kirbyadminpb "github.com/qbox/pay-sdk/kirby"
	pb "github.com/qbox/pay-sdk/measure"
	mppb "github.com/qbox/pay-sdk/measureproxy"

	kirbyAction "qiniu.io/pay/kirby/action"
	kirbyAdminModel "qiniu.io/pay/kirby/model/adminModel"
	kirbyDataplaneModel "qiniu.io/pay/kirby/model/dataplaneModel"
	kirbyService "qiniu.io/pay/kirby/service"
	"qiniu.io/pay/kirby/service/dataplane"
	"qiniu.io/pay/kirby/service/dataplane/common"
	"qiniu.io/pay/kirby/service/dataplane/mockStore"
	"qiniu.io/pay/kirby/service/dataplane/mysqlStore"
	"qiniu.io/pay/measured/action"
	"qiniu.io/pay/measured/config"
	"qiniu.io/pay/measured/i18n"
	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service"
	"qiniu.io/pay/measured/service/stat"
)

func main() {
	var confPath string
	var enableHTTPPprof bool
	var runMigration bool
	flag.StringVar(&confPath, "conf", "measured.yml", "config file path")
	flag.BoolVar(&enableHTTPPprof, "pprof", false, "enable net/http/pprof under /debug/pprof paths")
	flag.BoolVar(&runMigration, "automigrate", false, "enable auto migration of db schema")
	_ = flag.Bool("version", false, "print version info and exit")
	flag.Parse()
	cli.InitFlagMap()

	if cli.IsFlagProvided("version") {
		version.Print()
		return
	}

	conf, err := config.LoadMeasuredConfig(confPath)
	if err != nil {
		log.WithField("err", err).Fatal("failed to load config")
	}

	_, err = intl.Init(&conf.Intl, i18n.L10nFS, i18n.RelativePath)
	if err != nil {
		log.WithError(err).Fatal("failed to init l10n mechanism")
		return
	}
	ctx := tz.MustWithGlobalRefLocation(context.Background())

	// command-line --pprof switch has higher priority over config settings
	if cli.IsFlagProvided("pprof") {
		log.WithFields(log.Fields{
			"configValue": conf.RPC.EnablePprof,
			"cliValue":    enableHTTPPprof,
		}).Info("overriding pprof option with command-line flag")
		conf.RPC.EnablePprof = enableHTTPPprof
	}

	log.AddHook(hook.NewHook(hook.WithKeys("reqid")))
	log.SetFormatter(baselog.NewFlattenJSONFormatter())
	loggerEntry := rpc.NewLoggerEntry(log.StandardLogger())
	rpc.InitLogging(loggerEntry)

	baseDao, err := dao.InitMysqlDao(&conf.MySQL, &conf.Cache)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init dao layer")
	}

	measureDao := model.NewMeasureDao(baseDao)
	kirbyAdminDao := kirbyAdminModel.NewKirbyAdminDao(baseDao)

	var kirbyStoreInstance common.IDataStore
	if conf.Kirby.Persistence.SeriesMySQL.Host != "" {
		// 暂时不使用 bo-base/dao 的缓存机制，后边如果需要 & 合适的话，再开
		baseDPDao, err := dao.InitMysqlDao(&conf.Kirby.Persistence.SeriesMySQL, nil)
		if err != nil {
			log.WithError(err).Fatal("failed to init kirby persistence mysql dao")
		}

		kirbyDataplaneDao := kirbyDataplaneModel.NewKirbyDataplaneDao(baseDPDao, conf.Kirby.Debug)
		aggrp := kirbyAdminModel.NewCachedAggRStore(kirbyAdminDao.AggR)
		kirbyStoreInstance = mysqlStore.NewMySQLDataStore(kirbyDataplaneDao, aggrp, conf.Kirby.Debug)

	} else {
		// 没有指定序列存储数据库的 host，fallback to mock backend
		kirbyStoreInstance = mockStore.NewMockDataStore()
	}

	if runMigration {
		model.RegisterMigrate(baseDao.DB)
		kirbyAdminModel.RegisterMigrate(baseDao.DB)
	}

	dp, err := dataplane.NewPogDataplaneImpl(
		ctx,
		kirbyStoreInstance,
		conf.Kirby.MaxNumSeriesBeforeFlush,
		conf.Kirby.Debug,
	)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init kirby dataplane")
	}
	defer dp.DetachAndWait()

	var statClient stat.Client
	{
		dictConn, err := rpc.GrpcConnectWithName(
			conf.Services.Dict,
			rpc.ServicePayV4Dict,
			conf.RPC.Keepalive.Client,
		)

		if err != nil {
			log.WithError(err).Fatal("failed to establish dictd connection")
		}
		dictClient := dictpb.NewPayDictServiceClient(dictConn)

		mpConn, err := rpc.GrpcConnectWithName(
			conf.Services.Measureproxy,
			rpc.ServicePayV4MeasureProxy,
			conf.RPC.Keepalive.Client,
		)
		if err != nil {
			log.WithError(err).Fatal("failed to establish measureproxyd connection")
		}
		mpClient := mppb.NewPayMeasureProxyServiceClient(mpConn)

		statClient = stat.NewClient(
			measureDao,
			dictClient,
			mpClient,
			dp,
			conf.Measure.EnableKirbyDatapath,
			&conf.Cache,
		)
	}

	// BO-22888 需要能够给用户上 measured 范畴的分布式锁
	redisClient := redis.NewUniversalClient(&conf.Cache.RedisConfig)
	const userLockExpiryInterval = 5 * time.Minute // 一个不长不短的随便取的时间，感觉上比默认 1 小时好一点
	locker := lock.NewRedisLocker(redisClient, lock.WithExpiry(userLockExpiryInterval))

	service := service.NewMeasureBizService(
		measureDao,
		statClient,
		locker,
		conf.Cache.DefaultExpires,
	)
	if err != nil {
		log.WithField("err", err).Fatal("failed to init service layer")
	}

	kirbyService := kirbyService.NewKirbyService(kirbyAdminDao, dp)
	if conf.Kirby.Enabled {
		go func() {
			err := kirbyService.SyncDefinitionsWithDataplane()
			if err != nil {
				log.WithError(err).Error("initial sync of dataplane definitions failed")
			}
		}()
	}

	action := action.NewMeasureAction(service, conf.Measure.DefaultPageSize)

	kirbyAction := kirbyAction.NewKirbyAction(kirbyService, conf.Measure.DefaultPageSize)

	if err := rpc.Serve(
		&conf.RPC,
		loggerEntry,
		func(s *grpc.Server) {
			pb.RegisterPayMeasureServiceServer(s, action)
			kirbypb.RegisterKirbyServiceServer(s, kirbyAction)
			kirbyadminpb.RegisterKirbyAdminServiceServer(s, kirbyAction)
		},
		func(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) error {
			err1 := pb.RegisterPayMeasureServiceHandlerFromEndpoint(ctx, mux, endpoint, opts)
			if err1 != nil {
				return err1
			}
			err1 = kirbyadminpb.RegisterKirbyAdminServiceHandlerFromEndpoint(ctx, mux, endpoint, opts)
			if err1 != nil {
				return err1
			}
			return nil
		},
	); err != nil {
		log.WithField("err", err).Fatal("failed to serve")
	}
}
