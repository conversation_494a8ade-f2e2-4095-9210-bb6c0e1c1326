package action

import (
	"context"
	"errors"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	pb "github.com/qbox/pay-sdk/plan"

	"qiniu.io/pay/pland/action/adapter"
	"qiniu.io/pay/pland/models/enums"
)

// PlanWithPacks plan列表及套餐相关信息
func (p *PlanAction) PlanWithPacks(
	ctx context.Context,
	param *pb.PlanWithPacksParam,
) (*pb.PlanWithPacksResp, error) {
	plans, err := p.srv.ListOnlinePlans(ctx)
	if err != nil {
		return nil, err
	}
	for _, plan := range plans {
		err = p.srv.AttachPriceOnRights(ctx, plan)
		if err != nil {
			return nil, err
		}
	}
	return adapter.BuildPbPlanWithPacksResp(plans)
}

// CurrentPlanBase 用户当前的 plan 基础信息(不包含packs、rights 等信息)
func (p *PlanAction) CurrentPlanBase(
	ctx context.Context,
	param *pb.CurrentPlanParam,
) (*pb.CurrentPlanResp, error) {
	loc := tz.MustLocationFromCtx(ctx)

	// 默认查询当前月份的 plan
	month := base.ThisMonth(time.Now().In(loc))

	// 出账时会指定具体月份
	if param.GetMonth() != nil {
		month = param.GetMonth().AsTime().In(loc)
	}

	userPlan, plan, err := p.srv.GetUserCurrentPlan(
		ctx,
		param.GetUid(),
		month,
	)
	if err != nil {
		return nil, err
	}
	userPlan.PacksCode = "" // 这里重置为空，省去 BuildPbCurrentPlanResp 中不必要的组装 packs、rights 步骤
	return adapter.BuildPbCurrentPlanResp(userPlan, plan)
}

// CurrentPlan 用户当前的 plan
func (p *PlanAction) CurrentPlan(
	ctx context.Context,
	param *pb.CurrentPlanParam,
) (*pb.CurrentPlanResp, error) {

	loc := tz.MustLocationFromCtx(ctx)

	// 默认查询当前月份的 plan
	month := base.ThisMonth(time.Now().In(loc))

	// 出账时会指定具体月份
	if param.GetMonth() != nil {
		month = param.GetMonth().AsTime().In(loc)
	}

	userPlan, plan, err := p.srv.GetUserCurrentPlan(
		ctx,
		param.GetUid(),
		month,
	)
	if err != nil {
		return nil, err
	}
	err = p.srv.AttachPriceOnRights(ctx, plan)
	if err != nil {
		return nil, err
	}
	// 查询用量
	err = p.srv.AttachUsageOnRights(ctx, userPlan, plan)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCurrentPlanResp(userPlan, plan)
}

// SubscribePlan 订阅/续订/升级/降级 plan
func (p *PlanAction) SubscribePlan(
	ctx context.Context,
	param *pb.SubscribePlanParam,
) (*pb.SubscribePlanResp, error) {

	loc := tz.MustLocationFromCtx(ctx)

	in := adapter.BuildSubscribePlanParam(param)
	in.SubscribeSrc = enums.SubscribeSrcCustomer

	usrPlan, _, err := p.srv.GetUserCurrentPlan(
		ctx,
		param.GetUid(),
		base.ThisMonth(time.Now().In(loc)),
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	// 客户自主订阅对生效时间的限制：升级立即生效、降级次月生效

	// ① 新用户当前还不存在 plan, 立即生效
	// ② 升级, 也是立即生效
	if errors.Is(err, gorm.ErrRecordNotFound) ||
		usrPlan.Level.Compare(in.Level) == 1 {

		in.EffectType = enums.EffectTypeImmediately
	} else {
		in.EffectType = enums.EffectTypeNextMonth
	}

	err = p.srv.AdminSubscribePlan(ctx, in, time.Now().In(loc))

	if err != nil {
		return nil, err
	}
	return &pb.SubscribePlanResp{}, nil
}

// UnsubscribePlan 退订 plan
func (p *PlanAction) UnsubscribePlan(
	ctx context.Context,
	param *pb.UnsubscribePlanParam,
) (*pb.UnsubscribePlanResp, error) {

	loc := tz.MustLocationFromCtx(ctx)

	err := p.srv.AbortSubscribe(
		ctx,
		param.GetUid(),
		time.Now().In(loc),
	)
	if err != nil {
		return nil, err
	}

	return &pb.UnsubscribePlanResp{}, nil
}

// SwitchPack 切换套餐
func (p *PlanAction) SwitchPack(
	ctx context.Context,
	param *pb.SwitchPackParam,
) (*pb.SwitchPackResp, error) {
	loc := tz.MustLocationFromCtx(ctx)

	err := p.srv.SwitchPack(
		ctx,
		param.GetUid(),
		param.GetPackCode(),
		time.Now().In(loc),
	)
	if err != nil {
		return nil, err
	}
	return &pb.SwitchPackResp{}, nil
}

// SubscribeInfo 订阅信息
func (p *PlanAction) SubscribeInfo(
	ctx context.Context,
	param *pb.SubscribeInfoParam,
) (*pb.SubscribeInfoResp, error) {
	loc := tz.MustLocationFromCtx(ctx)

	info, plan, err := p.srv.GetNextMonthSubscribeInfo(
		ctx,
		param.GetUid(),
		time.Now().In(loc),
	)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbSubscribeInfo(info, plan)
}

// SubscribeRecords 订阅记录
func (p *PlanAction) SubscribeRecords(
	ctx context.Context,
	param *pb.SubscribeRecordsParam,
) (*pb.SubscribeRecordsResp, error) {

	offset, limit := p.Paging(param)

	records, total, planMap, err := p.srv.ListUserChanges(
		ctx,
		param.GetUid(),
		param.GetRecordCode(),
		enums.ChangeType(param.GetChangeType()),
		enums.PaymentStatus(param.GetPaymentStatus()),
		offset,
		limit,
	)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbUserChanges(records, total, planMap)
}
