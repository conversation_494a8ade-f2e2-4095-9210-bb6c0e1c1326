package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/pay-sdk/middleware/logging"
	pb "github.com/qbox/pay-sdk/wallet"
	"github.com/sirupsen/logrus"
)

func (a *PaymentAction) PayByParent(ctx context.Context, request *pb.PayByParentRequest) (*empty.Empty, error) {
	logger := logging.GetLogger(ctx)
	logger.WithFields(logrus.Fields{
		"uid":    request.Uid,
		"payUid": request.PayUid,
	}).Info("New-Wallet : PayByParent")

	a.genericService.PayByParent(ctx, request.Uid, request.PayUid)

	return &empty.Empty{}, nil
}
