package doraItemsQuerier_test

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/legacystat/common"
	dictpb "github.com/qbox/pay-sdk/dict"
	mppb "github.com/qbox/pay-sdk/measureproxy"
	mockdict "github.com/qbox/pay-sdk/mocks/dict"
	mockmp "github.com/qbox/pay-sdk/mocks/measureproxy"

	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service/stat/doraItemsQuerier"
	measureTest "qiniu.io/pay/measured/test"
)

func TestRefreshCache(t *testing.T) {
	// 为了能检查 ctx 被正确透传了
	type dummyKey struct{}
	ctx := context.WithValue(context.Background(), dummyKey{}, 1)

	loc := time.UTC
	ctx = tz.WithRefLocation(ctx, loc)

	ctrl := gomock.NewController(t)

	sandbox := measureTest.BuildSandbox(t)

	dao := model.NewMeasureDao(sandbox.TestWrap.BaseDao())

	// 准备数据
	{
		makeItemExtraArgs := func(itemID uint64, statKey string, statQuery string) {
			_, _ = sandbox.MeasureService.CreateItemStatConfig(ctx, &model.ItemStatConfig{
				ItemID:      itemID,
				StatSrcType: uint64(common.V3StatSrcDora),
			})
			_, _ = sandbox.MeasureService.CreateItemExtraArg(ctx, &model.ItemExtraArg{
				ItemID: itemID,
				Key:    "stat_key",
				Value:  statKey,
			})
			_, _ = sandbox.MeasureService.CreateItemExtraArg(ctx, &model.ItemExtraArg{
				ItemID: itemID,
				Key:    "stat_query",
				Value:  statQuery,
			})
		}

		makeItemExtraArgs(233, "merge", "cmd=foo:ABC&cmd=foo:DEF")
		makeItemExtraArgs(234, "foo", "cmd=ABC&pipeline=private")
		makeItemExtraArgs(235, "merge", "cmd=bar:GHI&cmd=baz:JKL")
		makeItemExtraArgs(236, "foo", "cmd=MNO")
		makeItemExtraArgs(237, "merge", "cmd=bar:PQR")
		makeItemExtraArgs(238, "merge", "cmd=baz:JKL&cmd=baz:STU")
	}

	// 准备依赖服务
	mockDictCl := mockdict.NewMockPayDictServiceClient(ctrl)
	mockMeasureProxyCl := mockmp.NewMockPayMeasureProxyServiceClient(ctrl)
	{
		// Items
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 233}).
			Times(1).
			Return(&dictpb.Item{Id: 233, GroupId: 2, Code: "foo:bar:a"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 234}).
			Times(1).
			Return(&dictpb.Item{Id: 234, GroupId: 3, Code: "foo:baz:b"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 235}).
			Times(1).
			Return(&dictpb.Item{Id: 235, GroupId: 2, Code: "foo:bar:c"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 236}).
			Times(1).
			Return(&dictpb.Item{Id: 236, GroupId: 4, Code: "baz:quux:d"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 237}).
			Times(1).
			Return(&dictpb.Item{Id: 237, GroupId: 3, Code: "foo:baz:e"}, nil)
		mockDictCl.EXPECT().
			GetItemByID(ctx, &dictpb.IDParam{Id: 238}).
			Times(1).
			Return(&dictpb.Item{Id: 238, GroupId: 4, Code: "baz:quux:f"}, nil)

		// ItemGroups
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 2}).
			Times(1).
			Return(&dictpb.ItemGroup{Id: 2, ProductId: 100, Code: "foo:bar"}, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 3}).
			Times(1).
			Return(&dictpb.ItemGroup{Id: 3, ProductId: 100, Code: "foo:baz"}, nil)
		mockDictCl.EXPECT().
			GetItemGroupByID(ctx, &dictpb.IDParam{Id: 4}).
			Times(1).
			Return(&dictpb.ItemGroup{Id: 4, ProductId: 200, Code: "baz:quux"}, nil)

		// Products
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: 100}).
			Times(1).
			Return(&dictpb.Product{Id: 100, Code: "mps"}, nil)
		mockDictCl.EXPECT().
			GetProductByID(ctx, &dictpb.IDParam{Id: 200}).
			Times(1).
			Return(&dictpb.Product{Id: 200, Code: "atlab"}, nil)

		mockMeasureProxyCl.EXPECT().
			ProxyGetDoraCmdsInUse(ctx, &mppb.GetItemsInUseParams{
				Uid:      1234,
				Begin:    timestamppb.New(time.Date(2021, 4, 1, 0, 0, 0, 0, loc)),
				End:      timestamppb.New(time.Date(2021, 5, 1, 0, 0, 0, 0, loc)),
				Src:      mppb.STAT_SRC_DORA,
				HaveZone: true,
			}).
			Times(2).
			Return(
				&mppb.DoraCmdsInUseResp{
					Cmds: []*mppb.LegacyDoraCmd{
						{Key: "foo", Cmd: "XYZ"}, // 没有对应计费项
						{Key: "bar", Cmd: "PQR"}, // itemID=237 (mps)
						{Key: "foo", Cmd: "ABC"}, // itemID=233 (mps); itemID=234 (mps)
						{Key: "bar", Cmd: "GHI"}, // itemID=235 (mps)
						{Key: "baz", Cmd: "JKL"}, // itemID=235 (mps); itemID=238 (atlab)
					},
				},
				nil,
			)
	}

	querier := doraItemsQuerier.New(dao, mockDictCl, mockMeasureProxyCl)

	// 第一次查询
	{
		resp, err := querier.DoraItemsInUse(ctx, &model.GetItemsInUseParams{
			UID:         1234,
			ProductCode: "mps",
			Begin:       time.Date(2021, 4, 1, 0, 0, 0, 0, loc),
			End:         time.Date(2021, 5, 1, 0, 0, 0, 0, loc),
			ZoneCode:    0,
		})
		assert.NoError(t, err)
		assert.ElementsMatch(t, model.ItemsInUseResp{
			"foo:baz:e", // 237
			"foo:bar:a", // 233 * 这两个可能顺序交换
			"foo:baz:b", // 234 * 这两个可能顺序交换
			"foo:bar:c", // 235
		}, resp)
	}

	// 第二次查询，不应该造成更多次的索引刷新（这一点由 mockDictCl 的方法调用次数验证）
	{
		resp, err := querier.DoraItemsInUse(ctx, &model.GetItemsInUseParams{
			UID:         1234,
			ProductCode: "atlab",
			Begin:       time.Date(2021, 4, 1, 0, 0, 0, 0, loc),
			End:         time.Date(2021, 5, 1, 0, 0, 0, 0, loc),
			ZoneCode:    0,
		})
		assert.NoError(t, err)
		assert.Equal(t, model.ItemsInUseResp{
			"baz:quux:f", // 238
		}, resp)
	}
}
