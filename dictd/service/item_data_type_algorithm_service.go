package service

import (
	"context"

	"qiniu.io/pay/dictd/model"
)

// GetItemDataTypeAlgorithmByID 根据ID获取计费项类型算法
func (s *DictBizService) GetItemDataTypeAlgorithmByID(
	ctx context.Context,
	id uint64,
) (*model.ItemDataTypeAlgorithm, error) {
	return s.dictDao.ItemDataTypeAlgorithm.GetByID(id, s.cacheExpires)
}

// ListItemDataTypeAlgorithmsByItemDataTypeID 根据计费项数据类型ID获取计费项数据类型算法
func (s *DictBizService) ListItemDataTypeAlgorithmsByItemDataTypeID(
	ctx context.Context,
	typeID uint64,
	offset int,
	limit int,
) ([]model.ItemDataTypeAlgorithm, error) {
	return s.dictDao.ItemDataTypeAlgorithm.ListByItemDataTypeID(typeID, offset, limit, s.cacheExpires)
}

// ListItemDataTypeAlgorithmsByItemAndDataType 根据计费项 code 和数据类型 type获取计费项数据类型算法
func (s *DictBizService) ListItemDataTypeAlgorithmsByItemAndDataType(
	ctx context.Context,
	item string,
	dataType string,
	offset int,
	limit int,
) ([]model.ItemDataTypeAlgorithm, error) {
	return s.dictDao.ItemDataTypeAlgorithm.ListByItemAndDataType(item, dataType, offset, limit, s.cacheExpires)
}

// CountItemDataTypeAlgorithmsByItemDataTypeID 根据计费项数据类型ID获取计费项数据类型算法数量
func (s *DictBizService) CountItemDataTypeAlgorithmsByItemDataTypeID(
	ctx context.Context,
	typeID uint64,
) (uint64, error) {
	return s.dictDao.ItemDataTypeAlgorithm.CountByDataTypeID(typeID, s.cacheExpires)
}

// CountItemDataTypeAlgorithmsByItemAndDataType 根据计费项 code 和数据类型 type 获取计费项数据类型算法数量
func (s *DictBizService) CountItemDataTypeAlgorithmsByItemAndDataType(
	ctx context.Context,
	item string,
	dataType string,
) (uint64, error) {
	return s.dictDao.ItemDataTypeAlgorithm.CountByItemAndDataType(item, dataType, s.cacheExpires)
}

// CreateItemDataTypeAlgorithm 创建计费项数据类型算法
func (s *DictBizService) CreateItemDataTypeAlgorithm(
	ctx context.Context,
	unit *model.ItemDataTypeAlgorithm,
) (*model.ItemDataTypeAlgorithm, error) {
	unit.ID = 0
	err := s.dictDao.ItemDataTypeAlgorithm.Save(unit, s.cacheExpires)
	return unit, err
}

// UpdateItemDataTypeAlgorithmByID 根据ID更新计费项数据类型算法
func (s *DictBizService) UpdateItemDataTypeAlgorithmByID(
	ctx context.Context,
	id uint64,
	unit *model.ItemDataTypeAlgorithm,
) (*model.ItemDataTypeAlgorithm, error) {
	unit.ID = id
	err := s.dictDao.ItemDataTypeAlgorithm.Save(unit, s.cacheExpires)
	return unit, err
}

// DeleteItemDataTypeAlgorithmByID 根据ID删除计费项数据类型算法
func (s *DictBizService) DeleteItemDataTypeAlgorithmByID(
	ctx context.Context,
	id uint64,
) (*model.ItemDataTypeAlgorithm, error) {
	unit, err := s.GetItemDataTypeAlgorithmByID(ctx, id)
	if err != nil {
		return nil, err
	}
	err = s.dictDao.ItemDataTypeAlgorithm.Delete(unit)
	return unit, err
}
