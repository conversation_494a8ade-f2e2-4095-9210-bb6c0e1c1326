package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/model"
)

// BuildPbUserBankVirtualAccount 构造 pb.UserBankVirtualAccount
func BuildPbUserBankVirtualAccount(bankVirtAccount *model.UserBankVirtualAccount) *pb.UserBankVirtualAccount {
	return &pb.UserBankVirtualAccount{
		Uid:                bankVirtAccount.UID,
		BankVirtualAccount: bankVirtAccount.BankVirtualAccount,
	}
}

// BuildPbBatchGetUIDByBankVirtualAccountResp builds the BatchGetUIDByBankVirtualAccountResp proto buffer type.
func BuildPbBatchGetUIDByBankVirtualAccountResp(bankVirtAccounts []*model.UserBankVirtualAccount) (*pb.BatchGetUIDByBankVirtualAccountResp, error) {
	pbBankVirtAccounts := make([]*pb.UserBankVirtualAccount, len(bankVirtAccounts))
	for i, bankVirtAccount := range bankVirtAccounts {
		pbBankVirtAccounts[i] = BuildPbUserBankVirtualAccount(bankVirtAccount)
	}

	return &pb.BatchGetUIDByBankVirtualAccountResp{
		UserBankVirtualAccounts: pbBankVirtAccounts,
	}, nil
}
