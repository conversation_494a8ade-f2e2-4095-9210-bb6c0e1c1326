package adapter

import (
	pb "github.com/qbox/pay-sdk/measureproxy"
	"qiniu.io/pay/measureproxyd/model"
)

// BuildPbUsersResp converts from model.UsersResp to the protobuf type.
func BuildPbUsersResp(x model.UsersResp) (*pb.UsersResp, error) {
	uids := make([]uint32, len(x))
	_ = copy(uids, x)

	return &pb.UsersResp{
		Uids: uids,
	}, nil
}

// BuildModelUsersResp converts from pb.UsersResp to the model layer type.
func BuildModelUsersResp(x *pb.UsersResp) (model.UsersResp, error) {
	uids := make([]uint32, len(x.Uids))
	_ = copy(uids, x.Uids)

	return uids, nil
}
