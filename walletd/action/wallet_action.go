package action

import (
	"gopkg.in/go-playground/validator.v9"

	"github.com/qbox/bo-base/v4/action"
	"github.com/qbox/bo-base/v4/intl"
	pb "github.com/qbox/pay-sdk/wallet"

	qpayService "qiniu.io/pay/qpay/wallet/service"
	"qiniu.io/pay/walletd/service"
)

// WalletAction implements the server definition in proto buffer
type WalletAction struct {
	*action.BaseAction
	pb.UnimplementedPayWalletServiceServer

	l10nProvider intl.L10nProvider

	walletBizSrv            *service.WalletBizService
	balanceInsufficiencySrv service.IBalanceInsufficiencyService
	bankVirtAccountSrv      *service.BankVirtualAccountService
	bankTransferSrv         *service.BankTransferService
	userCardSrv             qpayService.IUserCardService
	defaultPageSize         uint64
	validate                *validator.Validate
}

// NewWalletAction is constructor of WalletAction
func NewWalletAction(
	walletBizSrv *service.WalletBizService,
	l10nProvider intl.L10nProvider,
	balanceInsufficiencySrv service.IBalanceInsufficiencyService,
	bankVirtAccountSrv *service.BankVirtualAccountService,
	userCardSrv qpayService.IUserCardService,
	bankTransferSrv *service.BankTransferService,
	defaultPageSize uint64,
) *WalletAction {
	return &WalletAction{
		BaseAction:              action.NewBaseAction(defaultPageSize),
		walletBizSrv:            walletBizSrv,
		l10nProvider:            l10nProvider,
		balanceInsufficiencySrv: balanceInsufficiencySrv,
		bankVirtAccountSrv:      bankVirtAccountSrv,
		userCardSrv:             userCardSrv,
		bankTransferSrv:         bankTransferSrv,
		defaultPageSize:         defaultPageSize,
		validate:                validator.New(),
	}
}

func (a *WalletAction) GetWalletBizSrv() *service.WalletBizService {
	return a.walletBizSrv
}
