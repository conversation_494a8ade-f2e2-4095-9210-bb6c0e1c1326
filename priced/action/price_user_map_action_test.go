package action_test

import (
	"context"
	"testing"

	"github.com/qbox/pay-sdk/price"
	"github.com/stretchr/testify/assert"
)

func TestPriceUserMapAction(t *testing.T) {
	sandbox := buildSandbox(t)

	priceClient := sandbox.priceClient

	pums := []price.PriceUserMap{
		{
			Uid:     1,
			PriceId: 1,
		},
		{
			Uid:     2,
			PriceId: 2,
		},
		{
			Uid:     3,
			PriceId: 3,
		},
		{
			Uid:     4,
			PriceId: 4,
		},
	}

	assertFields := func(expect, actual *price.PriceUserMap, msgAndArgs ...any) {
		assert.Equal(t, expect.Uid, actual.Uid, msgAndArgs...)
		assert.Equal(t, expect.PriceId, actual.PriceId, msgAndArgs...)
	}

	for i, pum := range pums {
		// create
		m, err := priceClient.CreatePriceUserMap(context.Background(), &pum)
		if assert.NoError(t, err) {
			assert.NotZero(t, m.Id, "CreatePriceUserMap return zero id")
			assertFields(&pum, m)
		}
		pums[i].Id = m.Id

		// query
		pumId := m.Id
		m, err = priceClient.GetPriceUserMapByID(context.Background(), &price.IDParam{Id: pumId})
		if assert.NoError(t, err) {
			assertFields(&pum, m)
		}

		// update
		m.PriceId = 10
		updatedPriceUserMap, err := priceClient.UpdatePriceUserMapByID(context.Background(), &price.IDPriceUserMapParam{
			Id:           pumId,
			PriceUserMap: m,
		})
		if assert.NoError(t, err) {
			assertFields(m, updatedPriceUserMap)
			pums[i].PriceId = m.PriceId
		}
	}

	// list all price items
	list, err := priceClient.ListAllPriceUserMaps(context.Background(), &price.PagingParam{
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Len(t, pums, int(list.GetCount()))
		for _, item := range list.GetPriceUserMaps() {
			for _, expectedItem := range pums {
				if item.Id == expectedItem.Id {
					assertFields(&expectedItem, item)
					break
				}
			}
		}
	}

	// list all price items by price_id
	list, err = priceClient.ListAllPriceUserMapsByUID(context.Background(), &price.UIDPagingParam{
		Uid:      1,
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Equal(t, uint64(1), list.GetCount())
		for _, pum := range list.GetPriceUserMaps() {
			for _, expectedPum := range pums {
				if pum.Id == expectedPum.Id {
					assertFields(&expectedPum, pum)
					break
				}
			}
		}
	}

	// delete all price items
	for _, pum := range pums {
		deletedPriceUserMap, err := priceClient.DeletePriceUserMapByID(context.Background(), &price.IDParam{Id: pum.Id})
		if assert.NoError(t, err) {
			assertFields(deletedPriceUserMap, &pum)
		}
	}

	// check price item list, should be empty
	list, err = priceClient.ListAllPriceUserMaps(context.Background(), &price.PagingParam{
		Page:     0,
		PageSize: 10,
	})
	if assert.NoError(t, err) {
		assert.Zero(t, list.GetCount())
		assert.Empty(t, list.GetPriceUserMaps())
	}

	// de-duplication
	for _, pum := range pums {
		_, err := priceClient.DeletePriceUserMapByID(context.Background(), &price.IDParam{Id: pum.Id})
		assert.Error(t, err)
	}
}
