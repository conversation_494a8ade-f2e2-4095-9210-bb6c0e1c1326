package adapter

import (
	"github.com/qbox/bo-base/v4/legacystat/common"
	pb "github.com/qbox/pay-sdk/measureproxy"
	"qiniu.io/pay/measureproxyd/model"
)

// BuildPbDoraCmdsInUseResp converts from model.DoraCmdsInUseResp to the protobuf type.
func BuildPbDoraCmdsInUseResp(x model.DoraCmdsInUseResp) (*pb.DoraCmdsInUseResp, error) {
	pbCmds := make([]*pb.LegacyDoraCmd, len(x))
	for i, cmd := range x {
		pbCmd := BuildPbLegacyDoraCmd(&cmd)
		pbCmds[i] = pbCmd
	}

	return &pb.DoraCmdsInUseResp{
		Cmds: pbCmds,
	}, nil
}

// BuildModelDoraCmdsInUseResp converts from pb.DoraCmdsInUseResp to the model layer type.
func BuildModelDoraCmdsInUseResp(x *pb.DoraCmdsInUseResp) (model.DoraCmdsInUseResp, error) {
	modelCmds := make([]common.LegacyDoraCmd, len(x.Cmds))
	for i, cmd := range x.Cmds {
		modelCmds[i] = *BuildModelLegacyDoraCmd(cmd)
	}

	return modelCmds, nil
}
