package doraItemsQuerier

import (
	"context"

	dictpb "github.com/qbox/pay-sdk/dict"
)

// itemEntry 代表一个计费项和它所属的产品线，其中产品线字段用于按请求的产品线过滤掉不需要的计费项
//
// 由于可能会落缓存，从而被序列化/反序列化的缘故，字段是公有的
type itemEntry struct {
	// ItemCode 计费项 code
	ItemCode string
	// ProductCode 产品线 code
	ProductCode string
}

func queryItemMetadataDict(
	ctx context.Context,
	dictClient dictpb.PayDictServiceClient,
	itemIDs []uint64,
) (map[uint64]itemEntry, error) {
	if len(itemIDs) == 0 {
		return map[uint64]itemEntry{}, nil
	}

	items, err := queryDictItems(ctx, dictClient, itemIDs)
	if err != nil {
		return nil, err
	}

	var distinctGroupIDs []uint64
	{
		idsSet := make(map[uint64]struct{})
		for _, x := range items {
			idsSet[x.GroupId] = struct{}{}
		}
		for id := range idsSet {
			distinctGroupIDs = append(distinctGroupIDs, id)
		}
	}

	groups, err := queryDictItemGroups(ctx, dictClient, distinctGroupIDs)
	if err != nil {
		return nil, err
	}

	var distinctProductIDs []uint64
	{
		idsSet := make(map[uint64]struct{})
		for _, x := range groups {
			idsSet[x.ProductId] = struct{}{}
		}
		for id := range idsSet {
			distinctProductIDs = append(distinctProductIDs, id)
		}
	}

	products, err := queryDictProducts(ctx, dictClient, distinctProductIDs)
	if err != nil {
		return nil, err
	}

	// productID -> productCode
	productCodeMap := make(map[uint64]string, len(products))
	for _, p := range products {
		productCodeMap[p.Id] = p.Code
	}

	// groupID -> productCode
	groupProductCodeMap := make(map[uint64]string, len(groups))
	for _, g := range groups {
		groupProductCodeMap[g.Id] = productCodeMap[g.ProductId]
	}

	// assemble result
	result := make(map[uint64]itemEntry, len(items))
	for _, i := range items {
		result[i.Id] = itemEntry{
			ItemCode:    i.Code,
			ProductCode: groupProductCodeMap[i.GroupId],
		}
	}

	return result, nil
}
