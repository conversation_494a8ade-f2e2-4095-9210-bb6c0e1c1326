package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/dict"

	"qiniu.io/pay/dictd/model"
)

// BuildPbItemDataTypeAlgorithm converts model.ItemDataTypeAlgorithm to the protobuf type.
func BuildPbItemDataTypeAlgorithm(algorithm *model.ItemDataTypeAlgorithm) (*pb.ItemDataTypeAlgorithm, error) {
	return &pb.ItemDataTypeAlgorithm{
		Id:         algorithm.ID,
		DataTypeId: algorithm.DataTypeID,
		Code:       algorithm.Code,
		Name:       algorithm.Name,
		Script:     algorithm.Script,
		IsDefault:  algorithm.IsDefault,
		CreatedAt:  timestamppb.New(algorithm.CreatedAt),
		UpdatedAt:  timestamppb.New(algorithm.UpdatedAt),
	}, nil
}

// BuildItemDataTypeAlgorithm converts from pb.ItemDataTypeAlgorithm to the model layer type.
func BuildItemDataTypeAlgorithm(algorithm *pb.ItemDataTypeAlgorithm) (*model.ItemDataTypeAlgorithm, error) {
	var createdAt, updatedAt time.Time
	if algorithm.GetCreatedAt() != nil {
		err := algorithm.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = algorithm.GetCreatedAt().AsTime()
	}

	if algorithm.GetUpdatedAt() != nil {
		err := algorithm.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = algorithm.GetUpdatedAt().AsTime()
	}

	return &model.ItemDataTypeAlgorithm{
		ID:         algorithm.GetId(),
		DataTypeID: algorithm.GetDataTypeId(),
		Code:       algorithm.GetCode(),
		Name:       algorithm.GetName(),
		Script:     algorithm.GetScript(),
		IsDefault:  algorithm.GetIsDefault(),
		CreatedAt:  createdAt,
		UpdatedAt:  updatedAt,
	}, nil
}
