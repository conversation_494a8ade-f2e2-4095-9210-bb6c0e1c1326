package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"

	"qiniu.io/pay/priced/model"
)

// BuildPbPriceItemStair 转换数据层 model PriceItemStair 为 proto model
func BuildPbPriceItemStair(stair *model.PriceItemStair) (*pb.PriceItemStair, error) {
	i64, err := stair.Price.GetBigMoneyI64()
	if err != nil {
		return nil, err
	}
	return &pb.PriceItemStair{
		Id:          stair.ID,
		PriceItemId: stair.PriceItemID,
		UnitId:      stair.UnitID,
		Quantity:    stair.Quantity,
		Price:       uint64(i64),
		Order:       stair.Order,
		CreatedAt:   timestamppb.New(stair.CreatedAt),
		UpdatedAt:   timestamppb.New(stair.UpdatedAt),
	}, nil
}

// BuildPriceItemStair 转换 proto model 为数据层 model PriceItemStair
func BuildPriceItemStair(stair *pb.PriceItemStair) (*model.PriceItemStair, error) {
	var createdAt, updatedAt time.Time
	if stair.GetCreatedAt() != nil {
		err := stair.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		createdAt = stair.GetCreatedAt().AsTime()
	}

	if stair.GetUpdatedAt() != nil {
		err := stair.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, errors.Trace(err)
		}
		updatedAt = stair.GetUpdatedAt().AsTime()
	}

	return &model.PriceItemStair{
		ID:          stair.GetId(),
		PriceItemID: stair.GetPriceItemId(),
		UnitID:      stair.GetUnitId(),
		Quantity:    stair.GetQuantity(),
		Price:       base.NewNMoneyWithHighAccuracyI64(int64(stair.GetPrice())),
		Order:       stair.GetOrder(),
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
	}, nil
}
