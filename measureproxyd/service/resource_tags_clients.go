package service

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"golang.org/x/sync/errgroup"

	"github.com/qbox/bo-base/v4/errors"
	"github.com/qbox/pay-sdk/base/account"
	pb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measureproxyd/config"
	"qiniu.io/pay/measureproxyd/service/model"
)

// fusion 拉资源标签接口需要带一个 Account 的 header
const fusionAccountHeader = "Account"

type ResourceTagsService struct {
	kodoClient   *kodoClient
	fusionClient *fusionClient
}

type fusionClient struct {
	client        *http.Client
	host          string
	fusionAccount string
}

type kodoClient struct {
	client *http.Client
	host   string
}

func NewResourceTagsService(
	accCfg *account.AccConfig,
	cfg *config.ResourceTags,
) (*ResourceTagsService, error) {
	accTr, err := account.NewTransport(accCfg)
	if err != nil {
		return nil, err
	}
	return &ResourceTagsService{
		kodoClient:   newKodoClient(cfg.KodoResourceTagsHost, accTr),
		fusionClient: newFusionClient(cfg.FusionResourceTagsHost, cfg.FusionResourceTagsAccount, accTr),
	}, err

}

func newFusionClient(host string, fusionAccount string, accTransport http.RoundTripper) *fusionClient {
	client := http.Client{
		Transport: accTransport,
	}
	return &fusionClient{
		client:        &client,
		host:          host,
		fusionAccount: fusionAccount,
	}
}

func (k *fusionClient) queryResourceTags(req *model.FusionQueryReq) (*model.FusionResp, error) {
	pathAndParams := req.ToPathAndQueryParams()
	request, err := http.NewRequest(http.MethodGet, k.host+pathAndParams, nil)
	if err != nil {
		return nil, err
	}
	request.Header.Add(fusionAccountHeader, k.fusionAccount)

	response, err := k.client.Do(request)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("fusionClient Query read body failed. req=%+v, err=%s", req, err)
	}
	if response.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("fusionClient Query failed. status=%d, body=%s", response.StatusCode, string(body))
	}

	var resp model.FusionResp
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

func newKodoClient(host string, accTransport http.RoundTripper) *kodoClient {
	client := http.Client{
		Transport: accTransport,
	}
	return &kodoClient{
		client: &client,
		host:   host,
	}
}

func (k *kodoClient) queryResourceTags(req *model.KodoQueryReq) (*model.KodoResp, error) {
	pathAndparams := req.ToPathAndQueryParams()
	response, err := k.client.Get(k.host + pathAndparams)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("kodoClient Query read body failed. req=%+v, err=%s", req, err)
	}
	if response.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("kodoClient Query failed. status=%d, body=%s", response.StatusCode, string(body))
	}

	var resp model.KodoResp
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

func (s *ResourceTagsService) Query(uid uint32) ([]*model.ResourceTag, error) {
	var kodoResourceTags []*model.ResourceTag
	var fusionResourceTags []*model.ResourceTag
	var eg errgroup.Group

	eg.Go(func() error {
		req := &model.KodoQueryReq{
			UID:   int(uid),
			Limit: 100,
		}
		first := true
		for first || req.Marker != "" {
			first = false
			resp, err := s.kodoClient.queryResourceTags(req)
			if err != nil {
				return err
			}
			for _, obj := range resp.Buckets {
				kodoResourceTags = append(kodoResourceTags, &model.ResourceTag{
					ResourceName: obj.Name,
					ResourceType: pb.ResourceType_BUCKET,
					Tags:         obj.Tags,
				})
			}
			req.Marker = resp.NextMarker
		}
		return nil
	})

	eg.Go(func() error {
		req := &model.FusionQueryReq{
			UID:   int(uid),
			Limit: 100,
		}
		for {
			resourceTags, err := s.fusionClient.queryResourceTags(req)
			if err != nil {
				return err
			}
			if len(resourceTags.List) == 0 {
				break
			}

			for _, obj := range resourceTags.List {
				tags := make([]*model.Tag, 0, len(obj.KVTagList))
				for _, d := range obj.KVTagList {
					tags = append(tags, &model.Tag{
						Key:   d.Key,
						Value: d.Value,
					})
				}
				fusionResourceTags = append(fusionResourceTags, &model.ResourceTag{
					ResourceName: obj.Domain,
					ResourceType: pb.ResourceType_DOMAINS,
					Tags:         tags,
				})
			}
			req.Marker = resourceTags.Marker
		}
		return nil
	})

	err := eg.Wait()
	if err != nil {
		return nil, errors.Trace(err)
	}
	return append(kodoResourceTags, fusionResourceTags...), nil
}
