package adapter

import (
	pb "github.com/qbox/pay-sdk/plan"
	"google.golang.org/protobuf/types/known/timestamppb"
	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/models/enums"
)

// BuildSubscribePlanParam convert pb.SubscribePlanParam to models.Param
func BuildSubscribePlanParam(in *pb.SubscribePlanParam) *models.SubscribePlanParam {
	return &models.SubscribePlanParam{
		Uid:        in.GetUid(),
		Level:      enums.PlanLevel(in.GetLevel()),
		PackCode:   in.GetPackCode(),
		EffectType: enums.EffectType(in.GetEffectType()),
	}
}

// BuildPbSubscribeInfo convert user subscribe to pb.SubscribeInfoResp
func BuildPbSubscribeInfo(
	info *models.UserSubscribe,
	plan *models.Plan,
) (*pb.SubscribeInfoResp, error) {

	return &pb.SubscribeInfoResp{
		Month:        timestamppb.New(info.Month),
		Level:        info.Level.String(),
		Price:        plan.Price.String(),
		CurrencyType: plan.CurrencyType.String(),
	}, nil
}
