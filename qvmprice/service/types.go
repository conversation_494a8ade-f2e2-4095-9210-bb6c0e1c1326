package service

import (
	"time"

	"github.com/qbox/bo-base/v4/base"
)

// QVMPriceItemDetail QVM 计费项报价记录的接口请求、响应形式
type QVMPriceItemDetail struct {
	// UID UID
	UID uint64
	// ItemCode 计费项 code
	ItemCode string
	// ZoneCode 区域 code
	ZoneCode int64
	// StairPriceType 阶梯类型，语义同价格表 v3
	StairPriceType string
	// CurrencyType 币种（如 CNY USD）
	CurrencyType string
	// Stairs 价格阶梯，保证已经按 v3 价格表接口习惯的顺序排序
	Stairs []*QVMItemPriceStairDetail
	// EffectTime 生效时刻（含）
	EffectTime time.Time
	// DeadTime 失效时刻（不含）
	DeadTime time.Time
	// CreatedAt 本条记录是何时产生的
	//
	// 导入报价、改价操作将无视该字段。
	CreatedAt time.Time
	// IsVIP 本条记录是否为 VIP 报价
	//
	// 导入报价、改价操作将无视该字段。
	IsVIP bool
	// VIPOp 本条记录关联的改价操作 ID，只有当 is_vip 为真时才有值
	//
	// 导入报价、改价操作将无视该字段。
	VIPOp string
}

func (x *QVMPriceItemDetail) equalWithoutEffectDeadTimes(y *QVMPriceItemDetail) bool {
	if x == nil || y == nil {
		return x == nil && y == nil
	}
	if x.UID != y.UID ||
		x.ItemCode != y.ItemCode ||
		x.ZoneCode != y.ZoneCode ||
		x.StairPriceType != y.StairPriceType ||
		x.CurrencyType != y.CurrencyType {
		return false
	}
	if len(x.Stairs) != len(y.Stairs) {
		return false
	}
	for i, xStair := range x.Stairs {
		yStair := y.Stairs[i]
		if !xStair.equal(yStair) {
			return false
		}
	}
	return true
}

// QVMItemPriceStairDetail 价格阶梯的接口请求、响应形式
//
// 由于该类型只会在 QVMPriceItemDetail 中以正确顺序的列表形式存在，因此不包含数据库中记录的 order 字段。
type QVMItemPriceStairDetail struct {
	// Quantity 阶梯的量
	Quantity uint64
	// Price 阶梯价格
	Price *base.NMoney
}

func (x *QVMItemPriceStairDetail) equal(y *QVMItemPriceStairDetail) bool {
	if x == nil || y == nil {
		return x == nil && y == nil
	}
	if x.Quantity != y.Quantity {
		return false
	}
	r, err := x.Price.Cmp(y.Price)
	if err != nil || r != 0 {
		return false
	}
	return true
}

// QVMGroupDiscountDetail QVM 按组折扣报价记录的接口请求形式
type QVMGroupDiscountDetail struct {
	// GroupCode 计费项组 code
	GroupCode string
	// ZoneCode 区域 code
	ZoneCode int64
	// IsDisabled 该条报价是否被禁用
	IsDisabled bool
	// Discount 折扣的百分比，100 意为不打折
	Discount uint32
	// EffectTime 生效时刻（含）
	EffectTime time.Time
	// DeadTime 失效时刻（不含）
	DeadTime time.Time
}

type ItemSpec struct {
	ItemCode string
	ZoneCode int64
}

type ItemPricesResp struct {
	ItemCode string
	ZoneCode int64
	Prices   []*QVMPriceItemDetail
}
