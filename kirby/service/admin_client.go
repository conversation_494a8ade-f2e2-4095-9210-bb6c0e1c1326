package service

import (
	"context"
	"time"

	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/bo-base/v4/uuid"

	"qiniu.io/pay/kirby/model/adminModel"
)

func (s *KirbyService) SyncDefinitionsWithDataplane() error {
	epoch := time.Now()
	rtdd, err := s.adminDao.ListAllRTDDetails(epoch, epoch)
	if err != nil {
		return err
	}
	s.DP.RefreshRTDs(rtdd)

	aggrs, err := s.adminDao.AggR.ListAll(epoch, false)
	if err != nil {
		return err
	}
	s.DP.RefreshAggregationRules(aggrs)

	return nil
}

func generateCID(epoch time.Time) string {
	return "QCID" + uuid.NewWithEpoch(epoch)
}

// CreateClient 新建一个推量客户端
func (s *KirbyService) CreateClient(
	ctx context.Context,
	name string,
	qrnMatches []string,
) (*adminModel.Client, error) {
	epoch := time.Now()
	cid := generateCID(epoch)

	obj := adminModel.Client{
		ClientID: cid,
		Name:     name,
		Enabled:  false, // to be manually turned on later
	}
	err := s.adminDao.Client.Save(&obj)
	if err != nil {
		return nil, err
	}

	// TODO: save ACLs

	return &obj, nil
}

// ListAllClients 列出所有推量客户端的信息
func (s *KirbyService) ListAllClients(
	ctx context.Context,
) ([]*adminModel.Client, error) {
	return s.adminDao.Client.ListAll()
}

// GetClientDetail 查询一个推量客户端的明细
func (s *KirbyService) GetClientDetail(
	ctx context.Context,
	cid string,
	epoch time.Time,
) (*adminModel.ClientDetail, error) {
	loc := tz.MustLocationFromCtx(ctx)

	if epoch.UnixNano() == 0 || epoch.IsZero() {
		epoch = time.Now().In(loc)
	} else {
		epoch = epoch.In(loc)
	}

	obj, err := s.adminDao.Client.GetByCID(cid, epoch)
	if err != nil {
		return nil, err
	}

	return &adminModel.ClientDetail{
		ClientID: obj.ClientID,
		Name:     obj.Name,
		Enabled:  obj.Enabled,
		// TODO: ACLs
		CreatedAt: obj.CreatedAt,
		UpdatedAt: obj.UpdatedAt,
	}, nil
}

func (s *KirbyService) CreateRTD(
	ctx context.Context,
	x *adminModel.ResourceTypeDefinitionDetail,
) (*adminModel.ResourceTypeDefinitionDetail, error) {
	// TODO: 暴露这个？
	epoch := time.Now()
	obj, err := s.adminDao.SaveRTDDetail(ctx, x, epoch)
	if err != nil {
		return nil, err
	}

	go s.SyncDefinitionsWithDataplane()

	return obj, nil
}

func (s *KirbyService) GetRTDDetailByCode(
	code string,
	epoch time.Time,
) (*adminModel.ResourceTypeDefinitionDetail, error) {
	// TODO: effectTime
	return s.adminDao.GetRTDDetailByCode(code, epoch, epoch)
}
