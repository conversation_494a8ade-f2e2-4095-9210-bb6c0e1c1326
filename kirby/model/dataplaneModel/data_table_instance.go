package dataplaneModel

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	"github.com/jinzhu/gorm"
	"github.com/samber/lo"
)

type DataTableInstance struct {
	shardName string
	tbl       *gorm.DB
	numPoints int

	extraChecks bool
}

func (t *DataTableInstance) Ensure() error {
	stmt, err := makeCreateDataTableStmt(t.shardName, t.numPoints)
	if err != nil {
		return err
	}

	err = t.tbl.LogMode(false).Exec(stmt).Error
	if err != nil && !isTableAlreadyExisting(err) {
		return err
	}
	return nil
}

// map[numPoints]string
// e.g. cachedSelectQueries[5] = "`t0`, `p0`, `p1`, `p2`, `p3`, `p4`"
//
// negative keys: withSeriesKey=true
// e.g. cachedSelectQueries[-5] = "`series_key`, `t0`, `p0`, `p1`, `p2`, `p3`, `p4`"
var cachedSelectQueries sync.Map

func getSelectQuery(numPoints int, withSeriesKey bool) string {
	var cacheKey int
	if withSeriesKey {
		cacheKey = -numPoints
	} else {
		cacheKey = numPoints
	}

	if v, ok := cachedSelectQueries.Load(cacheKey); ok {
		return v.(string)
	}
	newV := makeSelectQuery(numPoints, withSeriesKey)
	cachedSelectQueries.Store(cacheKey, newV)
	return newV
}

func makeSelectQuery(numPoints int, withSeriesKey bool) string {
	var sb strings.Builder
	if withSeriesKey {
		sb.WriteString("`series_key`, ")
	}
	sb.WriteString("`t0`")
	for i := 0; i < numPoints; i++ {
		sb.WriteString(", `p")
		sb.WriteString(strconv.Itoa(i))
		sb.WriteRune('`')
	}
	return sb.String()
}

func (t *DataTableInstance) getSeries(key string) ([]dataRow, error) {
	rows, err := t.tbl.LogMode(false).Select(getSelectQuery(t.numPoints, false)).Where("`series_key` = ?", key).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if t.extraChecks {
		cols, err := rows.Columns()
		if err != nil {
			return nil, err
		}
		expectedNumCols := t.numPoints + 1 // t0 & numPoints
		if len(cols) != expectedNumCols {
			return nil, fmt.Errorf(
				"SANITY CHECK FAILED: result set has %d columns but expecting %d",
				len(cols),
				expectedNumCols,
			)
		}
	}

	var result []dataRow
	for rows.Next() {
		r := makeRowForScanning(t.numPoints)
		err = rows.Scan(r.scanArgsForT0AndPoints()...)
		if err != nil {
			return nil, err
		}
		result = append(result, r)
	}
	err = rows.Err()
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (t *DataTableInstance) listSeriesByKeys(keys []string) ([]dataRowAndKey, error) {
	rows, err := t.tbl.LogMode(false).Select(getSelectQuery(t.numPoints, true)).Where("`series_key` IN (?)", keys).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	if t.extraChecks {
		cols, err := rows.Columns()
		if err != nil {
			return nil, err
		}
		expectedNumCols := t.numPoints + 2 // series_key & t0 & numPoints
		if len(cols) != expectedNumCols {
			return nil, fmt.Errorf(
				"SANITY CHECK FAILED: result set has %d columns but expecting %d",
				len(cols),
				expectedNumCols,
			)
		}
	}

	var result []dataRowAndKey
	for rows.Next() {
		r := makeRowWithSeriesKeyForScanning(t.numPoints)
		err = rows.Scan(r.scanArgsForSKT0AndPoints()...)
		if err != nil {
			return nil, err
		}
		result = append(result, r)
	}
	err = rows.Err()
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (t *DataTableInstance) setSeries(
	seriesKey string,
	rows []dataRow,
) error {
	rowsToInsert := lo.Map(rows, func(x dataRow, _ int) dataRowAndKey {
		return dataRowAndKey{
			SeriesKey: seriesKey,
			T0:        x.T0,
			P:         x.P,
		}
	})
	return t.bulkInsert(rowsToInsert)
}

func (t *DataTableInstance) bulkInsert(
	rows []dataRowAndKey,
) error {
	if len(rows) == 0 {
		return nil
	}

	if t.extraChecks {
		for i, r := range rows {
			if len(r.P) != t.numPoints {
				return fmt.Errorf(
					"SANITY CHECK FAILED: row %d contains %d points, expected %d",
					i,
					len(r.P),
					t.numPoints,
				)
			}
		}
	}

	sqlTemplate, err := makeBulkInsertQueryTemplate(t.shardName, t.numPoints, len(rows))
	if err != nil {
		return err
	}

	values := make([]any, 0, len(rows)*(t.numPoints+2))
	for _, r := range rows {
		values = append(values, r.valuesForInsert()...)
	}

	return t.tbl.LogMode(false).Exec(sqlTemplate, values...).Error
}
