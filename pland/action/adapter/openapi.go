package adapter

import (
	"errors"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/plan"
	"qiniu.io/pay/pland/models"
	"qiniu.io/pay/pland/models/enums"
	"qiniu.io/pay/pland/service"
)

// BuildPbPlanWithPacksResp convert models.Plan to PlanWithPacks
func BuildPbPlanWithPacksResp(list []*models.Plan) (*pb.PlanWithPacksResp, error) {

	plans := make([]*pb.PlanWithPacks, 0)
	for _, plan := range list {
		planWithPacks := &pb.PlanWithPacks{
			Version:      plan.Version,
			Description:  plan.Description,
			Level:        plan.Level.String(),
			Price:        plan.Price.String(),
			CurrencyType: plan.CurrencyType.String(),
			Packs:        nil,
		}

		packs := make([]*pb.Packs, 0)
		if plan.Packs == nil {
			planWithPacks.Packs = packs
			plans = append(plans, planWithPacks)
			continue
		}

		for _, p := range plan.Packs.Packs {
			rights, err := buildPbRightsList(p.Rights)
			if err != nil {
				return nil, err
			}
			packs = append(packs, &pb.Packs{
				Code:        p.Code,
				Name:        p.Name,
				Description: p.Description,
				Rights:      rights,
			})
		}
		planWithPacks.Packs = packs
		plans = append(plans, planWithPacks)
	}

	return &pb.PlanWithPacksResp{
		Plans: plans,
	}, nil
}
func buildPbRightsList(
	rights []models.PackRights,
) ([]*pb.Rights, error) {
	res := make([]*pb.Rights, 0)
	for _, r := range rights {
		rs, err := buildPbRights(r)
		if err != nil {
			return nil, err
		}
		res = append(res, rs)
	}
	return res, nil
}

func buildPbRights(
	rights models.PackRights,
) (*pb.Rights, error) {
	switch rights.Type {
	case enums.RightsTypeFreeRespack:
		return &pb.Rights{
			Code:     rights.Code,
			Name:     rights.Name,
			Type:     rights.Type.String(),
			ItemCode: rights.ItemCode,
			IsCombo:  rights.IsCombo,
			Zone:     rights.Zone,
			Respack: &pb.RightsRespack{
				Name:          rights.Name,
				Quota:         rights.Quota,
				UnitStr:       rights.UnitStr,
				Used:          rights.Used,
				IsCombo:       rights.IsCombo,
				Zone:          rights.Zone,
				ZoneName:      rights.ZoneName,
				DeductDetails: rights.DeductDetails,
			},
		}, nil
	default:
		return &pb.Rights{
			Code:     rights.Code,
			Name:     rights.Name,
			Type:     rights.Type.String(),
			ItemCode: rights.ItemCode,
			IsCombo:  rights.IsCombo,
			Zone:     rights.Zone,
			Price:    buildPbRightsPrice(rights),
		}, nil
	}
}

func buildPbRightsPrice(rights models.PackRights) *pb.RightsPrice {
	if rights.Price == nil {
		return nil
	}
	pbRanges := make([]*pb.Range, len(rights.Price.Ranges))
	for i, r := range rights.Price.Ranges {
		pbRanges[i] = &pb.Range{
			From:    r.From,
			To:      r.To,
			FromStr: r.FromStr,
			ToStr:   r.ToStr,
			Price:   r.Price,
		}
	}
	return &pb.RightsPrice{
		Name:     rights.Name,
		DataType: "", // 为空==v4 的 default 默认计费类型
		UnitStr:  rights.UnitStr,
		Discount: rights.Discount,
		Zone:     rights.Zone,
		ZoneName: rights.ZoneName,
		Price: &pb.Price{
			Type:   rights.Price.Type,
			Ranges: pbRanges,
		},
	}
}

// BuildPbCurrentPlanResp build pb current plan response
func BuildPbCurrentPlanResp(
	userPlan *models.UserPlan,
	plan *models.Plan,
) (*pb.CurrentPlanResp, error) {

	cp := &pb.CurrentPlanResp{
		Version:              userPlan.Version,
		Level:                plan.Level.String(),
		RemainingSwitchCount: userPlan.RemainingSwitchCount,
		Month:                timestamppb.New(userPlan.Month),
	}
	pack, ok := plan.SearchPacks(userPlan.PacksCode)
	if !ok {
		return cp, nil
	}
	rights, err := buildPbRightsList(pack.Rights)
	if err != nil {
		return nil, err
	}

	cp.Packs = &pb.Packs{
		Code:        pack.Code,
		Name:        pack.Name,
		Description: pack.Description,
		Rights:      rights,
	}
	return cp, nil
}

// BuildPbUserChanges build pb subscribe records
func BuildPbUserChanges(
	records []*models.UserPlanChange,
	total uint64,
	planMap map[string]*models.Plan,
) (*pb.SubscribeRecordsResp, error) {
	res := &pb.SubscribeRecordsResp{
		Records: make([]*pb.SubscribeRecord, 0),
		Total:   total,
	}
	for _, record := range records {

		sr, err := buildPbSubscribeRecord(record, planMap)
		if err != nil {
			return nil, err
		}
		res.Records = append(res.Records, sr)
	}
	return res, nil
}

func buildPbSubscribeRecord(
	record *models.UserPlanChange,
	planMap map[string]*models.Plan,
) (*pb.SubscribeRecord, error) {

	sb := &pb.SubscribeRecord{
		Code:            record.Code,
		ChangeType:      pb.ChangeType(record.ChangeType),
		Level:           record.ActualLevel.String(),
		Name:            "",
		Version:         record.ActualVersion,
		PackCode:        record.ActualPacksCode,
		StartTime:       timestamppb.New(record.EffectMonth),
		EndTime:         timestamppb.New(base.NextMonth(record.EffectMonth)),
		PaymentTime:     timestamppb.New(record.PaymentTime),
		CreatedAt:       timestamppb.New(record.CreatedAt),
		PaymentStatus:   pb.PaymentStatus(record.PaymentStatus),
		PaymentAmount:   uint64(record.PaymentAmount.MustGetBigMoneyI64()),
		PaymentCurrency: record.CurrencyType.String(),
		RefundTime:      timestamppb.New(record.RefundTime),
	}
	if record.ActualLevel == enums.LevelEnterprise {
		// 企业版没有套餐，因此 name 字段为空
		return sb, nil
	}

	plan, ok := planMap[service.GenPlanIndexString(record.ActualVersion, record.ActualLevel)]
	if !ok {
		return nil, errors.New("plan not found")
	}

	pack, ok := plan.SearchPacks(record.ActualPacksCode)
	if !ok {
		return nil, errors.New("pack not found")
	}
	sb.Name = pack.Name

	return sb, nil
}
