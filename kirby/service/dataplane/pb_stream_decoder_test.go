package dataplane

import (
	"bytes"
	_ "embed"
	"io"
	"testing"

	"github.com/stretchr/testify/assert"

	pb "github.com/qbox/bo-sdk/kirbypb"
)

//go:embed testdata/kodo-series-cs-20230504.pb.zst
var kodoSeriesCs20230504 []byte

func TestPbStreamDecoderWithZstdInput(t *testing.T) {
	buf := bytes.NewBuffer(kodoSeriesCs20230504)
	z, err := newZstdStreamFromFirstChunk(buf)
	assert.NoError(t, err)

	// NOTE: 这里必须和 SeriesBundle 定义的 repeated MetricsBundle 字段的编号一致
	// see newBundleProcessCtx
	const attentionFieldNum = uint64(1)

	var dest pb.MetricsBundle
	pbsd := newPbStreamDecoder[*pb.MetricsBundle](z, attentionFieldNum)
	numBundles := 0
	totalSeries := 0
	for {
		err = pbsd.readOneInto(&dest)
		if err == io.EOF {
			break
		}
		assert.NoError(t, err)
		numBundles++
		totalSeries += len(dest.Series)
	}
	assert.EqualValues(t, 4, numBundles)
	assert.EqualValues(t, 21890, totalSeries)
}
