package test

import (
	"testing"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/lock"
	"github.com/qbox/bo-base/v4/test"

	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service"
)

type Sandbox struct {
	TestWrap       *test.Wrap
	MeasureService *service.MeasureBizService
}

func BuildSandbox(t *testing.T) *Sandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in measure/service return error")
	}

	userLocker := lock.NewRedisLocker(redis.NewUniversalClient(&redis.UniversalOptions{
		Addrs: []string{testWrap.Miniredis().Addr()},
	}))

	measureDao := model.NewMeasureDao(testWrap.BaseDao())
	measureService := service.NewMeasureBizService(measureDao, nil, userLocker, dao.CacheExpiresNoCache)

	return &Sandbox{
		TestWrap:       testWrap,
		MeasureService: measureService,
	}
}
