package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	pb "github.com/qbox/pay-sdk/measure"

	"qiniu.io/pay/measured/model"
	"qiniu.io/pay/measured/service"
)

// BuildPbPackageItemParam convert model.PackageItem to proto.PackageItemParam
func BuildPbPackageItemParam(x *model.PackageItem) (*pb.PackageItemParam, error) {
	return &pb.PackageItemParam{
		ItemId:      x.ItemID,
		ZoneId:      x.ZoneID,
		UnitId:      x.UnitID,
		Name:        x.Name,
		Description: x.Description,
		Remark:      x.Remark,
		CarryType:   BuildPbCarryType(x.CarryType),
		Period:      BuildPbPeriod(x.Period),
		Quantity:    x.Quantity,
		Price:       x.Price,
		EffectTime:  timestamppb.New(x.EffectTime.TimeIn(time.UTC)),
		DeadTime:    timestamppb.New(x.DeadTime.TimeIn(time.UTC)),
	}, nil
}

// BuildPackageItemParam convert proto.PackageItemParam to mdoel.PackageItem
func BuildPackageItemParam(x *pb.PackageItemParam) (*model.PackageItem, error) {
	var err error
	var effectTime, deadTime time.Time
	if x.GetEffectTime() != nil {
		err := x.GetEffectTime().CheckValid()
		if err != nil {
			return nil, err
		}
		effectTime = x.GetEffectTime().AsTime()
	}

	if x.GetDeadTime() != nil {
		err := x.GetDeadTime().CheckValid()
		if err != nil {
			return nil, err
		}
		deadTime = x.GetDeadTime().AsTime()
	}

	carryType, err := BuildModelCarryType(x.CarryType)
	if err != nil {
		return nil, err
	}

	period, err := BuildModelPeriod(x.Period)
	if err != nil {
		return nil, err
	}

	return &model.PackageItem{
		ItemID:      x.ItemId,
		ZoneID:      x.ZoneId,
		UnitID:      x.UnitId,
		Name:        x.Name,
		Description: x.Description,
		Remark:      x.Remark,
		CarryType:   carryType,
		Period:      period,
		Quantity:    x.Quantity,
		Price:       x.Price,
		EffectTime:  base.NewHNS(effectTime),
		DeadTime:    base.NewHNS(deadTime),
	}, nil
}

// BuildPbPackageWithItems convert service.PackageWithItems to proto.PackageWithItems
func BuildPbPackageWithItems(x *service.PackageWithItems) (*pb.PackageWithItems, error) {
	packageItems := make([]*pb.PackageItemParam, len(x.PackageItems))
	for i, packageItem := range x.PackageItems {
		m, err := BuildPbPackageItemParam(&packageItem)
		if err != nil {
			return nil, err
		}

		packageItems[i] = m
	}

	return &pb.PackageWithItems{
		Code:         x.Code,
		Type:         x.Type,
		Name:         x.Name,
		Description:  x.Description,
		Remark:       x.Remark,
		PackageItems: packageItems,
	}, nil
}

// BuildPackageWithItems convert proto.PackageWithItems to service.PackageWithItems
func BuildPackageWithItems(x *pb.PackageWithItems) (*service.PackageWithItems, error) {
	packageItems := make([]model.PackageItem, len(x.PackageItems))
	for i, packageItem := range x.PackageItems {
		m, err := BuildPackageItemParam(packageItem)
		if err != nil {
			return nil, err
		}

		packageItems[i] = *m
	}

	return &service.PackageWithItems{
		Code:         x.Code,
		Type:         x.Type,
		Name:         x.Name,
		Description:  x.Description,
		Remark:       x.Remark,
		PackageItems: packageItems,
	}, nil
}

// BuildPbPackageWithItemsResp convert service.PackageWithItemResp to proto.PackageWithItemResp
func BuildPbPackageWithItemsResp(x *service.PackageWithItemsResp) (*pb.PackageWithItemsResp, error) {
	p, err := BuildPbPackage(&x.Package)
	if err != nil {
		return nil, err
	}

	packageItems := make([]*pb.PackageItem, len(x.PackageItems))
	for i, packageItem := range x.PackageItems {
		m, err := BuildPbPackageItem(&packageItem)
		if err != nil {
			return nil, err
		}

		packageItems[i] = m
	}

	return &pb.PackageWithItemsResp{
		Package:      p,
		PackageItems: packageItems,
	}, nil
}

// BuildPackageWithItemsResp convert proto.PackageWithItemsResp to service.PackageWithItemsResp
func BuildPackageWithItemsResp(x *pb.PackageWithItemsResp) (*service.PackageWithItemsResp, error) {
	p, err := BuildModelPackage(x.Package)
	if err != nil {
		return nil, err
	}

	packageItems := make([]model.PackageItem, len(x.PackageItems))
	for i, packageItem := range x.PackageItems {
		m, err := BuildModelPackageItem(packageItem)
		if err != nil {
			return nil, err
		}

		packageItems[i] = *m
	}

	return &service.PackageWithItemsResp{
		Package:      *p,
		PackageItems: packageItems,
	}, nil
}

// BuildPbUIDTimePointQuery convert service.UIDTimePointQuery to proto.UIDTimePointQuery
func BuildPbUIDTimePointQuery(x *service.UIDTimePointQuery) (*pb.UIDTimePointQuery, error) {
	return &pb.UIDTimePointQuery{
		Uid: x.UID,
		At:  timestamppb.New(x.At.TimeIn(time.UTC)),
	}, nil
}

// BuildUIDTimePointQuery convert proto.UIDTimePointQuery to service.UIDTimePointQuery
func BuildUIDTimePointQuery(x *pb.UIDTimePointQuery) (*service.UIDTimePointQuery, error) {
	var at time.Time
	if x.GetAt() != nil {
		err := x.GetAt().CheckValid()
		if err != nil {
			return nil, err
		}
		at = x.GetAt().AsTime()
	}

	return &service.UIDTimePointQuery{
		UID: x.Uid,
		At:  base.NewHNS(at),
	}, nil
}

// BuildPbUIDTimeRangeQuery convert service.UIDTimeRangeQuery to proto.UIDTimeRangeQuery
func BuildPbUIDTimeRangeQuery(x *service.UIDTimeRangeQuery) (*pb.UIDTimeRangeQuery, error) {
	return &pb.UIDTimeRangeQuery{
		Uid:       x.UID,
		StartTime: timestamppb.New(x.StartTime.TimeIn(time.UTC)),
		EndTime:   timestamppb.New(x.EndTime.TimeIn(time.UTC)),
	}, nil
}

// BuildUIDTimeRangeQuery convert proto.UIDTimeRangeQuery to service.UIDTimeRangeQuery
func BuildUIDTimeRangeQuery(x *pb.UIDTimeRangeQuery) (*service.UIDTimeRangeQuery, error) {
	var startTime, endTime time.Time
	if x.GetStartTime() != nil {
		err := x.GetStartTime().CheckValid()
		if err != nil {
			return nil, err
		}
		startTime = x.GetStartTime().AsTime()
	}
	if x.GetEndTime() != nil {
		err := x.GetEndTime().CheckValid()
		if err != nil {
			return nil, err
		}
		endTime = x.GetEndTime().AsTime()
	}

	return &service.UIDTimeRangeQuery{
		UID:       x.Uid,
		StartTime: base.NewHNS(startTime),
		EndTime:   base.NewHNS(endTime),
	}, nil
}

// BuildBindPackageParam convert proto.BindPackageParam to service.BindPackageParam
func BuildBindPackageParam(x *pb.BindPackageParam) (*service.BindPackageParam, error) {
	var effectTime, deadTime time.Time
	if x.GetEffectTime() != nil {
		err := x.GetEffectTime().CheckValid()
		if err != nil {
			return nil, err
		}
		effectTime = x.GetEffectTime().AsTime()
	}

	if x.GetDeadTime() != nil {
		err := x.GetDeadTime().CheckValid()
		if err != nil {
			return nil, err
		}
		deadTime = x.GetDeadTime().AsTime()
	}

	return &service.BindPackageParam{
		UID:        x.Uid,
		Code:       x.Code,
		Excode:     x.Excode,
		EffectTime: base.NewHNS(effectTime),
		DeadTime:   base.NewHNS(deadTime),
		Idempotent: x.Idempotent,
	}, nil
}

// BuildPbBindPackageResp converts service.BindPackageResp to the protobuf type.
func BuildPbBindPackageResp(x *service.BindPackageResp) (*pb.BindPackageResp, error) {
	pkg, err := BuildPbPackage(&x.Package)
	if err != nil {
		return nil, err
	}

	pkgItems := make([]*pb.PackageItem, len(x.PackageItems))
	for i, pi := range x.PackageItems {
		tmp, err := BuildPbPackageItem(&pi)
		if err != nil {
			return nil, err
		}
		pkgItems[i] = tmp
	}

	um, err := BuildPbPackageUserMap(&x.PackageUserMap)
	if err != nil {
		return nil, err
	}

	return &pb.BindPackageResp{
		Package:        pkg,
		PackageItems:   pkgItems,
		PackageUserMap: um,
	}, nil
}

// BuildUpgradePackageParam convert proto.UpgradePackageParam to service.UpgradePackageParam
func BuildUpgradePackageParam(x *pb.UpgradePackageParam) (*service.UpgradePackageParam, error) {
	var effectTime, deadTime time.Time
	if x.GetEffectTime() != nil {
		err := x.GetEffectTime().CheckValid()
		if err != nil {
			return nil, err
		}
		effectTime = x.GetEffectTime().AsTime()
	}

	if x.GetDeadTime() != nil {
		err := x.GetDeadTime().CheckValid()
		if err != nil {
			return nil, err
		}
		deadTime = x.GetDeadTime().AsTime()
	}

	return &service.UpgradePackageParam{
		UID:        x.Uid,
		SrcCode:    x.SrcCode,
		DstCode:    x.DstCode,
		Excode:     x.Excode,
		EffectTime: base.NewHNS(effectTime),
		DeadTime:   base.NewHNS(deadTime),
	}, nil
}

// BuildUnbindPackageParam convert proto.UnbindPackageParam to service.UnbindPackageParam
func BuildUnbindPackageParam(x *pb.UnbindPackageParam) (*service.UnbindPackageParam, error) {
	return &service.UnbindPackageParam{
		UID:    x.Uid,
		Excode: x.Excode,
	}, nil
}
