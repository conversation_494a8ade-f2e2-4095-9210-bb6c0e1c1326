package dataplaneModel

import (
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/test"
	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
)

func TestCheckTimeAlignment(t *testing.T) {
	test.RunWithUTCAndCST(t, testCheckTimeAlignment)
}

func testCheckTimeAlignment(t *testing.T, loc *time.Location) {
	t20220101_0000 := time.Date(2022, time.January, 1, 0, 0, 0, 0, loc)
	t20220101_0004 := time.Date(2022, time.January, 1, 0, 4, 0, 0, loc)
	t20220101_0005 := time.Date(2022, time.January, 1, 0, 5, 0, 0, loc)
	t20220101_0030 := time.Date(2022, time.January, 1, 0, 30, 0, 0, loc)
	t20220101_0100 := time.Date(2022, time.January, 1, 1, 0, 0, 0, loc)
	t20220102_0000 := time.Date(2022, time.January, 2, 0, 0, 0, 0, loc)
	t20220201_0000 := time.Date(2022, time.February, 1, 0, 0, 0, 0, loc)

	testCases := []struct {
		desc   string
		g      adminModel.Granularity
		points []*pb.StatPoint
		ok     bool
	}{
		{
			desc:   "没有点",
			g:      adminModel.GranularityMonth,
			points: nil,
			ok:     true,
		},
		{
			desc: "g=5min 正向",
			g:    adminModel.Granularity5Min,
			points: []*pb.StatPoint{
				{Time: timestamppb.New(t20220101_0000), Value: 10},
				{Time: timestamppb.New(t20220101_0030), Value: 20},
				{Time: timestamppb.New(t20220101_0005), Value: 30},
			},
			ok: true,
		},
		{
			desc: "g=day 正向",
			g:    adminModel.GranularityDay,
			points: []*pb.StatPoint{
				{Time: timestamppb.New(t20220101_0000), Value: 10},
				{Time: timestamppb.New(t20220102_0000), Value: 20},
			},
			ok: true,
		},
		{
			desc: "g=month 正向",
			g:    adminModel.GranularityMonth,
			points: []*pb.StatPoint{
				{Time: timestamppb.New(t20220101_0000), Value: 10},
			},
			ok: true,
		},
		{
			desc: "g=month 正向：目前没有检查每个点是否越界",
			g:    adminModel.GranularityMonth,
			points: []*pb.StatPoint{
				{Time: timestamppb.New(t20220101_0000), Value: 10},
				{Time: timestamppb.New(t20220201_0000), Value: 20},
			},
			ok: true,
		},
		{
			desc: "g=5min 反向：不对齐",
			g:    adminModel.Granularity5Min,
			points: []*pb.StatPoint{
				{Time: timestamppb.New(t20220101_0000), Value: 10},
				{Time: timestamppb.New(t20220101_0004), Value: 20},
				{Time: timestamppb.New(t20220101_0030), Value: 30},
			},
			ok: false,
		},
		{
			desc: "g=day 反向：不对齐",
			g:    adminModel.GranularityDay,
			points: []*pb.StatPoint{
				{Time: timestamppb.New(t20220101_0000), Value: 10},
				{Time: timestamppb.New(t20220101_0030), Value: 20},
				{Time: timestamppb.New(t20220101_0100), Value: 30},
			},
			ok: false,
		},
		{
			desc: "g=day 反向：不对齐",
			g:    adminModel.GranularityMonth,
			points: []*pb.StatPoint{
				{Time: timestamppb.New(t20220101_0000), Value: 10},
				{Time: timestamppb.New(t20220101_0030), Value: 20},
				{Time: timestamppb.New(t20220102_0000), Value: 30},
			},
			ok: false,
		},
		{
			desc: "反向：不认识的 g",
			g:    adminModel.GranularityUnknown,
			points: []*pb.StatPoint{
				{Time: timestamppb.New(t20220101_0000), Value: 10},
				{Time: timestamppb.New(t20220101_0030), Value: 20},
				{Time: timestamppb.New(t20220101_0005), Value: 30},
			},
			ok: false,
		},
	}

	for _, tc := range testCases {
		err := checkTimeAlignment(tc.g, t20220101_0000, t20220201_0000, loc, tc.points)
		if tc.ok {
			assert.NoError(t, err)
		} else {
			assert.Error(t, err)
		}
	}
}

func makePointsArrayFromTestRepr(x string) []uint64 {
	if len(x) == 0 {
		return nil
	}

	y := make([]uint64, len(x))
	for i, ch := range x {
		var val uint64
		switch ch {
		case 'x':
			val = NonexistentValue
		case '0', '1', '2', '3', '4', '5', '6', '7', '8', '9':
			val = uint64(ch - '0')
		default:
			panic("unsupported test string format")
		}
		y[i] = val
	}
	return y
}

type testDataRowRepr struct {
	t0 time.Time
	p  string
}

func (r *testDataRowRepr) toDataRow() dataRow {
	return dataRow{
		T0: r.t0,
		P:  makePointsArrayFromTestRepr(r.p),
	}
}

func makeDataRowsFromTestRepr(rows []testDataRowRepr) []dataRow {
	return lo.Map(rows, func(x testDataRowRepr, _ int) dataRow { return x.toDataRow() })
}

func assertDataRows(t *testing.T, expectedRepr []testDataRowRepr, actual []dataRow, msgAndArgs ...any) {
	expected := makeDataRowsFromTestRepr(expectedRepr)
	assert.Len(t, actual, len(expected), msgAndArgs...)
	for i, expectedRow := range expected {
		actualRow := actual[i]
		assert.True(t, expectedRow.T0.Equal(actualRow.T0), msgAndArgs...)
		assert.Equal(t, expectedRow.P, actualRow.P, msgAndArgs...)
	}
}

func TestFillNonExistentPointsInplace(t *testing.T) {
	test.RunWithUTCAndCST(t, testFillNonExistentPointsInplace)
}

func testFillNonExistentPointsInplace(t *testing.T, loc *time.Location) {
	testCases := []struct {
		desc         string
		g            adminModel.Granularity
		t0           time.Time
		t            time.Time
		towardsEnd   bool
		originalRepr string
		expectedRepr string
	}{
		{
			desc:         "g=day, no-op",
			g:            adminModel.GranularityDay,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			towardsEnd:   false,
			originalRepr: "1234567890987654321012345678",
			expectedRepr: "1234567890987654321012345678",
		},
		{
			desc:         "g=day, no-op (t before t0)",
			g:            adminModel.GranularityDay,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			towardsEnd:   false,
			originalRepr: "1234567890987654321012345678",
			expectedRepr: "1234567890987654321012345678",
		},
		{
			desc:         "g=day, no-op (t after the row)",
			g:            adminModel.GranularityDay,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 3, 1, 0, 0, 0, 0, loc),
			towardsEnd:   false,
			originalRepr: "1234567890987654321012345678",
			expectedRepr: "1234567890987654321012345678",
		},
		{
			desc:         "g=month, no-op",
			g:            adminModel.GranularityMonth,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 3, 1, 0, 0, 0, 0, loc),
			towardsEnd:   false,
			originalRepr: "1",
			expectedRepr: "1",
		},
		{
			desc:         "g=day, 1 point towards start",
			g:            adminModel.GranularityDay,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 2, 2, 0, 0, 0, 0, loc),
			towardsEnd:   false,
			originalRepr: "1234567890987654321012345678",
			expectedRepr: "x234567890987654321012345678",
		},
		{
			desc:         "g=day, 5 points towards start",
			g:            adminModel.GranularityDay,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 2, 6, 0, 0, 0, 0, loc),
			towardsEnd:   false,
			originalRepr: "1234567890987654321012345678",
			expectedRepr: "xxxxx67890987654321012345678",
		},
		{
			desc:         "g=day, 27 points towards start",
			g:            adminModel.GranularityDay,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 2, 28, 0, 0, 0, 0, loc),
			towardsEnd:   false,
			originalRepr: "1234567890987654321012345678",
			expectedRepr: "xxxxxxxxxxxxxxxxxxxxxxxxxxx8",
		},
		{
			desc:         "g=day, 27 points towards end",
			g:            adminModel.GranularityDay,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 2, 2, 0, 0, 0, 0, loc),
			towardsEnd:   true,
			originalRepr: "1234567890987654321012345678",
			expectedRepr: "1xxxxxxxxxxxxxxxxxxxxxxxxxxx",
		},
		{
			desc:         "g=day, 23 points towards end",
			g:            adminModel.GranularityDay,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 2, 6, 0, 0, 0, 0, loc),
			towardsEnd:   true,
			originalRepr: "1234567890987654321012345678",
			expectedRepr: "12345xxxxxxxxxxxxxxxxxxxxxxx",
		},
		{
			desc:         "g=day, 1 point towards end",
			g:            adminModel.GranularityDay,
			t0:           time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			t:            time.Date(2023, 2, 28, 0, 0, 0, 0, loc),
			towardsEnd:   true,
			originalRepr: "1234567890987654321012345678",
			expectedRepr: "123456789098765432101234567x",
		},
	}

	for _, tc := range testCases {
		dest := dataRow{
			T0: tc.t0,
			P:  makePointsArrayFromTestRepr(tc.originalRepr),
		}
		fillNonExistentPointsInplace(&dest, tc.g, tc.t, tc.towardsEnd)

		expected := makePointsArrayFromTestRepr(tc.expectedRepr)
		assert.Equal(t, expected, dest.P, tc.desc)
	}
}

func TestPrepareEmptyDataRows(t *testing.T) {
	test.RunWithUTCAndCST(t, testPrepareEmptyDataRows)
}

func testPrepareEmptyDataRows(t *testing.T, loc *time.Location) {
	testCases := []struct {
		desc         string
		g            adminModel.Granularity
		start        time.Time
		end          time.Time
		expectedRepr []testDataRowRepr
	}{
		{
			desc:  "g=month, trivial",
			g:     adminModel.GranularityMonth,
			start: time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 6, 1, 0, 0, 0, 0, loc),
			expectedRepr: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "0"},
				{t0: time.Date(2023, 3, 1, 0, 0, 0, 0, loc), p: "0"},
				{t0: time.Date(2023, 4, 1, 0, 0, 0, 0, loc), p: "0"},
				{t0: time.Date(2023, 5, 1, 0, 0, 0, 0, loc), p: "0"},
			},
		},
		{
			desc:  "g=day, trivial",
			g:     adminModel.GranularityDay,
			start: time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 3, 1, 0, 0, 0, 0, loc),
			expectedRepr: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "0000000000000000000000000000"},
			},
		},
		{
			desc:  "g=day, February, leap year",
			g:     adminModel.GranularityDay,
			start: time.Date(2024, 2, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2024, 3, 1, 0, 0, 0, 0, loc),
			expectedRepr: []testDataRowRepr{
				{t0: time.Date(2024, 2, 1, 0, 0, 0, 0, loc), p: "00000000000000000000000000000"},
			},
		},
		{
			desc:  "g=day, pads",
			g:     adminModel.GranularityDay,
			start: time.Date(2023, 2, 10, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 2, 23, 0, 0, 0, 0, loc),
			expectedRepr: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "xxxxxxxxx0000000000000xxxxxx"},
			},
		},
		{
			desc:  "g=day, multiple months with pads",
			g:     adminModel.GranularityDay,
			start: time.Date(2023, 2, 10, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 3, 23, 0, 0, 0, 0, loc),
			expectedRepr: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "xxxxxxxxx0000000000000000000"},
				{t0: time.Date(2023, 3, 1, 0, 0, 0, 0, loc), p: "0000000000000000000000xxxxxxxxx"},
			},
		},
		{
			desc:  "g=5min, pads",
			g:     adminModel.Granularity5Min,
			start: time.Date(2023, 2, 10, 4, 5, 0, 0, loc),
			end:   time.Date(2023, 2, 12, 15, 35, 0, 0, loc),
			expectedRepr: []testDataRowRepr{
				{
					t0: time.Date(2023, 2, 10, 0, 0, 0, 0, loc),
					p:  strings.Repeat("x", 4*12+1) + strings.Repeat("0", 288-(4*12+1)),
				},
				{
					t0: time.Date(2023, 2, 11, 0, 0, 0, 0, loc),
					p:  strings.Repeat("0", 288),
				},
				{
					t0: time.Date(2023, 2, 12, 0, 0, 0, 0, loc),
					p:  strings.Repeat("0", 15*12+7) + strings.Repeat("x", 288-(15*12+7)),
				},
			},
		},
	}

	for _, tc := range testCases {
		actual := prepareEmptyDataRows(tc.g, tc.start, tc.end, loc)
		assertDataRows(t, tc.expectedRepr, actual, tc.desc)
	}
}

func TestRowsFromSparseReprAndTimespan(t *testing.T) {
	test.RunWithUTCAndCST(t, testRowsFromSparseReprAndTimespan)
}

func testRowsFromSparseReprAndTimespan(t *testing.T, loc *time.Location) {
	testCases := []struct {
		desc         string
		g            adminModel.Granularity
		start        time.Time
		end          time.Time
		points       []*pb.StatPoint
		ok           bool
		expectedRepr []testDataRowRepr
	}{
		{
			desc:         "g=unknown, trivial error",
			g:            adminModel.GranularityUnknown,
			start:        time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			end:          time.Date(2023, 6, 1, 0, 0, 0, 0, loc),
			points:       nil,
			ok:           false,
			expectedRepr: nil,
		},
		{
			desc:   "g=5min, empty input",
			g:      adminModel.Granularity5Min,
			start:  time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			end:    time.Date(2023, 1, 3, 0, 0, 0, 0, loc),
			points: nil,
			ok:     true,
			expectedRepr: []testDataRowRepr{
				{t0: time.Date(2023, 1, 1, 0, 0, 0, 0, loc), p: strings.Repeat("0", 288)},
				{t0: time.Date(2023, 1, 2, 0, 0, 0, 0, loc), p: strings.Repeat("0", 288)},
			},
		},
		{
			desc:  "g=5min, unaligned point",
			g:     adminModel.Granularity5Min,
			start: time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 1, 3, 0, 0, 0, 0, loc),
			points: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 4, 0, 0, loc)), Value: 1},
			},
			ok:           false,
			expectedRepr: nil,
		},
		{
			desc:  "g=month, trivial",
			g:     adminModel.GranularityMonth,
			start: time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 6, 1, 0, 0, 0, 0, loc),
			points: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 5, 1, 0, 0, 0, 0, loc)), Value: 3},
			},
			ok: true,
			expectedRepr: []testDataRowRepr{
				{t0: time.Date(2023, 1, 1, 0, 0, 0, 0, loc), p: "0"},
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "1"},
				{t0: time.Date(2023, 3, 1, 0, 0, 0, 0, loc), p: "2"},
				{t0: time.Date(2023, 4, 1, 0, 0, 0, 0, loc), p: "0"},
				{t0: time.Date(2023, 5, 1, 0, 0, 0, 0, loc), p: "3"},
			},
		},
		{
			desc:  "g=day, trivial",
			g:     adminModel.GranularityDay,
			start: time.Date(2023, 2, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 3, 1, 0, 0, 0, 0, loc),
			points: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 2, 2, 0, 0, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 2, 3, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 2, 5, 0, 0, 0, 0, loc)), Value: 3},
				{Time: timestamppb.New(time.Date(2023, 2, 7, 0, 0, 0, 0, loc)), Value: 4},
				{Time: timestamppb.New(time.Date(2023, 2, 11, 0, 0, 0, 0, loc)), Value: 5},
				{Time: timestamppb.New(time.Date(2023, 2, 13, 0, 0, 0, 0, loc)), Value: 6},
				{Time: timestamppb.New(time.Date(2023, 2, 17, 0, 0, 0, 0, loc)), Value: 7},
				{Time: timestamppb.New(time.Date(2023, 2, 19, 0, 0, 0, 0, loc)), Value: 8},
				{Time: timestamppb.New(time.Date(2023, 2, 23, 0, 0, 0, 0, loc)), Value: 9},
			},
			ok: true,
			expectedRepr: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "0120304000506000708000900000"},
			},
		},
		{
			desc:  "g=day, multi-month with pads",
			g:     adminModel.GranularityDay,
			start: time.Date(2023, 2, 5, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 3, 10, 0, 0, 0, 0, loc),
			points: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 2, 5, 0, 0, 0, 0, loc)), Value: 3},
				{Time: timestamppb.New(time.Date(2023, 2, 7, 0, 0, 0, 0, loc)), Value: 4},
				{Time: timestamppb.New(time.Date(2023, 2, 11, 0, 0, 0, 0, loc)), Value: 5},
				{Time: timestamppb.New(time.Date(2023, 2, 13, 0, 0, 0, 0, loc)), Value: 6},
				{Time: timestamppb.New(time.Date(2023, 2, 17, 0, 0, 0, 0, loc)), Value: 7},
				{Time: timestamppb.New(time.Date(2023, 2, 19, 0, 0, 0, 0, loc)), Value: 8},
				{Time: timestamppb.New(time.Date(2023, 2, 23, 0, 0, 0, 0, loc)), Value: 9},
				{Time: timestamppb.New(time.Date(2023, 3, 2, 0, 0, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 3, 9, 0, 0, 0, 0, loc)), Value: 2},
			},
			ok: true,
			expectedRepr: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "xxxx304000506000708000900000"},
				{t0: time.Date(2023, 3, 1, 0, 0, 0, 0, loc), p: "010000002xxxxxxxxxxxxxxxxxxxxxx"},
			},
		},
	}

	for _, tc := range testCases {
		actual, err := rowsFromSparseReprAndTimespan(tc.g, tc.start, tc.end, loc, tc.points)
		if tc.ok {
			assert.NoError(t, err, tc.desc)
			assertDataRows(t, tc.expectedRepr, actual, tc.desc)
		} else {
			assert.Error(t, err, tc.desc)
			assert.Nil(t, actual, tc.desc)
		}
	}
}

func TestSparseReprFromRows(t *testing.T) {
	test.RunWithUTCAndCST(t, testSparseReprFromRows)
}

func testSparseReprFromRows(t *testing.T, loc *time.Location) {
	testCases := []struct {
		desc     string
		g        adminModel.Granularity
		rows     []testDataRowRepr
		ok       bool
		expected []*pb.StatPoint
	}{
		{
			desc: "invalid g",
			g:    adminModel.Granularity(233),
			rows: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "xxxxx00010230076500000000000"},
				{t0: time.Date(2023, 3, 1, 0, 0, 0, 0, loc), p: "90000800004xxxxxxxxxxxxxxxxxxxx"},
			},
			ok:       false,
			expected: nil,
		},
		{
			desc: "g=day, empty output",
			g:    adminModel.GranularityDay,
			rows: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "xxxxx00000000000000000000000"},
				{t0: time.Date(2023, 3, 1, 0, 0, 0, 0, loc), p: "0000000000000000000000000000000"},
				{t0: time.Date(2023, 4, 1, 0, 0, 0, 0, loc), p: "00000xxxxxxxxxxxxxxxxxxxxxxxxx"},
			},
			ok:       true,
			expected: nil,
		},
		{
			desc: "g=day, trivial",
			g:    adminModel.GranularityDay,
			rows: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "xxxxx00010230076500000000000"},
				{t0: time.Date(2023, 3, 1, 0, 0, 0, 0, loc), p: "90000800004xxxxxxxxxxxxxxxxxxxx"},
			},
			ok: true,
			expected: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 2, 9, 0, 0, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 2, 11, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 2, 12, 0, 0, 0, 0, loc)), Value: 3},
				{Time: timestamppb.New(time.Date(2023, 2, 15, 0, 0, 0, 0, loc)), Value: 7},
				{Time: timestamppb.New(time.Date(2023, 2, 16, 0, 0, 0, 0, loc)), Value: 6},
				{Time: timestamppb.New(time.Date(2023, 2, 17, 0, 0, 0, 0, loc)), Value: 5},
				{Time: timestamppb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, loc)), Value: 9},
				{Time: timestamppb.New(time.Date(2023, 3, 6, 0, 0, 0, 0, loc)), Value: 8},
				{Time: timestamppb.New(time.Date(2023, 3, 11, 0, 0, 0, 0, loc)), Value: 4},
			},
		},
		{
			desc: "g=5min, trivial with unsorted input rows",
			g:    adminModel.Granularity5Min,
			rows: []testDataRowRepr{
				{t0: time.Date(2023, 2, 8, 0, 0, 0, 0, loc), p: strings.Repeat("0", 100) + "789" + strings.Repeat("x", 185)},
				{t0: time.Date(2023, 2, 7, 0, 0, 0, 0, loc), p: strings.Repeat("0", 288)},
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: fmt.Sprintf("%050d%050d%050d%050d%050d%038d", 6, 5, 4, 3, 2, 1)},
			},
			ok: true,
			expected: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 2, 1, 4, 5, 0, 0, loc)), Value: 6},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 8, 15, 0, 0, loc)), Value: 5},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 12, 25, 0, 0, loc)), Value: 4},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 16, 35, 0, 0, loc)), Value: 3},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 20, 45, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 23, 55, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 2, 8, 8, 20, 0, 0, loc)), Value: 7},
				{Time: timestamppb.New(time.Date(2023, 2, 8, 8, 25, 0, 0, loc)), Value: 8},
				{Time: timestamppb.New(time.Date(2023, 2, 8, 8, 30, 0, 0, loc)), Value: 9},
			},
		},
		{
			desc: "g=month, trivial",
			g:    adminModel.Granularity5Min,
			rows: []testDataRowRepr{
				{t0: time.Date(2023, 2, 1, 0, 0, 0, 0, loc), p: "1"},
				{t0: time.Date(2023, 3, 1, 0, 0, 0, 0, loc), p: "2"},
				{t0: time.Date(2023, 4, 1, 0, 0, 0, 0, loc), p: "0"},
				{t0: time.Date(2023, 5, 1, 0, 0, 0, 0, loc), p: "3"},
			},
			ok: true,
			expected: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 5, 1, 0, 0, 0, 0, loc)), Value: 3},
			},
		},
	}

	for _, tc := range testCases {
		rows := makeDataRowsFromTestRepr(tc.rows)
		actual, err := sparseReprFromRows(tc.g, loc, rows)
		if tc.ok {
			assert.NoError(t, err, tc.desc)
			assert.Equal(t, tc.expected, actual, tc.desc)
		} else {
			assert.Error(t, err, tc.desc)
			assert.Nil(t, actual, tc.desc)
		}
	}
}

func TestFillZeroesForSparseRepr(t *testing.T) {
	test.RunWithUTCAndCST(t, testFillZeroesForSparseRepr)
}

func testFillZeroesForSparseRepr(t *testing.T, loc *time.Location) {
	testCases := []struct {
		desc     string
		g        adminModel.Granularity
		start    time.Time
		end      time.Time
		input    []*pb.StatPoint
		expected []*pb.StatPoint
	}{
		{
			desc:  "g=month, empty input",
			g:     adminModel.GranularityMonth,
			start: time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 6, 1, 0, 0, 0, 0, loc),
			input: nil,
			expected: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 5, 1, 0, 0, 0, 0, loc)), Value: 0},
			},
		},
		{
			desc:  "g=month, already dense input",
			g:     adminModel.GranularityMonth,
			start: time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 6, 1, 0, 0, 0, 0, loc),
			input: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, loc)), Value: 3},
				{Time: timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, loc)), Value: 4},
				{Time: timestamppb.New(time.Date(2023, 5, 1, 0, 0, 0, 0, loc)), Value: 5},
			},
			expected: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, loc)), Value: 3},
				{Time: timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, loc)), Value: 4},
				{Time: timestamppb.New(time.Date(2023, 5, 1, 0, 0, 0, 0, loc)), Value: 5},
			},
		},
		{
			desc:  "g=month, sparse and unordered input, missing first point",
			g:     adminModel.GranularityMonth,
			start: time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 6, 1, 0, 0, 0, 0, loc),
			input: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 5, 1, 0, 0, 0, 0, loc)), Value: 5},
				{Time: timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, loc)), Value: 4},
			},
			expected: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, loc)), Value: 4},
				{Time: timestamppb.New(time.Date(2023, 5, 1, 0, 0, 0, 0, loc)), Value: 5},
			},
		},
		{
			desc:  "g=month, sparse and unordered input, missing end point",
			g:     adminModel.GranularityMonth,
			start: time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 6, 1, 0, 0, 0, 0, loc),
			input: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, loc)), Value: 4},
			},
			expected: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, loc)), Value: 1},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 2},
				{Time: timestamppb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, loc)), Value: 4},
				{Time: timestamppb.New(time.Date(2023, 5, 1, 0, 0, 0, 0, loc)), Value: 0},
			},
		},
		{
			desc:  "g=month, sparse and unordered input, missing start and end point",
			g:     adminModel.GranularityMonth,
			start: time.Date(2023, 1, 1, 0, 0, 0, 0, loc),
			end:   time.Date(2023, 6, 1, 0, 0, 0, 0, loc),
			input: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, loc)), Value: 4},
			},
			expected: []*pb.StatPoint{
				{Time: timestamppb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 3, 1, 0, 0, 0, 0, loc)), Value: 0},
				{Time: timestamppb.New(time.Date(2023, 4, 1, 0, 0, 0, 0, loc)), Value: 4},
				{Time: timestamppb.New(time.Date(2023, 5, 1, 0, 0, 0, 0, loc)), Value: 0},
			},
		},
	}

	for _, tc := range testCases {
		actual := fillZeroesForSparseRepr(tc.g, tc.start, tc.end, loc, tc.input)
		// order of output points is enforced and checked
		assert.Equal(t, tc.expected, actual, tc.desc)
	}
}
