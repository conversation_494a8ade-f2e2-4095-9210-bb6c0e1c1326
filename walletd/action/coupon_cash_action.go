package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// GetCouponCashByID 通过ID获取现金优惠券
func (a *WalletAction) GetCouponCashByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CouponCash, error) {
	cash, err := a.walletBizSrv.GetCouponCashByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponCash(cash)
}

// GetCouponCashByCode 通过 code 获取现金优惠券
func (a *WalletAction) GetCouponCashByCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.CouponCash, error) {
	cash, err := a.walletBizSrv.GetCouponCashByCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponCash(cash)
}

// CreateCouponCash 创建现金优惠券
func (a *WalletAction) CreateCouponCash(
	ctx context.Context,
	cash *pb.CouponCash,
) (*pb.CouponCash, error) {
	_cash, err := adapter.BuildCouponCash(cash)
	if err != nil {
		return nil, err
	}
	_cash, err = a.walletBizSrv.CreateCouponCash(ctx, _cash)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponCash(_cash)
}

// UpdateCouponCashByID 按照 ID 更新现金优惠券
func (a *WalletAction) UpdateCouponCashByID(
	ctx context.Context,
	param *pb.IDCouponCashParam,
) (*pb.CouponCash, error) {
	_cash, err := adapter.BuildCouponCash(param.GetCouponCash())
	if err != nil {
		return nil, err
	}
	_cash, err = a.walletBizSrv.UpdateCouponCashByID(ctx, param.GetId(), _cash)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponCash(_cash)
}

// UpdateCouponCashByCode 按照 code 更新现金优惠券
func (a *WalletAction) UpdateCouponCashByCode(
	ctx context.Context,
	param *pb.CodeCouponCashParam,
) (*pb.CouponCash, error) {
	_cash, err := adapter.BuildCouponCash(param.GetCouponCash())
	if err != nil {
		return nil, err
	}
	_cash, err = a.walletBizSrv.UpdateCouponCashByCode(ctx, param.GetCode(), _cash)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponCash(_cash)
}

// DeleteCouponCashByID 按照 ID 删除现金优惠券
func (a *WalletAction) DeleteCouponCashByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CouponCash, error) {
	_cash, err := a.walletBizSrv.DeleteCouponCashByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponCash(_cash)
}

// DeleteCouponCashByCode 按照 code 删除现金优惠券
func (a *WalletAction) DeleteCouponCashByCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.CouponCash, error) {
	_cash, err := a.walletBizSrv.DeleteCouponCashByCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbCouponCash(_cash)
}

// ListAllCouponCash 列举所有现金优惠券
func (a *WalletAction) ListAllCouponCash(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.CouponCashList, error) {
	count, err := a.walletBizSrv.CountAllCouponCash(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	cash, err := a.walletBizSrv.ListAllCouponCash(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponCashList{
		Count:      count,
		CouponCash: make([]*pb.CouponCash, len(cash)),
	}
	for i, c := range cash {
		_cash, err := adapter.BuildPbCouponCash(&c)
		if err != nil {
			return nil, err
		}
		list.CouponCash[i] = _cash
	}
	return list, nil
}

// CountAllCouponCash 获取所有现金优惠券数量
func (a *WalletAction) CountAllCouponCash(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountAllCouponCash(ctx)
	return &pb.CountParam{Count: count}, err
}

// ListCouponCashByScopeID 通过计费范围列举现金优惠券
func (a *WalletAction) ListCouponCashByScopeID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.CouponCashList, error) {
	count, err := a.walletBizSrv.CountCouponCashByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	cash, err := a.walletBizSrv.ListCouponCashByScopeID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponCashList{
		Count:      count,
		CouponCash: make([]*pb.CouponCash, len(cash)),
	}
	for i, cash := range cash {
		_cash, err := adapter.BuildPbCouponCash(&cash)
		if err != nil {
			return nil, err
		}
		list.CouponCash[i] = _cash
	}
	return list, nil
}

// CountCouponCashByScopeID 通过计费范围获取现金优惠券数量
func (a *WalletAction) CountCouponCashByScopeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountCouponCashByScopeID(ctx, param.GetId())
	return &pb.CountParam{Count: count}, err
}

// ListCouponCashByUID 通过用户ID列举现金优惠券
func (a *WalletAction) ListCouponCashByUID(
	ctx context.Context,
	param *pb.UIDPagingParam,
) (*pb.CouponCashList, error) {
	count, err := a.walletBizSrv.CountCouponCashByUID(ctx, param.GetUid())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	cash, err := a.walletBizSrv.ListCouponCashByUID(ctx, param.GetUid(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.CouponCashList{
		Count:      count,
		CouponCash: make([]*pb.CouponCash, len(cash)),
	}
	for i, c := range cash {
		_cash, err := adapter.BuildPbCouponCash(&c)
		if err != nil {
			return nil, err
		}
		list.CouponCash[i] = _cash
	}
	return list, nil
}

// CountCouponCashByUID 通过用户ID获取现金优惠券数量
func (a *WalletAction) CountCouponCashByUID(
	ctx context.Context,
	param *pb.UIDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountCouponCashByUID(ctx, param.GetUid())
	return &pb.CountParam{Count: count}, err
}
