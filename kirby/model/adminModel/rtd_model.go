package adminModel

import (
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"

	"qiniu.io/pay/kirby/model/qrn"
)

// ResourceTypeDefinition 资源类型定义
type ResourceTypeDefinition struct {
	// ID 主键
	ID uint64 `gorm:"primary_key"`
	// Code RTD 的唯一标识
	Code string `gorm:"column:code;type:VARCHAR(64);not null;unique_index:idx_rtd_code;comment:'RTD 的唯一标识'"`
	// QRNMatches 该 RTD 可匹配的 QRN 正则列表，以 JSON string array 形式存储
	QRNMatches qrn.MatchExprArray `gorm:"column:qrn_matches;type:TEXT(65535);comment:'该 RTD 可匹配的 QRN 正则列表，以 JSON string array 形式存储'"`
	// EffectTime 该 RTD 从何时开始生效（含）
	EffectTime time.Time `gorm:"column:effect_time;type:DATETIME(6);comment:'该 RTD 从何时开始生效（含）'"`

	CreatedAt time.Time `gorm:"column:created_at;type:DATETIME(6);default:now(6)"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:DATETIME(6);default:now(6) ON UPDATE now(6)"`
}

// ResourceTypeDefinitionDao is data access object of ResourceTypeDefinition model
type ResourceTypeDefinitionDao struct {
	base *dao.BaseDao
}

// NewResourceTypeDefinitionDao is constructor of ResourceTypeDefinitionDao
func NewResourceTypeDefinitionDao(base *dao.BaseDao) *ResourceTypeDefinitionDao {
	return &ResourceTypeDefinitionDao{
		base: base,
	}
}

// Save inserts or updates a ResourceTypeDefinition by id
func (d *ResourceTypeDefinitionDao) Save(model *ResourceTypeDefinition) error {
	err := d.base.Execute(func(value any) error {
		return d.base.Save(value).Error
	}, model)
	if err != nil {
		return errors.Trace(err).WithField("model", model)
	}
	return nil
}

func (d *ResourceTypeDefinitionDao) GetByCode(
	code string,
	effectTime time.Time,
	epoch time.Time,
) (*ResourceTypeDefinition, error) {
	var model ResourceTypeDefinition
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&ResourceTypeDefinition{}).
			Where(
				"`code` = ? AND `effect_time` <= ? AND `created_at` <= ?",
				code,
				effectTime,
				epoch,
			).
			First(value).
			Error
	}, &model)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{"code": code, "epoch": epoch})
	}
	return &model, nil
}

func (d *ResourceTypeDefinitionDao) ListAll(
	effectTime time.Time,
	epoch time.Time,
) ([]*ResourceTypeDefinition, error) {
	var result []*ResourceTypeDefinition
	err := d.base.Execute(func(value any) error {
		return d.base.Model(&ResourceTypeDefinition{}).
			Where(
				"`effect_time` <= ? AND `created_at` <= ?",
				effectTime,
				epoch,
			).
			Find(value).
			Error
	}, &result)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{"effectTime": effectTime, "epoch": epoch})
	}
	return result, nil
}
