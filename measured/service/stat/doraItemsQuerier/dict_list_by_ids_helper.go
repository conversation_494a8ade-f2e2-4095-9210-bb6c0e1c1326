package doraItemsQuerier

import (
	"context"

	"github.com/qbox/bo-base/v4/sync/resultgroup"
	dictpb "github.com/qbox/pay-sdk/dict"
)

func queryDictItems(
	ctx context.Context,
	dictClient dictpb.PayDictServiceClient,
	ids []uint64,
) ([]*dictpb.Item, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	// 拍脑袋拍的
	const maxConcurrency = 20

	// TODO: 目前没有 ListItemsByIDs 接口，先用并发查询搞一下
	result, err := resultgroup.ThrottledParallelMap(ids, maxConcurrency, func(id uint64) (*dictpb.Item, error) {
		obj, err := dictClient.GetItemByID(ctx, &dictpb.IDParam{Id: id})
		if err != nil {
			return nil, err
		}

		return obj, nil
	})
	if err != nil {
		return nil, err
	}

	return result, nil
}

// 抄的 queryDictItems，只有类型不一样
// 敲碗等泛型
func queryDictItemGroups(
	ctx context.Context,
	dictClient dictpb.PayDictServiceClient,
	ids []uint64,
) ([]*dictpb.ItemGroup, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	// 拍脑袋拍的
	const maxConcurrency = 20

	// TODO: 目前没有 ListItemGroupsByIDs 接口，先用并发查询搞一下
	result, err := resultgroup.ThrottledParallelMap(ids, maxConcurrency, func(id uint64) (*dictpb.ItemGroup, error) {
		obj, err := dictClient.GetItemGroupByID(ctx, &dictpb.IDParam{Id: id})
		if err != nil {
			return nil, err
		}

		return obj, nil
	})
	if err != nil {
		return nil, err
	}

	return result, nil
}

// 抄的 queryDictItems，只有类型不一样
// 敲碗等泛型
func queryDictProducts(
	ctx context.Context,
	dictClient dictpb.PayDictServiceClient,
	ids []uint64,
) ([]*dictpb.Product, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	// 拍脑袋拍的
	const maxConcurrency = 20

	// TODO: 目前没有 ListProductsByIDs 接口，先用并发查询搞一下
	result, err := resultgroup.ThrottledParallelMap(ids, maxConcurrency, func(id uint64) (*dictpb.Product, error) {
		obj, err := dictClient.GetProductByID(ctx, &dictpb.IDParam{Id: id})
		if err != nil {
			return nil, err
		}

		return obj, nil
	})
	if err != nil {
		return nil, err
	}

	return result, nil
}
