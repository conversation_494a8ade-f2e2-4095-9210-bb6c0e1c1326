package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"qiniu.io/pay/walletd/model"
)

func TestCouponCashRoundTrip(t *testing.T) {
	x := &model.CouponCash{
		ID:          233,
		Code:        "foo",
		Type:        "SPONSOR",
		ScopeID:     456,
		Name:        "test",
		Description: "test description",
		Remark:      "test remark",
		Money:       10000,
		EffectTime:  base.NewHNS(time.Now().Round(0)),
		DeadTime:    base.NewHNS(time.Now().Round(0)),
		CreatedAt:   time.Now().Round(0),
		UpdatedAt:   time.Now().Round(0),
	}

	y, err := BuildPbCouponCash(x)
	assert.NoError(t, err)

	z, err := BuildCouponCash(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.Code, z.Code)
	assert.Equal(t, x.Type, z.Type)
	assert.Equal(t, x.ScopeID, z.ScopeID)
	assert.Equal(t, x.Name, z.Name)
	assert.Equal(t, x.Description, z.Description)
	assert.Equal(t, x.Remark, z.Remark)
	assert.Equal(t, x.Money, z.Money)
	assert.Equal(t, x.EffectTime, z.EffectTime)
	assert.Equal(t, x.DeadTime, z.DeadTime)
	assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
}
