package action

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/dict"
	"qiniu.io/pay/dictd/action/adapter"
)

// GetItemByID 根据ID获取计费项
func (a *DictAction) GetItemByID(
	ctx context.Context,
	itemID *pb.IDParam,
) (*pb.Item, error) {
	item, err := a.dictBizSrv.GetItemByID(ctx, itemID.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItem(item)
}

// GetItemByCode 根据计费项code获取计费项
func (a *DictAction) GetItemByCode(
	ctx context.Context,
	param *pb.CodeParam,
) (*pb.Item, error) {
	item, err := a.dictBizSrv.GetItemByCode(ctx, param.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItem(item)
}

// MultiGetItemByCodes 根据计费项 codes 批量获取计费项
func (a *DictAction) MultiGetItemByCodes(
	ctx context.Context,
	param *pb.MultiCodeParam,
) (*pb.ItemList, error) {
	items, err := a.dictBizSrv.ListItemsByCodes(ctx, param.GetCodes())
	if err != nil {
		return nil, err
	}
	pbItems, err := adapter.BuildPbItems(items)
	if err != nil {
		return nil, err
	}
	return &pb.ItemList{
		Items: pbItems,
		Count: uint64(len(pbItems)),
	}, nil
}

// ListItemsByProductID 根据产品 ID 获取计费项列表
func (a *DictAction) ListItemsByProductID(
	ctx context.Context,
	param *pb.IDPagingActiveParam,
) (*pb.ItemList, error) {
	count, err := a.dictBizSrv.CountItemsByProductID(ctx, param.GetId(), param.GetOnlyActive())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	items, err := a.dictBizSrv.ListItemsByProductID(ctx, param.GetId(), param.GetOnlyActive(), offset, limit)
	if err != nil {
		return nil, err
	}
	pbItems, err := adapter.BuildPbItems(items)
	if err != nil {
		return nil, err
	}
	return &pb.ItemList{
		Items: pbItems,
		Count: count,
	}, nil
}

// ListItemsByProductCode 根据产品 code 获取计费项列表
func (a *DictAction) ListItemsByProductCode(
	ctx context.Context,
	param *pb.CodePagingActiveParam,
) (*pb.ItemList, error) {
	count, err := a.dictBizSrv.CountItemsByProductCode(ctx, param.GetCode(), param.GetOnlyActive())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	items, err := a.dictBizSrv.ListItemsByProductCode(ctx, param.GetCode(), param.GetOnlyActive(), offset, limit)
	if err != nil {
		return nil, err
	}
	pbItems, err := adapter.BuildPbItems(items)
	if err != nil {
		return nil, err
	}
	return &pb.ItemList{
		Items: pbItems,
		Count: count,
	}, nil
}

// CountItemsByProductID 根据产品 ID 获取计费项数量
func (a *DictAction) CountItemsByProductID(
	ctx context.Context,
	param *pb.IDActiveParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemsByProductID(ctx, param.GetId(), param.GetOnlyActive())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// CountItemsByProductCode 根据产品 code 获取计费项数量
func (a *DictAction) CountItemsByProductCode(
	ctx context.Context,
	param *pb.CodeActiveParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemsByProductCode(ctx, param.GetCode(), param.GetOnlyActive())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListItemsByGroupID 根据计费项组ID获取所有计费项
func (a *DictAction) ListItemsByGroupID(
	ctx context.Context,
	param *pb.IDPagingActiveParam,
) (*pb.ItemList, error) {
	count, err := a.dictBizSrv.CountItemsByGroupID(ctx, param.GetId(), param.GetOnlyActive())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	items, err := a.dictBizSrv.ListItemsByGroupID(ctx, param.GetId(), param.GetOnlyActive(), offset, limit)
	if err != nil {
		return nil, err
	}
	pbItems, err := adapter.BuildPbItems(items)
	if err != nil {
		return nil, err
	}
	return &pb.ItemList{
		Items: pbItems,
		Count: count,
	}, nil
}

// ListItemsByGroupCode 根据计费项组 code 获取所有计费项
func (a *DictAction) ListItemsByGroupCode(
	ctx context.Context,
	param *pb.CodePagingActiveParam,
) (*pb.ItemList, error) {
	count, err := a.dictBizSrv.CountItemsByGroupCode(ctx, param.GetCode(), param.GetOnlyActive())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	items, err := a.dictBizSrv.ListItemsByGroupCode(ctx, param.GetCode(), param.GetOnlyActive(), offset, limit)
	if err != nil {
		return nil, err
	}
	pbItems, err := adapter.BuildPbItems(items)
	if err != nil {
		return nil, err
	}
	return &pb.ItemList{
		Items: pbItems,
		Count: count,
	}, nil
}

// CountItemsByGroupID 根据计费项组ID获取计费项数量
func (a *DictAction) CountItemsByGroupID(
	ctx context.Context,
	param *pb.IDActiveParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemsByGroupID(ctx, param.GetId(), param.GetOnlyActive())
	return &pb.CountParam{Count: count}, err
}

// CountItemsByGroupCode 根据计费项组 code 获取计费项数量
func (a *DictAction) CountItemsByGroupCode(
	ctx context.Context,
	param *pb.CodeActiveParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemsByGroupCode(ctx, param.GetCode(), param.GetOnlyActive())
	return &pb.CountParam{Count: count}, err
}

// ListItemsByZoneID 根据区域/机房ID获取所有计费项
func (a *DictAction) ListItemsByZoneID(
	ctx context.Context,
	param *pb.IDPagingActiveParam,
) (*pb.ItemList, error) {
	count, err := a.dictBizSrv.CountItemsByZoneID(ctx, param.GetId(), param.GetOnlyActive())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	items, err := a.dictBizSrv.ListItemsByZoneID(ctx, param.GetId(), param.GetOnlyActive(), offset, limit)
	if err != nil {
		return nil, err
	}
	pbItems, err := adapter.BuildPbItems(items)
	if err != nil {
		return nil, err
	}
	return &pb.ItemList{
		Items: pbItems,
		Count: count,
	}, nil
}

// CountItemsByZoneID 根据机房获取
func (a *DictAction) CountItemsByZoneID(
	ctx context.Context,
	param *pb.IDActiveParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemsByZoneID(ctx, param.GetId(), param.GetOnlyActive())
	return &pb.CountParam{Count: count}, err
}

// ListAllItems 列出所有计费项
func (a *DictAction) ListAllItems(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.ItemList, error) {
	count, err := a.dictBizSrv.CountAllItems(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	items, err := a.dictBizSrv.ListAllItems(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	pbItems, err := adapter.BuildPbItems(items)
	if err != nil {
		return nil, err
	}
	return &pb.ItemList{
		Items: pbItems,
		Count: count,
	}, nil
}

// CreateItem 创建计费项
func (a *DictAction) CreateItem(
	ctx context.Context,
	item *pb.Item,
) (*pb.Item, error) {
	m, err := adapter.BuildItem(item)
	if err != nil {
		return nil, err
	}

	i, err := a.dictBizSrv.CreateItem(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItem(i)
}

// UpdateItemByID 根据计费项ID更新计费项
func (a *DictAction) UpdateItemByID(
	ctx context.Context,
	param *pb.IDItemParam,
) (*pb.Item, error) {
	item := param.GetItem()
	m, err := adapter.BuildItem(item)
	if err != nil {
		return nil, err
	}

	i, err := a.dictBizSrv.UpdateItemByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItem(i)
}

// UpdateItemByCode 根据计费项 code 更新计费项
func (a *DictAction) UpdateItemByCode(
	ctx context.Context,
	param *pb.CodeItemParam,
) (*pb.Item, error) {
	item := param.GetItem()
	m, err := adapter.BuildItem(item)
	if err != nil {
		return nil, err
	}

	i, err := a.dictBizSrv.UpdateItemByCode(ctx, param.GetCode(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItem(i)
}

// DeleteItemByID 根据计费项ID删除计费项
func (a *DictAction) DeleteItemByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.Item, error) {
	item, err := a.dictBizSrv.DeleteItemByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItem(item)
}

// SearchItemByFilter 按条件搜索
func (a *DictAction) SearchItemByFilter(
	ctx context.Context,
	param *pb.SearchItemReq,
) (*pb.ItemList, error) {
	filter := param.GetFilter()
	if filter == nil {
		filter = &pb.SearchItemFilter{}
	}
	items, err := a.dictBizSrv.SearchItemByFilter(ctx, filter.Ids, filter.ProductIDs,
		filter.ZoneIDs, filter.OnlyActive, int(param.Offset), int(param.Limit))
	if err != nil {
		return nil, err
	}
	pbItems, err := adapter.BuildPbItems(items)
	if err != nil {
		return nil, err
	}
	return &pb.ItemList{
		Items: pbItems,
		Count: uint64(len(pbItems)),
	}, nil
}

// SearchItemsByFuzzy 模糊搜索
func (a *DictAction) SearchItemsByFuzzy(
	ctx context.Context,
	param *pb.SearchFuzzyReq,
) (*pb.ItemList, error) {
	// 固定10条，防止前端参数过大
	items, err := a.dictBizSrv.SearchItemsByFuzzy(ctx, param.Keyword, 0, 10)
	if err != nil {
		return nil, err
	}
	pbItems, err := adapter.BuildPbItems(items)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return &pb.ItemList{
		Items: pbItems,
		Count: uint64(len(pbItems)),
	}, nil
}
