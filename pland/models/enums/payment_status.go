package enums

// PaymentStatus 支付状态
type PaymentStatus int

const (
	// PaymentStatusNone 无需付费
	PaymentStatusNone = 1
	// PaymentStatusUnpaid 未支付
	PaymentStatusUnpaid = 2
	// PaymentStatusPaid 已支付
	PaymentStatusPaid = 3
	// PaymentStatusRefund 已退款
	PaymentStatusRefund = 4
	// PaymentStatusFailed 支付失败
	PaymentStatusFailed = 5
)

// Valid valid a payment status
func (t PaymentStatus) Valid() bool {
	switch t {
	case PaymentStatusNone, PaymentStatusUnpaid,
		PaymentStatusPaid, PaymentStatusRefund, PaymentStatusFailed:
		return true
	default:
		return false
	}
}
