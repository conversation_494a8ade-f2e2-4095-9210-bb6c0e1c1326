package service

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	"qiniu.io/pay/measured/model"
)

// GetPackageUserMapByID 根据 ID 查询单个资源包-用户关联关系记录
func (s *MeasureBizService) GetPackageUserMapByID(
	ctx context.Context,
	id uint64,
) (*model.PackageUserMap, error) {
	return s.dao.PackageUserMap.GetByID(id, s.cacheExpires)
}

// ListAllPackageUserMaps 列举所有资源包-用户关联关系记录
func (s *MeasureBizService) ListAllPackageUserMaps(
	ctx context.Context,
	offset int,
	limit int,
) ([]model.PackageUserMap, error) {
	return s.dao.PackageUserMap.ListAll(offset, limit, s.cacheExpires)
}

// ListPackageUserMapsByExcode  通过 excode 查询 package-user-maps
func (s *MeasureBizService) ListPackageUserMapsByExcode(
	ctx context.Context,
	uid uint64,
	excode string,
) ([]model.PackageUserMap, error) {

	return s.dao.PackageUserMap.ListByExcode(uid, excode, s.cacheExpires)
}

// CountAllPackageUserMaps 获取资源包-用户关联关系记录总数
func (s *MeasureBizService) CountAllPackageUserMaps(
	ctx context.Context,
) (uint64, error) {
	return s.dao.PackageUserMap.CountAll(s.cacheExpires)
}

// ListPackageUserMapsByUID 根据 UID 列举资源包-用户关联关系记录
func (s *MeasureBizService) ListPackageUserMapsByUID(
	ctx context.Context,
	uid uint64,
	offset int,
	limit int,
) ([]model.PackageUserMap, error) {
	return s.dao.PackageUserMap.ListByUID(uid, offset, limit, s.cacheExpires)
}

// CountPackageUserMapsByUID 根据 UID 获取资源包-用户关联关系记录数量
func (s *MeasureBizService) CountPackageUserMapsByUID(
	ctx context.Context,
	uid uint64,
) (uint64, error) {
	return s.dao.PackageUserMap.CountByUID(uid, s.cacheExpires)
}

// CreatePackageUserMap 创建一个资源包-用户关联关系记录
func (s *MeasureBizService) CreatePackageUserMap(
	ctx context.Context,
	obj *model.PackageUserMap,
) (*model.PackageUserMap, error) {
	obj.ID = 0
	err := s.dao.PackageUserMap.Save(obj, s.cacheExpires)
	return obj, err
}

// DeletePackageUserMapByID 根据 ID 删除一个资源包-用户关联关系记录
func (s *MeasureBizService) DeletePackageUserMapByID(
	ctx context.Context,
	id uint64,
) (*model.PackageUserMap, error) {
	obj, err := s.dao.PackageUserMap.GetByID(id, s.cacheExpires)
	if err != nil {
		return nil, errors.Annotate(err, "query package_user_map failed").WithField("id", id)
	}
	err = s.dao.PackageUserMap.DeleteByID(id)
	return obj, err
}
