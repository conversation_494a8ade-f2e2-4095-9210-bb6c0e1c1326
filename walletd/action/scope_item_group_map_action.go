package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/wallet"
	"qiniu.io/pay/walletd/action/adapter"
)

// GetScopeItemGroupMapByID 根据ID获取计费范围-计费项组关系
func (a *WalletAction) GetScopeItemGroupMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeItemGroupMap, error) {
	scopeItemGroupMap, err := a.walletBizSrv.GetScopeItemGroupMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeItemGroupMap(scopeItemGroupMap)
}

// CreateScopeItemGroupMap 创建计费范围-计费项组关系
func (a *WalletAction) CreateScopeItemGroupMap(
	ctx context.Context,
	param *pb.ScopeItemGroupMap,
) (*pb.ScopeItemGroupMap, error) {
	m, err := adapter.BuildScopeItemGroupMap(param)
	if err != nil {
		return nil, err
	}

	scopeItemGroupMap, err := a.walletBizSrv.CreateScopeItemGroupMap(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeItemGroupMap(scopeItemGroupMap)
}

// UpdateScopeItemGroupMapByID 根据ID更新计费范围-计费项组关系
func (a *WalletAction) UpdateScopeItemGroupMapByID(
	ctx context.Context,
	param *pb.IDScopeItemGroupMapParam,
) (*pb.ScopeItemGroupMap, error) {
	m, err := adapter.BuildScopeItemGroupMap(param.GetScopeItemGroupMap())
	if err != nil {
		return nil, err
	}

	scopeItemGroupMap, err := a.walletBizSrv.UpdateScopeItemGroupMapByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeItemGroupMap(scopeItemGroupMap)
}

// DeleteScopeItemGroupMapByID 删除计费范围-计费项组关系
func (a *WalletAction) DeleteScopeItemGroupMapByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeItemGroupMap, error) {
	scopeItemGroupMap, err := a.walletBizSrv.DeleteScopeItemGroupMapByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbScopeItemGroupMap(scopeItemGroupMap)
}

// DeleteScopeItemGroupMapsByScopeID 根据计费范围ID删除所有计费范围-计费项组关系
func (a *WalletAction) DeleteScopeItemGroupMapsByScopeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ScopeItemGroupMapList, error) {
	result, err := a.walletBizSrv.DeleteScopeItemGroupMapByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	count, err := a.walletBizSrv.CountScopeItemGroupMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeItemGroupMapList{
		Count:              count,
		ScopeItemGroupMaps: make([]*pb.ScopeItemGroupMap, len(result)),
	}
	for i, _map := range result {
		m, err := adapter.BuildPbScopeItemGroupMap(&_map)
		if err != nil {
			return nil, err
		}
		list.ScopeItemGroupMaps[i] = m
	}
	return list, nil
}

// ListAllScopeItemGroupMaps 分页列举所有计费范围-计费项组关系记录
func (a *WalletAction) ListAllScopeItemGroupMaps(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.ScopeItemGroupMapList, error) {
	count, err := a.walletBizSrv.CountAllScopeItemGroupMaps(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	scopeItemGroupMaps, err := a.walletBizSrv.ListAllScopeItemGroupMaps(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeItemGroupMapList{
		Count:              count,
		ScopeItemGroupMaps: make([]*pb.ScopeItemGroupMap, len(scopeItemGroupMaps)),
	}
	for i, scopeItemGroupMap := range scopeItemGroupMaps {
		m, err := adapter.BuildPbScopeItemGroupMap(&scopeItemGroupMap)
		if err != nil {
			return nil, err
		}
		list.ScopeItemGroupMaps[i] = m
	}
	return list, nil
}

// CountAllScopeItemGroupMaps 获取所有计费范围-计费项组关系记录数量
func (a *WalletAction) CountAllScopeItemGroupMaps(
	ctx context.Context,
	_ *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountAllScopeItemGroupMaps(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, err
}

// ListScopeItemGroupMapsByGroupID 根据计费项组ID分页列举计费范围-计费项组关系记录
func (a *WalletAction) ListScopeItemGroupMapsByGroupID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ScopeItemGroupMapList, error) {
	count, err := a.walletBizSrv.CountScopeItemGroupMapsByGroupID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	scopeItemGroupMaps, err := a.walletBizSrv.ListScopeItemGroupMapsByGroupID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeItemGroupMapList{
		Count:              count,
		ScopeItemGroupMaps: make([]*pb.ScopeItemGroupMap, len(scopeItemGroupMaps)),
	}
	for i, scopeItemGroupMap := range scopeItemGroupMaps {
		m, err := adapter.BuildPbScopeItemGroupMap(&scopeItemGroupMap)
		if err != nil {
			return nil, err
		}
		list.ScopeItemGroupMaps[i] = m
	}
	return list, nil
}

// CountScopeItemGroupMapsByGroupID 根据计费项组ID分页获取计费范围-计费项组关系记录数量
func (a *WalletAction) CountScopeItemGroupMapsByGroupID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountScopeItemGroupMapsByGroupID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListScopeItemGroupMapsByScopeID 根据计费范围ID分页列举计费范围-计费项组关系记录
func (a *WalletAction) ListScopeItemGroupMapsByScopeID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ScopeItemGroupMapList, error) {
	count, err := a.walletBizSrv.CountScopeItemGroupMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	scopeItemGroupMaps, err := a.walletBizSrv.ListScopeItemGroupMapsByScopeID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ScopeItemGroupMapList{
		Count:              count,
		ScopeItemGroupMaps: make([]*pb.ScopeItemGroupMap, len(scopeItemGroupMaps)),
	}
	for i, scopeItemGroupMap := range scopeItemGroupMaps {
		m, err := adapter.BuildPbScopeItemGroupMap(&scopeItemGroupMap)
		if err != nil {
			return nil, err
		}
		list.ScopeItemGroupMaps[i] = m
	}
	return list, nil
}

// CountScopeItemGroupMapsByScopeID 根据计费范围ID分页获取计费范围-计费项组关系记录数量
func (a *WalletAction) CountScopeItemGroupMapsByScopeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.walletBizSrv.CountScopeItemGroupMapsByScopeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}
