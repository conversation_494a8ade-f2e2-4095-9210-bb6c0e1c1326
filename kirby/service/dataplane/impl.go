package dataplane

import (
	"context"
	"time"

	pb "github.com/qbox/bo-sdk/kirbypb"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/dataplaneModel"
)

type IKirbyDataplane interface {
	// DetachAndWait 通知数据面实现，本服务进程的当前连接即将结束，等待必要的清理工作（如有）完成后返回
	DetachAndWait()

	// RefreshRTDs 通知数据面实现，刷新 RTD 相关状态、缓存等
	RefreshRTDs([]*adminModel.ResourceTypeDefinitionDetail)
	// RefreshAggregationRules 通知数据面实现，刷新聚合规则相关状态、缓存等
	RefreshAggregationRules([]*adminModel.AggregationRule)

	MakeBundleProcessCtx(
		ctx context.Context,
		cm pb.CompressionMethod,
	) (*BundleProcessCtx, error)

	RunAggregations(
		ctx context.Context,
		month time.Time,
		since time.Time,
		epoch time.Time,
	) error

	// ProvisionShards 为一个月份建立分片（建表）
	//
	// 一般在给定月份到来之前，提前调用
	ProvisionShards(
		ctx context.Context,
		month time.Time,
	) error

	ListSeries(
		ctx context.Context,
		req *dataplaneModel.ReqListSeries,
	) (*dataplaneModel.SeriesDetailList, error)

	ListSeriesDistinctUIDs(
		ctx context.Context,
		req *dataplaneModel.ReqListSeries,
	) ([]uint64, error)

	ListAggregatedSeries(
		ctx context.Context,
		req *dataplaneModel.ReqListAggregatedSeries,
	) (*dataplaneModel.AggregatedSeriesDetailList, error)

	ListAggregatedSeriesDistinctUIDs(
		ctx context.Context,
		req *dataplaneModel.ReqListAggregatedSeries,
	) ([]uint64, error)

	QueryAggregatedSeriesData(
		ctx context.Context,
		uid uint64,
		month time.Time,
		itemCode string,
		zoneCode int64,
		dataType string,
		desiredG adminModel.Granularity,
		epoch time.Time,
	) ([]*pb.StatPoint, error)

	ListItemUIDsByMonthAndZone(
		ctx context.Context,
		itemCodes []string,
		month time.Time,
		zoneCode int64,
		epoch time.Time,
	) ([]uint64, error)

	ListUserItemsByMonthAndZone(
		ctx context.Context,
		uid uint64,
		month time.Time,
		zoneCode int64,
		epoch time.Time,
	) ([]string, error)
}
