package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/dictd/model"
)

func TestItemDataTypeAlgorithm(t *testing.T) {
	sandbox := buildSandbox(t)

	dictService := sandbox.dictService

	const dataTypeID uint64 = 1
	data := []model.ItemDataTypeAlgorithm{
		{
			DataTypeID: dataTypeID,
			Code:       "bandwidth:top",
			Name:       "月计费月峰值",
			Script:     "top",
			IsDefault:  true,
		},
		{
			DataTypeID: dataTypeID,
			Code:       "bandwidth:95%",
			Name:       "月计费95峰值",
			Script:     "95%",
			IsDefault:  false,
		},
	}
	assertFields := func(group *model.ItemDataTypeAlgorithm, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(data) }) {
			assert.Equal(t, data[n].DataTypeID, group.DataTypeID, msgAndArgs...)
			assert.Equal(t, data[n].Code, group.Code, msgAndArgs...)
			assert.Equal(t, data[n].Name, group.Name, msgAndArgs...)
			assert.Equal(t, data[n].Script, group.Script, msgAndArgs...)
			assert.Equal(t, data[n].IsDefault, group.IsDefault, msgAndArgs...)
		}
	}
	ctx := context.TODO()
	ids := make(map[uint64]int, len(data))
	for n, d := range data {
		algorithm, err := dictService.CreateItemDataTypeAlgorithm(ctx, &d)
		if assert.NoError(t, err, "CreateItemDataTypeAlgorithm") {
			assert.NotZero(t, algorithm.ID, "CreateItemDataTypeAlgorithm return zero id")
			ids[algorithm.ID] = n
		}

		algorithm, err = dictService.GetItemDataTypeAlgorithmByID(ctx, algorithm.ID)
		if assert.NoError(t, err, "GetItemDataTypeAlgorithmByID") {
			assertFields(algorithm, n, "GetItemDataTypeAlgorithmByID fields not match")
		}

		algorithm.Name += "suffix1"
		data[n].Name += "suffix1"
		algorithm, err = dictService.UpdateItemDataTypeAlgorithmByID(ctx, algorithm.ID, algorithm)
		if assert.NoError(t, err, "UpdateItemDataTypeAlgorithmByID") {
			assert.NotZero(t, algorithm.ID, "UpdateItemDataTypeAlgorithmByID return zero id")
		}
	}

	groups, err := dictService.ListItemDataTypeAlgorithmsByItemDataTypeID(ctx, dataTypeID, 0, 10)
	if assert.NoError(t, err, "ListItemDataTypeAlgorithmsByItemDataTypeID") {
		assert.Len(t, groups, len(data), "ListItemDataTypeAlgorithmsByItemDataTypeID unexpected length")
		count, err := dictService.CountItemDataTypeAlgorithmsByItemDataTypeID(ctx, dataTypeID)
		if assert.NoError(t, err, "CountItemDataTypeAlgorithmsByItemDataTypeID") {
			assert.Equal(t, uint64(len(data)), count, "CountItemDataTypeAlgorithmsByItemDataTypeID count unexpected")
		}
		for _, group := range groups {
			if assert.Contains(t, ids, group.ID) {
				assertFields(&group, ids[group.ID])
			}
		}
	}

	for id, n := range ids {
		group, err := dictService.DeleteItemDataTypeAlgorithmByID(ctx, id)
		if assert.NoError(t, err, "DeleteItemDataTypeAlgorithmByID") {
			assertFields(group, n)
		}
	}

	count, err := dictService.CountItemDataTypeAlgorithmsByItemDataTypeID(ctx, dataTypeID)
	if assert.NoError(t, err, "CountItemDataTypeAlgorithmsByItemDataTypeID") {
		assert.Equal(t, uint64(0), count, "CountItemDataTypeAlgorithmsByItemDataTypeID count after delete all is not zero")
	}
}
