package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/dict"
	"qiniu.io/pay/dictd/action/adapter"
)

// GetItemDataTypeUnitByID 根据ID获取计费项类型单位
func (a *DictAction) GetItemDataTypeUnitByID(
	ctx context.Context,
	itemDataTypeUnitID *pb.IDParam,
) (*pb.ItemDataTypeUnit, error) {
	unit, err := a.dictBizSrv.GetItemDataTypeUnitByID(ctx, itemDataTypeUnitID.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeUnit(unit)
}

// ListItemDataTypeUnitsByItemDataTypeID 根据计费项数据类型ID获取计费项数据类型单位
func (a *DictAction) ListItemDataTypeUnitsByItemDataTypeID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.ItemDataTypeUnitList, error) {
	count, err := a.dictBizSrv.CountItemDataTypeUnitsByItemDataTypeID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	units, err := a.dictBizSrv.ListItemDataTypeUnitsByItemDataTypeID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemDataTypeUnitList{
		Count:             count,
		ItemDataTypeUnits: make([]*pb.ItemDataTypeUnit, len(units)),
	}
	for i, unit := range units {
		u, err := adapter.BuildPbItemDataTypeUnit(&unit)
		if err != nil {
			return nil, err
		}
		list.ItemDataTypeUnits[i] = u
	}
	return list, nil
}

// ListItemDataTypeUnitsByItemAndDataType 根据计费项 code 和数据类型 code 获取计费项数据类型单位
func (a *DictAction) ListItemDataTypeUnitsByItemAndDataType(
	ctx context.Context,
	param *pb.ItemAndDataTypePagingParam,
) (*pb.ItemDataTypeUnitList, error) {
	count, err := a.dictBizSrv.CountItemDataTypeUnitsByItemAndDataType(ctx,
		param.GetItem(), param.GetDataTypeCode())
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(param)
	units, err := a.dictBizSrv.ListItemDataTypeUnitsByItemAndDataType(ctx,
		param.GetItem(), param.GetDataTypeCode(), offset, limit)
	if err != nil {
		return nil, err
	}
	list := &pb.ItemDataTypeUnitList{
		Count:             count,
		ItemDataTypeUnits: make([]*pb.ItemDataTypeUnit, len(units)),
	}
	for i, unit := range units {
		u, err := adapter.BuildPbItemDataTypeUnit(&unit)
		if err != nil {
			return nil, err
		}
		list.ItemDataTypeUnits[i] = u
	}
	return list, nil
}

// CountItemDataTypeUnitsByItemDataTypeID 根据计费项数据类型ID获取计费项数据类型单位数量
func (a *DictAction) CountItemDataTypeUnitsByItemDataTypeID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemDataTypeUnitsByItemDataTypeID(ctx, param.GetId())
	return &pb.CountParam{Count: count}, err
}

// CountItemDataTypeUnitsByItemAndDataType 根据计费项 code 和数据类型 code 获取计费项数据类型单位数量
func (a *DictAction) CountItemDataTypeUnitsByItemAndDataType(
	ctx context.Context,
	param *pb.ItemAndDataTypeParam,
) (*pb.CountParam, error) {
	count, err := a.dictBizSrv.CountItemDataTypeUnitsByItemAndDataType(ctx,
		param.GetItem(), param.GetDataTypeCode())
	return &pb.CountParam{Count: count}, err
}

// CreateItemDataTypeUnit 创建计费项数据类型单位
func (a *DictAction) CreateItemDataTypeUnit(
	ctx context.Context,
	unit *pb.ItemDataTypeUnit,
) (*pb.ItemDataTypeUnit, error) {
	m, err := adapter.BuildItemDataTypeUnit(unit)
	if err != nil {
		return nil, err
	}

	u, err := a.dictBizSrv.CreateItemDataTypeUnit(ctx, m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeUnit(u)
}

// UpdateItemDataTypeUnitByID 根据ID更新计费项数据类型单位
func (a *DictAction) UpdateItemDataTypeUnitByID(
	ctx context.Context,
	param *pb.IDItemDataTypeUnitParam,
) (*pb.ItemDataTypeUnit, error) {
	unit := param.GetItemDataTypeUnit()
	m, err := adapter.BuildItemDataTypeUnit(unit)
	if err != nil {
		return nil, err
	}

	u, err := a.dictBizSrv.UpdateItemDataTypeUnitByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeUnit(u)
}

// DeleteItemDataTypeUnitByID 根据ID删除计费项数据类型单位
func (a *DictAction) DeleteItemDataTypeUnitByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.ItemDataTypeUnit, error) {
	unit, err := a.dictBizSrv.DeleteItemDataTypeUnitByID(ctx, param.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbItemDataTypeUnit(unit)
}
