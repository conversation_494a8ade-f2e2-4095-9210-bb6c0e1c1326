package doraItemsQuerier

import (
	"errors"
	"fmt"

	"github.com/qbox/bo-base/v4/legacystat/common"

	"qiniu.io/pay/measured/model"
)

var errNoItemMetadata = errors.New("no item metadata")
var errNoStatKey = errors.New("no stat_key ItemExtraArg for item")
var errNoStatQuery = errors.New("no stat_query ItemExtraArg for item")

// itemCmdsMap 计费项到 cmd 列表的索引
type itemCmdsMap map[itemEntry][]common.LegacyDoraCmd

// doraCmdsItemsDict 从 Dora cmd key 映射到包含该 cmd 的计费项的索引
type doraCmdsItemsDict map[string][]itemEntry

func keyFromPbLegacyDoraCmd(x *common.LegacyDoraCmd) string {
	return fmt.Sprintf("%s:%s", x.Key, x.Cmd)
}

func keysFromDoraCmdsInUseResp(cmds []common.LegacyDoraCmd) []string {
	if len(cmds) == 0 {
		return nil
	}

	result := make([]string, len(cmds))
	for i, cmd := range cmds {
		result[i] = keyFromPbLegacyDoraCmd(&cmd)
	}

	return result
}

func itemCmdsMapFromItemExtraArgs(
	args []model.ItemExtraArg,
	itemMetadataDict map[uint64]itemEntry,
) (itemCmdsMap, error) {
	if len(args) == 0 {
		return itemCmdsMap{}, nil
	}

	// distinct itemIDs
	var itemIDs []uint64
	{
		seenItemIDs := make(map[uint64]struct{})
		for _, arg := range args {
			if _, ok := seenItemIDs[arg.ItemID]; ok {
				continue
			}
			seenItemIDs[arg.ItemID] = struct{}{}
			itemIDs = append(itemIDs, arg.ItemID)
		}
	}

	// args.filter(|x| x.key == "stat_key")
	statKeyMap := make(map[uint64]string)
	for _, arg := range args {
		if arg.Key != "stat_key" {
			continue
		}
		statKeyMap[arg.ItemID] = arg.Value
	}

	// args.filter(|x| x.key == "stat_query")
	statQueryMap := make(map[uint64]string)
	for _, arg := range args {
		if arg.Key != "stat_query" {
			continue
		}
		statQueryMap[arg.ItemID] = arg.Value
	}

	result := make(itemCmdsMap)
	for _, itemID := range itemIDs {
		itemMetadata, ok := itemMetadataDict[itemID]
		if !ok {
			// 缺 item 元数据
			return nil, errNoItemMetadata
		}

		statKey, ok := statKeyMap[itemID]
		if !ok {
			// 缺 stat_key
			return nil, errNoStatKey
		}

		statQuery, ok := statQueryMap[itemID]
		if !ok {
			// 缺 stat_query
			return nil, errNoStatQuery
		}

		parsedCmds, err := parseCmdsFromStatParams(statKey, statQuery)
		if err != nil {
			return nil, err
		}

		result[itemMetadata] = parsedCmds
	}

	return result, nil
}

func doraCmdsItemsDictFromItemCmdsMap(m itemCmdsMap) doraCmdsItemsDict {
	result := make(doraCmdsItemsDict)
	for entry, cmds := range m {
		for _, cmd := range cmds {
			key := keyFromPbLegacyDoraCmd(&cmd)
			// 零值语义
			result[key] = append(result[key], entry)
		}
	}
	return result
}
