package adapter

import (
	pb "github.com/qbox/pay-sdk/kirby"

	"qiniu.io/pay/kirby/model/dataplaneModel"
)

func buildSPCondition(in *pb.SPCondition) *dataplaneModel.SPCondition {
	return &dataplaneModel.SPCondition{
		Key:   in.Key,
		SevIn: in.SevIn,
	}
}

func buildSPConditions(in []*pb.SPCondition) []*dataplaneModel.SPCondition {
	if in == nil {
		return nil
	}

	y := make([]*dataplaneModel.SPCondition, len(in))
	for i, x := range in {
		y[i] = buildSPCondition(x)
	}
	return y
}
