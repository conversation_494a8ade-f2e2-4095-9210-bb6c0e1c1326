package dataplane

import (
	"bufio"
	"errors"
	"io"

	"google.golang.org/protobuf/encoding/protowire"
)

type vtMessageSink interface {
	UnmarshalVT([]byte) error
}

type pbStreamDecoder[M vtMessageSink] struct {
	src          *bufio.Reader
	attentionTag uint64
}

func newPbStreamDecoder[M vtMessageSink](r io.Reader, attentionFieldNum uint64) *pbStreamDecoder[M] {
	attentionTag := protowire.EncodeTag(protowire.Number(attentionFieldNum), protowire.BytesType)

	return &pbStreamDecoder[M]{
		src:          bufio.NewReader(r),
		attentionTag: attentionTag,
	}
}

func (d *pbStreamDecoder[M]) readOneInto(dst M) error {
	// read until an interesting TLV is encountered
	for {
		tag, err := d.readVarint()
		if err != nil {
			return err
		}

		if tag == d.attentionTag {
			break
		}

		err = d.skip(tag)
		if err != nil {
			return err
		}
	}

	buf, err := d.readBytes()
	if err != nil {
		return err
	}

	return dst.UnmarshalVT(buf)
}

func (d *pbStreamDecoder[_]) readVarint() (uint64, error) {
	// at most 10 bytes for uint64
	var tmp [10]byte
	i := 0

	// consume one byte at a time until MSB is clear
	// see protobuf encoding guide for details
	for {
		b, err := d.src.ReadByte()
		if err != nil {
			return 0, err
		}
		tmp[i] = b
		i++
		if b < 0x80 {
			break
		}
	}

	res, n := protowire.ConsumeVarint(tmp[:i])
	if n < 0 {
		return 0, protowire.ParseError(n)
	}
	return res, nil
}

func (d *pbStreamDecoder[_]) readBytes() ([]byte, error) {
	// read len
	l, err := d.readVarint()
	if err != nil {
		return nil, err
	}

	// read value
	b := make([]byte, int(l))
	_, err = io.ReadFull(d.src, b)
	if err != nil {
		return nil, err
	}

	return b, nil
}

func (d *pbStreamDecoder[_]) skip(tag uint64) error {
	_, wireType := protowire.DecodeTag(tag)
	switch wireType {
	case protowire.VarintType:
		_, err := d.readVarint()
		return err

	case protowire.Fixed32Type:
		_, err := d.src.Discard(4)
		return err

	case protowire.Fixed64Type:
		_, err := d.src.Discard(8)
		return err

	case protowire.BytesType:
		n, err := d.readVarint()
		if err != nil {
			return err
		}
		_, err = d.src.Discard(int(n))
		return err

	case protowire.StartGroupType, protowire.EndGroupType:
		// XXX: no support for groups, but we can't simply do nothing
		// as the TLVs inside the group may be interpreted differently
		//
		// instead, just error out to avoid having to do a similar dance
		// as in protowire's impl
		return errors.New("pb groups are not supported")

	default:
		// unrecognized pb wire type, should never happen
		return errors.New("unrecognized pb wire type")
	}
}
