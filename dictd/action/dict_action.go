package action

import (
	"gopkg.in/go-playground/validator.v9"

	"github.com/qbox/bo-base/v4/action"
	pb "github.com/qbox/pay-sdk/dict"

	"qiniu.io/pay/dictd/service"
)

// DictAction implements the server definition in protobuffer
type DictAction struct {
	*action.BaseAction
	pb.UnimplementedPayDictServiceServer

	dictBizSrv      *service.DictBizService
	defaultPageSize uint64
	validate        *validator.Validate
}

// NewDictAction is constructor of DictAction
func NewDictAction(dictBizSrv *service.DictBizService, defaultPageSize uint64) *DictAction {
	return &DictAction{
		BaseAction:      action.NewBaseAction(defaultPageSize),
		dictBizSrv:      dictBizSrv,
		defaultPageSize: defaultPageSize,
		validate:        validator.New(),
	}
}
