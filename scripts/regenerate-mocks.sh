#!/bin/bash

TOP="$(cd "$(dirname "${BASH_SOURCE[0]}")"/.. && pwd)"
cd "$TOP"

is_gnu_sed=false
_detect_gnu_sed () {
    local sed_version="$(sed --version 2>&1)"
    if [[ $? -eq 0 ]]; then
        echo "$sed_version" | grep 'GNU sed' > /dev/null 2>&1 || return
        is_gnu_sed=true
    fi
}
_detect_gnu_sed

sed_inplace () {
    if "$is_gnu_sed"; then
        sed -i "$@"
    else
        sed -i '' "$@"
    fi
}

parse_my_module_prefix () {
    grep '^module ' "$TOP/go.mod" | cut -f2 -d' '
}

MODULE_PREFIX="$(parse_my_module_prefix)"

# parses Go package name out of a Go file.
#
# use "-" for reading from stdin
go_package_name () {
    local path="$1"

    grep '^package ' "$path" | cut -f2 -d' '
}

remove_package_doc_line () {
    local filename="$1"

    sed_inplace '/^\/\/ Package .* is a generated GoMock package\.$/d' "$filename"
}

process_file () {
    local input_path="$1" # e.g. app/xxx/yyy/zzz.go
    local go_import_path
    local package_name
    local output_path
    local output

    # app/xxx/yyy/zzz.mockgen.go
    output_path="${input_path%.go}.mockgen.go"

    # qiniu.io/gaea/app/xxx/yyy
    go_import_path="$MODULE_PREFIX/$(dirname "$input_path")"

    # parse package name
    package_name="$(go_package_name "$input_path")"

    args=(
        -source "$input_path"
        -package "$package_name"
        -self_package "$go_import_path"
        -destination "$output_path"
    )

    echo "$0: mockgen ${args[@]}"
    mockgen "${args[@]}"
    remove_package_doc_line "$output_path"
}

process_file_reflect_mode () {
    local package="$1"      # e.g. app/services/gaea
    local package_name="$2" # e.g. gaea
    local types="$3"        # e.g. GaeaAdminService
    local full_import_path
    local output_path

    full_import_path="$MODULE_PREFIX/$package"
    output_path="$package/mocks.mockgen.go"

    args=(
        -destination "$output_path"
        -package "$package_name"
        -self_package "$full_import_path"
        "$full_import_path"
        "$types"
    )

    echo "$0: mockgen ${args[@]}"
    mockgen "${args[@]}"
    remove_package_doc_line "$output_path"
}

process_external_type_reflect_mode () {
    local package="$1"      # e.g. qbox.us/biz/api/gaeaadmin
    local package_name="$2" # e.g. gaeaadmin
    local types="$3"        # e.g. GaeaAdminService
    local full_import_path
    local output_path

    full_import_path="$MODULE_PREFIX/mocks/$package"
    output_path="mocks/$package/mocks.mockgen.go"

    args=(
        -destination "$output_path"
        -package "$package_name"
        -self_package "$full_import_path"
        "$package"
        "$types"
    )

    echo "$0: mockgen ${args[@]}"
    mockgen "${args[@]}"
    remove_package_doc_line "$output_path"
}

atexit_hook () {
    echo "$0: received exit signal, waiting for running tasks to complete..."
    wait
    echo "$0: exiting"
}

main () {
    local i=0

    # cleanup existing mock files
    rm -rf mocks
    for dir in "$@"; do
        find "$dir" -name '*.mockgen.go' -delete
    done

    trap atexit_hook EXIT

    # https://stackoverflow.com/questions/8677546/reading-null-delimited-strings-through-a-bash-loop
    while IFS= read -r -d $'\0' file; do
        process_file "$file" &
        : "$((i++))"
        if [[ "$((i % 4))" -eq 0 ]]; then
            wait
        fi
    done < <(rg -0 -l -g '**/*.go' -g '!app/mock/' -g "!$OUTPUT_DIR_BASE/" '^type [A-Za-z][A-Za-z0-9_]* interface' "$@")

    wait

    trap - EXIT

    # these types below require reflect mode to mock
    # currently none in this repo

    # mock some external types needed by us
    # currently none in this repo
    # example: process_external_type_reflect_mode qbox.us/biz/api/gaeaadmin gaeaadmin GaeaAdminService
}

packages=(
    coupon
    creditd
    dictd
    i18n
    jm
    kirby
    measured
    measureproxyd
    priced
    qpay
    qvmprice
    walletd
    pland
    dw
)

main "${packages[@]}"
