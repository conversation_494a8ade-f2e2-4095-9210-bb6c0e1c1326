package action

import (
	"context"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/price"
	"qiniu.io/pay/priced/action/adapter"
)

// GetPriceItemStairByID 根据 ID 获取阶梯价格
func (a *PriceAction) GetPriceItemStairByID(
	ctx context.Context,
	stairID *pb.IDParam,
) (*pb.PriceItemStair, error) {
	stair, err := a.priceBizSrv.GetPriceItemStairByID(ctx, stairID.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceItemStair(stair)
}

// CreatePriceItemStair 创建阶梯价格
func (a *PriceAction) CreatePriceItemStair(
	ctx context.Context,
	stair *pb.PriceItemStair,
) (*pb.PriceItemStair, error) {
	m, err := adapter.BuildPriceItemStair(stair)
	if err != nil {
		return nil, errors.Trace(err)
	}

	stairModel, err := a.priceBizSrv.CreatePriceItemStair(ctx, m)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceItemStair(stairModel)
}

// UpdatePriceItemStairByID 根据 ID 更新阶梯价格
func (a *PriceAction) UpdatePriceItemStairByID(
	ctx context.Context,
	param *pb.IDPriceItemStairParam,
) (*pb.PriceItemStair, error) {
	stair := param.GetStair()
	m, err := adapter.BuildPriceItemStair(stair)
	if err != nil {
		return nil, errors.Trace(err)
	}

	stairModel, err := a.priceBizSrv.UpdatePriceItemStairByID(ctx, param.GetId(), m)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return adapter.BuildPbPriceItemStair(stairModel)
}

// DeletePriceItemStairByID 根据 ID 删除阶梯价格
func (a *PriceAction) DeletePriceItemStairByID(
	ctx context.Context,
	param *pb.IDParam,
) (*pb.PriceItemStair, error) {
	stairModel, err := a.priceBizSrv.DeletePriceItemStairByID(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}

	return adapter.BuildPbPriceItemStair(stairModel)
}

// ListAllPriceItemStairs 查询所有的阶梯价格
func (a *PriceAction) ListAllPriceItemStairs(
	ctx context.Context,
	param *pb.PagingParam,
) (*pb.PriceItemStairList, error) {
	count, err := a.priceBizSrv.CountAllPriceItemStairs(ctx)
	if err != nil {
		return nil, errors.Trace(err)
	}
	offset, limit := a.Paging(param)
	stairModels, err := a.priceBizSrv.ListAllPriceItemStairs(ctx, offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}
	list := &pb.PriceItemStairList{
		Count:           count,
		PriceItemStairs: make([]*pb.PriceItemStair, len(stairModels)),
	}
	for i, stairModel := range stairModels {
		stair, err := adapter.BuildPbPriceItemStair(&stairModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceItemStairs[i] = stair
	}
	return list, nil
}

// ListAllPriceItemStairsByPriceItemID 根据基础价格 ID 查询所有的阶梯价格
func (a *PriceAction) ListAllPriceItemStairsByPriceItemID(
	ctx context.Context,
	param *pb.IDPagingParam,
) (*pb.PriceItemStairList, error) {
	count, err := a.priceBizSrv.CountAllPriceItemStairsByPriceID(ctx, param.GetId())
	if err != nil {
		return nil, errors.Trace(err)
	}
	offset, limit := a.Paging(param)
	stairModels, err := a.priceBizSrv.ListAllPriceItemStairsByPriceItemID(ctx, param.GetId(), offset, limit)
	if err != nil {
		return nil, errors.Trace(err)
	}
	list := &pb.PriceItemStairList{
		Count:           count,
		PriceItemStairs: make([]*pb.PriceItemStair, len(stairModels)),
	}
	for i, stairModel := range stairModels {
		stair, err := adapter.BuildPbPriceItemStair(&stairModel)
		if err != nil {
			return nil, errors.Trace(err)
		}
		list.PriceItemStairs[i] = stair
	}
	return list, nil
}
