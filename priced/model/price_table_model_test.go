package model_test

import (
	"testing"

	basedao "github.com/qbox/bo-base/v4/dao"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/priced/model"
)

func TestModelPriceTableCRUD(t *testing.T) {
	sandbox := buildSandbox(t)

	dao := sandbox.priceDao.PriceTable

	priceTables := []model.PriceTable{
		{
			Type:   model.PriceTableTypeDefault,
			Remark: "公开报价",
		},
		{
			Type:   model.PriceTableTypeVIP,
			Remark: "VIP 价格1",
		},
		{
			Type:   model.PriceTableTypeFinal,
			Remark: "最终价格信息",
		},
		{
			Type:   model.PriceTableTypeRefCost,
			Remark: "参考成本价格",
		},
	}

	assertFields := func(expect, actual *model.PriceTable, msgAndArgs ...any) {
		assert.Equal(t, expect.Type, actual.Type, msgAndArgs...)
		assert.Equal(t, expect.Remark, actual.Remark, msgAndArgs...)
	}

	for i, priceTable := range priceTables {
		// create
		err := dao.Save(&priceTable)
		if assert.NoError(t, err) {
			assert.NotZero(t, priceTable.ID, "CreatePriceTable return zero id")
		}
		priceTables[i].ID = priceTable.ID

		// query
		priceTableID := priceTable.ID
		m, err := dao.GetByID(priceTableID)
		if assert.NoError(t, err) {
			assertFields(&priceTable, m, "Unmatched priceTable")
		}
	}

	// list all price tables
	priceTableList, err := dao.List(0, 10)
	if assert.NoError(t, err) {
		assert.Len(t, priceTableList, len(priceTables))
	}

	// delete all price tables
	for _, priceTable := range priceTables {
		err := dao.DeleteByID(priceTable.ID)
		assert.NoError(t, err)
	}

	// check price table list, should be empty
	l, err := dao.List(0, 10, basedao.CacheExpiresNoCache)
	if assert.NoError(t, err) {
		assert.Empty(t, l)
	}

	// de-duplication
	for _, priceTable := range priceTables {
		err := dao.DeleteByID(priceTable.ID)
		assert.Error(t, err)
	}
}

func TestEnablePriceTable(t *testing.T) {
	sandbox := buildSandbox(t)

	dao := sandbox.priceDao.PriceTable

	priceTables := []model.PriceTable{
		{
			IsDisabled: true,
		},
		{
			IsDisabled: false,
		},
	}

	for i, priceTable := range priceTables {
		// create
		err := dao.Save(&priceTable)
		assert.NoError(t, err)
		priceTables[i].ID = priceTable.ID

		err = dao.EnablePriceTable(priceTable.ID)
		assert.NoError(t, err)

		// 目前写入操作会同步清理缓存了，不需要等待，直接查询
		m, err := dao.GetByID(priceTable.ID)
		assert.NoError(t, err)

		assert.False(t, m.IsDisabled)
	}
}
