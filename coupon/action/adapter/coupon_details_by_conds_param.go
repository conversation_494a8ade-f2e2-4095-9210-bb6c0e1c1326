package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/model"
)

// BuildListCouponDetailsByCondsParam converts pb.CouponDetailsByCondsParam to the model layer type.
func BuildListCouponDetailsByCondsParam(
	in *pb.CouponDetailsByCondsParam,
) (*model.ListCouponDetailsByCondsParam, error) {
	return &model.ListCouponDetailsByCondsParam{
		ListCouponsByCondsParam: model.ListCouponsByCondsParam{
			CouponIDs:   nil, // unexposed for now
			CouponCodes: in.CouponCodes,
			UID:         in.Uid,
			BatchID:     0, // unexposed for now
			States:      BuildCouponStates(in.State),
		},
		PayModeScopes: BuildCouponPayModes(in.PayMode),
	}, nil
}
