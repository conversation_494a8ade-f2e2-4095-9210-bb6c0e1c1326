package model

import (
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// ScopeItemMap model definition
type ScopeItemMap struct {
	ID         uint64 `gorm:"primary_key"`
	ScopeID    uint64 `gorm:"unique_index:idx_scope_item_map_scope_id_item_id"`
	ItemID     uint64 `gorm:"unique_index:idx_scope_item_map_scope_id_item_id"`
	ItemCode   string `gorm:"-"`
	IsExcluded bool
	CreatedAt  time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt  time.Time `sql:"type:DATETIME(6)"`
}

// TableName return table name of ScopeItemMap
func (*ScopeItemMap) TableName() string {
	return "scope_item_map"
}

// ScopeItemMapDao data access object of ScopeItemMap model
type ScopeItemMapDao struct {
	base *dao.BaseDao
}

// NewScopeItemMapDao constructor of ScopeItemMapDao
func NewScopeItemMapDao(base *dao.BaseDao) *ScopeItemMapDao {
	return &ScopeItemMapDao{
		base: base,
	}
}

// GetByID select a record by id
func (d *ScopeItemMapDao) GetByID(
	id uint64,
	expires ...time.Duration,
) (*ScopeItemMap, error) {
	model := &ScopeItemMap{}
	err := d.base.Where(&ScopeItemMap{ID: id}).
		First(model).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// ListAll select all records
func (d *ScopeItemMapDao) ListAll(
	offset int,
	limit int,
	expires ...time.Duration,
) (list []ScopeItemMap, err error) {
	err = d.base.Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"offset": offset,
			"limit":  limit,
		})
	}
	return
}

// CountAll select count of all records
func (d *ScopeItemMapDao) CountAll(
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&ScopeItemMap{}).
		Count(&count).
		Error
	return
}

// ListByItemID select records by item_id
func (d *ScopeItemMapDao) ListByItemID(
	itemID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (list []ScopeItemMap, err error) {
	err = d.base.Where(&ScopeItemMap{ItemID: itemID}).
		Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"item_id": itemID,
			"offset":  offset,
			"limit":   limit,
		})
	}
	return
}

// CountByItemID select count of records by item_id
func (d *ScopeItemMapDao) CountByItemID(
	itemID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&ScopeItemMap{}).
		Where(&ScopeItemMap{ItemID: itemID}).
		Count(&count).
		Error
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"item_id": itemID,
		})
	}
	return
}

// ListByScopeID select records by scope_id
func (d *ScopeItemMapDao) ListByScopeID(
	scopeID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (list []ScopeItemMap, err error) {
	err = d.base.Where(&ScopeItemMap{ScopeID: scopeID}).
		Offset(offset).
		Limit(limit).
		Find(&list).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return
}

// CountByScopeID select count of records by scope_id
func (d *ScopeItemMapDao) CountByScopeID(
	scopeID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	err = d.base.Model(&ScopeItemMap{}).
		Where(&ScopeItemMap{ScopeID: scopeID}).
		Count(&count).
		Error
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"scope_id": scopeID,
		})
	}
	return
}

// Save insert or update a record
func (d *ScopeItemMapDao) Save(
	model *ScopeItemMap,
	expires ...time.Duration,
) error {
	return d.base.Save(model).Error
}

// Delete delete a record
func (d *ScopeItemMapDao) Delete(
	model *ScopeItemMap,
) error {
	return d.base.Delete(model).Error
}
