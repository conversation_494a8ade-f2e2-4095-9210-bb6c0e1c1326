package dataplane

import (
	"errors"
	"sync"

	"qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/kirby/model/qrn"
)

var errNoRTDMatch = errors.New("no RTD match")

// e.g. qrn:kodo:z1:1382293843:bucket/my_bucket
// 首先按 QRN 格式解析: product="kodo" region="z1" uid=1382293843 ident=bucket/my_bucket
// 然后交由 QRN matcher 处理

type rtdMatcher struct {
	mu sync.RWMutex

	// map[productCode][]matcherEntry
	cachedMatchersByProduct map[string][]matcherEntry
}

type matcherEntry struct {
	expr *qrn.ParsedMatchExpr
	rtdd *adminModel.ResourceTypeDefinitionDetail
}

func newRTDMatcher() *rtdMatcher {
	return &rtdMatcher{
		mu:                      sync.RWMutex{},
		cachedMatchersByProduct: make(map[string][]matcherEntry),
	}
}

func (m *rtdMatcher) refreshCachedDefs(rtdDetails []*adminModel.ResourceTypeDefinitionDetail) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.cachedMatchersByProduct = make(map[string][]matcherEntry)
	for _, rtdd := range rtdDetails {
		productCodes, entries, err := makeMatcherEntry(rtdd)
		if err != nil {
			return err
		}

		for _, productCode := range productCodes {
			m.cachedMatchersByProduct[productCode] = append(
				m.cachedMatchersByProduct[productCode],
				entries...,
			)
		}
	}

	return nil
}

func makeMatcherEntry(
	rtdd *adminModel.ResourceTypeDefinitionDetail,
) (productCodes []string, entries []matcherEntry, err error) {
	for _, matchExprStr := range rtdd.QRNMatches {
		matchExpr, err := matchExprStr.Parse()
		if err != nil {
			return nil, nil, err
		}

		productCodes = append(productCodes, matchExpr.GetProductMatchExact())
		entries = append(entries, matcherEntry{
			expr: matchExpr,
			rtdd: rtdd,
		})
	}

	return productCodes, entries, nil
}

func (m *rtdMatcher) queryRTDForQRN(q *qrn.QRN) (*adminModel.ResourceTypeDefinitionDetail, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	productMatchers := m.cachedMatchersByProduct[q.Product]
	for _, e := range productMatchers {
		if e.expr.Matches(q) {
			return e.rtdd, nil
		}
	}

	return nil, errNoRTDMatch
}
