package action

import (
	"context"
	"errors"
	"io"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"

	pb "github.com/qbox/bo-sdk/kirbypb"
	"github.com/qbox/pay-sdk/middleware/logging"

	"qiniu.io/pay/kirby/service/dataplane"
)

var errInvalidCompressionMethod = status.Error(codes.InvalidArgument, "invalid compression method")
var errInconsistentCompressionMethod = status.Error(codes.InvalidArgument, "inconsistent compression method")

// IngestBundle 推入一包序列
//
// 由于 gRPC 单个 message 大小的限制，生产环境尽量使用 StreamBundle API。
func (a *KirbyAction) IngestBundle(
	ctx context.Context,
	req *pb.ReqIngestBundle,
) (*emptypb.Empty, error) {
	if req.Cm == pb.CompressionMethod_COMPRESSION_METHOD_UNKNOWN {
		// 这个字段必须指定
		return nil, errInvalidCompressionMethod
	}

	bp, err := a.srv.DP.MakeBundleProcessCtx(ctx, req.Cm)
	if err != nil {
		return nil, err
	}
	defer bp.Finalize()

	bp.Feed(req.Payload)
	bp.MarkEOF()
	bp.StartConsuming(ctx)
	bp.Finalize()

	return &emptypb.Empty{}, nil
}

// StreamBundle stream 入一包序列
//
// 所用的压缩算法由推入的第一个 ReqIngestBundle 确定
func (a *KirbyAction) StreamBundle(s pb.KirbyService_StreamBundleServer) error {
	ctx := s.Context()
	l := logging.GetLogger(ctx)

	var cm pb.CompressionMethod
	var bp *dataplane.BundleProcessCtx
	for {
		chunk, err := s.Recv()
		if err != nil {
			bp.MarkEOF()

			if errors.Is(err, io.EOF) {
				break
			}

			// 真错误
			l.WithError(err).Error("recv bundle chunk failed")
			bp.Finalize()
			return err
		}

		// 第一包 ReqIngestBundle 必须带一个合法的压缩算法
		// 因此可以用 cm 是否为零值来代表当前 chunk 是否为第一个 chunk
		if cm == pb.CompressionMethod_COMPRESSION_METHOD_UNKNOWN {
			// 第一包
			if chunk.Cm == pb.CompressionMethod_COMPRESSION_METHOD_UNKNOWN {
				// 这个字段必须指定
				return errInvalidCompressionMethod
			}
			cm = chunk.Cm
		} else {
			// 之后的包，cm 要么不传（零值），要么需要和第一包保持一致
			// 否则很难清楚定义这种中途变换 cm 的语义
			if !(chunk.Cm == pb.CompressionMethod_COMPRESSION_METHOD_UNKNOWN || chunk.Cm == cm) {
				return errInconsistentCompressionMethod
			}
		}

		// stream into dataplane
		if bp == nil {
			bp, err = a.srv.DP.MakeBundleProcessCtx(ctx, cm)
			if err != nil {
				return err
			}
			bp.StartConsuming(ctx)
		}
		bp.Feed(chunk.Payload)
	}
	bp.Finalize()

	return s.SendAndClose(&emptypb.Empty{})
}
