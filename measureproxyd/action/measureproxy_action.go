package action

import (
	"gopkg.in/go-playground/validator.v9"

	"github.com/qbox/bo-base/v4/action"
	pb "github.com/qbox/pay-sdk/measureproxy"

	"qiniu.io/pay/measureproxyd/service"
)

// MeasureProxyAction is implementation of the measureproxyd gRPC interface.
type MeasureProxyAction struct {
	*action.BaseAction
	pb.UnimplementedPayMeasureProxyServiceServer

	srv             *service.MeasureProxyBizService
	resourceTagsSrv *service.ResourceTagsService
	validate        *validator.Validate
}

// NewMeasureProxyAction constructs a MeasureProxyAction.
func NewMeasureProxyAction(srv *service.MeasureProxyBizService, resourceTagsSrv *service.ResourceTagsService) *MeasureProxyAction {
	return &MeasureProxyAction{
		BaseAction:      action.NewBaseAction(0),
		srv:             srv,
		resourceTagsSrv: resourceTagsSrv,
		validate:        validator.New(),
	}
}
