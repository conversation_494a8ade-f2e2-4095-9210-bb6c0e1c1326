package adapter

import (
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/model"
)

// BuildPbStatRespV1 converts from model.StatResp to the protobuf type.
func BuildPbStatRespV1(x *model.StatResp) (*pb.StatRespV1, error) {
	data := make([]*pb.StatPointV1, len(x.Data))
	for i, p := range x.Data {
		point, err := BuildPbStatPointV1(p)
		if err != nil {
			return nil, err
		}
		data[i] = point
	}
	return &pb.StatRespV1{
		Data: data,
	}, nil
}

// BuildModelStatRespV1 converts from pb.StatRespV1 to the model layer type.
func BuildModelStatRespV1(x *pb.StatRespV1) (*model.StatResp, error) {
	data := make([]model.StatPoint, len(x.Data))
	for i, p := range x.Data {
		point, err := BuildModelStatPointV1(p)
		if err != nil {
			return nil, err
		}
		data[i] = point
	}

	return &model.StatResp{
		Data: data,
	}, nil
}
