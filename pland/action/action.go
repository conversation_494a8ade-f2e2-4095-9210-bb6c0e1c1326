package action

import (
	"gopkg.in/go-playground/validator.v9"

	"github.com/qbox/bo-base/v4/action"
	pb "github.com/qbox/pay-sdk/plan"

	"qiniu.io/pay/pland/config"
	"qiniu.io/pay/pland/service"
)

// PlanAction is implementation of the pland gRPC interface.
type PlanAction struct {
	*action.BaseAction

	srv      *service.PlanService
	validate *validator.Validate
	cfg      *config.PlandConfig
}

var _ pb.PlanServiceServer = (*PlanAction)(nil)

// NewPlanAction constructs a PlanAction.
func NewPlanAction(
	srv *service.PlanService,
	conf *config.PlandConfig,
) *PlanAction {
	return &PlanAction{
		BaseAction: action.NewBaseAction(
			conf.Plan.DefaultPageSize,
		),
		srv:      srv,
		validate: validator.New(),
		cfg:      conf,
	}
}
