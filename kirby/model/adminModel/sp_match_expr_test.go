package adminModel

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSPMatchExprIsMatch(t *testing.T) {
	t.<PERSON>lle<PERSON>()

	t.Run("Nil", func(t *testing.T) {
		t.<PERSON>llel()

		spm := SPMatchExpr(nil)
		assert.False(t, spm.IsMatch(nil))
		assert.False(t, spm.IsMatch(map[string]string{}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": "yy"}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": "yy", "bar": "123"}))
	})

	t.Run("MatchesAll", func(t *testing.T) {
		t.Parallel()

		spm := SPMatchExpr{}
		assert.False(t, spm.IsMatch(nil))
		assert.True(t, spm.IsMatch(map[string]string{}))
		assert.True(t, spm.IsMatch(map[string]string{"foo": "yy"}))
		assert.True(t, spm.IsMatch(map[string]string{"foo": "yy", "bar": "123"}))
	})

	t.Run("EmptyContainsClause", func(t *testing.T) {
		t.Parallel()

		spm := SPMatchExpr{
			"foo": {StringEnumValueIn: []string{}},
		}
		assert.False(t, spm.IsMatch(map[string]string{"foo": "xx"}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": "yy"}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": "yy", "bar": "123"}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": ""}))
		assert.False(t, spm.IsMatch(map[string]string{"bar": "xx"}))
	})

	t.Run("Contains", func(t *testing.T) {
		t.Parallel()

		spm := SPMatchExpr{
			"foo": {StringEnumValueIn: []string{"xx", "yy"}},
		}
		assert.True(t, spm.IsMatch(map[string]string{"foo": "xx"}))
		assert.True(t, spm.IsMatch(map[string]string{"foo": "yy"}))
		assert.True(t, spm.IsMatch(map[string]string{"foo": "yy", "bar": "123"}))
		assert.False(t, spm.IsMatch(nil))
		assert.False(t, spm.IsMatch(map[string]string{}))
		assert.False(t, spm.IsMatch(map[string]string{"": ""}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": ""}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": "zz"}))
		assert.False(t, spm.IsMatch(map[string]string{"bar": "xx"}))
	})

	t.Run("Compound", func(t *testing.T) {
		t.Parallel()

		spm := SPMatchExpr{
			"foo": {StringEnumValueIn: []string{"xx", "yy"}},
			"bar": {StringEnumValueIn: []string{"123"}},
		}
		assert.True(t, spm.IsMatch(map[string]string{"foo": "xx", "bar": "123"}))
		assert.True(t, spm.IsMatch(map[string]string{"foo": "yy", "bar": "123"}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": "xx"}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": "yy"}))
		assert.False(t, spm.IsMatch(map[string]string{"bar": "123"}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": "", "bar": "123"}))
		assert.False(t, spm.IsMatch(map[string]string{"foo": "xx", "bar": "234"}))
	})
}
