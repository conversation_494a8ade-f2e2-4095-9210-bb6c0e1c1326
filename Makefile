# builder: build the Go builder image for dist
# e.g. make builder [GO_VERSION=go1.20]
#
# dist: build package for deploy with one of the pre-built builder images
# e.g. make dist [GO_VERSION=go1.20] [DIST_PACKAGE_PATH=/foo/bar/baz.tar.gz]

PWD := $(shell pwd)

# Go version to use
GO_VERSION ?= go1.20

# dist package path is either DIST_PACKAGE_PATH or
# bo-pay.2006-01-02-15-04-05.tar.gz formatted with time.Now()
DIST_PACKAGE_PATH ?= bo-pay.$(shell date '+%Y-%m-%d-%H-%M-%S').tar.gz

.PHONY: builder
builder:
	cd "${PWD}/ci/docker/builder" && \
		docker build \
			--rm \
			-t "qiniu-qa/bo-pay-builder:${GO_VERSION}" \
			--build-arg "go_version=${GO_VERSION}" \
			--build-arg "builder_uid=$(shell id -u)" \
			.


.PHONY: dist
dist:
	"${PWD}/ci/build/dist-docker.sh" \
		"${GO_VERSION}" \
		"${DIST_PACKAGE_PATH}"


.PHONY: build
build:
	go install -v ./...

.PHONY: priced
priced: build
	${GOPATH}/bin/priced -conf priced/priced.yml

.PHONY: dictd
dictd: build
	${GOPATH}/bin/dictd -conf dictd/dictd.yml

.PHONY: measured
measured: build
	${GOPATH}/bin/measured -conf measured/measured.yml

.PHONY: measureproxyd
measureproxyd: build
	${GOPATH}/bin/measureproxyd -conf measureproxyd/measureproxyd.yml

.PHONY: walletd
walletd: build
	${GOPATH}/bin/walletd -conf walletd/walletd.yml

.PHONY: creditd
creditd: build
	${GOPATH}/bin/creditd -conf creditd/creditd.yml

.PHONY: pland
pland: build
	${GOPATH}/bin/pland -conf pland/pland.yml

.PHONY: dw
dw: build
	${GOPATH}/bin/dw -conf dw/dw.yml

.PHONY: lint
lint:
	@./ci/run-lint.sh

.PHONY: test
test:
	go test -timeout=3m -v \
		./coupon/... \
		./creditd/... \
		./dictd/... \
		./jm/... \
		./kirby/... \
		./measured/... \
		./measureproxyd/... \
		./pland/... \
		./priced/... \
		./qpay/... \
		./qvmprice/... \
		./walletd/... \
		./dw/... \

# https://stackoverflow.com/a/5810179
.PHONY: check-ginkgo GINKGO-exists
check-ginkgo: GINKGO-exists
GINKGO-exists: ; @command -v ginkgo > /dev/null 2>&1

i18n_extract:
	goi18n extract --sourceLanguage zh -outdir walletd/i18n ./coupon ./jm ./qpay ./walletd
	goi18n extract --sourceLanguage zh -outdir creditd/i18n ./creditd
	goi18n extract --sourceLanguage zh -outdir dictd/i18n ./dictd
	goi18n extract --sourceLanguage zh -outdir measured/i18n ./measured
	goi18n extract --sourceLanguage zh -outdir measureproxyd/i18n ./measureproxyd
	goi18n extract --sourceLanguage zh -outdir priced/i18n ./priced ./qvmprice
	goi18n extract --sourceLanguage zh -outdir pland/i18n ./pland
