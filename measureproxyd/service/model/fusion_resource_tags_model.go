package model

import (
	"net/url"
	"strconv"
)

type FusionQueryReq struct {
	UID    int
	Limit  int
	Marker string
}

const (
	fusionPath = "/admin/domain/kv/tags?"
)

func (k *FusionQueryReq) ToPathAndQueryParams() string {
	u := url.Values{}
	if k.UID != 0 {
		u.Add("uid", strconv.Itoa(k.UID))
	}
	if k.Limit != 0 {
		u.Add("limit", strconv.Itoa(k.Limit))
	}
	if k.Marker != "" {
		u.Add("marker", k.<PERSON>)
	}
	return fusionPath + u.Encode()
}

type FusionResp struct {
	Marker string    `json:"marker"`
	List   []*Domain `json:"list"`
}

type Domain struct {
	Domain string `json:"domain"`
	// Deprecated: ignored，产品团队反馈说这个字段弃用了，改用 KVTagList 字段
	Tags      map[string]string `json:"tags"`
	KVTagList []*FusionKVTag    `json:"kvTagList"` // 小驼峰请不要变
}

type FusionKVTag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
