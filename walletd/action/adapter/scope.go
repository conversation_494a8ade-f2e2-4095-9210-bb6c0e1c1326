package adapter

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/walletd/model"
	"qiniu.io/pay/walletd/service"
)

// BuildPbScope converts model.Scope to the protobuf type.
func BuildPbScope(scope *model.Scope) (*pb.Scope, error) {
	return &pb.Scope{
		Id:        scope.ID,
		IsAll:     scope.IsAll,
		Remark:    scope.Remark,
		CreatedAt: timestamppb.New(scope.CreatedAt),
		UpdatedAt: timestamppb.New(scope.UpdatedAt),
	}, nil
}

// BuildScope converts from pb.Scope to the model layer type.
func BuildScope(scope *pb.Scope) (*model.Scope, error) {
	var createdAt, updatedAt time.Time
	if scope.GetCreatedAt() != nil {
		err := scope.GetCreatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		createdAt = scope.GetCreatedAt().AsTime()
	}

	if scope.GetUpdatedAt() != nil {
		err := scope.GetUpdatedAt().CheckValid()
		if err != nil {
			return nil, err
		}
		updatedAt = scope.GetUpdatedAt().AsTime()
	}

	return &model.Scope{
		ID:        scope.GetId(),
		IsAll:     scope.GetIsAll(),
		Remark:    scope.GetRemark(),
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	}, nil
}

// BuildPbScopeDetail converts service.ScopeDetail to the protobuf type.
func BuildPbScopeDetail(detail *service.ScopeDetail) (*pb.ScopeDetail, error) {
	products, err := BuildPbScopeProductMaps(detail.Products)
	if err != nil {
		return nil, err
	}
	groups, err := BuildPbScopeItemGroupMaps(detail.ItemGroups)
	if err != nil {
		return nil, err
	}
	items, err := BuildPbScopeItemMaps(detail.Items)
	if err != nil {
		return nil, err
	}
	return &pb.ScopeDetail{
		Id:       detail.ScopeID,
		IsAll:    detail.IsAll,
		Products: products,
		Groups:   groups,
		Items:    items,
	}, nil
}

// BuildScopeDetail converts from pb.ScopeDetail to the service layer type.
func BuildScopeDetail(detail *pb.ScopeDetail) (*service.ScopeDetail, error) {
	products, err := BuildScopeProductMaps(detail.GetProducts())
	if err != nil {
		return nil, err
	}

	groups, err := BuildScopeItemGroupMaps(detail.GetGroups())
	if err != nil {
		return nil, err
	}

	items, err := BuildScopeItemMaps(detail.GetItems())
	if err != nil {
		return nil, err
	}

	return &service.ScopeDetail{
		ScopeID:    detail.GetId(),
		IsAll:      detail.IsAll,
		Products:   products,
		ItemGroups: groups,
		Items:      items,
	}, nil
}
