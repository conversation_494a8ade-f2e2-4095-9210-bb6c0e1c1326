package adapter

import (
	"qiniu.io/pay/priced/model"
	"qiniu.io/pay/priced/service"

	pb "github.com/qbox/pay-sdk/price"
)

// BuildSwitchPriceUserAlgorithmByID 转换 proto model 为服务层 service.SwitchPriceUserAlgorithmByID
func BuildSwitchPriceUserAlgorithmByID(
	pbParam *pb.SwitchUserPriceAlgorithmParamByID) *service.SwitchPriceUserAlgorithmByID {
	return &service.SwitchPriceUserAlgorithmByID{
		UID:             pbParam.GetUid(),
		PriceID:         pbParam.GetPriceId(),
		ItemID:          pbParam.GetItemId(),
		ZoneID:          pbParam.GetZoneId(),
		FromAlgorithmID: pbParam.GetFromAlgorithmId(),
		ToAlgorithmID:   pbParam.GetToAlgorithmId(),
	}
}

// BuildSwitchPriceUserAlgorithmByCode 转换 proto model 为服务层 service.SwitchPriceUserAlgorithmByCode
func BuildSwitchPriceUserAlgorithmByCode(
	pbParam *pb.SwitchUserPriceAlgorithmParamByCode) *service.SwitchPriceUserAlgorithmByCode {
	return &service.SwitchPriceUserAlgorithmByCode{
		UID:               pbParam.GetUid(),
		PriceID:           pbParam.GetPriceId(),
		ItemCode:          pbParam.GetItemCode(),
		ZoneCode:          pbParam.GetZoneCode(),
		FromAlgorithmCode: pbParam.GetFromAlgorithmCode(),
		ToAlgorithmCode:   pbParam.GetToAlgorithmCode(),
	}
}

// BuildPbPriceStair 构建单个价格阶梯信息
func BuildPbPriceStair(param *service.PriceStair) (*pb.PriceStair, error) {
	i64, err := param.Price.GetBigMoneyI64()
	if err != nil {
		return nil, err
	}
	return &pb.PriceStair{
		From:  param.From,
		To:    param.To,
		Price: uint64(i64),
	}, nil
}

// BuildPbPriceStairs 构建价格阶梯信息
func BuildPbPriceStairs(param []*service.PriceStair) ([]*pb.PriceStair, error) {
	pbPriceStairs := make([]*pb.PriceStair, len(param))
	for i, stair := range param {
		s, err := BuildPbPriceStair(stair)
		if err != nil {
			return nil, err
		}
		pbPriceStairs[i] = s
	}
	return pbPriceStairs, nil
}

// BuildPbPriceInfo 构建价格信息
func BuildPbPriceInfo(param *service.PriceInfo) (*pb.PriceInfo, error) {
	stairs, err := BuildPbPriceStairs(param.PriceStairs)
	if err != nil {
		return nil, err
	}
	return &pb.PriceInfo{
		PriceStairs:    stairs,
		StairPriceType: param.StairPriceType,
		CurrencyType:   param.CurrencyType.String(),
	}, nil
}

func BuildPbPriceItemAlgorithm(param *model.PriceUserAlgorithm) *pb.PriceItemAlgorithm {
	return &pb.PriceItemAlgorithm{
		AlgorithmId:   param.AlgorithmID,
		AlgorithmCode: param.AlgorithmCode,
		EffectTime:    int64(param.EffectTime),
		DeadTime:      int64(param.DeadTime),
		IsDisabled:    param.IsDisabled,
		Description:   param.Description,
	}
}

func BuildPbAlgorithmAndPriceInfo(
	param *service.AlgorithmAndPriceInfo,
) (*pb.AlgorithmAndPriceInfo, error) {
	info, err := BuildPbPriceInfo(param.PriceInfo)
	if err != nil {
		return nil, err
	}
	return &pb.AlgorithmAndPriceInfo{
		IsInUse:            param.IsInUse,
		ItemDataType:       param.ItemDataType,
		PriceItemAlgorithm: BuildPbPriceItemAlgorithm(param.PriceUserAlgorithm),
		PriceInfo:          info,
	}, nil
}

func BuildPbAlgorithmAndPriceInfos(
	param []*service.AlgorithmAndPriceInfo,
) ([]*pb.AlgorithmAndPriceInfo, error) {
	pbAlgorithmAndPriceInfo := make([]*pb.AlgorithmAndPriceInfo, len(param))
	for i, algorithmAndPriceInfo := range param {
		info, err := BuildPbAlgorithmAndPriceInfo(algorithmAndPriceInfo)
		if err != nil {
			return nil, err
		}
		pbAlgorithmAndPriceInfo[i] = info
	}
	return pbAlgorithmAndPriceInfo, nil
}

// BuildPbItemGroupPriceUserAlgorithms 构造 pb.ItemGroupPriceUserAlgorithms
func BuildPbItemGroupPriceUserAlgorithms(
	param *service.ItemGroupPriceUserAlgorithms,
) (*pb.ItemGroupPriceUserAlgorithms, error) {
	pbItemUserAlgorithms := make([]*pb.ItemPriceUserAlgorithms, len(param.ItemPriceUserAlgorithms))
	for i, algorithm := range param.ItemPriceUserAlgorithms {
		infos, err := BuildPbAlgorithmAndPriceInfos(algorithm.AlgorithmAndPriceInfos)
		if err != nil {
			return nil, err
		}
		pbItemUserAlgorithms[i] = &pb.ItemPriceUserAlgorithms{
			Item:                   algorithm.Item,
			AlgorithmAndPriceInfos: infos,
		}
	}
	return &pb.ItemGroupPriceUserAlgorithms{
		ItemGroup:               param.ItemGroup,
		ItemPriceUserAlgorithms: pbItemUserAlgorithms,
	}, nil
}

// BuildPbGetUserPriceAlgorithmResp 构建 pb.GetUserPriceAlgorithmResp
func BuildPbGetUserPriceAlgorithmResp(
	param *service.ProductPriceUserAlgorithms,
) (*pb.GetUserPriceAlgorithmResp, error) {
	pbItemGroupPriceUserAlgorithms := make([]*pb.ItemGroupPriceUserAlgorithms, len(param.ItemGroupPriceUserAlgorithms))
	for i, itemGroupPriceUserAlgorithm := range param.ItemGroupPriceUserAlgorithms {
		ams, err := BuildPbItemGroupPriceUserAlgorithms(itemGroupPriceUserAlgorithm)
		if err != nil {
			return nil, err
		}
		pbItemGroupPriceUserAlgorithms[i] = ams
	}
	return &pb.GetUserPriceAlgorithmResp{
		Product:             param.Product,
		ItemGroupAlgorithms: pbItemGroupPriceUserAlgorithms,
	}, nil
}
