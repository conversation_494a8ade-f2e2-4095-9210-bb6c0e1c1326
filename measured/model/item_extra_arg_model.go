package model

import (
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/errors"
)

// ItemExtraArg model definition
type ItemExtraArg struct {
	ID        uint64    `gorm:"primary_key"`
	ItemID    uint64    `gorm:"index:idx_item_extra_arg_item_id"`
	Key       string    `gorm:"type:varchar(64)"`
	Value     string    `gorm:"type:varchar(128)"`
	CreatedAt time.Time `sql:"type:DATETIME(6)"`
	UpdatedAt time.Time `sql:"type:DATETIME(6)"`
}

// ItemExtraArgDao is data access object of ItemExtraArg model
type ItemExtraArgDao struct {
	base        *dao.BaseDao
	keys        dao.CacheKeys
	cachePrefix string
}

// GetCachePrefix get cache prefix
func (d *ItemExtraArgDao) GetCachePrefix() string {
	return "measure:item_extra_arg:"
}

// GetCacheRefs get cache refs
func (d *ItemExtraArgDao) GetCacheRefs() []dao.CacheLayer {
	return nil
}

// NewItemExtraArgDao is constructor of ItemExtraArgDao
func NewItemExtraArgDao(base *dao.BaseDao) *ItemExtraArgDao {
	cachePrefix := (*ItemExtraArgDao)(nil).GetCachePrefix()
	return &ItemExtraArgDao{
		base: base,
		keys: dao.NewCacheKeys(
			fmt.Sprintf("%s:id={ID}", cachePrefix),
		),
		cachePrefix: cachePrefix,
	}
}

// GetByID select a record of ItemExtraArg by id
func (d *ItemExtraArgDao) GetByID(id uint64, expires ...time.Duration) (*ItemExtraArg, error) {
	model := &ItemExtraArg{ID: id}
	keys := dao.NewCacheKeysFmt(
		"%s:extra_arg_id:%d",
		d.cachePrefix, id,
	)
	err := d.base.QueryWithSetCache(func(value any) error {
		return d.base.First(value, id).Error
	}, keys, model, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithField("id", id)
	}
	return model, nil
}

// ListByItemID select records of ItemExtraArg by item_id
func (d *ItemExtraArgDao) ListByItemID(
	itemID uint64,
	offset int,
	limit int,
	expires ...time.Duration,
) (data []ItemExtraArg, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:item_id=%d&offset=%d&limit=%d",
		d.cachePrefix,
		itemID,
		offset,
		limit,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Where(&ItemExtraArg{ItemID: itemID}).Offset(offset).Limit(limit).Find(value).Error
	}, keys, &data, expires...)
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"item_id": itemID,
			"offset":  offset,
			"limit":   limit,
		})
	}
	return
}

// ListByItemIDs select records of ItemExtraArg by list of item_ids
//
// NOTE: this query is uncached.
func (d *ItemExtraArgDao) ListByItemIDs(
	itemIDs []uint64,
	offset int,
	limit int,
) ([]ItemExtraArg, error) {
	if len(itemIDs) == 0 {
		return nil, nil
	}

	var result []ItemExtraArg
	err := d.base.
		Model(&ItemExtraArg{}).
		Where("`item_id` IN (?)", itemIDs).
		Offset(offset).
		Limit(limit).
		Find(&result).
		Error
	if err != nil {
		return nil, errors.Trace(err).WithFields(errors.Fields{
			"item_ids": itemIDs,
			"offset":   offset,
			"limit":    limit,
		})
	}
	return result, nil
}

// CountByItemID select count of ItemExtraArg by item_id
func (d *ItemExtraArgDao) CountByItemID(
	itemID uint64,
	expires ...time.Duration,
) (count uint64, err error) {
	keys := dao.NewCacheKeysFmt(
		"%s:count:item_id=%d:count",
		d.cachePrefix,
		itemID,
	)
	err = d.base.QueryWithSetCache(func(value any) error {
		return d.base.Model(&ItemExtraArg{}).Where(&ItemExtraArg{ItemID: itemID}).Count(value).Error
	}, keys, &count, expires...)
	if err != nil {
		return 0, errors.Trace(err).WithFields(errors.Fields{
			"item_id": itemID,
		})
	}
	return
}

// Save insert or update a record of ItemExtraArg by id
func (d *ItemExtraArgDao) Save(model *ItemExtraArg, expires ...time.Duration) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Save(value).Error
	}, d.keys, model, d)
}

// Delete delete a record of ItemExtraArg
func (d *ItemExtraArgDao) Delete(model *ItemExtraArg) error {
	return d.base.ExecuteWithSyncDelCache(func(value any) error {
		return d.base.Delete(value).Error
	}, d.keys, model, d)
}

// DeleteByID delete a record of ItemExtraArg by id
func (d *ItemExtraArgDao) DeleteByID(id uint64) error {
	model := &ItemExtraArg{}
	err := d.base.First(model, id).Error
	if err != nil {
		return errors.Trace(err).WithField("id", id)
	}
	return d.Delete(model)
}
