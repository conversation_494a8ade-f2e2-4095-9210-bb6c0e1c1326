package adapter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"qiniu.io/pay/walletd/model"
)

func TestScopeItemGroupMapRoundTrip(t *testing.T) {
	x := &model.ScopeItemGroupMap{
		ID:         233,
		ScopeID:    2333,
		GroupID:    1,
		IsExcluded: false,
		CreatedAt:  time.Now().Round(0),
		UpdatedAt:  time.Now().Round(0),
	}

	y, err := BuildPbScopeItemGroupMap(x)
	assert.NoError(t, err)

	z, err := BuildScopeItemGroupMap(y)
	assert.NoError(t, err)

	assert.Equal(t, x.ID, z.ID)
	assert.Equal(t, x.ScopeID, z.ScopeID)
	assert.Equal(t, x.GroupID, z.GroupID)
	assert.Equal(t, x.IsExcluded, z.IsExcluded)
	assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
	assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
}

func TestScopeItemGroupMapsRoundTrip(t *testing.T) {
	xs := []model.ScopeItemGroupMap{
		{
			ID:         233,
			ScopeID:    2333,
			GroupID:    1,
			IsExcluded: false,
			CreatedAt:  time.Now().Round(0),
			UpdatedAt:  time.Now().Round(0),
		},
		{
			ID:         233,
			ScopeID:    2334,
			GroupID:    2,
			IsExcluded: true,
			CreatedAt:  time.Now().Round(0),
			UpdatedAt:  time.Now().Round(0),
		},
	}

	y, err := BuildPbScopeItemGroupMaps(xs)
	assert.NoError(t, err)

	zs, err := BuildScopeItemGroupMaps(y)
	assert.NoError(t, err)

	for i, x := range xs {
		z := zs[i]
		assert.Equal(t, x.ID, z.ID)
		assert.Equal(t, x.ScopeID, z.ScopeID)
		assert.Equal(t, x.GroupID, z.GroupID)
		assert.Equal(t, x.IsExcluded, z.IsExcluded)
		assert.Equal(t, x.CreatedAt.UnixNano(), z.CreatedAt.UnixNano())
		assert.Equal(t, x.UpdatedAt.UnixNano(), z.UpdatedAt.UnixNano())
	}
}
