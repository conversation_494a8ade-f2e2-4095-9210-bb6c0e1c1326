package action_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/qbox/pay-sdk/wallet"
	"github.com/stretchr/testify/assert"
)

func TestScopeItemMapInAction(t *testing.T) {
	sandbox := buildSandbox(t)

	walletClient := sandbox.walletClient

	const scopeID uint64 = 1
	ms := []wallet.ScopeItemMap{
		{
			ScopeId:    scopeID,
			ItemId:     1,
			IsExcluded: false,
		},
		{
			ScopeId:    scopeID,
			ItemId:     2,
			IsExcluded: false,
		},
		{
			ScopeId:    scopeID,
			ItemId:     3,
			IsExcluded: false,
		},
	}
	assertFields := func(_map *wallet.ScopeItemMap, n int, msgAndArgs ...any) {
		if assert.Condition(t, func() bool { return n < len(ms) }) {
			assert.Equal(t, ms[n].GetScopeId(), _map.GetScopeId(), msgAndArgs...)
			assert.Equal(t, ms[n].GetItemId(), _map.GetItemId(), msgAndArgs...)
			assert.Equal(t, ms[n].GetIsExcluded(), _map.GetIsExcluded(), msgAndArgs...)
		}
	}
	ids := make(map[uint64]int, len(ms)+1)
	{
		for n, m := range ms {
			_map, err := walletClient.CreateScopeItemMap(context.Background(), &m)
			if assert.NoError(t, err, "CreateScopeItemMap") {
				assert.NotZero(t, _map.GetId(), "CreateScopeItemMap")
				ids[_map.GetId()] = n
			}
			_map, err = walletClient.GetScopeItemMapByID(context.Background(), &wallet.IDParam{Id: _map.GetId()})
			if assert.NoError(t, err, "GetScopeItemMapByID") {
				assertFields(_map, n, "GetScopeItemMapByID")
			}
		}

		count, err := walletClient.CountAllScopeItemMaps(context.Background(), &empty.Empty{})
		if assert.NoError(t, err, "CountAllScopeItemMaps") {
			assert.Len(t, ms, int(count.GetCount()), "CountAllScopeItemMaps")
		}
	}

	list, err := walletClient.ListAllScopeItemMaps(context.Background(), &wallet.PagingParam{Page: 1, PageSize: 10})
	if assert.NoError(t, err, "ListAllScopeItemMaps") {
		assert.Len(t, ms, int(list.GetCount()), "ListAllScopeItemMaps")
		for _, _map := range list.GetScopeItemMaps() {
			if assert.Contains(t, ids, _map.GetId(), "ListAllScopeItemMaps") {
				assertFields(_map, ids[_map.GetId()], "ListAllScopeItemMaps")
			}
		}
	}
	{
		list, err := walletClient.ListScopeItemMapsByScopeID(context.Background(), &wallet.IDPagingParam{Id: scopeID, PageSize: 10, Page: 1})
		if assert.NoError(t, err, "ListScopeItemMapsByScopeID") {
			assert.Len(t, ms, int(list.GetCount()), "ListScopeItemMapsByScopeID")
			for _, _map := range list.GetScopeItemMaps() {
				if assert.Contains(t, ids, _map.GetId(), "ListScopeItemMapsByScopeID") {
					assertFields(_map, ids[_map.GetId()], "ListScopeItemMapsByScopeID")
				}
			}
		}
		count, err := walletClient.CountScopeItemMapsByScopeID(context.Background(), &wallet.IDParam{Id: scopeID})
		if assert.NoError(t, err, "CountScopeItemMapsByScopeID") {
			assert.Len(t, ms, int(count.GetCount()), "CountScopeItemMapsByScopeID")
		}
	}
	{
		const testItemID uint64 = 1
		ms = append(ms, wallet.ScopeItemMap{
			ScopeId:    2,
			ItemId:     testItemID,
			IsExcluded: false,
		})
		scope, err := walletClient.CreateScopeItemMap(context.Background(), &ms[len(ms)-1])
		if assert.NoError(t, err, "CreateScopeItemMap") {
			assert.NotZero(t, scope.GetId(), "CreateScopeItemMap")
			ids[scope.GetId()] = len(ms) - 1
		}

		list, err = walletClient.ListScopeItemMapsByItemID(context.Background(), &wallet.IDPagingParam{Id: testItemID, PageSize: 10, Page: 1})
		if assert.NoError(t, err, "ListScopeItemMapsByItemID") {
			assert.Equal(t, 2, int(list.GetCount()), "ListScopeItemMapsByItemID")
			for _, _map := range list.GetScopeItemMaps() {
				assert.Equal(t, testItemID, _map.GetItemId(), "ListScopeItemMapsByItemID->item.ID")
				if assert.Contains(t, ids, _map.GetId(), "ListScopeItemMapsByItemID") {
					assertFields(_map, ids[_map.GetId()], "ListScopeItemMapsByItemID")
				}
			}
		}
		count, err := walletClient.CountScopeItemMapsByItemID(context.Background(), &wallet.IDParam{Id: testItemID})
		if assert.NoError(t, err, "CountScopeItemMapsByItemID") {
			assert.Equal(t, uint64(2), count.GetCount(), "CountScopeItemMapsByItemID")
		}
	}
	for id, n := range ids {
		_map, err := walletClient.GetScopeItemMapByID(context.Background(), &wallet.IDParam{Id: id})
		if assert.NoError(t, err, "GetScopeItemMapByID") {
			assertFields(_map, n, "GetScopeItemMapByID")
		}
		ms[n].ItemId += 100
		_map.ItemId += 100
		_map, err = walletClient.UpdateScopeItemMapByID(context.Background(), &wallet.IDScopeItemMapParam{Id: _map.GetId(), ScopeItemMap: _map})
		if assert.NoError(t, err, "UpdateScopeItemMapByID") {
			assert.Equal(t, ms[n].GetItemId(), _map.GetItemId(), "UpdateScopeItemMapByID")
		}
	}
	for id, n := range ids {
		_map, err := walletClient.DeleteScopeItemMapByID(context.Background(), &wallet.IDParam{Id: id})
		if assert.NoError(t, err, "DeleteScopeItemMapByID") {
			assertFields(_map, n, "DeleteScopeItemMapByID")
		}
	}
}
