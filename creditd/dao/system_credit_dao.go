package dao

import (
	"github.com/jinzhu/gorm"
	"github.com/qbox/bo-base/v4/dao"
	"qiniu.io/pay/creditd/model"
)

type SystemCreditDao struct {
	base *dao.BaseDao
}

// NewSystemCreditDao is constructor of SystemCreditDao
func NewSystemCreditDao(base *dao.BaseDao) *SystemCreditDao {
	return &SystemCreditDao{
		base: base,
	}
}

// DoTransaction do a transaction
func (d *SystemCreditDao) DoTransaction(fn func(creditDao *SystemCreditDao) error) error {
	return d.base.DoTransaction(func(base *dao.BaseDao) error {
		creditDao := NewSystemCreditDao(base)
		return fn(creditDao)
	})
}

func (d *SystemCreditDao) GetSystemCredit(uid uint64) (*model.SystemCredit, error) {
	var credit model.SystemCredit
	err := d.base.Model(&model.SystemCredit{}).
		Where("uid=? and is_deleted=false", uid).
		First(&credit).
		Error
	if gorm.IsRecordNotFoundError(err) {
		return &model.SystemCredit{
			UID: uid,
		}, nil
	}
	if err != nil {
		return nil, err
	}
	return &credit, nil
}

func (d *SystemCreditDao) SaveSystemCredit(credit model.SystemCredit) error {
	return d.base.Model(&model.SystemCredit{}).Save(&credit).Error
}
