package service

import (
	"context"

	"qiniu.io/pay/dictd/model"

	"github.com/qbox/pay-sdk/middleware/logging"
)

// GetAllProvinces 获取所有省级行政区划记录
func (s *DictBizService) GetAllProvinces(
	ctx context.Context,
) ([]*model.Province, error) {
	l := logging.GetLogger(ctx)
	result, err := s.dictDao.Province.GetAll()
	if err != nil {
		l.WithError(err).Error("GetAllProvinces failed")
		return nil, err
	}
	return result, nil
}
