package action_test

import (
	"log"
	"net"
	"net/http"
	"strconv"
	"testing"
	"time"

	"google.golang.org/grpc/keepalive"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/qbox/bo-base/v4/dao"
	"github.com/qbox/bo-base/v4/lock"
	"github.com/qbox/bo-base/v4/rpc"
	"github.com/qbox/bo-base/v4/test"
	pbDict "github.com/qbox/pay-sdk/dict"
	pb "github.com/qbox/pay-sdk/wallet"

	dictAction "qiniu.io/pay/dictd/action"
	dictModel "qiniu.io/pay/dictd/model"
	dictService "qiniu.io/pay/dictd/service"
	"qiniu.io/pay/i18n/intlmockhelper"
	"qiniu.io/pay/walletd/action"
	"qiniu.io/pay/walletd/config"
	"qiniu.io/pay/walletd/model"
	"qiniu.io/pay/walletd/service"
)

type sandbox struct {
	testWrap         *test.Wrap
	dictTestWrap     *test.Wrap
	walletServer     *grpc.Server
	walletClientConn *grpc.ClientConn
	walletClient     pb.PayWalletServiceClient

	listener net.Listener

	dictListener net.Listener
	dictServer   *grpc.Server
}

func buildSandbox(t *testing.T) *sandbox {
	testWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(model.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in walletd/action return error")
	}

	dictTestWrap, err := test.NewTestWrap(t, test.WithMigrateFuncs(dictModel.RegisterMigrate))
	if err != nil {
		logrus.WithError(err).Fatalln("NewTestWrap in price/dict return error")
	}

	dictDao := dictModel.NewDictDao(dictTestWrap.BaseDao())
	dictService := dictService.NewDictBizService(dictDao, dao.CacheExpiresNoCache)
	dictAction := dictAction.NewDictAction(dictService, 10)

	dictServer := grpc.NewServer()
	pbDict.RegisterPayDictServiceServer(dictServer, dictAction)
	reflection.Register(dictServer)

	sandbox := new(sandbox)
	sandbox.dictServer = dictServer
	sandbox.initDictServer()

	conf := &config.WalletdConfig{
		Services: config.ServiceConfig{
			Dict: sandbox.dictListener.Addr().String(),
		},
		Cache: dao.CacheConfig{
			Enabled: true,
			Prefix:  strconv.FormatInt(time.Now().UnixNano(), 10),
			RedisConfig: redis.UniversalOptions{
				Addrs: []string{testWrap.Miniredis().Addr()},
			},
			DefaultExpires: time.Second * 5,
		},
	}

	userLocker := lock.NewRedisLocker(redis.NewUniversalClient(&redis.UniversalOptions{
		Addrs: []string{testWrap.Miniredis().Addr()},
	}))

	walletDao := model.NewWalletDao(testWrap.BaseDao())
	walletService, err := service.NewWalletBizService(conf, walletDao, http.DefaultClient, userLocker, dao.CacheExpiresNoCache)
	if err != nil {
		logrus.WithError(err).Fatalln("init wallet service failed")
	}

	l10nProvider := intlmockhelper.NewCommonMockL10nProvider(t)
	walletAction := action.NewWalletAction(walletService, l10nProvider, nil, nil, nil, nil, 10)

	walletServer := grpc.NewServer()
	pb.RegisterPayWalletServiceServer(walletServer, walletAction)
	reflection.Register(walletServer)

	sandbox.testWrap = testWrap
	sandbox.dictTestWrap = dictTestWrap
	sandbox.walletServer = walletServer

	sandbox.initWalletServer()
	sandbox.initWalletClient()
	t.Cleanup(sandbox.cleanup)

	return sandbox
}

func (s *sandbox) initWalletServer() {
	var err error
	s.listener, err = net.Listen("tcp", ":0")
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	go func() {
		if err := s.walletServer.Serve(s.listener); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()
}

func (s *sandbox) initWalletClient() {
	var err error
	s.walletClientConn, err = rpc.GrpcConnectWithName(s.listener.Addr().String(), rpc.ServicePayV4Wallet, keepalive.ClientParameters{})
	if err != nil {
		log.Fatalf("did not connect: %v", err)
	}
	s.walletClient = pb.NewPayWalletServiceClient(s.walletClientConn)
}

func (s *sandbox) initDictServer() {
	var err error
	s.dictListener, err = net.Listen("tcp", ":0")
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	go func() {
		if err := s.dictServer.Serve(s.dictListener); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()

	time.Sleep(time.Millisecond * 300)
}

func (s *sandbox) closeDictServer() {
	if s.dictServer != nil {
		s.dictServer.Stop()
	}
}

func (s *sandbox) closeWalletClientConn() {
	if s.walletClientConn != nil {
		s.walletClientConn.Close()
	}
}

func (s *sandbox) closeWalletServer() {
	if s.walletServer != nil {
		s.walletServer.Stop()
	}
}

func (s *sandbox) cleanup() {
	s.closeWalletClientConn()
	s.closeWalletServer()
	s.closeDictServer()
}
