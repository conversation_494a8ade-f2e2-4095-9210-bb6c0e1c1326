package stat

import (
	"github.com/qbox/bo-base/v4/errors"
	"github.com/vmihailenco/msgpack"
)

var errKVLengthNotTwo = errors.New("length of encoded KV pair is not 2")

// Override (de)serialization for *mppb.KV to get rid of the XXX_* fields.
//
// Wire format is a tuple: (K, V)

var _ msgpack.CustomEncoder = (*kv)(nil)
var _ msgpack.CustomDecoder = (*kv)(nil)

// EncodeMsgpack encodes the key-value pair.
func (x *kv) EncodeMsgpack(enc *msgpack.Encoder) error {
	err := enc.EncodeArrayLen(2)
	if err != nil {
		return errors.Trace(err)
	}

	err = enc.EncodeString(x.K)
	if err != nil {
		return errors.Trace(err)
	}

	err = enc.EncodeString(x.V)
	if err != nil {
		return errors.Trace(err)
	}

	return nil
}

// DecodeMsgpack decodes the key-value pair.
func (x *kv) DecodeMsgpack(dec *msgpack.Decoder) error {
	l, err := dec.DecodeArrayLen()
	if err != nil {
		return errors.Trace(err)
	}
	if l != 2 {
		return errors.Trace(errKVLengthNotTwo)
	}

	k, err := dec.DecodeString()
	if err != nil {
		return errors.Trace(err)
	}

	v, err := dec.DecodeString()
	if err != nil {
		return errors.Trace(err)
	}

	x.K = k
	x.V = v
	return nil
}
