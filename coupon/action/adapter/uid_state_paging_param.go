package adapter

import (
	pb "github.com/qbox/pay-sdk/wallet"

	"qiniu.io/pay/coupon/model"
)

// BuildListCouponsByCondsParam converts pb.UIDStatePagingParam to the model layer type.
func BuildListCouponsByCondsParam(
	in *pb.UIDStatePagingParam,
) (*model.ListCouponsByCondsParam, error) {
	return &model.ListCouponsByCondsParam{
		CouponIDs:     nil, // unexposed for now
		CouponCodes:   nil, // unexposed for now
		UID:           in.Uid,
		BatchID:       0, // unexposed for now
		States:        BuildCouponStates(in.State),
		PayModeScopes: nil, // not existent in coupon table alone
	}, nil
}
