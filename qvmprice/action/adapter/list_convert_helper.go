package adapter

func mapSliceFallible[T any, U any](
	fn func(T) (U, error),
	l []T,
) ([]U, error) {
	result := make([]U, len(l))
	for i, x := range l {
		y, err := fn(x)
		if err != nil {
			return nil, err
		}

		result[i] = y
	}

	return result, nil
}

func mapSlice[T any, U any](
	fn func(T) U,
	l []T,
) []U {
	result := make([]U, len(l))
	for i, x := range l {
		result[i] = fn(x)
	}

	return result
}
