package service_test

import (
	"context"
	"testing"

	"github.com/qbox/pay-sdk/dict"
	"github.com/stretchr/testify/assert"
	"qiniu.io/pay/walletd/model"
	"qiniu.io/pay/walletd/service"
)

func TestScopeDetailIsEmpty(t *testing.T) {
	type TestCase struct {
		Desc        string
		ScopeDetail service.ScopeDetail
		Empty       bool
	}

	testCases := []TestCase{
		{
			Desc: "empty",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
			},
			Empty: true,
		},
		{
			Desc: "has product with excluded",
			ScopeDetail: service.ScopeDetail{
				IsAll: true,
				Products: []model.ScopeProductMap{
					{
						ProductID:  1,
						IsExcluded: true,
					},
				},
			},
			Empty: false,
		},
		{
			Desc: "has product, not excluded",
			ScopeDetail: service.ScopeDetail{
				IsAll: true,
				Products: []model.ScopeProductMap{
					{
						ProductID:  1,
						IsExcluded: false,
					},
				},
			},
			Empty: false,
		},
	}

	for _, testCase := range testCases {
		assert.Equal(t, testCase.Empty, testCase.ScopeDetail.IsEmpty(), testCase.Desc)
	}
}

func TestScopeDetailItemsContain(t *testing.T) {
	type TestCase struct {
		Desc        string
		ScopeDetail service.ScopeDetail
		ItemID      uint64
		Hit         bool
		Contain     bool
	}

	testCases := []TestCase{
		{
			Desc: "empty",
			ScopeDetail: service.ScopeDetail{
				IsAll: true,
			},
			ItemID:  1,
			Hit:     false,
			Contain: false,
		},
		{
			Desc: "item_id with excluded",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
				Items: []model.ScopeItemMap{
					{
						ItemID:     1,
						IsExcluded: true,
					},
				},
			},
			ItemID:  1,
			Hit:     true,
			Contain: false,
		},
		{
			Desc: "item_id without excluded",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
				Items: []model.ScopeItemMap{
					{
						ItemID:     1,
						IsExcluded: false,
					},
				},
			},
			ItemID:  1,
			Hit:     true,
			Contain: true,
		},
		{
			Desc: "with different item_id",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
				Items: []model.ScopeItemMap{
					{
						ItemID:     1,
						IsExcluded: false,
					},
				},
			},
			ItemID:  2,
			Hit:     false,
			Contain: false,
		},
	}

	for _, testCase := range testCases {
		hit, result := testCase.ScopeDetail.ItemsContain(testCase.ItemID)
		assert.Equal(t, testCase.Hit, hit, testCase.Desc)
		assert.Equal(t, testCase.Contain, result, testCase.Desc)
	}
}

func TestScopeDetailGroupsContain(t *testing.T) {
	type TestCase struct {
		Desc        string
		ScopeDetail service.ScopeDetail
		GroupID     uint64
		Hit         bool
		Contain     bool
	}

	testCases := []TestCase{
		{
			Desc: "empty",
			ScopeDetail: service.ScopeDetail{
				IsAll: true,
			},
			GroupID: 1,
			Hit:     false,
			Contain: false,
		},
		{
			Desc: "group_id with excluded",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
				ItemGroups: []model.ScopeItemGroupMap{
					{
						GroupID:    1,
						IsExcluded: true,
					},
				},
			},
			GroupID: 1,
			Hit:     true,
			Contain: false,
		},
		{
			Desc: "group_id without excluded",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
				ItemGroups: []model.ScopeItemGroupMap{
					{
						GroupID:    1,
						IsExcluded: false,
					},
				},
			},
			GroupID: 1,
			Hit:     true,
			Contain: true,
		},
		{
			Desc: "with different group_id",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
				ItemGroups: []model.ScopeItemGroupMap{
					{
						GroupID:    1,
						IsExcluded: false,
					},
				},
			},
			GroupID: 2,
			Hit:     false,
			Contain: false,
		},
	}

	for _, testCase := range testCases {
		hit, result := testCase.ScopeDetail.GroupsContain(testCase.GroupID)
		assert.Equal(t, testCase.Hit, hit, testCase.Desc)
		assert.Equal(t, testCase.Contain, result, testCase.Desc)
	}
}

func TestScopeDetailProductsContain(t *testing.T) {
	type TestCase struct {
		Desc        string
		ScopeDetail service.ScopeDetail
		ProductID   uint64
		Hit         bool
		Contain     bool
	}

	testCases := []TestCase{
		{
			Desc: "empty",
			ScopeDetail: service.ScopeDetail{
				IsAll: true,
			},
			ProductID: 1,
			Hit:       false,
			Contain:   false,
		},
		{
			Desc: "product_id with excluded",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
				Products: []model.ScopeProductMap{
					{
						ProductID:  1,
						IsExcluded: true,
					},
				},
			},
			ProductID: 1,
			Hit:       true,
			Contain:   false,
		},
		{
			Desc: "product_id without excluded",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
				Products: []model.ScopeProductMap{
					{
						ProductID:  1,
						IsExcluded: false,
					},
				},
			},
			ProductID: 1,
			Hit:       true,
			Contain:   true,
		},
		{
			Desc: "with different product_id",
			ScopeDetail: service.ScopeDetail{
				IsAll: false,
				Products: []model.ScopeProductMap{
					{
						ProductID:  1,
						IsExcluded: false,
					},
				},
			},
			ProductID: 2,
			Hit:       false,
			Contain:   false,
		},
	}

	for _, testCase := range testCases {
		hit, result := testCase.ScopeDetail.ProductsContain(testCase.ProductID)
		assert.Equal(t, testCase.Hit, hit, testCase.Desc)
		assert.Equal(t, testCase.Contain, result, testCase.Desc)
	}
}

func TestScopeCheckLegacy(t *testing.T) {
	sandbox := buildSandbox(t)

	walletService := sandbox.walletService
	dictClient := sandbox.dictClient

	product, err := dictClient.CreateProduct(context.Background(), &dict.Product{
		Code: "fusion",
	})
	assert.NoError(t, err)

	group, err := dictClient.CreateItemGroup(context.Background(), &dict.ItemGroup{
		ProductId: product.Id,
		Code:      "fusion:transfer",
	})
	assert.NoError(t, err)

	item, err := dictClient.CreateItem(context.Background(), &dict.Item{
		GroupId: group.Id,
		Code:    "fusion:transfer:http",
	})
	assert.NoError(t, err)

	scope, err := walletService.CreateScope(context.Background(), &model.Scope{})
	assert.NoError(t, err)

	scopeDetail, err := walletService.CreateScopeDetail(context.Background(),
		&service.ScopeDetail{
			ScopeID: scope.ID,
			IsAll:   false,
			Products: []model.ScopeProductMap{
				{ProductID: product.Id},
			},
			ItemGroups: []model.ScopeItemGroupMap{
				{GroupID: group.Id},
			},
			Items: []model.ScopeItemMap{
				{ItemID: item.Id},
			},
		})
	assert.NoError(t, err)

	type TestCase struct {
		Desc      string
		ItemID    uint64
		GroupID   uint64
		ProductID uint64
		Err       bool
		OK        bool
	}

	testCases := []TestCase{
		{
			Desc:      "all empty",
			ItemID:    0,
			GroupID:   0,
			ProductID: 0,
			Err:       false,
			OK:        false,
		},
		{
			Desc:      "empty item",
			ItemID:    0,
			GroupID:   group.Id,
			ProductID: 0,
			Err:       false,
			OK:        true,
		},
		{
			Desc:      "invalid item",
			ItemID:    item.Id + 1,
			GroupID:   0,
			ProductID: 0,
			Err:       true,
			OK:        false,
		},
		{
			Desc:      "right item",
			ItemID:    item.Id,
			GroupID:   0,
			ProductID: 0,
			Err:       false,
			OK:        true,
		},
		{
			Desc:      "empty item, invalid group",
			ItemID:    0,
			GroupID:   group.Id + 1,
			ProductID: 0,
			Err:       true,
			OK:        false,
		},
		{
			Desc:      "right group",
			ItemID:    0,
			GroupID:   group.Id,
			ProductID: 0,
			Err:       false,
			OK:        true,
		},
		{
			Desc:      "empty group",
			ItemID:    0,
			GroupID:   0,
			ProductID: product.Id,
			Err:       false,
			OK:        true,
		},
	}

	for _, testCase := range testCases {
		ok, err := walletService.ScopeCheckLegacyByID(context.Background(),
			scopeDetail.ScopeID, testCase.ItemID, testCase.GroupID, testCase.ProductID)
		assert.Equal(t, testCase.Err, err != nil, testCase.Desc)
		assert.Equal(t, testCase.OK, ok, testCase.Desc)
	}

	// ScopeCheckByItemCode
	ok, err := walletService.ScopeCheckByItemCode(context.Background(), scope.ID, item.Code)
	assert.NoError(t, err)
	assert.True(t, ok)

	ok, err = walletService.ScopeCheckByItemCode(context.Background(), scope.ID, "invalid code")
	assert.Error(t, err)
	assert.False(t, ok)
}
