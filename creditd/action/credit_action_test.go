package action_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl/tz"
	pb "github.com/qbox/pay-sdk/credit"
)

func TestCreditAction_all(t *testing.T) {
	sandbox := buildSandbox(t, withUserCard(false), withCurrencyType(base.CurrencyTypeCNY))
	ctx := tz.WithRefLocation(context.Background(), time.UTC)

	creditClient := sandbox.creditAction

	pbCredits := []pb.Credit{
		{
			Uid:          1,
			CustomerName: "test 1",
			CreditLine:   12,
			CreditPeriod: 3,
			PeriodUnit:   1,
			Status:       pb.StatusUnactivated,
			Remark:       "test only 1",
			AuthTime:     time.Now().UnixNano(),
		}, {
			Uid:          2,
			CustomerName: "test 2",
			CreditLine:   1020,
			CreditPeriod: 8,
			PeriodUnit:   1,
			Status:       pb.StatusUnactivated,
			Remark:       "test only 2",
			AuthTime:     time.Now().UnixNano(),
		},
	}

	// test CreateCredit
	for _, pbCredit := range pbCredits {
		// create
		m, err := creditClient.CreateCredit(ctx, &pbCredit)
		if assert.NoError(t, err) {
			assert.NotZero(t, m.Id, "CreateCredit return zero id")
			assert.Equal(t, pbCredit.GetUid(), m.GetUid())
			assert.Equal(t, pbCredit.GetCustomerName(), m.GetCustomerName())
			assert.Equal(t, pbCredit.GetCreditPeriod(), m.GetCreditPeriod())
			assert.Equal(t, pbCredit.GetStatus(), m.GetStatus())
		}
	}

	// test listing given uid & status (matched)
	reqByUID := &pb.ListManualUIDCreditsReq{
		Uid:      uint64(2),
		Status:   []pb.Status{pb.StatusUnactivated},
		Page:     1,
		PageSize: 10,
	}

	listByUIDResp, err := creditClient.GetManualCreditsByUID(ctx, reqByUID)
	if assert.NoError(t, err) &&
		assert.Equal(t, uint64(1), listByUIDResp.Count) {
		pbCredit := listByUIDResp.Credits[0]
		assert.Equal(t, pbCredit.CustomerName, pbCredits[1].CustomerName)
		assert.Equal(t, pbCredit.CreditLine, pbCredits[1].CreditLine)
		assert.Equal(t, pbCredit.Status, pbCredits[1].Status)
	}

	// test listing given uid & status (not matched)
	reqByUIDStatusRevoked := &pb.ListManualUIDCreditsReq{
		Uid:      uint64(2),
		Status:   []pb.Status{pb.StatusRevoked},
		Page:     1,
		PageSize: 10,
	}

	listByUIDStatusRevokedResp, err := creditClient.GetManualCreditsByUID(ctx, reqByUIDStatusRevoked)
	if assert.NoError(t, err) {
		assert.Equal(t, uint64(0), listByUIDStatusRevokedResp.Count)
	}

	// test listing given status (matched)
	listReq := &pb.ListManualCreditsReq{
		Status:   []pb.Status{pb.StatusUnactivated},
		Page:     1,
		PageSize: 10,
	}

	listResp, err := creditClient.ListManualCredits(ctx, listReq)
	if assert.NoError(t, err) {
		assert.Equal(t, uint64(2), listResp.Count)
	}

	// test listing given status (not matched)
	listStatusRevokedReq := &pb.ListManualCreditsReq{
		Status:   []pb.Status{pb.StatusRevoked},
		Page:     1,
		PageSize: 10,
	}

	listResp2, err := creditClient.ListManualCredits(ctx, listStatusRevokedReq)
	if assert.NoError(t, err) {
		assert.Equal(t, uint64(0), listResp2.Count)
	}

	// test activate credit
	activateCreditReq := &pb.UIDParam{
		Uid: 2,
	}
	activatedCredit, err := creditClient.ActivateManualCreditByUID(ctx, activateCreditReq)
	if assert.NoError(t, err) {
		assert.Equal(t, pbCredits[1].GetCreditLine(), activatedCredit.CreditLine)
		assert.Equal(t, pbCredits[1].GetCustomerName(), activatedCredit.GetCustomerName())
		assert.Equal(t, uint64(8), activatedCredit.GetCreditPeriod())
		assert.Equal(t, pb.StatusValid, activatedCredit.GetStatus())
		assert.Equal(t, uint64(2), activatedCredit.GetUid())
	}

	// test revoke credit
	revokeCreditReq := &pb.UIDParam{
		Uid: 2,
	}
	revokedCredit, err := creditClient.RevokeManualCreditByUID(ctx, revokeCreditReq)
	if assert.NoError(t, err) {
		assert.Equal(t, pbCredits[1].GetCreditLine(), revokedCredit.CreditLine)
		assert.Equal(t, pbCredits[1].GetCustomerName(), revokedCredit.GetCustomerName())
		assert.Equal(t, uint64(8), revokedCredit.GetCreditPeriod())
		assert.Equal(t, pb.StatusRevoked, revokedCredit.GetStatus())
		assert.Equal(t, uint64(2), revokedCredit.GetUid())
	}

	// test cancel credit
	cancelCreditReq := &pb.UIDParam{
		Uid: 1,
	}
	canceledCredit, err := creditClient.CancelManualCreditByUID(ctx, cancelCreditReq)
	if assert.NoError(t, err) {
		assert.Equal(t, int64(12), canceledCredit.CreditLine)
		assert.Equal(t, pbCredits[0].GetCustomerName(), canceledCredit.GetCustomerName())
		assert.Equal(t, pbCredits[0].GetCreditPeriod(), canceledCredit.GetCreditPeriod())
		assert.Equal(t, pb.StatusUnauthorized, canceledCredit.GetStatus())
		assert.Equal(t, uint64(1), canceledCredit.GetUid())
	}

	// test cancel failed
	_, err = creditClient.CancelManualCreditByUID(ctx, cancelCreditReq)
	assert.Error(t, err)

	// test activate failed
	_, err = creditClient.ActivateManualCreditByUID(ctx, activateCreditReq)
	assert.Error(t, err)

	// test revoke failed
	_, err = creditClient.RevokeManualCreditByUID(ctx, revokeCreditReq)
	assert.Error(t, err)
}
