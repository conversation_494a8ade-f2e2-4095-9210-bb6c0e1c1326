package service

import (
	"context"

	"qiniu.io/pay/dictd/model"
)

// GetItemGroupByID 通过id获取计费项组
func (s *DictBizService) GetItemGroupByID(
	ctx context.Context,
	id uint64,
) (*model.ItemGroup, error) {
	return s.dictDao.ItemGroup.GetByID(id, s.cacheExpires)
}

// GetItemGroupByCode 通过 code 获取计费项组
func (s *DictBizService) GetItemGroupByCode(
	ctx context.Context,
	code string,
) (*model.ItemGroup, error) {
	return s.dictDao.ItemGroup.GetByCode(code, s.cacheExpires)
}

// GetItemGroupByProductIDAndCode 通过产品ID和计费项组code获取计费项组
func (s *DictBizService) GetItemGroupByProductIDAndCode(
	ctx context.Context,
	productID uint64,
	code string,
) (*model.ItemGroup, error) {
	return s.dictDao.ItemGroup.GetByProductIDAndCode(productID, code)
}

// GetItemGroupByProductAndCode 通过产品 Code 和计费项组 Code 获取计费项组
func (s *DictBizService) GetItemGroupByProductAndCode(
	ctx context.Context,
	product string,
	code string,
) (*model.ItemGroup, error) {
	return s.dictDao.ItemGroup.GetByProductAndCode(product, code)
}

// ListItemGroupsByProductID 通过产品id获取计费项组
func (s *DictBizService) ListItemGroupsByProductID(
	ctx context.Context,
	productID uint64,
	offset int,
	limit int,
) ([]model.ItemGroup, error) {
	return s.dictDao.ItemGroup.ListByProductID(productID, offset, limit, s.cacheExpires)
}

// ListItemGroupsByProductCode 通过产品 Code 获取计费项组
func (s *DictBizService) ListItemGroupsByProductCode(
	ctx context.Context,
	product string,
	offset int,
	limit int,
) ([]model.ItemGroup, error) {
	return s.dictDao.ItemGroup.ListByProductCode(product, offset, limit, s.cacheExpires)
}

// CountItemGroupsByProductID 通过产品id获取计费项组数量
func (s *DictBizService) CountItemGroupsByProductID(
	ctx context.Context,
	productID uint64,
) (uint64, error) {
	return s.dictDao.ItemGroup.CountByProductID(productID, s.cacheExpires)
}

// CountItemGroupsByProductCode 通过产品 Code 获取计费项组数量
func (s *DictBizService) CountItemGroupsByProductCode(
	ctx context.Context,
	product string,
) (uint64, error) {
	return s.dictDao.ItemGroup.CountByProductCode(product, s.cacheExpires)
}

// CreateItemGroup 创建计费项组
func (s *DictBizService) CreateItemGroup(
	ctx context.Context,
	group *model.ItemGroup,
) (*model.ItemGroup, error) {
	group.ID = 0
	err := s.dictDao.ItemGroup.Save(group, s.cacheExpires)
	return group, err
}

// UpdateItemGroupByID 通过id更新计费项组
func (s *DictBizService) UpdateItemGroupByID(
	ctx context.Context,
	id uint64,
	group *model.ItemGroup,
) (*model.ItemGroup, error) {
	group.ID = id
	err := s.dictDao.ItemGroup.Save(group, s.cacheExpires)
	return group, err
}

// UpdateItemGroupByCode 通过 Code 更新计费项组
func (s *DictBizService) UpdateItemGroupByCode(
	ctx context.Context,
	code string,
	group *model.ItemGroup,
) (*model.ItemGroup, error) {
	group.Code = code
	err := s.dictDao.ItemGroup.UpdateByCode(code, group, s.cacheExpires)
	return group, err
}

// DeleteItemGroupByID 通过id删除计费项组
func (s *DictBizService) DeleteItemGroupByID(
	ctx context.Context,
	id uint64,
) (*model.ItemGroup, error) {
	group, err := s.GetItemGroupByID(ctx, id)
	if err != nil {
		return nil, err
	}
	err = s.dictDao.ItemGroup.Delete(group)
	return group, err
}
