package adapter

import (
	pb "github.com/qbox/pay-sdk/qvmprice"

	"qiniu.io/pay/qvmprice/service"
)

func buildPbItemPricesResp(
	x *service.ItemPricesResp,
) (*pb.ItemPricesResp, error) {
	priceItems, err := buildPbQVMPriceItemDetails(x.Prices)
	if err != nil {
		return nil, err
	}

	return &pb.ItemPricesResp{
		ItemCode:   x.ItemCode,
		ZoneCode:   x.ZoneCode,
		PriceItems: priceItems,
	}, nil
}

func BuildPbItemPricesResps(
	x []*service.ItemPricesResp,
) ([]*pb.ItemPricesResp, error) {
	return mapSliceFallible(buildPbItemPricesResp, x)
}
