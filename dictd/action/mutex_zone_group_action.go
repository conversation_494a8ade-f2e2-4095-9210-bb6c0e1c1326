package action

import (
	"context"

	pb "github.com/qbox/pay-sdk/dict"
	"google.golang.org/protobuf/types/known/emptypb"
	"qiniu.io/pay/dictd/action/adapter"
)

func (a *DictAction) GetMutexZoneGroups(
	ctx context.Context,
	param *pb.GetMutexZoneGroupParam,
) (*pb.MutexZoneGroupList, error) {
	mutexZoneGroups, err := a.dictBizSrv.ListMutexZoneGroupByItemCode(ctx, param.GetItemCodes())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbMutexZoneGroupList(mutexZoneGroups), nil
}

func (a *DictAction) CreateMutexZoneGroup(
	ctx context.Context,
	mutexZoneGroup *pb.MutexZoneGroup,
) (*emptypb.Empty, error) {
	group := adapter.BuildMutexZoneGroup(mutexZoneGroup)
	err := a.dictBizSrv.CreateOrUpdateMutexZoneGroup(ctx, group)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (a *DictAction) ListMutexZoneGroups(
	ctx context.Context,
	empty *emptypb.Empty,
) (*pb.MutexZoneGroupList, error) {
	mutexZoneGroups, err := a.dictBizSrv.ListMutexZoneGroups(ctx)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbMutexZoneGroupList(mutexZoneGroups), nil
}
