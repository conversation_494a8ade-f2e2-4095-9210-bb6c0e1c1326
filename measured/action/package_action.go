package action

import (
	"context"

	"github.com/golang/protobuf/ptypes/empty"
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/action/adapter"
)

// impl PayMeasureServiceServer for MeasureAction

// GetPackageByID 根据 ID 查询单个资源包
func (a *MeasureAction) GetPackageByID(
	ctx context.Context,
	p *pb.IDParam,
) (*pb.Package, error) {
	obj, err := a.srv.GetPackageByID(ctx, p.GetId())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackage(obj)
}

// GetPackageByCode 根据 Code 查询单个资源包
func (a *MeasureAction) GetPackageByCode(
	ctx context.Context,
	p *pb.CodeParam,
) (*pb.Package, error) {
	obj, err := a.srv.GetPackageByCode(ctx, p.GetCode())
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackage(obj)
}

// CountAllPackages 获取所有资源包总数
func (a *MeasureAction) CountAllPackages(
	ctx context.Context,
	p *empty.Empty,
) (*pb.CountParam, error) {
	count, err := a.srv.CountAllPackages(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.CountParam{Count: count}, nil
}

// ListAllPackages 列举所有资源包
func (a *MeasureAction) ListAllPackages(
	ctx context.Context,
	p *pb.PagingParam,
) (*pb.PackageList, error) {
	count, err := a.srv.CountAllPackages(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(p)
	objs, err := a.srv.ListPackagesByConds(ctx, "", offset, limit)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageList(objs, count)
}

// CreatePackage 创建一个资源包
func (a *MeasureAction) CreatePackage(
	ctx context.Context,
	p *pb.Package,
) (*pb.Package, error) {
	obj, err := adapter.BuildModelPackage(p)
	if err != nil {
		return nil, err
	}
	obj, err = a.srv.CreatePackage(ctx, obj)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackage(obj)
}

// UpdatePackageByID 根据 ID 更新一个资源包
func (a *MeasureAction) UpdatePackageByID(
	ctx context.Context,
	p *pb.IDPackageParam,
) (*pb.Package, error) {
	obj, err := adapter.BuildModelPackage(p.GetPackage())
	if err != nil {
		return nil, err
	}
	obj, err = a.srv.UpdatePackageByID(ctx, p.GetId(), obj)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackage(obj)
}

// ListFreeInUsePackages 查询正在使用中的免费 Package
func (a *MeasureAction) ListFreeInUsePackages(
	ctx context.Context,
	p *pb.UIDParam,
) (*pb.PackageList, error) {
	objs, err := a.srv.ListFreeInUsePackages(ctx, p.Uid)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageList(objs, uint64(len(objs)))
}

// SearchPackages 模糊搜索 Packages
func (a *MeasureAction) SearchPackages(
	ctx context.Context,
	p *pb.SearchPackagesParam,
) (*pb.PackageList, error) {
	count, err := a.srv.CountAllPackages(ctx)
	if err != nil {
		return nil, err
	}
	offset, limit := a.Paging(p)
	objs, err := a.srv.ListPackagesByConds(ctx, p.Pattern, offset, limit)
	if err != nil {
		return nil, err
	}
	return adapter.BuildPbPackageList(objs, count)
}
