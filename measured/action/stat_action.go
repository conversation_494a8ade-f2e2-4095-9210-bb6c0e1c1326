package action

import (
	"context"
	"encoding/json"

	"github.com/qbox/bo-base/v4/errors"
	pb "github.com/qbox/pay-sdk/measure"
	"qiniu.io/pay/measured/action/adapter"
)

// impl PayMeasureServiceServer for MeasureAction

// GetStatV1 拉量接口 v1
func (a *MeasureAction) GetStatV1(
	ctx context.Context,
	p *pb.StatReqV1,
) (*pb.StatRespV1, error) {
	params, err := adapter.BuildModelStatReqV1(p)
	if err != nil {
		return nil, errors.Trace(err)
	}

	result, err := a.srv.GetStat(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}

	resp, err := adapter.BuildPbStatRespV1(result)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// GetUsersV1 拉某产品线某区域有量用户列表
func (a *MeasureAction) GetUsersV1(
	ctx context.Context,
	p *pb.GetUsersParamsV1,
) (*pb.UsersRespV1, error) {
	params, err := adapter.BuildModelGetUsersParamsV1(p)
	if err != nil {
		return nil, errors.Trace(err)
	}

	result, err := a.srv.GetUsers(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}

	resp, err := adapter.BuildPbUsersRespV1(result)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// GetItemsInUseV1 拉单个用户有量计费项列表
func (a *MeasureAction) GetItemsInUseV1(
	ctx context.Context,
	p *pb.GetItemsInUseParamsV1,
) (*pb.ItemsInUseRespV1, error) {
	params, err := adapter.BuildModelGetItemsInUseParamsV1(p)
	if err != nil {
		return nil, errors.Trace(err)
	}

	result, err := a.srv.GetItemsInUse(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}

	resp, err := adapter.BuildPbItemsInUseRespV1(result)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return resp, nil
}

// SyncCustomBillsV1 同步自定义账单
func (a *MeasureAction) SyncCustomBillsV1(
	ctx context.Context,
	p *pb.SyncCustomBillParamsV1,
) (*pb.SyncCustomBillRespV1, error) {
	params, err := adapter.BuildModelSyncCustomBillParams(p)
	if err != nil {
		return nil, errors.Trace(err)
	}
	bills, err := a.srv.SyncCustomBills(ctx, params)
	if err != nil {
		return nil, errors.Trace(err)
	}
	b, err := json.Marshal(bills)
	if err != nil {
		return nil, errors.Trace(err)
	}
	return &pb.SyncCustomBillRespV1{
		Data: string(b),
	}, nil
}
