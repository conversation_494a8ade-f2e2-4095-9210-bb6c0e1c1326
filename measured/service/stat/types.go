package stat

import (
	"sync"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	mppb "github.com/qbox/pay-sdk/measureproxy"

	kirbyAdminModel "qiniu.io/pay/kirby/model/adminModel"
	"qiniu.io/pay/measured/model"
)

// statQueryContext 拉量上下文
//
// NOTE: 一般代码中这个类型的变量称为 `scx`
//
// 一次拉量包含 0 到多条计量轴（一组时序数据就叫轴），
// 每条轴代表一个计费项一个时间段的计量，
// 合量方式目前就是相同时间的计量直接相加
type statQueryContext struct {
	debug bool
	// axes 所有要被拉量并参与合量的计量轴定义
	axes []*axisQueryParams
	// postprocessStrat 合量方式
	postprocessStrat postprocessStrategy

	dataMu *sync.RWMutex
	// axesData 每条轴的拉量结果，只会被原子更新
	axesData []*model.StatResp
}

// axisQueryParams 单条计量轴的拉量参数，目前含义为单个计费项某个时间段内的计量
type axisQueryParams struct {
	// uid UID
	uid uint32
	// begin 拉量起始时间
	begin *timestamp.Timestamp
	// end 拉量结束时间
	end *timestamp.Timestamp
	// params 拉量参数
	params *itemAxisParams
}

// itemAxisParams 单个计费项的拉量参数
type itemAxisParams struct {
	// KirbyParams 如果该查询走 kirby datapath，则此处为 kirby 的查询参数
	// 如此字段值为 nil，则意味着该查询走 measureproxy datapath
	KirbyParams *itemKirbyAxisParams

	// src 计量源种类
	Src mppb.StatSrc
	// haveZone 该查询带不带 zone 参数，用于兼容没有区域概念的老式计量源
	HaveZone bool
	// zoneCode 区域代码
	ZoneCode uint32
	// itemExtras 解析过的老式计量源拉量参数透传
	ItemExtras *parsedStatExtras

	Resource resource
}

// itemKirbyAxisParam 走 kirby datapath 的查询参数
type itemKirbyAxisParams struct {
	ItemCode string
	ZoneCode int64
	DataType string
	G        kirbyAdminModel.Granularity
	Epoch    time.Time
}

type resource struct {
	ResourceType mppb.ResourceType
	ResourceName string
}

// postprocessStrategy 计量轴的合量方式
type postprocessStrategy int

const (
	// postprocessStrategyUnknown 非法合量方式（零值）
	postprocessStrategyUnknown postprocessStrategy = 0
	// postprocessStrategySum 取各轴计量之和
	postprocessStrategySum postprocessStrategy = 1
	// postprocessStrategyMax 取各轴同一时刻每个点的最大值
	//
	// 目前只有 MULTI 计量源会使用到，用于极个别特殊计费项
	postprocessStrategyMax postprocessStrategy = 2
)
